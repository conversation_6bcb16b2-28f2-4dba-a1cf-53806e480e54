# System Patterns

## Architecture Overview
Dự án sử dụng kiến trúc **Multi-App Modular Clean Architecture** trên nền tảng Flutter:
- Hai ứng dụng chính: Mobile (smartphone/tablet) và Terminal (kiosk/fixed display)
- Mỗi app có entry point, UI, navigation, config riêng
- 70%+ code dùng chung qua thư mục `shared/` (core, data, domain, presentation)
- Clean Architecture: phân lớp rõ ràng (presentation, domain, data, core)

## Key Technical Decisions
- Áp dụng Clean Architecture cho maintainability, testability
- Modular hóa code, tối đa hóa code reuse giữa các app
- Sử dụng scripts và Makefile cho build, test, CI/CD
- Tách biệt cấu hình, DI, network, storage, error handling
- Hỗ trợ migration từ single-app sang multi-app

## Integration Patterns
- Các app sử dụng chung business logic, models, use-cases qua `shared/`
- Mỗi app có UI, navigation, config riêng biệt nhưng dùng chung core logic
- Build system hỗ trợ build/test từng app hoặc đồng thời 
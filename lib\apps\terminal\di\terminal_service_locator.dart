import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

// Shared dependencies
import '../../../shared/core/di/shared_service_locator.dart';

// Terminal-specific modules
import 'modules/terminal_kiosk_module.dart';
import 'modules/terminal_ui_module.dart';
import 'modules/terminal_hardware_module.dart';

// Terminal configuration
import '../../../shared/core/config/app_config_factory.dart';
import '../../../shared/core/config/base_app_config.dart';

/// Terminal App Service Locator
/// 
/// This class manages terminal-specific dependencies and integrates
/// with shared dependencies. It provides terminal app-specific services
/// like kiosk mode, hardware integration, and terminal UI components.
/// 
/// Usage:
/// ```dart
/// // In terminal main.dart
/// await TerminalServiceLocator.setupTerminalDependencies();
/// ```
class TerminalServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  static bool _isTerminalInitialized = false;

  /// Setup terminal-specific dependencies
  /// 
  /// This method first ensures shared dependencies are initialized,
  /// then registers terminal-specific services.
  static Future<void> setupTerminalDependencies() async {
    if (_isTerminalInitialized) {
      if (kDebugMode) {
        print('⚠️ Terminal dependencies already initialized');
      }
      return;
    }

    if (kDebugMode) {
      print('🖥️ Setting up terminal dependencies...');
    }

    // ============================================================================
    // SETUP CONFIGURATION FIRST
    // ============================================================================
    if (!AppConfigFactory.isInitialized) {
      AppConfigFactory.initialize(AppType.terminal);
    }

    // ============================================================================
    // SETUP SHARED DEPENDENCIES
    // ============================================================================
    await SharedServiceLocator.setupSharedDependencies();

    // ============================================================================
    // REGISTER TERMINAL-SPECIFIC MODULES
    // ============================================================================

    // 1. Terminal Kiosk Module
    registerTerminalKioskDependencies();

    // 2. Terminal UI Module
    registerTerminalUIDependencies();

    // 3. Terminal Hardware Module
    registerTerminalHardwareDependencies();

    _isTerminalInitialized = true;

    // Log successful setup
    _logTerminalDependencyStatus();
  }

  /// Reset terminal dependencies (useful for testing)
  static Future<void> resetTerminalDependencies() async {
    if (kDebugMode) {
      print('🔄 Resetting terminal dependencies...');
    }

    // Unregister terminal modules in reverse order
    unregisterTerminalHardwareDependencies();
    unregisterTerminalUIDependencies();
    unregisterTerminalKioskDependencies();

    // Reset shared dependencies
    await SharedServiceLocator.resetSharedDependencies();

    _isTerminalInitialized = false;

    if (kDebugMode) {
      print('✅ Terminal dependencies reset complete');
    }
  }

  /// Check if terminal dependencies are initialized
  static bool get isInitialized => _isTerminalInitialized && SharedServiceLocator.isInitialized;

  /// Get terminal dependency status for debugging
  static Map<String, dynamic> getTerminalDependencyStatus() {
    return {
      'terminal_initialized': _isTerminalInitialized,
      'shared_status': SharedServiceLocator.getSharedDependencyStatus(),
      'terminal_modules': {
        'kiosk': areTerminalKioskDependenciesRegistered(),
        'ui': areTerminalUIDependenciesRegistered(),
        'hardware': areTerminalHardwareDependenciesRegistered(),
      },
    };
  }

  /// Log terminal dependency registration status for debugging
  static void _logTerminalDependencyStatus() {
    if (kDebugMode) {
      print('🖥️ Terminal Service Locator Setup Complete');
      print('📊 Terminal Dependency Status:');

      final status = getTerminalDependencyStatus();
      final terminalModules = status['terminal_modules'] as Map<String, bool>;
      
      terminalModules.forEach((key, value) {
        final icon = value ? '✅' : '❌';
        if (kDebugMode) {
          print('  Terminal ${key.toUpperCase()}: $icon');
        }
      });
    }
  }

  /// Clean up terminal resources when app is disposed
  static Future<void> disposeTerminalDependencies() async {
    if (kDebugMode) {
      print('🧹 Disposing terminal dependencies...');
    }

    try {
      // Dispose terminal-specific resources
      // Note: Add specific disposal logic when terminal services are implemented

      // Dispose shared dependencies
      await SharedServiceLocator.disposeSharedDependencies();
      
      _isTerminalInitialized = false;

      if (kDebugMode) {
        print('✅ Terminal dependencies disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error disposing terminal dependencies: $e');
      }
      rethrow;
    }
  }

  /// Validate that all required terminal dependencies are registered
  static bool validateTerminalDependencies() {
    final sharedValid = SharedServiceLocator.validateSharedDependencies();
    final terminalStatus = getTerminalDependencyStatus();
    final terminalModules = terminalStatus['terminal_modules'] as Map<String, bool>;
    final terminalValid = terminalModules.values.every((isRegistered) => isRegistered);

    if (kDebugMode) {
      if (sharedValid && terminalValid) {
        print('✅ All terminal dependencies validated successfully');
      } else {
        print('❌ Some terminal dependencies are missing');
        if (!sharedValid) {
          print('  - Shared dependencies validation failed');
        }
        if (!terminalValid) {
          terminalModules.forEach((key, value) {
            if (!value) {
              if (kDebugMode) {
                print('  - Terminal $key: MISSING');
              }
            }
          });
        }
      }
    }

    return sharedValid && terminalValid;
  }

  /// Get GetIt instance for terminal dependencies
  static GetIt get instance => _getIt;
}

/// Terminal dependency configuration
class TerminalDependencyConfig {
  /// Terminal-specific configuration
  static bool get enableKioskMode => !kDebugMode;
  static bool get enableTerminalLogging => true;
  
  /// Terminal UI configuration
  static const Duration terminalTimeoutDuration = Duration(minutes: 5);
  static const Duration terminalIdleDuration = Duration(minutes: 2);
  
  /// Terminal hardware configuration
  static const Duration hardwareInitTimeout = Duration(seconds: 30);
  static const int hardwareRetryAttempts = 3;
  
  /// Terminal kiosk configuration
  static const bool enableAutoRestart = true;
  static const Duration autoRestartInterval = Duration(hours: 24);
}

/// Mixin for terminal classes that need access to dependencies
mixin TerminalDependencyMixin {
  /// Get terminal GetIt instance
  GetIt get terminalDI => TerminalServiceLocator.instance;
  
  /// Check if terminal dependencies are ready
  bool get areTerminalDependenciesReady => TerminalServiceLocator.isInitialized;
  
  /// Validate terminal dependencies before use
  void ensureTerminalDependenciesReady() {
    if (!areTerminalDependenciesReady) {
      throw StateError(
        'Terminal dependencies not initialized. Call TerminalServiceLocator.setupTerminalDependencies() first.',
      );
    }
  }
}

import '../../../shared/core/config/base_app_config.dart';

/// Mobile Application Configuration
/// 
/// This class provides mobile-specific configuration settings
/// while inheriting shared configuration from BaseAppConfig.
/// It includes mobile-specific UI settings, features, and behaviors.
class MobileAppConfig extends BaseAppConfig {
  // Singleton pattern
  static MobileAppConfig? _instance;
  MobileAppConfig._internal();
  
  factory MobileAppConfig() {
    _instance ??= MobileAppConfig._internal();
    return _instance!;
  }

  // ============================================================================
  // APP IDENTIFICATION
  // ============================================================================
  
  @override
  AppType get appType => AppType.mobile;
  
  @override
  String get appName => 'C-Face Mobile Temp';
  
  @override
  String get appVersion => '1.0.0';
  
  @override
  String get buildNumber => '1';

  // ============================================================================
  // MOBILE-SPECIFIC UI CONFIGURATION
  // ============================================================================
  
  @override
  MobileUIConfig get uiConfig => MobileUIConfig();

  // ============================================================================
  // MOBILE-SPECIFIC FEATURE FLAGS
  // ============================================================================
  
  @override
  MobileFeatureFlags get featureFlags => MobileFeatureFlags();

  // ============================================================================
  // MOBILE-SPECIFIC CAMERA CONFIGURATION
  // ============================================================================
  
  @override
  MobileCameraConfig get cameraConfig => MobileCameraConfig();

  // ============================================================================
  // MOBILE-SPECIFIC SETTINGS
  // ============================================================================
  
  /// Enable haptic feedback
  bool get enableHapticFeedback => true;
  
  /// Enable device orientation changes
  bool get enableOrientationChanges => true;
  
  /// Default orientation preference
  DeviceOrientation get defaultOrientation => DeviceOrientation.portrait;
  
  /// Enable background app refresh
  bool get enableBackgroundRefresh => true;
  
  /// Battery optimization mode
  BatteryOptimizationMode get batteryOptimizationMode => 
      isDevelopment ? BatteryOptimizationMode.none : BatteryOptimizationMode.balanced;
  
  /// Network optimization for mobile data
  bool get optimizeForMobileData => true;
  
  /// Enable location services
  bool get enableLocationServices => false;
  
  /// Enable contact access
  bool get enableContactAccess => false;
  
  /// Enable photo library access
  bool get enablePhotoLibraryAccess => true;
  
  /// Maximum concurrent network requests
  int get maxConcurrentRequests => 3;
  
  /// Image compression quality (0.0 to 1.0)
  double get imageCompressionQuality => 0.8;
  
  /// Enable app state restoration
  bool get enableStateRestoration => true;
  
  /// Auto-save interval for user data
  Duration get autoSaveInterval => const Duration(minutes: 2);

  // ============================================================================
  // MOBILE NAVIGATION CONFIGURATION
  // ============================================================================
  
  /// Enable deep linking
  bool get enableDeepLinking => true;
  
  /// Enable route logging for debugging
  bool get enableRouteLogging => isDevelopment;
  
  /// Default route transition type
  RouteTransitionType get defaultRouteTransition => RouteTransitionType.slide;
  
  /// Enable swipe to go back
  bool get enableSwipeToGoBack => true;

  // ============================================================================
  // MOBILE SECURITY CONFIGURATION
  // ============================================================================
  
  /// Enable app lock when backgrounded
  bool get enableAppLock => true;
  
  /// App lock timeout when backgrounded
  Duration get appLockTimeout => const Duration(minutes: 5);
  
  /// Enable screenshot prevention in secure screens
  bool get preventScreenshots => isProduction;
  
  /// Enable jailbreak/root detection
  bool get enableRootDetection => isProduction;

  // ============================================================================
  // VALIDATION
  // ============================================================================
  
  @override
  bool validateAppSpecificConfig() {
    try {
      // Validate mobile-specific settings
      if (imageCompressionQuality < 0.0 || imageCompressionQuality > 1.0) {
        return false;
      }
      if (maxConcurrentRequests <= 0) {
        return false;
      }
      
      // Validate sub-configurations
      return uiConfig.validate() && 
             featureFlags.validate() && 
             cameraConfig.validate();
    } catch (e) {
      return false;
    }
  }
}

/// Mobile UI Configuration
class MobileUIConfig implements UIConfig {
  @override
  Duration get defaultAnimationDuration => const Duration(milliseconds: 300);
  
  @override
  Duration get pageTransitionDuration => const Duration(milliseconds: 250);
  
  @override
  Duration get snackbarDuration => const Duration(seconds: 3);
  
  @override
  Duration get loadingIndicatorDelay => const Duration(milliseconds: 500);
  
  /// Mobile-specific UI settings
  Duration get bottomSheetAnimationDuration => const Duration(milliseconds: 300);
  Duration get dialogAnimationDuration => const Duration(milliseconds: 200);
  Duration get tabSwitchAnimationDuration => const Duration(milliseconds: 150);
  
  /// Mobile gesture settings
  double get swipeThreshold => 50.0;
  Duration get doubleTapTimeout => const Duration(milliseconds: 300);
  Duration get longPressTimeout => const Duration(milliseconds: 500);
  
  /// Mobile layout settings
  double get minTouchTargetSize => 44.0;
  double get defaultPadding => 16.0;
  double get compactPadding => 8.0;
  
  bool validate() {
    return swipeThreshold > 0 && 
           minTouchTargetSize > 0 && 
           defaultPadding >= 0;
  }
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'defaultAnimationDuration': defaultAnimationDuration.inMilliseconds,
      'pageTransitionDuration': pageTransitionDuration.inMilliseconds,
      'snackbarDuration': snackbarDuration.inSeconds,
      'loadingIndicatorDelay': loadingIndicatorDelay.inMilliseconds,
      'swipeThreshold': swipeThreshold,
      'minTouchTargetSize': minTouchTargetSize,
      'defaultPadding': defaultPadding,
    };
  }
}

/// Mobile Feature Flags
class MobileFeatureFlags implements FeatureFlags {
  @override
  bool get enableFaceCapture => true;
  
  @override
  bool get enableUserManagement => true;
  
  @override
  bool get enableOfflineMode => true;
  
  @override
  bool get enableBiometricAuth => true;
  
  @override
  bool get enablePushNotifications => true;
  
  /// Mobile-specific features
  bool get enableDarkMode => true;
  bool get enableSystemTheme => true;
  bool get enableAccessibility => true;
  bool get enableVoiceOver => true;
  bool get enableLargeText => true;
  bool get enableReducedMotion => true;
  bool get enableShakeToReport => true;
  bool get enableQuickActions => true;
  bool get enableWidgets => false;
  bool get enableSiri => false;
  
  bool validate() => true; // All boolean flags are valid by default
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'enableFaceCapture': enableFaceCapture,
      'enableUserManagement': enableUserManagement,
      'enableOfflineMode': enableOfflineMode,
      'enableBiometricAuth': enableBiometricAuth,
      'enablePushNotifications': enablePushNotifications,
      'enableDarkMode': enableDarkMode,
      'enableSystemTheme': enableSystemTheme,
      'enableAccessibility': enableAccessibility,
      'enableShakeToReport': enableShakeToReport,
    };
  }
}

/// Mobile Camera Configuration
class MobileCameraConfig implements CameraConfig {
  @override
  CameraResolution get defaultCameraResolution => CameraResolution.medium; // Optimized for Snapdragon 8 Gen 2
  
  @override
  double get cameraAspectRatio => 4 / 3; // Better for mobile screens
  
  @override
  Duration get cameraCaptureTimeout => const Duration(seconds: 10);
  
  @override
  int get maxFaceCaptures => 5;
  
  @override
  Duration get cacheFaceDuration => const Duration(minutes: 1);
  
  /// Mobile-specific camera settings
  bool get enableFlash => true;
  bool get enableZoom => true;
  bool get enableFrontCamera => true;
  bool get enableCameraSwitch => true;
  bool get enableGridLines => true;
  bool get enableImageStabilization => true;
  bool get autoFocusEnabled => true;
  double get maxZoomLevel => 8.0;
  CameraQuality get imageQuality => CameraQuality.high;
  
  bool validate() {
    return maxFaceCaptures > 0 && 
           maxZoomLevel > 1.0 && 
           cameraAspectRatio > 0;
  }
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'defaultCameraResolution': defaultCameraResolution.name,
      'cameraAspectRatio': cameraAspectRatio,
      'cameraCaptureTimeout': cameraCaptureTimeout.inSeconds,
      'maxFaceCaptures': maxFaceCaptures,
      'enableFlash': enableFlash,
      'enableZoom': enableZoom,
      'maxZoomLevel': maxZoomLevel,
      'imageQuality': imageQuality.name,
    };
  }
}

/// Mobile-specific enums
enum DeviceOrientation {
  portrait,
  landscape,
  auto,
}

enum BatteryOptimizationMode {
  none,
  balanced,
  aggressive,
}

enum RouteTransitionType {
  slide,
  fade,
  scale,
  rotation,
}

enum CameraQuality {
  low,
  medium,
  high,
  max,
}

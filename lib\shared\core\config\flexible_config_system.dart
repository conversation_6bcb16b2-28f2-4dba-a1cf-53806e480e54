/// Flexible Configuration System Architecture
/// 
/// This file defines the architecture for a flexible configuration system
/// that supports multiple configuration sources with priority-based loading.

import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Configuration source types with priority order
enum ConfigSource {
  /// Runtime changes (highest priority) - Admin UI, hot reload
  runtime(priority: 1),
  
  /// Environment variables - Deployment specific
  environment(priority: 2),
  
  /// Local configuration files - App specific settings
  localFile(priority: 3),
  
  /// Remote configuration - Server managed settings
  remote(priority: 4),
  
  /// Hardcoded defaults (lowest priority) - Fallback values
  defaults(priority: 5);

  const ConfigSource({required this.priority});
  final int priority;
}

/// Configuration value types for validation
enum ConfigValueType {
  string,
  integer,
  double,
  boolean,
  duration,
  color,
  list,
  map,
}

/// Configuration parameter definition
class ConfigParameter<T> {
  final String key;
  final String category;
  final String description;
  final ConfigValueType type;
  final T defaultValue;
  final T? minValue;
  final T? maxValue;
  final List<T>? allowedValues;
  final bool isRequired;
  final bool isSecure;
  final String? environmentKey;
  final T Function(dynamic)? parser;
  final bool Function(T)? validator;

  const ConfigParameter({
    required this.key,
    required this.category,
    required this.description,
    required this.type,
    required this.defaultValue,
    this.minValue,
    this.maxValue,
    this.allowedValues,
    this.isRequired = false,
    this.isSecure = false,
    this.environmentKey,
    this.parser,
    this.validator,
  });

  /// Validate a value against parameter constraints
  bool isValid(T value) {
    // Custom validator
    if (validator != null && !validator!(value)) {
      return false;
    }

    // Range validation
    if (minValue != null && value is Comparable && value.compareTo(minValue!) < 0) {
      return false;
    }
    if (maxValue != null && value is Comparable && value.compareTo(maxValue!) > 0) {
      return false;
    }

    // Allowed values validation
    if (allowedValues != null && !allowedValues!.contains(value)) {
      return false;
    }

    return true;
  }

  /// Parse value from dynamic input
  T parseValue(dynamic input) {
    if (parser != null) {
      return parser!(input);
    }

    switch (type) {
      case ConfigValueType.string:
        return input.toString() as T;
      case ConfigValueType.integer:
        return int.parse(input.toString()) as T;
      case ConfigValueType.double:
        return double.parse(input.toString()) as T;
      case ConfigValueType.boolean:
        if (input is bool) return input as T;
        return (input.toString().toLowerCase() == 'true') as T;
      case ConfigValueType.duration:
        if (input is int) return Duration(milliseconds: input) as T;
        return Duration(milliseconds: int.parse(input.toString())) as T;
      case ConfigValueType.color:
        if (input is int) return input as T;
        return int.parse(input.toString().replaceFirst('#', ''), radix: 16) as T;
      case ConfigValueType.list:
        if (input is List) return input as T;
        return jsonDecode(input.toString()) as T;
      case ConfigValueType.map:
        if (input is Map) return input as T;
        return jsonDecode(input.toString()) as T;
    }
  }
}

/// Configuration change event
class ConfigChangeEvent<T> {
  final String key;
  final T oldValue;
  final T newValue;
  final ConfigSource source;
  final DateTime timestamp;

  ConfigChangeEvent({
    required this.key,
    required this.oldValue,
    required this.newValue,
    required this.source,
    required this.timestamp,
  });
}

/// Configuration provider interface
abstract class ConfigProvider {
  String get name;
  ConfigSource get source;
  Future<void> initialize();
  Future<Map<String, dynamic>> loadConfiguration();
  Future<void> saveConfiguration(Map<String, dynamic> config);
  Stream<Map<String, dynamic>> get configStream;
  Future<void> dispose();
}

/// Configuration manager interface
abstract class IConfigurationManager {
  /// Get configuration value
  T getValue<T>(String key, {T? defaultValue});
  
  /// Set configuration value
  Future<void> setValue<T>(String key, T value, {ConfigSource source = ConfigSource.runtime});
  
  /// Register configuration parameter
  void registerParameter<T>(ConfigParameter<T> parameter);
  
  /// Get all parameters in a category
  Map<String, dynamic> getCategoryValues(String category);
  
  /// Listen to configuration changes
  Stream<ConfigChangeEvent> get changeStream;
  
  /// Reload configuration from all sources
  Future<void> reload();
  
  /// Export configuration
  Map<String, dynamic> exportConfiguration({bool includeSecure = false});
  
  /// Import configuration
  Future<void> importConfiguration(Map<String, dynamic> config);
  
  /// Validate all configuration
  List<String> validateConfiguration();
}

/// Configuration categories
class ConfigCategories {
  static const String faceDetection = 'face_detection';
  static const String network = 'network';
  static const String ui = 'ui';
  static const String performance = 'performance';
  static const String camera = 'camera';
  static const String security = 'security';
  static const String cache = 'cache';
  static const String kiosk = 'kiosk';
  static const String debug = 'debug';
  static const String relay = 'relay';
  static const String relayDevice = 'relay_device';
  static const String relayNetwork = 'relay_network';
  static const String relayTiming = 'relay_timing';
}

/// Configuration keys
class ConfigKeys {
  // Face Detection
  static const String minFaceQualityForDetection = 'face_detection.min_quality_detection';
  static const String minFaceQualityForRecognition = 'face_detection.min_quality_recognition';
  static const String minFaceQualityForAvatarCapture = 'face_detection.min_quality_avatar_capture';
  static const String significantQualityChange = 'face_detection.significant_quality_change';
  static const String recognitionThrottleDuration = 'face_detection.recognition_throttle_duration';
  static const String userDisplayTimeout = 'face_detection.user_display_timeout';
  static const String avatarCaptureThrottle = 'face_detection.avatar_capture_throttle';
  static const String avatarDisplayDuration = 'face_detection.avatar_display_duration';
  static const String frameSkipCount = 'face_detection.frame_skip_count';
  static const String detectionTimeout = 'face_detection.detection_timeout';
  static const String avatarPaddingFactor = 'face_detection.avatar_padding_factor';
  static const String avatarTargetSize = 'face_detection.avatar_target_size';
  static const String avatarImageQuality = 'face_detection.avatar_image_quality';
  static const String recognitionImageQuality = 'face_detection.recognition_image_quality';
  static const String maxRecognitionRetries = 'face_detection.max_recognition_retries';
  static const String recognitionTimeout = 'face_detection.recognition_timeout';
  static const String maxImageSizeBytes = 'face_detection.max_image_size_bytes';
  static const String compressionQuality = 'face_detection.compression_quality';
  static const String imageFormat = 'face_detection.image_format';
  static const String sourceIdentifier = 'face_detection.source_identifier';


  
  // Network
  static const String baseApiUrl = 'network.base_api_url';
  static const String baseUrl = 'network.base_url';
  static const String apiVersion = 'network.api_version';
  static const String requestTimeout = 'network.request_timeout';
  static const String connectTimeout = 'network.connect_timeout';
  static const String receiveTimeout = 'network.receive_timeout';
  static const String sendTimeout = 'network.send_timeout';
  static const String maxRetryAttempts = 'network.max_retry_attempts';
  static const String retryDelay = 'network.retry_delay';
  static const String deviceId = 'network.device_id';

  // Additional Network Keys
  static const String devBaseUrl = 'network.dev_base_url';
  static const String devBaseUrlFallback = 'network.dev_base_url_fallback';
  static const String stagingBaseUrl = 'network.staging_base_url';
  static const String prodBaseUrl = 'network.prod_base_url';
  static const String currentBaseUrl = 'network.current_base_url';
  
  // Server communication keys
  static const String heartbeatInterval = 'network.heartbeat_interval';
  static const String pingInterval = 'network.ping_interval';
  static const String serverHealthCheckInterval = 'network.server_health_check_interval';
  static const String autoReconnect = 'network.auto_reconnect';
  static const String maxReconnectAttempts = 'network.max_reconnect_attempts';
  static const String reconnectDelay = 'network.reconnect_delay';
  
  // UI
  static const String primaryColor = 'ui.primary_color';
  static const String successColor = 'ui.success_color';
  static const String warningColor = 'ui.warning_color';
  static const String errorColor = 'ui.error_color';
  static const String backgroundColor = 'ui.background_color';
  static const String avatarSize = 'ui.avatar_size';
  static const String avatarBorderRadius = 'ui.avatar_border_radius';
  static const String qualityBadgeSize = 'ui.quality_badge_size';
  static const String guideFrameStrokeWidth = 'ui.guide_frame_stroke_width';
  static const String guideFrameCornerRadius = 'ui.guide_frame_corner_radius';
  static const String guideFrameMargin = 'ui.guide_frame_margin';
  static const String progressIndicatorStrokeWidth = 'ui.progress_indicator_stroke_width';
  static const String animationDuration = 'ui.animation_duration';
  static const String progressAnimationDuration = 'ui.progress_animation_duration';
  static const String qualityIndicatorSize = 'ui.quality_indicator_size';
  static const String qualityIndicatorFontSize = 'ui.quality_indicator_font_size';
  
  // Performance
  static const String normalFrameRate = 'performance.normal_frame_rate';
  static const String optimizedFrameRate = 'performance.optimized_frame_rate';
  static const String extremePowerSavingFrameRate = 'performance.extreme_power_saving_frame_rate';
  static const String powerSavingDelay = 'performance.power_saving_delay';
  static const String resourceOptimizationDelay = 'performance.resource_optimization_delay';
  static const String extremePowerSavingDelay = 'performance.extreme_power_saving_delay';
  static const String faceAbsenceForOptimization = 'performance.face_absence_for_optimization';
  static const String faceAbsenceForExtremeSaving = 'performance.face_absence_for_extreme_saving';
  static const String maxBrightness = 'performance.max_brightness';
  static const String minBrightness = 'performance.min_brightness';
  static const String powerSavingBrightness = 'performance.power_saving_brightness';
  static const String brightnessTransitionDuration = 'performance.brightness_transition_duration';

  // Extended UI Color Keys
  static const String primaryLightColor = 'ui.primary_light_color';
  static const String primaryBackgroundColor = 'ui.primary_background_color';
  static const String surfaceColor = 'ui.surface_color';
  static const String surfaceVariantColor = 'ui.surface_variant_color';
  static const String textPrimaryColor = 'ui.text_primary_color';
  static const String textSecondaryColor = 'ui.text_secondary_color';
  static const String textTertiaryColor = 'ui.text_tertiary_color';
  static const String textPlaceholderColor = 'ui.text_placeholder_color';
  static const String textOnPrimaryColor = 'ui.text_on_primary_color';

  // Camera
  static const String defaultCameraResolution = 'camera.default_resolution';
  static const String cameraAspectRatio = 'camera.aspect_ratio';
  static const String maxFaceCaptures = 'camera.max_face_captures';
  
  // Security
  static const String minPasswordLength = 'security.min_password_length';
  static const String maxPasswordLength = 'security.max_password_length';
  static const String biometricAuthEnabled = 'security.biometric_auth_enabled';
  
  // Cache
  static const String cacheApiDuration = 'cache.api_duration';
  static const String maxCacheSizeMB = 'cache.max_size_mb';
  
  // Kiosk
  static const String idleTimeout = 'kiosk.idle_timeout';
  static const String autoReturnHome = 'kiosk.auto_return_home';
  static const String fullscreenMode = 'kiosk.fullscreen_mode';
  
  // Debug
  static const String enableFaceDetectionLogs = 'debug.enable_face_detection_logs';
  static const String enablePerformanceMonitoring = 'debug.enable_performance_monitoring';
}

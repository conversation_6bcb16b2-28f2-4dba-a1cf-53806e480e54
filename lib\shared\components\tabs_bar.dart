import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../icons/app_icons.dart';

/// Bottom tabs bar component với custom SVG icons
class TabsBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onTabSelected;

  const TabsBar({
    super.key,
    required this.selectedIndex,
    required this.onTabSelected,
  });

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: selectedIndex,
      onTap: onTabSelected,
      type: BottomNavigationBarType.fixed,
      backgroundColor: Colors.white,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.textSecondary,
      selectedFontSize: 10,
      unselectedFontSize: 10,
      iconSize: 24,
      elevation: 8,
      items: [
        BottomNavigationBarItem(
          icon: AppIcons.tabHome(color: selectedIndex == 0 ? AppColors.primary : AppColors.textSecondary),
          label: 'Trang chủ',
        ),
        BottomNavigationBarItem(
          icon: AppIcons.tabTools(color: selectedIndex == 1 ? AppColors.primary : AppColors.textSecondary),
          label: 'Công cụ',
        ),
        BottomNavigationBarItem(
          icon: AppIcons.tabNotifications(color: selectedIndex == 2 ? AppColors.primary : AppColors.textSecondary),
          label: 'Thông báo',
        ),
        BottomNavigationBarItem(
          icon: AppIcons.tabAccount(color: selectedIndex == 3 ? AppColors.primary : AppColors.textSecondary),
          label: 'Tài khoản',
        ),
      ],
    );
  }

}

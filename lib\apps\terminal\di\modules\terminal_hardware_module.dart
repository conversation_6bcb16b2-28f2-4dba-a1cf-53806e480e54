import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Terminal Hardware Module
/// 
/// This module registers terminal-specific hardware dependencies
/// including hardware integration services, device management,
/// peripheral control, and hardware monitoring.
/// 
/// Note: This module will be fully implemented when terminal
/// hardware integration features are developed.
void registerTerminalHardwareDependencies() {
  // TODO: Implement when terminal hardware features are developed
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<TerminalHardwareManager>(
  //   () => TerminalHardwareManagerImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalCameraService>(
  //   () => TerminalCameraServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalDisplayService>(
  //   () => TerminalDisplayServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalPeripheralService>(
  //   () => TerminalPeripheralServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalHealthMonitor>(
  //   () => TerminalHealthMonitorImpl(),
  // );
}

/// Unregister terminal hardware dependencies (for testing)
void unregisterTerminalHardwareDependencies() {
  // TODO: Implement when hardware dependencies are added
  // if (getIt.isRegistered<TerminalHardwareManager>()) {
  //   getIt.unregister<TerminalHardwareManager>();
  // }
  // if (getIt.isRegistered<TerminalCameraService>()) {
  //   getIt.unregister<TerminalCameraService>();
  // }
  // if (getIt.isRegistered<TerminalDisplayService>()) {
  //   getIt.unregister<TerminalDisplayService>();
  // }
  // if (getIt.isRegistered<TerminalPeripheralService>()) {
  //   getIt.unregister<TerminalPeripheralService>();
  // }
  // if (getIt.isRegistered<TerminalHealthMonitor>()) {
  //   getIt.unregister<TerminalHealthMonitor>();
  // }
}

/// Reset terminal hardware module (clear và re-register)
void resetTerminalHardwareModule() {
  unregisterTerminalHardwareDependencies();
  registerTerminalHardwareDependencies();
}

/// Check if terminal hardware dependencies are registered
bool areTerminalHardwareDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<TerminalHardwareManager>() &&
  //        getIt.isRegistered<TerminalCameraService>() &&
  //        getIt.isRegistered<TerminalDisplayService>() &&
  //        getIt.isRegistered<TerminalPeripheralService>() &&
  //        getIt.isRegistered<TerminalHealthMonitor>();
  return true; // Placeholder
}

/// Get terminal hardware dependencies for debugging
Map<String, bool> getTerminalHardwareDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'TerminalHardwareManager': getIt.isRegistered<TerminalHardwareManager>(),
    // 'TerminalCameraService': getIt.isRegistered<TerminalCameraService>(),
    // 'TerminalDisplayService': getIt.isRegistered<TerminalDisplayService>(),
    // 'TerminalPeripheralService': getIt.isRegistered<TerminalPeripheralService>(),
    // 'TerminalHealthMonitor': getIt.isRegistered<TerminalHealthMonitor>(),
  };
}

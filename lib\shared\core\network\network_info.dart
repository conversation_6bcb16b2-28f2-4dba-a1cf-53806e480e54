import 'package:connectivity_plus/connectivity_plus.dart';

/// Abstract interface for network connectivity information
abstract class NetworkInfo {
  /// Check if device is connected to internet
  Future<bool> get isConnected;
  
  /// Get current connection type
  Future<ConnectivityResult> get connectionType;
  
  /// Stream of connectivity changes
  Stream<ConnectivityResult> get onConnectivityChanged;
  
  /// Check if connected to WiFi
  Future<bool> get isConnectedToWiFi;
  
  /// Check if connected to mobile data
  Future<bool> get isConnectedToMobile;
}

/// Implementation of NetworkInfo using connectivity_plus package
class NetworkInfoImpl implements NetworkInfo {
  final Connectivity connectivity;

  NetworkInfoImpl(this.connectivity);

  @override
  Future<bool> get isConnected async {
    final result = await connectivity.checkConnectivity();
    return !result.contains(ConnectivityResult.none);
  }

  @override
  Future<ConnectivityResult> get connectionType async {
    final results = await connectivity.checkConnectivity();
    return results.isNotEmpty ? results.first : ConnectivityResult.none;
  }

  @override
  Stream<ConnectivityResult> get onConnectivityChanged {
    return connectivity.onConnectivityChanged.map((results) =>
        results.isNotEmpty ? results.first : ConnectivityResult.none);
  }

  @override
  Future<bool> get isConnectedToWiFi async {
    final result = await connectivity.checkConnectivity();
    return result.contains(ConnectivityResult.wifi);
  }

  @override
  Future<bool> get isConnectedToMobile async {
    final result = await connectivity.checkConnectivity();
    return result.contains(ConnectivityResult.mobile);
  }
}

/// Network status enum for easier handling
enum NetworkStatus {
  connected,
  disconnected,
  wifi,
  mobile,
  unknown,
}

/// Network info utilities
class NetworkInfoUtils {
  /// Convert ConnectivityResult to NetworkStatus
  static NetworkStatus connectivityResultToNetworkStatus(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return NetworkStatus.wifi;
      case ConnectivityResult.mobile:
        return NetworkStatus.mobile;
      case ConnectivityResult.none:
        return NetworkStatus.disconnected;
      default:
        return NetworkStatus.unknown;
    }
  }

  /// Check if network status indicates connection
  static bool isConnectedStatus(NetworkStatus status) {
    return status == NetworkStatus.connected ||
           status == NetworkStatus.wifi ||
           status == NetworkStatus.mobile;
  }

  /// Get human readable network status
  static String getNetworkStatusText(NetworkStatus status) {
    switch (status) {
      case NetworkStatus.connected:
        return 'Connected';
      case NetworkStatus.disconnected:
        return 'Disconnected';
      case NetworkStatus.wifi:
        return 'WiFi';
      case NetworkStatus.mobile:
        return 'Mobile Data';
      case NetworkStatus.unknown:
        return 'Unknown';
    }
  }
}

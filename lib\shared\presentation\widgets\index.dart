/// Shared Widgets Index - Export all widget components
///
/// This file exports all widget components that provide consistent
/// UI elements and functionality across mobile and terminal applications.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/presentation/widgets/index.dart';
///
/// // Use any widget
/// CButton(
///   text: 'Click me',
///   onPressed: () {},
/// )
///
/// // Use navigation wrapper
/// Widget myScreen = MyScreen().withBackNavigation();
/// ```
library;

// ============================================================================
// WIDGET EXPORTS
// ============================================================================

/// Common widgets for UI components
export 'common/index.dart';

/// Navigation widgets for back navigation functionality
export 'navigation/index.dart';

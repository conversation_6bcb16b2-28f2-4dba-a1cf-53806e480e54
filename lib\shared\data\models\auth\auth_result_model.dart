import 'package:flutter/foundation.dart';
import '../../../domain/entities/auth/auth_result.dart';
import '../user/user_model.dart';
import '../../../services/jwt_decoder_service.dart';

/// Data model for authentication result
/// 
/// Represents the response from authentication API calls
class AuthResultModel {
  final String accessToken;
  final String? refreshToken;
  final UserModel? user;
  final DateTime? expiresAt;
  final String? tokenType;
  final List<String>? scopes;
  final Map<String, dynamic>? metadata;

  const AuthResultModel({
    required this.accessToken,
    this.refreshToken,
    this.user,
    this.expiresAt,
    this.tokenType = 'Bearer',
    this.scopes,
    this.metadata,
  });

  /// Create from JSON
  factory AuthResultModel.fromJson(Map<String, dynamic> json) {
    // Extract access_token - handle different field names and null values
    final accessToken = json['access_token'] as String? ??
                       json['accessToken'] as String? ??
                       json['token'] as String?;

    if (accessToken == null) {
      throw Exception('Cannot convert to AuthResult entity: missing required field access_token');
    }

    // Calculate expiresAt from expires_in if available
    DateTime? expiresAt;
    if (json['expires_at'] != null) {
      expiresAt = DateTime.parse(json['expires_at'] as String);
    } else if (json['expires_in'] != null) {
      final expiresInSeconds = json['expires_in'] as int;
      expiresAt = DateTime.now().add(Duration(seconds: expiresInSeconds));
    }

    // Extract refresh_token from API response
    final refreshToken = json['refresh_token'] as String?;

    // Extract user from API response
    UserModel? user = json['user'] != null
        ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
        : null;

    // Extract scopes from API response
    List<String>? scopes = json['scopes'] != null
        ? List<String>.from(json['scopes'] as List)
        : null;

    // FALLBACK: If missing critical data, try to decode from access token
    if (user == null || expiresAt == null || scopes == null) {
      try {
        final jwtDecoder = JwtDecoderService();
        final jwtPayload = jwtDecoder.decodeToken(accessToken);

        if (jwtPayload != null) {
          // Fallback for expiresAt
          if (expiresAt == null && jwtPayload.exp != null) {
            expiresAt = DateTime.fromMillisecondsSinceEpoch(jwtPayload.exp! * 1000);
          }

          // Fallback for user info
          if (user == null && jwtPayload.username != null) {
            user = UserModel(
              id: jwtPayload.sub ?? '',
              username: jwtPayload.username ?? '',
              name: jwtPayload.username ?? '', // Use username as fallback for name
              code: '', // Default empty code
              subId: 0, // Default subId
              status: 'ACTIVE', // Default status
              mappings: [], // Default empty mappings
              currentTenantId: jwtPayload.currentTenantId,
              createdAt: jwtPayload.issuedTime,
              updatedAt: DateTime.now(),
            );
          }

          // Fallback for scopes - try multiple sources
          if (scopes == null) {
            // Try from top-level scopes first
            if (jwtPayload.scopes.isNotEmpty) {
              scopes = jwtPayload.scopes;
            } else if (jwtPayload.role?.additionalData?['scopes'] != null) {
              // Fallback to role.scopes
              try {
                scopes = List<String>.from(jwtPayload.role!.additionalData!['scopes'] as List);
              } catch (e) {
                // If both fail, leave as null
              }
            }
          }
        }
      } catch (e) {
        // JWT decode failed, continue with what we have
        // Don't throw error here to avoid breaking the flow
        if (kDebugMode) {
          print('⚠️ JWT fallback failed: $e');
        }
      }
    }

    return AuthResultModel(
      accessToken: accessToken,
      refreshToken: refreshToken,
      user: user,
      expiresAt: expiresAt,
      tokenType: json['token_type'] as String? ?? 'Bearer',
      scopes: scopes,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'user': user?.toJson(),
      'expires_at': expiresAt?.toIso8601String(),
      'token_type': tokenType,
      'scopes': scopes,
      'metadata': metadata,
    };
  }

  /// Convert to domain entity
  AuthResult toEntity() {
    // Try to get missing data from JWT token if needed
    UserModel? finalUser = user;
    DateTime? finalExpiresAt = expiresAt;

    if (finalUser == null || finalExpiresAt == null) {
      try {
        final jwtDecoder = JwtDecoderService();
        final jwtPayload = jwtDecoder.decodeToken(accessToken);

        if (jwtPayload != null) {
          // Fallback for expiresAt
          if (finalExpiresAt == null && jwtPayload.exp != null) {
            finalExpiresAt = DateTime.fromMillisecondsSinceEpoch(jwtPayload.exp! * 1000);
          }

          // Fallback for user info
          if (finalUser == null && jwtPayload.username != null) {
            finalUser = UserModel(
              id: jwtPayload.sub ?? '',
              username: jwtPayload.username ?? '',
              name: jwtPayload.username ?? '', // Use username as fallback for name
              code: '', // Default empty code
              subId: 0, // Default subId
              status: 'ACTIVE', // Default status
              mappings: [], // Default empty mappings
              currentTenantId: jwtPayload.currentTenantId,
              createdAt: jwtPayload.issuedTime,
              updatedAt: DateTime.now(),
            );
          }
        }
      } catch (e) {
        // JWT decode failed, continue with what we have
      }
    }

    // Ensure we have required fields for AuthResult entity
    if (finalUser == null || finalExpiresAt == null) {
      throw Exception('Cannot convert to AuthResult entity: missing required fields (user: ${finalUser != null}, expiresAt: ${finalExpiresAt != null})');
    }

    return AuthResult(
      accessToken: accessToken,
      refreshToken: refreshToken,
      user: finalUser.toEntity(),
      expiresAt: finalExpiresAt,
      tokenType: tokenType ?? 'Bearer',
      scopes: scopes ?? [],
      metadata: metadata ?? {},
    );
  }

  /// Create from domain entity
  factory AuthResultModel.fromEntity(AuthResult entity) {
    return AuthResultModel(
      accessToken: entity.accessToken,
      refreshToken: entity.refreshToken,
      user: entity.user != null ? UserModel.fromEntity(entity.user!) : null,
      expiresAt: entity.expiresAt,
      tokenType: entity.tokenType,
      scopes: entity.scopes,
      metadata: entity.metadata,
    );
  }

  /// Copy with new values
  AuthResultModel copyWith({
    String? accessToken,
    String? refreshToken,
    UserModel? user,
    DateTime? expiresAt,
    String? tokenType,
    List<String>? scopes,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResultModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      user: user ?? this.user,
      expiresAt: expiresAt ?? this.expiresAt,
      tokenType: tokenType ?? this.tokenType,
      scopes: scopes ?? this.scopes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if token is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    if (expiresAt == null) return false;
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt!);
  }

  /// Get time until expiration
  Duration? get timeUntilExpiration {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// Check if has specific scope
  bool hasScope(String scope) {
    return scopes?.contains(scope) ?? false;
  }

  /// Check if has any of the specified scopes
  bool hasAnyScope(List<String> requiredScopes) {
    if (scopes == null) return false;
    return requiredScopes.any((scope) => scopes!.contains(scope));
  }

  /// Check if has all of the specified scopes
  bool hasAllScopes(List<String> requiredScopes) {
    if (scopes == null) return false;
    return requiredScopes.every((scope) => scopes!.contains(scope));
  }

  /// Get metadata value
  T? getMetadata<T>(String key) {
    return metadata?[key] as T?;
  }

  /// Set metadata value
  AuthResultModel setMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata[key] = value;
    return copyWith(metadata: newMetadata);
  }

  /// Remove metadata key
  AuthResultModel removeMetadata(String key) {
    if (metadata == null) return this;
    final newMetadata = Map<String, dynamic>.from(metadata!);
    newMetadata.remove(key);
    return copyWith(metadata: newMetadata);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResultModel &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.user == user &&
        other.expiresAt == expiresAt &&
        other.tokenType == tokenType;
  }

  @override
  int get hashCode {
    return accessToken.hashCode ^
        refreshToken.hashCode ^
        user.hashCode ^
        expiresAt.hashCode ^
        tokenType.hashCode;
  }

  @override
  String toString() {
    return 'AuthResultModel(accessToken: [HIDDEN], refreshToken: ${refreshToken != null ? '[HIDDEN]' : 'null'}, user: $user, expiresAt: $expiresAt, tokenType: $tokenType, scopes: $scopes)';
  }

  /// Create a sanitized version for logging
  AuthResultModel sanitized() {
    return AuthResultModel(
      accessToken: '[HIDDEN]',
      refreshToken: refreshToken != null ? '[HIDDEN]' : null,
      user: user,
      expiresAt: expiresAt,
      tokenType: tokenType,
      scopes: scopes,
      metadata: metadata,
    );
  }

  /// Validate the auth result
  List<String> validate() {
    final errors = <String>[];

    if (accessToken.isEmpty) {
      errors.add('Access token is required');
    }

    if (tokenType != null && !['Bearer', 'Basic'].contains(tokenType)) {
      errors.add('Invalid token type: $tokenType');
    }

    if (expiresAt != null && expiresAt!.isBefore(DateTime.now())) {
      errors.add('Token is already expired');
    }

    return errors;
  }

  /// Check if auth result is valid
  bool get isValid => validate().isEmpty;
}

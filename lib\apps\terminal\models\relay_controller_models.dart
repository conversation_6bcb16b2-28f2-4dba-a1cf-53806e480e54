// Mock models for RelayController to avoid dependency issues
// These are simplified versions for the terminal app

abstract class RelayController {
  String get deviceId;
  String get deviceName;
  bool get isConnected;
  
  Future<void> triggerOn();
  Future<void> triggerOff();
  Future<String> getStatus();
  void dispose();
}

class HttpRelayController implements RelayController {
  @override
  final String deviceId;
  
  @override
  final String deviceName;
  
  final String urlOn;
  final String urlOff;
  
  bool _isConnected = false;
  String _lastStatus = 'unknown';

  HttpRelayController({
    required this.deviceId,
    required this.urlOn,
    required this.urlOff,
    required this.deviceName,
  });

  @override
  bool get isConnected => _isConnected;

  @override
  Future<void> triggerOn() async {
    // Mock implementation
    await Future.delayed(const Duration(milliseconds: 300));
    _lastStatus = 'on';
    _isConnected = true;
  }

  @override
  Future<void> triggerOff() async {
    // Mock implementation
    await Future.delayed(const Duration(milliseconds: 300));
    _lastStatus = 'off';
    _isConnected = true;
  }

  @override
  Future<String> getStatus() async {
    // Mock implementation
    await Future.delayed(const Duration(milliseconds: 100));
    _isConnected = true;
    return _lastStatus;
  }

  @override
  void dispose() {
    _isConnected = false;
  }
}

class SecureHttpRelayController implements RelayController {
  @override
  final String deviceId;
  
  @override
  final String deviceName;
  
  final String deviceType;
  final String serverBaseUrl;
  
  bool _isConnected = false;
  String _lastStatus = 'unknown';

  SecureHttpRelayController({
    required this.deviceId,
    required this.deviceType,
    required this.serverBaseUrl,
    required this.deviceName,
  });

  @override
  bool get isConnected => _isConnected;

  @override
  Future<void> triggerOn() async {
    // Mock implementation with security
    await Future.delayed(const Duration(milliseconds: 500));
    _lastStatus = 'on';
    _isConnected = true;
  }

  @override
  Future<void> triggerOff() async {
    // Mock implementation with security
    await Future.delayed(const Duration(milliseconds: 500));
    _lastStatus = 'off';
    _isConnected = true;
  }

  @override
  Future<String> getStatus() async {
    // Mock implementation with security
    await Future.delayed(const Duration(milliseconds: 200));
    _isConnected = true;
    return _lastStatus;
  }

  @override
  void dispose() {
    _isConnected = false;
  }
}

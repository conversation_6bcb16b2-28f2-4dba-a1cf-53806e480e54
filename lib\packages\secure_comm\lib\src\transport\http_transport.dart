import 'dart:convert';
import 'package:http/http.dart' as http;
import 'transport_interface.dart';
import '../models/secure_message.dart';
import '../models/device_registration.dart';
import '../exceptions.dart';

/// HTTP transport implementation for secure communication
class HttpTransport implements TransportInterface {
  /// Base URL for the server
  final String baseUrl;

  /// Request timeout duration
  final Duration timeout;

  /// Additional headers to include in requests
  final Map<String, String> headers;

  /// HTTP client instance
  final http.Client _client = http.Client();

  /// Creates a new HTTP transport
  HttpTransport(
    this.baseUrl, {
    this.timeout = const Duration(seconds: 30),
    this.headers = const {},
  });

  @override
  String get transportType => 'http';

  @override
  bool get isConnected => true; // HTTP is stateless

  @override
  Future<DeviceRegistrationResponse> registerDevice(
    DeviceRegistrationRequest request,
  ) async {
    try {
      final response = await _sendRequest(
        'POST',
        '/api/device/register',
        body: jsonEncode(request.toJson()),
        requireAuth: false,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return DeviceRegistrationResponse.fromJson(data);

    } catch (e) {
      throw DeviceRegistrationException('HTTP registration failed', e);
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await _sendRequest(
        'POST',
        '/api/device/refresh',
        body: jsonEncode({'refresh_token': refreshToken}),
        requireAuth: false,
      );

      return jsonDecode(response.body) as Map<String, dynamic>;

    } catch (e) {
      throw AuthenticationException('HTTP token refresh failed', e);
    }
  }

  @override
  Future<SecureResponse> sendMessage({
    required SecureMessage message,
    required String accessToken,
  }) async {
    try {
      final response = await _sendRequest(
        'POST',
        '/api/message',
        body: jsonEncode(message.toJson()),
        accessToken: accessToken,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return SecureResponse.fromJson(data);

    } catch (e) {
      throw TransportException('HTTP message send failed', e);
    }
  }

  @override
  Future<SecureResponse> sendPlainTextMessage(Map<String, dynamic> message) async {
    try {
      final response = await _sendRequest(
        'POST',
        '/api/message/plain',
        body: jsonEncode(message),
        requireAuth: false,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return SecureResponse.fromJson(data);

    } catch (e) {
      throw TransportException('HTTP plain text message send failed', e);
    }
  }

  @override
  Future<void> revokeCredentials(String accessToken) async {
    try {
      await _sendRequest(
        'POST',
        '/api/device/revoke',
        body: jsonEncode({'access_token': accessToken}),
        accessToken: accessToken,
      );
    } catch (e) {
      throw AuthenticationException('HTTP credential revocation failed', e);
    }
  }

  @override
  Future<void> dispose() async {
    _client.close();
  }

  /// Send HTTP request with error handling
  Future<http.Response> _sendRequest(
    String method,
    String endpoint,
    {
    String? body,
    String? accessToken,
    bool requireAuth = true,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      'User-Agent': 'SecureComm/1.0',
      ...headers,
    };

    // Add authorization header if provided
    if (accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $accessToken';
    }

    try {
      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(url, headers: requestHeaders).timeout(timeout);
          break;
        case 'POST':
          response = await _client.post(url, headers: requestHeaders, body: body).timeout(timeout);
          break;
        case 'PUT':
          response = await _client.put(url, headers: requestHeaders, body: body).timeout(timeout);
          break;
        case 'DELETE':
          response = await _client.delete(url, headers: requestHeaders).timeout(timeout);
          break;
        default:
          throw TransportException('Unsupported HTTP method: $method');
      }

      // Check for HTTP errors
      if (response.statusCode >= 400) {
        String errorMessage = 'HTTP ${response.statusCode}';
        
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = errorData['error'] as String? ?? errorMessage;
        } catch (_) {
          // Use default error message if response is not JSON
        }

        if (response.statusCode == 401) {
          throw AuthenticationException(errorMessage);
        } else if (response.statusCode == 403) {
          throw AuthorizationException(errorMessage);
        } else if (response.statusCode == 429) {
          throw RateLimitException(errorMessage);
        } else {
          throw ServerResponseException(errorMessage);
        }
      }

      return response;

    } on TimeoutException {
      throw TimeoutException('HTTP request timeout');
    } catch (e) {
      if (e is SecureCommException) {
        rethrow;
      }
      throw NetworkException('HTTP request failed', e);
    }
  }
}

/// HTTP transport with custom endpoints
class CustomHttpTransport extends HttpTransport {
  /// Custom endpoint mappings
  final Map<String, String> endpoints;

  CustomHttpTransport(
    super.baseUrl, {
    super.timeout,
    super.headers,
    this.endpoints = const {},
  });

  @override
  Future<DeviceRegistrationResponse> registerDevice(
    DeviceRegistrationRequest request,
  ) async {
    final endpoint = endpoints['register'] ?? '/api/device/register';
    
    try {
      final response = await _sendRequest(
        'POST',
        endpoint,
        body: jsonEncode(request.toJson()),
        requireAuth: false,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return DeviceRegistrationResponse.fromJson(data);

    } catch (e) {
      throw DeviceRegistrationException('Custom HTTP registration failed', e);
    }
  }

  @override
  Future<SecureResponse> sendMessage({
    required SecureMessage message,
    required String accessToken,
  }) async {
    // Use message type to determine endpoint
    final endpoint = endpoints[message.type] ?? 
                    endpoints['message'] ?? 
                    '/api/message';
    
    try {
      final response = await _sendRequest(
        'POST',
        endpoint,
        body: jsonEncode(message.toJson()),
        accessToken: accessToken,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return SecureResponse.fromJson(data);

    } catch (e) {
      throw TransportException('Custom HTTP message send failed', e);
    }
  }
}

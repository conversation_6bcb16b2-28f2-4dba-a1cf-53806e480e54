<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Relay Controller Test Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .device-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .device-card.online {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }

        .device-card.offline {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-name {
            font-weight: bold;
            font-size: 1.2em;
            color: #2c3e50;
        }

        .device-id {
            font-size: 0.9em;
            color: #6c757d;
            background: white;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .device-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.on {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .status-indicator.off {
            background: #dc3545;
        }

        .device-controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-on {
            background: #28a745;
            color: white;
        }

        .btn-on:hover {
            background: #218838;
        }

        .btn-off {
            background: #dc3545;
            color: white;
        }

        .btn-off:hover {
            background: #c82333;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1em;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
        }

        .register-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .logs-container {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-timestamp {
            color: #3498db;
        }

        .log-device {
            color: #e74c3c;
            font-weight: bold;
        }

        .log-action {
            color: #2ecc71;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
        }

        .connection-status.connected {
            background: #28a745;
        }

        .connection-status.disconnected {
            background: #dc3545;
        }

        .api-info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .api-info h3 {
            color: #004085;
            margin-bottom: 15px;
        }

        .api-endpoint {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #007bff;
        }

        .method {
            color: #28a745;
            font-weight: bold;
        }

        .method.post {
            color: #ffc107;
        }

        .command-center {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .command-group {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .command-group h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 16px;
        }

        .command-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .command-payload {
            background: #ffffff;
            border-radius: 6px;
            padding: 15px;
            border: 1px solid #dee2e6;
            margin-top: 10px;
        }

        .payload-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .payload-group label {
            color: #495057;
            font-size: 12px;
            font-weight: bold;
        }

        .form-control {
            background: #fff;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px 12px;
            color: #495057;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        textarea.form-control {
            min-height: 80px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* Face Recognition Styles */
        .settings-container {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .setting-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .setting-description {
            color: #6c757d;
            font-size: 0.9em;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .stats-row {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stat-label {
            color: #6c757d;
        }

        .stat-value {
            font-weight: bold;
            color: #2c3e50;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .pending-list, .recognition-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .pending-item, .recognition-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .pending-item {
            border-left: 4px solid #ffc107;
        }

        .recognition-item.recognized {
            border-left: 4px solid #28a745;
        }

        .recognition-item.rejected {
            border-left: 4px solid #dc3545;
        }

        .item-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .item-id {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .item-timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }

        .item-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn-approve {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .btn-approve:hover {
            background: #218838;
        }

        .btn-reject {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .btn-reject:hover {
            background: #c82333;
        }

        .user-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .user-info h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
        }

        .user-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
            font-size: 0.9em;
        }

        .user-detail {
            color: #495057;
        }

        .empty-state {
            text-align: center;
            color: #6c757d;
            padding: 40px;
            font-style: italic;
        }

        .face-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #dee2e6;
            margin-right: 15px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .face-image:hover {
            transform: scale(1.1);
            border-color: #007bff;
        }

        .item-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .item-details {
            flex: 1;
        }

        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .modal-image {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #bbb;
        }

        .gallery-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .face-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            max-height: 500px;
            overflow-y: auto;
        }

        .gallery-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .gallery-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .gallery-item.recognized {
            border-left: 4px solid #28a745;
        }

        .gallery-item.rejected {
            border-left: 4px solid #dc3545;
        }

        .gallery-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 8px;
        }

        .gallery-info {
            font-size: 0.8em;
            color: #6c757d;
        }

        .gallery-info .status {
            font-weight: bold;
            text-transform: uppercase;
        }

        .gallery-info .status.recognized {
            color: #28a745;
        }

        .gallery-info .status.rejected {
            color: #dc3545;
        }

        .gallery-info .user-name {
            color: #2c3e50;
            font-weight: 600;
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">Connecting...</div>
    
    <div class="container">
        <div class="header">
            <h1>� WebSocket Relay Controller Dashboard</h1>
            <p>Real-time device management via WebSocket communication</p>
        </div>

        <div class="main-content">
            <!-- API Information -->
            <div class="section">
                <h2>📡 API Endpoints</h2>
                <div class="api-info">
                    <h3>🔐 Secure API Endpoints</h3>
                    <div class="api-endpoint">
                        <span class="method post">POST</span> /api/device/register - Register device (get JWT + secret)
                    </div>
                    <div class="api-endpoint">
                        <span class="method post">POST</span> /api/device/refresh - Refresh access token
                    </div>
                    <div class="api-endpoint">
                        <span class="method post">POST</span> /relay/control - Secure relay control (JWT + HMAC)
                    </div>
                    <div class="api-endpoint">
                        <span class="method">GET</span> /relay/status - Get relay status (JWT required)
                    </div>
                    <div class="api-endpoint">
                        <span class="method">GET</span> /api/devices - List registered devices (JWT required)
                    </div>
                </div>
                <div class="api-info" style="margin-top: 20px;">
                    <h3>📡 Legacy API Endpoints</h3>
                    <div class="api-endpoint">
                        <span class="method">GET</span> /relay/on?deviceId=xxx - Turn relay on
                    </div>
                    <div class="api-endpoint">
                        <span class="method">GET</span> /relay/off?deviceId=xxx - Turn relay off
                    </div>
                    <div class="api-endpoint">
                        <span class="method">GET</span> /relay/status?deviceId=xxx - Get relay status
                    </div>
                    <div class="api-endpoint">
                        <span class="method post">POST</span> /register - Register a device (legacy)
                    </div>
                    <div class="api-endpoint">
                        <span class="method post">POST</span> /relay/control - Control relay via JSON
                    </div>
                </div>
                <div class="api-info" style="margin-top: 20px; background: #e8f5e8; border-color: #28a745;">
                    <h3>🔑 Security Features</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                        <div>✅ JWT Authentication</div>
                        <div>✅ HMAC Request Signing</div>
                        <div>✅ Replay Attack Prevention</div>
                        <div>✅ Device Scope Management</div>
                        <div>✅ Token Refresh & Revocation</div>
                        <div>✅ Hardware Fingerprinting</div>
                    </div>
                </div>
            </div>

            <!-- Device Registration -->
            <div class="section">
                <h2>📝 Register New Device</h2>
                <div class="register-form">
                    <div class="form-group">
                        <label for="deviceId">Device ID:</label>
                        <input type="text" id="deviceId" placeholder="e.g., relay-001">
                    </div>
                    <div class="form-group">
                        <label for="deviceName">Device Name:</label>
                        <input type="text" id="deviceName" placeholder="e.g., Living Room Relay">
                    </div>
                    <button class="btn btn-primary" onclick="registerDevice()">Register Device</button>

                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
                        <h3>🧪 WebSocket Test Commands</h3>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 10px;">
                            <button class="btn btn-secondary" onclick="sendPing()">🏓 Send Ping</button>
                            <button class="btn btn-secondary" onclick="getStatus()">📊 Get Status</button>
                            <button class="btn btn-secondary" onclick="controlRelay('default_relay', 'on')">🔌 Relay ON</button>
                            <button class="btn btn-secondary" onclick="controlRelay('default_relay', 'off')">⚡ Relay OFF</button>
                            <button class="btn btn-secondary" onclick="controlRelay('default_relay', 'toggle')">🔄 Toggle</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Device List -->
            <div class="section">
                <h2>🔌 Connected Devices</h2>
                <div class="device-grid" id="deviceGrid">
                    <div class="device-card">
                        <div class="device-header">
                            <div class="device-name">No devices registered</div>
                        </div>
                        <p>Register a device above or use the Flutter app to connect devices.</p>
                    </div>
                </div>
            </div>

            <!-- Command Center -->
            <div class="section">
                <h2>🎮 Command Center</h2>
                <div class="command-center">
                    <div class="command-group">
                        <h3>📡 Send Commands to Devices</h3>
                        <div class="command-form">
                            <select id="targetDevice" class="form-control">
                                <option value="">Select Device...</option>
                            </select>
                            <select id="commandType" class="form-control">
                                <option value="relay_control">Relay Control</option>
                                <option value="ping">Ping/Pong</option>
                                <option value="status_request">Status Request</option>
                                <option value="config_update">Config Update</option>
                                <option value="custom">Custom Command</option>
                            </select>
                            <div id="commandPayload" class="command-payload">
                                <div class="payload-group" id="relayPayload">
                                    <label>Action:</label>
                                    <select id="relayAction" class="form-control">
                                        <option value="on">Turn ON</option>
                                        <option value="off">Turn OFF</option>
                                        <option value="toggle">Toggle</option>
                                    </select>
                                    <label>Relay ID:</label>
                                    <input type="text" id="relayId" class="form-control" value="main_relay" placeholder="Relay ID">
                                </div>
                                <div class="payload-group" id="pingPayload" style="display:none;">
                                    <label>Message:</label>
                                    <input type="text" id="pingMessage" class="form-control" value="ping" placeholder="Ping message">
                                </div>
                                <div class="payload-group" id="statusPayload" style="display:none;">
                                    <label>Component:</label>
                                    <input type="text" id="statusComponent" class="form-control" value="relay" placeholder="Component to check">
                                </div>
                                <div class="payload-group" id="configPayload" style="display:none;">
                                    <label>Config JSON:</label>
                                    <textarea id="configJson" class="form-control" placeholder='{"setting": "value"}'></textarea>
                                </div>
                                <div class="payload-group" id="customPayload" style="display:none;">
                                    <label>Custom JSON:</label>
                                    <textarea id="customJson" class="form-control" placeholder='{"custom": "data"}'></textarea>
                                </div>
                            </div>
                            <button onclick="sendCommand()" class="btn btn-primary">Send Command</button>
                        </div>
                    </div>

                    <div class="command-group">
                        <h3>📊 Quick Actions</h3>
                        <div class="quick-actions">
                            <button onclick="pingAllDevices()" class="btn btn-info">Ping All Devices</button>
                            <button onclick="getDeviceStatuses()" class="btn btn-secondary">Get All Status</button>
                            <button onclick="emergencyStop()" class="btn btn-danger">Emergency Stop</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Face Recognition Management -->
            <div class="section">
                <h2>👤 Face Recognition Management</h2>

                <!-- Recognition Settings -->
                <div class="card">
                    <h3>⚙️ Recognition Settings</h3>
                    <div class="settings-container">
                        <div class="setting-item">
                            <label class="switch">
                                <input type="checkbox" id="autoRecognizeToggle" onchange="toggleAutoRecognize()">
                                <span class="slider"></span>
                            </label>
                            <span class="setting-label">Auto Recognition Mode</span>
                            <span class="setting-description" id="autoModeDescription">Automatically generate random user data</span>
                        </div>
                        <div class="stats-row">
                            <div class="stat-item">
                                <span class="stat-label">Pending Approvals:</span>
                                <span class="stat-value" id="pendingCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Recognitions (Manual Mode) -->
                <div class="card" id="pendingRecognitionsCard" style="display: none;">
                    <h3>⏳ Pending Recognition Requests</h3>
                    <div id="pendingRecognitionsList" class="pending-list">
                        <div class="empty-state">No pending recognition requests</div>
                    </div>
                </div>

                <!-- Recent Recognitions -->
                <div class="card">
                    <h3>📋 Recent Recognition Results</h3>
                    <div id="recentRecognitionsList" class="recognition-list">
                        <div class="empty-state">No recent recognitions</div>
                    </div>
                </div>

                <!-- Saved Face Images Gallery -->
                <div class="card">
                    <h3>🖼️ Saved Face Images</h3>
                    <div class="gallery-controls">
                        <button onclick="loadFaceImages()" class="btn btn-secondary">🔄 Refresh</button>
                        <span id="imageCount" class="stat-value">0 images</span>
                    </div>
                    <div id="faceImagesGallery" class="face-gallery">
                        <div class="empty-state">No saved face images</div>
                    </div>
                </div>
            </div>

            <!-- Activity Logs -->
            <div class="section">
                <h2>📊 Activity Logs</h2>
                <div class="logs-container" id="logsContainer">
                    <div class="log-entry">
                        <span class="log-timestamp">[System]</span> 
                        <span class="log-action">Server started</span> - Waiting for device connections...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <span class="close-modal" onclick="closeImageModal()">&times;</span>
        <div class="modal-content">
            <img id="modalImage" class="modal-image" src="" alt="Face Image">
        </div>
    </div>

    <script>
        // WebSocket connection
        let ws = null;
        const devices = new Map();
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        // Connection status
        const connectionStatus = document.getElementById('connectionStatus');

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = () => {
                    connectionStatus.textContent = 'Connected';
                    connectionStatus.className = 'connection-status connected';
                    addLog('System', 'CONNECT', 'WebSocket', 'Connected to server');
                    reconnectAttempts = 0;
                };

                ws.onclose = () => {
                    connectionStatus.textContent = 'Disconnected';
                    connectionStatus.className = 'connection-status disconnected';
                    addLog('System', 'DISCONNECT', 'WebSocket', 'Disconnected from server');

                    // Auto-reconnect
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        setTimeout(() => {
                            addLog('System', 'RECONNECT', 'WebSocket', `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`);
                            connectWebSocket();
                        }, 2000 * reconnectAttempts);
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    addLog('System', 'ERROR', 'WebSocket', 'Connection error');
                };

                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (error) {
                        console.error('Error parsing WebSocket message:', error);
                    }
                };

            } catch (error) {
                console.error('Failed to create WebSocket connection:', error);
                addLog('System', 'ERROR', 'WebSocket', 'Failed to connect');
            }
        }

        function handleWebSocketMessage(message) {
            const { type, data, deviceList } = message;

            switch (type) {
                case 'welcome':
                    if (deviceList) {
                        deviceList.forEach(device => {
                            devices.set(device.id, device);
                        });
                        updateDeviceGrid();
                    }
                    break;

                case 'deviceStateChanged':
                    if (data && data.deviceId && data.device) {
                        devices.set(data.deviceId, data.device);
                        updateDeviceGrid();
                    }
                    break;

                case 'deviceLog':
                    if (data && data.deviceId && data.log) {
                        addLog(data.deviceId, data.log.action, data.log.method, data.log.details);
                    }
                    break;

                case 'relay_status_update':
                    if (data) {
                        const deviceId = data.device_id || data.relay_id || 'default_relay';
                        addLog(deviceId, 'STATUS_UPDATE', 'WebSocket', `Status: ${data.status}`);

                        // Update device state if we have the device info
                        if (data.device) {
                            devices.set(deviceId, data.device);
                            updateDeviceGrid();
                        } else if (devices.has(deviceId)) {
                            // Update just the state if we don't have full device info
                            const device = devices.get(deviceId);
                            device.state = data.status === 'on' || data.status === 'unlock';
                            device.lastUpdated = new Date().toISOString();
                            devices.set(deviceId, device);
                            updateDeviceGrid();
                        }
                    }
                    break;

                case 'response':
                    // Handle command responses
                    if (data) {
                        const deviceId = data.device_id || data.relay_id || 'Server';
                        if (data.success) {
                            addLog(deviceId, 'COMMAND_SUCCESS', 'WebSocket', `Response: ${JSON.stringify(data.data || {})}`);
                        } else {
                            addLog(deviceId, 'COMMAND_ERROR', 'WebSocket', `Error: ${data.error}`);
                        }
                    }
                    break;

                case 'faceRecognition':
                    // Handle face recognition events
                    if (data) {
                        const deviceId = data.device_id || 'Unknown';
                        if (data.recognized) {
                            addLog(deviceId, 'FACE_RECOGNIZED', 'Face Recognition',
                                  `User: ${data.user?.name || 'Unknown'} (${(data.confidence * 100).toFixed(1)}%)`);

                            // Add to recent recognitions
                            recentRecognitions.unshift({
                                ...data,
                                timestamp: new Date().toISOString()
                            });
                            renderRecentRecognitions();
                        } else {
                            addLog(deviceId, 'FACE_PENDING', 'Face Recognition',
                                  `Recognition pending approval: ${data.recognitionId?.substring(0, 8) || 'Unknown'}`);

                            // Refresh pending list if in manual mode
                            if (!faceRecognitionSettings.autoRecognize) {
                                loadPendingRecognitions();
                                loadFaceRecognitionSettings();
                            }
                        }
                    }
                    break;

                default:
                    console.log('Unknown message type:', type, message);
            }
        }

        function sendWebSocketMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
            } else {
                addLog('System', 'ERROR', 'WebSocket', 'Not connected to server');
            }
        }

        // Face Recognition Management
        let faceRecognitionSettings = { autoRecognize: true, pendingCount: 0 };
        let pendingRecognitions = [];
        let recentRecognitions = [];

        // Load face recognition settings
        async function loadFaceRecognitionSettings() {
            try {
                const response = await fetch('/api/face/settings');
                const data = await response.json();
                if (data.success) {
                    faceRecognitionSettings = data.data;
                    updateFaceRecognitionUI();
                }
            } catch (error) {
                console.error('Error loading face recognition settings:', error);
            }
        }

        // Toggle auto recognition mode
        async function toggleAutoRecognize() {
            const toggle = document.getElementById('autoRecognizeToggle');
            const autoRecognize = toggle.checked;

            try {
                const response = await fetch('/api/face/settings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ autoRecognize })
                });

                const data = await response.json();
                if (data.success) {
                    faceRecognitionSettings = data.data;
                    updateFaceRecognitionUI();
                    addLog('System', 'SETTINGS', 'Face Recognition', `Auto mode: ${autoRecognize ? 'ON' : 'OFF'}`);
                }
            } catch (error) {
                console.error('Error updating face recognition settings:', error);
                // Revert toggle on error
                toggle.checked = !autoRecognize;
            }
        }

        // Update face recognition UI
        function updateFaceRecognitionUI() {
            const toggle = document.getElementById('autoRecognizeToggle');
            const description = document.getElementById('autoModeDescription');
            const pendingCount = document.getElementById('pendingCount');
            const pendingCard = document.getElementById('pendingRecognitionsCard');

            toggle.checked = faceRecognitionSettings.autoRecognize;
            pendingCount.textContent = faceRecognitionSettings.pendingCount;

            if (faceRecognitionSettings.autoRecognize) {
                description.textContent = 'Automatically generate random user data';
                pendingCard.style.display = 'none';
            } else {
                description.textContent = 'Manual approval required for recognition';
                pendingCard.style.display = 'block';
                loadPendingRecognitions();
            }
        }

        // Load pending recognitions
        async function loadPendingRecognitions() {
            try {
                const response = await fetch('/api/face/pending');
                const data = await response.json();
                if (data.success) {
                    pendingRecognitions = data.data;
                    renderPendingRecognitions();
                }
            } catch (error) {
                console.error('Error loading pending recognitions:', error);
            }
        }

        // Render pending recognitions list
        function renderPendingRecognitions() {
            const container = document.getElementById('pendingRecognitionsList');

            if (pendingRecognitions.length === 0) {
                container.innerHTML = '<div class="empty-state">No pending recognition requests</div>';
                return;
            }

            container.innerHTML = pendingRecognitions.map(item => `
                <div class="pending-item">
                    <div class="item-header">
                        <span class="item-id">${item.recognitionId.substring(0, 8)}...</span>
                        <span class="item-timestamp">${new Date(item.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="item-content">
                        ${item.savedImagePath || item.imageDataUrl ? `
                            <img src="${item.savedImagePath || item.imageDataUrl}"
                                 class="face-image"
                                 alt="Face Image"
                                 onclick="showImageModal('${item.savedImagePath || item.imageDataUrl}')"
                                 title="Click to view full size">
                        ` : ''}
                        <div class="item-details">
                            <div>
                                <strong>Device:</strong> ${item.deviceId}<br>
                                <strong>Status:</strong> ${item.status}
                            </div>
                            <div class="item-actions">
                                <button class="btn-approve" onclick="approveRecognition('${item.recognitionId}', true)">
                                    ✅ Approve
                                </button>
                                <button class="btn-reject" onclick="approveRecognition('${item.recognitionId}', false)">
                                    ❌ Reject
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Approve or reject recognition
        async function approveRecognition(recognitionId, approved) {
            try {
                const response = await fetch(`/api/face/approve/${recognitionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ approved })
                });

                const data = await response.json();
                if (data.success) {
                    // Add to recent recognitions
                    recentRecognitions.unshift({
                        ...data.data,
                        action: approved ? 'approved' : 'rejected'
                    });

                    // Refresh pending list
                    loadPendingRecognitions();
                    loadFaceRecognitionSettings();
                    renderRecentRecognitions();

                    addLog('Admin', approved ? 'APPROVE' : 'REJECT', 'Face Recognition',
                          `Recognition ${recognitionId.substring(0, 8)} ${approved ? 'approved' : 'rejected'}`);
                }
            } catch (error) {
                console.error('Error approving recognition:', error);
            }
        }

        // Render recent recognitions
        function renderRecentRecognitions() {
            const container = document.getElementById('recentRecognitionsList');

            if (recentRecognitions.length === 0) {
                container.innerHTML = '<div class="empty-state">No recent recognitions</div>';
                return;
            }

            container.innerHTML = recentRecognitions.slice(0, 10).map(item => `
                <div class="recognition-item ${item.recognized ? 'recognized' : 'rejected'}">
                    <div class="item-header">
                        <span class="item-id">${item.recognitionId.substring(0, 8)}...</span>
                        <span class="item-timestamp">${new Date(item.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="item-content">
                        ${item.savedImagePath || item.imageDataUrl ? `
                            <img src="${item.savedImagePath || item.imageDataUrl}"
                                 class="face-image"
                                 alt="Face Image"
                                 onclick="showImageModal('${item.savedImagePath || item.imageDataUrl}')"
                                 title="Click to view full size">
                        ` : ''}
                        <div class="item-details">
                            <div>
                                <strong>Status:</strong> ${item.recognized ? '✅ Recognized' : '❌ Not Recognized'}<br>
                                ${item.confidence ? `<strong>Confidence:</strong> ${(item.confidence * 100).toFixed(1)}%<br>` : ''}
                                ${item.action ? `<strong>Action:</strong> ${item.action}<br>` : ''}
                            </div>
                            ${item.user ? `
                                <div class="user-info">
                                    <h4>👤 ${item.user.name}</h4>
                                    <div class="user-details">
                                        <div class="user-detail"><strong>ID:</strong> ${item.user.employeeId || 'N/A'}</div>
                                        <div class="user-detail"><strong>Department:</strong> ${item.user.department || 'N/A'}</div>
                                        <div class="user-detail"><strong>Position:</strong> ${item.user.position || 'N/A'}</div>
                                        <div class="user-detail"><strong>Access Level:</strong> ${item.user.accessLevel || 'N/A'}</div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Image modal functions
        function showImageModal(imageUrl) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageUrl;
            modal.style.display = 'block';
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        // Load saved face images
        async function loadFaceImages() {
            try {
                const response = await fetch('/api/face/images');
                const data = await response.json();
                if (data.success) {
                    renderFaceImagesGallery(data.data);
                    document.getElementById('imageCount').textContent = `${data.total} images`;
                }
            } catch (error) {
                console.error('Error loading face images:', error);
            }
        }

        // Render face images gallery
        function renderFaceImagesGallery(images) {
            const container = document.getElementById('faceImagesGallery');

            if (images.length === 0) {
                container.innerHTML = '<div class="empty-state">No saved face images</div>';
                return;
            }

            container.innerHTML = images.map(image => `
                <div class="gallery-item ${image.status}">
                    <img src="${image.path}"
                         class="gallery-image"
                         alt="Face Image"
                         onclick="showImageModal('${image.path}')"
                         title="Click to view full size">
                    <div class="gallery-info">
                        <div class="status ${image.status}">${image.status}</div>
                        <div class="user-name">${image.userName.replace(/_/g, ' ')}</div>
                        <div>ID: ${image.recognitionId}</div>
                        <div>${new Date(image.created).toLocaleString()}</div>
                        <div>${(image.size / 1024).toFixed(1)} KB</div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize connection
        connectWebSocket();

        // Initialize face recognition
        loadFaceRecognitionSettings();

        // Load face images gallery
        loadFaceImages();

        // Register device
        function registerDevice() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const deviceName = document.getElementById('deviceName').value.trim();

            if (!deviceId) {
                alert('Please enter a device ID');
                return;
            }

            sendWebSocketMessage({
                type: 'registerDevice',
                message_id: `reg_${Date.now()}`,
                payload: {
                    deviceId,
                    deviceName: deviceName || `Device ${deviceId}`,
                    type: 'relay'
                }
            });

            // Clear form
            document.getElementById('deviceId').value = '';
            document.getElementById('deviceName').value = '';
        }

        // Control relay
        function controlRelay(deviceId, command) {
            sendWebSocketMessage({
                type: 'relay_control',
                message_id: `relay_${Date.now()}`,
                payload: {
                    action: command,
                    deviceId: deviceId
                }
            });
        }

        // Send ping
        function sendPing() {
            sendWebSocketMessage({
                type: 'ping',
                message_id: `ping_${Date.now()}`,
                payload: {
                    timestamp: Date.now()
                }
            });
        }

        // Get status
        function getStatus() {
            sendWebSocketMessage({
                type: 'status_request',
                message_id: `status_${Date.now()}`,
                payload: {
                    component: 'relay'
                }
            });
        }



        // Add log entry
        function addLog(deviceId, action, method, details) {
            const logsContainer = document.getElementById('logsContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleTimeString();
            const detailsStr = typeof details === 'object' ? JSON.stringify(details) : details;
            
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-device">${deviceId}</span>
                <span class="log-action">${action}</span>
                via ${method} - ${detailsStr}
            `;
            
            logsContainer.insertBefore(logEntry, logsContainer.firstChild);
            
            // Keep only last 50 log entries
            while (logsContainer.children.length > 50) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        // Allow Enter key to register device
        document.getElementById('deviceName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                registerDevice();
            }
        });

        document.getElementById('deviceId').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                registerDevice();
            }
        });

        // Command Center Functions

        // Update command payload UI based on command type
        document.getElementById('commandType').addEventListener('change', (e) => {
            const commandType = e.target.value;
            const payloadGroups = document.querySelectorAll('.payload-group');

            payloadGroups.forEach(group => group.style.display = 'none');

            switch(commandType) {
                case 'relay_control':
                    document.getElementById('relayPayload').style.display = 'block';
                    break;
                case 'ping':
                    document.getElementById('pingPayload').style.display = 'block';
                    break;
                case 'status_request':
                    document.getElementById('statusPayload').style.display = 'block';
                    break;
                case 'config_update':
                    document.getElementById('configPayload').style.display = 'block';
                    break;
                case 'custom':
                    document.getElementById('customPayload').style.display = 'block';
                    break;
            }
        });

        // Send command to device
        function sendCommand() {
            const deviceId = document.getElementById('targetDevice').value;
            const commandType = document.getElementById('commandType').value;

            if (!deviceId) {
                alert('Please select a device');
                return;
            }

            if (!commandType) {
                alert('Please select a command type');
                return;
            }

            let payload = {};

            switch(commandType) {
                case 'relay_control':
                    payload = {
                        action: document.getElementById('relayAction').value,
                        relay_id: document.getElementById('relayId').value || 'main_relay'
                    };
                    break;
                case 'ping':
                    payload = {
                        message: document.getElementById('pingMessage').value || 'ping'
                    };
                    break;
                case 'status_request':
                    payload = {
                        component: document.getElementById('statusComponent').value || 'relay'
                    };
                    break;
                case 'config_update':
                    try {
                        payload = JSON.parse(document.getElementById('configJson').value || '{}');
                    } catch (e) {
                        alert('Invalid JSON in config payload');
                        return;
                    }
                    break;
                case 'custom':
                    try {
                        payload = JSON.parse(document.getElementById('customJson').value || '{}');
                    } catch (e) {
                        alert('Invalid JSON in custom payload');
                        return;
                    }
                    break;
            }

            // Send command via WebSocket
            sendWebSocketMessage({
                type: commandType,
                message_id: `cmd_${Date.now()}`,
                payload: {
                    ...payload,
                    deviceId: deviceId
                }
            });

            addLog(deviceId, `COMMAND_SENT`, 'WebSocket', `${commandType}: ${JSON.stringify(payload)}`);
        }

        // Quick action functions
        function pingAllDevices() {
            devices.forEach((device, deviceId) => {
                sendWebSocketMessage({
                    type: 'ping',
                    message_id: `ping_all_${Date.now()}_${deviceId}`,
                    payload: {
                        message: 'ping_all',
                        deviceId: deviceId
                    }
                });
            });
            addLog('ALL', 'PING_ALL', 'WebSocket', 'Ping sent to all devices');
        }

        function getDeviceStatuses() {
            devices.forEach((device, deviceId) => {
                sendWebSocketMessage({
                    type: 'status_request',
                    message_id: `status_all_${Date.now()}_${deviceId}`,
                    payload: {
                        component: 'all',
                        deviceId: deviceId
                    }
                });
            });
            addLog('ALL', 'STATUS_REQUEST', 'WebSocket', 'Status request sent to all devices');
        }

        function emergencyStop() {
            if (confirm('Are you sure you want to send emergency stop to all devices?')) {
                devices.forEach((device, deviceId) => {
                    sendWebSocketMessage({
                        type: 'relay_control',
                        message_id: `emergency_${Date.now()}_${deviceId}`,
                        payload: {
                            action: 'off',
                            relay_id: 'all',
                            emergency: true,
                            deviceId: deviceId
                        }
                    });
                });
                addLog('ALL', 'EMERGENCY_STOP', 'WebSocket', 'Emergency stop sent to all devices');
            }
        }

        // Update device select when devices change
        function updateDeviceSelect() {
            const select = document.getElementById('targetDevice');
            select.innerHTML = '<option value="">Select Device...</option>';

            devices.forEach((device, deviceId) => {
                const option = document.createElement('option');
                option.value = deviceId;
                option.textContent = `${device.name} (${deviceId})`;
                select.appendChild(option);
            });
        }

        // Enhanced device grid with terminal-relay hierarchy support
        function updateDeviceGrid() {
            const grid = document.getElementById('deviceGrid');

            if (devices.size === 0) {
                grid.innerHTML = `
                    <div class="device-card">
                        <div class="device-header">
                            <div class="device-name">No devices registered</div>
                        </div>
                        <p>Register a device above or use the Flutter app to connect devices.</p>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="generateTerminalId()">Generate Terminal ID</button>
                        </div>
                    </div>
                `;
                updateDeviceSelect();
                return;
            }

            // Group devices by terminal
            const grouped = groupDevicesByTerminal();
            grid.innerHTML = '';

            // Display terminal groups
            for (const [terminalId, terminalData] of Object.entries(grouped.terminals)) {
                const terminalGroup = document.createElement('div');
                terminalGroup.className = 'terminal-group';
                terminalGroup.style.cssText = `
                    margin-bottom: 30px;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    padding: 20px;
                    background: linear-gradient(135deg, #ebf3fd 0%, #d6e8fc 100%);
                `;

                // Terminal card
                const terminal = terminalData.terminal;
                const relays = terminalData.relays || [];
                
                terminalGroup.innerHTML = `
                    <div class="terminal-header" style="margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #3498db;">
                        <h3 style="color: #2c3e50; margin: 0;">${terminal.displayName || terminal.name}</h3>
                        <div style="font-size: 0.9em; color: #7f8c8d;">Terminal ID: ${terminal.id}</div>
                        <div style="font-size: 0.8em; color: #27ae60;">
                            ${terminal.namingValid ? '✓ Valid naming format' : '⚠ Legacy format'}
                        </div>
                    </div>
                    <div class="relay-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 15px;">
                        ${relays.map(relay => `
                            <div class="device-card relay-card ${relay.state ? 'online' : 'offline'}" style="margin: 0;">
                                <div class="device-header">
                                    <div class="device-name">${relay.displayName || relay.name}</div>
                                    <div class="device-id" style="font-size: 0.8em;">${relay.id}</div>
                                </div>
                                <div class="device-status">
                                    <div class="status-indicator ${relay.state ? 'on' : 'off'}"></div>
                                    <span>Status: ${relay.state ? 'ON' : 'OFF'}</span>
                                </div>
                                <div class="device-controls">
                                    <button class="btn btn-on" onclick="controlRelay('${relay.id}', 'on')">
                                        Turn ON
                                    </button>
                                    <button class="btn btn-off" onclick="controlRelay('${relay.id}', 'off')">
                                        Turn OFF
                                    </button>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.8em; color: #6c757d;">
                                    Last updated: ${new Date(relay.lastUpdated).toLocaleString()}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                grid.appendChild(terminalGroup);
            }

            // Display ungrouped devices
            if (grouped.ungrouped.length > 0) {
                const ungroupedSection = document.createElement('div');
                ungroupedSection.innerHTML = `
                    <h3 style="color: #e67e22; margin-bottom: 15px;">Legacy/Ungrouped Devices</h3>
                    <div class="ungrouped-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">
                        ${grouped.ungrouped.map(device => `
                            <div class="device-card ${device.state ? 'online' : 'offline'}">
                                <div class="device-header">
                                    <div class="device-name">${device.displayName || device.name}</div>
                                    <div class="device-id">${device.id}</div>
                                </div>
                                <div class="device-status">
                                    <div class="status-indicator ${device.state ? 'on' : 'off'}"></div>
                                    <span>Status: ${device.state ? 'ON' : 'OFF'}</span>
                                </div>
                                <div class="device-controls">
                                    <button class="btn btn-on" onclick="controlRelay('${device.id}', 'on')">
                                        Turn ON
                                    </button>
                                    <button class="btn btn-off" onclick="controlRelay('${device.id}', 'off')">
                                        Turn OFF
                                    </button>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.8em; color: #6c757d;">
                                    Last updated: ${new Date(device.lastUpdated).toLocaleString()}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                grid.appendChild(ungroupedSection);
            }

            updateDeviceSelect();
        }

        // Group devices by terminal for display
        function groupDevicesByTerminal() {
            const terminals = {};
            const ungrouped = [];

            devices.forEach(device => {
                if (device.isTerminal) {
                    if (!terminals[device.id]) {
                        terminals[device.id] = {
                            terminal: device,
                            relays: []
                        };
                    }
                } else if (device.isRelay && device.terminalId) {
                    if (!terminals[device.terminalId]) {
                        terminals[device.terminalId] = {
                            terminal: { id: device.terminalId, name: `Terminal ${device.terminalId}`, displayName: `Terminal ${device.terminalId}` },
                            relays: []
                        };
                    }
                    terminals[device.terminalId].relays.push(device);
                } else {
                    ungrouped.push(device);
                }
            });

            return { terminals, ungrouped };
        }

        // Generate new terminal ID
        function generateTerminalId() {
            fetch('/api/naming/generate-terminal-id')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Generated Terminal ID: ${data.terminal_id}\n\nRelays will be named:\n${data.naming_summary.relays.numbered.map(r => `${r.id} (${r.displayName})`).join('\n')}`);
                    }
                })
                .catch(error => console.error('Error generating terminal ID:', error));
        }
    </script>
</body>
</html>

# Device Management Implementation - Always-On & Screen Brightness Control

## Tổng quan

Đã triển khai hệ thống quản lý thiết bị toàn diện để đảm bảo ứng dụng terminal luôn hoạt động và có khả năng điều chỉnh độ sáng màn hình.

## Các tính năng chính

### 🔒 **Always-On Functionality**
- ✅ Wake lock để giữ màn hình luôn sáng
- ✅ Foreground service để app không bị kill
- ✅ Auto-start khi boot device
- ✅ Battery optimization exemption
- ✅ Show when locked & turn screen on

### 💡 **Screen Brightness Control**
- ✅ Programmatic brightness control (0-100%)
- ✅ Save/restore brightness settings
- ✅ System brightness integration
- ✅ Real-time brightness adjustment

### 🚀 **Auto-Start & Boot Integration**
- ✅ Boot receiver for auto-start
- ✅ Multiple boot action support
- ✅ Service restart on failure
- ✅ Persistent operation

## Android Permissions & Configuration

### Permissions Added
```xml
<!-- Device management permissions -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.WRITE_SETTINGS" />
<uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />

<!-- Screen brightness control -->
<uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />

<!-- Device admin permissions -->
<uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
<uses-permission android:name="android.permission.DEVICE_POWER" />

<!-- Boot receiver -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- Foreground service -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" />

<!-- Battery optimization -->
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
```

### Activity Configuration
```xml
<activity
    android:name="com.ccam.terminal.MainActivity"
    android:keepScreenOn="true"
    android:showWhenLocked="true"
    android:turnScreenOn="true"
    android:excludeFromRecents="false"
    android:clearTaskOnLaunch="false"
    android:stateNotNeeded="true">
```

## Native Android Components

### 1. BootReceiver
**File**: `android/app/src/terminal/kotlin/com/ccam/terminal/BootReceiver.kt`

```kotlin
class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                startTerminalApp(context)
            }
        }
    }
}
```

**Chức năng**:
- Tự động start app khi device boot
- Support multiple boot actions
- Start keep-alive service

### 2. KeepAliveService
**File**: `android/app/src/terminal/kotlin/com/ccam/terminal/KeepAliveService.kt`

```kotlin
class KeepAliveService : Service() {
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        return START_STICKY // Restart if killed
    }
}
```

**Chức năng**:
- Foreground service với notification
- Wake lock management
- Auto-restart khi bị kill
- Persistent background operation

### 3. DeviceAdminReceiver
**File**: `android/app/src/terminal/kotlin/com/ccam/terminal/DeviceAdminReceiver.kt`

```kotlin
class DeviceAdminReceiver : DeviceAdminReceiver() {
    // Advanced device control capabilities
    // Password management, lock screen control, etc.
}
```

### 4. Enhanced MainActivity
**File**: `android/app/src/terminal/kotlin/com/ccam/terminal/MainActivity.kt`

```kotlin
class MainActivity : FlutterActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Keep screen on and show when locked
        window.addFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
        )
        
        startKeepAliveService()
    }
}
```

## Flutter Services

### DeviceManagementService
**File**: `lib/shared/services/device_management_service.dart`

#### Core Features
```dart
class DeviceManagementService {
  // Wake lock management
  Future<void> enableWakeLock();
  Future<void> disableWakeLock();
  
  // Screen brightness control
  Future<void> setBrightness(double brightness); // 0.0 to 1.0
  Future<double> getBrightness();
  Future<void> resetBrightness();
  
  // Battery optimization
  Future<void> _requestBatteryOptimizationExemption();
  
  // Keep-alive mechanisms
  Future<void> _startKeepAliveService();
  void _setupKeepAliveTimer();
}
```

#### Initialization Process
```dart
Future<void> initialize() async {
  // Load saved settings
  await _loadSettings();
  
  // Enable wake lock
  await enableWakeLock();
  
  // Restore brightness
  await _restoreBrightness();
  
  // Request battery optimization exemption
  await _requestBatteryOptimizationExemption();
  
  // Start keep-alive service
  await _startKeepAliveService();
  
  // Setup periodic keep-alive checks
  _setupKeepAliveTimer();
}
```

### DeviceManagementWidget
**File**: `lib/shared/widgets/device_management_widget.dart`

#### UI Controls
- **Brightness Slider**: Real-time brightness adjustment
- **Wake Lock Toggle**: Enable/disable screen always-on
- **Device Info Display**: Platform, model, version info
- **Control Buttons**: Reset brightness, refresh status

```dart
Widget _buildBrightnessControl() {
  return Slider(
    value: _brightness,
    min: 0.1,
    max: 1.0,
    divisions: 9,
    onChanged: _setBrightness,
  );
}
```

## Flutter Packages Added

```yaml
dependencies:
  # Device management
  wakelock_plus: ^1.2.8          # Keep screen awake
  screen_brightness: ^1.0.1       # Control screen brightness
  device_apps: ^2.2.0            # App management
  flutter_background_service: ^5.0.10  # Background service
  auto_start_flutter: ^0.1.1     # Auto-start on boot
  system_settings: ^3.0.0        # System settings access
  flutter_window_manager: ^0.2.0 # Window management
  disable_battery_optimization: ^1.1.1  # Battery optimization
  
  # Kiosk mode
  kiosk_mode: ^2.0.0             # Kiosk mode functionality
  android_intent_plus: ^5.1.0    # Android intents
```

## Integration với Terminal App

### Stream Screen Integration
```dart
class _StreamScreenState extends State<StreamScreen> {
  final DeviceManagementService _deviceManagementService = 
      DeviceManagementService.instance;
  
  @override
  void initState() {
    super.initState();
    _initializeDeviceManagement();
  }
  
  Future<void> _initializeDeviceManagement() async {
    await _deviceManagementService.initialize();
  }
  
  @override
  void dispose() {
    _deviceManagementService.dispose();
    super.dispose();
  }
}
```

## Usage Examples

### Basic Device Management
```dart
final deviceService = DeviceManagementService.instance;

// Initialize service
await deviceService.initialize();

// Control brightness
await deviceService.setBrightness(0.8); // 80% brightness

// Enable wake lock
await deviceService.enableWakeLock();

// Get device info
final deviceInfo = await deviceService.getDeviceInfo();
```

### Brightness Control
```dart
// Set brightness to 50%
await deviceService.setBrightness(0.5);

// Get current brightness
final currentBrightness = await deviceService.getBrightness();

// Reset to system default
await deviceService.resetBrightness();
```

### Wake Lock Management
```dart
// Keep screen always on
await deviceService.enableWakeLock();

// Allow screen to turn off
await deviceService.disableWakeLock();

// Check status
final isEnabled = deviceService.isWakeLockEnabled;
```

## Configuration & Settings

### Brightness Settings
- **Range**: 0.1 to 1.0 (10% to 100%)
- **Divisions**: 9 steps for UI slider
- **Persistence**: Saved to SharedPreferences
- **Auto-restore**: On app restart

### Keep-Alive Settings
```dart
static const Duration _keepAliveInterval = Duration(minutes: 5);
static const String _wakeLockTag = "TerminalApp:KeepAlive";
static const int _notificationId = 1001;
```

### Service Configuration
```dart
// Foreground service type
android:foregroundServiceType="systemExempted"

// Service restart policy
return START_STICKY; // Auto-restart if killed
```

## Testing & Validation

### Test Scenarios
1. **Boot Test**: Restart device, verify auto-start
2. **Kill Test**: Force kill app, verify service restart
3. **Brightness Test**: Adjust brightness, verify persistence
4. **Wake Lock Test**: Enable/disable, verify screen behavior
5. **Battery Test**: Long-running operation, verify no kill
6. **Lock Screen Test**: Lock device, verify app shows when unlocked

### Expected Behaviors
- ✅ App auto-starts on device boot
- ✅ Screen stays on when wake lock enabled
- ✅ Brightness changes persist across restarts
- ✅ Service restarts if killed by system
- ✅ App shows on lock screen when needed
- ✅ Battery optimization exemption requested

## Troubleshooting

### Common Issues
1. **App not auto-starting**: Check boot receiver permissions
2. **Screen turning off**: Verify wake lock permissions
3. **Brightness not changing**: Check WRITE_SETTINGS permission
4. **Service killed**: Request battery optimization exemption
5. **Lock screen issues**: Verify SHOW_WHEN_LOCKED permission

### Debug Commands
```bash
# Check if app has battery optimization exemption
adb shell dumpsys deviceidle whitelist

# Check wake lock status
adb shell dumpsys power

# Check running services
adb shell dumpsys activity services
```

Hệ thống này đảm bảo terminal app hoạt động **24/7** với khả năng **điều chỉnh độ sáng** linh hoạt và **tự động khởi động** khi cần thiết.

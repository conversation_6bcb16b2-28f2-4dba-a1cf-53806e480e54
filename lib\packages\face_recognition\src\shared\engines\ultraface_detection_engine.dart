import 'dart:typed_data';
import 'dart:ui';
import 'dart:math' as math;

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:image/image.dart' as img;

import '../../core/interfaces/face_detection_engine.dart';
import '../../core/models/face_recognition_config.dart';
import '../utils/image_utils.dart';

/// UltraFace detection engine optimized for embedded devices
class UltraFaceDetectionEngine implements FaceDetectionEngine {
  static const String _modelAssetPath = 'packages/face_recognition/assets/models/ultraface_320.tflite';
  
  Interpreter? _interpreter;
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  
  // Model parameters
  static const int _inputWidth = 320;
  static const int _inputHeight = 240;
  static const int _numAnchors = 4420;
  static const double _confidenceThreshold = 0.7;
  static const double _nmsThreshold = 0.3;
  
  // Pre-allocated buffers for performance
  Float32List? _inputBuffer;
  Float32List? _scoresBuffer;
  Float32List? _boxesBuffer;
  
  final FaceRecognitionConfig _faceConfig;
  
  UltraFaceDetectionEngine({required FaceRecognitionConfig config}) : _faceConfig = config;
  
  @override
  String get engineName => 'UltraFace';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing UltraFace Detection Engine');
      }
      
      // Load TFLite model
      _interpreter = await Interpreter.fromAsset(_modelAssetPath);
      
      // Configure interpreter options
      final options = InterpreterOptions();
      if (_faceConfig.enableGPUAcceleration) {
        options.addDelegate(GpuDelegate());
      }
      options.threads = _faceConfig.processingThreads;
      
      // Allocate tensors
      _interpreter!.allocateTensors();
      
      // Pre-allocate buffers
      _inputBuffer = Float32List(_inputWidth * _inputHeight * 3);
      _scoresBuffer = Float32List(_numAnchors * 2);
      _boxesBuffer = Float32List(_numAnchors * 4);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ UltraFace engine initialized successfully');
        print('   Input size: ${_inputWidth}x${_inputHeight}');
        print('   GPU acceleration: ${_faceConfig.enableGPUAcceleration}');
        print('   Threads: ${_faceConfig.processingThreads}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize UltraFace engine: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<List<FaceDetection>> detectFaces(Uint8List imageBytes) async {
    if (!_isInitialized) {
      throw FaceDetectionException('UltraFace engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Decode and preprocess image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw FaceDetectionException('Failed to decode image', engineName: engineName);
      }
      
      // Preprocess image
      _preprocessImage(image);
      
      // Run inference
      final outputs = <int, Object>{
        0: _scoresBuffer!,
        1: _boxesBuffer!,
      };
      
      _interpreter!.runForMultipleInputs([_inputBuffer], outputs);
      
      // Post-process results
      final faces = _postprocessResults(image.width, image.height);
      
      stopwatch.stop();
      
      if (kDebugMode) {
        print('🔍 UltraFace detection completed in ${stopwatch.elapsedMilliseconds}ms');
        print('   Detected faces: ${faces.length}');
      }
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ UltraFace detection failed: $e');
      }
      throw FaceDetectionException('Detection failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  @override
  Future<List<FaceDetection>> processCameraImage(CameraImage image) async {
    if (!_isInitialized) {
      throw FaceDetectionException('UltraFace engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Convert camera image to RGB
      final rgbImage = ImageUtils.convertCameraImageToRGB(image);
      
      // Create image object
      final imgImage = img.Image.fromBytes(
        width: image.width,
        height: image.height,
        bytes: rgbImage.buffer,
        format: img.Format.uint8,
        numChannels: 3,
      );
      
      // Preprocess image
      _preprocessImage(imgImage);
      
      // Run inference
      final outputs = <int, Object>{
        0: _scoresBuffer!,
        1: _boxesBuffer!,
      };
      
      _interpreter!.runForMultipleInputs([_inputBuffer], outputs);
      
      // Post-process results
      final faces = _postprocessResults(image.width, image.height);
      
      stopwatch.stop();
      
      if (kDebugMode && faces.isNotEmpty) {
        print('🔍 UltraFace camera processing: ${stopwatch.elapsedMilliseconds}ms, ${faces.length} faces');
      }
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ UltraFace camera processing failed: $e');
      }
      throw FaceDetectionException('Camera processing failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  /// Preprocess image for UltraFace model
  void _preprocessImage(img.Image image) {
    // Resize image to model input size
    final resized = img.copyResize(
      image,
      width: _inputWidth,
      height: _inputHeight,
      interpolation: img.Interpolation.linear,
    );
    
    // Convert to float and normalize
    var pixelIndex = 0;
    for (int y = 0; y < _inputHeight; y++) {
      for (int x = 0; x < _inputWidth; x++) {
        final pixel = resized.getPixel(x, y);
        
        // Normalize to [-1, 1] range
        _inputBuffer![pixelIndex++] = (pixel.r - 127.5) / 127.5;
        _inputBuffer![pixelIndex++] = (pixel.g - 127.5) / 127.5;
        _inputBuffer![pixelIndex++] = (pixel.b - 127.5) / 127.5;
      }
    }
  }
  
  /// Post-process model outputs to extract face detections
  List<FaceDetection> _postprocessResults(int originalWidth, int originalHeight) {
    final detections = <FaceDetection>[];
    
    // Generate anchors
    final anchors = _generateAnchors();
    
    // Process each anchor
    for (int i = 0; i < _numAnchors; i++) {
      final scoreIndex = i * 2;
      final boxIndex = i * 4;
      
      // Get confidence score (background vs face)
      final backgroundScore = _scoresBuffer![scoreIndex];
      final faceScore = _scoresBuffer![scoreIndex + 1];
      final confidence = _sigmoid(faceScore - backgroundScore);
      
      // Skip low confidence detections
      if (confidence < _confidenceThreshold) continue;
      
      // Decode bounding box
      final anchor = anchors[i];
      final dx = _boxesBuffer![boxIndex];
      final dy = _boxesBuffer![boxIndex + 1];
      final dw = _boxesBuffer![boxIndex + 2];
      final dh = _boxesBuffer![boxIndex + 3];
      
      // Apply deltas to anchor
      final centerX = anchor.centerX + dx * anchor.width;
      final centerY = anchor.centerY + dy * anchor.height;
      final width = anchor.width * math.exp(dw);
      final height = anchor.height * math.exp(dh);
      
      // Convert to original image coordinates
      final scaleX = originalWidth / _inputWidth;
      final scaleY = originalHeight / _inputHeight;
      
      final left = (centerX - width / 2) * scaleX;
      final top = (centerY - height / 2) * scaleY;
      final right = (centerX + width / 2) * scaleX;
      final bottom = (centerY + height / 2) * scaleY;
      
      // Create bounding box
      final boundingBox = Rect.fromLTRB(
        math.max(0, left),
        math.max(0, top),
        math.min(originalWidth.toDouble(), right),
        math.min(originalHeight.toDouble(), bottom),
      );
      
      // Skip invalid boxes
      if (boundingBox.width < _faceConfig.minFaceSize || 
          boundingBox.height < _faceConfig.minFaceSize) continue;
      
      // Create face detection
      final detection = FaceDetection(
        boundingBox: boundingBox,
        landmarks: [], // UltraFace doesn't provide landmarks
        confidence: confidence,
        quality: _calculateFaceQuality(boundingBox, originalWidth, originalHeight),
        pose: const FacePose(), // No pose estimation
        metadata: {
          'engine': engineName,
          'anchor_index': i,
        },
      );
      
      detections.add(detection);
    }
    
    // Apply Non-Maximum Suppression
    return _applyNMS(detections);
  }
  
  /// Generate anchor boxes for UltraFace model
  List<_Anchor> _generateAnchors() {
    final anchors = <_Anchor>[];
    
    // UltraFace anchor configuration
    final steps = [8, 16, 32, 64];
    final minSizes = [
      [10.0, 16.0, 24.0],
      [32.0, 48.0],
      [64.0, 96.0],
      [128.0, 192.0, 256.0],
    ];
    
    for (int i = 0; i < steps.length; i++) {
      final step = steps[i];
      final sizes = minSizes[i];
      
      final featureMapW = (_inputWidth / step).ceil();
      final featureMapH = (_inputHeight / step).ceil();
      
      for (int h = 0; h < featureMapH; h++) {
        for (int w = 0; w < featureMapW; w++) {
          for (final size in sizes) {
            final centerX = (w + 0.5) * step;
            final centerY = (h + 0.5) * step;
            
            anchors.add(_Anchor(
              centerX: centerX,
              centerY: centerY,
              width: size,
              height: size,
            ));
          }
        }
      }
    }
    
    return anchors;
  }
  
  /// Apply Non-Maximum Suppression to remove overlapping detections
  List<FaceDetection> _applyNMS(List<FaceDetection> detections) {
    if (detections.isEmpty) return detections;
    
    // Sort by confidence (descending)
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    final keep = <bool>[];
    for (int i = 0; i < detections.length; i++) {
      keep.add(true);
    }
    
    for (int i = 0; i < detections.length; i++) {
      if (!keep[i]) continue;
      
      for (int j = i + 1; j < detections.length; j++) {
        if (!keep[j]) continue;
        
        final iou = _calculateIoU(detections[i].boundingBox, detections[j].boundingBox);
        if (iou > _nmsThreshold) {
          keep[j] = false;
        }
      }
    }
    
    final result = <FaceDetection>[];
    for (int i = 0; i < detections.length; i++) {
      if (keep[i]) {
        result.add(detections[i]);
      }
    }
    
    return result.take(_faceConfig.maxFaces).toList();
  }
  
  /// Calculate Intersection over Union (IoU) between two rectangles
  double _calculateIoU(Rect a, Rect b) {
    final intersection = a.intersect(b);
    if (intersection.isEmpty) return 0.0;
    
    final intersectionArea = intersection.width * intersection.height;
    final unionArea = (a.width * a.height) + (b.width * b.height) - intersectionArea;
    
    return intersectionArea / unionArea;
  }
  
  /// Calculate face quality score based on size and position
  double _calculateFaceQuality(Rect boundingBox, int imageWidth, int imageHeight) {
    // Size quality (larger faces are better)
    final faceArea = boundingBox.width * boundingBox.height;
    final imageArea = imageWidth * imageHeight;
    final sizeRatio = faceArea / imageArea;
    final sizeQuality = math.min(1.0, sizeRatio * 20); // Normalize
    
    // Position quality (centered faces are better)
    final centerX = boundingBox.center.dx;
    final centerY = boundingBox.center.dy;
    final imageCenterX = imageWidth / 2;
    final imageCenterY = imageHeight / 2;
    
    final distanceFromCenter = math.sqrt(
      math.pow(centerX - imageCenterX, 2) + math.pow(centerY - imageCenterY, 2)
    );
    final maxDistance = math.sqrt(
      math.pow(imageCenterX, 2) + math.pow(imageCenterY, 2)
    );
    final positionQuality = 1.0 - (distanceFromCenter / maxDistance);
    
    // Combine qualities
    return (sizeQuality * 0.7 + positionQuality * 0.3).clamp(0.0, 1.0);
  }
  
  /// Sigmoid activation function
  double _sigmoid(double x) {
    return 1.0 / (1.0 + math.exp(-x));
  }
  
  @override
  void dispose() {
    _interpreter?.close();
    _interpreter = null;
    _inputBuffer = null;
    _scoresBuffer = null;
    _boxesBuffer = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ UltraFace engine disposed');
    }
  }
}

/// Anchor box for UltraFace model
class _Anchor {
  final double centerX;
  final double centerY;
  final double width;
  final double height;
  
  const _Anchor({
    required this.centerX,
    required this.centerY,
    required this.width,
    required this.height,
  });
}

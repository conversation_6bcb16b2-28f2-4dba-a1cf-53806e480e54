import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

/// Custom log filter to reduce spam from native libraries
class LogFilter {
  static final List<String> _spamPatterns = [
    'FaceDetectorV2Jni',
    'detectFacesImageByteArray.start()',
    'detectFacesImageByteArray.end()',
    'ML Kit',
    'GoogleApiAvailability',
    'DynamiteModule',
    'CameraX',
  ];

  static final List<String> _allowedPatterns = [
    '🔍', // Face recognition
    '👁️', // Face detection
    '✅', // Success
    '❌', // Error
    '⚠️', // Warning
    '🚀', // Trigger
    '📊', // Performance
    '🏥', // Health check
    '🔧', // Fix/Config
    '🎯', // Target/Goal
  ];

  /// Filter and log message if it's not spam
  static void log(String message, {String? tag, LogLevel level = LogLevel.debug}) {
    if (!kDebugMode) return;

    // Check if message contains spam patterns
    final isSpam = _spamPatterns.any((pattern) => 
        message.toLowerCase().contains(pattern.toLowerCase()));
    
    // Check if message contains allowed patterns (always show these)
    final isAllowed = _allowedPatterns.any((pattern) => 
        message.contains(pattern));

    // Skip spam messages unless they're explicitly allowed
    if (isSpam && !isAllowed) {
      return;
    }

    // Log the message
    final logMessage = tag != null ? '[$tag] $message' : message;
    
    switch (level) {
      case LogLevel.verbose:
        developer.log(logMessage, level: 500);
        break;
      case LogLevel.debug:
        developer.log(logMessage, level: 700);
        break;
      case LogLevel.info:
        developer.log(logMessage, level: 800);
        break;
      case LogLevel.warning:
        developer.log(logMessage, level: 900);
        break;
      case LogLevel.error:
        developer.log(logMessage, level: 1000);
        break;
    }
  }

  /// Log debug message with filtering
  static void d(String message, {String? tag}) {
    log(message, tag: tag, level: LogLevel.debug);
  }

  /// Log info message with filtering
  static void i(String message, {String? tag}) {
    log(message, tag: tag, level: LogLevel.info);
  }

  /// Log warning message with filtering
  static void w(String message, {String? tag}) {
    log(message, tag: tag, level: LogLevel.warning);
  }

  /// Log error message with filtering
  static void e(String message, {String? tag}) {
    log(message, tag: tag, level: LogLevel.error);
  }

  /// Add custom spam pattern
  static void addSpamPattern(String pattern) {
    if (!_spamPatterns.contains(pattern)) {
      _spamPatterns.add(pattern);
    }
  }

  /// Remove spam pattern
  static void removeSpamPattern(String pattern) {
    _spamPatterns.remove(pattern);
  }

  /// Add allowed pattern (always show)
  static void addAllowedPattern(String pattern) {
    if (!_allowedPatterns.contains(pattern)) {
      _allowedPatterns.add(pattern);
    }
  }

  /// Get current spam patterns
  static List<String> get spamPatterns => List.unmodifiable(_spamPatterns);

  /// Get current allowed patterns
  static List<String> get allowedPatterns => List.unmodifiable(_allowedPatterns);

  /// Clear all filters (show all logs)
  static void clearFilters() {
    _spamPatterns.clear();
    _allowedPatterns.clear();
  }

  /// Reset to default filters
  static void resetToDefaults() {
    _spamPatterns.clear();
    _spamPatterns.addAll([
      'FaceDetectorV2Jni',
      'detectFacesImageByteArray.start()',
      'detectFacesImageByteArray.end()',
      'ML Kit',
      'GoogleApiAvailability',
      'DynamiteModule',
      'CameraX',
    ]);

    _allowedPatterns.clear();
    _allowedPatterns.addAll([
      '🔍', '👁️', '✅', '❌', '⚠️', '🚀', '📊', '🏥', '🔧', '🎯',
    ]);
  }

  /// Enable/disable specific log categories
  static void configureCategories({
    bool showFaceDetection = true,
    bool showFaceRecognition = true,
    bool showPerformance = true,
    bool showErrors = true,
    bool showWarnings = true,
    bool showNativeLibraries = false,
  }) {
    _spamPatterns.clear();
    _allowedPatterns.clear();

    if (!showNativeLibraries) {
      _spamPatterns.addAll([
        'FaceDetectorV2Jni',
        'detectFacesImageByteArray',
        'ML Kit',
        'GoogleApiAvailability',
        'DynamiteModule',
        'CameraX',
      ]);
    }

    if (showFaceDetection) {
      _allowedPatterns.add('👁️');
    }

    if (showFaceRecognition) {
      _allowedPatterns.add('🔍');
    }

    if (showPerformance) {
      _allowedPatterns.addAll(['📊', '🚀']);
    }

    if (showErrors) {
      _allowedPatterns.add('❌');
    }

    if (showWarnings) {
      _allowedPatterns.add('⚠️');
    }
  }
}

/// Log levels for filtering
enum LogLevel {
  verbose,
  debug,
  info,
  warning,
  error,
}

/// Extension for easy logging
extension LogFilterExtension on String {
  void logD({String? tag}) => LogFilter.d(this, tag: tag);
  void logI({String? tag}) => LogFilter.i(this, tag: tag);
  void logW({String? tag}) => LogFilter.w(this, tag: tag);
  void logE({String? tag}) => LogFilter.e(this, tag: tag);
}

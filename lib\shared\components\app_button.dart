import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';
import 'app_loading.dart';

enum AppButtonType {
  primary,
  secondary,
  outline,
  text,
}

enum AppButtonSize {
  small,
  medium,
  large,
}

/// Button component tái sử dụng cho toàn bộ ứng dụng
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final double? height;
  final Widget? icon;
  final EdgeInsetsGeometry? padding;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = true,
    this.height,
    this.icon,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: height ?? _getButtonHeight(),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: _getButtonStyle(),
        child: isLoading ? _buildLoadingWidget() : _buildButtonContent(),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          SizedBox(width: AppDimensions.spacing8),
          Text(text, style: _getTextStyle()),
        ],
      );
    }
    return Text(text, style: _getTextStyle());
  }

  Widget _buildLoadingWidget() {
    return AppLoading(
      size: _getLoadingSize(),
      color: _getLoadingColor(),
    );
  }

  double _getButtonHeight() {
    switch (size) {
      case AppButtonSize.small:
        return AppDimensions.buttonHeightS;
      case AppButtonSize.medium:
        return AppDimensions.buttonHeightM;
      case AppButtonSize.large:
        return AppDimensions.buttonHeightL;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 20;
      case AppButtonSize.large:
        return 24;
    }
  }

  Color _getLoadingColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppColors.textOnPrimary;
      case AppButtonType.secondary:
        return AppColors.primary;
      case AppButtonType.outline:
        return AppColors.primary;
      case AppButtonType.text:
        return AppColors.primary;
    }
  }

  ButtonStyle _getButtonStyle() {
    final isDisabled = onPressed == null && !isLoading;

    switch (type) {
      case AppButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: isDisabled ? AppColors.disabledButton : AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: padding ?? _getDefaultPadding(),
        );

      case AppButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: isDisabled ? AppColors.neutral200 : AppColors.surface,
          foregroundColor: isDisabled ? AppColors.textTertiary : AppColors.primary,
          elevation: 0,
          shadowColor: Colors.transparent,
          side: BorderSide(
            color: isDisabled ? AppColors.neutral200 : AppColors.border,
            width: AppDimensions.borderNormal,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: padding ?? _getDefaultPadding(),
        );

      case AppButtonType.outline:
        return ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: isDisabled ? AppColors.textTertiary : AppColors.primary,
          elevation: 0,
          shadowColor: Colors.transparent,
          side: BorderSide(
            color: isDisabled ? AppColors.neutral200 : AppColors.primary,
            width: AppDimensions.borderNormal,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: padding ?? _getDefaultPadding(),
        );

      case AppButtonType.text:
        return ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: isDisabled ? AppColors.textTertiary : AppColors.primary,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: padding ?? _getDefaultPadding(),
        );
    }
  }

  EdgeInsetsGeometry _getDefaultPadding() {
    switch (size) {
      case AppButtonSize.small:
        return EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        );
      case AppButtonSize.medium:
        return EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingS,
        );
      case AppButtonSize.large:
        return EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        );
    }
  }

  TextStyle _getTextStyle() {
    final isDisabled = onPressed == null && !isLoading;

    Color? textColor;
    switch (type) {
      case AppButtonType.primary:
        // Primary button: always white text (both enabled and disabled)
        textColor = AppColors.textOnPrimary;
        break;
      case AppButtonType.secondary:
        textColor = isDisabled ? AppColors.textTertiary : AppColors.primary;
        break;
      case AppButtonType.outline:
        textColor = isDisabled ? AppColors.textTertiary : AppColors.primary;
        break;
      case AppButtonType.text:
        textColor = isDisabled ? AppColors.textTertiary : AppColors.primary;
        break;
    }

    switch (size) {
      case AppButtonSize.small:
        return AppTextStyles.buttonSmall.copyWith(color: textColor);
      case AppButtonSize.medium:
        return AppTextStyles.buttonMedium.copyWith(color: textColor);
      case AppButtonSize.large:
        return AppTextStyles.buttonLarge.copyWith(color: textColor);
    }
  }
}

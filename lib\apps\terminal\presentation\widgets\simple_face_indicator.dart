import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../../../../core/constants/app_colors.dart';

/// Simple face indicator widget for terminal app
/// Shows basic face detection status without complex processing
class SimpleFaceIndicator extends StatelessWidget {
  final Face? detectedFace;
  final bool isVisible;

  const SimpleFaceIndicator({
    super.key,
    this.detectedFace,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      bottom: 100,
      right: 20,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: BorderRadius.circular(40),
          border: Border.all(
            color: _getBorderColor(),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            _getIcon(),
            color: _getIconColor(),
            size: 32,
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (detectedFace != null) {
      return const Color(0xFF1E293B).withValues(alpha: 0.9);
    }
    return const Color(0xFF374151).withValues(alpha: 0.7);
  }

  Color _getBorderColor() {
    if (detectedFace != null) {
      return AppColors.primary;
    }
    return Colors.grey;
  }

  IconData _getIcon() {
    if (detectedFace != null) {
      return Icons.face;
    }
    return Icons.face_retouching_off;
  }

  Color _getIconColor() {
    if (detectedFace != null) {
      return AppColors.primary;
    }
    return Colors.grey[400]!;
  }
}

import 'package:flutter/foundation.dart';
import '../../../../shared/presentation/providers/base/base_auth_provider.dart';
import '../../../../shared/domain/use_cases/auth/login_use_case.dart';
import '../../../../shared/domain/use_cases/auth/logout_use_case.dart';
import '../../../../shared/domain/use_cases/auth/refresh_token_use_case.dart';
import '../../../../shared/services/token_refresh_service.dart';
import '../../../../shared/domain/entities/auth/auth_result.dart';
import '../../../../shared/domain/entities/user/user.dart';
import '../../../../shared/core/errors/failures.dart';
import '../../../../shared/services/error_handler_service.dart';
import '../../../../shared/services/validation_service.dart';
import '../../../../shared/services/navigation_service.dart';
import '../../../../shared/services/auth_state_service.dart';
import '../../../../shared/data/models/auth/auth_result_model.dart';
import '../../../../shared/data/models/user/user_model.dart';
import 'package:flutter/material.dart';

/// Mobile-specific authentication provider
///
/// Extends BaseAuthProvider with mobile-specific functionality
/// and error handling for the mobile app context
class AuthProvider extends BaseAuthProvider {
  AuthProvider({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required RefreshTokenUseCase refreshTokenUseCase,
  }) : super(
    loginUseCase: loginUseCase,
    logoutUseCase: logoutUseCase,
    refreshTokenUseCase: refreshTokenUseCase,
  );

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> initialize() async {
    try {
      // Check stored authentication state
      final isAuthenticated = await AuthStateService().isAuthenticated();

      if (isAuthenticated) {
        // Get stored auth result and user data
        final authResult = await AuthStateService().getAuthResult();
        final user = await AuthStateService().getUser();

        if (authResult != null && user != null) {
          // Restore authentication state manually
          _restoreAuthenticationState(authResult, user);

          if (kDebugMode) {
            debugPrint('Auth restored from storage - user: ${user.name}');
          }
        } else {
          // Invalid stored data, clear and set unauthenticated
          await AuthStateService().clearAuthData();
          reset();
          setAuthStatus(AuthStatus.unauthenticated);
        }
      } else {
        // No valid authentication, set unauthenticated
        reset();
        setAuthStatus(AuthStatus.unauthenticated);
      }

      if (kDebugMode) {
        debugPrint('Auth initialization completed - status: $authStatus');
      }
    } catch (e) {
      // If initialization fails, clear data and set unauthenticated
      await AuthStateService().clearAuthData();
      reset();
      setAuthStatus(AuthStatus.unauthenticated);
      debugPrint('Auth initialization failed: $e');
    }
  }

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  /// Login with username and password
  /// Returns true if login successful, false otherwise
  Future<bool> loginWithCredentials({
    required String userName,
    required String password,
  }) async {
    try {
      // Call parent login method
      await super.login(
        email: userName,
        password: password,
      );

      // Check if login was successful
      return authStatus == AuthStatus.authenticated;
    } catch (e) {
      // Handle any unexpected errors
      final failure = ServerFailure('Lỗi không xác định: ${e.toString()}');
      setError(failure);
      return false;
    }
  }

  /// Login with enhanced validation
  /// Returns true if login successful, false otherwise
  Future<bool> loginWithValidation({
    required String userName,
    required String password,
    String? serverAddress,
    bool isOnPremise = false,
  }) async {
    try {
      // Validate parameters first
      final validationError = validateLoginParams(
        userName: userName,
        password: password,
        serverAddress: serverAddress,
        isOnPremise: isOnPremise,
      );

      if (validationError != null) {
        setError(validationError);
        return false;
      }

      // Call parent login method
      await super.login(
        email: userName.trim(),
        password: password,
      );

      // Check if login was successful
      return authStatus == AuthStatus.authenticated;
    } catch (e) {
      // Handle any unexpected errors
      final failure = ServerFailure('Lỗi không xác định: ${e.toString()}');
      setError(failure);
      return false;
    }
  }

  /// Get user-friendly error message based on failure type
  String getUserFriendlyError(Failure failure) {
    final errorHandler = ErrorHandlerService();
    return errorHandler.getErrorMessage(failure);
  }

  /// Check if error is retryable
  bool isErrorRetryable(Failure failure) {
    final errorHandler = ErrorHandlerService();
    return errorHandler.isRetryable(failure);
  }

  /// Get error severity for UI display
  ErrorSeverity getErrorSeverity(Failure failure) {
    final errorHandler = ErrorHandlerService();
    return errorHandler.getErrorSeverity(failure);
  }

  /// Handle login success - mobile specific implementation
  @override
  void onLoginSuccess(AuthResult authResult) {
    // First, manually set the authentication state (duplicating _handleLoginSuccess logic)
    // since we can't access the private method from base class

    // Set user and tokens manually
    setAuthStatus(AuthStatus.authenticated);

    // Register this provider with TokenRefreshService for HTTP interceptor
    TokenRefreshService.instance.setAuthProvider(this);

    // Mobile-specific login success handling
    if (kDebugMode) {
      print('Mobile login successful for user: ${authResult.user?.name ?? 'Unknown'}');
      print('Mobile auth provider registered with TokenRefreshService');
    }

    // Could add mobile-specific analytics, notifications, etc.
    // Call parent implementation (which is empty but good practice)
    super.onLoginSuccess(authResult);
  }

  /// Handle logout success - mobile specific implementation
  @override
  void onLogoutSuccess() {
    // Clear TokenRefreshService reference
    TokenRefreshService.instance.clear();

    // Mobile-specific logout handling
    if (kDebugMode) {
      print('Mobile logout successful - TokenRefreshService cleared');
    }

    // Could add mobile-specific cleanup, analytics, etc.
    // For now, just call parent implementation
    super.onLogoutSuccess();
  }

  /// Handle authentication errors with mobile-specific error messages
  @override
  void onAuthError(Failure failure) {
    final errorHandler = ErrorHandlerService();

    // Log error for debugging
    errorHandler.logError(failure);

    // Get user-friendly message
    final userFriendlyMessage = getUserFriendlyError(failure);
    final userFailure = ServerFailure(userFriendlyMessage);
    setError(userFailure);

    if (kDebugMode) {
      print('Mobile auth error: ${failure.toString()}');
      print('User-friendly message: $userFriendlyMessage');
      print('Is retryable: ${isErrorRetryable(failure)}');
      print('Severity: ${getErrorSeverity(failure)}');
    }
  }

  /// Check if user should be redirected to login
  bool shouldRedirectToLogin() {
    return authStatus == AuthStatus.unauthenticated ||
           authStatus == AuthStatus.error ||
           isTokenExpired();
  }

  /// Clear any error messages
  @override
  void clearError() {
    if (failure != null) {
      super.clearError();
    }
  }

  /// Validate login parameters before calling use case
  ValidationFailure? validateLoginParams({
    required String userName,
    required String password,
    String? serverAddress,
    bool isOnPremise = false,
  }) {
    final validationService = ValidationService();
    return validationService.validateLoginForm(
      userName: userName,
      password: password,
      serverAddress: serverAddress,
      isOnPremise: isOnPremise,
    );
  }



  /// Get current authentication token
  String? get currentToken => accessToken;

  /// Handle post-login navigation
  Future<void> handlePostLoginNavigation(BuildContext context) async {
    if (!context.mounted) return;

    try {
      if (kDebugMode) {
        print('🚀 AuthProvider: Handling post-login navigation');
        print('Auth Status: $authStatus');
        print('Is Authenticated: $isAuthenticated');
      }

      if (isAuthenticated) {
        // Get current tenant ID from user data
        String? currentTenantId;
        try {
          // Access currentUser directly from base class
          final user = currentUser;
          currentTenantId = user?.currentTenantId;

          if (kDebugMode) {
            print('Current Tenant ID from user: $currentTenantId');
            print('User data: ${user?.name} (${user?.username})');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ AuthProvider: Error getting current tenant ID: $e');
          }
          currentTenantId = null;
        }

        if (context.mounted) {
          await NavigationService.navigateToPostLoginWithTenant(
            context,
            currentTenantId: currentTenantId,
          );
        }

        if (kDebugMode) {
          print('✅ AuthProvider: Post-login navigation completed successfully');
        }
      } else {
        if (kDebugMode) {
          print('❌ AuthProvider: User not authenticated, cannot navigate to dashboard');
        }

        if (context.mounted) {
          await NavigationService.navigateToLogin(context, reason: 'Authentication required');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AuthProvider: Error in post-login navigation: $e');
      }

      // Fallback to login
      if (context.mounted) {
        await NavigationService.navigateToLogin(context, reason: 'Navigation error');
      }
    }
  }

  /// Handle logout navigation
  Future<void> handleLogoutNavigation(BuildContext context) async {
    if (!context.mounted) return;

    try {
      if (kDebugMode) {
        print('🔐 AuthProvider: Handling logout navigation');
      }

      if (context.mounted) {
        await NavigationService.navigateToLogin(context, reason: 'User logged out');
      }

      if (kDebugMode) {
        print('✅ AuthProvider: Logout navigation completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AuthProvider: Error in logout navigation: $e');
      }
    }
  }

  /// Update current user data (for profile updates)
  void updateCurrentUser(User user) async {
    try {
      // Get current auth result
      final authResult = await AuthStateService().getAuthResult();
      if (authResult != null) {
        // Create updated auth result with new user data
        final userModel = UserModel.fromEntity(user);
        final updatedAuthResult = AuthResultModel(
          accessToken: authResult.accessToken,
          refreshToken: authResult.refreshToken,
          tokenType: authResult.tokenType,
          expiresAt: authResult.expiresAt,
          user: userModel,
        );

        // Store updated auth result
        await AuthStateService().storeAuthResult(updatedAuthResult);

        // Force a re-initialization to reload the user data
        await initialize();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating current user: $e');
      }
    }

    notifyListeners();
  }
  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Restore authentication state from stored data
  void _restoreAuthenticationState(AuthResultModel authResult, UserModel user) {
    // Only restore if we have valid expiry date
    if (authResult.expiresAt == null) {
      // If no expiry date, clear auth data and set unauthenticated
      reset();
      setAuthStatus(AuthStatus.unauthenticated);
      return;
    }

    // Convert AuthResultModel to AuthResult entity
    final authResultEntity = AuthResult(
      accessToken: authResult.accessToken,
      refreshToken: authResult.refreshToken,
      expiresAt: authResult.expiresAt!,
      user: user.toEntity(),
    );

    // Manually set the authentication state (since _handleLoginSuccess is private)
    // We'll set the fields directly using the public setters and methods
    setAuthStatus(AuthStatus.authenticated);

    // The base class will handle setting internal fields through the login success flow
    // We need to trigger this through the public interface
    onLoginSuccess(authResultEntity);
  }




}
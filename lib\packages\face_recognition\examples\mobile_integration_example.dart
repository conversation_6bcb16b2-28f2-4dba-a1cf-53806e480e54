/// Example integration of Face Recognition Package in Mobile App
/// 
/// This example shows how to integrate the face recognition system
/// into the mobile app for user management and face enrollment.

import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

// Import the face recognition package
import '../face_recognition.dart';

/// Example mobile screen for face enrollment
class MobileFaceEnrollmentScreen extends StatefulWidget {
  final String userId;
  final String userName;
  final String projectId;
  
  const MobileFaceEnrollmentScreen({
    super.key,
    required this.userId,
    required this.userName,
    required this.projectId,
  });

  @override
  State<MobileFaceEnrollmentScreen> createState() => _MobileFaceEnrollmentScreenState();
}

class _MobileFaceEnrollmentScreenState extends State<MobileFaceEnrollmentScreen> {
  // Face recognition system
  MobileFaceSystem? _faceSystem;
  
  // Camera
  CameraController? _cameraController;
  
  // Enrollment state
  EnrollmentSession? _enrollmentSession;
  EnrollmentProgress? _enrollmentProgress;
  bool _isInitialized = false;
  bool _isEnrolling = false;
  String _statusMessage = 'Initializing...';
  String _feedbackMessage = '';
  
  @override
  void initState() {
    super.initState();
    _initializeSystem();
  }
  
  /// Initialize the mobile face recognition system
  Future<void> _initializeSystem() async {
    try {
      // Initialize face recognition for mobile
      _faceSystem = await FaceRecognitionMobile.initialize(
        deviceType: MobileDeviceType.android,
        performanceProfile: PerformanceProfile.balanced,
        serverEndpoint: 'https://api.yourcompany.com/face',
        apiKey: 'your-api-key',
      );
      
      // Initialize camera
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        // Use front camera for enrollment
        final frontCamera = cameras.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front,
          orElse: () => cameras.first,
        );
        
        _cameraController = CameraController(
          frontCamera,
          ResolutionPreset.high,
          enableAudio: false,
        );
        
        await _cameraController!.initialize();
      }
      
      // Listen to face system changes
      _faceSystem!.addListener(_onFaceSystemChanged);
      
      setState(() {
        _isInitialized = true;
        _statusMessage = 'Ready to start enrollment';
      });
      
    } catch (e) {
      setState(() {
        _statusMessage = 'Initialization failed: $e';
      });
    }
  }
  
  /// Start face enrollment process
  Future<void> _startEnrollment() async {
    if (_faceSystem == null || _isEnrolling) return;
    
    try {
      setState(() {
        _isEnrolling = true;
        _statusMessage = 'Starting enrollment...';
      });
      
      _enrollmentSession = await _faceSystem!.startEnrollment(
        userId: widget.userId,
        userName: widget.userName,
        projectId: widget.projectId,
        accessLevel: AccessLevel.user,
      );
      
      setState(() {
        _statusMessage = 'Position your face in the frame';
        _feedbackMessage = 'Look straight at the camera';
      });
      
    } catch (e) {
      setState(() {
        _isEnrolling = false;
        _statusMessage = 'Failed to start enrollment: $e';
      });
    }
  }
  
  /// Capture image for enrollment
  Future<void> _captureEnrollmentImage() async {
    if (_cameraController == null || _enrollmentSession == null) return;
    
    try {
      final image = await _cameraController!.takePicture();
      final imageBytes = await image.readAsBytes();
      
      _enrollmentProgress = await _faceSystem!.processEnrollmentImage(imageBytes);
      
      setState(() {
        _feedbackMessage = _enrollmentProgress!.feedback;
      });
      
      // Auto-complete if enough samples
      if (_enrollmentProgress!.isComplete) {
        await _completeEnrollment();
      }
      
    } catch (e) {
      setState(() {
        _feedbackMessage = 'Capture failed: $e';
      });
    }
  }
  
  /// Complete enrollment process
  Future<void> _completeEnrollment() async {
    if (_faceSystem == null) return;
    
    try {
      setState(() {
        _statusMessage = 'Completing enrollment...';
      });
      
      final result = await _faceSystem!.completeEnrollment();
      
      if (result.success) {
        setState(() {
          _statusMessage = '✅ Enrollment completed successfully!';
          _feedbackMessage = 'User ${widget.userName} has been enrolled';
          _isEnrolling = false;
        });
        
        // Navigate back or show success screen
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        });
      } else {
        setState(() {
          _statusMessage = '❌ Enrollment failed';
          _feedbackMessage = result.errorMessage ?? 'Unknown error';
          _isEnrolling = false;
        });
      }
      
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Enrollment failed: $e';
        _isEnrolling = false;
      });
    }
  }
  
  /// Cancel enrollment process
  Future<void> _cancelEnrollment() async {
    if (_faceSystem == null) return;
    
    await _faceSystem!.cancelEnrollment();
    
    setState(() {
      _isEnrolling = false;
      _enrollmentSession = null;
      _enrollmentProgress = null;
      _statusMessage = 'Enrollment cancelled';
      _feedbackMessage = '';
    });
  }
  
  /// Handle face system state changes
  void _onFaceSystemChanged() {
    if (!mounted) return;
    
    // Update UI based on detected faces
    if (_faceSystem!.detectedFaces.isNotEmpty && _isEnrolling) {
      final bestFace = _faceSystem!.detectedFaces.first;
      
      setState(() {
        if (bestFace.quality > 0.8) {
          _feedbackMessage = 'Good quality - hold still';
        } else if (bestFace.quality > 0.6) {
          _feedbackMessage = 'Improve lighting or move closer';
        } else {
          _feedbackMessage = 'Face quality too low';
        }
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Enroll ${widget.userName}'),
        actions: [
          if (_isEnrolling)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: _cancelEnrollment,
            ),
        ],
      ),
      body: Column(
        children: [
          // Camera preview
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.all(16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _isInitialized && _cameraController != null
                    ? Stack(
                        children: [
                          CameraPreview(_cameraController!),
                          
                          // Face detection overlay
                          if (_faceSystem != null)
                            CustomPaint(
                              size: Size.infinite,
                              painter: MobileFaceDetectionPainter(
                                faces: _faceSystem!.detectedFaces,
                                imageSize: _cameraController?.value.previewSize ?? Size.zero,
                              ),
                            ),
                          
                          // Face guide overlay
                          Center(
                            child: Container(
                              width: 200,
                              height: 250,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.5),
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(100),
                              ),
                            ),
                          ),
                        ],
                      )
                    : const Center(
                        child: CircularProgressIndicator(),
                      ),
              ),
            ),
          ),
          
          // Status and progress
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Status message
                  Text(
                    _statusMessage,
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Feedback message
                  Text(
                    _feedbackMessage,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Progress indicator
                  if (_enrollmentProgress != null) ...[
                    LinearProgressIndicator(
                      value: _enrollmentProgress!.progress,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${_enrollmentProgress!.completedSamples}/${_enrollmentProgress!.requiredSamples} samples captured',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                  
                  const Spacer(),
                  
                  // Action buttons
                  Row(
                    children: [
                      if (!_isEnrolling) ...[
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? _startEnrollment : null,
                            child: const Text('Start Enrollment'),
                          ),
                        ),
                      ] else ...[
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _captureEnrollmentImage,
                            child: const Text('Capture Sample'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _cancelEnrollment,
                            child: const Text('Cancel'),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _cameraController?.dispose();
    _faceSystem?.removeListener(_onFaceSystemChanged);
    FaceRecognitionMobile.dispose();
    super.dispose();
  }
}

/// Custom painter for mobile face detection overlay
class MobileFaceDetectionPainter extends CustomPainter {
  final List<FaceDetection> faces;
  final Size imageSize;
  
  MobileFaceDetectionPainter({
    required this.faces,
    required this.imageSize,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (faces.isEmpty || imageSize == Size.zero) return;
    
    for (final face in faces) {
      // Draw face quality indicator
      final paint = Paint()
        ..color = _getQualityColor(face.quality)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0;
      
      final rect = _transformRect(face.boundingBox, imageSize, size);
      canvas.drawRect(rect, paint);
      
      // Draw quality percentage
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${(face.quality * 100).toStringAsFixed(0)}%',
          style: TextStyle(
            color: _getQualityColor(face.quality),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(rect.left, rect.top - 25),
      );
    }
  }
  
  Color _getQualityColor(double quality) {
    if (quality > 0.8) return Colors.green;
    if (quality > 0.6) return Colors.orange;
    return Colors.red;
  }
  
  Rect _transformRect(Rect rect, Size imageSize, Size canvasSize) {
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;
    
    return Rect.fromLTRB(
      rect.left * scaleX,
      rect.top * scaleY,
      rect.right * scaleX,
      rect.bottom * scaleY,
    );
  }
  
  @override
  bool shouldRepaint(MobileFaceDetectionPainter oldDelegate) {
    return faces != oldDelegate.faces || imageSize != oldDelegate.imageSize;
  }
}

import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

// Network
import '../../network/network_info.dart';
import '../../network/api_client.dart';
import '../../../services/api_endpoints.dart';

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến Network
void registerNetworkDependencies() {
  // ============================================================================
  // EXTERNAL DEPENDENCIES
  // ============================================================================
  
  // HTTP Client
  getIt.registerLazySingleton<http.Client>(
    () => http.Client(),
  );

  // Connectivity
  getIt.registerLazySingleton<Connectivity>(
    () => Connectivity(),
  );

  // ============================================================================
  // NETWORK SERVICES
  // ============================================================================

  // API Client
  getIt.registerLazySingleton<ApiClient>(
    () => ApiClient(
      client: getIt<http.Client>(),
      baseUrl: ApiEndpoints.devBaseUrl,
    ),
  );

  // Network Info
  getIt.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(getIt<Connectivity>()),
  );
}

/// Unregister tất cả network dependencies (for testing)
void unregisterNetworkDependencies() {
  // Network Services
  if (getIt.isRegistered<NetworkInfo>()) {
    getIt.unregister<NetworkInfo>();
  }
  if (getIt.isRegistered<ApiClient>()) {
    getIt.unregister<ApiClient>();
  }

  // External Dependencies
  if (getIt.isRegistered<Connectivity>()) {
    getIt.unregister<Connectivity>();
  }
  if (getIt.isRegistered<http.Client>()) {
    getIt.unregister<http.Client>();
  }
}

/// Reset network module (clear và re-register)
void resetNetworkModule() {
  unregisterNetworkDependencies();
  registerNetworkDependencies();
}

/// Check if network dependencies are registered
bool areNetworkDependenciesRegistered() {
  return getIt.isRegistered<NetworkInfo>() &&
         getIt.isRegistered<ApiClient>() &&
         getIt.isRegistered<http.Client>();
}

/// Get network-related dependencies for debugging
Map<String, bool> getNetworkDependenciesStatus() {
  return {
    'http.Client': getIt.isRegistered<http.Client>(),
    'Connectivity': getIt.isRegistered<Connectivity>(),
    'ApiClient': getIt.isRegistered<ApiClient>(),
    'NetworkInfo': getIt.isRegistered<NetworkInfo>(),
  };
}

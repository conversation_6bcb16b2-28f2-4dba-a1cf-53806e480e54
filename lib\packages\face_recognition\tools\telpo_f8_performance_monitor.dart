import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// Real-time performance monitoring tool for Telpo F8 face detection
/// Tracks FPS, memory usage, CPU usage, and thermal performance
class TelpoF8PerformanceMonitor {
  static TelpoF8PerformanceMonitor? _instance;
  static TelpoF8PerformanceMonitor get instance => _instance ??= TelpoF8PerformanceMonitor._();
  
  TelpoF8PerformanceMonitor._();
  
  // Monitoring state
  bool _isMonitoring = false;
  Timer? _monitoringTimer;
  
  // Performance metrics
  final List<PerformanceSnapshot> _snapshots = [];
  int _frameCount = 0;
  DateTime _lastFrameTime = DateTime.now();
  double _currentFPS = 0.0;
  
  // Callbacks
  Function(PerformanceSnapshot)? _onSnapshotCallback;
  Function(PerformanceAlert)? _onAlertCallback;
  
  // Thresholds for alerts
  static const double _maxMemoryMB = 150.0;
  static const double _maxCpuPercent = 60.0;
  static const double _maxTemperatureC = 85.0;
  static const double _minFPS = 30.0;
  
  /// Start performance monitoring
  void startMonitoring({
    Duration interval = const Duration(seconds: 5),
    Function(PerformanceSnapshot)? onSnapshot,
    Function(PerformanceAlert)? onAlert,
  }) {
    if (_isMonitoring) return;
    
    _onSnapshotCallback = onSnapshot;
    _onAlertCallback = onAlert;
    _isMonitoring = true;
    
    if (kDebugMode) {
      print('📊 Starting Telpo F8 performance monitoring...');
    }
    
    _monitoringTimer = Timer.periodic(interval, (_) async {
      final snapshot = await _captureSnapshot();
      _snapshots.add(snapshot);
      
      // Keep only last 100 snapshots
      if (_snapshots.length > 100) {
        _snapshots.removeAt(0);
      }
      
      // Check for alerts
      _checkAlerts(snapshot);
      
      // Notify callback
      _onSnapshotCallback?.call(snapshot);
      
      if (kDebugMode) {
        _logSnapshot(snapshot);
      }
    });
  }
  
  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    
    if (kDebugMode) {
      print('📊 Performance monitoring stopped');
    }
  }
  
  /// Record a frame for FPS calculation
  void recordFrame() {
    final now = DateTime.now();
    _frameCount++;
    
    final timeDiff = now.difference(_lastFrameTime).inMicroseconds / 1000000.0;
    if (timeDiff > 0) {
      _currentFPS = 1.0 / timeDiff;
    }
    
    _lastFrameTime = now;
  }
  
  /// Get current performance snapshot
  Future<PerformanceSnapshot> getCurrentSnapshot() async {
    return await _captureSnapshot();
  }
  
  /// Get performance history
  List<PerformanceSnapshot> getHistory() {
    return List.unmodifiable(_snapshots);
  }
  
  /// Get performance summary
  PerformanceSummary getSummary() {
    if (_snapshots.isEmpty) {
      return PerformanceSummary.empty();
    }
    
    final fps = _snapshots.map((s) => s.fps).toList();
    final memory = _snapshots.map((s) => s.memoryUsageMB).toList();
    final cpu = _snapshots.map((s) => s.cpuUsagePercent).toList();
    final temp = _snapshots.map((s) => s.temperatureC).toList();
    
    return PerformanceSummary(
      avgFPS: _calculateAverage(fps),
      minFPS: fps.reduce((a, b) => a < b ? a : b),
      maxFPS: fps.reduce((a, b) => a > b ? a : b),
      avgMemoryMB: _calculateAverage(memory),
      maxMemoryMB: memory.reduce((a, b) => a > b ? a : b),
      avgCpuPercent: _calculateAverage(cpu),
      maxCpuPercent: cpu.reduce((a, b) => a > b ? a : b),
      avgTemperatureC: _calculateAverage(temp),
      maxTemperatureC: temp.reduce((a, b) => a > b ? a : b),
      totalSnapshots: _snapshots.length,
      monitoringDuration: _snapshots.isNotEmpty 
          ? _snapshots.last.timestamp.difference(_snapshots.first.timestamp)
          : Duration.zero,
    );
  }
  
  /// Export performance data to JSON
  Map<String, dynamic> exportData() {
    return {
      'summary': getSummary().toMap(),
      'snapshots': _snapshots.map((s) => s.toMap()).toList(),
      'exportTime': DateTime.now().toIso8601String(),
    };
  }
  
  // Private methods
  
  Future<PerformanceSnapshot> _captureSnapshot() async {
    final timestamp = DateTime.now();
    
    // Get memory usage
    double memoryMB = 0.0;
    try {
      final memInfo = await Process.run('cat', ['/proc/self/status']);
      final vmRSSMatch = RegExp(r'VmRSS:\s+(\d+)\s+kB').firstMatch(memInfo.stdout);
      if (vmRSSMatch != null) {
        memoryMB = int.parse(vmRSSMatch.group(1)!) / 1024.0;
      }
    } catch (e) {
      // Fallback: estimate based on Dart VM
      memoryMB = 100.0; // Default estimate
    }
    
    // Get CPU usage (simplified)
    double cpuPercent = 0.0;
    try {
      final cpuInfo = await Process.run('cat', ['/proc/stat']);
      // Simplified CPU calculation - in real implementation would be more complex
      cpuPercent = 30.0; // Placeholder
    } catch (e) {
      cpuPercent = 0.0;
    }
    
    // Get temperature
    double temperatureC = 0.0;
    try {
      final tempFiles = [
        '/sys/class/thermal/thermal_zone0/temp',
        '/sys/class/thermal/thermal_zone1/temp',
        '/sys/devices/virtual/thermal/thermal_zone0/temp',
      ];
      
      for (final tempFile in tempFiles) {
        try {
          final tempResult = await Process.run('cat', [tempFile]);
          final tempStr = tempResult.stdout.toString().trim();
          if (tempStr.isNotEmpty) {
            temperatureC = int.parse(tempStr) / 1000.0;
            break;
          }
        } catch (e) {
          continue;
        }
      }
    } catch (e) {
      temperatureC = 0.0;
    }
    
    return PerformanceSnapshot(
      timestamp: timestamp,
      fps: _currentFPS,
      memoryUsageMB: memoryMB,
      cpuUsagePercent: cpuPercent,
      temperatureC: temperatureC,
      frameCount: _frameCount,
    );
  }
  
  void _checkAlerts(PerformanceSnapshot snapshot) {
    final alerts = <PerformanceAlert>[];
    
    if (snapshot.memoryUsageMB > _maxMemoryMB) {
      alerts.add(PerformanceAlert(
        type: AlertType.highMemory,
        message: 'High memory usage: ${snapshot.memoryUsageMB.toStringAsFixed(1)}MB',
        value: snapshot.memoryUsageMB,
        threshold: _maxMemoryMB,
        timestamp: snapshot.timestamp,
      ));
    }
    
    if (snapshot.cpuUsagePercent > _maxCpuPercent) {
      alerts.add(PerformanceAlert(
        type: AlertType.highCpu,
        message: 'High CPU usage: ${snapshot.cpuUsagePercent.toStringAsFixed(1)}%',
        value: snapshot.cpuUsagePercent,
        threshold: _maxCpuPercent,
        timestamp: snapshot.timestamp,
      ));
    }
    
    if (snapshot.temperatureC > _maxTemperatureC) {
      alerts.add(PerformanceAlert(
        type: AlertType.highTemperature,
        message: 'High temperature: ${snapshot.temperatureC.toStringAsFixed(1)}°C',
        value: snapshot.temperatureC,
        threshold: _maxTemperatureC,
        timestamp: snapshot.timestamp,
      ));
    }
    
    if (snapshot.fps < _minFPS && snapshot.fps > 0) {
      alerts.add(PerformanceAlert(
        type: AlertType.lowFps,
        message: 'Low FPS: ${snapshot.fps.toStringAsFixed(1)}',
        value: snapshot.fps,
        threshold: _minFPS,
        timestamp: snapshot.timestamp,
      ));
    }
    
    for (final alert in alerts) {
      _onAlertCallback?.call(alert);
    }
  }
  
  void _logSnapshot(PerformanceSnapshot snapshot) {
    print('📊 Performance: FPS=${snapshot.fps.toStringAsFixed(1)}, '
          'Memory=${snapshot.memoryUsageMB.toStringAsFixed(1)}MB, '
          'CPU=${snapshot.cpuUsagePercent.toStringAsFixed(1)}%, '
          'Temp=${snapshot.temperatureC.toStringAsFixed(1)}°C');
  }
  
  double _calculateAverage(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
}

/// Performance snapshot at a specific point in time
class PerformanceSnapshot {
  final DateTime timestamp;
  final double fps;
  final double memoryUsageMB;
  final double cpuUsagePercent;
  final double temperatureC;
  final int frameCount;
  
  const PerformanceSnapshot({
    required this.timestamp,
    required this.fps,
    required this.memoryUsageMB,
    required this.cpuUsagePercent,
    required this.temperatureC,
    required this.frameCount,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'fps': fps,
      'memoryUsageMB': memoryUsageMB,
      'cpuUsagePercent': cpuUsagePercent,
      'temperatureC': temperatureC,
      'frameCount': frameCount,
    };
  }
}

/// Performance summary over a period of time
class PerformanceSummary {
  final double avgFPS;
  final double minFPS;
  final double maxFPS;
  final double avgMemoryMB;
  final double maxMemoryMB;
  final double avgCpuPercent;
  final double maxCpuPercent;
  final double avgTemperatureC;
  final double maxTemperatureC;
  final int totalSnapshots;
  final Duration monitoringDuration;
  
  const PerformanceSummary({
    required this.avgFPS,
    required this.minFPS,
    required this.maxFPS,
    required this.avgMemoryMB,
    required this.maxMemoryMB,
    required this.avgCpuPercent,
    required this.maxCpuPercent,
    required this.avgTemperatureC,
    required this.maxTemperatureC,
    required this.totalSnapshots,
    required this.monitoringDuration,
  });
  
  factory PerformanceSummary.empty() {
    return const PerformanceSummary(
      avgFPS: 0.0,
      minFPS: 0.0,
      maxFPS: 0.0,
      avgMemoryMB: 0.0,
      maxMemoryMB: 0.0,
      avgCpuPercent: 0.0,
      maxCpuPercent: 0.0,
      avgTemperatureC: 0.0,
      maxTemperatureC: 0.0,
      totalSnapshots: 0,
      monitoringDuration: Duration.zero,
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'avgFPS': avgFPS,
      'minFPS': minFPS,
      'maxFPS': maxFPS,
      'avgMemoryMB': avgMemoryMB,
      'maxMemoryMB': maxMemoryMB,
      'avgCpuPercent': avgCpuPercent,
      'maxCpuPercent': maxCpuPercent,
      'avgTemperatureC': avgTemperatureC,
      'maxTemperatureC': maxTemperatureC,
      'totalSnapshots': totalSnapshots,
      'monitoringDurationSeconds': monitoringDuration.inSeconds,
    };
  }
}

/// Performance alert when thresholds are exceeded
class PerformanceAlert {
  final AlertType type;
  final String message;
  final double value;
  final double threshold;
  final DateTime timestamp;
  
  const PerformanceAlert({
    required this.type,
    required this.message,
    required this.value,
    required this.threshold,
    required this.timestamp,
  });
}

enum AlertType {
  highMemory,
  highCpu,
  highTemperature,
  lowFps,
}

# Back Navigation System - Hướng dẫn toàn diện

Hệ thống điều hướng "back" toàn diện cho Flutter mobile app, hỗ trợ edge swipe navigation và hardware back button.

## 🎯 Tổng quan

Hệ thống này cung cấp chức năng điều hướng "back" nhất quán cho toàn bộ ứng dụng Flutter mobile, bao gồm:

- **Edge Swipe Navigation**: Vuốt từ cạnh bên để quay lại
- **Hardware Back Button**: X<PERSON> lý nút back vật lý/ảo Android
- **Exit Confirmation**: <PERSON><PERSON><PERSON> nhận thoát ứng dụng
- **Tích hợp GoRouter**: Hoạt động seamless với routing system
- **Auto-apply**: Tự động áp dụng cho màn hình mới

## 🚀 Tính năng chính

### 1. Edge Swipe Navigation
- Vuốt từ cạnh trái màn hình để quay lại màn hình trước
- Tương tự như tính năng điều hướng cạnh mặc định trên Android
- <PERSON><PERSON> thể bật/tắt cho từng màn hình
- Threshold và velocity có thể tùy chỉnh

### 2. Hardware Back Button Support
- Xử lý sự kiện khi người dùng nhấn nút Back trên thanh điều hướng Android
- Xử lý phím Back vật lý
- Tích hợp hoàn toàn với GoRouter
- Không xung đột với navigation stack

### 3. Exit Confirmation
- Hiển thị dialog xác nhận khi thoát ứng dụng
- Áp dụng cho các màn hình chính (root screens)
- Custom exit confirmation logic
- Tự động detect root screens

### 4. Custom Back Routes
- Định nghĩa route tùy chỉnh cho back navigation
- Linh hoạt trong việc điều hướng
- Override default back behavior
- Support complex navigation flows

### 5. Intelligent Back Logic
- Tự động `context.pop()` nếu có thể
- Fallback về `/dashboard` nếu không thể pop
- Xử lý đặc biệt cho authentication flow
- Smart tab navigation handling

## 🏗️ Architecture

### Components chính

#### BackNavigationWrapper
Widget wrapper chính để xử lý back navigation.

```dart
BackNavigationWrapper(
  enableEdgeSwipe: true,
  enableHardwareBack: true,
  isRootScreen: false,
  customBackRoute: '/dashboard',
  onExitConfirmation: () => showCustomDialog(),
  child: MyScreen(),
)
```

#### BackNavigationService
Service quản lý logic back navigation.

```dart
// Xử lý back navigation
await BackNavigationService().handleBackNavigation(context);

// Kiểm tra xem có nên hiển thị back button không
bool shouldShow = BackNavigationService().shouldShowBackButton('/current-route');

// Xử lý tab back navigation
await BackNavigationService().handleTabBackNavigation(context, currentTabIndex);
```

## 📖 Cách sử dụng

### 1. Sử dụng Extension Methods (Khuyến nghị)

```dart
// Back navigation cơ bản - TỰ ĐỘNG xử lý mọi thứ
Widget myScreen = MyScreen().withBackNavigation();

// Root screen với exit confirmation
Widget rootScreen = DashboardScreen().withRootBackNavigation();

// Custom back route
Widget customScreen = ProfileScreen().withCustomBackNavigation('/dashboard');
```

### 2. Sử dụng trực tiếp (Advanced)

```dart
BackNavigationWrapper(
  enableEdgeSwipe: true,
  enableHardwareBack: true,
  isRootScreen: true,
  onExitConfirmation: () {
    // Custom exit confirmation logic
    showMyCustomExitDialog();
  },
  child: MyScreen(),
)
```

### 3. Tích hợp với GoRouter

Đã được tích hợp sẵn trong `mobile_router.dart`:

```dart
GoRoute(
  path: '/login',
  name: 'login',
  builder: (context, state) => const LoginScreen().withRootBackNavigation(),
),

GoRoute(
  path: '/profile',
  name: 'profile',
  builder: (context, state) => const ProfileScreen().withCustomBackNavigation('/dashboard'),
),

// Màn hình mới - CHỈ CẦN THÊM .withBackNavigation()
GoRoute(
  path: '/new-screen',
  name: 'new-screen',
  builder: (context, state) => const NewScreen().withBackNavigation(),
),
```

## 🎨 Cấu hình cho các loại màn hình

### Authentication Screens
```dart
// Login Screen - Root screen với exit confirmation
const LoginScreen().withRootBackNavigation()

// Tenants Screen - Back về login
const TenantsScreen().withCustomBackNavigation('/login')

// Tenant Create Screen - Back về tenants
const TenantCreateScreen().withCustomBackNavigation('/tenants')
```

### Main App Screens
```dart
// Dashboard - Root screen với exit confirmation
const DashboardScreen().withRootBackNavigation()

// Profile - Back về dashboard
const ProfileScreen().withCustomBackNavigation('/dashboard')

// Tools - Back về dashboard
const ToolsScreen().withCustomBackNavigation('/dashboard')
```

### Tab Navigation
Đối với MainScreen với tab navigation, back navigation được xử lý đặc biệt:
- Nếu đang ở tab đầu tiên: hiển thị exit confirmation
- Nếu đang ở tab khác: chuyển về tab đầu tiên

```dart
// MainScreen tự động xử lý tab back navigation
BackNavigationWrapper(
  isRootScreen: true,
  onExitConfirmation: _handleExitConfirmation,
  child: Scaffold(/* tab content */),
)
```

## ⚙️ Tùy chỉnh nâng cao

### Custom Exit Confirmation
```dart
BackNavigationWrapper(
  isRootScreen: true,
  onExitConfirmation: () {
    // Custom logic
    showDialog(
      context: context,
      builder: (context) => MyCustomExitDialog(),
    );
  },
  child: MyScreen(),
)
```

### Disable Edge Swipe
```dart
MyScreen().withBackNavigation(
  enableEdgeSwipe: false,
  enableHardwareBack: true,
)
```

### Disable Hardware Back
```dart
MyScreen().withBackNavigation(
  enableEdgeSwipe: true,
  enableHardwareBack: false,
)
```

### Disable tất cả (Loading screens)
```dart
LoadingScreen().withBackNavigation(
  enableEdgeSwipe: false,
  enableHardwareBack: false,
)
```

## 💡 Best Practices

1. **Sử dụng extension methods** cho code ngắn gọn và dễ đọc
2. **Root screens** nên sử dụng `withRootBackNavigation()`
3. **Sub screens** nên sử dụng `withCustomBackNavigation()` với route phù hợp
4. **Splash screen** nên disable cả edge swipe và hardware back
5. **Loading screens** nên disable back navigation
6. **Test thoroughly** trên cả Android và iOS
7. **Màn hình mới chỉ cần** `.withBackNavigation()` - không cần code thêm

## 🔧 Troubleshooting

### Back navigation không hoạt động
- ✅ Kiểm tra xem đã wrap screen với BackNavigationWrapper chưa
- ✅ Kiểm tra GoRouter configuration
- ✅ Kiểm tra enableEdgeSwipe và enableHardwareBack flags
- ✅ Đảm bảo import đúng: `import 'package:c_face_terminal/shared/presentation/widgets/navigation/index.dart';`

### Exit confirmation không hiển thị
- ✅ Đảm bảo isRootScreen = true
- ✅ Kiểm tra onExitConfirmation callback
- ✅ Kiểm tra route có trong danh sách root screens không

### Edge swipe quá nhạy/không nhạy
- ✅ Điều chỉnh `_edgeSwipeThreshold` (default: 50.0)
- ✅ Điều chỉnh `_swipeVelocityThreshold` (default: 300.0)
- ✅ Test trên thiết bị thật, không chỉ emulator

### Custom back button không hoạt động
```dart
// Sử dụng service có sẵn
IconButton(
  icon: Icon(Icons.arrow_back),
  onPressed: () => context.handleBackWithService(),
)

// Hoặc đơn giản
IconButton(
  icon: Icon(Icons.arrow_back),
  onPressed: () => context.pop(),
)
```

## 🎯 Khi nào cần code thêm?

### ❌ KHÔNG cần code thêm cho:
- Màn hình mới thông thường
- Edge swipe navigation
- Hardware back button
- Logic back navigation cơ bản

### ✅ CẦN code thêm khi:
1. **Tự tạo back button trong UI**
2. **Logic back đặc biệt** (custom confirmation, save data, etc.)
3. **Disable back navigation** cho loading/splash screens
4. **Custom exit confirmation** với UI riêng

## 📁 File Structure

```
lib/
├── shared/
│   ├── presentation/
│   │   └── widgets/
│   │       └── navigation/
│   │           ├── back_navigation_wrapper.dart
│   │           └── index.dart
│   └── services/
│       └── back_navigation_service.dart
├── apps/
│   └── mobile/
│       └── routes/
│           └── mobile_router.dart (đã tích hợp)
└── docs/
    └── BackNavigation.md (file này)
```

## 🚀 Quick Start cho màn hình mới

```dart
// 1. Tạo màn hình mới
class NewScreen extends StatelessWidget {
  const NewScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('New Screen')),
      body: Center(child: Text('Content')),
    );
  }
}

// 2. Thêm vào GoRouter
GoRoute(
  path: '/new-screen',
  name: 'new-screen',
  builder: (context, state) => const NewScreen().withBackNavigation(),
),

// 3. XONG! Đã có đầy đủ back navigation 🎉
```

---

**Lưu ý**: Hệ thống này đã được test và tối ưu cho Flutter mobile app. Mọi màn hình mới chỉ cần thêm `.withBackNavigation()` là đã có đầy đủ chức năng back navigation!

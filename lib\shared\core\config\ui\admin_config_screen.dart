/// Admin Configuration Screen
/// 
/// Provides a comprehensive interface for administrators to configure
/// all application parameters in real-time.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../configuration_manager.dart';
import '../config_parameters_registry.dart';
import '../flexible_config_system.dart';
import 'config_title_helper.dart';
import 'config_help_dialog.dart';

class AdminConfigScreen extends StatefulWidget {
  const AdminConfigScreen({super.key});

  @override
  State<AdminConfigScreen> createState() => _AdminConfigScreenState();
}

class _AdminConfigScreenState extends State<AdminConfigScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, dynamic> _currentValues = {};
  final Map<String, String> _validationErrors = {};
  final Map<String, bool> _categoryManualMode = {}; // Track manual/default mode per category
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeTabs();
    _loadCurrentConfiguration();
  }

  void _initializeTabs() {
    final categoryList = [
      ConfigCategories.faceDetection,
      ConfigCategories.network,
      ConfigCategories.ui,
      ConfigCategories.performance,
      ConfigCategories.camera,
      ConfigCategories.security,
      ConfigCategories.cache,
      ConfigCategories.kiosk,
      ConfigCategories.debug,
    ];
    
    _tabController = TabController(
      length: categoryList.length,
      vsync: this,
    );
  }

  Future<void> _loadCurrentConfiguration() async {
    setState(() => _isLoading = true);

    try {
      final manager = ConfigurationManager.instance;
      final parameters = ConfigParametersRegistry.getAllParameters();

      // Initialize category manual mode states
      final categories = [
        ConfigCategories.faceDetection,
        ConfigCategories.network,
        ConfigCategories.ui,
        ConfigCategories.performance,
        ConfigCategories.camera,
        ConfigCategories.security,
        ConfigCategories.cache,
        ConfigCategories.kiosk,
        ConfigCategories.debug,
      ];

      for (final category in categories) {
        final manualModeKey = '${category}_manual_mode';
        _categoryManualMode[category] = manager.getValue<bool>(manualModeKey, defaultValue: false);
      }

      for (final parameter in parameters.values) {
        final value = manager.getValue(parameter.key, defaultValue: parameter.defaultValue);
        _currentValues[parameter.key] = value;

        // Format value properly for display
        String displayValue;
        if (value is Duration) {
          // Display duration as seconds for consistency with parser
          final seconds = value.inMilliseconds / 1000;
          displayValue = seconds == seconds.toInt() ? '${seconds.toInt()}' : seconds.toStringAsFixed(1);
        } else if (value is Color) {
          final argb = ((value.a * 255).round() << 24) |
                      ((value.r * 255).round() << 16) |
                      ((value.g * 255).round() << 8) |
                      (value.b * 255).round();
          displayValue = '0x${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
        } else {
          displayValue = value.toString();
        }

        _controllers[parameter.key] = TextEditingController(text: displayValue);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load configuration: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveConfiguration() async {
    setState(() => _isLoading = true);
    _validationErrors.clear();
    
    try {
      final manager = ConfigurationManager.instance;
      final parameters = ConfigParametersRegistry.getAllParameters();
      
      // Validate only changed values
      bool hasErrors = false;
      final changedValues = <String, dynamic>{};

      for (final entry in _controllers.entries) {
        final parameter = parameters[entry.key];
        if (parameter == null) continue;

        // Check if value has changed
        final currentDisplayValue = entry.value.text;
        final originalValue = _currentValues[entry.key];

        String originalDisplayValue;
        if (originalValue is Duration) {
          // Display duration as seconds for consistency with parser
          final seconds = originalValue.inMilliseconds / 1000;
          originalDisplayValue = seconds == seconds.toInt() ? '${seconds.toInt()}' : seconds.toStringAsFixed(1);
        } else if (originalValue is Color) {
          final argb = ((originalValue.a * 255).round() << 24) |
                      ((originalValue.r * 255).round() << 16) |
                      ((originalValue.g * 255).round() << 8) |
                      (originalValue.b * 255).round();
          originalDisplayValue = '0x${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
        } else {
          originalDisplayValue = originalValue.toString();
        }

        // Only validate if value has changed
        if (currentDisplayValue != originalDisplayValue) {
          try {
            final value = parameter.parseValue(currentDisplayValue);
            if (!parameter.isValid(value)) {
              _validationErrors[entry.key] = 'Invalid value for ${parameter.description}';
              hasErrors = true;
            } else {
              changedValues[entry.key] = value;
            }
          } catch (e) {
            String errorMessage = 'Parse error: $e';
            
            // Provide more user-friendly error messages for common issues
            if (e.toString().contains('FormatException')) {
              if (entry.key == 'face_detection.recognition_throttle_duration') {
                errorMessage = 'Vui lòng nhập số giây (ví dụ: 5 hoặc 8.5)';
              } else if (entry.key.contains('duration') || entry.key.contains('timeout') || entry.key.contains('delay')) {
                errorMessage = 'Vui lòng nhập số giây (ví dụ: 5 hoặc 8.5)';
              } else {
                errorMessage = 'Định dạng không hợp lệ: $e';
              }
            }
            
            _validationErrors[entry.key] = errorMessage;
            hasErrors = true;
          }
        }
      }
      
      if (hasErrors) {
        setState(() {});
        _showErrorSnackBar('Vui lòng sửa các lỗi validation trước khi lưu');
        return;
      }

      if (changedValues.isEmpty) {
        _showSuccessSnackBar('Không có thay đổi nào để lưu');
        return;
      }

      // Save only changed values
      for (final entry in changedValues.entries) {
        await manager.setValue(entry.key, entry.value);
        _currentValues[entry.key] = entry.value;
      }

      // Save manual mode states
      for (final entry in _categoryManualMode.entries) {
        final manualModeKey = '${entry.key}_manual_mode';
        await manager.setValue(manualModeKey, entry.value);
      }

      setState(() => _hasUnsavedChanges = false);
      _showSuccessSnackBar('✅ Đã lưu cấu hình thành công (${changedValues.length} thay đổi)');
      
    } catch (e) {
      _showErrorSnackBar('Failed to save configuration: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await _showConfirmDialog(
      'Reset to Defaults',
      'Are you sure you want to reset all configuration to default values? This cannot be undone.',
    );
    
    if (!confirmed) return;
    
    setState(() => _isLoading = true);
    
    try {
      final parameters = ConfigParametersRegistry.getAllParameters();
      
      for (final parameter in parameters.values) {
        _controllers[parameter.key]?.text = parameter.defaultValue.toString();
        _currentValues[parameter.key] = parameter.defaultValue;
      }
      
      setState(() => _hasUnsavedChanges = true);
      _showSuccessSnackBar('🔄 Đã reset cấu hình về mặc định. Nhấn Lưu để áp dụng.');
      
    } catch (e) {
      _showErrorSnackBar('Failed to reset configuration: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportConfiguration() async {
    try {
      final manager = ConfigurationManager.instance;
      final config = manager.exportConfiguration(includeSecure: false);
      
      await Clipboard.setData(ClipboardData(
        text: config.entries
            .map((e) => '${e.key}=${e.value}')
            .join('\n'),
      ));
      
      _showSuccessSnackBar('📋 Đã xuất cấu hình vào clipboard');
    } catch (e) {
      _showErrorSnackBar('Failed to export configuration: $e');
    }
  }

  Future<void> _importConfiguration() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data?.text == null || data!.text!.isEmpty) {
      _showErrorSnackBar('No configuration data found in clipboard');
      return;
    }

    try {
      final lines = data.text!.split('\n');
      final config = <String, dynamic>{};

      for (final line in lines) {
        if (line.trim().isEmpty || !line.contains('=')) continue;

        final parts = line.split('=');
        if (parts.length >= 2) {
          final key = parts[0].trim();
          final value = parts.sublist(1).join('=').trim();
          config[key] = value;
        }
      }

      if (config.isEmpty) {
        _showErrorSnackBar('No valid configuration found in clipboard');
        return;
      }

      final confirmed = await _showConfirmDialog(
        'Import Configuration',
        'Import ${config.length} configuration values? This will overwrite current values.',
      );

      if (!confirmed) return;

      // Update controllers with imported values
      final parameters = ConfigParametersRegistry.getAllParameters();
      for (final entry in config.entries) {
        final parameter = parameters[entry.key];
        if (parameter != null && _controllers.containsKey(entry.key)) {
          _controllers[entry.key]!.text = entry.value.toString();
        }
      }

      setState(() => _hasUnsavedChanges = true);
      _showSuccessSnackBar('📥 Đã nhập cấu hình. Nhấn Lưu để áp dụng.');

    } catch (e) {
      _showErrorSnackBar('Failed to import configuration: $e');
    }
  }

  /// Toggle manual mode for a category
  Future<void> _toggleCategoryManualMode(String category) async {
    final currentMode = _categoryManualMode[category] ?? false;
    final newMode = !currentMode;

    setState(() {
      _categoryManualMode[category] = newMode;
      _hasUnsavedChanges = true;
    });

    // Save manual mode state
    try {
      final manager = ConfigurationManager.instance;
      final manualModeKey = '${category}_manual_mode';
      await manager.setValue(manualModeKey, newMode);

      // If switching to default mode, reset all parameters in this category to defaults
      if (!newMode) {
        final parameters = ConfigParametersRegistry.getParametersByCategory(category);
        for (final parameter in parameters.values) {
          final defaultDisplayValue = _getDefaultDisplayValue(parameter);
          _controllers[parameter.key]?.text = defaultDisplayValue;
          _currentValues[parameter.key] = parameter.defaultValue;
          await manager.setValue(parameter.key, parameter.defaultValue);
        }

        _showSuccessSnackBar('🔄 Đã chuyển về chế độ mặc định cho ${_getCategoryDisplayName(category)}');
      } else {
        _showSuccessSnackBar('✏️ Đã bật chế độ cấu hình thủ công cho ${_getCategoryDisplayName(category)}');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to toggle manual mode: $e');
    }
  }

  /// Get category display name in Vietnamese
  String _getCategoryDisplayName(String category) {
    switch (category) {
      case ConfigCategories.faceDetection:
        return 'Nhận diện khuôn mặt';
      case ConfigCategories.network:
        return 'Mạng';
      case ConfigCategories.ui:
        return 'Giao diện';
      case ConfigCategories.performance:
        return 'Hiệu suất';
      case ConfigCategories.camera:
        return 'Camera';
      case ConfigCategories.security:
        return 'Bảo mật';
      case ConfigCategories.cache:
        return 'Cache';
      case ConfigCategories.kiosk:
        return 'Kiosk';
      case ConfigCategories.debug:
        return 'Debug';
      default:
        return category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Configuration'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Nhận diện khuôn mặt'),
            Tab(text: 'Mạng'),
            Tab(text: 'Giao diện'),
            Tab(text: 'Hiệu suất'),
            Tab(text: 'Camera'),
            Tab(text: 'Bảo mật'),
            Tab(text: 'Cache'),
            Tab(text: 'Kiosk'),
            Tab(text: 'Debug'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportConfiguration,
            tooltip: 'Export Configuration',
          ),
          IconButton(
            icon: const Icon(Icons.upload),
            onPressed: _importConfiguration,
            tooltip: 'Import Configuration',
          ),
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetToDefaults,
            tooltip: 'Reset to Defaults',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildCategoryTab(ConfigCategories.faceDetection),
                _buildCategoryTab(ConfigCategories.network),
                _buildCategoryTab(ConfigCategories.ui),
                _buildCategoryTab(ConfigCategories.performance),
                _buildCategoryTab(ConfigCategories.camera),
                _buildCategoryTab(ConfigCategories.security),
                _buildCategoryTab(ConfigCategories.cache),
                _buildCategoryTab(ConfigCategories.kiosk),
                _buildCategoryTab(ConfigCategories.debug),
              ],
            ),
      floatingActionButton: _hasUnsavedChanges
          ? FloatingActionButton.extended(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
              label: const Text('Save Changes'),
            )
          : null,
    );
  }

  Widget _buildCategoryTab(String category) {
    final parameters = ConfigParametersRegistry.getParametersByCategory(category);
    final isManualMode = _categoryManualMode[category] ?? false;

    if (parameters.isEmpty) {
      return const Center(
        child: Text('No parameters found for this category'),
      );
    }

    return Column(
      children: [
        // Category manual mode toggle
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                isManualMode ? Icons.edit : Icons.auto_awesome,
                color: isManualMode ? Colors.orange : Colors.green,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isManualMode ? 'Cấu hình thủ công' : 'Sử dụng giá trị mặc định',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isManualMode ? Colors.orange.shade700 : Colors.green.shade700,
                      ),
                    ),
                    Text(
                      isManualMode
                        ? 'Cho phép chỉnh sửa các tham số trong danh mục này'
                        : 'Tất cả tham số sử dụng giá trị mặc định được khuyến nghị',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: isManualMode,
                onChanged: (_) => _toggleCategoryManualMode(category),
                activeColor: Colors.orange,
                inactiveThumbColor: Colors.green,
                inactiveTrackColor: Colors.green.withValues(alpha: 0.3),
              ),
            ],
          ),
        ),
        // Parameters list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: parameters.length,
            itemBuilder: (context, index) {
              final parameter = parameters.values.elementAt(index);
              return _buildParameterCard(parameter, isManualMode);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildParameterCard(ConfigParameter parameter, bool isManualMode) {
    final controller = _controllers[parameter.key];
    final hasError = _validationErrors.containsKey(parameter.key);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ConfigTitleHelper.getTitle(parameter.key),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        parameter.key,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade500,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    // Help button
                    IconButton(
                      icon: Icon(
                        Icons.help_outline,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      onPressed: () {
                        ConfigHelpDialog.show(
                          context,
                          parameter.key,
                          controller?.text ?? '',
                        );
                      },
                      tooltip: 'Xem hướng dẫn chi tiết',
                    ),
                    if (parameter.isRequired)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Required',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              parameter.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: controller,
              enabled: isManualMode,
              decoration: InputDecoration(
                labelText: 'Value',
                hintText: _getParameterHint(parameter),
                errorText: hasError ? _validationErrors[parameter.key] : null,
                border: const OutlineInputBorder(),
                suffixIcon: isManualMode ? IconButton(
                  icon: const Icon(Icons.restore),
                  onPressed: () {
                    controller?.text = _getDefaultDisplayValue(parameter);
                    setState(() => _hasUnsavedChanges = true);
                  },
                  tooltip: 'Reset to default',
                ) : Icon(
                  Icons.lock,
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                ),
                helperText: isManualMode ? null : 'Bật chế độ thủ công để chỉnh sửa',
              ),
              onChanged: isManualMode ? (value) {
                setState(() => _hasUnsavedChanges = true);
                _validationErrors.remove(parameter.key);
              } : null,
            ),
            if (parameter.minValue != null || parameter.maxValue != null) ...[
              const SizedBox(height: 8),
              Text(
                'Range: ${parameter.minValue ?? 'No min'} - ${parameter.maxValue ?? 'No max'}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            if (parameter.allowedValues != null) ...[
              const SizedBox(height: 8),
              Text(
                'Allowed values: ${parameter.allowedValues!.join(', ')}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            if (parameter.environmentKey != null) ...[
              const SizedBox(height: 8),
              Text(
                'Environment variable: ${parameter.environmentKey}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.blue.shade600,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<bool> _showConfirmDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// Get parameter hint text based on type
  String _getParameterHint(ConfigParameter parameter) {
    if (parameter.type == ConfigValueType.duration) {
      final defaultSeconds = (parameter.defaultValue as Duration).inMilliseconds / 1000;
      return 'Nhập số giây (ví dụ: $defaultSeconds)';
    }
    return 'Default: ${_getDefaultDisplayValue(parameter)}';
  }

  /// Get properly formatted default display value
  String _getDefaultDisplayValue(ConfigParameter parameter) {
    final value = parameter.defaultValue;
    if (value is Duration) {
      final seconds = value.inMilliseconds / 1000;
      return seconds == seconds.toInt() ? '${seconds.toInt()}' : seconds.toStringAsFixed(1);
    } else if (value is Color) {
      final argb = ((value.a * 255).round() << 24) |
                  ((value.r * 255).round() << 16) |
                  ((value.g * 255).round() << 8) |
                  (value.b * 255).round();
      return '0x${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
    }
    return value.toString();
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}

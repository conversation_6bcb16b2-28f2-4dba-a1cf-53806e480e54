import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Test communication with the new relay controller test server
/// 
/// This demonstrates the updated API endpoints and communication patterns
/// that are now supported by RelayApiService.
class TestServerCommunication {
  static const String baseUrl = 'http://localhost:3000';
  
  /// Test device registration using secure API
  static Future<void> testSecureDeviceRegistration() async {
    print('\n🔐 Testing Secure Device Registration...');
    
    final deviceData = {
      'device_id': 'T-A3B4-R01',
      'device_type': 'relay',
      'device_name': 'Main Door Relay',
      'hardware_hash': 'hash_12345',
      'app_version': '1.0.0',
      'terminal_id': 'T-A3B4',
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/device/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(deviceData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('✅ Device registered successfully!');
        print('Access Token: ${data['access_token']}');
        print('Secret Key: ${data['secret_key']}');
        return data;
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test legacy device registration
  static Future<void> testLegacyDeviceRegistration() async {
    print('\n📱 Testing Legacy Device Registration...');
    
    final deviceData = {
      'deviceId': 'T-A3B4-R02',
      'deviceName': 'Secondary Door Relay',
      'type': 'relay',
      'terminalId': 'T-A3B4',
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(deviceData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        print('✅ Legacy device registered successfully!');
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test relay control
  static Future<void> testRelayControl() async {
    print('\n🔌 Testing Relay Control...');
    
    final controlData = {
      'deviceId': 'T-A3B4-R01',
      'command': 'ON',
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/relay/control'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(controlData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        print('✅ Relay control successful!');
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test face recognition
  static Future<void> testFaceRecognition() async {
    print('\n👤 Testing Face Recognition...');
    
    // Sample base64 image data (placeholder)
    final imageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    
    final recognitionData = {
      'image_data': imageData,
      'confidence_score': 0.85,
      'metadata': {
        'source': 'test_client',
        'timestamp': DateTime.now().toIso8601String(),
      },
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/face/recognize'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(recognitionData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('✅ Face recognition successful!');
        print('Recognized: ${data['data']['recognized']}');
        print('User: ${data['data']['user']?['name'] ?? 'Unknown'}');
        print('Confidence: ${data['data']['confidence']}');
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test secure messaging
  static Future<void> testSecureMessaging() async {
    print('\n🔐 Testing Secure Messaging...');
    
    final messageData = {
      'type': 'ping',
      'payload': {
        'message': 'Hello from test client',
        'timestamp': DateTime.now().toIso8601String(),
      },
      'message_id': 'test_${DateTime.now().millisecondsSinceEpoch}',
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/message'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(messageData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        print('✅ Secure messaging successful!');
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test plain text messaging
  static Future<void> testPlainMessaging() async {
    print('\n📨 Testing Plain Text Messaging...');
    
    final messageData = {
      'type': 'ping',
      'payload': {
        'message': 'Hello from plain text client',
      },
      'device_id': 'T-A3B4-R01',
      'device_type': 'relay',
      'message_id': 'plain_${DateTime.now().millisecondsSinceEpoch}',
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/message/plain'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(messageData),
      );
      
      print('Status: ${response.statusCode}');
      print('Response: ${response.body}');
      
      if (response.statusCode == 200) {
        print('✅ Plain text messaging successful!');
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Test device naming system
  static Future<void> testDeviceNaming() async {
    print('\n🏷️ Testing Device Naming System...');
    
    try {
      // Generate terminal ID
      final terminalResponse = await http.get(
        Uri.parse('$baseUrl/api/naming/generate-terminal-id'),
      );
      
      print('Terminal ID Generation:');
      print('Status: ${terminalResponse.statusCode}');
      print('Response: ${terminalResponse.body}');
      
      if (terminalResponse.statusCode == 200) {
        final terminalData = jsonDecode(terminalResponse.body);
        final terminalId = terminalData['terminal_id'];
        
        // Generate relay ID
        final relayResponse = await http.post(
          Uri.parse('$baseUrl/api/naming/generate-relay-id'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'terminal_id': terminalId,
            'relay_index': 1,
          }),
        );
        
        print('\nRelay ID Generation:');
        print('Status: ${relayResponse.statusCode}');
        print('Response: ${relayResponse.body}');
        
        if (relayResponse.statusCode == 200) {
          print('✅ Device naming system working!');
        }
      }
    } catch (e) {
      print('❌ Error: $e');
    }
  }
  
  /// Run all tests
  static Future<void> runAllTests() async {
    print('🚀 Starting Test Server Communication Tests...');
    print('Server: $baseUrl');
    
    await testSecureDeviceRegistration();
    await testLegacyDeviceRegistration();
    await testRelayControl();
    await testFaceRecognition();
    await testSecureMessaging();
    await testPlainMessaging();
    await testDeviceNaming();
    
    print('\n✅ All tests completed!');
  }
}

/// Main function to run tests
void main() async {
  await TestServerCommunication.runAllTests();
}

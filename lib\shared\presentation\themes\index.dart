/// Themes Index - Export all theme-related classes
///
/// This file exports all theme configurations, color schemes, and text themes
/// for consistent styling across mobile and terminal applications.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/presentation/themes/index.dart';
///
/// // Use in MaterialApp
/// MaterialApp(
///   theme: MobileTheme.lightTheme,
///   darkTheme: MobileTheme.darkTheme,
///   // ... other properties
/// )
///
/// // Or for terminal app
/// MaterialApp(
///   theme: TerminalTheme.lightTheme,
///   darkTheme: TerminalTheme.darkTheme,
///   // ... other properties
/// )
/// ```
library;

// ============================================================================
// THEME EXPORTS
// ============================================================================

/// Base app theme with common styling
export 'app_theme.dart';

/// Mobile-specific theme optimized for touch interactions
export 'mobile_theme.dart';

/// Terminal-specific theme optimized for desktop/kiosk interactions
export 'terminal_theme.dart';

/// Color schemes for light and dark themes
export 'color_schemes.dart';

/// Text themes and typography system
export 'text_themes.dart';

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <include layout="@layout/titlebar" />

    <Button
        android:id="@+id/led_open"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        style="@style/buttonNumberStyle"
        android:layout_margin="5dp"
        android:onClick="onClick"
        android:background="@drawable/button_number_violet_shape"
        android:text="@string/bt_led_open" />

    <Button
        android:id="@+id/led_close"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        style="@style/buttonNumberStyle"
        android:layout_margin="5dp"
        android:onClick="onClick"
        android:background="@drawable/button_number_violet_shape"
        android:text="@string/bt_led_close" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="Brightness: "/>

    <SeekBar
        android:layout_margin="20dp"
        android:id="@+id/seekbar"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:max="255"
        android:progress="70" />

</LinearLayout>

/// Navigation Widgets Index - Export all navigation-related widgets
///
/// This file exports all navigation widget components that provide consistent
/// back navigation functionality across the mobile application.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/presentation/widgets/navigation/index.dart';
///
/// // Use BackNavigationWrapper
/// Widget myScreen = MyScreen().withBackNavigation();
/// 
/// // Use BackNavigationService
/// await context.handleBackWithService();
/// ```
library;

// ============================================================================
// NAVIGATION WIDGET EXPORTS
// ============================================================================

/// Back navigation wrapper widget for handling edge swipe and hardware back
export 'back_navigation_wrapper.dart';

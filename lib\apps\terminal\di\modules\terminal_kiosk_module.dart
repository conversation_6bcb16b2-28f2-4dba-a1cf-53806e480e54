import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Terminal Kiosk Module
/// 
/// This module registers terminal-specific kiosk dependencies
/// including kiosk mode services, session management,
/// auto-restart functionality, and terminal-specific security.
/// 
/// Note: This module will be fully implemented when terminal
/// kiosk features are developed.
void registerTerminalKioskDependencies() {
  // TODO: Implement when terminal kiosk features are developed
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<KioskModeService>(
  //   () => KioskModeServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalSessionManager>(
  //   () => TerminalSessionManagerImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalAutoRestartService>(
  //   () => TerminalAutoRestartServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalSecurityService>(
  //   () => TerminalSecurityServiceImpl(),
  // );
}

/// Unregister terminal kiosk dependencies (for testing)
void unregisterTerminalKioskDependencies() {
  // TODO: Implement when kiosk dependencies are added
  // if (getIt.isRegistered<KioskModeService>()) {
  //   getIt.unregister<KioskModeService>();
  // }
  // if (getIt.isRegistered<TerminalSessionManager>()) {
  //   getIt.unregister<TerminalSessionManager>();
  // }
  // if (getIt.isRegistered<TerminalAutoRestartService>()) {
  //   getIt.unregister<TerminalAutoRestartService>();
  // }
  // if (getIt.isRegistered<TerminalSecurityService>()) {
  //   getIt.unregister<TerminalSecurityService>();
  // }
}

/// Reset terminal kiosk module (clear và re-register)
void resetTerminalKioskModule() {
  unregisterTerminalKioskDependencies();
  registerTerminalKioskDependencies();
}

/// Check if terminal kiosk dependencies are registered
bool areTerminalKioskDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<KioskModeService>() &&
  //        getIt.isRegistered<TerminalSessionManager>() &&
  //        getIt.isRegistered<TerminalAutoRestartService>() &&
  //        getIt.isRegistered<TerminalSecurityService>();
  return true; // Placeholder
}

/// Get terminal kiosk dependencies for debugging
Map<String, bool> getTerminalKioskDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'KioskModeService': getIt.isRegistered<KioskModeService>(),
    // 'TerminalSessionManager': getIt.isRegistered<TerminalSessionManager>(),
    // 'TerminalAutoRestartService': getIt.isRegistered<TerminalAutoRestartService>(),
    // 'TerminalSecurityService': getIt.isRegistered<TerminalSecurityService>(),
  };
}

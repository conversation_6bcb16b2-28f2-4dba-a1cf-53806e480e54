import 'dart:convert';
import 'dart:typed_data';
import 'package:usb_serial/usb_serial.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses USB Serial communication.
/// 
/// This controller sends "ON\n" and "OFF\n" commands through USB OTG
/// to control relay states.
/// 
/// Example usage:
/// ```dart
/// final controller = UsbRelayController();
/// 
/// // List available devices
/// final devices = await UsbRelayController.getAvailableDevices();
/// 
/// // Connect to first available device
/// if (devices.isNotEmpty) {
///   await controller.connect(devices.first);
///   await controller.triggerOn();
///   await controller.triggerOff();
///   await controller.dispose();
/// }
/// ```
class UsbRelayController extends RelayController {
  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Baud rate for serial communication.
  final int baudRate;

  /// Data bits for serial communication.
  final int dataBits;

  /// Stop bits for serial communication.
  final int stopBits;

  /// Parity for serial communication.
  final int parity;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  UsbPort? _port;
  UsbDevice? _connectedDevice;
  bool _isConnected = false;

  /// Creates a new [UsbRelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [baudRate] is the baud rate for serial communication (default: 9600).
  /// [dataBits] is the number of data bits (default: 8).
  /// [stopBits] is the number of stop bits (default: 1).
  /// [parity] is the parity setting (default: 0 for none).
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  UsbRelayController({
    required super.deviceId,
    super.deviceName = 'USB Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.baudRate = 9600,
    this.dataBits = 8,
    this.stopBits = 1,
    this.parity = 0,
    this.timeoutSeconds = 10,
  });

  /// Connects to a USB device.
  ///
  /// [device] is the USB device to connect to.
  /// This method must be called before using [triggerOn] or [triggerOff].
  ///
  /// Throws [UsbRelayException] if connection fails.
  Future<void> connect(UsbDevice device) async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      _port = await device.create();
      if (_port == null) {
        throw const UsbRelayException('Failed to create USB port');
      }

      bool openResult = await _port!.open();
      if (!openResult) {
        throw const UsbRelayException('Failed to open USB port');
      }

      // Configure serial parameters
      await _port!.setDTR(true);
      await _port!.setRTS(true);
      await _port!.setPortParameters(
        baudRate,
        dataBits,
        stopBits,
        parity,
      );

      _connectedDevice = device;
      _isConnected = true;
    } catch (e) {
      if (e is UsbRelayException) {
        rethrow;
      }
      throw UsbRelayException('Failed to connect to USB device', e);
    }
  }

  /// Connects to the first available USB device.
  /// 
  /// This is a convenience method that automatically finds and connects
  /// to the first available USB device.
  /// 
  /// Throws [UsbRelayException] if no devices are available or connection fails.
  Future<void> connectToFirstAvailable() async {
    final devices = await getAvailableDevices();
    if (devices.isEmpty) {
      throw const UsbRelayException('No USB devices available');
    }
    await connect(devices.first);
  }

  /// Disconnects from the USB device.
  Future<void> disconnect() async {
    try {
      if (_port != null && _isConnected) {
        await _port!.close();
        _isConnected = false;
        _connectedDevice = null;
      }
    } catch (e) {
      throw UsbRelayException('Failed to disconnect from USB device', e);
    }
  }

  /// Sends a command to the USB device.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected || _port == null) {
        throw const UsbRelayException('Not connected to USB device');
      }

      Uint8List data = Uint8List.fromList(utf8.encode(command));
      await _port!.write(data);
    } catch (e) {
      if (e is UsbRelayException) {
        rethrow;
      }
      throw UsbRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets the connected USB device info.
  UsbDevice? get connectedDevice => _connectedDevice;

  /// Gets available USB devices.
  /// 
  /// Returns a list of available USB devices that can be used for serial communication.
  static Future<List<UsbDevice>> getAvailableDevices() async {
    try {
      return await UsbSerial.listDevices();
    } catch (e) {
      throw UsbRelayException('Failed to get available USB devices', e);
    }
  }

  /// Checks if USB host mode is supported on the device.
  static Future<bool> isUsbHostSupported() async {
    try {
      final devices = await UsbSerial.listDevices();
      return devices.isNotEmpty || true; // Assume supported if we can list devices
    } catch (e) {
      return false;
    }
  }
}

/// A simplified USB relay controller that automatically connects to the first available device.
/// 
/// Example usage:
/// ```dart
/// final controller = AutoConnectUsbRelayController();
/// 
/// await controller.initialize();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class AutoConnectUsbRelayController extends UsbRelayController {
  /// Creates an auto-connecting USB relay controller.
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON.
  /// [offCommand] is the command sent to turn the relay OFF.
  /// [baudRate] is the baud rate for serial communication.
  /// [timeoutSeconds] is the connection timeout in seconds.
  AutoConnectUsbRelayController({
    required super.deviceId,
    super.deviceName = 'Auto USB Relay',
    super.onCommand = 'ON\n',
    super.offCommand = 'OFF\n',
    super.baudRate = 9600,
    super.timeoutSeconds = 10,
  });

  /// Initializes the controller by connecting to the first available USB device.
  /// 
  /// This method automatically finds and connects to the first available USB device.
  /// 
  /// Throws [UsbRelayException] if no devices are available or connection fails.
  Future<void> initialize() async {
    await connectToFirstAvailable();
  }
}

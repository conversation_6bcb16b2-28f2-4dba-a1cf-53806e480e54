name: relay_controller
description: A Flutter library for controlling relays through various communication methods including Bluetooth, HTTP, MQTT, and USB Serial.
version: 1.0.0
homepage: https://github.com/your-username/relay_controller

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # HTTP communication
  http: ^1.1.0

  # Cryptography for security
  crypto: ^3.0.3

  # Bluetooth communication (modern alternatives)
  bluetooth_classic: ^0.0.4
  flutter_blue_classic: ^0.0.6

  # MQTT communication (optional)
  mqtt_client: ^10.0.0

  # USB Serial communication (optional)
  usb_serial: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:

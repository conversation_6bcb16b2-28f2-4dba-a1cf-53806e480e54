library;

/// Configurable color definitions for the application
///
/// This class now uses the flexible configuration system instead of hardcoded values.
/// All colors can be configured through environment variables, configuration files,
/// or runtime changes through the admin interface.

import 'package:flutter/material.dart';
import '../../shared/core/config/config_helper.dart';

class AppColors {
  AppColors._();

  // Primary Colors
  static Color get primary => Color(ConfigHelper.getValue('ui.primary_color', 0xFF008FD3));
  static Color get primaryLight => Color(ConfigHelper.getValue('ui.primary_light_color', 0xFFABDBF2));
  static Color get primaryBackground => Color(ConfigHelper.getValue('ui.primary_background_color', 0xFFE5F3FA));

  // Background Colors
  static Color get background => Color(ConfigHelper.getValue('ui.background_color', 0xFFF4FBFF));
  static Color get surface => Color(ConfigHelper.getValue('ui.surface_color', 0xFFFFFFFF));
  static Color get surfaceVariant => Color(ConfigHelper.getValue('ui.surface_variant_color', 0xFFF5F5F5));

  // Text Colors
  static Color get textPrimary => Color(ConfigHelper.getValue('ui.text_primary_color', 0xFF1F2329));
  static Color get textSecondary => Color(ConfigHelper.getValue('ui.text_secondary_color', 0xFF85888C));
  static Color get textTertiary => Color(ConfigHelper.getValue('ui.text_tertiary_color', 0xFF8F959E));
  static Color get textPlaceholder => Color(ConfigHelper.getValue('ui.text_placeholder_color', 0xFF8F959E));
  static Color get textOnPrimary => Color(ConfigHelper.getValue('ui.text_on_primary_color', 0xFFFFFFFF));

  // Border Colors
  static const Color border = Color(0xFFE5E6E7);
  static const Color borderFocused = Color(0xFF008FD3);

  // Status Colors
  static const Color error = Color(0xFFF54A45);
  static const Color success = Color(0xFF40BF24);
  static const Color successBackground = Color(0xFFEEFAED);
  static const Color warning = Color(0xFFFF9500);
  static const Color info = Color(0xFF007AFF);

  // Neutral Colors
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFE5E6E7);
  static const Color neutral300 = Color(0xFFD1D5DB);
  static const Color neutral400 = Color(0xFF9CA3AF);
  static const Color neutral500 = Color(0xFF6B7280);
  static const Color neutral600 = Color(0xFF4B5563);
  static const Color neutral700 = Color(0xFF374151);
  static const Color neutral800 = Color(0xFF1F2937);
  static const Color neutral900 = Color(0xFF111827);

  // Specific Component Colors
  static const Color avatarBackground = Color(0xFFE5F3FA);
  static const Color disabledButton = Color(0xFFABDBF2);
  static const Color eyeIcon = Color(0xFF53637A);

  // Gradients
  static LinearGradient get primaryGradient => LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

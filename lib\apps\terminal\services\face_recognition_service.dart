import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/secure_comm_models.dart';

class FaceRecognitionService {
  final SecureComm _secureComm;

  FaceRecognitionService(this._secureComm);

  /// Compress image to reduce size before sending
  Future<Uint8List> _compressImage(Uint8List imageData) async {
    try {
      // If image is already small enough, return as-is
      if (imageData.length < 500000) { // 500KB
        return imageData;
      }

      // For larger images, we could implement compression here
      // For now, just return the original data
      // TODO: Add image compression using image package

      if (kDebugMode) {
        print('⚠️ Large image detected: ${imageData.length} bytes');
        print('   Consider implementing image compression');
      }

      return imageData;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Image compression error: $e');
      }
      return imageData;
    }
  }

  /// Send face image for recognition
  Future<FaceRecognitionResult?> recognizeFace({
    required Uint8List imageData,
    required double confidenceScore,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (kDebugMode) {
        print('🔍 G<PERSON>i yêu cầu nhận diện khuôn mặt lên server...');
        print('   📷 Kích thước ảnh gốc: ${(imageData.length / 1024).toStringAsFixed(1)} KB');
        print('   📊 Độ tin cậy: ${(confidenceScore * 100).toStringAsFixed(1)}%');
      }

      // Compress image if needed
      final compressedImage = await _compressImage(imageData);

      if (kDebugMode) {
        print('   Compressed image size: ${compressedImage.length} bytes');
        print('   Compression ratio: ${((imageData.length - compressedImage.length) / imageData.length * 100).toStringAsFixed(1)}%');
      }

      // Convert image to base64
      final base64Image = base64Encode(compressedImage);

      if (kDebugMode) {
        print('   Base64 size: ${base64Image.length} characters');
      }

      final response = await _secureComm.sendMessage(
        type: 'face_recognition',
        payload: {
          'image_data': base64Image,
          'confidence_score': confidenceScore,
          'metadata': metadata ?? {},
        },
      );

      if (response.success && response.data != null) {
        final result = FaceRecognitionResult.fromJson(response.data!);
        
        if (kDebugMode) {
          print('✅ Nhận được phản hồi nhận diện khuôn mặt từ server');
          print('   🎯 Kết quả nhận diện: ${result.recognized ? "Thành công" : "Không nhận diện được"}');
          if (result.recognized && result.user != null) {
            print('   👤 Người dùng: ${result.user!.name}');
            print('   📊 Độ tin cậy: ${((result.confidence ?? 0) * 100).toStringAsFixed(1)}%');
          }
        }

        return result;
      } else {
        if (kDebugMode) {
          print('❌ Nhận diện khuôn mặt thất bại: ${response.data}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Lỗi nhận diện khuôn mặt: $e');
      }
      return null;
    }
  }
}

class FaceRecognitionResult {
  final bool success;
  final bool recognized;
  final RecognizedUser? user;
  final double? confidence;
  final String recognitionId;
  final String timestamp;
  final int? processingTime;
  final String? status;
  final String? message;
  final bool allowAccess;
  final String? reason;

  FaceRecognitionResult({
    required this.success,
    required this.recognized,
    this.user,
    this.confidence,
    required this.recognitionId,
    required this.timestamp,
    this.processingTime,
    this.status,
    this.message,
    required this.allowAccess,
    this.reason,
  });

  factory FaceRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionResult(
      success: json['success'] ?? false,
      recognized: json['recognized'] ?? false,
      user: json['user'] != null ? RecognizedUser.fromJson(json['user']) : null,
      confidence: json['confidence']?.toDouble(),
      recognitionId: json['recognitionId'] ?? json['recognition_id'] ?? '',
      timestamp: json['timestamp'] ?? '',
      processingTime: json['processingTime']?.toInt() ?? json['processing_time']?.toInt(),
      status: json['status'],
      message: json['message'],
      allowAccess: json['allowAccess'] ?? json['allow_access'] ?? false,
      reason: json['reason'],
    );
  }
}

class RecognizedUser {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final String? department;
  final String? position;
  final String? avatar;
  final String? employeeId;
  final String? accessLevel;
  final String? lastSeen;

  RecognizedUser({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.department,
    this.position,
    this.avatar,
    this.employeeId,
    this.accessLevel,
    this.lastSeen,
  });

  factory RecognizedUser.fromJson(Map<String, dynamic> json) {
    return RecognizedUser(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'],
      department: json['department'],
      position: json['position'],
      avatar: json['avatar'],
      employeeId: json['employeeId'] ?? json['employee_id'],
      accessLevel: json['accessLevel'] ?? json['access_level'],
      lastSeen: json['lastSeen'] ?? json['last_seen'],
    );
  }
}

/// Side effect function called when face is recognized
void recognizedSideEffect(FaceRecognitionResult result) {
  if (kDebugMode) {
    print('🎯 Kích hoạt hiệu ứng phụ nhận diện khuôn mặt');
    print('   🆔 ID nhận diện: ${result.recognitionId}');
    print('   ✅ Kết quả: ${result.recognized ? "Thành công" : "Thất bại"}');

    if (result.recognized && result.user != null) {
      print('   👤 Tên người dùng: ${result.user!.name}');
      print('   🏷️ Mã nhân viên: ${result.user!.employeeId}');
      print('   🏢 Phòng ban: ${result.user!.department}');
      print('   🔐 Cấp độ truy cập: ${result.user!.accessLevel}');
      print('   📊 Độ tin cậy: ${((result.confidence ?? 0) * 100).toStringAsFixed(1)}%');
      
      // TODO: Add your side effects here:
      // - Update UI with user info
      // - Log access attempt
      // - Trigger hardware actions (door unlock, etc.)
      // - Send notifications
      // - Update attendance system
      // - etc.
      
    } else {
      print('   Status: ${result.status}');
      print('   Message: ${result.message}');
      
      // TODO: Handle unrecognized face:
      // - Show access denied message
      // - Log security event
      // - Take additional photos
      // - Alert security
      // - etc.
    }
  }
}

import 'package:dartz/dartz.dart';
import '../../entities/auth/auth_result.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/errors/failures.dart';

class LoginUseCase {
  final AuthRepository repository;

  LoginUseCase(this.repository);

  Future<Either<Failure, AuthResult>> call(LoginParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to perform login
    return await repository.login(
      userName: params.userName,
      password: params.password,
    );
  }

  ValidationFailure? _validateParams(LoginParams params) {
    final errors = <String, List<String>>{};

    // Validate username
    if (params.userName.isEmpty) {
      errors['userName'] = ['Username is required'];
    } else if (params.userName.length < 3) {
      errors['userName'] = ['Username must be at least 3 characters'];
    }

    // Validate password
    if (params.password.isEmpty) {
      errors['password'] = ['Password is required'];
    } else if (params.password.length < 6) {
      errors['password'] = ['Password must be at least 6 characters'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Invalid login parameters',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class LoginParams {
  final String userName;
  final String password;

  const LoginParams({
    required this.userName,
    required this.password,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginParams &&
        other.userName == userName &&
        other.password == password;
  }

  @override
  int get hashCode => userName.hashCode ^ password.hashCode;

  @override
  String toString() => 'LoginParams(userName: $userName, password: [HIDDEN])';
}

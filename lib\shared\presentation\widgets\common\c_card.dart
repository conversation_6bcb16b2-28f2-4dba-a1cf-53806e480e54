import 'package:flutter/material.dart';

/// Custom card widget with consistent styling
/// 
/// Provides consistent card styling across mobile and terminal apps
class CC<PERSON> extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final Color? shadowColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final Color? selectedColor;
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final List<Widget>? actions;

  const CCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.shadowColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.selectedColor,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.actions,
  });

  /// Create a simple card with just content
  const CCard.simple({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.shadowColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.selectedColor,
  }) : title = null,
       subtitle = null,
       leading = null,
       trailing = null,
       actions = null;

  /// Create a card with header (title, subtitle, leading, trailing)
  const CCard.withHeader({
    super.key,
    required this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.actions,
    this.padding,
    this.margin,
    this.color,
    this.shadowColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.selectedColor,
  });

  /// Create a selectable card
  const CCard.selectable({
    super.key,
    required this.child,
    required this.isSelected,
    this.selectedColor,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.color,
    this.shadowColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardTheme = theme.cardTheme;
    
    final effectiveColor = isSelected 
        ? (selectedColor ?? theme.colorScheme.primaryContainer)
        : (color ?? cardTheme.color ?? theme.colorScheme.surface);
    
    final effectiveElevation = elevation ?? cardTheme.elevation ?? 2.0;
    final effectiveBorderRadius = borderRadius ?? 
        (cardTheme.shape as RoundedRectangleBorder?)?.borderRadius ?? 
        BorderRadius.circular(12.0);
    
    Widget cardContent = child;
    
    // Add header if provided
    if (title != null || subtitle != null || leading != null || trailing != null || actions != null) {
      cardContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          if (title != null || subtitle != null) const SizedBox(height: 16),
          child,
        ],
      );
    }
    
    // Add padding
    cardContent = Padding(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: cardContent,
    );
    
    Widget card = Container(
      margin: margin,
      decoration: BoxDecoration(
        color: effectiveColor,
        borderRadius: effectiveBorderRadius,
        border: border ?? (isSelected ? Border.all(
          color: theme.colorScheme.primary,
          width: 2.0,
        ) : null),
        boxShadow: effectiveElevation > 0 ? [
          BoxShadow(
            color: shadowColor ?? theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: effectiveElevation * 2,
            offset: Offset(0, effectiveElevation),
          ),
        ] : null,
      ),
      child: cardContent,
    );
    
    // Add tap functionality
    if (onTap != null || onLongPress != null) {
      card = InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: effectiveBorderRadius as BorderRadius?,
        child: card,
      );
    }
    
    return card;
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        if (leading != null) ...[
          leading!,
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(
                  title!,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
        ),
        if (trailing != null) ...[
          const SizedBox(width: 12),
          trailing!,
        ],
        if (actions != null && actions!.isNotEmpty) ...[
          const SizedBox(width: 8),
          ...actions!.map((action) => Padding(
            padding: const EdgeInsets.only(left: 4),
            child: action,
          )),
        ],
      ],
    );
  }
}

/// Card list item for consistent list styling
class CCardListItem extends StatelessWidget {
  final Widget? leading;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const CCardListItem({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return CCard.selectable(
      isSelected: isSelected,
      onTap: onTap,
      onLongPress: onLongPress,
      padding: padding ?? const EdgeInsets.all(12.0),
      margin: margin ?? const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: 12),
            trailing!,
          ],
        ],
      ),
    );
  }
}

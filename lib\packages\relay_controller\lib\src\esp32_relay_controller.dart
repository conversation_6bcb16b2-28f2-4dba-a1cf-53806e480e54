import 'dart:convert';
import 'dart:typed_data';
import 'package:usb_serial/usb_serial.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';
import 'usb_ttl_relay_controller.dart';

/// ESP32-specific relay controller that supports advanced relay commands.
/// 
/// This controller supports ESP32 relay commands like:
/// - R0:1 (Turn relay 0 ON)
/// - R0:0 (Turn relay 0 OFF)  
/// - R1:TOGGLE (Toggle relay 1)
/// - R2:500 (Turn relay 2 ON for 500ms then auto OFF)
/// - ALL:1 (Turn all relays ON)
/// - ALL:0 (Turn all relays OFF)
/// 
/// Example usage:
/// ```dart
/// final controller = Esp32RelayController(
///   deviceId: 'esp32-relay-001',
///   relayCount: 4,
/// );
/// 
/// await controller.connect(usbDevice);
/// await controller.controlRelay(0, RelayAction.on);
/// await controller.controlRelay(1, RelayAction.toggle);
/// await controller.controlRelayTimed(2, 500); // ON for 500ms
/// await controller.controlAllRelays(RelayAction.off);
/// ```
class Esp32RelayController extends RelayController {
  /// Number of relays available on the ESP32 device
  final int relayCount;

  /// Baud rate for serial communication
  final int baudRate;

  /// Data bits for serial communication
  final int dataBits;

  /// Stop bits for serial communication
  final int stopBits;

  /// Parity for serial communication
  final int parity;

  /// Connection timeout in seconds
  final int timeoutSeconds;

  /// Command terminator (default: empty for ESP32)
  final String commandTerminator;

  UsbPort? _port;
  UsbDevice? _connectedDevice;
  bool _isConnected = false;

  /// Creates a new [Esp32RelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceName] is the device name/description.
  /// [relayCount] is the number of relays (default: 4).
  /// [baudRate] is the baud rate for serial communication (default: 115200 for ESP32).
  /// [commandTerminator] is added to the end of each command (default: empty).
  Esp32RelayController({
    required super.deviceId,
    super.deviceName = 'ESP32 Relay Controller',
    this.relayCount = 4,
    this.baudRate = 115200,
    this.dataBits = 8,
    this.stopBits = 1,
    this.parity = 0,
    this.timeoutSeconds = 10,
    this.commandTerminator = '',
  });

  /// Connects to a USB device.
  ///
  /// [device] is the USB device to connect to.
  /// This method must be called before using any relay control methods.
  ///
  /// Throws [UsbRelayException] if connection fails.
  Future<void> connect(UsbDevice device) async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      _port = await device.create();
      if (_port == null) {
        throw const UsbRelayException('Failed to create USB port');
      }

      bool openResult = await _port!.open();
      if (!openResult) {
        throw const UsbRelayException('Failed to open USB port');
      }

      // Configure serial parameters for ESP32
      await _port!.setDTR(true);
      await _port!.setRTS(true);
      await _port!.setPortParameters(
        baudRate,
        dataBits,
        stopBits,
        parity,
      );

      _connectedDevice = device;
      _isConnected = true;
    } catch (e) {
      throw UsbRelayException('Failed to connect to ESP32 device', e);
    }
  }

  /// Controls a specific relay.
  ///
  /// [relayIndex] is the relay number (0 to relayCount-1).
  /// [action] is the action to perform.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlRelay(int relayIndex, RelayAction action) async {
    _validateConnection();
    _validateRelayIndex(relayIndex);

    String command;
    switch (action) {
      case RelayAction.on:
        command = 'R$relayIndex:1';
        break;
      case RelayAction.off:
        command = 'R$relayIndex:0';
        break;
      case RelayAction.toggle:
        command = 'R$relayIndex:TOGGLE';
        break;
    }

    await _sendCommand(command);
  }

  /// Controls a relay with timed operation.
  ///
  /// [relayIndex] is the relay number (0 to relayCount-1).
  /// [durationMs] is the duration in milliseconds to keep relay ON.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlRelayTimed(int relayIndex, int durationMs) async {
    _validateConnection();
    _validateRelayIndex(relayIndex);

    if (durationMs <= 0) {
      throw const UsbRelayException('Duration must be positive');
    }

    final command = 'R$relayIndex:$durationMs';
    await _sendCommand(command);
  }

  /// Controls all relays at once.
  ///
  /// [action] is the action to perform on all relays.
  /// Note: TOGGLE action is not supported for all relays.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlAllRelays(RelayAction action) async {
    _validateConnection();

    String command;
    switch (action) {
      case RelayAction.on:
        command = 'ALL:1';
        break;
      case RelayAction.off:
        command = 'ALL:0';
        break;
      case RelayAction.toggle:
        throw const UsbRelayException('TOGGLE action not supported for all relays');
    }

    await _sendCommand(command);
  }

  /// Sends a raw command to the ESP32.
  ///
  /// [command] is the raw command string to send.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> sendRawCommand(String command) async {
    _validateConnection();
    await _sendCommand(command);
  }

  /// Gets relay configuration info.
  RelayConfiguration get configuration => RelayConfiguration(
    relayCount: relayCount,
    deviceId: deviceId,
    deviceName: deviceName,
    isConnected: _isConnected,
    deviceProfile: DeviceProfile.esp32(),
  );

  // Legacy interface compatibility
  @override
  Future<void> triggerOn() async {
    // Default to relay 0 for backward compatibility
    await controlRelay(0, RelayAction.on);
  }

  @override
  Future<void> triggerOff() async {
    // Default to relay 0 for backward compatibility
    await controlRelay(0, RelayAction.off);
  }

  /// Internal method to send commands to ESP32.
  Future<void> _sendCommand(String command) async {
    try {
      final fullCommand = command + commandTerminator;
      final data = utf8.encode(fullCommand);
      await _port!.write(Uint8List.fromList(data));
    } catch (e) {
      throw UsbRelayException('Failed to send command: $command', e);
    }
  }

  /// Validates that the controller is connected.
  void _validateConnection() {
    if (!_isConnected || _port == null) {
      throw const UsbRelayException('ESP32 controller is not connected');
    }
  }

  /// Validates relay index.
  void _validateRelayIndex(int relayIndex) {
    if (relayIndex < 0 || relayIndex >= relayCount) {
      throw UsbRelayException('Invalid relay index: $relayIndex. Must be 0-${relayCount - 1}');
    }
  }

  /// Checks if the controller is connected.
  bool get isConnected => _isConnected;

  /// Gets the connected device info.
  UsbDevice? get connectedDevice => _connectedDevice;

  /// Disconnects from the device and cleans up resources.
  Future<void> disconnect() async {
    if (_port != null) {
      await _port!.close();
      _port = null;
    }
    _connectedDevice = null;
    _isConnected = false;
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets available USB devices.
  static Future<List<UsbDevice>> getAvailableDevices() async {
    try {
      return await UsbSerial.listDevices();
    } catch (e) {
      throw UsbRelayException('Failed to get available USB devices', e);
    }
  }

  /// Checks if USB host mode is supported on the device.
  static Future<bool> isUsbHostSupported() async {
    try {
      final devices = await UsbSerial.listDevices();
      return devices.isNotEmpty || true; // Assume supported if we can list devices
    } catch (e) {
      return false;
    }
  }
}



/// Auto-connecting ESP32 relay controller that automatically connects to the first available device.
/// 
/// Example usage:
/// ```dart
/// final controller = AutoConnectEsp32RelayController(
///   deviceId: 'auto-esp32-001',
///   relayCount: 4,
/// );
/// 
/// await controller.initialize();
/// await controller.controlRelay(0, RelayAction.on);
/// await controller.dispose();
/// ```
class AutoConnectEsp32RelayController extends Esp32RelayController {
  /// Creates an auto-connecting ESP32 relay controller.
  AutoConnectEsp32RelayController({
    required super.deviceId,
    super.deviceName = 'Auto ESP32 Relay',
    super.relayCount = 4,
    super.baudRate = 115200,
    super.timeoutSeconds = 10,
  });

  /// Initializes the controller by automatically connecting to the first available device.
  ///
  /// Throws [UsbRelayException] if no devices are available or connection fails.
  Future<void> initialize() async {
    final devices = await Esp32RelayController.getAvailableDevices();
    if (devices.isEmpty) {
      throw const UsbRelayException('No USB devices available for ESP32 relay controller');
    }

    await connect(devices.first);
  }
} 
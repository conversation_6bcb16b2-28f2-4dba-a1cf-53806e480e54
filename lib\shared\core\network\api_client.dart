import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../errors/exceptions.dart';
import '../../services/api_endpoints.dart';
import '../../services/cookie_service.dart';

class ApiClient {
  final http.Client _client;
  String? _accessToken;
  String _baseUrl;

  ApiClient({
    http.Client? client,
    String? baseUrl,
  }) : _client = client ?? http.Client(),
       _baseUrl = baseUrl ?? ApiEndpoints.devBaseUrl;

  void setAccessToken(String? token) {
    _accessToken = token;
  }

  String? get accessToken => _accessToken;

  /// Clear all authentication data
  Future<void> clearAuthenticationData() async {
    _accessToken = null;

    // Clear cookies
    try {
      final uri = Uri.parse(_baseUrl);
      final domain = uri.host;
      await CookieService().clearCookiesForDomain(domain);

      if (kDebugMode) {
        print('🧹 Cleared authentication data and cookies for domain: $domain');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Failed to clear cookies: $e');
      }
    }
  }

  void setBaseUrl(String baseUrl) {
    _baseUrl = baseUrl;
  }

  String get baseUrl => _baseUrl;

  Future<Map<String, String>> get _defaultHeaders async {
    final headers = <String, String>{
      ApiConstants.contentTypeHeader: ApiConstants.applicationJson,
      ApiConstants.acceptHeader: ApiConstants.applicationJson,
      if (_accessToken != null)
        ApiConstants.authorizationHeader: '${ApiConstants.bearerPrefix}$_accessToken',
    };

    // Add cookies if available
    await _addCookieHeader(headers);

    return headers;
  }

  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final requestHeaders = await _defaultHeaders;
      final finalHeaders = <String, String>{...requestHeaders, ...?headers};

      if (kDebugMode) {
        _logRequest('GET', uri.toString(), finalHeaders);
      }

      final response = await _client
          .get(
            uri,
            headers: finalHeaders,
          )
          .timeout(Duration(milliseconds: ApiConstants.receiveTimeout));

      return _handleResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on HttpException {
      throw const NetworkException('HTTP error occurred');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = _buildUri(endpoint, null);
      final requestHeaders = await _defaultHeaders;
      final finalHeaders = <String, String>{...requestHeaders, ...?headers};

      if (kDebugMode) {
        _logRequest('POST', uri.toString(), finalHeaders, body);
      }

      final response = await _client
          .post(
            uri,
            headers: finalHeaders,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(Duration(milliseconds: ApiConstants.sendTimeout));

      return _handleResponse(response);
    } on SocketException catch (e) {
      throw NetworkException('Connection failed: ${e.message}. Please check if the server is running at ${Uri.parse(endpoint).host}:${Uri.parse(endpoint).port}');
    } on HttpException catch (e) {
      throw NetworkException('HTTP error: ${e.message}');
    } on TimeoutException {
      throw const NetworkException('Request timeout. Please check your connection and try again.');
    } catch (e) {
      if (e.toString().contains('Connection refused')) {
        throw const NetworkException('Cannot connect to server. Please ensure the backend is running at http://localhost:3000');
      }
      throw NetworkException('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = _buildUri(endpoint, null);
      final requestHeaders = await _defaultHeaders;
      final finalHeaders = <String, String>{...requestHeaders, ...?headers};

      if (kDebugMode) {
        _logRequest('PUT', uri.toString(), finalHeaders, body);
      }

      final response = await _client
          .put(
            uri,
            headers: finalHeaders,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(Duration(milliseconds: ApiConstants.sendTimeout));

      return _handleResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on HttpException {
      throw const NetworkException('HTTP error occurred');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    try {
      final uri = _buildUri(endpoint, null);
      final requestHeaders = await _defaultHeaders;
      final finalHeaders = <String, String>{...requestHeaders, ...?headers};

      if (kDebugMode) {
        _logRequest('DELETE', uri.toString(), finalHeaders);
      }

      final response = await _client
          .delete(
            uri,
            headers: finalHeaders,
          )
          .timeout(Duration(milliseconds: ApiConstants.receiveTimeout));

      return _handleResponse(response);
    } on SocketException {
      throw const NetworkException('No internet connection');
    } on HttpException {
      throw const NetworkException('HTTP error occurred');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  Uri _buildUri(String endpoint, Map<String, dynamic>? queryParameters) {
    // Combine base URL with endpoint
    final fullUrl = endpoint.startsWith('http')
        ? endpoint
        : '$_baseUrl${ApiEndpoints.apiVersion}$endpoint';

    final uri = Uri.parse(fullUrl);
    if (queryParameters != null && queryParameters.isNotEmpty) {
      return uri.replace(queryParameters: queryParameters.map(
        (key, value) => MapEntry(key, value.toString()),
      ));
    }
    return uri;
  }

  Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;

    // Check for Set-Cookie headers and process them
    _processSetCookieHeaders(response);

    try {
      final responseBody = jsonDecode(response.body) as Map<String, dynamic>;

      if (statusCode >= 200 && statusCode < 300) {
        return responseBody;
      } else {
        _throwAppropriateException(statusCode, responseBody);
        // This line should never be reached due to the exception above
        throw ServerException('Unexpected error', statusCode: statusCode);
      }
    } catch (e) {
      if (e is AppException) rethrow;
      throw ServerException(
        'Failed to parse response: $e',
        statusCode: statusCode,
      );
    }
  }

  /// Process Set-Cookie headers from HTTP response
  void _processSetCookieHeaders(http.Response response) {
    try {
      final setCookieHeaders = response.headers['set-cookie'];
      if (setCookieHeaders != null && setCookieHeaders.isNotEmpty) {
        if (kDebugMode) {
          print('🍪 Found Set-Cookie header: $setCookieHeaders');
        }

        // Parse and store cookies while preserving original values
        _parseAndStoreCookies(setCookieHeaders);
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error processing Set-Cookie headers: $e');
      }
    }
  }

  /// Parse Set-Cookie header and store cookies with preserved values
  void _parseAndStoreCookies(String setCookieHeader) {
    try {
      // Split multiple cookies if they exist
      final cookieStrings = setCookieHeader.split(',');

      for (final cookieString in cookieStrings) {
        final parts = cookieString.trim().split(';');
        if (parts.isNotEmpty) {
          final nameValue = parts[0].trim();
          final equalIndex = nameValue.indexOf('=');

          if (equalIndex > 0) {
            final name = nameValue.substring(0, equalIndex).trim();
            final value = nameValue.substring(equalIndex + 1).trim();

            if (kDebugMode) {
              print('🍪 Parsed cookie: $name=$value (length: ${value.length})');
            }

            // Store the cookie with original value preserved
            if (name == ApiConstants.refreshTokenCookieName) {
              final uri = Uri.parse(_baseUrl);
              final domain = uri.host;

              // Use our custom storage to preserve the original value
              CookieService().storeRawCookieValue(domain, name, value);

              if (kDebugMode) {
                print('🍪 Stored ${ApiConstants.refreshTokenCookieName} in raw storage: ${value.length} chars');
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error parsing Set-Cookie header: $e');
      }
    }
  }

  void _throwAppropriateException(int statusCode, Map<String, dynamic> responseBody) {
    final message = responseBody['message'] ?? 'Unknown error occurred';
    final code = responseBody['code']?.toString();

    switch (statusCode) {
      case ApiConstants.badRequestCode:
        throw ValidationException(message, code: code);
      case ApiConstants.unauthorizedCode:
        throw AuthException(message, code: code);
      case ApiConstants.forbiddenCode:
        throw AuthException(message, code: code);
      case ApiConstants.notFoundCode:
        throw ServerException(message, code: code, statusCode: statusCode);
      case ApiConstants.internalServerErrorCode:
        throw ServerException(message, code: code, statusCode: statusCode);
      default:
        throw ServerException(message, code: code, statusCode: statusCode);
    }
  }

  /// Add Cookie header from CookieService
  Future<void> _addCookieHeader(Map<String, String> headers) async {
    try {
      // Extract domain from base URL
      final uri = Uri.parse(_baseUrl);
      final domain = uri.host;

      // Get cookies for domain
      final cookies = await CookieService().getCookiesForDomain(domain);

      if (cookies.isNotEmpty) {
        // Build cookie string - preserve original encoding
        final cookieString = cookies.map((cookie) {
          // Check if the cookie value needs to be URL-encoded
          // If it contains special characters that were originally encoded, preserve them
          final value = cookie.value;
          if (kDebugMode) {
            print('🍪 Original cookie value for ${cookie.name}: $value');
          }
          return '${cookie.name}=$value';
        }).join('; ');

        headers['Cookie'] = cookieString;

        if (kDebugMode) {
          print('🍪 Added Cookie header: $cookieString');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Failed to add Cookie header: $e');
      }
      // Don't throw error - cookie header is not critical
    }
  }

  /// Log HTTP request details with optimized formatting
  void _logRequest(String method, String url, Map<String, String> headers, [dynamic body]) {
    if (!kDebugMode) return;

    print('🔄 $method URL: $url');

    if (body != null) {
      print('🔄 $method Body: ${jsonEncode(body)}');
    }

    // Log headers with truncated Authorization token to avoid log truncation
    final logHeaders = Map<String, String>.from(headers);
    if (logHeaders.containsKey('Authorization')) {
      final authHeader = logHeaders['Authorization']!;
      if (authHeader.length > 50) {
        logHeaders['Authorization'] = '${authHeader.substring(0, 50)}...';
      }
    }
    print('🔄 $method Headers: $logHeaders');

    // Specifically log Cookie header if present
    if (headers.containsKey('Cookie')) {
      print('🍪 Cookie Header: ${headers['Cookie']}');
    }
  }

  void dispose() {
    _client.close();
  }
}

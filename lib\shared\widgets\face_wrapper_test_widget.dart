import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/face_capture_provider.dart';
import '../providers/face_detection_provider.dart';
import '../providers/face_providers_lifecycle_manager.dart';
import 'face_providers_wrapper.dart';

/// Test widget để verify FaceProvidersWrapper functionality
/// 
/// Test scenarios:
/// 1. Lazy loading - providers chỉ được tạo khi cần
/// 2. Auto-initialization - providers được initialize tự động
/// 3. Provider access - có thể access providers từ child widgets
/// 4. Cleanup - providers được dispose khi widget unmount
/// 5. Multiple wrappers - nhiều wrappers có thể share providers
class FaceWrapperTestWidget extends StatefulWidget {
  const FaceWrapperTestWidget({super.key});

  @override
  State<FaceWrapperTestWidget> createState() => _FaceWrapperTestWidgetState();
}

class _FaceWrapperTestWidgetState extends State<FaceWrapperTestWidget> {
  bool _showWrapper1 = false;
  bool _showWrapper2 = false;
  String _statusMessage = '';
  List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _updateStatus();
  }

  void _addLog(String message) {
    if (mounted) {
      setState(() {
        _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
        if (_logs.length > 15) {
          _logs.removeAt(0);
        }
      });
    }
  }

  void _updateStatus() {
    final lifecycleManager = FaceProvidersLifecycleManager.instance;
    _statusMessage = 'Lifecycle State: ${lifecycleManager.state.name}\n'
        'Reference Count: ${lifecycleManager.referenceCount}\n'
        'Is Ready: ${lifecycleManager.isReady}\n'
        'Wrapper 1: ${_showWrapper1 ? "Shown" : "Hidden"}\n'
        'Wrapper 2: ${_showWrapper2 ? "Shown" : "Hidden"}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Wrapper Test'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Wrapper Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _statusMessage,
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Control buttons
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _showWrapper1 = !_showWrapper1;
                            });
                            _addLog('Wrapper 1 ${_showWrapper1 ? "shown" : "hidden"}');
                            _updateStatus();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _showWrapper1 ? Colors.red : Colors.green,
                          ),
                          child: Text(_showWrapper1 ? 'Hide Wrapper 1' : 'Show Wrapper 1'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _showWrapper2 = !_showWrapper2;
                            });
                            _addLog('Wrapper 2 ${_showWrapper2 ? "shown" : "hidden"}');
                            _updateStatus();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _showWrapper2 ? Colors.red : Colors.green,
                          ),
                          child: Text(_showWrapper2 ? 'Hide Wrapper 2' : 'Show Wrapper 2'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showWrapper1 = false;
                        _showWrapper2 = false;
                      });
                      _addLog('All wrappers hidden');
                      _updateStatus();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                    ),
                    child: const Text('Hide All Wrappers'),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Wrappers section
              Expanded(
                child: Column(
                  children: [
                    // Wrapper 1
                    if (_showWrapper1)
                      Expanded(
                        child: Card(
                          color: Colors.blue.shade50,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: FaceProvidersWrapper(
                              autoInitialize: true,
                              onReady: () => _addLog('Wrapper 1: Providers ready'),
                              onDisposed: () => _addLog('Wrapper 1: Providers disposed'),
                              onError: (error) => _addLog('Wrapper 1: Error - $error'),
                              child: _TestChild(
                                title: 'Wrapper 1 Child',
                                color: Colors.blue,
                                onLog: _addLog,
                                onStatusUpdate: _updateStatus,
                              ),
                            ),
                          ),
                        ),
                      ),
                    
                    const SizedBox(height: 8),
                    
                    // Wrapper 2
                    if (_showWrapper2)
                      Expanded(
                        child: Card(
                          color: Colors.purple.shade50,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: FaceProvidersWrapper(
                              autoInitialize: true,
                              onReady: () => _addLog('Wrapper 2: Providers ready'),
                              onDisposed: () => _addLog('Wrapper 2: Providers disposed'),
                              onError: (error) => _addLog('Wrapper 2: Error - $error'),
                              child: _TestChild(
                                title: 'Wrapper 2 Child',
                                color: Colors.purple,
                                onLog: _addLog,
                                onStatusUpdate: _updateStatus,
                              ),
                            ),
                          ),
                        ),
                      ),
                    
                    // Logs section
                    if (!_showWrapper1 && !_showWrapper2)
                      Expanded(
                        child: Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'Activity Logs',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 80,
                                      child: TextButton(
                                        onPressed: () {
                                          setState(() {
                                            _logs.clear();
                                          });
                                        },
                                        child: const Text('Clear'),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Expanded(
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: SingleChildScrollView(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: _logs.map((log) => Padding(
                                          padding: const EdgeInsets.only(bottom: 2),
                                          child: Text(
                                            log,
                                            style: const TextStyle(
                                              fontFamily: 'monospace',
                                              fontSize: 12,
                                            ),
                                          ),
                                        )).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Test child widget that can access face providers
class _TestChild extends StatelessWidget {
  final String title;
  final Color color;
  final Function(String) onLog;
  final VoidCallback onStatusUpdate;

  const _TestChild({
    required this.title,
    required this.color,
    required this.onLog,
    required this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 8),
              Consumer2<FaceCaptureProvider, FaceDetectionProvider>(
                builder: (context, cameraProvider, faceProvider, child) {
                  return Column(
                    children: [
                      Text(
                        'Camera: ${cameraProvider.status.name}',
                        style: TextStyle(color: color),
                      ),
                      Text(
                        'Face Detection: ${faceProvider.isDetecting ? "Active" : "Inactive"}',
                        style: TextStyle(color: color),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () {
                          onLog('$title: Providers accessed successfully');
                          onStatusUpdate();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: color,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Test Access'),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

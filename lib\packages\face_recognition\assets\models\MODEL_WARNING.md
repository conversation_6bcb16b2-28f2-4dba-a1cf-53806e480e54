# ⚠️ TEMPORARY MODEL REPLACEMENT WARNING

## Current Status
The models in this directory are **TEMPORARY REPLACEMENTS**:

### Original Expected Models:
- `ultraface_320.tflite` - UltraFace 320x240 face detection model
- `mobilefacenet.tflite` - MobileFaceNet face recognition model  
- `mediapipe_face.tflite` - MediaPipe face detection model

### Current Temporary Models:
- `ultraface_320.tflite` - **MediaPipe BlazeFace** (224.4 KB)
- `mobilefacenet.tflite` - **MediaPipe Face Landmarker** (3.6 MB)
- `mediapipe_face.tflite` - **MediaPipe BlazeFace** (224.4 KB)

## Why Temporary?
1. **UltraFace models**: Original repository URLs are not accessible
2. **MobileFaceNet models**: Need proper conversion from original sources
3. **Compatibility**: Current models may have different input/output formats

## Impact on Application:
- ✅ **Face detection**: Should work (BlazeFace is similar to UltraFace)
- ⚠️ **Face recognition**: May need code adjustments (different model architecture)
- ⚠️ **Preprocessing**: Input size/format may differ from expected

## Next Steps:
1. **Find original UltraFace TFLite models** or convert from PyTorch
2. **Get proper MobileFaceNet TFLite models** 
3. **Test compatibility** with existing Flutter code
4. **Update inference code** if needed for new model formats

## How to Replace:
1. Download/convert the correct models
2. Replace files in this directory
3. Update model loading code if input/output formats differ
4. Test thoroughly with your Flutter app

---
*Generated by: scripts/create_model_warning.py*
*Date: $(date)*

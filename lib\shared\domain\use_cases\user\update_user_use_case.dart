import 'package:dartz/dartz.dart';
import '../../entities/user/user.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';

class UpdateUserUseCase {
  final UserRepository repository;

  UpdateUserUseCase(this.repository);

  Future<Either<Failure, User>> call(UpdateUserParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to update user
    return await repository.updateUser(
      userId: params.userId,
      name: params.name,
      email: params.email,
      phone: params.phone,
      dob: params.dob,
      gender: params.gender,
      unitId: params.unitId,
      memberRoleId: params.memberRoleId,
    );
  }

  ValidationFailure? _validateParams(UpdateUserParams params) {
    final errors = <String, List<String>>{};

    // Validate required fields
    if (params.userId.trim().isEmpty) {
      errors['userId'] = ['User ID is required'];
    }

    // Validate email format if provided
    if (params.email != null && params.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(params.email!)) {
        errors['email'] = ['Invalid email format'];
      }
    }

    // Validate phone format if provided
    if (params.phone != null && params.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[0-9]{10,15}$');
      if (!phoneRegex.hasMatch(params.phone!.replaceAll(RegExp(r'[\s-]'), ''))) {
        errors['phone'] = ['Invalid phone format'];
      }
    }

    // Validate date format if provided
    if (params.dob != null && params.dob!.isNotEmpty) {
      try {
        DateTime.parse(params.dob!);
      } catch (e) {
        errors['dob'] = ['Invalid date format (YYYY-MM-DD)'];
      }
    }

    // Validate gender if provided
    if (params.gender != null && params.gender!.isNotEmpty) {
      if (!['male', 'female', 'other'].contains(params.gender!.toLowerCase())) {
        errors['gender'] = ['Gender must be male, female, or other'];
      }
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Validation failed',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class UpdateUserParams {
  final String userId;
  final String? name;
  final String? email;
  final String? phone;
  final String? dob;
  final String? gender;
  final String? unitId;
  final String? memberRoleId;

  UpdateUserParams({
    required this.userId,
    this.name,
    this.email,
    this.phone,
    this.dob,
    this.gender,
    this.unitId,
    this.memberRoleId,
  });
}

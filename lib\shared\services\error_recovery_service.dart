import 'dart:async';
import 'package:flutter/foundation.dart';
import '../providers/face_capture_provider.dart';
import '../providers/face_detection_provider.dart';
import '../models/camera_config.dart';

/// Service for handling error recovery in face detection and camera operations
class ErrorRecoveryService {
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  /// Recover camera provider from errors
  static Future<bool> recoverCameraProvider(
    FaceCaptureProvider cameraProvider, {
    CameraConfig? fallbackConfig,
    int maxAttempts = _maxRetryAttempts,
  }) async {
    if (kDebugMode) {
      print('Starting camera provider recovery...');
    }

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        if (kDebugMode) {
          print('Camera recovery attempt $attempt/$maxAttempts');
        }

        // Dispose current camera
        await cameraProvider.disposeCamera();
        
        // Wait before retry
        if (attempt > 1) {
          await Future.delayed(_retryDelay);
        }

        // Try to reinitialize with fallback config if provided
        final config = fallbackConfig ?? cameraProvider.currentConfig;
        await cameraProvider.initializeCamera(config);

        if (cameraProvider.isCameraReady) {
          if (kDebugMode) {
            print('Camera recovery successful on attempt $attempt');
          }
          return true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Camera recovery attempt $attempt failed: $e');
        }
        
        if (attempt == maxAttempts) {
          if (kDebugMode) {
            print('Camera recovery failed after $maxAttempts attempts');
          }
          return false;
        }
      }
    }

    return false;
  }

  /// Recover face detection provider from errors
  static Future<bool> recoverFaceDetectionProvider(
    FaceDetectionProvider faceProvider, {
    FaceDetectionConfig? fallbackConfig,
    int maxAttempts = _maxRetryAttempts,
  }) async {
    if (kDebugMode) {
      print('Starting face detection provider recovery...');
    }

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        if (kDebugMode) {
          print('Face detection recovery attempt $attempt/$maxAttempts');
        }

        // Wait before retry
        if (attempt > 1) {
          await Future.delayed(_retryDelay);
        }

        // Attempt recovery
        await faceProvider.attemptRecovery();

        // Check if recovery was successful
        if (!faceProvider.needsRecovery) {
          if (kDebugMode) {
            print('Face detection recovery successful on attempt $attempt');
          }
          return true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('Face detection recovery attempt $attempt failed: $e');
        }
        
        if (attempt == maxAttempts) {
          if (kDebugMode) {
            print('Face detection recovery failed after $maxAttempts attempts');
          }
          return false;
        }
      }
    }

    return false;
  }

  /// Comprehensive recovery for both providers
  static Future<RecoveryResult> recoverBothProviders(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider, {
    CameraConfig? fallbackCameraConfig,
    FaceDetectionConfig? fallbackFaceConfig,
  }) async {
    if (kDebugMode) {
      print('Starting comprehensive provider recovery...');
    }

    final result = RecoveryResult();
    
    try {
      // Recover face detection first (less disruptive)
      if (faceProvider.needsRecovery) {
        result.faceDetectionRecovered = await recoverFaceDetectionProvider(
          faceProvider,
          fallbackConfig: fallbackFaceConfig,
        );
      } else {
        result.faceDetectionRecovered = true;
      }

      // Recover camera if needed
      if (!cameraProvider.isCameraReady || 
          cameraProvider.status == CameraStatus.error) {
        result.cameraRecovered = await recoverCameraProvider(
          cameraProvider,
          fallbackConfig: fallbackCameraConfig,
        );
      } else {
        result.cameraRecovered = true;
      }

      // Re-establish connection between providers if both recovered
      if (result.cameraRecovered && result.faceDetectionRecovered) {
        await _reestablishProviderConnection(cameraProvider, faceProvider);
        result.connectionReestablished = true;
      }

      result.success = result.cameraRecovered && result.faceDetectionRecovered;
      
      if (kDebugMode) {
        print('Comprehensive recovery result: ${result.success ? 'SUCCESS' : 'FAILED'}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Comprehensive recovery failed: $e');
      }
      result.error = e.toString();
    }

    return result;
  }

  /// Re-establish connection between camera and face detection providers
  static Future<void> _reestablishProviderConnection(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider,
  ) async {
    try {
      // Set up image stream callback
      cameraProvider.setImageStreamCallback((image, camera) {
        faceProvider.detectFacesFromImage(image, camera);
      });

      // Enable image stream if camera is ready
      if (cameraProvider.isCameraReady && !cameraProvider.isStreamingEnabled) {
        await cameraProvider.toggleImageStream(true);
      }

      if (kDebugMode) {
        print('Provider connection reestablished');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to reestablish provider connection: $e');
      }
      rethrow;
    }
  }

  /// Monitor providers and auto-recover if needed
  static StreamSubscription<void> startAutoRecovery(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider, {
    Duration checkInterval = const Duration(seconds: 10),
    CameraConfig? fallbackCameraConfig,
    FaceDetectionConfig? fallbackFaceConfig,
  }) {
    return Stream.periodic(checkInterval).listen((_) async {
      try {
        // Check if recovery is needed
        final needsCameraRecovery = !cameraProvider.isCameraReady || 
                                   cameraProvider.status == CameraStatus.error;
        final needsFaceRecovery = faceProvider.needsRecovery;

        if (needsCameraRecovery || needsFaceRecovery) {
          if (kDebugMode) {
            print('Auto-recovery triggered - Camera: $needsCameraRecovery, Face: $needsFaceRecovery');
          }

          await recoverBothProviders(
            cameraProvider,
            faceProvider,
            fallbackCameraConfig: fallbackCameraConfig,
            fallbackFaceConfig: fallbackFaceConfig,
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('Auto-recovery error: $e');
        }
      }
    });
  }

  /// Get recovery recommendations based on current state
  static RecoveryRecommendation getRecoveryRecommendation(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider,
  ) {
    final recommendation = RecoveryRecommendation();

    // Analyze camera provider
    if (!cameraProvider.isCameraReady) {
      recommendation.needsCameraRecovery = true;
      recommendation.cameraIssues.add('Camera not ready');
    }
    
    if (cameraProvider.status == CameraStatus.error) {
      recommendation.needsCameraRecovery = true;
      recommendation.cameraIssues.add('Camera in error state');
    }

    if (cameraProvider.status == CameraStatus.permissionDenied) {
      recommendation.needsCameraRecovery = true;
      recommendation.cameraIssues.add('Camera permission denied');
    }

    // Analyze face detection provider
    if (faceProvider.needsRecovery) {
      recommendation.needsFaceRecovery = true;
      recommendation.faceIssues.add('Too many consecutive errors');
    }

    if (faceProvider.consecutiveErrors > 0) {
      recommendation.faceIssues.add('${faceProvider.consecutiveErrors} consecutive errors');
    }

    // Generate recommendations
    if (recommendation.needsCameraRecovery) {
      recommendation.recommendations.add('Restart camera with fallback configuration');
    }

    if (recommendation.needsFaceRecovery) {
      recommendation.recommendations.add('Reset face detection with performance configuration');
    }

    if (!recommendation.needsCameraRecovery && !recommendation.needsFaceRecovery) {
      recommendation.recommendations.add('System is operating normally');
    }

    return recommendation;
  }
}

/// Result of recovery operation
class RecoveryResult {
  bool success = false;
  bool cameraRecovered = false;
  bool faceDetectionRecovered = false;
  bool connectionReestablished = false;
  String? error;

  @override
  String toString() {
    return 'RecoveryResult(success: $success, camera: $cameraRecovered, '
           'face: $faceDetectionRecovered, connection: $connectionReestablished, '
           'error: $error)';
  }
}

/// Recovery recommendation
class RecoveryRecommendation {
  bool needsCameraRecovery = false;
  bool needsFaceRecovery = false;
  List<String> cameraIssues = [];
  List<String> faceIssues = [];
  List<String> recommendations = [];

  bool get needsRecovery => needsCameraRecovery || needsFaceRecovery;

  @override
  String toString() {
    return 'RecoveryRecommendation(needsRecovery: $needsRecovery, '
           'camera: $needsCameraRecovery, face: $needsFaceRecovery, '
           'recommendations: $recommendations)';
  }
}

# Common Components

<PERSON><PERSON><PERSON> mục này chứa các component chuẩn hóa được sử dụng trong toàn bộ ứng dụng.

## Components

### 1. CustomButton
Button component với các variant khác nhau.

```dart
// Primary button
CustomButton(
  text: 'Đăng nhập',
  onPressed: () => print('Pressed'),
  isLoading: false,
)

// Secondary button
CustomButton(
  text: 'Hủy',
  variant: ButtonVariant.secondary,
  onPressed: () => print('Pressed'),
)

// Text button
CustomButton(
  text: 'Quên mật khẩu?',
  variant: ButtonVariant.text,
  onPressed: () => print('Pressed'),
)
```

### 2. CustomTextField
Input field component với label và validation.

```dart
CustomTextField(
  controller: _controller,
  label: 'Tên đăng nhập',
  hintText: 'Nhập tên đăng nhập',
  isRequired: true,
  validator: (value) {
    if (value == null || value.isEmpty) {
      return '<PERSON>ui lòng nhập tên đăng nhập';
    }
    return null;
  },
)

// Password field
CustomTextField(
  controller: _passwordController,
  label: 'Mật khẩu',
  hintText: 'Nhập mật khẩu',
  isPassword: true,
  isRequired: true,
  obscureText: _obscurePassword,
  onToggleObscure: () => setState(() {
    _obscurePassword = !_obscurePassword;
  }),
)
```

### 3. CustomLabel
Label component với required indicator.

```dart
CustomLabel(
  text: 'Tên đăng nhập',
  isRequired: true,
)
```

### 4. ErrorMessage
Error message component với dismiss functionality.

```dart
ErrorMessage(
  message: 'Đã xảy ra lỗi',
  onDismiss: () => print('Dismissed'),
)
```

## Constants

### AppColors
Chứa tất cả màu sắc được sử dụng trong ứng dụng.

```dart
AppColors.primary
AppColors.textHeading
AppColors.textSubtle
AppColors.errorRed
// ...
```

### AppTextStyles
Chứa tất cả text styles được sử dụng trong ứng dụng.

```dart
AppTextStyles.heading
AppTextStyles.subtitle
AppTextStyles.label
AppTextStyles.buttonText
// ...
```

## Usage

Import tất cả components:
```dart
import '../../widgets/common/index.dart';
import '../../../core/constants/index.dart';
```

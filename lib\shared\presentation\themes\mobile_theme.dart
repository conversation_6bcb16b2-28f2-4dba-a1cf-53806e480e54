import 'package:flutter/material.dart';
import 'app_theme.dart';
import 'color_schemes.dart';
import 'text_themes.dart';

/// Mobile-specific theme configuration
/// 
/// Extends the base app theme with mobile-specific customizations
/// Optimized for touch interactions and mobile UI patterns
class MobileTheme {
  
  // ============================================================================
  // MOBILE THEME CONSTANTS
  // ============================================================================
  
  static const double mobileBottomNavHeight = 80.0;
  static const double mobileFabSize = 56.0;
  static const double mobileAppBarHeight = 56.0;
  static const double mobilePadding = 16.0;
  static const double mobileCardMargin = 8.0;
  
  // ============================================================================
  // MOBILE LIGHT THEME
  // ============================================================================
  
  static ThemeData get lightTheme {
    final baseTheme = AppTheme.lightTheme;
    
    return baseTheme.copyWith(
      // Mobile-specific app bar
      appBarTheme: baseTheme.appBarTheme.copyWith(
        toolbarHeight: mobileAppBarHeight,
        titleSpacing: mobilePadding,
        backgroundColor: AppColorSchemes.lightColorScheme.surface,
        foregroundColor: AppColorSchemes.lightColorScheme.onSurface,
        elevation: 1.0,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
      ),
      
      // Mobile-optimized bottom navigation
      bottomNavigationBarTheme: baseTheme.bottomNavigationBarTheme.copyWith(
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColorSchemes.lightColorScheme.surface,
        selectedItemColor: AppColorSchemes.lightColorScheme.primary,
        unselectedItemColor: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        selectedLabelStyle: AppTextThemes.lightTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextThemes.lightTextTheme.labelSmall,
        elevation: 8.0,
        enableFeedback: true,
      ),
      
      // Mobile-optimized floating action button
      floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        sizeConstraints: const BoxConstraints.tightFor(
          width: mobileFabSize,
          height: mobileFabSize,
        ),
        elevation: 6.0,
        focusElevation: 8.0,
        hoverElevation: 8.0,
        highlightElevation: 12.0,
      ),
      
      // Mobile-optimized cards
      cardTheme: baseTheme.cardTheme.copyWith(
        margin: const EdgeInsets.all(mobileCardMargin),
        elevation: 2.0,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
      ),
      
      // Mobile-optimized list tiles
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: mobilePadding,
          vertical: 8.0,
        ),
        minVerticalPadding: 8.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        ),
        tileColor: AppColorSchemes.lightColorScheme.surface,
        selectedTileColor: AppColorSchemes.lightColorScheme.primaryContainer,
        iconColor: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        textColor: AppColorSchemes.lightColorScheme.onSurface,
        titleTextStyle: AppTextThemes.lightTextTheme.bodyLarge,
        subtitleTextStyle: AppTextThemes.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        ),
      ),
      
      // Mobile-optimized navigation drawer
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColorSchemes.lightColorScheme.surface,
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
        elevation: 16.0,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.2),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16.0),
            bottomRight: Radius.circular(16.0),
          ),
        ),
      ),
      
      // Mobile-optimized tab bar
      tabBarTheme: TabBarThemeData(
        labelColor: AppColorSchemes.lightColorScheme.primary,
        unselectedLabelColor: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        labelStyle: AppTextThemes.lightTextTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextThemes.lightTextTheme.titleSmall,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            color: AppColorSchemes.lightColorScheme.primary,
            width: 3.0,
          ),
        ),
        indicatorSize: TabBarIndicatorSize.label,
        dividerColor: AppColorSchemes.lightColorScheme.outline.withOpacity(0.2),
      ),
      
      // Mobile-optimized snack bar
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColorSchemes.lightColorScheme.inverseSurface,
        contentTextStyle: AppTextThemes.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.lightColorScheme.onInverseSurface,
        ),
        actionTextColor: AppColorSchemes.lightColorScheme.inversePrimary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        ),
        elevation: 6.0,
      ),
    );
  }
  
  // ============================================================================
  // MOBILE DARK THEME
  // ============================================================================
  
  static ThemeData get darkTheme {
    final baseTheme = AppTheme.darkTheme;
    
    return baseTheme.copyWith(
      // Mobile-specific app bar
      appBarTheme: baseTheme.appBarTheme.copyWith(
        toolbarHeight: mobileAppBarHeight,
        titleSpacing: mobilePadding,
        backgroundColor: AppColorSchemes.darkColorScheme.surface,
        foregroundColor: AppColorSchemes.darkColorScheme.onSurface,
        elevation: 1.0,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.3),
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
      ),
      
      // Mobile-optimized bottom navigation
      bottomNavigationBarTheme: baseTheme.bottomNavigationBarTheme.copyWith(
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColorSchemes.darkColorScheme.surface,
        selectedItemColor: AppColorSchemes.darkColorScheme.primary,
        unselectedItemColor: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        selectedLabelStyle: AppTextThemes.darkTextTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextThemes.darkTextTheme.labelSmall,
        elevation: 8.0,
        enableFeedback: true,
      ),
      
      // Mobile-optimized floating action button
      floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        sizeConstraints: const BoxConstraints.tightFor(
          width: mobileFabSize,
          height: mobileFabSize,
        ),
        elevation: 6.0,
        focusElevation: 8.0,
        hoverElevation: 8.0,
        highlightElevation: 12.0,
      ),
      
      // Mobile-optimized cards
      cardTheme: baseTheme.cardTheme.copyWith(
        margin: const EdgeInsets.all(mobileCardMargin),
        elevation: 2.0,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.3),
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
      ),
      
      // Mobile-optimized list tiles
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: mobilePadding,
          vertical: 8.0,
        ),
        minVerticalPadding: 8.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        ),
        tileColor: AppColorSchemes.darkColorScheme.surface,
        selectedTileColor: AppColorSchemes.darkColorScheme.primaryContainer,
        iconColor: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        textColor: AppColorSchemes.darkColorScheme.onSurface,
        titleTextStyle: AppTextThemes.darkTextTheme.bodyLarge,
        subtitleTextStyle: AppTextThemes.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        ),
      ),
      
      // Mobile-optimized navigation drawer
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColorSchemes.darkColorScheme.surface,
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
        elevation: 16.0,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.4),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16.0),
            bottomRight: Radius.circular(16.0),
          ),
        ),
      ),
      
      // Mobile-optimized tab bar
      tabBarTheme: TabBarThemeData(
        labelColor: AppColorSchemes.darkColorScheme.primary,
        unselectedLabelColor: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        labelStyle: AppTextThemes.darkTextTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextThemes.darkTextTheme.titleSmall,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            color: AppColorSchemes.darkColorScheme.primary,
            width: 3.0,
          ),
        ),
        indicatorSize: TabBarIndicatorSize.label,
        dividerColor: AppColorSchemes.darkColorScheme.outline.withOpacity(0.2),
      ),
      
      // Mobile-optimized snack bar
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColorSchemes.darkColorScheme.inverseSurface,
        contentTextStyle: AppTextThemes.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.darkColorScheme.onInverseSurface,
        ),
        actionTextColor: AppColorSchemes.darkColorScheme.inversePrimary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        ),
        elevation: 6.0,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/device_registration_provider.dart';

class DeviceRegistrationScreen extends StatefulWidget {
  const DeviceRegistrationScreen({super.key});

  @override
  State<DeviceRegistrationScreen> createState() => _DeviceRegistrationScreenState();
}

class _DeviceRegistrationScreenState extends State<DeviceRegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _deviceIdController = TextEditingController();
  final _deviceNameController = TextEditingController();
  final _deviceTypeController = TextEditingController();
  final _locationController = TextEditingController();
  final _serverUrlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    // Set default values
    _deviceIdController.text = 'terminal-${DateTime.now().millisecondsSinceEpoch}';
    _deviceNameController.text = 'Security Terminal';
    _deviceTypeController.text = 'face_terminal';
    _locationController.text = 'Main Entrance';
    _serverUrlController.text = 'http://localhost:3000';
  }

  @override
  void dispose() {
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    _deviceTypeController.dispose();
    _locationController.dispose();
    _serverUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Device Registration'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<DeviceRegistrationProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 32),

                // Registration Form
                _buildRegistrationForm(provider),
                const SizedBox(height: 32),

                // Registration Button
                _buildRegistrationButton(provider),
                const SizedBox(height: 24),

                // Progress/Error Display
                _buildStatusDisplay(provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Icon(
              Icons.security,
              size: 64,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Device Registration',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Register this terminal with the security server to enable secure communication and access control.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationForm(DeviceRegistrationProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Device Information',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Device ID
              TextFormField(
                controller: _deviceIdController,
                decoration: const InputDecoration(
                  labelText: 'Device ID',
                  hintText: 'Unique identifier for this device',
                  prefixIcon: Icon(Icons.fingerprint),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Device ID is required';
                  }
                  if (value.length < 3) {
                    return 'Device ID must be at least 3 characters';
                  }
                  return null;
                },
                enabled: provider.status != DeviceRegistrationStatus.registering,
              ),
              const SizedBox(height: 16),

              // Device Name
              TextFormField(
                controller: _deviceNameController,
                decoration: const InputDecoration(
                  labelText: 'Device Name',
                  hintText: 'Human-readable name for this device',
                  prefixIcon: Icon(Icons.label),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Device name is required';
                  }
                  return null;
                },
                enabled: provider.status != DeviceRegistrationStatus.registering,
              ),
              const SizedBox(height: 16),

              // Device Type
              DropdownButtonFormField<String>(
                value: _deviceTypeController.text.isNotEmpty 
                    ? _deviceTypeController.text 
                    : null,
                decoration: const InputDecoration(
                  labelText: 'Device Type',
                  prefixIcon: Icon(Icons.category),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'face_terminal',
                    child: Text('Face Recognition Terminal'),
                  ),
                  DropdownMenuItem(
                    value: 'relay_controller',
                    child: Text('Relay Controller'),
                  ),
                  DropdownMenuItem(
                    value: 'access_terminal',
                    child: Text('Access Control Terminal'),
                  ),
                  DropdownMenuItem(
                    value: 'security_terminal',
                    child: Text('Security Terminal'),
                  ),
                ],
                onChanged: provider.status != DeviceRegistrationStatus.registering
                    ? (value) {
                        if (value != null) {
                          _deviceTypeController.text = value;
                        }
                      }
                    : null,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Device type is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Location
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(
                  labelText: 'Location',
                  hintText: 'Physical location of this device',
                  prefixIcon: Icon(Icons.location_on),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Location is required';
                  }
                  return null;
                },
                enabled: provider.status != DeviceRegistrationStatus.registering,
              ),
              const SizedBox(height: 16),

              // Server URL
              TextFormField(
                controller: _serverUrlController,
                decoration: const InputDecoration(
                  labelText: 'Server URL',
                  hintText: 'URL of the security server',
                  prefixIcon: Icon(Icons.cloud),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Server URL is required';
                  }
                  final uri = Uri.tryParse(value);
                  if (uri == null || !uri.hasAbsolutePath) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
                enabled: provider.status != DeviceRegistrationStatus.registering,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRegistrationButton(DeviceRegistrationProvider provider) {
    return SizedBox(
      height: 56,
      child: ElevatedButton.icon(
        onPressed: provider.status == DeviceRegistrationStatus.registering
            ? null
            : _handleRegistration,
        icon: provider.status == DeviceRegistrationStatus.registering
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.app_registration),
        label: Text(
          provider.status == DeviceRegistrationStatus.registering
              ? 'Registering...'
              : 'Register Device',
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusDisplay(DeviceRegistrationProvider provider) {
    if (provider.status == DeviceRegistrationStatus.registering) {
      return Card(
        color: Colors.blue.shade50,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const LinearProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                provider.registrationProgress.isNotEmpty
                    ? provider.registrationProgress
                    : 'Registering device...',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (provider.status == DeviceRegistrationStatus.error) {
      return Card(
        color: Colors.red.shade50,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.error,
                color: Colors.red.shade700,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                'Registration Failed',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                provider.errorMessage ?? 'Unknown error occurred',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: provider.clearError,
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }

    if (provider.status == DeviceRegistrationStatus.registered) {
      return Card(
        color: Colors.green.shade50,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green.shade700,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                'Registration Successful',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Device has been successfully registered with the server.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Continue'),
              ),
            ],
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Future<void> _handleRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final provider = Provider.of<DeviceRegistrationProvider>(
      context,
      listen: false,
    );

    final success = await provider.registerDevice(
      deviceId: _deviceIdController.text.trim(),
      deviceName: _deviceNameController.text.trim(),
      deviceType: _deviceTypeController.text.trim(),
      location: _locationController.text.trim(),
      serverUrl: _serverUrlController.text.trim(),
      capabilities: _getDeviceCapabilities(),
      metadata: _getDeviceMetadata(),
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Device registered successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  List<String> _getDeviceCapabilities() {
    switch (_deviceTypeController.text) {
      case 'face_terminal':
        return ['face_auth', 'relay_control', 'image_upload'];
      case 'relay_controller':
        return ['relay_control', 'status_monitoring'];
      case 'access_terminal':
        return ['face_auth', 'relay_control', 'access_logging'];
      case 'security_terminal':
        return ['face_auth', 'relay_control', 'image_upload', 'security_monitoring'];
      default:
        return ['basic_communication'];
    }
  }

  Map<String, dynamic> _getDeviceMetadata() {
    return {
      'app_version': '1.0.0',
      'platform': 'flutter',
      'registration_time': DateTime.now().toIso8601String(),
      'capabilities_description': _getCapabilitiesDescription(),
    };
  }

  String _getCapabilitiesDescription() {
    switch (_deviceTypeController.text) {
      case 'face_terminal':
        return 'Face recognition with door control';
      case 'relay_controller':
        return 'Multi-zone relay control system';
      case 'access_terminal':
        return 'Access control with logging';
      case 'security_terminal':
        return 'Comprehensive security terminal';
      default:
        return 'Basic communication device';
    }
  }
}

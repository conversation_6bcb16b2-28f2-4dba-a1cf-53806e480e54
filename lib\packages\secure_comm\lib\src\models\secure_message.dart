import 'dart:convert';

/// Standard secure message format for device-server communication
class SecureMessage {
  /// Unique device identifier
  final String deviceId;

  /// Message type (e.g., 'face_auth', 'relay_control', 'status', 'image_upload')
  final String type;

  /// Message payload - can contain any data structure
  final Map<String, dynamic> payload;

  /// Unix timestamp when message was created
  final int timestamp;

  /// HMAC signature of the message
  final String? signature;

  /// Optional message ID for tracking
  final String? messageId;

  /// Optional priority level
  final int priority;

  /// Optional encryption flag
  final bool encrypted;

  const SecureMessage({
    required this.deviceId,
    required this.type,
    required this.payload,
    required this.timestamp,
    this.signature,
    this.messageId,
    this.priority = 0,
    this.encrypted = false,
  });

  /// Create a new message with current timestamp
  factory SecureMessage.create({
    required String deviceId,
    required String type,
    required Map<String, dynamic> payload,
    String? messageId,
    int priority = 0,
    bool encrypted = false,
  }) {
    return SecureMessage(
      deviceId: deviceId,
      type: type,
      payload: payload,
      timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      messageId: messageId,
      priority: priority,
      encrypted: encrypted,
    );
  }

  /// Create message from JSON
  factory SecureMessage.fromJson(Map<String, dynamic> json) {
    return SecureMessage(
      deviceId: json['device_id'] as String,
      type: json['type'] as String,
      payload: Map<String, dynamic>.from(json['payload'] as Map),
      timestamp: json['timestamp'] as int,
      signature: json['signature'] as String?,
      messageId: json['message_id'] as String?,
      priority: json['priority'] as int? ?? 0,
      encrypted: json['encrypted'] as bool? ?? false,
    );
  }

  /// Convert message to JSON (without signature for signing)
  Map<String, dynamic> toJson({bool includeSignature = true}) {
    final json = <String, dynamic>{
      'device_id': deviceId,
      'type': type,
      'payload': payload,
      'timestamp': timestamp,
      'priority': priority,
      'encrypted': encrypted,
    };

    if (messageId != null) {
      json['message_id'] = messageId;
    }

    if (includeSignature && signature != null) {
      json['signature'] = signature;
    }

    return json;
  }

  /// Get canonical string representation for signing
  String getCanonicalString() {
    final data = toJson(includeSignature: false);
    return _canonicalizeJson(data);
  }

  /// Create a signed copy of this message
  SecureMessage withSignature(String signature) {
    return SecureMessage(
      deviceId: deviceId,
      type: type,
      payload: payload,
      timestamp: timestamp,
      signature: signature,
      messageId: messageId,
      priority: priority,
      encrypted: encrypted,
    );
  }

  /// Create a copy with new payload
  SecureMessage withPayload(Map<String, dynamic> newPayload) {
    return SecureMessage(
      deviceId: deviceId,
      type: type,
      payload: newPayload,
      timestamp: timestamp,
      signature: signature,
      messageId: messageId,
      priority: priority,
      encrypted: encrypted,
    );
  }

  /// Check if message is expired based on timestamp
  bool isExpired({int toleranceSeconds = 300}) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final age = now - timestamp;
    return age > toleranceSeconds;
  }

  /// Canonicalize JSON for consistent signing
  static String _canonicalizeJson(Map<String, dynamic> data) {
    final sortedKeys = data.keys.toList()..sort();
    final parts = <String>[];

    for (final key in sortedKeys) {
      final value = data[key];
      if (value != null) {
        String valueStr;
        if (value is Map) {
          valueStr = _canonicalizeJson(value as Map<String, dynamic>);
        } else if (value is List) {
          valueStr = jsonEncode(value);
        } else {
          valueStr = value.toString();
        }
        parts.add('$key=$valueStr');
      }
    }

    return parts.join('&');
  }

  @override
  String toString() {
    return 'SecureMessage(deviceId: $deviceId, type: $type, timestamp: $timestamp, '
           'messageId: $messageId, priority: $priority, encrypted: $encrypted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SecureMessage &&
        other.deviceId == deviceId &&
        other.type == type &&
        other.timestamp == timestamp &&
        other.messageId == messageId;
  }

  @override
  int get hashCode {
    return Object.hash(deviceId, type, timestamp, messageId);
  }
}

/// Response message from server
class SecureResponse {
  /// Response status
  final bool success;

  /// Response data
  final Map<String, dynamic>? data;

  /// Error message if any
  final String? error;

  /// Response timestamp
  final int timestamp;

  /// Optional signature for response verification
  final String? signature;

  const SecureResponse({
    required this.success,
    this.data,
    this.error,
    required this.timestamp,
    this.signature,
  });

  factory SecureResponse.fromJson(Map<String, dynamic> json) {
    return SecureResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
      error: json['error'] as String?,
      timestamp: json['timestamp'] as int? ?? 
                 (DateTime.now().millisecondsSinceEpoch ~/ 1000),
      signature: json['signature'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      if (data != null) 'data': data,
      if (error != null) 'error': error,
      'timestamp': timestamp,
      if (signature != null) 'signature': signature,
    };
  }

  @override
  String toString() {
    return 'SecureResponse(success: $success, error: $error, timestamp: $timestamp)';
  }
}

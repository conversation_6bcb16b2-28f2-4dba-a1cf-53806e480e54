class ShiftDetail {
  final String id;
  final String shiftId;
  final bool isOvernight;
  final String checkInStartTime;
  final String lateThresholdTime;
  final String halfDayMissedStartTime;
  final String breakTime;
  final String checkOutStartTime;
  final String earlyLeaveThresholdTime;
  final String halfDayMissedEndTime;
  final double totalWorkingHours;
  final bool checkInRequired;
  final String flexLateThresholdTime;
  final String flexHalfDayMissedTime;

  ShiftDetail({
    required this.id,
    required this.shiftId,
    required this.isOvernight,
    required this.checkInStartTime,
    required this.lateThresholdTime,
    required this.halfDayMissedStartTime,
    required this.breakTime,
    required this.checkOutStartTime,
    required this.earlyLeaveThresholdTime,
    required this.halfDayMissedEndTime,
    required this.totalWorkingHours,
    required this.checkInRequired,
    required this.flexLateThresholdTime,
    required this.flexHalfDayMissedTime,
  });
}

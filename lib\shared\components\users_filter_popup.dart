import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../icons/app_icons.dart';
import '../data/services/units_api_service.dart';

enum FilterType { main, status, unit }

/// Popup filter cho màn hình users
class UsersFilterPopup extends StatefulWidget {
  final String? selectedUnit;
  final String? selectedStatus;
  final Function(String? unitId, String? status)? onApplyFilter;
  final VoidCallback? onClearFilter;

  const UsersFilterPopup({
    super.key,
    this.selectedUnit,
    this.selectedStatus,
    this.onApplyFilter,
    this.onClearFilter,
  });

  @override
  State<UsersFilterPopup> createState() => _UsersFilterPopupState();
}

class _UsersFilterPopupState extends State<UsersFilterPopup> {
  FilterType _currentFilterType = FilterType.main;
  String? _tempSelectedUnit;
  String? _tempSelectedStatus;

  // Units service and data
  final UnitsApiService _unitsService = UnitsApiService();
  List<UnitOption> _units = [];
  bool _isLoadingUnits = false;

  @override
  void initState() {
    super.initState();
    _tempSelectedUnit = widget.selectedUnit;
    _tempSelectedStatus = widget.selectedStatus;
    _loadUnits();
  }

  Future<void> _loadUnits() async {
    setState(() {
      _isLoadingUnits = true;
    });

    try {
      final units = await _unitsService.getUnitsForFilter();
      setState(() {
        _units = units;
        _isLoadingUnits = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingUnits = false;
      });
      // Handle error silently, show empty list
    }
  }

  @override
  Widget build(BuildContext context) {
    final maxHeight = MediaQuery.of(context).size.height * 0.5; // 50% màn hình

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildDivider(),
          Flexible(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }



  Widget _buildHeader() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentFilterType != FilterType.main)
            GestureDetector(
              onTap: () => setState(() => _currentFilterType = FilterType.main),
              child: AppIcons.arrowLeft(
                size: 24,
                color: const Color(0xFF141B34),
              ),
            )
          else
            const SizedBox(width: 24),
          Expanded(
            child: Text(
              _getHeaderTitle(),
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2329),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: AppIcons.close(size: 24, color: const Color(0xFF1F2329)),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(height: 1, color: const Color(0xFFDDE4EE));
  }

  String _getHeaderTitle() {
    switch (_currentFilterType) {
      case FilterType.main:
        return 'Bộ lọc';
      case FilterType.status:
        return 'Trạng thái';
      case FilterType.unit:
        return 'Đơn vị';
    }
  }

  Widget _buildContent() {
    switch (_currentFilterType) {
      case FilterType.main:
        return _buildMainFilter();
      case FilterType.status:
        return _buildStatusFilter();
      case FilterType.unit:
        return _buildUnitFilter();
    }
  }

  Widget _buildMainFilter() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFilterItem(
          title: 'Trạng thái',
          value: _getStatusDisplayText(),
          onTap: () => setState(() => _currentFilterType = FilterType.status),
          showArrow: true,
        ),
        _buildFilterItem(
          title: 'Đơn vị',
          value: _getSelectedUnitDisplayText(),
          onTap: () => setState(() => _currentFilterType = FilterType.unit),
          showArrow: true,
        ),
        const SizedBox(height: 16),
        _buildActionButtons(),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Scrollable content
        Flexible(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 12),
                _buildStatusOption('Tất cả', null),
                _buildStatusOption('Đang hoạt động', 'active'),
                _buildStatusOption('Ngừng hoạt động', 'inactive'),
              ],
            ),
          ),
        ),
        // Sticky button
        _buildApplyButton(),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildUnitFilter() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Scrollable content
        Flexible(
          child: _isLoadingUnits
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32.0),
                    child: CircularProgressIndicator(),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 12),
                      _buildUnitOption('Tất cả', null),
                      ..._units.map((unit) => _buildUnitOption(unit.name, unit.id)),
                    ],
                  ),
                ),
        ),
        // Sticky button
        _buildApplyButton(),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildFilterItem({
    required String title,
    required String value,
    required VoidCallback onTap,
    bool showArrow = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 46.25,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2329),
                ),
              ),
            ),
            Text(
              value,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFF1F2329),
              ),
            ),
            if (showArrow) ...[
              const SizedBox(width: 8),
              AppIcons.arrowRight(size: 14, color: const Color(0xFF8F959E)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusOption(String title, String? value) {
    final isSelected = _tempSelectedStatus == value;

    return GestureDetector(
      onTap: () => setState(() => _tempSelectedStatus = value),
      child: Container(
        height: 46.25,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2329),
                ),
              ),
            ),
            if (isSelected) AppIcons.check(size: 14, color: AppColors.primary),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitOption(String title, String? value) {
    final isSelected = _tempSelectedUnit == value;

    return GestureDetector(
      onTap: () => setState(() => _tempSelectedUnit = value),
      child: Container(
        height: 46.25,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2329),
                ),
              ),
            ),
            if (isSelected) AppIcons.check(size: 14, color: AppColors.primary),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _handleClearFilter,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primary),
                ),
                child: Center(
                  child: Text(
                    'Xoá bộ lọc',
                    style: AppTextStyles.caption.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: GestureDetector(
              onTap: _handleApplyFilter,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    'Lọc',
                    style: AppTextStyles.caption.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplyButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: GestureDetector(
        onTap: _handleApplyFilter,
        child: Container(
          width: double.infinity,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              'Lọc',
              style: AppTextStyles.caption.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getStatusDisplayText() {
    if (_tempSelectedStatus == null) return 'Tất cả';
    if (_tempSelectedStatus == 'active') return 'Đang hoạt động';
    if (_tempSelectedStatus == 'inactive') return 'Ngừng hoạt động';
    return 'Tất cả';
  }

  String _getSelectedUnitDisplayText() {
    if (_tempSelectedUnit == null) return 'Tất cả';

    // Find the unit name by ID
    final selectedUnit = _units.firstWhere(
      (unit) => unit.id == _tempSelectedUnit,
      orElse: () => UnitOption(id: '', name: 'Tất cả'),
    );

    return selectedUnit.name;
  }

  void _handleApplyFilter() {
    widget.onApplyFilter?.call(_tempSelectedUnit, _tempSelectedStatus);
    Navigator.of(context).pop();
  }

  void _handleClearFilter() {
    setState(() {
      _tempSelectedUnit = null;
      _tempSelectedStatus = null;
    });
    widget.onClearFilter?.call();
    Navigator.of(context).pop();
  }
}

import 'package:dartz/dartz.dart';
import '../../domain/entities/auth/auth_result.dart';
import '../../domain/entities/user/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_client.dart';
import '../data_sources/remote/auth_remote_data_source.dart';
import '../data_sources/local/auth_local_data_source.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final ApiClient apiClient;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.apiClient,
  });

  @override
  Future<Either<Failure, AuthResult>> login({
    required String userName,
    required String password,
  }) async {
    try {
      // Call remote data source to login
      final authResult = await remoteDataSource.login(
        userName: userName,
        password: password,
      );

      // Save auth result locally
      await localDataSource.saveAuthResult(authResult);

      // Set access token in API client
      apiClient.setAccessToken(authResult.accessToken);

      return Right(authResult.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      // Try to logout from server
      try {
        await remoteDataSource.logout();
      } catch (e) {
        // Continue with local logout even if server logout fails
      }

      // Clear local auth data
      await localDataSource.clearAuthData();

      // Clear access token from API client
      apiClient.setAccessToken(null);

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to logout: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthResult>> refreshToken(String refreshToken) async {
    try {
      final authResult = await remoteDataSource.refreshToken(refreshToken);

      // Save new auth result locally
      await localDataSource.saveAuthResult(authResult);

      // Update access token in API client
      apiClient.setAccessToken(authResult.accessToken);

      return Right(authResult.toEntity());
    } on AuthException catch (e) {
      // If refresh fails, clear local auth data
      await localDataSource.clearAuthData();
      apiClient.setAccessToken(null);
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to refresh token: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      // Try to get user from local storage first
      final localUser = await localDataSource.getUser();
      if (localUser != null) {
        return Right(localUser.toEntity());
      }

      // If not found locally, fetch from server
      final remoteUser = await remoteDataSource.getCurrentUser();
      
      // Save user locally
      await localDataSource.saveUser(remoteUser);

      return Right(remoteUser.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get current user: $e'));
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    return await localDataSource.isAuthenticated();
  }

  @override
  Future<String?> getAccessToken() async {
    return await localDataSource.getAccessToken();
  }

  @override
  Future<String?> getRefreshToken() async {
    return await localDataSource.getRefreshToken();
  }

  @override
  Future<void> clearAuthData() async {
    await localDataSource.clearAuthData();
    apiClient.setAccessToken(null);
  }

  @override
  Future<Either<Failure, void>> logoutAll() async {
    try {
      // Try to logout from all devices on server
      try {
        await remoteDataSource.logoutAll();
      } catch (e) {
        // Continue with local logout even if server logout fails
      }

      // Clear local auth data
      await localDataSource.clearAuthData();

      // Clear access token from API client
      apiClient.setAccessToken(null);

      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to logout from all devices: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> verifyToken() async {
    try {
      final isValid = await remoteDataSource.verifyToken();
      return Right(isValid);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to verify token: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> updateProfile({
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
  }) async {
    try {
      final updatedUser = await remoteDataSource.updateProfile(
        name: name,
        email: email,
        phone: phone,
        dob: dob,
        gender: gender,
      );

      // Save updated user locally
      await localDataSource.saveUser(updatedUser);

      return Right(updatedUser.toEntity());
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update profile: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final result = await remoteDataSource.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );

      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to change password: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getSessions() async {
    try {
      final sessions = await remoteDataSource.getSessions();
      return Right(sessions);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get sessions: $e'));
    }
  }
}

/// Quick Configuration Widget
/// 
/// Provides quick access to commonly used configuration parameters
/// for runtime adjustments without opening the full admin interface.

import 'package:flutter/material.dart';
import '../configuration_manager.dart';
import '../config_helper.dart';
import '../flexible_config_system.dart';
import '../config_parameters_registry.dart';
import 'config_title_helper.dart';
import 'config_help_dialog.dart';

class QuickConfigWidget extends StatefulWidget {
  final List<String> configKeys;
  final String title;
  final VoidCallback? onChanged;

  const QuickConfigWidget({
    super.key,
    required this.configKeys,
    this.title = 'Quick Configuration',
    this.onChanged,
  });

  @override
  State<QuickConfigWidget> createState() => _QuickConfigWidgetState();
}

class _QuickConfigWidgetState extends State<QuickConfigWidget> {
  final Map<String, TextEditingController> _controllers = {};
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final manager = ConfigurationManager.instance;
    final registry = ConfigParametersRegistry.getAllParameters();

    for (final key in widget.configKeys) {
      try {
        // Get parameter definition to get default value
        final parameter = registry[key];
        if (parameter != null) {
          final value = manager.getValue(key, defaultValue: parameter.defaultValue);

          // Format value properly for display
          String displayValue;
          if (value is Duration) {
            // Display duration as seconds for user-friendly input
            displayValue = _formatDurationForDisplay(value);
          } else if (value is Color) {
            final argb = ((value.a * 255).round() << 24) |
                        ((value.r * 255).round() << 16) |
                        ((value.g * 255).round() << 8) |
                        (value.b * 255).round();
            displayValue = '0x${argb.toRadixString(16).padLeft(8, '0').toUpperCase()}';
          } else {
            displayValue = value.toString();
          }

          _controllers[key] = TextEditingController(text: displayValue);
        } else {
          _controllers[key] = TextEditingController(text: 'Unknown parameter');
        }
      } catch (e) {
        _controllers[key] = TextEditingController(text: 'Error loading value');
      }
    }
  }

  Future<void> _updateValue(String key, String value) async {
    try {
      final manager = ConfigurationManager.instance;
      final registry = ConfigParametersRegistry.getAllParameters();
      final parameter = registry[key];

      if (parameter == null) {
        throw Exception('Parameter not found: $key');
      }

      // Parse value based on parameter type
      dynamic parsedValue = parameter.parseValue(value);

      // Validate the value
      if (!parameter.isValid(parsedValue)) {
        throw Exception('Invalid value for parameter $key');
      }

      await manager.setValue(key, parsedValue);
      widget.onChanged?.call();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getFriendlyUpdateMessage(key, parsedValue)),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Lỗi khi cập nhật $key: $e';
        
        // Provide more user-friendly error messages for common issues
        if (e.toString().contains('FormatException')) {
          if (key.contains('duration') || key.contains('timeout') || key.contains('delay')) {
            errorMessage = '⚠️ ${_getFriendlyParameterName(key)}: Vui lòng nhập số giây (ví dụ: 5 hoặc 8.5)';
          } else if (key.contains('quality') || key.contains('threshold')) {
            errorMessage = '⚠️ ${_getFriendlyParameterName(key)}: Vui lòng nhập số từ 0.0 đến 1.0 (ví dụ: 0.4 hoặc 0.6)';
          } else if (key.contains('color')) {
            errorMessage = '⚠️ ${_getFriendlyParameterName(key)}: Vui lòng nhập mã màu hex (ví dụ: 0xFF1976D2)';
          } else if (key.contains('size') || key.contains('width') || key.contains('height')) {
            errorMessage = '⚠️ ${_getFriendlyParameterName(key)}: Vui lòng nhập số pixel (ví dụ: 100 hoặc 200)';
          } else {
            errorMessage = '⚠️ ${_getFriendlyParameterName(key)}: Định dạng không hợp lệ';
          }
        } else if (e.toString().contains('Invalid value')) {
          errorMessage = '⚠️ Giá trị không hợp lệ cho ${_getFriendlyParameterName(key)}';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// Format Duration for display (as seconds)
  String _formatDurationForDisplay(Duration duration) {
    final seconds = duration.inMilliseconds / 1000;
    if (seconds == seconds.toInt()) {
      return '${seconds.toInt()}';
    } else {
      return seconds.toStringAsFixed(1);
    }
  }

  /// Get friendly update message for configuration changes
  String _getFriendlyUpdateMessage(String key, dynamic value) {
    switch (key) {
      case 'face_detection.min_quality_detection':
        return '✅ Đã cập nhật ngưỡng chất lượng phát hiện khuôn mặt: ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.min_quality_recognition':
        return '✅ Đã cập nhật ngưỡng chất lượng nhận diện khuôn mặt: ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.min_quality_avatar_capture':
        return '✅ Đã cập nhật ngưỡng chất lượng chụp avatar: ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.recognition_throttle_duration':
        if (value is Duration) {
          return '✅ Đã cập nhật thời gian giãn cách nhận diện: ${_formatDurationForDisplay(value)} giây';
        }
        return '✅ Đã cập nhật thời gian giãn cách nhận diện: $value giây';
      case 'face_detection.frame_skip_count':
        return '✅ Đã cập nhật số khung hình bỏ qua: $value';
      case 'network.base_api_url':
        return '✅ Đã cập nhật URL API cơ sở: $value';
      case 'network.request_timeout':
        if (value is Duration) {
          return '✅ Đã cập nhật thời gian chờ request: ${_formatDurationForDisplay(value)}s';
        }
        return '✅ Đã cập nhật thời gian chờ request: ${value}s';
      case 'network.max_retry_attempts':
        return '✅ Đã cập nhật số lần thử lại tối đa: $value';
      case 'network.retry_delay':
        if (value is Duration) {
          return '✅ Đã cập nhật độ trễ thử lại: ${_formatDurationForDisplay(value)}s';
        }
        return '✅ Đã cập nhật độ trễ thử lại: ${value}s';
      case 'camera.normal_frame_rate':
        return '✅ Đã cập nhật tốc độ khung hình bình thường: ${value}fps';
      case 'camera.optimized_frame_rate':
        return '✅ Đã cập nhật tốc độ khung hình tối ưu: ${value}fps';
      case 'camera.power_saving_delay':
        if (value is Duration) {
          return '✅ Đã cập nhật độ trễ tiết kiệm pin: ${_formatDurationForDisplay(value)}s';
        }
        return '✅ Đã cập nhật độ trễ tiết kiệm pin: ${value}s';
      case 'ui.primary_color':
        return '✅ Đã cập nhật màu chính của giao diện';
      case 'ui.success_color':
        return '✅ Đã cập nhật màu thành công';
      case 'ui.avatar_size':
        return '✅ Đã cập nhật kích thước avatar: ${value}px';
      case 'ui.animation_duration':
        if (value is Duration) {
          return '✅ Đã cập nhật thời gian animation: ${_formatDurationForDisplay(value)}s';
        }
        return '✅ Đã cập nhật thời gian animation: ${value}s';
      case 'debug.enable_face_detection_logs':
        return '✅ Đã ${value ? "bật" : "tắt"} logs phát hiện khuôn mặt';
      case 'debug.enable_performance_monitoring':
        return '✅ Đã ${value ? "bật" : "tắt"} giám sát hiệu suất';
      default:
        // For other keys, try to make them more readable
        final friendlyKey = key
            .replaceAll('_', ' ')
            .replaceAll('.', ' → ')
            .split(' ')
            .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : '')
            .join(' ');
        return '✅ Đã cập nhật $friendlyKey: $value';
    }
  }

  /// Get friendly parameter name for error messages
  String _getFriendlyParameterName(String key) {
    switch (key) {
      case 'face_detection.recognition_throttle_duration':
        return 'Thời gian giãn cách nhận diện';
      case 'face_detection.detection_timeout':
        return 'Thời gian chờ phát hiện';
      case 'face_detection.user_display_timeout':
        return 'Thời gian hiển thị người dùng';
      case 'face_detection.avatar_capture_throttle':
        return 'Thời gian giãn cách chụp avatar';
      case 'network.request_timeout':
        return 'Thời gian chờ request';
      case 'network.retry_delay':
        return 'Độ trễ thử lại';
      case 'ui.animation_duration':
        return 'Thời gian animation';
      case 'camera.power_saving_delay':
        return 'Độ trễ tiết kiệm pin';
      default:
        return key.replaceAll('_', ' ').replaceAll('.', ' → ');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Column(
        children: [
          ListTile(
            title: Text(widget.title),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () => setState(() => _isExpanded = !_isExpanded),
            ),
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: widget.configKeys.map(_buildConfigField).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildConfigField(String key) {
    final controller = _controllers[key];
    if (controller == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              ConfigTitleHelper.getShortTitle(key),
              style: const TextStyle(fontSize: 12),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: TextFormField(
              controller: controller,
              decoration: const InputDecoration(
                isDense: true,
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              ),
              style: const TextStyle(fontSize: 12),
              onFieldSubmitted: (value) => _updateValue(key, value),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.help_outline, size: 16),
            onPressed: () {
              ConfigHelpDialog.show(
                context,
                key,
                controller.text,
              );
            },
            tooltip: 'Xem hướng dẫn',
          ),
          IconButton(
            icon: const Icon(Icons.check, size: 16),
            onPressed: () => _updateValue(key, controller.text),
            tooltip: 'Apply',
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
}

/// Predefined quick config widgets for common use cases
class QuickConfigPresets {
  static Widget faceDetectionConfig({VoidCallback? onChanged}) {
    return QuickConfigWidget(
      title: 'Nhận diện khuôn mặt',
      configKeys: [
        ConfigKeys.minFaceQualityForDetection,
        ConfigKeys.minFaceQualityForRecognition,
        ConfigKeys.recognitionThrottleDuration,
        ConfigKeys.frameSkipCount,
      ],
      onChanged: onChanged,
    );
  }

  static Widget networkConfig({VoidCallback? onChanged}) {
    return QuickConfigWidget(
      title: 'Mạng',
      configKeys: [
        ConfigKeys.baseApiUrl,
        ConfigKeys.requestTimeout,
        ConfigKeys.maxRetryAttempts,
        ConfigKeys.retryDelay,
      ],
      onChanged: onChanged,
    );
  }

  static Widget performanceConfig({VoidCallback? onChanged}) {
    return QuickConfigWidget(
      title: 'Hiệu suất',
      configKeys: [
        ConfigKeys.normalFrameRate,
        ConfigKeys.optimizedFrameRate,
        ConfigKeys.powerSavingDelay,
      ],
      onChanged: onChanged,
    );
  }

  static Widget uiConfig({VoidCallback? onChanged}) {
    return QuickConfigWidget(
      title: 'Giao diện',
      configKeys: [
        ConfigKeys.primaryColor,
        ConfigKeys.successColor,
        ConfigKeys.avatarSize,
        ConfigKeys.animationDuration,
      ],
      onChanged: onChanged,
    );
  }

  static Widget debugConfig({VoidCallback? onChanged}) {
    return QuickConfigWidget(
      title: 'Debug',
      configKeys: [
        ConfigKeys.enableFaceDetectionLogs,
        ConfigKeys.enablePerformanceMonitoring,
      ],
      onChanged: onChanged,
    );
  }
}

/// Configuration dashboard with multiple quick config widgets
class ConfigDashboard extends StatelessWidget {
  final VoidCallback? onConfigChanged;

  const ConfigDashboard({
    super.key,
    this.onConfigChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to full admin config screen
              Navigator.of(context).pushNamed('/admin-config');
            },
            tooltip: 'Full Configuration',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            QuickConfigPresets.faceDetectionConfig(onChanged: onConfigChanged),
            QuickConfigPresets.networkConfig(onChanged: onConfigChanged),
            QuickConfigPresets.performanceConfig(onChanged: onConfigChanged),
            QuickConfigPresets.uiConfig(onChanged: onConfigChanged),
            QuickConfigPresets.debugConfig(onChanged: onConfigChanged),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        try {
                          await ConfigurationManager.instance.reload();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: const Text('🔄 Đã tải lại cấu hình'),
                                backgroundColor: Theme.of(context).colorScheme.primary,
                              ),
                            );
                          }
                          onConfigChanged?.call();
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('❌ Lỗi khi tải lại: $e'),
                                backgroundColor: Theme.of(context).colorScheme.error,
                              ),
                            );
                          }
                        }
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Reload Config'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        final errors = ConfigurationManager.instance.validateConfiguration();
                        final message = errors.isEmpty
                            ? '✅ Cấu hình hợp lệ'
                            : '⚠️ Tìm thấy ${errors.length} lỗi validation';
                        
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(message),
                            backgroundColor: errors.isEmpty ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.tertiary,
                          ),
                        );
                      },
                      icon: const Icon(Icons.check_circle),
                      label: const Text('Validate'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:io';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses MQTT communication.
/// 
/// This controller publishes "ON" and "OFF" messages to specified MQTT topics
/// to control relay states.
/// 
/// Example usage:
/// ```dart
/// final controller = MqttRelayController(
///   brokerHost: 'mqtt.example.com',
///   brokerPort: 1883,
///   topic: 'home/relay/control',
/// );
/// 
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class MqttRelayController extends RelayController {
  /// MQTT broker hostname or IP address.
  final String brokerHost;

  /// MQTT broker port.
  final int brokerPort;

  /// MQTT topic to publish commands to.
  final String topic;

  /// MQTT client identifier.
  final String clientId;

  /// Username for MQTT authentication (optional).
  final String? username;

  /// Password for MQTT authentication (optional).
  final String? password;

  /// Message to publish for turning relay ON.
  final String onMessage;

  /// Message to publish for turning relay OFF.
  final String offMessage;

  /// Quality of Service level for MQTT messages.
  final MqttQos qos;

  /// Whether to retain published messages.
  final bool retain;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  /// Whether to use secure connection (TLS/SSL).
  final bool useSecureConnection;

  MqttServerClient? _client;
  bool _isConnected = false;

  /// Creates a new [MqttRelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [brokerHost] is the MQTT broker hostname or IP address.
  /// [brokerPort] is the MQTT broker port (default: 1883 for non-secure, 8883 for secure).
  /// [topic] is the MQTT topic to publish commands to.
  /// [deviceName] is the device name/description.
  /// [clientId] is the MQTT client identifier (default: auto-generated).
  /// [username] is the username for authentication (optional).
  /// [password] is the password for authentication (optional).
  /// [onMessage] is the message to publish for turning relay ON (default: "ON").
  /// [offMessage] is the message to publish for turning relay OFF (default: "OFF").
  /// [qos] is the Quality of Service level (default: MqttQos.atLeastOnce).
  /// [retain] whether to retain published messages (default: false).
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  /// [useSecureConnection] whether to use TLS/SSL (default: false).
  MqttRelayController({
    required super.deviceId,
    required this.brokerHost,
    int? brokerPort,
    required this.topic,
    super.deviceName = 'MQTT Relay',
    String? clientId,
    this.username,
    this.password,
    this.onMessage = 'ON',
    this.offMessage = 'OFF',
    this.qos = MqttQos.atLeastOnce,
    this.retain = false,
    this.timeoutSeconds = 10,
    this.useSecureConnection = false,
  })  : brokerPort = brokerPort ?? (useSecureConnection ? 8883 : 1883),
        clientId = clientId ?? 'relay_controller_${DateTime.now().millisecondsSinceEpoch}';

  /// Connects to the MQTT broker.
  /// 
  /// This method must be called before using [triggerOn] or [triggerOff].
  /// 
  /// Throws [MqttRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      _client = MqttServerClient.withPort(brokerHost, clientId, brokerPort);
      
      // Configure client
      _client!.logging(on: false);
      _client!.onConnected = _onConnected;
      _client!.onDisconnected = _onDisconnected;
      _client!.onUnsubscribed = _onUnsubscribed;
      _client!.onSubscribed = _onSubscribed;
      _client!.onSubscribeFail = _onSubscribeFail;
      _client!.pongCallback = _onPong;
      _client!.keepAlivePeriod = 20;
      _client!.connectTimeoutPeriod = timeoutSeconds * 1000;

      // Configure secure connection if needed
      if (useSecureConnection) {
        _client!.secure = true;
        _client!.securityContext = SecurityContext.defaultContext;
      }

      // Set up connection message
      final connMessage = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);

      // Add authentication if provided
      if (username != null && password != null) {
        connMessage.authenticateAs(username!, password!);
      }

      _client!.connectionMessage = connMessage;

      // Connect to broker
      await _client!.connect();

      if (_client!.connectionStatus!.state != MqttConnectionState.connected) {
        throw MqttRelayException(
          'Failed to connect to MQTT broker: ${_client!.connectionStatus!.returnCode}',
        );
      }

      _isConnected = true;
    } catch (e) {
      if (e is MqttRelayException) {
        rethrow;
      }
      throw MqttRelayException('Failed to connect to MQTT broker', e);
    }
  }

  /// Disconnects from the MQTT broker.
  Future<void> disconnect() async {
    try {
      if (_client != null && _isConnected) {
        _client!.disconnect();
        _isConnected = false;
      }
    } catch (e) {
      throw MqttRelayException('Failed to disconnect from MQTT broker', e);
    }
  }

  /// Publishes a message to the MQTT topic.
  Future<void> _publishMessage(String message) async {
    try {
      if (!_isConnected || _client == null) {
        throw const MqttRelayException('Not connected to MQTT broker');
      }

      final builder = MqttClientPayloadBuilder();
      builder.addString(message);

      _client!.publishMessage(topic, qos, builder.payload!, retain: retain);
    } catch (e) {
      if (e is MqttRelayException) {
        rethrow;
      }
      throw MqttRelayException('Failed to publish message: $message', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _publishMessage(onMessage);
  }

  @override
  Future<void> triggerOff() async {
    await _publishMessage(offMessage);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets the current connection state.
  MqttConnectionState? get connectionState => _client?.connectionStatus?.state;

  // Event handlers
  void _onConnected() {
    _isConnected = true;
  }

  void _onDisconnected() {
    _isConnected = false;
  }

  void _onSubscribed(String topic) {
    // Handle subscription events if needed
  }

  void _onSubscribeFail(String topic) {
    // Handle subscription failures if needed
  }

  void _onUnsubscribed(String? topic) {
    // Handle unsubscription events if needed
  }

  void _onPong() {
    // Handle ping responses if needed
  }
}

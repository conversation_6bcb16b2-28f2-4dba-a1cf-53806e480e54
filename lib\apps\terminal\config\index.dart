/// Terminal Configuration Index
/// 
/// This file exports all terminal-specific configuration components.
/// It provides easy access to terminal app configuration, kiosk settings,
/// hardware configuration, and utilities specific to the terminal application.
/// 
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/terminal/config/index.dart';
/// 
/// // Initialize terminal configuration
/// AppConfigFactory.initialize(AppType.terminal);
/// 
/// // Get terminal configuration
/// final config = AppConfigFactory.current as TerminalAppConfig;
/// 
/// // Access terminal-specific settings
/// final kioskMode = config.enableKioskMode;
/// final hardwareMonitoring = config.enableHardwareMonitoring;
/// final uiConfig = config.uiConfig;
/// ```
library;

// ============================================================================
// TERMINAL CONFIGURATION
// ============================================================================
export 'terminal_app_config.dart';

// ============================================================================
// SHARED CONFIGURATION (RE-EXPORT FOR CONVENIENCE)
// ============================================================================
export '../../../shared/core/config/base_app_config.dart';
export '../../../shared/core/config/app_config_factory.dart';

// ============================================================================
// TERMINAL CONFIGURATION UTILITIES
// ============================================================================

// Import required classes for utility functions
import 'terminal_app_config.dart';
import '../../../shared/core/config/app_config_factory.dart';
import '../../../shared/core/config/base_app_config.dart';

/// Quick setup function for terminal configuration
void setupTerminalConfig() {
  AppConfigFactory.initialize(AppType.terminal);
}

/// Get terminal configuration instance
TerminalAppConfig getTerminalConfig() {
  final config = AppConfigFactory.current;
  if (config is! TerminalAppConfig) {
    throw StateError('Current configuration is not TerminalAppConfig');
  }
  return config;
}

/// Validate terminal configuration
bool validateTerminalConfig() {
  try {
    final config = getTerminalConfig();
    return config.validateConfig();
  } catch (e) {
    return false;
  }
}

/// Get terminal configuration summary
Map<String, dynamic> getTerminalConfigSummary() {
  try {
    final config = getTerminalConfig();
    return {
      'appName': config.appName,
      'appVersion': config.appVersion,
      'appType': config.appType.name,
      'environment': config.environment.name,
      'isValid': config.validateConfig(),
      'kioskMode': config.enableKioskMode,
      'hardwareMonitoring': config.enableHardwareMonitoring,
      'features': config.featureFlags.toMap(),
      'ui': config.uiConfig.toMap(),
      'camera': config.cameraConfig.toMap(),
    };
  } catch (e) {
    return {'error': e.toString()};
  }
}

/// Check if terminal configuration is initialized
bool isTerminalConfigInitialized() {
  return AppConfigFactory.isInitialized && 
         AppConfigFactory.currentAppType == AppType.terminal;
}

/// Reset terminal configuration (useful for testing)
void resetTerminalConfig() {
  AppConfigFactory.reset();
}

/// Switch to terminal configuration from another app type
void switchToTerminalConfig() {
  AppConfigFactory.switchToAppType(AppType.terminal);
}

/// Get kiosk mode configuration
Map<String, dynamic> getKioskModeConfig() {
  try {
    final config = getTerminalConfig();
    return {
      'enableKioskMode': config.enableKioskMode,
      'kioskTimeout': config.kioskTimeout.inMinutes,
      'enableAutoRestart': config.enableAutoRestart,
      'autoRestartInterval': config.autoRestartInterval.inHours,
      'enableScreensaver': config.enableScreensaver,
      'screensaverTimeout': config.screensaverTimeout.inMinutes,
      'disableSystemUI': config.disableSystemUI,
      'preventAppSwitching': config.preventAppSwitching,
    };
  } catch (e) {
    return {'error': e.toString()};
  }
}

/// Get hardware monitoring configuration
Map<String, dynamic> getHardwareMonitoringConfig() {
  try {
    final config = getTerminalConfig();
    return {
      'enableHardwareMonitoring': config.enableHardwareMonitoring,
      'hardwareCheckInterval': config.hardwareCheckInterval.inMinutes,
      'enableTemperatureMonitoring': config.enableTemperatureMonitoring,
      'temperatureWarningThreshold': config.temperatureWarningThreshold,
      'temperatureCriticalThreshold': config.temperatureCriticalThreshold,
      'enableMemoryMonitoring': config.enableMemoryMonitoring,
      'memoryWarningThreshold': config.memoryWarningThreshold,
      'memoryCriticalThreshold': config.memoryCriticalThreshold,
      'enableStorageMonitoring': config.enableStorageMonitoring,
      'storageWarningThreshold': config.storageWarningThreshold,
      'storageCriticalThreshold': config.storageCriticalThreshold,
    };
  } catch (e) {
    return {'error': e.toString()};
  }
}

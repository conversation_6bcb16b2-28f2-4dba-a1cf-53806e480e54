/// Configuration Status Widget
/// 
/// Displays the current status of the configuration system including
/// active providers, validation status, and recent changes.

import 'package:flutter/material.dart';
import 'dart:async';
import '../configuration_manager.dart';
import '../config_initializer.dart';
import '../flexible_config_system.dart';
import '../config_parameters_registry.dart';

class ConfigStatusWidget extends StatefulWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const ConfigStatusWidget({
    super.key,
    this.showDetails = false,
    this.onTap,
  });

  @override
  State<ConfigStatusWidget> createState() => _ConfigStatusWidgetState();
}

class _ConfigStatusWidgetState extends State<ConfigStatusWidget> {
  Map<String, dynamic> _stats = {};
  List<String> _validationErrors = [];
  List<ConfigChangeEvent> _recentChanges = [];
  StreamSubscription<ConfigChangeEvent>? _changeSubscription;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatus();
    _listenToChanges();
  }

  void _loadStatus() {
    setState(() => _isLoading = true);

    try {
      final manager = ConfigurationManager.instance;
      final registry = ConfigParametersRegistry.getAllParameters();

      _stats = {
        'initialized': manager.isInitialized,
        'total_parameters': registry.length,
        'active_providers': manager.activeProviders.length,
        'categories': _getCategoriesStats(registry),
        'validation_errors': 0,
      };

      _validationErrors = manager.validateConfiguration();
      _stats['validation_errors'] = _validationErrors.length;
    } catch (e) {
      _stats = {'error': e.toString(), 'initialized': false};
      _validationErrors = ['Failed to load status: $e'];
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Map<String, int> _getCategoriesStats(Map<String, ConfigParameter> parameters) {
    final categories = <String, int>{};
    for (final parameter in parameters.values) {
      categories[parameter.category] = (categories[parameter.category] ?? 0) + 1;
    }
    return categories;
  }

  void _listenToChanges() {
    try {
      final manager = ConfigurationManager.instance;
      if (!manager.isInitialized) return;

      _changeSubscription = manager.changeStream.listen((event) {
        setState(() {
          _recentChanges.insert(0, event);
          if (_recentChanges.length > 10) {
            _recentChanges.removeLast();
          }
        });
      });
    } catch (e) {
      // Ignore if change stream is not available
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Loading configuration status...'),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Card(
        color: _getStatusColor(),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              if (widget.showDetails) ...[
                const SizedBox(height: 12),
                _buildDetails(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final isHealthy = _validationErrors.isEmpty && _stats['initialized'] == true;
    
    return Row(
      children: [
        Icon(
          isHealthy ? Icons.check_circle : Icons.warning,
          color: isHealthy ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.tertiary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Configuration System',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (_stats['initialized'] == true) ...[
                      Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Active',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatsSection(),
        if (_validationErrors.isNotEmpty) ...[
          const SizedBox(height: 12),
          _buildValidationSection(),
        ],
        if (_recentChanges.isNotEmpty) ...[
          const SizedBox(height: 12),
          _buildRecentChangesSection(),
        ],
      ],
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statistics',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: [
            _buildStatChip('Parameters', _stats['total_parameters']?.toString() ?? '0'),
            _buildStatChip('Providers', _stats['active_providers']?.toString() ?? '0'),
            _buildStatChip('Categories', _stats['categories']?.length?.toString() ?? '0'),
            if (_stats['validation_errors'] != null)
              _buildStatChip(
                'Errors',
                _stats['validation_errors'].toString(),
                color: _stats['validation_errors'] > 0 ? Colors.red : Colors.green,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatChip(String label, String value, {Color? color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: (color ?? Colors.blue).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: (color ?? Colors.blue).withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color ?? Colors.blue.shade700,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color ?? Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Validation Issues',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.red.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _validationErrors.take(3).map((error) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.error_outline, size: 16, color: Colors.red.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      error,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
        if (_validationErrors.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              '... and ${_validationErrors.length - 3} more issues',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRecentChangesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Changes',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: _recentChanges.take(3).map((change) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16, color: Colors.blue.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          change.key,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                        Text(
                          '${change.oldValue} → ${change.newValue}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.blue.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    _formatTime(change.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.blue.shade500,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    if (_stats['initialized'] != true) {
      return Colors.red.shade50;
    } else if (_validationErrors.isNotEmpty) {
      return Colors.orange.shade50;
    } else {
      return Colors.green.shade50;
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);
    
    if (diff.inMinutes < 1) {
      return 'now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}h ago';
    } else {
      return '${diff.inDays}d ago';
    }
  }

  @override
  void dispose() {
    _changeSubscription?.cancel();
    super.dispose();
  }
}

/// Compact configuration status indicator for app bars or status bars
class ConfigStatusIndicator extends StatelessWidget {
  final VoidCallback? onTap;

  const ConfigStatusIndicator({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    final stats = ConfigInitializer.getStatistics();
    final errors = ConfigInitializer.validate();
    final isHealthy = errors.isEmpty && stats['initialized'] == true;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isHealthy ? Colors.green.shade100 : Colors.orange.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isHealthy ? Icons.check_circle : Icons.warning,
              size: 16,
              color: isHealthy ? Colors.green.shade700 : Colors.orange.shade700,
            ),
            const SizedBox(width: 4),
            Text(
              'Config',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isHealthy ? Colors.green.shade700 : Colors.orange.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

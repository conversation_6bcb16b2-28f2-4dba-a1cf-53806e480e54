import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing device settings and keeping the app always active
class DeviceManagementService {
  static DeviceManagementService? _instance;
  static DeviceManagementService get instance => _instance ??= DeviceManagementService._();
  
  DeviceManagementService._();
  
  static const MethodChannel _channel = MethodChannel('com.ccam.terminal/device_management');
  
  bool _isInitialized = false;
  bool _isWakeLockEnabled = false;
  double _currentBrightness = 0.5;
  double _savedBrightness = 0.5;
  Timer? _keepAliveTimer;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isWakeLockEnabled => _isWakeLockEnabled;
  double get currentBrightness => _currentBrightness;
  
  /// Initialize device management service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🔧 Initializing Device Management Service...');
      }
      
      // Load saved settings
      await _loadSettings();
      
      // Enable wake lock to keep screen on
      await enableWakeLock();
      
      // Set initial brightness
      await _restoreBrightness();
      
      // Request battery optimization exemption (optional)
      try {
        await _requestBatteryOptimizationExemption();
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Battery optimization exemption failed (optional): $e');
        }
      }
      
      // Start keep-alive mechanisms (handled by native Android service)
      await _startKeepAliveService();
      
      // Setup periodic keep-alive checks
      _setupKeepAliveTimer();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Device Management Service initialized');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Device Management Service: $e');
      }
      rethrow;
    }
  }
  
  /// Enable wake lock to keep screen on
  Future<void> enableWakeLock() async {
    try {
      if (!_isWakeLockEnabled) {
        await WakelockPlus.enable();
        _isWakeLockEnabled = true;
        
        if (kDebugMode) {
          print('🔒 Wake lock enabled - screen will stay on');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to enable wake lock: $e');
      }
    }
  }
  
  /// Disable wake lock
  Future<void> disableWakeLock() async {
    try {
      if (_isWakeLockEnabled) {
        await WakelockPlus.disable();
        _isWakeLockEnabled = false;
        
        if (kDebugMode) {
          print('🔓 Wake lock disabled');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to disable wake lock: $e');
      }
    }
  }
  
  /// Set screen brightness (0.0 to 1.0)
  Future<void> setBrightness(double brightness) async {
    try {
      brightness = brightness.clamp(0.0, 1.0);

      // Use application brightness (no permission required)
      await ScreenBrightness.instance.setApplicationScreenBrightness(brightness);
      _currentBrightness = brightness;

      // Save brightness setting
      await _saveBrightness(brightness);

      if (kDebugMode) {
        print('💡 Screen brightness set to ${(brightness * 100).toInt()}%');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set brightness: $e');
      }
    }
  }
  
  /// Get current screen brightness
  Future<double> getBrightness() async {
    try {
      final brightness = await ScreenBrightness.instance.application;
      _currentBrightness = brightness;
      return brightness;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get brightness: $e');
      }
      return _currentBrightness;
    }
  }

  /// Reset brightness to system default
  Future<void> resetBrightness() async {
    try {
      await ScreenBrightness.instance.resetApplicationScreenBrightness();
      _currentBrightness = await getBrightness();

      if (kDebugMode) {
        print('🔄 Screen brightness reset to system default');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to reset brightness: $e');
      }
    }
  }
  
  /// Request battery optimization exemption using native Android intent
  Future<void> _requestBatteryOptimizationExemption() async {
    try {
      if (Platform.isAndroid) {
        // Use method channel to check and request battery optimization exemption
        final result = await _channel.invokeMethod('requestBatteryOptimizationExemption');

        if (result == true) {
          if (kDebugMode) {
            print('🔋 Battery optimization exemption requested');
          }
        } else {
          if (kDebugMode) {
            print('✅ Battery optimization already disabled or request failed');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to request battery optimization exemption: $e');
      }
    }
  }
  
  /// Start native keep-alive service
  Future<void> _startKeepAliveService() async {
    try {
      if (Platform.isAndroid) {
        await _channel.invokeMethod('startKeepAliveService');
        
        if (kDebugMode) {
          print('🚀 Keep-alive service started');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to start keep-alive service: $e');
      }
    }
  }
  
  /// Setup periodic keep-alive timer
  void _setupKeepAliveTimer() {
    _keepAliveTimer?.cancel();
    
    _keepAliveTimer = Timer.periodic(const Duration(minutes: 5), (_) async {
      try {
        // Refresh wake lock
        if (_isWakeLockEnabled) {
          await WakelockPlus.disable();
          await WakelockPlus.enable();
        }
        
        // Ping keep-alive service
        if (Platform.isAndroid) {
          await _channel.invokeMethod('pingKeepAlive');
        }
        
        if (kDebugMode) {
          print('💓 Keep-alive ping sent');
        }
        
      } catch (e) {
        if (kDebugMode) {
          print('❌ Keep-alive ping failed: $e');
        }
      }
    });
  }
  
  /// Load saved settings
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _savedBrightness = prefs.getDouble('screen_brightness') ?? 0.5;
      
      if (kDebugMode) {
        print('📱 Loaded saved brightness: ${(_savedBrightness * 100).toInt()}%');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load settings: $e');
      }
    }
  }
  
  /// Save brightness setting
  Future<void> _saveBrightness(double brightness) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('screen_brightness', brightness);
      _savedBrightness = brightness;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to save brightness: $e');
      }
    }
  }
  
  /// Restore saved brightness
  Future<void> _restoreBrightness() async {
    try {
      await setBrightness(_savedBrightness);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to restore brightness: $e');
      }
    }
  }
  
  /// Get device information
  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdk': androidInfo.version.sdkInt,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        };
      }
      
      return {'platform': 'Unknown'};
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get device info: $e');
      }
      return {'platform': 'Error'};
    }
  }
  
  /// Dispose service
  Future<void> dispose() async {
    _keepAliveTimer?.cancel();
    
    try {
      await disableWakeLock();
      
      if (Platform.isAndroid) {
        await _channel.invokeMethod('stopKeepAliveService');
      }
      
      _isInitialized = false;
      
      if (kDebugMode) {
        print('🔧 Device Management Service disposed');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to dispose Device Management Service: $e');
      }
    }
  }
}

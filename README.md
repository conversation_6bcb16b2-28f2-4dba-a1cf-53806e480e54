# C-Face Terminal - Multi-App Project

Dự án Flutter multi-app với kiến trúc Clean Architecture, hỗ trợ cả ứng dụng mobile và terminal.

## 🏗️ Cấu Trúc Multi-App

Dự án sử dụng **Multi-App Architecture** với Clean Architecture principles:

```
lib/
├── apps/
│   ├── mobile/              # 📱 Ứng dụng mobile
│   │   ├── presentation/    # Mobile UI components
│   │   ├── routes/          # Mobile navigation
│   │   ├── config/          # Mobile configuration
│   │   └── main_mobile.dart # Mobile entry point
│   └── terminal/            # 🖥️ Ứng dụng terminal/kiosk
│       ├── presentation/    # Terminal UI components
│       ├── routes/          # Terminal navigation
│       ├── config/          # Terminal configuration
│       └── main_terminal.dart # Terminal entry point
└── shared/                  # 🔄 Code chia sẻ (70%+)
    ├── core/                # Infrastructure & utilities
    │   ├── config/          # Shared configurations
    │   ├── constants/       # Constants & themes
    │   ├── di/              # Dependency injection
    │   ├── errors/          # Error handling
    │   ├── network/         # Network layer
    │   ├── storage/         # Storage services
    │   └── utils/           # Utilities & extensions
    ├── data/                # External data sources
    │   ├── data_sources/    # API & local data sources
    │   ├── models/          # Data models
    │   └── repositories/    # Repository implementations
    ├── domain/              # Business logic (pure Dart)
    │   ├── entities/        # Business entities
    │   ├── repositories/    # Repository interfaces
    │   └── use_cases/       # Business use cases
    └── presentation/        # Shared UI components
        ├── widgets/         # Shared widgets
        ├── themes/          # Shared themes
        └── providers/       # Base providers
```

## 📋 Migration Documentation

**🚨 QUAN TRỌNG**: Dự án này đang trong giai đoạn migration từ single-app sang multi-app structure.

Tất cả tài liệu migration được tổ chức trong folder **`doc/migration/`**:

| File | Mục Đích | Đối Tượng |
|:-----|:---------|:----------|
| **[📊 MIGRATION_OVERVIEW.md](doc/migration/MIGRATION_OVERVIEW.md)** | Tóm tắt tổng quan migration | PM, Stakeholders |
| **[📝 MIGRATION_PLAN.md](doc/migration/MIGRATION_PLAN.md)** | Kế hoạch chi tiết 35 tasks | Development Team |
| **[✅ MIGRATION_CHECKLIST.md](doc/migration/MIGRATION_CHECKLIST.md)** | Checklist thực hiện từng bước | Developers, QA |
| **[🛠️ MIGRATION_SCRIPTS.md](doc/migration/MIGRATION_SCRIPTS.md)** | Scripts và templates hỗ trợ | Developers, DevOps |

## 🚀 Quick Start Migration

### 1. Đọc Migration Overview
```bash
cat doc/migration/MIGRATION_OVERVIEW.md
```

### 2. Bắt Đầu Migration Process
```bash
# Review kế hoạch chi tiết
cat doc/migration/MIGRATION_PLAN.md

# Follow checklist từng bước
cat doc/migration/MIGRATION_CHECKLIST.md
```

### 3. Sử Dụng Migration Scripts
```bash
# Xem scripts hỗ trợ
cat doc/migration/MIGRATION_SCRIPTS.md
```

## 📊 Migration Status

| Metric | Target | Current | Status |
|:-------|:-------|:--------|:-------|
| **Migration Progress** | 100% | 0% | 🔴 Pending |
| **Total Tasks** | 35 | 0 completed | 🔴 Not Started |
| **Estimated Time** | 143 hours | 0 hours | 🔴 Not Started |
| **Code Reuse Target** | >70% | TBD | 🔴 TBD |

## 📱 Target Applications

### Mobile App (`lib/apps/mobile/`)
- **Target**: Smartphones và tablets
- **Features**: Full feature set với mobile UX
- **Navigation**: Bottom navigation, drawer, responsive design
- **Entry Point**: `main_mobile.dart`

### Terminal App (`lib/apps/terminal/`)
- **Target**: Kiosk terminals và fixed displays
- **Features**: Simplified UI, auto-timeout, admin access
- **Navigation**: Linear flow, large touch-friendly buttons
- **Entry Point**: `main_terminal.dart`

## 🎯 Migration Goals

1. **Multi-App Support**: Mobile + Terminal applications
2. **Maximum Code Reuse**: >70% shared business logic
3. **Clean Architecture**: Maintain architectural principles
4. **Zero Downtime**: No disruption to current functionality
5. **Scalability**: Easy to add new app types in future
/// Runtime Configuration Provider
/// 
/// Manages runtime configuration changes with in-memory storage,
/// persistence support, and validation.

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../flexible_config_system.dart';
import '../config_parameters_registry.dart';

/// Runtime configuration provider for dynamic changes
class RuntimeConfigProvider implements ConfigProvider {
  final bool enablePersistence;
  final String persistenceKey;
  
  Map<String, dynamic> _currentConfig = {};
  final StreamController<Map<String, dynamic>> _configController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  SharedPreferences? _prefs;
  bool _isInitialized = false;

  RuntimeConfigProvider({
    this.enablePersistence = true,
    this.persistenceKey = 'runtime_config',
  });

  @override
  String get name => 'RuntimeConfigProvider';

  @override
  ConfigSource get source => ConfigSource.runtime;

  @override
  Stream<Map<String, dynamic>> get configStream => _configController.stream;

  @override
  Future<void> initialize() async {
    if (enablePersistence) {
      _prefs = await SharedPreferences.getInstance();
      await _loadPersistedConfiguration();
    }
    
    _isInitialized = true;
    debugPrint('RuntimeConfigProvider initialized');
  }

  @override
  Future<Map<String, dynamic>> loadConfiguration() async {
    if (enablePersistence && _prefs != null) {
      await _loadPersistedConfiguration();
    }
    return Map<String, dynamic>.from(_currentConfig);
  }

  @override
  Future<void> saveConfiguration(Map<String, dynamic> config) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    // Validate configuration before saving
    final validatedConfig = _validateConfiguration(config);
    
    _currentConfig = validatedConfig;
    _configController.add(_currentConfig);
    
    // Persist if enabled
    if (enablePersistence && _prefs != null) {
      await _persistConfiguration();
    }
    
    debugPrint('Runtime configuration saved: ${validatedConfig.keys.length} parameters');
  }

  @override
  Future<void> dispose() async {
    await _configController.close();
    _isInitialized = false;
  }

  /// Load persisted configuration from SharedPreferences
  Future<void> _loadPersistedConfiguration() async {
    if (_prefs == null) return;

    try {
      final configJson = _prefs!.getString(persistenceKey);
      if (configJson != null && configJson.isNotEmpty) {
        final config = jsonDecode(configJson) as Map<String, dynamic>;
        final validatedConfig = _validateConfiguration(config);
        
        if (validatedConfig.isNotEmpty) {
          _currentConfig = validatedConfig;
          _configController.add(_currentConfig);
          debugPrint('Persisted runtime configuration loaded: ${validatedConfig.keys.length} parameters');
        }
      }
    } catch (e) {
      debugPrint('Error loading persisted configuration: $e');
      _currentConfig = {};
    }
  }

  /// Persist configuration to SharedPreferences
  Future<void> _persistConfiguration() async {
    if (_prefs == null) return;

    try {
      final configJson = jsonEncode(_currentConfig);
      await _prefs!.setString(persistenceKey, configJson);
      debugPrint('Runtime configuration persisted');
    } catch (e) {
      debugPrint('Error persisting configuration: $e');
    }
  }

  /// Validate configuration against registered parameters
  Map<String, dynamic> _validateConfiguration(Map<String, dynamic> config) {
    final validatedConfig = <String, dynamic>{};
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final entry in config.entries) {
      final parameter = parameters[entry.key];
      
      if (parameter == null) {
        debugPrint('Unknown configuration parameter: ${entry.key}');
        continue;
      }
      
      try {
        final parsedValue = parameter.parseValue(entry.value);
        
        if (parameter.isValid(parsedValue)) {
          validatedConfig[entry.key] = parsedValue;
        } else {
          debugPrint('Invalid value for ${entry.key}: ${entry.value}');
        }
      } catch (e) {
        debugPrint('Error parsing value for ${entry.key}: $e');
      }
    }
    
    return validatedConfig;
  }

  /// Set a single configuration value
  Future<void> setValue<T>(String key, T value) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    final parameter = ConfigParametersRegistry.getParameter(key);
    if (parameter == null) {
      throw ArgumentError('Unknown configuration parameter: $key');
    }

    if (!parameter.isValid(value)) {
      throw ArgumentError('Invalid value for $key: $value');
    }

    final newConfig = Map<String, dynamic>.from(_currentConfig);
    newConfig[key] = value;
    
    await saveConfiguration(newConfig);
  }

  /// Get a single configuration value
  T? getValue<T>(String key) {
    final value = _currentConfig[key];
    return value is T ? value : null;
  }

  /// Remove a configuration value
  Future<void> removeValue(String key) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    if (_currentConfig.containsKey(key)) {
      final newConfig = Map<String, dynamic>.from(_currentConfig);
      newConfig.remove(key);
      await saveConfiguration(newConfig);
    }
  }

  /// Clear all runtime configuration
  Future<void> clearConfiguration() async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    await saveConfiguration({});
    
    if (enablePersistence && _prefs != null) {
      await _prefs!.remove(persistenceKey);
    }
    
    debugPrint('Runtime configuration cleared');
  }

  /// Get configuration values by category
  Map<String, dynamic> getValuesByCategory(String category) {
    final parameters = ConfigParametersRegistry.getParametersByCategory(category);
    final categoryValues = <String, dynamic>{};
    
    for (final parameter in parameters.values) {
      final value = _currentConfig[parameter.key];
      if (value != null) {
        categoryValues[parameter.key] = value;
      }
    }
    
    return categoryValues;
  }

  /// Set multiple configuration values by category
  Future<void> setValuesByCategory(String category, Map<String, dynamic> values) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    final parameters = ConfigParametersRegistry.getParametersByCategory(category);
    final newConfig = Map<String, dynamic>.from(_currentConfig);
    
    for (final entry in values.entries) {
      final parameter = parameters[entry.key];
      if (parameter != null && parameter.isValid(entry.value)) {
        newConfig[entry.key] = entry.value;
      } else {
        debugPrint('Invalid value for ${entry.key}: ${entry.value}');
      }
    }
    
    await saveConfiguration(newConfig);
  }

  /// Check if a configuration value exists
  bool hasValue(String key) {
    return _currentConfig.containsKey(key);
  }

  /// Get all configuration keys
  Set<String> getAllKeys() {
    return _currentConfig.keys.toSet();
  }

  /// Get configuration size
  int get configurationSize => _currentConfig.length;

  /// Check if configuration is empty
  bool get isEmpty => _currentConfig.isEmpty;

  /// Get configuration as JSON string
  String toJson() {
    return jsonEncode(_currentConfig);
  }

  /// Load configuration from JSON string
  Future<void> fromJson(String jsonString) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    try {
      final config = jsonDecode(jsonString) as Map<String, dynamic>;
      await saveConfiguration(config);
    } catch (e) {
      debugPrint('Error loading configuration from JSON: $e');
      rethrow;
    }
  }

  /// Create a backup of current configuration
  Map<String, dynamic> createBackup() {
    return Map<String, dynamic>.from(_currentConfig);
  }

  /// Restore configuration from backup
  Future<void> restoreFromBackup(Map<String, dynamic> backup) async {
    if (!_isInitialized) {
      throw StateError('RuntimeConfigProvider not initialized');
    }

    await saveConfiguration(backup);
    debugPrint('Configuration restored from backup');
  }

  /// Get configuration statistics
  Map<String, dynamic> getStatistics() {
    final parameters = ConfigParametersRegistry.getAllParameters();
    final categories = <String, int>{};
    
    for (final key in _currentConfig.keys) {
      final parameter = parameters[key];
      if (parameter != null) {
        categories[parameter.category] = (categories[parameter.category] ?? 0) + 1;
      }
    }
    
    return {
      'total_parameters': _currentConfig.length,
      'categories': categories,
      'persistence_enabled': enablePersistence,
      'is_initialized': _isInitialized,
    };
  }

  /// Validate current configuration
  List<String> validateCurrentConfiguration() {
    final errors = <String>[];
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final entry in _currentConfig.entries) {
      final parameter = parameters[entry.key];
      
      if (parameter == null) {
        errors.add('Unknown parameter: ${entry.key}');
        continue;
      }
      
      if (!parameter.isValid(entry.value)) {
        errors.add('Invalid value for ${entry.key}: ${entry.value}');
      }
    }
    
    return errors;
  }
}

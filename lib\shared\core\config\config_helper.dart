/// Configuration Helper
/// 
/// Provides convenient static methods for accessing configuration values
/// with type safety and default fallbacks.

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'flexible_config_system.dart';
import 'configuration_manager.dart';

/// Helper class for easy configuration access
class ConfigHelper {
  static ConfigurationManager get _manager {
    final manager = ConfigurationManager.instance;
    // Return manager even if not initialized - it will use default values
    return manager;
  }

  // ============================================================================
  // FACE DETECTION CONFIGURATION
  // ============================================================================

  /// Get minimum face quality for detection
  static double get minFaceQualityForDetection {
    return _manager.getValue<double>(
      ConfigKeys.minFaceQualityForDetection,
      defaultValue: 0.4,
    );
  }

  /// Get minimum face quality for recognition
  static double get minFaceQualityForRecognition {
    return _manager.getValue<double>(
      ConfigKeys.minFaceQualityForRecognition,
      defaultValue: 0.4,
    );
  }

  /// Get recognition throttle duration
  static Duration get recognitionThrottleDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.recognitionThrottleDuration,
      defaultValue: Duration(seconds: 8),
    );
  }

  /// Get frame skip count
  static int get frameSkipCount {
    return _manager.getValue<int>(
      ConfigKeys.frameSkipCount,
      defaultValue: 1,
    );
  }

  /// Get detection timeout
  static Duration get detectionTimeout {
    return _manager.getValue<Duration>(
      ConfigKeys.detectionTimeout,
      defaultValue: Duration(milliseconds: 80),
    );
  }

  /// Get minimum face quality for avatar capture
  static double get minFaceQualityForAvatarCapture {
    return _manager.getValue<double>(
      ConfigKeys.minFaceQualityForAvatarCapture,
      defaultValue: 0.4,
    );
  }

  /// Get significant quality change threshold
  static double get significantQualityChange {
    return _manager.getValue<double>(
      ConfigKeys.significantQualityChange,
      defaultValue: 0.1,
    );
  }

  /// Get user display timeout
  static Duration get userDisplayTimeout {
    return _manager.getValue<Duration>(
      ConfigKeys.userDisplayTimeout,
      defaultValue: Duration(seconds: 3),
    );
  }

  /// Get avatar capture throttle
  static Duration get avatarCaptureThrottle {
    return _manager.getValue<Duration>(
      ConfigKeys.avatarCaptureThrottle,
      defaultValue: Duration(seconds: 2),
    );
  }

  /// Get avatar display duration
  static Duration get avatarDisplayDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.avatarDisplayDuration,
      defaultValue: Duration(seconds: 10),
    );
  }

  /// Get avatar padding factor
  static double get avatarPaddingFactor {
    return _manager.getValue<double>(
      ConfigKeys.avatarPaddingFactor,
      defaultValue: 0.3,
    );
  }

  /// Get avatar target size
  static int get avatarTargetSize {
    return _manager.getValue<int>(
      ConfigKeys.avatarTargetSize,
      defaultValue: 200,
    );
  }

  /// Get avatar image quality
  static int get avatarImageQuality {
    return _manager.getValue<int>(
      ConfigKeys.avatarImageQuality,
      defaultValue: 85,
    );
  }

  /// Get recognition image quality
  static int get recognitionImageQuality {
    return _manager.getValue<int>(
      ConfigKeys.recognitionImageQuality,
      defaultValue: 85,
    );
  }

  /// Get max recognition retries
  static int get maxRecognitionRetries {
    return _manager.getValue<int>(
      ConfigKeys.maxRecognitionRetries,
      defaultValue: 3,
    );
  }

  /// Get recognition timeout
  static Duration get recognitionTimeout {
    return _manager.getValue<Duration>(
      ConfigKeys.recognitionTimeout,
      defaultValue: Duration(seconds: 10),
    );
  }

  /// Get max image size in bytes
  static int get maxImageSizeBytes {
    return _manager.getValue<int>(
      ConfigKeys.maxImageSizeBytes,
      defaultValue: 5 * 1024 * 1024,
    );
  }

  /// Get compression quality
  static int get compressionQuality {
    return _manager.getValue<int>(
      ConfigKeys.compressionQuality,
      defaultValue: 85,
    );
  }

  /// Get image format
  static String get imageFormat {
    return _manager.getValue<String>(
      ConfigKeys.imageFormat,
      defaultValue: 'jpeg',
    );
  }

  /// Get source identifier
  static String get sourceIdentifier {
    return _manager.getValue<String>(
      ConfigKeys.sourceIdentifier,
      defaultValue: 'terminal_camera_stream',
    );
  }

  // ============================================================================
  // NETWORK CONFIGURATION
  // ============================================================================

  /// Get base API URL
  static String get baseApiUrl {
    return _manager.getValue<String>(
      ConfigKeys.baseApiUrl,
      defaultValue: 'http://10.161.80.12:1081/api/v3.1',
    );
  }

  /// Get request timeout
  static Duration get requestTimeout {
    return _manager.getValue<Duration>(
      ConfigKeys.requestTimeout,
      defaultValue: Duration(seconds: 30),
    );
  }

  /// Get max retry attempts
  static int get maxRetryAttempts {
    return _manager.getValue<int>(
      ConfigKeys.maxRetryAttempts,
      defaultValue: 3,
    );
  }

  /// Get retry delay
  static Duration get retryDelay {
    return _manager.getValue<Duration>(
      ConfigKeys.retryDelay,
      defaultValue: Duration(seconds: 2),
    );
  }

  /// Get device ID
  static String get deviceId {
    return _manager.getValue<String>(
      ConfigKeys.deviceId,
      defaultValue: 'terminal_001',
    );
  }

  // ============================================================================
  // UI CONFIGURATION
  // ============================================================================

  /// Get primary color
  static Color get primaryColor {
    final colorValue = _manager.getValue<int>(
      ConfigKeys.primaryColor,
      defaultValue: 0xFF2196F3,
    );
    return Color(colorValue);
  }

  /// Get success color
  static Color get successColor {
    final colorValue = _manager.getValue<int>(
      ConfigKeys.successColor,
      defaultValue: 0xFF4CAF50,
    );
    return Color(colorValue);
  }

  /// Get error color
  static Color get errorColor {
    final colorValue = _manager.getValue<int>(
      ConfigKeys.errorColor,
      defaultValue: 0xFFF44336,
    );
    return Color(colorValue);
  }

  /// Get avatar size
  static double get avatarSize {
    return _manager.getValue<double>(
      ConfigKeys.avatarSize,
      defaultValue: 120.0,
    );
  }

  /// Get animation duration
  static Duration get animationDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.animationDuration,
      defaultValue: Duration(milliseconds: 300),
    );
  }

  /// Get warning color
  static Color get warningColor {
    final colorValue = _manager.getValue<int>(
      ConfigKeys.warningColor,
      defaultValue: 0xFFFF9800,
    );
    return Color(colorValue);
  }

  /// Get background color
  static Color get backgroundColor {
    final colorValue = _manager.getValue<int>(
      ConfigKeys.backgroundColor,
      defaultValue: 0xFF000000,
    );
    return Color(colorValue);
  }

  /// Get avatar border radius
  static double get avatarBorderRadius {
    return _manager.getValue<double>(
      ConfigKeys.avatarBorderRadius,
      defaultValue: 8.0,
    );
  }

  /// Get quality badge size
  static double get qualityBadgeSize {
    return _manager.getValue<double>(
      ConfigKeys.qualityBadgeSize,
      defaultValue: 24.0,
    );
  }

  /// Get guide frame stroke width
  static double get guideFrameStrokeWidth {
    return _manager.getValue<double>(
      ConfigKeys.guideFrameStrokeWidth,
      defaultValue: 2.0,
    );
  }

  /// Get guide frame corner radius
  static double get guideFrameCornerRadius {
    return _manager.getValue<double>(
      ConfigKeys.guideFrameCornerRadius,
      defaultValue: 12.0,
    );
  }

  /// Get guide frame margin
  static double get guideFrameMargin {
    return _manager.getValue<double>(
      ConfigKeys.guideFrameMargin,
      defaultValue: 50.0,
    );
  }

  /// Get progress indicator stroke width
  static double get progressIndicatorStrokeWidth {
    return _manager.getValue<double>(
      ConfigKeys.progressIndicatorStrokeWidth,
      defaultValue: 3.0,
    );
  }

  /// Get progress animation duration
  static Duration get progressAnimationDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.progressAnimationDuration,
      defaultValue: Duration(milliseconds: 300),
    );
  }

  /// Get quality indicator size
  static double get qualityIndicatorSize {
    return _manager.getValue<double>(
      ConfigKeys.qualityIndicatorSize,
      defaultValue: 24.0,
    );
  }

  /// Get quality indicator font size
  static double get qualityIndicatorFontSize {
    return _manager.getValue<double>(
      ConfigKeys.qualityIndicatorFontSize,
      defaultValue: 8.0,
    );
  }

  // ============================================================================
  // PERFORMANCE CONFIGURATION
  // ============================================================================

  /// Get normal frame rate
  static int get normalFrameRate {
    return _manager.getValue<int>(
      ConfigKeys.normalFrameRate,
      defaultValue: 30,
    );
  }

  /// Get optimized frame rate
  static int get optimizedFrameRate {
    return _manager.getValue<int>(
      ConfigKeys.optimizedFrameRate,
      defaultValue: 15,
    );
  }

  /// Get power saving delay
  static Duration get powerSavingDelay {
    return _manager.getValue<Duration>(
      ConfigKeys.powerSavingDelay,
      defaultValue: Duration(seconds: 30),
    );
  }

  /// Get extreme power saving frame rate
  static int get extremePowerSavingFrameRate {
    return _manager.getValue<int>(
      ConfigKeys.extremePowerSavingFrameRate,
      defaultValue: 5,
    );
  }

  /// Get resource optimization delay
  static Duration get resourceOptimizationDelay {
    return _manager.getValue<Duration>(
      ConfigKeys.resourceOptimizationDelay,
      defaultValue: Duration(seconds: 10),
    );
  }

  /// Get extreme power saving delay
  static Duration get extremePowerSavingDelay {
    return _manager.getValue<Duration>(
      ConfigKeys.extremePowerSavingDelay,
      defaultValue: Duration(seconds: 30),
    );
  }

  /// Get face absence for optimization
  static Duration get faceAbsenceForOptimization {
    return _manager.getValue<Duration>(
      ConfigKeys.faceAbsenceForOptimization,
      defaultValue: Duration(seconds: 10),
    );
  }

  /// Get face absence for extreme saving
  static Duration get faceAbsenceForExtremeSaving {
    return _manager.getValue<Duration>(
      ConfigKeys.faceAbsenceForExtremeSaving,
      defaultValue: Duration(seconds: 30),
    );
  }

  /// Get max brightness
  static double get maxBrightness {
    return _manager.getValue<double>(
      ConfigKeys.maxBrightness,
      defaultValue: 1.0,
    );
  }

  /// Get min brightness
  static double get minBrightness {
    return _manager.getValue<double>(
      ConfigKeys.minBrightness,
      defaultValue: 0.0,
    );
  }

  /// Get power saving brightness
  static double get powerSavingBrightness {
    return _manager.getValue<double>(
      ConfigKeys.powerSavingBrightness,
      defaultValue: 0.1,
    );
  }

  /// Get brightness transition duration
  static Duration get brightnessTransitionDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.brightnessTransitionDuration,
      defaultValue: Duration(milliseconds: 500),
    );
  }

  // ============================================================================
  // CAMERA CONFIGURATION
  // ============================================================================

  /// Get default camera resolution
  static String get defaultCameraResolution {
    return _manager.getValue<String>(
      ConfigKeys.defaultCameraResolution,
      defaultValue: 'medium',
    );
  }

  /// Get camera aspect ratio
  static double get cameraAspectRatio {
    return _manager.getValue<double>(
      ConfigKeys.cameraAspectRatio,
      defaultValue: 16 / 9,
    );
  }

  /// Get max face captures
  static int get maxFaceCaptures {
    return _manager.getValue<int>(
      ConfigKeys.maxFaceCaptures,
      defaultValue: 5,
    );
  }

  // ============================================================================
  // SECURITY CONFIGURATION
  // ============================================================================

  /// Get minimum password length
  static int get minPasswordLength {
    return _manager.getValue<int>(
      ConfigKeys.minPasswordLength,
      defaultValue: 6,
    );
  }

  /// Get maximum password length
  static int get maxPasswordLength {
    return _manager.getValue<int>(
      ConfigKeys.maxPasswordLength,
      defaultValue: 128,
    );
  }

  /// Check if biometric auth is enabled
  static bool get biometricAuthEnabled {
    return _manager.getValue<bool>(
      ConfigKeys.biometricAuthEnabled,
      defaultValue: true,
    );
  }

  // ============================================================================
  // CACHE CONFIGURATION
  // ============================================================================

  /// Get cache API duration
  static Duration get cacheApiDuration {
    return _manager.getValue<Duration>(
      ConfigKeys.cacheApiDuration,
      defaultValue: Duration(minutes: 5),
    );
  }

  /// Get max cache size in MB
  static int get maxCacheSizeMB {
    return _manager.getValue<int>(
      ConfigKeys.maxCacheSizeMB,
      defaultValue: 50,
    );
  }

  // ============================================================================
  // KIOSK CONFIGURATION
  // ============================================================================

  /// Get idle timeout
  static Duration get idleTimeout {
    return _manager.getValue<Duration>(
      ConfigKeys.idleTimeout,
      defaultValue: Duration(minutes: 2),
    );
  }

  /// Check if auto return home is enabled
  static bool get autoReturnHome {
    return _manager.getValue<bool>(
      ConfigKeys.autoReturnHome,
      defaultValue: true,
    );
  }

  /// Check if fullscreen mode is enabled
  static bool get fullscreenMode {
    return _manager.getValue<bool>(
      ConfigKeys.fullscreenMode,
      defaultValue: true,
    );
  }

  // ============================================================================
  // DEBUG CONFIGURATION
  // ============================================================================

  /// Check if face detection logs are enabled
  static bool get enableFaceDetectionLogs {
    return _manager.getValue<bool>(
      ConfigKeys.enableFaceDetectionLogs,
      defaultValue: false,
    );
  }

  /// Check if performance monitoring is enabled
  static bool get enablePerformanceMonitoring {
    return _manager.getValue<bool>(
      ConfigKeys.enablePerformanceMonitoring,
      defaultValue: false,
    );
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Set configuration value
  static Future<void> setValue<T>(String key, T value) async {
    await _manager.setValue(key, value);
  }

  /// Get configuration value with custom default
  static T getValue<T>(String key, T defaultValue) {
    try {
      return _manager.getValue<T>(key, defaultValue: defaultValue);
    } catch (e) {
      // If ConfigurationManager is not initialized, return default value
      if (e is StateError && e.toString().contains('not initialized')) {
        if (kDebugMode) {
          print('⚠️ ConfigHelper: Using default value for $key because ConfigurationManager is not initialized');
        }
        return defaultValue;
      }
      rethrow;
    }
  }

  /// Get all configuration values for a category
  static Map<String, dynamic> getCategoryValues(String category) {
    return _manager.getCategoryValues(category);
  }

  /// Listen to configuration changes
  static Stream<ConfigChangeEvent> get changeStream {
    return _manager.changeStream;
  }

  /// Reload configuration from all sources
  static Future<void> reload() async {
    await _manager.reload();
  }

  /// Validate current configuration
  static List<String> validate() {
    return _manager.validateConfiguration();
  }

  /// Export configuration
  static Map<String, dynamic> export({bool includeSecure = false}) {
    return _manager.exportConfiguration(includeSecure: includeSecure);
  }

  /// Import configuration
  static Future<void> import(Map<String, dynamic> config) async {
    await _manager.importConfiguration(config);
  }
}

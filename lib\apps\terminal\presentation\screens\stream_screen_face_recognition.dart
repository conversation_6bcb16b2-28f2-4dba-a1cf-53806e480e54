import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

import '../../../../shared/providers/face_detection_provider.dart';
import '../../../../shared/services/camera_image_converter.dart';
import '../../services/face_recognition_service.dart';
import '../../services/relay_trigger_service.dart';
import '../../models/relay_trigger_config.dart';
import '../../../../shared/core/config/configuration_manager.dart';
import '../../../../shared/core/config/flexible_config_system.dart';

/// Mixin for handling face recognition functionality in StreamScreen
/// This separates face recognition logic from the main screen
mixin StreamScreenFaceRecognitionMixin {
  // Abstract getters that must be implemented by the using class
  FaceDetectionProvider get faceDetectionProvider;
  FaceRecognitionService? get faceRecognitionService;
  Face? get bestFace;
  bool get isRecognizing;
  bool get isRecognitionThrottleActive;
  Duration get recognitionThrottleDuration;

  // Relay trigger service for hardware integration
  final RelayTriggerService _relayTriggerService = RelayTriggerService();
  double get throttleProgress;
  
  // Abstract setters/methods that must be implemented by the using class
  void setRecognizing(bool value);
  void setCurrentUser(dynamic user);
  void setLastRecognitionTime(DateTime? time);
  void startRecognitionThrottle();
  void triggerSideEffect(dynamic result);

  /// Initialize relay trigger service
  Future<void> initializeRelayTriggers() async {
    await _relayTriggerService.initialize();
    if (kDebugMode) {
      print('🔧 Relay trigger service initialized for face recognition');
    }
  }

  /// Handle access decision and trigger appropriate relays
  void _handleAccessDecision(dynamic result, bool accessGranted) {
    if (accessGranted) {
      // 🔌 TRIGGER: Access granted - open terminal door + success LED
      _relayTriggerService.triggerScenario(
        RelayTriggerScenario.accessGranted,
        metadata: {
          'timestamp': DateTime.now().toIso8601String(),
          'user_name': result?.user?.name ?? 'Unknown',
          'user_id': result?.user?.id ?? 'Unknown',
          'confidence': result?.confidence ?? 0.0,
          'trigger_source': 'access_granted',
        },
      );

      if (kDebugMode) {
        print('🔌 Access GRANTED - Triggering terminal door + success LED');
        print('   User: ${result?.user?.name ?? 'Unknown'}');
        print('   Confidence: ${((result?.confidence ?? 0.0) * 100).toStringAsFixed(1)}%');
      }
    } else {
      // 🔌 TRIGGER: Access denied - warning LED
      _relayTriggerService.triggerScenario(
        RelayTriggerScenario.accessDenied,
        metadata: {
          'timestamp': DateTime.now().toIso8601String(),
          'reason': result?.message ?? 'Recognition failed',
          'status': result?.status ?? 'Unknown',
          'trigger_source': 'access_denied',
        },
      );

      if (kDebugMode) {
        print('🔌 Access DENIED - Triggering warning LED');
        print('   Reason: ${result?.message ?? 'Recognition failed'}');
      }
    }
  }

  /// Handle no face timeout - turn off auxiliary devices
  void handleNoFaceTimeout() {
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.noFaceTimeout,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'no_face_timeout',
        'reason': 'Power saving mode',
      },
    );

    if (kDebugMode) {
      print('🔌 No face timeout - Switching to power saving mode');
      print('   Turning off LED lighting and IR camera');
    }
  }

  /// Emergency stop all relays
  Future<void> emergencyStopAllRelays() async {
    await _relayTriggerService.emergencyStop();
    if (kDebugMode) {
      print('🚨 Emergency stop - All relays turned off immediately');
    }
  }

  /// Get relay trigger service for external access
  RelayTriggerService get relayTriggerService => _relayTriggerService;

  /// Trigger face recognition if conditions are met
  void triggerFaceRecognition() {
    // Skip if throttle is active
    if (isRecognitionThrottleActive) {
      if (kDebugMode) {
        print('⏸️ Recognition skipped - throttle active (${(throttleProgress * 100).toStringAsFixed(1)}%)');
      }
      return;
    }

    // Skip if no face or already recognizing
    if (bestFace == null || isRecognizing || faceRecognitionService == null) {
      return;
    }

    // 🔌 TRIGGER: Face detected - activate LED lighting and IR camera
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.faceDetected,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'face_quality': faceDetectionProvider.getFaceQuality(bestFace!),
        'trigger_source': 'face_detection',
      },
    );

    // Check face quality
    final quality = faceDetectionProvider.getFaceQuality(bestFace!);
    final minQuality = ConfigurationManager.instance.getValue(
      ConfigKeys.minFaceQualityForRecognition,
      defaultValue: 0.4,
    );

    if (quality < minQuality) {
      if (kDebugMode) {
        print('⏸️ Bỏ qua nhận diện - Chất lượng khuôn mặt thấp: ${(quality * 100).toStringAsFixed(1)}% (tối thiểu: ${(minQuality * 100).toStringAsFixed(1)}%)');
      }
      return;
    }

    if (kDebugMode) {
      print('🚀 Bắt đầu nhận diện khuôn mặt - Chất lượng: ${(quality * 100).toStringAsFixed(1)}%');
    }

    // Start throttle timer
    startRecognitionThrottle();

    // Perform actual face recognition
    performFaceRecognition(quality);
  }

  /// Perform the actual face recognition process
  Future<void> performFaceRecognition(double quality) async {
    setRecognizing(true);

    // 🔌 TRIGGER: Face recognition started - activate backup sensors
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.faceRecognitionStart,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'face_quality': quality,
        'trigger_source': 'recognition_start',
      },
    );

    try {
      // Get current camera image for recognition
      final cameraImage = faceDetectionProvider.latestCameraImage;
      final cameraDescription = faceDetectionProvider.latestCameraDescription;

      if (cameraImage == null || cameraDescription == null) {
        if (kDebugMode) {
          print('❌ No camera image available for recognition');
        }
        setRecognizing(false);
        return;
      }

      // Convert camera image to bytes for recognition
      final imageBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
        quality: 85, // Good quality for recognition
      );

      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ Failed to convert camera image to bytes');
        }
        setRecognizing(false);
        return;
      }

      if (kDebugMode) {
        print('🔍 Đang xử lý nhận diện khuôn mặt...');
        print('   📊 Chất lượng khuôn mặt: ${(quality * 100).toStringAsFixed(1)}%');
        print('   📷 Kích thước ảnh: ${(imageBytes.length / 1024).toStringAsFixed(1)} KB');
      }

      // Send to server for recognition
      final result = await faceRecognitionService!.recognizeFace(
        imageData: imageBytes,
        confidenceScore: quality,
        metadata: {
          'source': 'terminal_camera_stream',
          'timestamp': DateTime.now().toIso8601String(),
          'device_id': 'terminal_001',
          'quality': quality,
        },
      );

      if (result != null && result.recognized && result.user != null) {
        setCurrentUser(result.user);
        setLastRecognitionTime(DateTime.now());
        setRecognizing(false);

        if (kDebugMode) {
          print('✅ Face recognized: ${result.user!.name}');
        }

        // 🔌 TRIGGER: Access granted - open terminal door
        _handleAccessDecision(result, true);

        // Trigger side effects for successful recognition
        triggerSideEffect(result);
      } else {
        // Clear current user if recognition failed
        setCurrentUser(null);
        setLastRecognitionTime(null);
        setRecognizing(false);

        // 🔌 TRIGGER: Access denied - warning LED
        _handleAccessDecision(result, false);

        if (kDebugMode) {
          print('❌ Face not recognized');
          if (result != null) {
            print('   Status: ${result.status}');
            print('   Message: ${result.message}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition error: $e');
      }
      setRecognizing(false);
    }
  }

  /// Force recognition after throttle completion to refresh server data
  /// This bypasses normal quality and state checks to ensure fresh data
  /// ONLY runs when a face is currently detected
  Future<void> forcedRecognitionAfterThrottle() async {
    // Double check: only proceed if face is currently detected
    if (bestFace == null || faceRecognitionService == null) {
      if (kDebugMode) {
        print('⏸️ FORCED recognition cancelled - no face detected or service unavailable');
      }
      return;
    }

    // Clear any stale client data first
    setCurrentUser(null);
    setLastRecognitionTime(null);
    setRecognizing(true);

    if (kDebugMode) {
      print('🔄 FORCED recognition - clearing stale client data');
      print('   Requesting fresh data from server...');
      print('   This prevents users from standing too long without authentication');
    }

    try {
      // CRITICAL: Re-check if face is still present before proceeding
      // Face might have disappeared between throttle completion and this execution
      if (bestFace == null) {
        if (kDebugMode) {
          print('❌ FORCED recognition cancelled - face disappeared during execution');
          print('   Face was present at throttle completion but gone now');
        }
        setRecognizing(false);
        return;
      }

      // Check face quality again to ensure it's still valid
      final currentQuality = faceDetectionProvider.getFaceQuality(bestFace!);
      if (currentQuality < 0.3) { // Lower threshold for forced recognition
        if (kDebugMode) {
          print('❌ FORCED recognition cancelled - face quality too low: ${(currentQuality * 100).toStringAsFixed(1)}%');
        }
        setRecognizing(false);
        return;
      }

      if (kDebugMode) {
        print('✅ FORCED recognition proceeding - face confirmed present with quality: ${(currentQuality * 100).toStringAsFixed(1)}%');
      }

      // Start new throttle cycle for the forced request
      startRecognitionThrottle();

      // Get current camera image for recognition
      final cameraImage = faceDetectionProvider.latestCameraImage;
      final cameraDescription = faceDetectionProvider.latestCameraDescription;

      if (cameraImage == null || cameraDescription == null) {
        if (kDebugMode) {
          print('❌ No camera image available for forced recognition');
        }
        setRecognizing(false);
        return;
      }

      // Convert camera image to bytes for recognition
      final imageBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
        quality: 85, // Good quality for recognition
      );

      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ Failed to convert camera image to bytes for forced recognition');
        }
        setRecognizing(false);
        return;
      }

      // Force recognition with lower quality threshold to ensure server contact
      final quality = faceDetectionProvider.getFaceQuality(bestFace!);
      
      if (kDebugMode) {
        print('🔄 FORCED recognition - bypassing quality checks');
        print('   Face quality: ${(quality * 100).toStringAsFixed(1)}%');
        print('   Image size: ${imageBytes.length} bytes');
      }

      // Send to server for recognition (forced, bypasses normal quality checks)
      final result = await faceRecognitionService!.recognizeFace(
        imageData: imageBytes,
        confidenceScore: quality,
        metadata: {
          'source': 'forced_recognition_after_throttle',
          'timestamp': DateTime.now().toIso8601String(),
          'device_id': 'terminal_001',
          'forced': true,
          'reason': 'prevent_long_wait_without_auth',
        },
      );

      if (result != null && result.recognized && result.user != null) {
        setCurrentUser(result.user);
        setLastRecognitionTime(DateTime.now());
        setRecognizing(false);

        if (kDebugMode) {
          print('✅ FORCED recognition successful: ${result.user!.name}');
          print('   Server provided fresh data - client error prevented');
        }

        // Trigger side effects for successful recognition
        triggerSideEffect(result);
      } else {
        // Clear current user if forced recognition failed
        setCurrentUser(null);
        setLastRecognitionTime(null);
        setRecognizing(false);

        if (kDebugMode) {
          print('❌ FORCED recognition failed - server contacted but no match');
          print('   Fresh data received from server');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ FORCED recognition error: $e');
      }
      setRecognizing(false);
    }
  }
}

/// Base exception class for relay controller errors.
class RelayControllerException implements Exception {
  /// The error message.
  final String message;

  /// Optional underlying cause of the exception.
  final dynamic cause;

  /// Creates a new [RelayControllerException].
  const RelayControllerException(this.message, [this.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'RelayControllerException: $message\nCaused by: $cause';
    }
    return 'RelayControllerException: $message';
  }
}

/// Exception thrown when Bluetooth operations fail.
class BluetoothRelayException extends RelayControllerException {
  const BluetoothRelayException(super.message, [super.cause]);
}

/// Exception thrown when HTTP operations fail.
class HttpRelayException extends RelayControllerException {
  const HttpRelayException(super.message, [super.cause]);
}

/// Exception thrown when MQTT operations fail.
class MqttRelayException extends RelayControllerException {
  const MqttRelayException(super.message, [super.cause]);
}

/// Exception thrown when USB operations fail.
class UsbRelayException extends RelayControllerException {
  const UsbRelayException(super.message, [super.cause]);
}

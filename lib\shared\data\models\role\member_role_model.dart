/// Member role data model for API communication
class MemberRoleModel {
  final String id;
  final String name;
  final String? description;
  final String? tenantId;
  final List<String> scopes;
  final String? dataScope;
  final String? createdBy;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const MemberRoleModel({
    required this.id,
    required this.name,
    this.description,
    this.tenantId,
    this.scopes = const [],
    this.dataScope,
    this.createdBy,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create MemberRoleModel from JSON
  factory MemberRoleModel.fromJson(Map<String, dynamic> json) {
    return MemberRoleModel(
      id: json['id'] as String? ?? json['_id'] as String,
      name: json['name'] as String? ?? '',
      description: json['description'] as String?,
      tenantId: json['tenant_id'] as String?,
      scopes: (json['scopes'] as List<dynamic>?)?.cast<String>() ?? [],
      dataScope: json['data_scope'] as String?,
      createdBy: json['created_by'] as String?,
      isActive: json['is_active'] as bool? ?? json['isActive'] as bool? ?? true,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : json['createdAt'] != null
              ? DateTime.parse(json['createdAt'] as String)
              : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      if (description != null) 'description': description,
      if (tenantId != null) 'tenant_id': tenantId,
      'scopes': scopes,
      if (dataScope != null) 'data_scope': dataScope,
      if (createdBy != null) 'created_by': createdBy,
      'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MemberRoleModel copyWith({
    String? id,
    String? name,
    String? description,
    String? tenantId,
    List<String>? scopes,
    String? dataScope,
    String? createdBy,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return MemberRoleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      tenantId: tenantId ?? this.tenantId,
      scopes: scopes ?? this.scopes,
      dataScope: dataScope ?? this.dataScope,
      createdBy: createdBy ?? this.createdBy,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemberRoleModel && 
      runtimeType == other.runtimeType && 
      id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MemberRoleModel(id: $id, name: $name, isActive: $isActive)';
  }
}

/// UI model for member roles (simplified for dropdowns)
class MemberRoleUIModel {
  final String id;
  final String name;
  final String? description;
  final bool isActive;

  const MemberRoleUIModel({
    required this.id,
    required this.name,
    this.description,
    this.isActive = true,
  });

  factory MemberRoleUIModel.fromMemberRoleModel(MemberRoleModel model) {
    return MemberRoleUIModel(
      id: model.id,
      name: model.name,
      description: model.description,
      isActive: model.isActive,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MemberRoleUIModel && 
      runtimeType == other.runtimeType && 
      id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MemberRoleUIModel(id: $id, name: $name)';
  }
}

/// Option model for role selection dropdowns
class RoleOption {
  final String id;
  final String name;
  final String? description;

  const RoleOption({
    required this.id,
    required this.name,
    this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RoleOption && 
      runtimeType == other.runtimeType && 
      id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'RoleOption(id: $id, name: $name)';
  }
}

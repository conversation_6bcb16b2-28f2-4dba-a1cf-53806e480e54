# API Documentation - Login Functionality

## 🌐 API Overview

Tài liệu này mô tả chi tiết API endpoints và integration patterns cho chức năng đăng nhập trong c-face-terminal.

## 🔗 Base URL Configuration

### Dynamic Base URL Support

#### On Cloud Mode
```dart
// Environment-based URLs
String getBaseUrlByEnvironment(AppEnvironment environment) {
  switch (environment) {
    case AppEnvironment.development:
      return 'http://*************:5000';
    case AppEnvironment.staging:
      return 'https://staging-api.c-faces.com';
    case AppEnvironment.production:
      return 'https://api.c-faces.com';
  }
}
```

#### On Premise Mode
```dart
// User-defined server address
String baseUrl = _serverAddressController.text; // e.g., "https://company-server.local:8080"
```

## 🔐 Authentication Endpoint

### Login API

#### Endpoint
```
POST {{baseUrl}}/api/v3.1/identity/login
```

#### Headers
```http
Content-Type: application/json
Accept: application/json
```

#### Request Body
```json
{
  "username": "string",
  "password": "string"
}
```

#### Request Example
```http
POST https://api.c-faces.com/api/v3.1/identity/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### Success Response (200 OK)
```json
{
  "code": "SUCCESS",
  "success": true,
  "statusCode": 200,
  "message": "Success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "expires_in": 899,
    "token_type": "Bearer",
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "name": "System Administrator",
      "email": "<EMAIL>",
      "gender": "other",
      "roles": ["user", "admin"],
      "scopes": ["read", "write"],
      "current_tenant_id": "550e8400-e29b-41d4-a716-446655440001"
    }
  }
}
```

#### Error Responses

##### Invalid Credentials (401 Unauthorized)
```json
{
  "code": "INVALID_CREDENTIALS",
  "success": false,
  "statusCode": 401,
  "message": "Invalid username or password",
  "data": null
}
```

##### Validation Error (400 Bad Request)
```json
{
  "code": "VALIDATION_ERROR",
  "success": false,
  "statusCode": 400,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "username",
        "message": "Username is required"
      },
      {
        "field": "password",
        "message": "Password must be at least 6 characters"
      }
    ]
  }
}
```

##### Server Error (500 Internal Server Error)
```json
{
  "code": "INTERNAL_ERROR",
  "success": false,
  "statusCode": 500,
  "message": "Internal server error",
  "data": null
}
```

##### Network/Connection Error
```json
{
  "code": "NETWORK_ERROR",
  "success": false,
  "statusCode": 0,
  "message": "Cannot connect to server. Please check your connection.",
  "data": null
}
```

## 🔧 Implementation Details

### Dart/Flutter Integration

#### API Client Configuration
```dart
class AuthRemoteDataSource {
  final ApiClient apiClient;
  
  AuthRemoteDataSource(this.apiClient);
  
  Future<AuthResultModel> login({
    required String userName,
    required String password,
  }) async {
    try {
      final response = await apiClient.post(
        ApiEndpoints.login,
        body: {
          'username': userName,
          'password': password,
        },
      );
      
      // Check response structure
      if (response['success'] == true && response['data'] != null) {
        return AuthResultModel.fromJson(response['data']);
      } else {
        throw AuthException(
          response['message'] ?? 'Login failed',
          code: response['code'] ?? 'UNKNOWN_ERROR',
        );
      }
    } on NetworkException catch (e) {
      throw AuthException(
        'Không thể kết nối đến server: ${e.message}',
        code: 'NETWORK_ERROR',
      );
    } catch (e) {
      throw AuthException(
        'Lỗi đăng nhập: ${e.toString()}',
        code: 'UNKNOWN_ERROR',
      );
    }
  }
}
```

#### Response Model
```dart
class AuthResultModel {
  final String accessToken;
  final int expiresIn;
  final String tokenType;
  final UserModel user;
  
  AuthResultModel({
    required this.accessToken,
    required this.expiresIn,
    required this.tokenType,
    required this.user,
  });
  
  factory AuthResultModel.fromJson(Map<String, dynamic> json) {
    return AuthResultModel(
      accessToken: json['access_token'] as String,
      expiresIn: json['expires_in'] as int,
      tokenType: json['token_type'] as String,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
    );
  }
  
  AuthResult toEntity() {
    return AuthResult(
      accessToken: accessToken,
      expiresIn: expiresIn,
      tokenType: tokenType,
      user: user.toEntity(),
    );
  }
}
```

#### Error Handling
```dart
class AuthException implements Exception {
  final String message;
  final String code;
  
  const AuthException(this.message, {required this.code});
  
  @override
  String toString() => 'AuthException: $message (Code: $code)';
}

// Error mapping
AuthException mapApiError(Map<String, dynamic> response) {
  final code = response['code'] as String? ?? 'UNKNOWN_ERROR';
  final message = response['message'] as String? ?? 'Unknown error occurred';
  
  switch (code) {
    case 'INVALID_CREDENTIALS':
      return AuthException('Tên đăng nhập hoặc mật khẩu không đúng', code: code);
    case 'VALIDATION_ERROR':
      return AuthException('Dữ liệu đầu vào không hợp lệ', code: code);
    case 'INTERNAL_ERROR':
      return AuthException('Lỗi server nội bộ', code: code);
    default:
      return AuthException(message, code: code);
  }
}
```

## 🔄 Token Management

### Access Token Usage
```dart
class ApiClient {
  String? _accessToken;
  
  void setAccessToken(String? token) {
    _accessToken = token;
  }
  
  Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (_accessToken != null)
      'Authorization': 'Bearer $_accessToken',
  };
}
```

### Token Storage
```dart
class AuthLocalDataSource {
  final SecureStorageService secureStorage;
  
  static const String _accessTokenKey = 'access_token';
  static const String _userDataKey = 'user_data';
  static const String _tokenExpiryKey = 'token_expiry';
  
  Future<void> saveAuthResult(AuthResultModel authResult) async {
    await Future.wait([
      secureStorage.write(_accessTokenKey, authResult.accessToken),
      secureStorage.write(_userDataKey, jsonEncode(authResult.user.toJson())),
      secureStorage.write(
        _tokenExpiryKey, 
        DateTime.now().add(Duration(seconds: authResult.expiresIn)).toIso8601String(),
      ),
    ]);
  }
  
  Future<String?> getAccessToken() async {
    return await secureStorage.read(_accessTokenKey);
  }
  
  Future<void> clearAuthData() async {
    await Future.wait([
      secureStorage.delete(_accessTokenKey),
      secureStorage.delete(_userDataKey),
      secureStorage.delete(_tokenExpiryKey),
    ]);
  }
}
```

## 🧪 Testing Examples

### Mock API Responses
```dart
// Success response mock
final mockSuccessResponse = {
  'code': 'SUCCESS',
  'success': true,
  'statusCode': 200,
  'message': 'Success',
  'data': {
    'access_token': 'mock_access_token',
    'expires_in': 3600,
    'token_type': 'Bearer',
    'user': {
      'id': 'mock_user_id',
      'username': 'testuser',
      'name': 'Test User',
      'email': '<EMAIL>',
      'gender': 'other',
      'roles': ['user'],
      'scopes': [],
      'current_tenant_id': 'mock_tenant_id',
    },
  },
};

// Error response mock
final mockErrorResponse = {
  'code': 'INVALID_CREDENTIALS',
  'success': false,
  'statusCode': 401,
  'message': 'Invalid username or password',
  'data': null,
};
```

### Integration Test Example
```dart
testWidgets('should login successfully with valid credentials', (tester) async {
  // Setup mock HTTP client
  final mockClient = MockClient((request) async {
    if (request.url.path.endsWith('/api/v3.1/identity/login')) {
      return http.Response(
        jsonEncode(mockSuccessResponse),
        200,
        headers: {'content-type': 'application/json'},
      );
    }
    return http.Response('Not Found', 404);
  });
  
  // Test implementation...
});
```

## 📋 Checklist

### API Integration Checklist
- [ ] Endpoint URL updated to `/api/v3.1/identity/login`
- [ ] Request body format matches specification
- [ ] Response parsing handles all fields correctly
- [ ] Error responses mapped to appropriate exceptions
- [ ] Token storage implemented securely
- [ ] Network error handling implemented
- [ ] Timeout configuration set appropriately
- [ ] Retry logic implemented for network failures
- [ ] Base URL configuration supports both modes
- [ ] API client headers include proper authentication

### Testing Checklist
- [ ] Unit tests for API client
- [ ] Unit tests for data models
- [ ] Integration tests for login flow
- [ ] Error scenario tests
- [ ] Network failure tests
- [ ] Mock server setup for development
- [ ] Performance tests for response times

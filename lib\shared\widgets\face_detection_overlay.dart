import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

class FaceBoundingBoxPainter extends CustomPainter {
  final List<Face> faces;
  final Face? bestFace;
  final Size imageSize;
  final Size canvasSize;
  final bool isFrontCamera;
  final Function(Face) getFaceQuality;

  FaceBoundingBoxPainter({
    required this.faces,
    required this.bestFace,
    required this.imageSize,
    required this.canvasSize,
    required this.isFrontCamera,
    required this.getFaceQuality,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Only draw the best face to avoid clutter
    if (bestFace != null) {
      _drawFaceBoundingBox(canvas, bestFace!, true);
      _drawFaceInfo(canvas, bestFace!);
    }
  }

  void _drawFaceBoundingBox(Canvas canvas, Face face, bool isBest) {
    final rect = _transformRect(face.boundingBox);
    final quality = getFaceQuality(face);
    final color = _getQualityColor(quality);

    // Only draw corner indicators (no full rectangle)
    _drawCornerIndicators(canvas, rect, color, isBest);
  }

  void _drawCornerIndicators(
    Canvas canvas,
    Rect rect,
    Color color,
    bool isBest,
  ) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = isBest ? 4.0 : 3.0
      ..strokeCap = StrokeCap.round;

    const cornerLength = 20.0;

    // Top-left corner
    canvas.drawLine(
      Offset(rect.left, rect.top + cornerLength),
      Offset(rect.left, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.top),
      Offset(rect.left + cornerLength, rect.top),
      paint,
    );

    // Top-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.top),
      Offset(rect.right, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.top),
      Offset(rect.right, rect.top + cornerLength),
      paint,
    );

    // Bottom-left corner
    canvas.drawLine(
      Offset(rect.left, rect.bottom - cornerLength),
      Offset(rect.left, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.bottom),
      Offset(rect.left + cornerLength, rect.bottom),
      paint,
    );

    // Bottom-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.bottom),
      Offset(rect.right, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.bottom),
      Offset(rect.right, rect.bottom - cornerLength),
      paint,
    );
  }

  void _drawFaceInfo(Canvas canvas, Face face) {
    final rect = _transformRect(face.boundingBox);
    final quality = getFaceQuality(face);
    final qualityLevel = _getQualityLevel(quality);

    // Draw quality indicator
    _drawQualityIndicator(canvas, rect, quality, qualityLevel);

    // Draw tracking ID if available
    if (face.trackingId != null) {
      _drawTrackingId(canvas, rect, face.trackingId!);
    }

    // Draw landmarks if available
    if (face.landmarks.isNotEmpty) {
      // Filter out null landmarks
      final validLandmarks = <FaceLandmarkType, FaceLandmark>{};
      face.landmarks.forEach((type, landmark) {
        if (landmark != null) {
          validLandmarks[type] = landmark;
        }
      });
      if (validLandmarks.isNotEmpty) {
        _drawLandmarks(canvas, validLandmarks);
      }
    }
  }

  void _drawQualityIndicator(
    Canvas canvas,
    Rect rect,
    double quality,
    String qualityLevel,
  ) {
    final color = _getQualityColor(quality);
    final qualityPercentage = '${(quality * 100).toInt()}%';
    final displayText = '$qualityLevel - $qualityPercentage';

    // Background for quality text
    final textPainter = TextPainter(
      text: TextSpan(
        text: displayText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    final bgRect = Rect.fromLTWH(
      rect.left,
      rect.top - 25,
      textPainter.width + 8,
      20,
    );

    final bgPaint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(bgRect, const Radius.circular(4)),
      bgPaint,
    );

    textPainter.paint(canvas, Offset(rect.left + 4, rect.top - 23));

    // Quality score bar
    _drawQualityBar(canvas, rect, quality, color);
  }

  void _drawQualityBar(Canvas canvas, Rect rect, double quality, Color color) {
    const barWidth = 60.0;
    const barHeight = 4.0;

    final barRect = Rect.fromLTWH(
      rect.right - barWidth,
      rect.top - 8,
      barWidth,
      barHeight,
    );

    // Background bar
    final bgPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(barRect, const Radius.circular(2)),
      bgPaint,
    );

    // Quality fill
    final fillRect = Rect.fromLTWH(
      barRect.left,
      barRect.top,
      barWidth * quality,
      barHeight,
    );

    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(fillRect, const Radius.circular(2)),
      fillPaint,
    );
  }

  void _drawTrackingId(Canvas canvas, Rect rect, int trackingId) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'ID: $trackingId',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    final bgRect = Rect.fromLTWH(
      rect.right - textPainter.width - 8,
      rect.bottom + 5,
      textPainter.width + 8,
      16,
    );

    final bgPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.7)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(bgRect, const Radius.circular(4)),
      bgPaint,
    );

    textPainter.paint(
      canvas,
      Offset(rect.right - textPainter.width - 4, rect.bottom + 7),
    );
  }

  void _drawLandmarks(
    Canvas canvas,
    Map<FaceLandmarkType, FaceLandmark> landmarks,
  ) {
    final paint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    landmarks.forEach((type, landmark) {
      final point = _transformPoint(landmark.position);
      canvas.drawCircle(point, 2, paint);
    });
  }

  Rect _transformRect(Rect rect) {
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;

    // Debug coordinate transformation
    if (kDebugMode) {
      print('🎯 Face Detection Coordinate Transform:');
      print('   Image size: ${imageSize.width.toInt()}x${imageSize.height.toInt()}');
      print('   Canvas size: ${canvasSize.width.toInt()}x${canvasSize.height.toInt()}');
      print('   Scale factors: X=${scaleX.toStringAsFixed(3)}, Y=${scaleY.toStringAsFixed(3)}');
      print('   Original rect: ${rect.left.toInt()}, ${rect.top.toInt()}, ${rect.width.toInt()}x${rect.height.toInt()}');
    }

    double left = rect.left * scaleX;
    double top = rect.top * scaleY;
    double right = rect.right * scaleX;
    double bottom = rect.bottom * scaleY;

    // Handle front camera mirroring
    if (isFrontCamera) {
      final temp = left;
      left = canvasSize.width - right;
      right = canvasSize.width - temp;

      if (kDebugMode) {
        print('   Front camera mirroring applied');
      }
    }

    final transformedRect = Rect.fromLTRB(left, top, right, bottom);

    if (kDebugMode) {
      print('   Transformed rect: ${transformedRect.left.toInt()}, ${transformedRect.top.toInt()}, ${transformedRect.width.toInt()}x${transformedRect.height.toInt()}');
    }

    return transformedRect;
  }

  Offset _transformPoint(Point<int> point) {
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;

    double x = point.x * scaleX;
    double y = point.y * scaleY;

    // Handle front camera mirroring
    if (isFrontCamera) {
      x = canvasSize.width - x;
    }

    return Offset(x, y);
  }

  Color _getQualityColor(double quality) {
    if (quality >= 0.85) return const Color(0xFF4CAF50); // Green - Excellent
    if (quality >= 0.7) return const Color(0xFF8BC34A); // Light Green - Good
    if (quality >= 0.55) return const Color(0xFFFFC107); // Amber - Fair
    if (quality >= 0.4) return const Color(0xFFFF9800); // Orange - Acceptable
    return const Color(0xFFF44336); // Red - Poor (should not be displayed)
  }

  String _getQualityLevel(double quality) {
    if (quality >= 0.9) return 'EXCELLENT';
    if (quality >= 0.75) return 'GOOD';
    if (quality >= 0.6) return 'FAIR';
    if (quality >= 0.5) return 'OK';
    return 'POOR';
  }

  @override
  bool shouldRepaint(covariant FaceBoundingBoxPainter oldDelegate) {
    // Only repaint if faces have actually changed
    if (faces.length != oldDelegate.faces.length) return true;
    if (bestFace != oldDelegate.bestFace) return true;

    // Check if any face positions have changed significantly
    for (int i = 0; i < faces.length; i++) {
      if (i >= oldDelegate.faces.length) return true;

      final oldRect = oldDelegate.faces[i].boundingBox;
      final newRect = faces[i].boundingBox;

      // Check if position changed by more than 5 pixels
      if ((oldRect.left - newRect.left).abs() > 5 ||
          (oldRect.top - newRect.top).abs() > 5 ||
          (oldRect.width - newRect.width).abs() > 5 ||
          (oldRect.height - newRect.height).abs() > 5) {
        return true;
      }
    }

    return false; // No significant changes, don't repaint
  }
}

class FaceDetectionOverlay extends StatelessWidget {
  final List<Face> faces;
  final Face? bestFace;
  final Size imageSize;
  final Size? canvasSize; // Optional, will use MediaQuery if not provided
  final bool isFrontCamera;
  final Function(Face) getFaceQuality;
  final Widget child;

  const FaceDetectionOverlay({
    super.key,
    required this.faces,
    required this.bestFace,
    required this.imageSize,
    this.canvasSize, // Optional parameter
    required this.isFrontCamera,
    required this.getFaceQuality,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        Positioned.fill(
          child: CustomPaint(
            painter: FaceBoundingBoxPainter(
              faces: faces,
              bestFace: bestFace,
              imageSize: imageSize,
              canvasSize: canvasSize ?? MediaQuery.of(context).size,
              isFrontCamera: isFrontCamera,
              getFaceQuality: getFaceQuality,
            ),
          ),
        ),
      ],
    );
  }
}

import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../models/camera_config.dart';
import '../providers/face_detection_provider.dart';

/// Utility class for face detection operations
class FaceDetectionUtils {
  
  /// Validate camera configuration
  static bool validateCameraConfig(CameraConfig config, List<CameraDescription> availableCameras) {
    if (availableCameras.isEmpty) {
      if (kDebugMode) {
        print('No cameras available');
      }
      return false;
    }

    // Check if preferred camera is available
    final hasPreferredCamera = availableCameras.any(
      (camera) => camera.lensDirection == config.preferredLensDirection,
    );

    if (!hasPreferredCamera) {
      if (kDebugMode) {
        print('Warning: Preferred camera direction ${config.preferredLensDirection} not available');
      }
    }

    return true;
  }

  /// Validate face detection configuration
  static bool validateFaceDetectionConfig(FaceDetectionConfig config) {
    if (config.frameSkip <= 0) {
      if (kDebugMode) {
        print('Invalid frame skip value: ${config.frameSkip}');
      }
      return false;
    }

    if (config.minFaceSize <= 0 || config.minFaceSize > 1.0) {
      if (kDebugMode) {
        print('Invalid min face size: ${config.minFaceSize}');
      }
      return false;
    }

    if (config.qualityThreshold < 0 || config.qualityThreshold > 1.0) {
      if (kDebugMode) {
        print('Invalid quality threshold: ${config.qualityThreshold}');
      }
      return false;
    }

    return true;
  }

  /// Get optimal camera configuration based on use case
  static CameraConfig getOptimalCameraConfig(String useCase) {
    switch (useCase.toLowerCase()) {
      case 'face_capture':
      case 'selfie':
        return CameraConfig.faceCapture;
      case 'high_quality':
      case 'photo':
        return CameraConfig.highQuality;
      case 'performance':
      case 'streaming':
        return CameraConfig.performance;
      default:
        return CameraConfig.faceCapture;
    }
  }

  /// Get optimal face detection configuration based on use case
  static FaceDetectionConfig getOptimalFaceDetectionConfig(String useCase) {
    switch (useCase.toLowerCase()) {
      case 'high_accuracy':
      case 'photo':
        return FaceDetectionConfig.highAccuracy;
      case 'performance':
      case 'streaming':
        return FaceDetectionConfig.performance;
      default:
        return FaceDetectionConfig.defaultConfig;
    }
  }

  /// Create face detector options from configuration
  static FaceDetectorOptions createFaceDetectorOptions(
    FaceDetectionConfig config, {
    FaceDetectorMode performanceMode = FaceDetectorMode.fast,
    bool enableTracking = true,
    bool enableLandmarks = false,
    bool enableContours = false,
    bool enableClassification = true,
  }) {
    return FaceDetectorOptions(
      performanceMode: performanceMode,
      enableTracking: enableTracking,
      enableLandmarks: enableLandmarks,
      enableContours: enableContours,
      enableClassification: enableClassification,
      minFaceSize: config.minFaceSize,
    );
  }

  /// Calculate face detection performance metrics
  static Map<String, dynamic> calculatePerformanceMetrics(
    FaceDetectionProvider provider,
    Duration timeWindow,
  ) {
    // This would be implemented with actual performance tracking
    return {
      'consecutiveErrors': provider.consecutiveErrors,
      'needsRecovery': provider.needsRecovery,
      'isDetecting': provider.isDetecting,
      'hasFaces': provider.hasFaces,
      'faceQuality': provider.faceQuality,
      'faceDirection': provider.faceDirection.toString(),
    };
  }

  /// Get recommended configuration adjustments based on performance
  static Map<String, dynamic> getRecommendedAdjustments(
    Map<String, dynamic> performanceMetrics,
  ) {
    final recommendations = <String, dynamic>{};

    final consecutiveErrors = performanceMetrics['consecutiveErrors'] as int? ?? 0;
    final faceQuality = performanceMetrics['faceQuality'] as double? ?? 0.0;

    if (consecutiveErrors > 3) {
      recommendations['suggestion'] = 'Consider using performance config to reduce errors';
      recommendations['config'] = FaceDetectionConfig.performance;
    } else if (faceQuality < 0.3) {
      recommendations['suggestion'] = 'Consider using high accuracy config for better quality';
      recommendations['config'] = FaceDetectionConfig.highAccuracy;
    } else {
      recommendations['suggestion'] = 'Current configuration is optimal';
      recommendations['config'] = null;
    }

    return recommendations;
  }

  /// Format face direction for display
  static String formatFaceDirection(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return 'Trung tâm';
      case FaceDirection.top:
        return 'Nhìn lên';
      case FaceDirection.bottom:
        return 'Nhìn xuống';
      case FaceDirection.left:
        return 'Nhìn trái';
      case FaceDirection.right:
        return 'Nhìn phải';
      case FaceDirection.unknown:
        return 'Không xác định';
    }
  }

  /// Format face quality level for display
  static String formatFaceQualityLevel(String qualityLevel) {
    switch (qualityLevel.toLowerCase()) {
      case 'excellent':
        return 'Xuất sắc';
      case 'good':
        return 'Tốt';
      case 'fair':
        return 'Khá';
      case 'acceptable':
        return 'Chấp nhận được';
      case 'poor':
        return 'Kém';
      default:
        return qualityLevel;
    }
  }

  /// Get color for face quality level
  static Color getFaceQualityColor(String qualityLevel) {
    switch (qualityLevel.toLowerCase()) {
      case 'excellent':
        return const Color(0xFF4CAF50); // Green
      case 'good':
        return const Color(0xFF8BC34A); // Light Green
      case 'fair':
        return const Color(0xFFFFEB3B); // Yellow
      case 'acceptable':
        return const Color(0xFFFF9800); // Orange
      case 'poor':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Check if face detection should be paused based on app state
  static bool shouldPauseDetection({
    required bool appIsActive,
    required bool cameraIsReady,
    required bool hasCallback,
  }) {
    return !appIsActive || !cameraIsReady || !hasCallback;
  }

  /// Get debug information for troubleshooting
  static Map<String, dynamic> getDebugInfo(
    FaceDetectionProvider provider,
    CameraConfig cameraConfig,
  ) {
    return {
      'faceDetectionConfig': {
        'frameSkip': provider.config.frameSkip,
        'minDetectionInterval': provider.config.minDetectionInterval.inMilliseconds,
        'qualityThreshold': provider.config.qualityThreshold,
        'minFaceArea': provider.config.minFaceArea,
      },
      'cameraConfig': {
        'resolution': cameraConfig.resolution.toString(),
        'lensDirection': cameraConfig.preferredLensDirection.toString(),
        'enableImageStream': cameraConfig.enableImageStream,
      },
      'currentState': {
        'isDetecting': provider.isDetecting,
        'consecutiveErrors': provider.consecutiveErrors,
        'needsRecovery': provider.needsRecovery,
        'facesCount': provider.faces.length,
        'hasBestFace': provider.hasBestFace,
      },
    };
  }
}

import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';

/// Loading spinner component với SVG quay liên tục
class AppLoading extends StatefulWidget {
  final double size;
  final Color? color;

  const AppLoading({
    super.key,
    this.size = 24.0,
    this.color,
  });

  @override
  State<AppLoading> createState() => _AppLoadingState();
}

class _AppLoadingState extends State<AppLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.rotate(
            angle: _animationController.value * 2.0 * 3.14159,
            child: CustomPaint(
              size: Size(widget.size, widget.size),
              painter: _LoadingSpinnerPainter(
                color: widget.color ?? AppColors.primary,
              ),
            ),
          );
        },
      ),
    );
  }
}

class _LoadingSpinnerPainter extends CustomPainter {
  final Color color;

  _LoadingSpinnerPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Scale factor để điều chỉnh từ kích thước gốc (25x24) xuống kích thước hiện tại
    final scaleX = size.width / 25.0;
    final scaleY = size.height / 24.0;

    // SVG Path từ design
    final path = Path();
    path.moveTo(13.7021 * scaleX, 6 * scaleY);
    path.cubicTo(
      15.2153 * scaleX, 6 * scaleY,
      16.6847 * scaleX, 6.29597 * scaleY,
      18.0664 * scaleX, 6.88281 * scaleY,
    );
    path.cubicTo(
      19.4022 * scaleX, 7.44559 * scaleY,
      20.5997 * scaleX, 8.25596 * scaleY,
      21.6289 * scaleX, 9.28516 * scaleY,
    );
    path.cubicTo(
      22.6581 * scaleX, 10.3144 * scaleY,
      23.4663 * scaleX, 11.5141 * scaleY,
      24.0312 * scaleX, 12.8477 * scaleY,
    );
    path.cubicTo(
      24.6159 * scaleX, 14.2294 * scaleY,
      24.9121 * scaleX, 15.6988 * scaleY,
      24.9121 * scaleX, 17.2119 * scaleY,
    );
    path.cubicTo(
      24.9142 * scaleX, 17.6475 * scaleY,
      24.5616 * scaleX, 17.9999 * scaleY,
      24.126 * scaleX, 18 * scaleY,
    );
    path.cubicTo(
      23.6903 * scaleX, 18 * scaleY,
      23.338 * scaleX, 17.6476 * scaleY,
      23.3379 * scaleX, 17.2119 * scaleY,
    );
    path.cubicTo(
      23.3379 * scaleX, 15.9113 * scaleY,
      23.0836 * scaleX, 14.6499 * scaleY,
      22.5801 * scaleX, 13.4609 * scaleY,
    );
    path.cubicTo(
      22.0956 * scaleX, 12.3163 * scaleY,
      21.3939 * scaleX, 11.276 * scaleY,
      20.5146 * scaleX, 10.3975 * scaleY,
    );
    path.cubicTo(
      19.6371 * scaleX, 9.51699 * scaleY,
      18.5966 * scaleX, 8.81517 * scaleY,
      17.4512 * scaleX, 8.33203 * scaleY,
    );
    path.cubicTo(
      16.2643 * scaleX, 7.83061 * scaleY,
      15.0028 * scaleX, 7.57617 * scaleY,
      13.7021 * scaleX, 7.57617 * scaleY,
    );
    path.cubicTo(
      13.2665 * scaleX, 7.57605 * scaleY,
      12.9141 * scaleX, 7.22377 * scaleY,
      12.9141 * scaleX, 6.78809 * scaleY,
    );
    path.cubicTo(
      12.9142 * scaleX, 6.35251 * scaleY,
      13.2666 * scaleX, 6.00013 * scaleY,
      13.7021 * scaleX, 6 * scaleY,
    );
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
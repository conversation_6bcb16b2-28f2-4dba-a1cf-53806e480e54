import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// Ultra-lightweight ML Kit configuration for optimal hardware performance
class LightweightMLConfig {
  
  /// ULTRA-FAST face detection options - minimal processing
  static final FaceDetectorOptions ultraFast = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.fast,        // Fastest mode
    enableTracking: true,                         // No tracking overhead
    enableLandmarks: false,                        // No landmark detection
    enableContours: false,                         // No contour detection
    enableClassification: false,                   // No smile/eyes classification
    minFaceSize: 0.3,                             // 30% min face size (less processing)
  );

  /// BALANCED performance - slightly more features but still lightweight
  static final FaceDetectorOptions balanced = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.fast,
    enableTracking: true,                         // Still no tracking
    enableLandmarks: false,                        // Still no landmarks
    enableContours: false,                         // Still no contours
    enableClassification: false,                   // Still no classification
    minFaceSize: 0.2,                             // 20% min face size
  );

  /// MINIMAL features - only basic face detection
  static final FaceDetectorOptions minimal = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.fast,
    enableTracking: false,
    enableLandmarks: false,
    enableContours: false,
    enableClassification: false,
    minFaceSize: 0.15,                            // 15% min face size
  );

  /// TELPO F8 TERMINAL optimized configuration
  static final FaceDetectorOptions telpoF8Optimized = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.fast,        // Fast but not ultra-fast for better accuracy
    enableTracking: true,                          // ENABLE tracking for F8 - better face continuity
    enableLandmarks: false,                        // Still no landmarks
    enableContours: false,                         // Still no contours
    enableClassification: false,                   // Still no classification
    minFaceSize: 0.2,                             // 20% min face - good balance for F8
  );

  /// Power saving mode (exit on touch)
  static final FaceDetectorOptions powerSavingMode = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.fast,
    enableTracking: false,
    enableLandmarks: false,
    enableContours: false,
    enableClassification: false,
    minFaceSize: 0.35,                            // Larger faces only for power saving
  );

  /// Get recommended config based on hardware capability
  static FaceDetectorOptions getRecommendedConfig({
    bool isLowEndDevice = false,
    bool prioritizeSpeed = true,
    bool isTelpoF8 = false,
    bool isPowerSaving = false,
  }) {
    if (isPowerSaving) {
      return powerSavingMode;
    } else if (isTelpoF8) {
      return telpoF8Optimized;
    } else if (isLowEndDevice || prioritizeSpeed) {
      return ultraFast;
    } else {
      return balanced;
    }
  }

  /// Performance optimization tips
  static const List<String> optimizationTips = [
    '🚀 Use ResolutionPreset.low for camera (480p)',
    '🎯 Set minFaceSize to 0.3 for faster detection',
    '⚡ Disable all advanced features (tracking, landmarks, contours)',
    '📱 Use NV21 format for best Android compatibility',
    '🔄 Process every 2nd or 3rd frame instead of every frame',
    '💾 Use buffer pooling to reduce memory allocations',
    '🎮 Consider frame skipping during heavy processing',
  ];

  /// TELPO F8 Terminal specific recommendations
  static Map<String, dynamic> getTelpoF8Recommendations() {
    return {
      'camera': {
        'resolution': 'ResolutionPreset.medium (720p) - F8 can handle it',
        'format': 'ImageFormatGroup.nv21',
        'fps': '20-25 FPS optimal for F8',
        'pauseStreamForCapture': false,
      },
      'mlkit': {
        'performanceMode': 'FaceDetectorMode.fast',
        'enableTracking': true, // ENABLED for F8 - better face continuity
        'enableLandmarks': false,
        'enableContours': false,
        'enableClassification': false,
        'minFaceSize': 0.2, // 20% for good balance on F8
      },
      'processing': {
        'frameSkipping': 'Every 2nd frame (normal), Every 5th frame (power saving)',
        'powerSaving': 'Auto-enter after 30s, exit on touch',
        'fpsMonitoring': 'Real-time display',
        'touchDetection': 'Full screen touch detection',
      },
      'telpoF8Specific': {
        'screenSize': '8 inch optimal viewing distance: 50-80cm',
        'processingPower': 'Good - can handle 720p + fast ML processing',
        'powerManagement': 'Adaptive frame skipping based on touch activity',
        'userInterface': 'Touch to wake from power saving mode',
      }
    };
  }

  /// Hardware-specific recommendations (general)
  static Map<String, dynamic> getHardwareRecommendations() {
    return {
      'camera': {
        'resolution': 'ResolutionPreset.low (480p)',
        'format': 'ImageFormatGroup.nv21',
        'fps': '15-20 FPS max',
        'pauseStreamForCapture': false,
      },
      'mlkit': {
        'performanceMode': 'FaceDetectorMode.fast',
        'enableTracking': false,
        'enableLandmarks': false,
        'enableContours': false,
        'enableClassification': false,
        'minFaceSize': 0.3,
      },
      'processing': {
        'frameSkipping': 'Process every 2nd frame',
        'bufferPooling': 'Enable for memory efficiency',
        'imageResizing': 'Disable to avoid YUV corruption',
        'concurrentProcessing': 'Disable to reduce CPU load',
      },
      'memory': {
        'maxBufferSize': '1MB per buffer',
        'poolSize': '3-5 buffers max',
        'garbageCollection': 'Manual cleanup after processing',
      }
    };
  }

  /// Debug configuration for troubleshooting
  static final FaceDetectorOptions debug = FaceDetectorOptions(
    performanceMode: FaceDetectorMode.accurate,    // More accurate for debugging
    enableTracking: false,                         // Still keep disabled
    enableLandmarks: false,                        // Still keep disabled
    enableContours: false,                         // Still keep disabled
    enableClassification: false,                   // Still keep disabled
    minFaceSize: 0.1,                             // Lower threshold for debugging
  );

  /// Get configuration summary
  static String getConfigSummary(FaceDetectorOptions options) {
    return '''
🔧 ML Kit Configuration:
   Performance: ${options.performanceMode}
   Tracking: ${options.enableTracking}
   Landmarks: ${options.enableLandmarks}
   Contours: ${options.enableContours}
   Classification: ${options.enableClassification}
   Min Face Size: ${(options.minFaceSize * 100).toInt()}%
   
💡 Optimization Level: ${_getOptimizationLevel(options)}
''';
  }

  static String _getOptimizationLevel(FaceDetectorOptions options) {
    if (options.performanceMode == FaceDetectorMode.fast &&
        !options.enableTracking &&
        !options.enableLandmarks &&
        !options.enableContours &&
        !options.enableClassification &&
        options.minFaceSize >= 0.3) {
      return 'ULTRA-LIGHTWEIGHT ⚡';
    } else if (options.performanceMode == FaceDetectorMode.fast &&
               !options.enableTracking &&
               !options.enableLandmarks &&
               !options.enableContours &&
               !options.enableClassification) {
      return 'LIGHTWEIGHT 🚀';
    } else {
      return 'STANDARD 📱';
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

// Shared dependencies
import '../../../shared/core/di/shared_service_locator.dart';

// Mobile-specific modules
import 'modules/mobile_navigation_module.dart';
import 'modules/mobile_ui_module.dart';
import 'modules/mobile_camera_module.dart';

// Mobile configuration
import '../../../shared/core/config/app_config_factory.dart';
import '../../../shared/core/config/base_app_config.dart';

/// Mobile App Service Locator
/// 
/// This class manages mobile-specific dependencies and integrates
/// with shared dependencies. It provides mobile app-specific services
/// while leveraging shared core functionality.
/// 
/// Usage:
/// ```dart
/// // In mobile main.dart
/// await MobileServiceLocator.setupMobileDependencies();
/// ```
class MobileServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  static bool _isMobileInitialized = false;

  /// Setup mobile-specific dependencies
  /// 
  /// This method first ensures shared dependencies are initialized,
  /// then registers mobile-specific services.
  static Future<void> setupMobileDependencies() async {
    if (_isMobileInitialized) {
      if (kDebugMode) {
        print('⚠️ Mobile dependencies already initialized');
      }
      return;
    }

    if (kDebugMode) {
      print('📱 Setting up mobile dependencies...');
    }

    // ============================================================================
    // SETUP CONFIGURATION FIRST
    // ============================================================================
    if (!AppConfigFactory.isInitialized) {
      AppConfigFactory.initialize(AppType.mobile);
    }

    // ============================================================================
    // SETUP SHARED DEPENDENCIES
    // ============================================================================
    await SharedServiceLocator.setupSharedDependencies();

    // ============================================================================
    // REGISTER MOBILE-SPECIFIC MODULES
    // ============================================================================

    // 1. Mobile Navigation Module
    registerMobileNavigationDependencies();

    // 2. Mobile UI Module
    registerMobileUIDependencies();

    // 3. Mobile Camera Module
    registerMobileCameraDependencies();

    _isMobileInitialized = true;

    // Log successful setup
    _logMobileDependencyStatus();
  }

  /// Reset mobile dependencies (useful for testing)
  static Future<void> resetMobileDependencies() async {
    if (kDebugMode) {
      print('🔄 Resetting mobile dependencies...');
    }

    // Unregister mobile modules in reverse order
    unregisterMobileCameraDependencies();
    unregisterMobileUIDependencies();
    unregisterMobileNavigationDependencies();

    // Reset shared dependencies
    await SharedServiceLocator.resetSharedDependencies();

    _isMobileInitialized = false;

    if (kDebugMode) {
      print('✅ Mobile dependencies reset complete');
    }
  }

  /// Check if mobile dependencies are initialized
  static bool get isInitialized => _isMobileInitialized && SharedServiceLocator.isInitialized;

  /// Get mobile dependency status for debugging
  static Map<String, dynamic> getMobileDependencyStatus() {
    return {
      'mobile_initialized': _isMobileInitialized,
      'shared_status': SharedServiceLocator.getSharedDependencyStatus(),
      'mobile_modules': {
        'navigation': areMobileNavigationDependenciesRegistered(),
        'ui': areMobileUIDependenciesRegistered(),
        'camera': areMobileCameraDependenciesRegistered(),
      },
    };
  }

  /// Log mobile dependency registration status for debugging
  static void _logMobileDependencyStatus() {
    if (kDebugMode) {
      print('📱 Mobile Service Locator Setup Complete');
      print('📊 Mobile Dependency Status:');

      final status = getMobileDependencyStatus();
      final mobileModules = status['mobile_modules'] as Map<String, bool>;
      
      mobileModules.forEach((key, value) {
        final icon = value ? '✅' : '❌';
        if (kDebugMode) {
          print('  Mobile ${key.toUpperCase()}: $icon');
        }
      });
    }
  }

  /// Clean up mobile resources when app is disposed
  static Future<void> disposeMobileDependencies() async {
    if (kDebugMode) {
      print('🧹 Disposing mobile dependencies...');
    }

    try {
      // Dispose mobile-specific resources
      // Note: Add specific disposal logic when mobile services are implemented

      // Dispose shared dependencies
      await SharedServiceLocator.disposeSharedDependencies();
      
      _isMobileInitialized = false;

      if (kDebugMode) {
        print('✅ Mobile dependencies disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error disposing mobile dependencies: $e');
      }
      rethrow;
    }
  }

  /// Validate that all required mobile dependencies are registered
  static bool validateMobileDependencies() {
    final sharedValid = SharedServiceLocator.validateSharedDependencies();
    final mobileStatus = getMobileDependencyStatus();
    final mobileModules = mobileStatus['mobile_modules'] as Map<String, bool>;
    final mobileValid = mobileModules.values.every((isRegistered) => isRegistered);

    if (kDebugMode) {
      if (sharedValid && mobileValid) {
        print('✅ All mobile dependencies validated successfully');
      } else {
        print('❌ Some mobile dependencies are missing');
        if (!sharedValid) {
          print('  - Shared dependencies validation failed');
        }
        if (!mobileValid) {
          mobileModules.forEach((key, value) {
            if (!value) {
              if (kDebugMode) {
                print('  - Mobile $key: MISSING');
              }
            }
          });
        }
      }
    }

    return sharedValid && mobileValid;
  }

  /// Get GetIt instance for mobile dependencies
  static GetIt get instance => _getIt;
}

/// Mobile dependency configuration
class MobileDependencyConfig {
  /// Mobile-specific configuration
  static bool get enableMobileAnalytics => !kDebugMode;
  static bool get enableMobileCrashReporting => !kDebugMode;
  
  /// Mobile UI configuration
  static const Duration mobileAnimationDuration = Duration(milliseconds: 300);
  static const Duration mobilePageTransitionDuration = Duration(milliseconds: 250);
  
  /// Mobile camera configuration
  static const Duration mobileCameraTimeout = Duration(seconds: 10);
  static const int mobileCameraQuality = 85;
  
  /// Mobile navigation configuration
  static const bool enableMobileDeepLinking = true;
  static const bool enableMobileRouteLogging = true;
}

/// Mixin for mobile classes that need access to dependencies
mixin MobileDependencyMixin {
  /// Get mobile GetIt instance
  GetIt get mobileDI => MobileServiceLocator.instance;
  
  /// Check if mobile dependencies are ready
  bool get areMobileDependenciesReady => MobileServiceLocator.isInitialized;
  
  /// Validate mobile dependencies before use
  void ensureMobileDependenciesReady() {
    if (!areMobileDependenciesReady) {
      throw StateError(
        'Mobile dependencies not initialized. Call MobileServiceLocator.setupMobileDependencies() first.',
      );
    }
  }
}

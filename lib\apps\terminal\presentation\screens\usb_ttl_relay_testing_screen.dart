import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:usb_serial/usb_serial.dart';
import '../../../../shared/services/relay_management_service.dart';
import 'package:relay_controller/relay_controller.dart';

class UsbTtlRelayTestingScreen extends StatefulWidget {
  final bool showAppBar;

  const UsbTtlRelayTestingScreen({super.key, this.showAppBar = true});

  @override
  State<UsbTtlRelayTestingScreen> createState() =>
      _UsbTtlRelayTestingScreenState();
}

class _UsbTtlRelayTestingScreenState extends State<UsbTtlRelayTestingScreen> {
  // Relay service
  final RelayManagementService _relayService = RelayManagementService.instance;

  // USB device state
  List<UsbDevice> _availableDevices = [];
  UsbDevice? _selectedDevice;
  bool _isScanning = false;
  bool _isConnecting = false;
  bool _isConnected = false;

  // Configuration
  int _baudRate = 9600;
  String _commandTerminator = '\n';
  String _deviceProfile = 'esp32';

  // Controllers
  final TextEditingController _customCommandController =
      TextEditingController();

  // Direct USB Serial
  UsbPort? _usbPort;

  // Test logs
  List<String> _testLogs = [];

  @override
  void dispose() {
    _customCommandController.dispose();
    _closeUsbPort();
    super.dispose();
  }

  void _addLog(String message) {
    setState(() {
      _testLogs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
  }

  // Status helper methods
  MaterialColor _getStatusColor() {
    if (_selectedDevice == null) return Colors.orange;
    if (_isConnecting) return Colors.blue;
    if (_isConnected) return Colors.green;
    return Colors.grey;
  }

  IconData _getStatusIcon() {
    if (_selectedDevice == null) return Icons.warning;
    if (_isConnecting) return Icons.sync;
    if (_isConnected) return Icons.check_circle;
    return Icons.usb;
  }

  String _getStatusText() {
    if (_selectedDevice == null) return 'No USB-TTL device selected';
    if (_isConnecting) return 'Connecting to ${_selectedDevice!.deviceName}...';
    if (_isConnected) return 'Connected: ${_selectedDevice!.deviceName}';
    return 'Device selected: ${_selectedDevice!.deviceName}';
  }

  @override
  Widget build(BuildContext context) {
    final body = SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Device Selection Section
          _buildDeviceSelection(),

          const SizedBox(height: 20),

          // Configuration Section
          _buildConfiguration(),

          const SizedBox(height: 20),

          // Quick Control Buttons
          _buildQuickControls(),

          const SizedBox(height: 20),

          // Custom Command Section
          _buildCustomCommand(),

          const SizedBox(height: 20),

          // Test Logs
          _buildTestLogs(),
        ],
      ),
    );

    if (widget.showAppBar) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('USB-TTL Relay Testing'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: body,
      );
    } else {
      return body;
    }
  }

  Widget _buildDeviceSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Device Selection',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 16),

            // Device Status
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getStatusColor().shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getStatusColor()),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        _getStatusIcon(),
                        color: _getStatusColor(),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getStatusText(),
                          style: TextStyle(
                            color: _getStatusColor().shade800,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Connection Details
                  if (_selectedDevice != null && _usbPort != null) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.settings,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Config: $_baudRate baud, $_deviceProfile profile',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Scan Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isScanning ? null : _scanForUsbDevices,
                icon: _isScanning
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
                label: Text(_isScanning ? 'Scanning...' : 'Rescan USB Devices'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Device Dropdown
            if (_availableDevices.isNotEmpty) ...[
              Text(
                'Available Devices (${_availableDevices.length})',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),

              DropdownButton<UsbDevice>(
                value: _selectedDevice,
                isExpanded: true,
                items: _availableDevices.map((device) {
                  final vidPid = (device.vid != null && device.pid != null)
                      ? ' [${device.vid!.toRadixString(16).toUpperCase()}:${device.pid!.toRadixString(16).toUpperCase()}]'
                      : '';
                  return DropdownMenuItem<UsbDevice>(
                    value: device,
                    child: Text('${device.deviceName}$vidPid'),
                  );
                }).toList(),
                onChanged: (device) {
                  setState(() {
                    _selectedDevice = device;
                    _isConnected =
                        false; // Reset connection when device changes
                  });
                  _closeUsbPort(); // Close previous connection
                  _addLog('📱 Selected device: ${device?.deviceName}');
                },
              ),

              const SizedBox(height: 16),

              // Connect/Disconnect Button
              if (_selectedDevice != null)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isConnecting
                        ? null
                        : (_isConnected ? _disconnectDevice : _connectDevice),
                    icon: _isConnecting
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(_isConnected ? Icons.link_off : Icons.link),
                    label: Text(
                      _isConnecting
                          ? 'Connecting...'
                          : (_isConnected
                                ? 'Disconnect Device'
                                : 'Connect Device'),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isConnected ? Colors.red : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConfiguration() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuration',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 16),

            // Configuration in compact layout
            Column(
              children: [
                // Baud Rate Selection
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Baud Rate:',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: DropdownButton<int>(
                        value: _baudRate,
                        isExpanded: true,
                        isDense: true,
                        items: const [
                          DropdownMenuItem(
                            value: 9600,
                            child: Text('9600 (Rec.)'),
                          ),
                          DropdownMenuItem(value: 19200, child: Text('19200')),
                          DropdownMenuItem(value: 38400, child: Text('38400')),
                          DropdownMenuItem(value: 57600, child: Text('57600')),
                          DropdownMenuItem(
                            value: 115200,
                            child: Text('115200'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _baudRate = value!;
                          });
                          _addLog('⚙️ Baud rate changed to: $value');
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Device Profile Selection
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        'Profile:',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: DropdownButton<String>(
                        value: _deviceProfile,
                        isExpanded: true,
                        isDense: true,
                        items: const [
                          DropdownMenuItem(
                            value: 'esp32',
                            child: Text('ESP32 (R0:1)'),
                          ),
                          DropdownMenuItem(
                            value: 'arduino',
                            child: Text('Arduino (REL_0_ON)'),
                          ),
                          DropdownMenuItem(
                            value: 'simple',
                            child: Text('Simple (01)'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _deviceProfile = value!;
                            // Auto-set terminator based on profile
                            switch (value) {
                              case 'esp32':
                                _commandTerminator = '\n';
                                break;
                              case 'arduino':
                                _commandTerminator = '\r\n';
                                break;
                              case 'simple':
                                _commandTerminator = '\n';
                                break;
                            }
                          });
                          _addLog('⚙️ Device profile changed to: $value');
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Controls',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 16),

            // Individual Relay Controls
            Text(
              'Individual Relays:',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),

            // Safe Wrap layout instead of GridView
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                for (int i = 0; i < 4; i++) ...[
                  SizedBox(
                    width: 70,
                    child: ElevatedButton(
                      onPressed: () => _sendRelayCommand(i, true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      child: Text(
                        'R$i\nON',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 70,
                    child: ElevatedButton(
                      onPressed: () => _sendRelayCommand(i, false),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      child: Text(
                        'R$i\nOFF',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 16),

            // All Relays Controls
            Text('All Relays:', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _sendAllRelaysCommand(true),
                    icon: const Icon(Icons.power_settings_new),
                    label: const Text('Turn All ON'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _sendAllRelaysCommand(false),
                    icon: const Icon(Icons.power_off),
                    label: const Text('Turn All OFF'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCommand() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Custom Command',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 16),

            TextField(
              controller: _customCommandController,
              decoration: const InputDecoration(
                labelText: 'Enter custom command',
                hintText: 'R0:1',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.terminal),
              ),
            ),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _sendCustomCommand,
                icon: const Icon(Icons.send),
                label: const Text('Send Custom Command'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestLogs() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Test Logs',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(
                  width: 80,
                  child: TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _testLogs.clear();
                      });
                    },
                    icon: const Icon(Icons.clear, size: 16),
                    label: const Text('Clear', style: TextStyle(fontSize: 12)),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Container(
              height: 200,
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade400),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _testLogs.isEmpty
                      ? '💡 No logs yet. Start testing to see logs here.\n\n'
                            '� TIP: Upload ESP32 code below for response verification.'
                      : _testLogs.join('\n'),
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: Colors.greenAccent,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // ESP32 Code Helper
            _buildEsp32CodeHelper(),
          ],
        ),
      ),
    );
  }

  Widget _buildEsp32CodeHelper() {
    return Card(
      child: ExpansionTile(
        leading: Icon(Icons.code, color: Colors.blue.shade700),
        title: Text(
          'ESP32 Response Code',
          style: TextStyle(
            color: Colors.blue.shade700,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: const Text(
          'Click to view/copy ESP32 code for response verification',
        ),
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        'ESP32 Arduino Code:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 70,
                      child: TextButton.icon(
                        onPressed: () {
                          // Copy to clipboard functionality can be added here
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('💡 Copy this code to Arduino IDE'),
                              backgroundColor: Colors.blue,
                            ),
                          );
                        },
                        icon: const Icon(Icons.copy, size: 16),
                        label: const Text(
                          'Copy',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Safe container with constrained height
                Container(
                  height: 200,
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade900,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey.shade600),
                  ),
                  child: SingleChildScrollView(
                    child: SelectableText(
                      '''void setup() {
  Serial.begin(9600);
  pinMode(2, OUTPUT); // Relay pin
  Serial.println("ESP32 Ready");
}

void loop() {
  if (Serial.available()) {
    String cmd = Serial.readStringUntil('\\n');
    cmd.trim();

    if (cmd == "R0:1") {
      digitalWrite(2, HIGH);
      Serial.println("R0:1 OK");
    } else if (cmd == "R0:0") {
      digitalWrite(2, LOW);
      Serial.println("R0:0 OK");
    } else if (cmd == "ALL:1") {
      digitalWrite(2, HIGH);
      Serial.println("ALL:1 OK");
    } else if (cmd == "ALL:0") {
      digitalWrite(2, LOW);
      Serial.println("ALL:0 OK");
    } else {
      Serial.println("Unknown: " + cmd);
    }
  }
}''',
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 11,
                        color: Colors.cyanAccent,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '💡 This code will send responses back to the app, allowing you to verify command delivery.',
                    style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Implementation methods
  Future<void> _scanForUsbDevices() async {
    setState(() {
      _isScanning = true;
    });

    _addLog('🔍 Scanning for USB devices...');

    try {
      final devices = await UsbTtlRelayController.getAvailableDevices();

      setState(() {
        _availableDevices = devices;
        _selectedDevice = devices.isNotEmpty ? devices.first : null;
      });

      _addLog('📱 Found ${devices.length} USB device(s)');

      if (devices.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'No USB devices found. Please connect a USB-TTL device.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        for (int i = 0; i < devices.length; i++) {
          final device = devices[i];
          _addLog('  ${i + 1}. ${device.deviceName}');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Found ${devices.length} USB device(s)'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      _addLog('❌ USB scan failed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('USB scan failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  Future<void> _sendRelayCommand(int relayIndex, bool turnOn) async {
    if (_selectedDevice == null) {
      _showDeviceNotSelectedError();
      return;
    }

    if (!_isConnected) {
      _showDeviceNotConnectedError();
      return;
    }

    String command;
    switch (_deviceProfile) {
      case 'esp32':
        command = 'R$relayIndex:${turnOn ? '1' : '0'}';
        break;
      case 'arduino':
        command = 'REL_${relayIndex}_${turnOn ? 'ON' : 'OFF'}';
        break;
      case 'simple':
        command = '$relayIndex${turnOn ? '1' : '0'}';
        break;
      default:
        command = 'R$relayIndex:${turnOn ? '1' : '0'}';
    }

    await _sendCommand(command);
  }

  Future<void> _sendAllRelaysCommand(bool turnOn) async {
    if (_selectedDevice == null) {
      _showDeviceNotSelectedError();
      return;
    }

    if (!_isConnected) {
      _showDeviceNotConnectedError();
      return;
    }

    String command;
    switch (_deviceProfile) {
      case 'esp32':
        command = 'ALL:${turnOn ? '1' : '0'}';
        break;
      case 'arduino':
        command = 'ALL_${turnOn ? 'ON' : 'OFF'}';
        break;
      case 'simple':
        command = 'A${turnOn ? '1' : '0'}';
        break;
      default:
        command = 'ALL:${turnOn ? '1' : '0'}';
    }

    await _sendCommand(command);
  }

  Future<void> _sendCustomCommand() async {
    final command = _customCommandController.text.trim();
    if (command.isEmpty) {
      _addLog('❌ No command entered');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a command'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedDevice == null) {
      _showDeviceNotSelectedError();
      return;
    }

    if (!_isConnected) {
      _showDeviceNotConnectedError();
      return;
    }

    await _sendCommand(command);
    _customCommandController.clear();
  }

  Future<void> _sendCommand(String command) async {
    final commandWithTerminator = command.endsWith(_commandTerminator)
        ? command
        : '$command$_commandTerminator';

    _addLog(
      '📤 Sending: "$commandWithTerminator" to ${_selectedDevice!.deviceName}',
    );

    try {
      // Use direct USB serial communication for better control
      await _sendCommandDirectly(commandWithTerminator);
    } catch (e) {
      _addLog('❌ Failed to send command: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send command: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendCommandDirectly(String command) async {
    // Check if device is connected
    if (!_isConnected || _usbPort == null) {
      throw Exception('Device not connected. Please connect first.');
    }

    try {
      // Send command
      final data = Uint8List.fromList(command.codeUnits);
      _addLog('📤 Transmitting ${data.length} bytes: $command');

      await _usbPort!.write(data);
      _addLog('📤 Command transmitted to USB port');

      _addLog('✅ Command sent successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Command "$command" sent (${data.length} bytes)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _addLog('❌ Direct send failed: $e');
      rethrow;
    }
  }

  void _startListeningForResponses() {
    _addLog('👂 Started listening for ESP32 responses...');

    _usbPort!.inputStream?.listen(
      (data) {
        if (data.isNotEmpty) {
          final response = String.fromCharCodes(data).trim();
          if (response.isNotEmpty) {
            _addLog('📥 ESP32 Response: "$response"');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('📥 ESP32: $response'),
                  backgroundColor: Colors.blue,
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          }
        }
      },
      onError: (error) {
        _addLog('❌ Response listening error: $error');
      },
    );
  }

  Future<void> _connectDevice() async {
    if (_selectedDevice == null) return;

    setState(() {
      _isConnecting = true;
    });

    _addLog(
      '🔗 Requesting permission and connecting to ${_selectedDevice!.deviceName}...',
    );

    try {
      // Request permission first (permission is handled automatically by create/open)
      _addLog('🔑 Requesting device permission...');

      // Create and open port
      _usbPort = await _selectedDevice!.create();
      if (_usbPort == null) {
        throw Exception('Failed to create USB port');
      }

      final openResult = await _usbPort!.open();
      if (!openResult) {
        throw Exception('Failed to open USB port');
      }

      // Configure port
      await _usbPort!.setDTR(true);
      await _usbPort!.setRTS(true);
      await _usbPort!.setPortParameters(
        _baudRate,
        UsbPort.DATABITS_8,
        UsbPort.STOPBITS_1,
        UsbPort.PARITY_NONE,
      );

      _addLog('🔧 USB port configured: $_baudRate baud, 8N1');

      // Start listening for responses
      _startListeningForResponses();

      setState(() {
        _isConnected = true;
      });

      _addLog('✅ Successfully connected to ${_selectedDevice!.deviceName}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Connected to ${_selectedDevice!.deviceName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _addLog('❌ Connection failed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Connection failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Cleanup on failure
      await _closeUsbPort();
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _disconnectDevice() async {
    _addLog('🔌 Disconnecting from device...');

    await _closeUsbPort();

    setState(() {
      _isConnected = false;
    });

    _addLog('✅ Device disconnected');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Device disconnected'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  Future<void> _closeUsbPort() async {
    if (_usbPort != null) {
      try {
        await _usbPort!.close();
        _usbPort = null;
        _addLog('🔌 USB port closed');
      } catch (e) {
        _addLog('❌ Error closing USB port: $e');
      }
    }
  }

  void _showDeviceNotSelectedError() {
    _addLog('❌ No USB device selected');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Please scan and select a USB device first'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showDeviceNotConnectedError() {
    _addLog('❌ Device not connected');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Please connect to the device first'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

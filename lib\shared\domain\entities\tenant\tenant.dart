/// Tenant domain entity
/// 
/// Represents a tenant/organization in the system
class Tenant {
  final String id;
  final String name;
  final String? address;
  final String? unitId;
  final String createdBy;
  final DateTime createdAt;
  final List<String> mappings;
  final DateTime? updatedAt;

  const Tenant({
    required this.id,
    required this.name,
    this.address,
    this.unitId,
    required this.createdBy,
    required this.createdAt,
    this.mappings = const [],
    this.updatedAt,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tenant && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Tenant(id: $id, name: $name, address: $address)';
  }
}

/// Utilities Index - Export all utility classes
///
/// This file exports all utility classes for common operations like
/// date formatting, string manipulation, validation, and logging.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/utils/index.dart';
/// 
/// // Date utilities
/// final formatted = AppDateUtils.formatDisplayDate(DateTime.now());
/// final age = AppDateUtils.calculateAge(birthDate);
/// 
/// // String utilities
/// final capitalized = StringUtils.capitalize('hello world');
/// final masked = StringUtils.maskEmail('<EMAIL>');
/// 
/// // Validation utilities
/// final emailError = ValidationUtils.validateEmail('invalid-email');
/// final passwordError = ValidationUtils.validatePassword('weak');
/// 
/// // Logging
/// Logger.i('Tag', 'Info message');
/// Logger.e('Tag', 'Error message', error, stackTrace);
/// ```

// ============================================================================
// UTILITIES EXPORTS
// ============================================================================

/// Date formatting and manipulation utilities
export 'date_utils.dart';

/// Logging utilities for debugging
export 'logger.dart';

/// String processing and manipulation utilities
export 'string_utils.dart';

/// Comprehensive validation utilities
export 'validation_utils.dart';

import '../core/interfaces/face_recognition_service.dart';
import '../core/models/face_recognition_config.dart';
import '../shared/services/network_detection_service.dart';
import '../shared/services/online_recognition_service.dart';
import '../shared/services/offline_recognition_service.dart';
import 'hybrid_recognition_service.dart';

/// Factory for creating face recognition services
class RecognitionServiceFactory {
  /// Create a recognition service based on mode
  static FaceRecognitionService create({
    required RecognitionMode mode,
    required FaceRecognitionConfig config,
    required NetworkDetectionService networkService,
  }) {
    switch (mode) {
      case RecognitionMode.online:
        return OnlineRecognitionServiceAdapter(
          service: OnlineRecognitionService(config: config),
        );
      
      case RecognitionMode.offline:
        return OfflineRecognitionServiceAdapter(
          service: OfflineRecognitionService(config: config),
        );
      
      case RecognitionMode.hybrid:
        return HybridRecognitionService(
          config: config,
          networkService: networkService,
        );
    }
  }
}

/// Adapter for OnlineRecognitionService to match interface
class OnlineRecognitionServiceAdapter implements FaceRecognitionService {
  final OnlineRecognitionService _service;
  
  OnlineRecognitionServiceAdapter({required OnlineRecognitionService service})
      : _service = service;
  
  @override
  Future<void> initialize() => _service.initialize();
  
  @override
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes) async {
    final result = await _service.recognizeFace(faceImageBytes);
    if (result == null) return null;
    
    return RecognitionResult(
      userId: result.userId,
      userName: result.userName,
      confidence: result.confidence,
      source: result.source,
      timestamp: result.timestamp,
      metadata: result.metadata,
    );
  }
  
  @override
  bool get isThrottled => false; // Implement throttling logic
  
  @override
  void setOnlineMode(bool isOnline) {
    // Online service is always online
  }
  
  @override
  String get serviceName => 'Online Recognition';
  
  @override
  bool get isInitialized => true; // Implement proper check
  
  @override
  Future<void> dispose() => _service.dispose();
}

/// Adapter for OfflineRecognitionService to match interface
class OfflineRecognitionServiceAdapter implements FaceRecognitionService {
  final OfflineRecognitionService _service;
  
  OfflineRecognitionServiceAdapter({required OfflineRecognitionService service})
      : _service = service;
  
  @override
  Future<void> initialize() => _service.initialize();
  
  @override
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes) async {
    final result = await _service.recognizeFace(faceImageBytes);
    if (result == null) return null;
    
    return RecognitionResult(
      userId: result.userId,
      userName: result.userName,
      confidence: result.confidence,
      source: result.source,
      timestamp: result.timestamp,
      metadata: result.metadata,
    );
  }
  
  @override
  bool get isThrottled => false; // Implement throttling logic
  
  @override
  void setOnlineMode(bool isOnline) {
    // Offline service ignores online mode
  }
  
  @override
  String get serviceName => 'Offline Recognition';
  
  @override
  bool get isInitialized => true; // Implement proper check
  
  @override
  Future<void> dispose() => _service.dispose();
}

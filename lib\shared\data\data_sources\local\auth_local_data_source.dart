import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../models/auth/auth_result_model.dart';
import '../../models/user/user_model.dart';
import 'dart:convert';

abstract class AuthLocalDataSource {
  Future<void> saveAuthResult(AuthResultModel authResult);
  Future<AuthResultModel?> getAuthResult();
  Future<void> saveAccessToken(String token);
  Future<String?> getAccessToken();
  Future<void> saveRefreshToken(String token);
  Future<String?> getRefreshToken();
  Future<void> saveUser(UserModel user);
  Future<UserModel?> getUser();
  Future<void> clearAuthData();
  Future<bool> isAuthenticated();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorage secureStorage;

  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_data';
  static const String _authResultKey = 'auth_result';
  static const String _expiresAtKey = 'expires_at';

  AuthLocalDataSourceImpl({required this.secureStorage});

  @override
  Future<void> saveAuthResult(AuthResultModel authResult) async {
    await Future.wait([
      saveAccessToken(authResult.accessToken),
      if (authResult.refreshToken != null)
        saveRefreshToken(authResult.refreshToken!),
      if (authResult.user != null)
        saveUser(authResult.user!),
      if (authResult.expiresAt != null)
        secureStorage.write(
          key: _expiresAtKey,
          value: authResult.expiresAt!.toIso8601String(),
        ),
      secureStorage.write(
        key: _authResultKey,
        value: jsonEncode(authResult.toJson()),
      ),
    ]);
  }

  @override
  Future<AuthResultModel?> getAuthResult() async {
    try {
      final authResultJson = await secureStorage.read(key: _authResultKey);
      if (authResultJson != null) {
        final authResultMap = jsonDecode(authResultJson) as Map<String, dynamic>;
        return AuthResultModel.fromJson(authResultMap);
      }
      return null;
    } catch (e) {
      // If there's an error reading the auth result, return null
      return null;
    }
  }

  @override
  Future<void> saveAccessToken(String token) async {
    await secureStorage.write(key: _accessTokenKey, value: token);
  }

  @override
  Future<String?> getAccessToken() async {
    return await secureStorage.read(key: _accessTokenKey);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    await secureStorage.write(key: _refreshTokenKey, value: token);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await secureStorage.read(key: _refreshTokenKey);
  }

  @override
  Future<void> saveUser(UserModel user) async {
    await secureStorage.write(
      key: _userKey,
      value: jsonEncode(user.toJson()),
    );
  }

  @override
  Future<UserModel?> getUser() async {
    try {
      final userJson = await secureStorage.read(key: _userKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      }
      return null;
    } catch (e) {
      // If there's an error reading the user, return null
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    await Future.wait([
      secureStorage.delete(key: _accessTokenKey),
      secureStorage.delete(key: _refreshTokenKey),
      secureStorage.delete(key: _userKey),
      secureStorage.delete(key: _authResultKey),
      secureStorage.delete(key: _expiresAtKey),
    ]);
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return false;

      final expiresAtString = await secureStorage.read(key: _expiresAtKey);
      if (expiresAtString == null) return false;

      final expiresAt = DateTime.tryParse(expiresAtString);
      if (expiresAt == null) return false;

      // Check if token is not expired
      return DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }
}

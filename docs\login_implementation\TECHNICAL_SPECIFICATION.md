# Đặc T<PERSON> Kỹ Thuật - <PERSON><PERSON><PERSON>ăng Đăng Nhập

## 🏗️ Architecture Overview

### Clean Architecture Pattern
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Login Screen   │    │  Auth Provider  │                │
│  │                 │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Login UseCase  │    │ Auth Repository │                │
│  │                 │    │   (Interface)   │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │Auth Repository  │    │ Remote Data     │                │
│  │ Implementation  │    │    Source       │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Component Specifications

### 1. Login Screen (`login_screen.dart`)

#### Current State
```dart
class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _serverAddressController = TextEditingController();
  String _selectedToggle = 'On Cloud';
  bool _isLoading = false;
}
```

#### Required Enhancements
```dart
class _LoginScreenState extends State<LoginScreen> {
  // Existing fields...
  
  // New additions
  late AuthProvider _authProvider;
  
  @override
  void initState() {
    super.initState();
    _authProvider = context.read<AuthProvider>();
  }
  
  void _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    
    // Dynamic base URL logic
    String baseUrl = _selectedToggle == 'On Cloud' 
        ? ApiEndpoints.getBaseUrlByEnvironment(AppConfig.environment)
        : _serverAddressController.text;
    
    // Update API client configuration
    await ServiceLocator().updateBaseUrl(baseUrl);
    
    // Perform login
    final success = await _authProvider.login(
      userName: _usernameController.text,
      password: _passwordController.text,
    );
    
    if (success) {
      if (mounted) goToDashboard();
    }
  }
}
```

### 2. Auth Provider (`auth_provider.dart`)

#### Target Implementation
```dart
class AuthProvider extends BaseAuthProvider {
  AuthProvider({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
  }) : super(
    loginUseCase: loginUseCase,
    logoutUseCase: logoutUseCase,
  );
  
  @override
  Future<bool> login({
    required String userName,
    required String password,
  }) async {
    setAuthStatus(AuthStatus.loading);
    
    try {
      final result = await loginUseCase(LoginParams(
        userName: userName,
        password: password,
      ));
      
      return result.fold(
        (failure) {
          handleAuthError(failure);
          return false;
        },
        (authResult) {
          handleLoginSuccess(authResult);
          return true;
        },
      );
    } catch (e) {
      setError('Lỗi không xác định: ${e.toString()}');
      return false;
    }
  }
}
```

### 3. API Endpoints Configuration

#### Current State
```dart
class ApiEndpoints {
  static const String login = '/auth/login';
}
```

#### Required Update
```dart
class ApiEndpoints {
  static const String login = '/api/v3.1/identity/login';
  
  // Dynamic base URL support
  static String getBaseUrlByEnvironment(AppEnvironment environment) {
    switch (environment) {
      case AppEnvironment.development:
        return 'http://192.168.137.1:5000';
      case AppEnvironment.staging:
        return 'https://staging-api.c-faces.com';
      case AppEnvironment.production:
        return 'https://api.c-faces.com';
    }
  }
}
```

### 4. Service Locator Enhancement

#### Required Addition
```dart
class ServiceLocator {
  // Existing code...
  
  Future<void> updateBaseUrl(String newBaseUrl) async {
    // Update HTTP client base URL
    _httpClientService.updateBaseUrl(newBaseUrl);
    
    // Update API client
    final apiClient = _httpClientService.apiClient;
    apiClient.updateBaseUrl(newBaseUrl);
    
    // Notify other services if needed
    await _authService.onBaseUrlChanged(newBaseUrl);
  }
}
```

## 📡 API Integration

### Request Specification
```http
POST {{baseUrl}}/api/v3.1/identity/login
Content-Type: application/json

{
  "username": "string",
  "password": "string"
}
```

### Response Format
```json
{
  "code": "SUCCESS",
  "success": true,
  "statusCode": 200,
  "message": "Success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 899,
    "token_type": "Bearer",
    "user": {
      "id": "uuid-string",
      "username": "admin",
      "name": "System Administrator",
      "email": "<EMAIL>",
      "gender": "other",
      "roles": ["user"],
      "scopes": [],
      "current_tenant_id": "uuid-string"
    }
  }
}
```

### Error Response Format
```json
{
  "code": "INVALID_CREDENTIALS",
  "success": false,
  "statusCode": 401,
  "message": "Invalid username or password",
  "data": null
}
```

## 🔄 Data Flow

### Login Process Flow
```mermaid
sequenceDiagram
    participant UI as Login Screen
    participant P as Auth Provider
    participant UC as Login UseCase
    participant R as Auth Repository
    participant DS as Remote DataSource
    participant API as Backend API
    
    UI->>UI: User enters credentials
    UI->>UI: Select On Cloud/On Premise
    UI->>P: login(username, password)
    P->>UC: call(LoginParams)
    UC->>R: login(username, password)
    R->>DS: login(username, password)
    DS->>API: POST /api/v3.1/identity/login
    API-->>DS: AuthResult
    DS-->>R: AuthResultModel
    R->>R: Save to local storage
    R->>R: Set API client token
    R-->>UC: Either<Failure, AuthResult>
    UC-->>P: Either<Failure, AuthResult>
    P->>P: Update auth state
    P-->>UI: Success/Failure
    UI->>UI: Navigate to dashboard via goToDashboard()
```

## 🛡️ Security Considerations

### Token Management
- Access token lưu trong secure storage
- Automatic token refresh khi gần hết hạn
- Clear tokens khi logout
- Validate token format trước khi sử dụng

### Input Validation
```dart
class LoginValidation {
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập tên đăng nhập';
    }
    if (value.length < 3) {
      return 'Tên đăng nhập phải có ít nhất 3 ký tự';
    }
    return null;
  }
  
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập mật khẩu';
    }
    if (value.length < 6) {
      return 'Mật khẩu phải có ít nhất 6 ký tự';
    }
    return null;
  }
  
  static String? validateServerAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập địa chỉ server';
    }
    
    // Basic URL validation
    final urlPattern = RegExp(r'^https?://[^\s/$.?#].[^\s]*$');
    if (!urlPattern.hasMatch(value)) {
      return 'Địa chỉ server không hợp lệ';
    }
    
    return null;
  }
}
```

## 🎨 UI/UX Specifications

### Loading States
- Show loading indicator trên login button
- Disable form inputs during login
- Show progress message: "Đang đăng nhập..."

### Error Handling
- Display error messages below form
- Use consistent error styling
- Auto-hide errors after 5 seconds
- Specific messages cho từng loại lỗi

### Success States
- Brief success message
- Smooth transition to dashboard screen
- Clear form data after successful login

## 📊 Performance Requirements

### Response Times
- Login request: < 3 seconds
- UI feedback: < 100ms
- Navigation: < 500ms

### Memory Usage
- Minimal memory footprint
- Proper disposal of controllers
- Efficient state management

### Network Optimization
- Request timeout: 30 seconds
- Retry logic: 3 attempts
- Connection pooling
- Request/response compression

## 🧪 Testing Strategy

### Unit Tests
```dart
// Auth Provider Tests
testWidgets('should login successfully with valid credentials', (tester) async {
  // Test implementation
});

testWidgets('should show error with invalid credentials', (tester) async {
  // Test implementation
});

// Use Case Tests
test('should return AuthResult when login succeeds', () async {
  // Test implementation
});

test('should return Failure when login fails', () async {
  // Test implementation
});
```

### Integration Tests
- End-to-end login flow
- Toggle functionality
- Error scenarios
- Network failure handling

### Widget Tests
- Form validation
- UI state changes
- Button interactions
- Navigation behavior

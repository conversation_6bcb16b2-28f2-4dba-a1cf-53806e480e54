import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến Face Detection
/// 
/// Note: This module will be fully implemented when face domain/data layers are migrated
void registerFaceDependencies() {
  // TODO: Implement when face domain and data layers are migrated
  // This is a placeholder to prevent import errors
}

/// Unregister tất cả face dependencies (for testing)
void unregisterFaceDependencies() {
  // TODO: Implement when face dependencies are added
}

/// Reset face module (clear và re-register)
void resetFaceModule() {
  unregisterFaceDependencies();
  registerFaceDependencies();
}

/// Check if face dependencies are registered
bool areFaceDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  return true; // Placeholder
}

/// Get face-related dependencies for debugging
Map<String, bool> getFaceDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
  };
}

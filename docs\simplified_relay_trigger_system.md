# Simplified Relay Trigger System - Immediate ON/OFF

## 🎯 **Design Philosophy**

Đã đơn giản hóa relay trigger system để chỉ thực hiện **immediate ON/OFF** thay vì complex hold time và defer logic.

### Before (Complex)
- ❌ Hold time với timers
- ❌ Defer logic phức tạp
- ❌ Connection checking trước mỗi command
- ❌ Complex error recovery
- ❌ Active trigger tracking với cleanup

### After (Simple)
- ✅ **Immediate ON/OFF**: Trigger ngay lập tức
- ✅ **Best effort**: <PERSON><PERSON> gắng thực hiện, không phức tạp hóa
- ✅ **Minimal error handling**: Chỉ track consecutive failures
- ✅ **No timers**: <PERSON>hông cần quản lý timers
- ✅ **Clean code**: Dễ hiểu và maintain

## 🔧 **Implementation**

### Core Trigger Logic
```dart
Future<void> _executeAction(
  RelayTriggerScenario scenario,
  RelayTriggerAction action,
  Map<String, dynamic>? metadata,
) async {
  try {
    if (kDebugMode) {
      print('🔌 Triggered: ${scenario.name} -> R${action.relayIndex} (${action.relayName})');
    }

    // Simple ON/OFF sequence
    try {
      // Turn ON relay
      await _relayService.controlRelay(action.relayIndex, RelayAction.on);
      
      // Small delay to ensure relay activates (100ms)
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Turn OFF relay immediately
      await _relayService.controlRelay(action.relayIndex, RelayAction.off);

      // Reset failure counter on success
      _consecutiveFailures = 0;

      if (kDebugMode) {
        print('✅ Relay R${action.relayIndex} triggered successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Relay control failed: $e');
      }
      rethrow;
    }

  } catch (e) {
    _consecutiveFailures++;
    
    if (kDebugMode) {
      print('❌ Failed to trigger relay R${action.relayIndex}: $e');
      print('   Consecutive failures: $_consecutiveFailures');
    }
    
    // Only handle connection loss if too many consecutive failures
    if (_consecutiveFailures >= _maxConsecutiveFailures) {
      if (kDebugMode) {
        print('🚨 Too many consecutive failures, checking connection...');
      }
      await _handleConnectionLoss();
    }
  }
}
```

### Emergency Stop (Simplified)
```dart
Future<void> emergencyStop() async {
  if (kDebugMode) {
    print('🚨 EMERGENCY STOP - Turning off all relays immediately');
  }

  // Cancel all timers (if any)
  for (final timer in _activeTriggers.values) {
    timer.cancel();
  }

  // Simple turn off all relays (best effort)
  for (int i = 0; i < 4; i++) {
    try {
      await _relayService.controlRelay(i, RelayAction.off);
      if (kDebugMode) {
        print('✅ Emergency stop: R$i turned off');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Emergency stop failed for R$i: $e');
      }
      // Continue trying other relays even if one fails
    }
  }

  // Clear tracking
  _activeTriggers.clear();
  _activeScenarios.clear();
}
```

## 🚀 **Key Benefits**

### 1. Simplicity
- **No complex state management**: Không cần track active triggers
- **No timers**: Không cần quản lý Timer objects
- **Immediate execution**: ON → delay 100ms → OFF

### 2. Reliability
- **Best effort approach**: Cố gắng thực hiện, không fail hard
- **Minimal dependencies**: Ít phụ thuộc vào connection state
- **Graceful degradation**: Continue hoạt động dù có lỗi

### 3. Performance
- **Fast execution**: Không có delay hay waiting
- **Low memory**: Không cần store timers và state
- **Efficient**: Ít CPU overhead

### 4. Maintainability
- **Clean code**: Dễ đọc và hiểu
- **Less bugs**: Ít logic phức tạp = ít bugs
- **Easy debugging**: Straightforward execution flow

## 📊 **Execution Flow**

```mermaid
graph TD
    A[Trigger Request] --> B[Cancel Existing Timer]
    B --> C[Turn ON Relay]
    C --> D[Wait 100ms]
    D --> E[Turn OFF Relay]
    E --> F[Reset Error Counter]
    F --> G[Success]
    
    C --> H[Error?]
    H --> I[Increment Error Counter]
    I --> J{Too Many Errors?}
    J -->|No| K[Continue]
    J -->|Yes| L[Handle Connection Loss]
```

## 🔄 **Error Handling Strategy**

### Minimal Error Handling
```dart
// Only track consecutive failures
_consecutiveFailures++;

// Only handle connection loss after many failures
if (_consecutiveFailures >= _maxConsecutiveFailures) {
  await _handleConnectionLoss();
}
```

### No Complex Recovery
- ❌ **No connection checking** trước mỗi command
- ❌ **No timeout handling** cho individual commands
- ❌ **No retry logic** cho failed commands
- ✅ **Simple counting** của consecutive failures
- ✅ **Connection loss handling** chỉ khi quá nhiều lỗi

## 🎯 **Usage Examples**

### Basic Trigger
```dart
// Access granted scenario
await relayTriggerService.triggerScenario(
  RelayTriggerScenario.accessGranted,
  metadata: {'user_id': 'user123'},
);

// Result: R1 ON → 100ms delay → R1 OFF
// Log: "🔌 Triggered: Access granted -> R1 (Terminal Door)"
// Log: "✅ Relay R1 triggered successfully"
```

### Multiple Relays
```dart
// Multiple relay scenario
await relayTriggerService.triggerScenario(
  RelayTriggerScenario.multipleRelays,
  metadata: {'action': 'full_access'},
);

// Result: Each relay triggers independently
// R0 ON → 100ms → R0 OFF
// R1 ON → 100ms → R1 OFF
// R2 ON → 100ms → R2 OFF
```

### Emergency Stop
```dart
// Emergency stop all relays
await relayTriggerService.emergencyStop();

// Result: All relays R0-R3 turned OFF immediately
// Best effort - continues even if some fail
```

## 📋 **Configuration Impact**

### Simplified Configuration
```dart
class RelayTriggerAction {
  final int relayIndex;
  final String relayName;
  final String description;
  final int delayMs;        // Still used for initial delay
  // Removed: durationSeconds (not needed)
  // Removed: priority (not needed for simple triggers)
}
```

### Scenario Examples
```dart
static const accessGranted = RelayTriggerScenario(
  name: 'Access granted',
  description: 'Grant access to terminal',
  actions: [
    RelayTriggerAction(
      relayIndex: 1,
      relayName: 'Terminal Door',
      description: 'Unlock terminal door',
      delayMs: 0, // Immediate trigger
    ),
  ],
);
```

## ✅ **Expected Results**

### Performance
- ⚡ **Faster execution**: No waiting for timers
- 🔋 **Lower resource usage**: No timer management
- 📱 **Better responsiveness**: Immediate feedback

### Reliability
- 🛡️ **Fewer failure points**: Less complex logic
- 🔄 **Better recovery**: Simple error handling
- 📊 **Predictable behavior**: Consistent ON/OFF pattern

### User Experience
- 🎯 **Immediate response**: Relay triggers instantly
- 🔍 **Clear feedback**: Simple success/failure messages
- 🚀 **Consistent behavior**: Same pattern for all triggers

## 🔧 **Migration Notes**

### Removed Features
- ❌ **Hold time management**: No more duration-based triggers
- ❌ **Active trigger tracking**: No need to track running timers
- ❌ **Complex turn-off logic**: No `_turnOffRelay` method
- ❌ **Connection pre-checking**: No validation before each command

### Maintained Features
- ✅ **Scenario-based triggering**: Still use scenarios
- ✅ **Multiple relay support**: Can trigger multiple relays
- ✅ **Emergency stop**: Simplified but still available
- ✅ **Error logging**: Debug information maintained
- ✅ **Delay support**: Initial delay before trigger still works

Hệ thống relay trigger giờ đây **đơn giản, nhanh chóng và đáng tin cậy** với approach "immediate ON/OFF" thay vì complex hold time management.

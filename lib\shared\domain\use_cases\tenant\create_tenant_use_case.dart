import 'package:dartz/dartz.dart';
import '../../entities/tenant/tenant.dart';
import '../../repositories/tenant_repository.dart';
import '../../../core/errors/failures.dart';

class CreateTenantUseCase {
  final TenantRepository repository;

  CreateTenantUseCase(this.repository);

  Future<Either<Failure, Tenant>> call(CreateTenantParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to create tenant
    return await repository.createTenant(
      name: params.name,
      address: params.address,
      unitId: params.unitId,
      mappings: params.mappings,
    );
  }

  ValidationFailure? _validateParams(CreateTenantParams params) {
    final errors = <String, List<String>>{};

    // Validate name (required)
    if (params.name.trim().isEmpty) {
      errors['name'] = ['Tên tổ chức không được để trống'];
    }

    // Validate name length
    if (params.name.trim().length > 255) {
      errors['name'] = ['Tên tổ chức không được vượt quá 255 ký tự'];
    }

    // Validate address length if provided
    if (params.address != null && params.address!.length > 500) {
      errors['address'] = ['Địa chỉ không được vượt quá 500 ký tự'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Dữ liệu không hợp lệ',
        code: 'VALIDATION_ERROR',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class CreateTenantParams {
  final String name;
  final String? address;
  final String? unitId;
  final List<String>? mappings;

  CreateTenantParams({
    required this.name,
    this.address,
    this.unitId,
    this.mappings,
  });
}

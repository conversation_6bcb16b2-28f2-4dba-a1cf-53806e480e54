import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Notification icon component for TabsBar
class NotificationIcon extends StatelessWidget {
  final bool isActive;
  final double size;

  const NotificationIcon({
    super.key,
    required this.isActive,
    this.size = 18.0,
  });

  @override
  Widget build(BuildContext context) {
    final color = isActive ? '#008FD3' : '#9CA5B3';
    
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M2.77244 10.7955C2.61295 11.8103 3.326 12.5146 4.19904 12.8657C7.54611 14.2114 12.2039 14.2114 15.551 12.8657C16.424 12.5146 17.1371 11.8103 16.9776 10.7955C16.8795 10.1719 16.3949 9.6526 16.0358 9.1455C15.5655 8.47314 15.5187 7.73979 15.5187 6.95956C15.5187 3.94433 12.9919 1.5 9.875 1.5C6.7581 1.5 4.23134 3.94433 4.23134 6.95956C4.23128 7.73979 4.18454 8.47314 3.71421 9.1455C3.35513 9.6526 2.87046 10.1719 2.77244 10.7955Z"
            stroke="$color"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M7.625 15.75C8.2221 16.2164 9.0106 16.5 9.875 16.5C10.7394 16.5 11.5279 16.2164 12.125 15.75"
            stroke="$color"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>''',
        width: size,
        height: size,
      ),
    );
  }
}

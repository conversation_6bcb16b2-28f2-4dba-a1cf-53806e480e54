import 'dart:async';
import 'package:flutter/material.dart';
import 'package:usb_serial/usb_serial.dart';
import 'package:relay_controller/relay_controller.dart';

/// Enhanced USB-TTL Testing Widget with device selection and preset commands
class EnhancedUsbTtlTestingWidget extends StatefulWidget {
  final Function(String)? onLog;
  
  const EnhancedUsbTtlTestingWidget({
    super.key,
    this.onLog,
  });

  @override
  State<EnhancedUsbTtlTestingWidget> createState() => _EnhancedUsbTtlTestingWidgetState();
}

class _EnhancedUsbTtlTestingWidgetState extends State<EnhancedUsbTtlTestingWidget> {
  final TextEditingController _customCommandController = TextEditingController();
  
  List<UsbDevice> _availableDevices = [];
  UsbDevice? _selectedDevice;
  UsbTtlRelayController? _controller;
  bool _isLoading = false;
  bool _isConnected = false;
  String _connectionStatus = 'Disconnected';
  
  // Relay selection
  String _selectedRelay = 'R0';
  final List<String> _relayOptions = ['R0', 'R1', 'R2', 'R3', 'ALL'];

  // Command selection
  String _selectedCommand = '1';
  final Map<String, String> _commandOptions = {
    '1': 'Bật (ON)',
    '0': 'Tắt (OFF)',
    'TOGGLE': 'Đảo trạng thái',
    '500': 'Bật 500ms rồi tắt',
    '1000': 'Bật 1000ms rồi tắt',
    '2000': 'Bật 2000ms rồi tắt',
  };

  @override
  void initState() {
    super.initState();
    _scanForDevices();
  }

  @override
  void dispose() {
    _customCommandController.dispose();
    _disconnectDevice();
    super.dispose();
  }

  void _log(String message) {
    widget.onLog?.call(message);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _scanForDevices() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Scanning for devices...';
    });

    try {
      final devices = await UsbTtlRelayController.getAvailableDevices();
      
      // Filter for USB-TTL devices
      final usbTtlDevices = devices.where((device) {
        final deviceName = device.deviceName.toLowerCase();
        final productName = device.productName?.toLowerCase() ?? '';
        final manufacturerName = device.manufacturerName?.toLowerCase() ?? '';
        final vid = device.vid;
        final pid = device.pid;
        
        // Check by vendor/product ID (most reliable)
        if (vid != null && pid != null) {
          final knownUsbTtlIds = {
            0x067B: [0x2303], // Prolific PL2303
            0x0403: [0x6001, 0x6015, 0x6010, 0x6011], // FTDI
            0x1A86: [0x7523, 0x5523], // CH340/CH341
            0x10C4: [0xEA60, 0xEA70], // CP210x
            0x2341: [0x0043, 0x0001, 0x0010], // Arduino
          };
          
          if (knownUsbTtlIds.containsKey(vid)) {
            final productIds = knownUsbTtlIds[vid]!;
            if (productIds.contains(pid)) {
              return true;
            }
          }
        }
        
        // Fallback to name-based detection
        return deviceName.contains('pl2303') ||
               deviceName.contains('ch340') ||
               deviceName.contains('ch341') ||
               deviceName.contains('ftdi') ||
               deviceName.contains('cp210') ||
               productName.contains('usb-serial') ||
               productName.contains('uart') ||
               productName.contains('ttl') ||
               productName.contains('serial') ||
               manufacturerName.contains('prolific') ||
               manufacturerName.contains('ftdi') ||
               manufacturerName.contains('qinheng') ||
               manufacturerName.contains('wch');
      }).toList();

      setState(() {
        _availableDevices = usbTtlDevices;
        _selectedDevice = usbTtlDevices.isNotEmpty ? usbTtlDevices.first : null;
        _connectionStatus = usbTtlDevices.isEmpty 
            ? 'No USB-TTL devices found' 
            : 'Found ${usbTtlDevices.length} USB-TTL device(s)';
      });

      _log('Found ${usbTtlDevices.length} USB-TTL device(s)');

    } catch (e) {
      setState(() {
        _connectionStatus = 'Scan failed: $e';
      });
      _log('Device scan failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _connectToDevice() async {
    if (_selectedDevice == null) {
      _log('No device selected');
      return;
    }

    setState(() {
      _isLoading = true;
      _connectionStatus = 'Connecting...';
    });

    try {
      _controller = UsbTtlRelayController(
        deviceId: 'usb-ttl-test',
        deviceName: 'USB-TTL Test Device',
        relayCount: 4,
        baudRate: 115200,
        deviceProfile: DeviceProfile.esp32(),
      );

      await _controller!.connect(_selectedDevice!);

      setState(() {
        _isConnected = true;
        _connectionStatus = 'Connected to ${_selectedDevice!.deviceName}';
      });

      _log('✅ Connected to ${_selectedDevice!.deviceName}');

    } catch (e) {
      setState(() {
        _connectionStatus = 'Connection failed: $e';
      });
      _log('❌ Connection failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _disconnectDevice() async {
    if (_controller != null) {
      try {
        await _controller!.dispose();
        setState(() {
          _isConnected = false;
          _connectionStatus = 'Disconnected';
        });
        _log('Disconnected from device');
      } catch (e) {
        _log('Disconnect error: $e');
      } finally {
        _controller = null;
      }
    }
  }

  Future<void> _sendCustomCommand() async {
    final command = _customCommandController.text.trim();
    if (command.isEmpty) {
      _log('❌ No command entered');
      return;
    }

    await _sendCommand(command);
  }

  Future<void> _sendSelectedPresetCommand() async {
    final command = '$_selectedRelay:$_selectedCommand';
    await _sendCommand(command);
  }

  Future<void> _sendQuickCommand(String command) async {
    await _sendCommand(command);
  }

  Future<void> _sendCommand(String command) async {
    if (!_isConnected || _controller == null) {
      _log('❌ Device not connected');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      _log('📤 Sending command: $command');
      
      // Send raw command
      await _controller!.sendRawCommand(command);
      
      _log('✅ Command sent successfully: $command');

    } catch (e) {
      _log('❌ Command failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print('EnhancedUsbTtlTestingWidget build() called');
    print('_availableDevices.length: ${_availableDevices.length}');
    print('_connectionStatus: $_connectionStatus');
    print('_isLoading: $_isLoading');
    print('_isConnected: $_isConnected');

    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              // Debug Info
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('DEBUG INFO:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue.shade800)),
                    Text('Devices: ${_availableDevices.length}', style: TextStyle(color: Colors.blue.shade700)),
                    Text('Status: $_connectionStatus', style: TextStyle(color: Colors.blue.shade700)),
                    Text('Loading: $_isLoading', style: TextStyle(color: Colors.blue.shade700)),
                    Text('Connected: $_isConnected', style: TextStyle(color: Colors.blue.shade700)),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Header
              Row(
                children: [
                  Icon(
                    Icons.usb,
                    color: _isConnected ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Enhanced USB-TTL Testing',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_isLoading)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Connection Status
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isConnected ? Colors.green.shade50 : Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isConnected ? Colors.green : Colors.orange,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isConnected ? Icons.check_circle : Icons.warning,
                      color: _isConnected ? Colors.green : Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _connectionStatus,
                        style: TextStyle(
                          color: _isConnected ? Colors.green.shade800 : Colors.orange.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              Text(
                        "Demacia world",
                        style: TextStyle(
                          color: _isConnected ? Colors.green.shade800 : Colors.orange.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),

              // Device Selection
              _buildDeviceSelection(),

              const SizedBox(height: 16),

              // Custom Command Input
              _buildCustomCommandSection(),

              const SizedBox(height: 16),

              // Preset Commands
              _buildPresetCommandsSection(),
            ],
          ),
        ),
      );
  }

  Widget _buildDeviceSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'USB Devices (${_availableDevices.length})',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: _isLoading ? null : _scanForDevices,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('Scan'),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        if (_availableDevices.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text('No USB-TTL devices found'),
            ),
          )
        else
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                DropdownButton<UsbDevice>(
                  value: _selectedDevice,
                  isExpanded: true,
                  items: _availableDevices.map((device) {
                    final vidPid = (device.vid != null && device.pid != null)
                      ? ' [${device.vid!.toRadixString(16).toUpperCase()}:${device.pid!.toRadixString(16).toUpperCase()}]'
                      : '';
                    return DropdownMenuItem<UsbDevice>(
                      value: device,
                      child: Text('${device.deviceName}$vidPid'),
                    );
                  }).toList(),
                  onChanged: _isLoading ? null : (device) {
                    setState(() {
                      _selectedDevice = device;
                    });
                  },
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: (_isLoading || _selectedDevice == null || _isConnected) 
                            ? null 
                            : _connectToDevice,
                        icon: const Icon(Icons.link, size: 16),
                        label: const Text('Connect'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: (_isLoading || !_isConnected) 
                            ? null 
                            : _disconnectDevice,
                        icon: const Icon(Icons.link_off, size: 16),
                        label: const Text('Disconnect'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCustomCommandSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Command',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customCommandController,
                decoration: const InputDecoration(
                  labelText: 'Enter command',
                  hintText: 'R0:1',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                enabled: !_isLoading,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: (_isLoading || !_isConnected) ? null : _sendCustomCommand,
              icon: const Icon(Icons.send, size: 16),
              label: const Text('Send'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPresetCommandsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preset Commands',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),

        // Relay Selection
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chọn Relay:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<String>(
                    value: _selectedRelay,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: _relayOptions.map((relay) {
                      return DropdownMenuItem<String>(
                        value: relay,
                        child: Text(relay),
                      );
                    }).toList(),
                    onChanged: _isLoading ? null : (value) {
                      setState(() {
                        _selectedRelay = value!;
                      });
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Command Selection
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chọn Command:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<String>(
                    value: _selectedCommand,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: _commandOptions.entries.map((entry) {
                      return DropdownMenuItem<String>(
                        value: entry.key,
                        child: Text(entry.value),
                      );
                    }).toList(),
                    onChanged: _isLoading ? null : (value) {
                      setState(() {
                        _selectedCommand = value!;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Send Preset Command Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: (_isLoading || !_isConnected) ? null : _sendSelectedPresetCommand,
            icon: Icon(
              _getCommandIcon(_selectedCommand),
              size: 18,
            ),
            label: Text('Gửi $_selectedRelay:$_selectedCommand'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getCommandColor('$_selectedRelay:$_selectedCommand'),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Quick Action Buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: (_isLoading || !_isConnected) ? null : () => _sendQuickCommand('ALL:1'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('ALL ON'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton(
                onPressed: (_isLoading || !_isConnected) ? null : () => _sendQuickCommand('ALL:0'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('ALL OFF'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getCommandColor(String command) {
    if (command.contains(':1') || command == 'ALL:1') {
      return Colors.green;
    } else if (command.contains(':0') || command == 'ALL:0') {
      return Colors.red;
    } else if (command.contains('TOGGLE')) {
      return Colors.blue;
    } else if (command.contains(':')) {
      return Colors.purple; // Timed commands
    }
    return Colors.grey;
  }

  IconData _getCommandIcon(String command) {
    switch (command) {
      case '1':
        return Icons.power_settings_new;
      case '0':
        return Icons.power_off;
      case 'TOGGLE':
        return Icons.swap_horiz;
      case '500':
      case '1000':
      case '2000':
        return Icons.timer;
      default:
        return Icons.send;
    }
  }
}

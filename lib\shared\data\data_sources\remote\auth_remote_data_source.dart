import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../../core/network/api_client.dart';
import '../../../core/constants/api_constants.dart';
import '../../../services/api_endpoints.dart';
import '../../../services/http_client_service.dart';
import '../../../services/platform_http_service.dart';
import '../../../services/cookie_service.dart';
import '../../../services/auth_state_service.dart';
import '../../../services/jwt_decoder_service.dart';
import '../../../services/selected_tenant_service.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/auth/auth_result_model.dart';
import '../../models/user/user_model.dart';
import '../../../domain/entities/tenant/tenant.dart';

abstract class AuthRemoteDataSource {
  Future<AuthResultModel> login({
    required String userName,
    required String password,
  });

  Future<void> logout();

  Future<void> logoutAll();

  Future<AuthResultModel> refreshToken(String refreshToken);

  Future<UserModel> getCurrentUser();

  Future<bool> verifyToken();

  Future<UserModel> updateProfile({
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
  });

  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  });

  Future<List<Map<String, dynamic>>> getSessions();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;
  final HttpClientService httpClientService;
  final PlatformHttpService platformHttpService;

  AuthRemoteDataSourceImpl({
    required this.apiClient,
    required this.httpClientService,
    required this.platformHttpService,
  });

  @override
  Future<AuthResultModel> login({
    required String userName,
    required String password,
  }) async {
    try {
      // Use platform-aware HTTP service
      print('\n🔧 ===== USING PLATFORM HTTP SERVICE =====');
      print('📱 Platform: ${platformHttpService.getPlatformInfo()}');
      print('=========================================\n');

      final response = await platformHttpService.post(
        ApiEndpoints.login,
        body: {
          'username': userName,
          'password': password,
        },
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final responseData = response['data'] as Map<String, dynamic>;
        print('🔍 Login response data keys: ${responseData.keys}');
        print('🔍 Has refresh_token: ${responseData.containsKey('refresh_token')}');
        if (responseData.containsKey('refresh_token')) {
          print('🔍 Refresh token value: ${responseData['refresh_token']}');
        }

        // Get refresh token based on configuration
        String? refreshToken;
        if (ApiConstants.useRefreshTokenFromCookie) {
          // Get refresh token from cookie
          final baseUrl = ApiEndpoints.devBaseUrl;
          final uri = Uri.parse(baseUrl);
          final domain = uri.host;

          refreshToken = await CookieService().getRefreshTokenCookie(domain);
          print('🍪 Retrieved refresh token from cookie: ${refreshToken != null ? '${refreshToken.length} chars' : 'null'}');

          if (refreshToken == null) {
            print('⚠️ No refresh token found in cookie, falling back to response body');
            refreshToken = responseData['refresh_token'] as String?;
          }
        } else {
          // Get refresh token from response body (legacy mode)
          refreshToken = responseData['refresh_token'] as String?;
          print('📦 Retrieved refresh token from response body: ${refreshToken != null ? '${refreshToken.length} chars' : 'null'}');
        }

        // Create AuthResult with the refresh token from the appropriate source
        final authResult = AuthResultModel.fromJson({
          ...responseData,
          'refresh_token': refreshToken,
        });

        // Store authentication state
        await _storeAuthenticationState(authResult);

        // Extract and process JWT token data for future purposes
        final accessToken = authResult.accessToken;
        await _processJwtTokenData(accessToken);

        // Set refresh token cookie if we got it from response body (for backward compatibility)
        if (!ApiConstants.useRefreshTokenFromCookie && refreshToken != null) {
          print('🍪 Setting refresh token cookie from response body: $refreshToken');
          await _setRefreshTokenCookie(refreshToken);
        }

        return authResult;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Login failed';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Login failed: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      final response = await apiClient.post(ApiEndpoints.logout);

      // Handle the new API response format
      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Logout failed';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }

      // Clear all authentication data
      await _clearAllAuthenticationData();
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Logout failed: $e');
    }
  }

  @override
  Future<AuthResultModel> refreshToken(String refreshToken) async {
    try {
      // The new API uses cookies for refresh tokens
      // Set the refresh token in Cookie header
      final cookieHeader = '${ApiConstants.refreshTokenCookieName}=$refreshToken';

      if (kDebugMode) {
        print('🔄 Refreshing token with cookie: ${ApiConstants.refreshTokenCookieName}=${refreshToken.length} chars');
      }

      final response = await apiClient.post(
        ApiEndpoints.refreshToken,
        headers: {
          'Cookie': cookieHeader,
        },
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final responseData = response['data'] as Map<String, dynamic>;

        if (kDebugMode) {
          print('🔍 Refresh token response data keys: ${responseData.keys}');
          print('🔍 Has access_token: ${responseData.containsKey('access_token')}');
          print('🔍 Has refresh_token in body: ${responseData.containsKey('refresh_token')}');
        }

        // Get new access token from response body
        final newAccessToken = responseData['access_token'] as String?;

        if (newAccessToken == null) {
          throw AuthException('Refresh token response missing access_token in body');
        }

        // Get new refresh token from cookie (preferred) or fallback to response body
        String? newRefreshToken;
        if (ApiConstants.useRefreshTokenFromCookie) {
          final baseUrl = ApiEndpoints.devBaseUrl;
          final uri = Uri.parse(baseUrl);
          final domain = uri.host;

          newRefreshToken = await CookieService().getRefreshTokenCookie(domain);
          if (newRefreshToken != null) {
            if (kDebugMode) {
              print('🍪 New refresh token retrieved from cookie: ${newRefreshToken.length} chars');
            }
          } else {
            if (kDebugMode) {
              print('⚠️ No refresh token found in cookie, falling back to response body');
            }
            newRefreshToken = responseData['refresh_token'] as String?;
          }
        } else {
          // Legacy mode: get refresh token from response body
          newRefreshToken = responseData['refresh_token'] as String?;
          if (kDebugMode) {
            print('� Using refresh token from response body: ${newRefreshToken?.length ?? 0} chars');
          }
        }

        // Create AuthResult with tokens from appropriate sources
        // The AuthResultModel.fromJson will handle fallback logic for missing user/expiry data
        final authResultData = <String, dynamic>{
          'access_token': newAccessToken,
          'refresh_token': newRefreshToken,
          'token_type': responseData['token_type'] ?? 'Bearer',
          'expires_in': responseData['expires_in'],
          'expires_at': responseData['expires_at'],
          'user': responseData['user'], // Let AuthResultModel handle fallback
          'scopes': responseData['scopes'],
          'tenant_switched': responseData['tenant_switched'],
        };

        // Remove null values except required fields
        authResultData.removeWhere((key, value) =>
          value == null && !['access_token', 'refresh_token', 'token_type'].contains(key));

        if (kDebugMode) {
          print('🔍 Final auth result data keys: ${authResultData.keys}');
          print('🔍 Access token length: ${newAccessToken.length}');
          print('🔍 Refresh token length: ${newRefreshToken?.length ?? 0}');
          print('🔍 Token type: ${authResultData['token_type']}');
          print('🔍 Expires in: ${authResultData['expires_in']} seconds');
          print('🔍 User info in response: ${responseData['user'] != null ? 'Available' : 'Not available (will use JWT fallback)'}');
        }

        final authResult = AuthResultModel.fromJson(authResultData);

        if (kDebugMode) {
          print('✅ AuthResult created successfully:');
          print('   - User: ${authResult.user?.username ?? 'null'}');
          print('   - ExpiresAt: ${authResult.expiresAt}');
          print('   - Scopes: ${authResult.scopes?.length ?? 0} items');
        }

        // Store the updated authentication state
        await _storeAuthenticationState(authResult);

        return authResult;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Token refresh failed';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Token refresh failed: $e');
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final response = await apiClient.get(ApiEndpoints.me);

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get user profile';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Failed to get user profile: $e');
    }
  }

  @override
  Future<void> logoutAll() async {
    try {
      final response = await apiClient.post(ApiEndpoints.logout); // Using logout for now

      // Handle the new API response format
      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Logout all failed';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }

      // Clear all authentication data
      await _clearAllAuthenticationData(clearAllCookies: true);
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Logout all failed: $e');
    }
  }

  @override
  Future<bool> verifyToken() async {
    try {
      final response = await apiClient.get(ApiEndpoints.me); // Using me endpoint for token verification

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        return response['data']['valid'] == true;
      } else {
        return false;
      }
    } catch (e) {
      // If there's any error, consider token invalid
      // This is expected behavior for token verification
      return false;
    }
  }

  @override
  Future<UserModel> updateProfile({
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
  }) async {
    final body = <String, dynamic>{};

    if (name != null) body['name'] = name;
    if (email != null) body['email'] = email;
    if (phone != null) body['phone'] = phone;
    if (dob != null) body['dob'] = dob.toIso8601String();
    if (gender != null) body['gender'] = gender;

    try {
      final response = await apiClient.put(
        ApiEndpoints.updateProfile,
        body: body,
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update profile';
        final errorCode = response['error']?['code'];
        throw ValidationException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ValidationException) rethrow;
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Failed to update profile: $e');
    }
  }

  @override
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await apiClient.post(
        ApiEndpoints.changePassword,
        body: {
          'current_password': currentPassword,
          'new_password': newPassword,
          'confirm_password': confirmPassword,
        },
      );

      // Handle the new API response format
      if (response['success'] == true) {
        return true;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to change password';
        final errorCode = response['error']?['code'];
        final fieldErrors = response['error']?['field_errors'] as Map<String, dynamic>?;

        if (fieldErrors != null) {
          // Convert field errors to proper format
          final Map<String, List<String>> convertedErrors = {};
          fieldErrors.forEach((key, value) {
            if (value is List) {
              convertedErrors[key] = value.cast<String>();
            } else if (value is String) {
              convertedErrors[key] = [value];
            }
          });

          throw ValidationException(
            errorMessage,
            code: errorCode,
            fieldErrors: convertedErrors,
          );
        } else {
          throw AuthException(errorMessage, code: errorCode);
        }
      }
    } catch (e) {
      if (e is ValidationException) rethrow;
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Failed to change password: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getSessions() async {
    try {
      final response = await apiClient.get(ApiEndpoints.me); // Using me endpoint for sessions

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final sessions = response['data']['sessions'] as List<dynamic>? ?? [];
        return sessions.cast<Map<String, dynamic>>();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get sessions';
        final errorCode = response['error']?['code'];
        throw AuthException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      throw ServerException('Failed to get sessions: $e');
    }
  }

  /// Store authentication state using AuthStateService
  Future<void> _storeAuthenticationState(AuthResultModel authResult) async {
    try {
      await AuthStateService().storeAuthResult(authResult);
      print('🔐 Authentication state stored successfully');
    } catch (e) {
      print('🔴 Failed to store authentication state: $e');
      // Don't throw error - auth state storage is not critical for login
    }
  }



  /// Set refresh token cookie
  Future<void> _setRefreshTokenCookie(String refreshToken) async {
    try {
      // Extract domain from base URL
      final baseUrl = ApiEndpoints.devBaseUrl;
      final uri = Uri.parse(baseUrl);
      final domain = uri.host;

      // Set refresh token cookie
      await CookieService().setRefreshTokenCookie(refreshToken, domain);
      print('🍪 Refresh token cookie set successfully (memory-only)');
    } catch (e) {
      print('🔴 Failed to set refresh token cookie: $e');
      // Don't throw error - cookie setting is not critical for login
    }
  }

  /// Process JWT token data for various purposes
  Future<void> _processJwtTokenData(String accessToken) async {
    try {
      final jwtDecoder = GetIt.instance<JwtDecoderService>();
      final jwtPayload = jwtDecoder.decodeToken(accessToken);

      if (jwtPayload != null) {
        // Extract tenant information and set in SelectedTenantService
        await _setTenantFromJwt(jwtPayload);

        // Extract user information for future use
        await _setUserInfoFromJwt(jwtPayload);

        // Extract permissions for future use
        await _setPermissionsFromJwt(jwtPayload);

        // Log successful extraction
        if (kDebugMode) {
          print('✅ JWT token data processed successfully');
          print('👤 User: ${jwtPayload.username}');
          print('🏢 Tenant: ${jwtPayload.currentTenant?.name ?? 'None'}');
          print('🔑 Permissions: ${jwtPayload.entityPermissions.length} items');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Failed to decode JWT token');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error processing JWT token data: $e');
      }
      // Don't throw error - JWT processing is not critical for login
    }
  }

  /// Set tenant information from JWT payload
  Future<void> _setTenantFromJwt(dynamic jwtPayload) async {
    try {
      if (jwtPayload.currentTenant?.name != null) {
        // Create Tenant object from JWT data
        final tenant = Tenant(
          id: jwtPayload.currentTenant!.id ?? jwtPayload.currentTenantId ?? '',
          name: jwtPayload.currentTenant!.name!,
          createdBy: jwtPayload.sub ?? 'system', // Use JWT sub as creator
          createdAt: DateTime.now(), // Current time as creation time
        );

        final selectedTenantService = SelectedTenantService();
        selectedTenantService.setSelectedTenant(tenant);

        if (kDebugMode) {
          print('🏢 Tenant set from JWT: ${jwtPayload.currentTenant!.name}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error setting tenant from JWT: $e');
      }
    }
  }

  /// Set user information from JWT payload (for future use)
  Future<void> _setUserInfoFromJwt(dynamic jwtPayload) async {
    try {
      // TODO: Store user info in a service for future use
      // Example: UserInfoService().setCurrentUser(jwtPayload.username, jwtPayload.sub);

      if (kDebugMode) {
        print('👤 User info available: ${jwtPayload.username} (${jwtPayload.sub})');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error setting user info from JWT: $e');
      }
    }
  }

  /// Set permissions from JWT payload (for future use)
  Future<void> _setPermissionsFromJwt(dynamic jwtPayload) async {
    try {
      // TODO: Store permissions in a service for future use
      // Example: PermissionsService().setUserPermissions(jwtPayload.entityPermissions);

      if (kDebugMode) {
        print('🔑 Permissions available: ${jwtPayload.entityPermissions.length} items');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Error setting permissions from JWT: $e');
      }
    }
  }

  /// Clear all authentication data
  Future<void> _clearAllAuthenticationData({bool clearAllCookies = false}) async {
    try {
      // Clear authentication state
      await AuthStateService().clearAuthData();

      // Clear cookies
      if (clearAllCookies) {
        await CookieService().clearAllCookies();
      } else {
        final uri = Uri.parse(ApiEndpoints.devBaseUrl);
        await CookieService().clearCookiesForDomain(uri.host);
      }

      // Clear ApiClient authentication data
      await apiClient.clearAuthenticationData();

      if (kDebugMode) {
        print('🔐 All authentication data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Failed to clear authentication data: $e');
      }
      // Don't throw error - clearing is not critical
    }
  }
}

import 'package:flutter/material.dart';

/// Custom painter that draws a circular progress animation around a container border
/// 
/// This painter draws an arc that represents progress from 0.0 to 1.0
/// around the border of a container, creating a circular progress effect.
class CircularProgressBorderPainter extends CustomPainter {
  /// Progress value from 0.0 to 1.0
  final double progress;
  
  /// Color of the progress arc
  final Color progressColor;
  
  /// Width of the progress arc stroke
  final double strokeWidth;
  
  /// Border radius of the container
  final double borderRadius;
  
  /// Whether to use a gradient for the progress arc
  final bool useGradient;
  
  /// Start color for gradient (if useGradient is true)
  final Color? gradientStartColor;
  
  /// End color for gradient (if useGradient is true)
  final Color? gradientEndColor;

  CircularProgressBorderPainter({
    required this.progress,
    required this.progressColor,
    this.strokeWidth = 2.0,
    this.borderRadius = 15.0,
    this.useGradient = false,
    this.gradientStartColor,
    this.gradientEndColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0.0) return; // Don't draw anything if no progress

    // Calculate the rect for the rounded rectangle
    final rect = Rect.fromLTWH(
      strokeWidth / 2,
      strokeWidth / 2,
      size.width - strokeWidth,
      size.height - strokeWidth,
    );

    // Create a path for the rounded rectangle
    final path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          rect,
          Radius.circular(borderRadius),
        ),
      );

    // Calculate the total length of the path
    final pathMetrics = path.computeMetrics();
    final pathMetric = pathMetrics.first;
    final double totalLength = pathMetric.length;

    // Ensure progress completes the full circle
    final double clampedProgress = progress.clamp(0.0, 1.0);
    final double progressLength = totalLength * clampedProgress;

    final Path extractPath = Path();

    // Extract the path segment - ensure it completes at 1.0
    if (clampedProgress >= 1.0) {
      // Draw the complete path when progress is 1.0
      extractPath.addPath(path, Offset.zero);
    } else {
      // Extract partial path
      extractPath.addPath(
        pathMetric.extractPath(0, progressLength),
        Offset.zero,
      );
    }

    // Create the paint for the progress arc
    final Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Apply color transition from yellow to blue based on progress
    Color currentColor;
    if (useGradient && gradientStartColor != null && gradientEndColor != null) {
      // Interpolate between start and end colors based on progress
      currentColor = Color.lerp(gradientStartColor!, gradientEndColor!, clampedProgress) ?? progressColor;
    } else {
      // Default color transition from yellow to blue
      currentColor = Color.lerp(Colors.yellow.shade600, Colors.blue.shade400, clampedProgress) ?? progressColor;
    }

    paint.color = currentColor;

    // Draw the progress arc
    canvas.drawPath(extractPath, paint);
  }

  @override
  bool shouldRepaint(CircularProgressBorderPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.borderRadius != borderRadius ||
        oldDelegate.useGradient != useGradient ||
        oldDelegate.gradientStartColor != gradientStartColor ||
        oldDelegate.gradientEndColor != gradientEndColor;
  }
}

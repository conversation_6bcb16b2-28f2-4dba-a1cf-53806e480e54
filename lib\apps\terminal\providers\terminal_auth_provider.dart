import 'package:flutter/foundation.dart';

/// Authentication status enumeration
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Terminal auth provider for kiosk mode and admin authentication
/// 
/// Handles both kiosk mode (no authentication required) and admin mode
/// (requires admin credentials for configuration access)
class TerminalAuthProvider extends ChangeNotifier {
  AuthStatus _authStatus = AuthStatus.initial;
  String? _errorMessage;
  bool _isAdminAuthenticated = false;
  String? _adminToken;

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Current authentication status
  AuthStatus get authStatus => _authStatus;

  /// Whether admin is authenticated
  bool get isAdminAuthenticated => _isAdminAuthenticated;

  /// Current admin token
  String? get adminToken => _adminToken;

  /// Current error message
  String? get errorMessage => _errorMessage;

  /// Whether authentication is in progress
  bool get isLoading => _authStatus == AuthStatus.loading;

  /// Whether there's an authentication error
  bool get hasError => _authStatus == AuthStatus.error;

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  /// Initialize authentication state
  Future<void> initialize() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate checking stored admin authentication
      await Future.delayed(const Duration(milliseconds: 500));
      
      // For terminal app, default to unauthenticated (kiosk mode)
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAdminAuthenticated = false;
      _adminToken = null;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  /// Admin login with credentials
  Future<bool> adminLogin(String username, String password) async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate admin login API call
      await Future.delayed(const Duration(seconds: 1));
      
      // For demo purposes, accept specific admin credentials
      if (username == 'admin' && password == 'admin123') {
        _setAuthStatus(AuthStatus.authenticated);
        _isAdminAuthenticated = true;
        _adminToken = 'admin_token_${DateTime.now().millisecondsSinceEpoch}';
        _errorMessage = null;
        notifyListeners();
        return true;
      } else {
        _setError('Invalid admin credentials');
        return false;
      }
    } catch (error) {
      _setError(error.toString());
      return false;
    }
  }

  /// Admin logout
  Future<void> adminLogout() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate logout API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAdminAuthenticated = false;
      _adminToken = null;
      _errorMessage = null;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  /// Check admin authentication status
  Future<void> checkAdminAuthStatus() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate checking admin authentication status
      await Future.delayed(const Duration(milliseconds: 300));
      
      // For now, assume admin is not authenticated
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAdminAuthenticated = false;
      _adminToken = null;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  /// Validate admin token
  Future<bool> validateAdminToken(String token) async {
    try {
      // Simulate token validation
      await Future.delayed(const Duration(milliseconds: 200));
      
      // Simple token validation (in real app, this would be server-side)
      if (token.startsWith('admin_token_') && _adminToken == token) {
        return true;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /// Refresh admin token
  Future<bool> refreshAdminToken() async {
    if (!_isAdminAuthenticated || _adminToken == null) {
      return false;
    }
    
    try {
      // Simulate token refresh
      await Future.delayed(const Duration(milliseconds: 500));
      
      _adminToken = 'admin_token_${DateTime.now().millisecondsSinceEpoch}';
      notifyListeners();
      return true;
    } catch (error) {
      _setError(error.toString());
      return false;
    }
  }

  // ============================================================================
  // KIOSK MODE METHODS
  // ============================================================================

  /// Enter kiosk mode (no authentication required)
  void enterKioskMode() {
    _setAuthStatus(AuthStatus.unauthenticated);
    _isAdminAuthenticated = false;
    _adminToken = null;
    _errorMessage = null;
    notifyListeners();
  }

  /// Check if currently in kiosk mode
  bool get isKioskMode => !_isAdminAuthenticated;

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /// Set authentication status
  void _setAuthStatus(AuthStatus status) {
    _authStatus = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
  }

  /// Set error state
  void _setError(String message) {
    _authStatus = AuthStatus.error;
    _errorMessage = message;
    _isAdminAuthenticated = false;
    _adminToken = null;
  }

  /// Clear error
  void clearError() {
    if (_authStatus == AuthStatus.error) {
      _authStatus = AuthStatus.unauthenticated;
      _errorMessage = null;
      notifyListeners();
    }
  }
}

import 'dart:io';
import '../providers/face_detection_provider.dart';

/// Model để lưu trữ kết quả face capture
class FaceCaptureResult {
  final bool success;
  final Map<FaceDirection, String> capturedImages;
  final DateTime timestamp;

  FaceCaptureResult({
    required this.success,
    required this.capturedImages,
    required this.timestamp,
  });

  /// Tạo kết quả thành công với danh sách hình ảnh
  factory FaceCaptureResult.success(Map<FaceDirection, String?> images) {
    // Lọc ra những hình ảnh không null
    final validImages = <FaceDirection, String>{};
    images.forEach((direction, path) {
      if (path != null) {
        validImages[direction] = path;
      }
    });

    return FaceCaptureResult(
      success: true,
      capturedImages: validImages,
      timestamp: DateTime.now(),
    );
  }

  /// Tạo kết quả thất bại
  factory FaceCaptureResult.failure() {
    return FaceCaptureResult(
      success: false,
      capturedImages: {},
      timestamp: DateTime.now(),
    );
  }

  /// <PERSON><PERSON>m tra xem có hình ảnh nào được capture không
  bool get hasImages => capturedImages.isNotEmpty;

  /// Số lượng hình ảnh đã capture
  int get imageCount => capturedImages.length;

  /// Danh sách các hướng đã capture
  List<FaceDirection> get capturedDirections => capturedImages.keys.toList();

  /// Lấy đường dẫn hình ảnh theo hướng
  String? getImagePath(FaceDirection direction) {
    return capturedImages[direction];
  }

  /// Kiểm tra xem hướng cụ thể đã được capture chưa
  bool hasDirection(FaceDirection direction) {
    return capturedImages.containsKey(direction);
  }

  /// Lấy hình ảnh đại diện (ưu tiên front, nếu không có thì lấy hình đầu tiên)
  String? get representativeImagePath {
    if (capturedImages.containsKey(FaceDirection.front)) {
      return capturedImages[FaceDirection.front];
    }
    return capturedImages.values.isNotEmpty ? capturedImages.values.first : null;
  }

  /// Kiểm tra file hình ảnh có tồn tại không
  bool validateImages() {
    for (final path in capturedImages.values) {
      if (!File(path).existsSync()) {
        return false;
      }
    }
    return true;
  }

  /// Kiểm tra xem đã capture đủ tất cả hướng chưa (5 hướng: front, top, bottom, left, right)
  bool get isComplete {
    const requiredDirections = [
      FaceDirection.front,
      FaceDirection.top,
      FaceDirection.bottom,
      FaceDirection.left,
      FaceDirection.right,
    ];

    return requiredDirections.every((direction) => capturedImages.containsKey(direction));
  }

  @override
  String toString() {
    return 'FaceCaptureResult(success: $success, imageCount: $imageCount, directions: ${capturedDirections.map((d) => d.name).join(', ')})';
  }
}

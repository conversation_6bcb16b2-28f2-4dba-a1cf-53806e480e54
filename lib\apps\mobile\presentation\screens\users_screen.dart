import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/components/app_list_view.dart';
import '../../../../shared/components/app_list_controller.dart';
import '../../../../shared/components/users_filter_popup.dart';
import '../../../../shared/data/services/users_api_service.dart';
import '../../../../shared/widgets/authenticated_image.dart';

import 'users_screen/user_detail_screen.dart';
import 'users_screen/user_item_detail_screen.dart';

/// <PERSON><PERSON>n hình danh sách thành viên
class UsersScreen extends StatefulWidget {
  const UsersScreen({super.key});

  @override
  State<UsersScreen> createState() => _UsersScreenState();
}

// Global flag to track when user list needs refresh
class UserListRefreshNotifier {
  static bool needsRefresh = false;

  static void markForRefresh() {
    needsRefresh = true;
  }

  static bool checkAndClearRefresh() {
    final needs = needsRefresh;
    needsRefresh = false;
    return needs;
  }
}

class _UsersScreenState extends State<UsersScreen> with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  final AppListController<UserUIModel> _listController =
      AppListController<UserUIModel>(pageSize: 20);
  late final UsersApiService _usersService;

  String? _selectedUnit;
  UserStatus? _selectedStatus;
  bool _needsRefreshOnResume = false;

  @override
  void initState() {
    super.initState();
    _usersService = GetIt.instance<UsersApiService>();
    _loadInitialData();
    _setupSearchListener();
    WidgetsBinding.instance.addObserver(this);
  }



  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && _needsRefreshOnResume) {
      if (kDebugMode) {
        print('UsersScreen: App resumed, performing refresh');
      }
      _needsRefreshOnResume = false;
      _performRefresh();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Force clear loading state if we're returning to this screen
    if (kDebugMode) {
      print('UsersScreen: didChangeDependencies called, loading state: ${_listController.isLoading}');
    }

    // Check if we need to refresh due to user deletion
    if (UserListRefreshNotifier.checkAndClearRefresh()) {
      if (kDebugMode) {
        print('UsersScreen: Refresh flag detected, performing refresh');
      }
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _performRefresh();
        }
      });
    }

    // If loading state is stuck, force clear it
    if (_listController.isLoading) {
      if (kDebugMode) {
        print('UsersScreen: Force clearing stuck loading state');
      }
      _listController.clearLoadingState();
    }
  }

  @override
  void didUpdateWidget(UsersScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (kDebugMode) {
      print('UsersScreen: didUpdateWidget called - screen refreshed');
    }
  }

  void _loadInitialData() async {
    if (kDebugMode) {
      print('UsersScreen: Loading initial data...');
    }

    try {
      await _listController.loadData(
        (page, pageSize) => _usersService.getUsers(
          page: page,
          pageSize: pageSize,
          searchQuery: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
          unitFilter: _selectedUnit,
          statusFilter: _selectedStatus,
        ),
      );

      if (kDebugMode) {
        print('UsersScreen: Data loaded successfully, ${_listController.items.length} items');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UsersScreen: Error loading data: $e');
      }
    }
  }

  void _setupSearchListener() {
    _searchController.addListener(() {
      // Debounce search
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadInitialData();
        }
      });
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Sticky header
            _buildHeader(context),

            // Sticky search and filter
            Container(
              color: AppColors.background,
              child: _buildSearchAndFilter(),
            ),

            // Sticky user count
            Container(color: AppColors.background, child: _buildUserCount()),

            // Scrollable user list
            Expanded(child: _buildUsersList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Text(
            'Danh sách thành viên',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () async {
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserDetailScreen(),
                ),
              );

              if (result == true) {
                // Refresh user list
                _loadInitialData();
              }
            },
            child: Row(
              children: [
                Icon(Icons.add, size: 14, color: AppColors.primary),
                SizedBox(width: AppDimensions.spacing4),
                Text(
                  'Thêm mới',
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 36,
                  padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.search,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: AppDimensions.spacing8),
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          style: AppTextStyles.caption,
                          textAlignVertical: TextAlignVertical.center,
                          decoration: InputDecoration(
                            hintText: 'Theo tên hoặc email',
                            hintStyle: AppTextStyles.caption.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 8,
                            ),
                            isDense: true,
                            focusedBorder: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            errorBorder: InputBorder.none,
                            focusedErrorBorder: InputBorder.none,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacing8),
              GestureDetector(
                onTap: _showFilterPopup,
                child: Container(
                  height: 36,
                  width: 36,
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: Icon(
                    Icons.filter_list,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserCount() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.spacing12,
      ),
      child: Row(
        children: [
          Text(
            'Tổng:',
            style: AppTextStyles.caption.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(width: AppDimensions.spacing4),
          AnimatedBuilder(
            animation: _listController,
            builder: (context, child) {
              // Show total count from API if available, otherwise show loaded items count
              final totalCount = _usersService.totalCount > 0
                  ? _usersService.totalCount
                  : _listController.length;
              return Text(
                '$totalCount người',
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    return AnimatedBuilder(
      animation: _listController,
      builder: (context, child) {
        return AppListView<UserUIModel>(
          items: _listController.items,
          itemBuilder: (context, user, index) => _buildUserItem(user),
          onLoadMore: () async {
            await _listController.loadMore(
              (page, pageSize) => _usersService.getUsers(
                page: page,
                pageSize: pageSize,
                searchQuery: _searchController.text.trim().isEmpty
                    ? null
                    : _searchController.text.trim(),
                unitFilter: _selectedUnit,
                statusFilter: _selectedStatus,
              ),
            );
            return _listController.items;
          },
          onRefresh: () async {
            await _listController.refresh(
              (page, pageSize) => _usersService.getUsers(
                page: page,
                pageSize: pageSize,
                searchQuery: _searchController.text.trim().isEmpty
                    ? null
                    : _searchController.text.trim(),
                unitFilter: _selectedUnit,
                statusFilter: _selectedStatus,
              ),
            );
            return _listController.items;
          },
          padding: EdgeInsets.fromLTRB(
            AppDimensions.paddingM,
            0,
            AppDimensions.paddingM,
            AppDimensions.paddingM,
          ),
          separator: SizedBox(height: AppDimensions.spacing12),
          isLoading: _listController.isLoading,
          error: _listController.error,
          hasMoreData: _listController.hasMoreData,
          enableInfiniteScroll: true,
          enablePullToRefresh: true,
          loadMoreThreshold: 3,
        );
      },
    );
  }

  Widget _buildUserItem(UserUIModel user) {
    return GestureDetector(
      onTap: () => _navigateToUserDetail(user),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingS),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            SizedBox(width: AppDimensions.spacing8),
            _buildUserAvatar(user),
            SizedBox(width: AppDimensions.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        user.name,
                        style: AppTextStyles.caption.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF081F41),
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacing4),
                      Text(
                        '-',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF67718E),
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacing4),
                      Text(
                        user.id,
                        style: AppTextStyles.caption.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacing4),
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        size: 12,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: AppDimensions.spacing4),
                      Text(
                        user.unit,
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacing4),
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: user.status == UserStatus.active
                              ? const Color(0xFF40BF24)
                              : AppColors.textSecondary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacing4),
                      Text(
                        user.status == UserStatus.active
                            ? 'Đang hoạt động'
                            : 'Không hoạt động',
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(width: AppDimensions.spacing8),
            Icon(Icons.chevron_right, size: 16, color: AppColors.textPrimary),
          ],
        ),
      ),
    );
  }

  void _navigateToUserDetail(UserUIModel user) async {
    if (kDebugMode) {
      print('UsersScreen: Navigating to user detail for ${user.name}');
    }

    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => UserItemDetailScreen(user: user)),
    );

    if (kDebugMode) {
      print('UsersScreen: Returned from user detail with result: $result (type: ${result.runtimeType})');
    }

    if (result == true || result == 'refresh_needed') {
      if (kDebugMode) {
        print('UsersScreen: User action confirmed (result: $result), processing refresh');
      }

      // User was deleted, remove from list immediately as fallback
      final userIndex = _listController.findItemIndex((item) => item.realId == user.realId);
      if (userIndex >= 0) {
        if (kDebugMode) {
          print('UsersScreen: Removing deleted user from list at index $userIndex');
        }
        _listController.removeItem(userIndex);
      }

      // Continue with immediate refresh
      _performRefresh();
    } else if (result == null) {
      if (kDebugMode) {
        print('UsersScreen: Result is null, setting flag for refresh on resume');
      }

      // Set flag to refresh when app/screen becomes active again
      _needsRefreshOnResume = true;

      // Also try immediate delayed refresh as backup
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          if (kDebugMode) {
            print('UsersScreen: Performing delayed refresh due to null result');
          }
          _performRefresh();
        }
      });
    } else {
      if (kDebugMode) {
        print('UsersScreen: No action needed, result: $result');
      }
    }
  }

  Future<void> _performRefresh() async {
    // Then try to refresh the full list
    if (kDebugMode) {
      print('UsersScreen: Refreshing user list after user action');
    }

    try {
      if (kDebugMode) {
        print('UsersScreen: Starting refresh, loading state: ${_listController.isLoading}');
      }

      // Add timeout to prevent hanging
      await _listController.refresh(
        (page, pageSize) => _usersService.getUsers(
          page: page,
          pageSize: pageSize,
          searchQuery: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
          unitFilter: _selectedUnit,
          statusFilter: _selectedStatus,
        ),
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          if (kDebugMode) {
            print('UsersScreen: Refresh timeout, force clearing loading state');
          }
          // Force clear loading state on timeout
          _listController.clearLoadingState();
        },
      );

      if (kDebugMode) {
        print('UsersScreen: Refresh completed successfully, loading state: ${_listController.isLoading}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UsersScreen: Error during refresh: $e, loading state: ${_listController.isLoading}');
      }

      // Force clear loading state on error
      _listController.clearLoadingState();

      // Show error message but don't block navigation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không thể tải lại danh sách: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showFilterPopup() {
    showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      isScrollControlled: true,
      showDragHandle: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            UsersFilterPopup(
              selectedUnit: _selectedUnit,
              selectedStatus: _selectedStatus?.name,
              onApplyFilter: _handleApplyFilter,
              onClearFilter: _handleClearFilter,
            ),
          ],
        ),
      ),
    );
  }

  void _handleApplyFilter(String? unitId, String? status) {
    setState(() {
      _selectedUnit = unitId;
      _selectedStatus = status != null
          ? UserStatus.values.firstWhere(
              (e) => e.name == status,
              orElse: () => UserStatus.active,
            )
          : null;
    });
    _loadInitialData();
  }

  void _handleClearFilter() {
    setState(() {
      _selectedUnit = null;
      _selectedStatus = null;
    });
    _loadInitialData();
  }

  /// Build user avatar widget
  Widget _buildUserAvatar(UserUIModel user) {
    if (user.avatar.isNotEmpty) {
      return CircleAvatar(
        radius: 21,
        backgroundColor: AppColors.border,
        child: AuthenticatedImage(
          imageId: user.avatar,
          width: 42,
          height: 42,
          fit: BoxFit.cover,
          borderRadius: BorderRadius.circular(21), // Circular border
          placeholder: Icon(
            Icons.person,
            size: 24,
            color: AppColors.textSecondary,
          ),
        ),
      );
    } else {
      return CircleAvatar(
        radius: 21,
        backgroundColor: AppColors.border,
        child: Icon(
          Icons.person,
          size: 24,
          color: AppColors.textSecondary,
        ),
      );
    }
  }

}

import 'package:flutter/material.dart';
import '../providers/device_registration_provider.dart';
import '../services/system_info_service.dart';

void main() {
  runApp(TestTerminalApp());
}

class TestTerminalApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Terminal Test App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: TestTerminalScreen(),
    );
  }
}

class TestTerminalScreen extends StatefulWidget {
  @override
  _TestTerminalScreenState createState() => _TestTerminalScreenState();
}

class _TestTerminalScreenState extends State<TestTerminalScreen> {
  final DeviceRegistrationProvider _registrationProvider = DeviceRegistrationProvider();
  final SystemInfoService _systemInfoService = SystemInfoService();
  
  String _log = '';
  bool _isLoading = false;
  Map<String, dynamic>? _systemInfo;

  @override
  void initState() {
    super.initState();
    _addLog('Terminal Test App initialized');
    _initializeProvider();
  }

  void _addLog(String message) {
    setState(() {
      _log += '${DateTime.now().toIso8601String()}: $message\n';
    });
    print(message);
  }

  Future<void> _initializeProvider() async {
    _addLog('Initializing DeviceRegistrationProvider...');
    try {
      await _registrationProvider.initialize();
      _addLog('DeviceRegistrationProvider initialized successfully');
      _addLog('Status: ${_registrationProvider.status}');
      _addLog('Is Registered: ${_registrationProvider.isRegistered}');
      _addLog('Is Connected: ${_registrationProvider.isConnected}');
    } catch (e) {
      _addLog('Failed to initialize provider: $e');
    }
  }

  Future<void> _testSystemInfo() async {
    _addLog('Testing system info collection...');
    setState(() {
      _isLoading = true;
    });
    
    try {
      final systemInfo = await _systemInfoService.getSystemInfo();
      setState(() {
        _systemInfo = systemInfo;
      });
      _addLog('System info collected successfully');
      _addLog('Platform: ${systemInfo['platform']}');
      _addLog('Memory Total: ${systemInfo['memory_total']}');
      _addLog('CPU Usage: ${systemInfo['cpu_usage_percent']}%');
      _addLog('Device Name: ${systemInfo['device_name']}');
    } catch (e) {
      _addLog('Failed to collect system info: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testDeviceRegistration() async {
    _addLog('Testing device registration...');
    setState(() {
      _isLoading = true;
    });
    
    try {
      final success = await _registrationProvider.registerDevice(
        deviceId: 'test-terminal-flutter-001',
        deviceName: 'Flutter Test Terminal',
        deviceType: 'face_terminal',
        location: 'Test Lab',
        serverUrl: 'http://localhost:3000',
        capabilities: ['face_auth', 'relay_control', 'heartbeat'],
        metadata: {
          'test_mode': true,
          'flutter_version': '3.x',
        },
      );
      
      if (success) {
        _addLog('Device registration successful!');
        _addLog('Status: ${_registrationProvider.status}');
        _addLog('Is Registered: ${_registrationProvider.isRegistered}');
        _addLog('Is Connected: ${_registrationProvider.isConnected}');
        _addLog('Available Scopes: ${_registrationProvider.availableScopes}');
      } else {
        _addLog('Device registration failed!');
        _addLog('Error: ${_registrationProvider.errorMessage}');
      }
    } catch (e) {
      _addLog('Device registration error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testHeartbeat() async {
    if (!_registrationProvider.isConnected) {
      _addLog('Cannot send heartbeat - device not connected');
      return;
    }
    
    _addLog('Testing heartbeat...');
    setState(() {
      _isLoading = true;
    });
    
    try {
      await _registrationProvider.sendHeartbeat();
      _addLog('Heartbeat sent successfully!');
      _addLog('Last Heartbeat: ${_registrationProvider.lastHeartbeat}');
    } catch (e) {
      _addLog('Heartbeat failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testUnregister() async {
    _addLog('Testing device unregistration...');
    setState(() {
      _isLoading = true;
    });
    
    try {
      await _registrationProvider.unregisterDevice();
      _addLog('Device unregistered successfully!');
      _addLog('Status: ${_registrationProvider.status}');
      _addLog('Is Registered: ${_registrationProvider.isRegistered}');
      _addLog('Is Connected: ${_registrationProvider.isConnected}');
    } catch (e) {
      _addLog('Device unregistration error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearLog() {
    setState(() {
      _log = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Terminal Test App'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Status Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Device Status', style: Theme.of(context).textTheme.headlineSmall),
                    SizedBox(height: 8),
                    Text('Registration Status: ${_registrationProvider.status}'),
                    Text('Is Registered: ${_registrationProvider.isRegistered}'),
                    Text('Is Connected: ${_registrationProvider.isConnected}'),
                    Text('Error: ${_registrationProvider.errorMessage ?? 'None'}'),
                    Text('Last Heartbeat: ${_registrationProvider.lastHeartbeat ?? 'Never'}'),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Action Buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _testSystemInfo,
                  child: Text('Test System Info'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testDeviceRegistration,
                  child: Text('Register Device'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testHeartbeat,
                  child: Text('Send Heartbeat'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testUnregister,
                  child: Text('Unregister'),
                ),
                ElevatedButton(
                  onPressed: _clearLog,
                  child: Text('Clear Log'),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Log Section
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Log', style: Theme.of(context).textTheme.headlineSmall),
                      SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _log.isEmpty ? 'No logs yet...' : _log,
                            style: TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            if (_isLoading)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}

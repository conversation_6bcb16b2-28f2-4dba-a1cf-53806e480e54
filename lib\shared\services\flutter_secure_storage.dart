import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service class để quản lý việc lưu trữ dữ liệu b<PERSON><PERSON> mật
/// Sử dụng flutter_secure_storage để mã hóa và lưu trữ dữ liệu nhạy cảm
class SecureStorageService {
  static const SecureStorageService _instance = SecureStorageService._internal();

  factory SecureStorageService() => _instance;

  const SecureStorageService._internal();

  // Cấu hình cho FlutterSecureStorage
  static const AndroidOptions _androidOptions = AndroidOptions(
    encryptedSharedPreferences: true,
    sharedPreferencesName: 'face_terminal_secure_prefs',
    preferencesKeyPrefix: 'face_terminal_',
  );

  static const IOSOptions _iosOptions = IOSOptions(
    groupId: 'group.com.example.face_terminal',
    accountName: 'face_terminal_keychain',
    synchronizable: true,
    accessibility: KeychainAccessibility.first_unlock_this_device,
  );

  static const LinuxOptions _linuxOptions = LinuxOptions();

  static const WindowsOptions _windowsOptions = WindowsOptions();

  static const MacOsOptions _macOsOptions = MacOsOptions(
    groupId: 'group.com.example.face_terminal',
    accountName: 'face_terminal_keychain',
    synchronizable: true,
    accessibility: KeychainAccessibility.first_unlock_this_device,
  );

  static const WebOptions _webOptions = WebOptions(
    dbName: 'face_terminal_secure_storage',
    publicKey: 'face_terminal_public_key',
  );

  // Instance của FlutterSecureStorage với cấu hình tùy chỉnh
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: _androidOptions,
    iOptions: _iosOptions,
    lOptions: _linuxOptions,
    wOptions: _windowsOptions,
    mOptions: _macOsOptions,
    webOptions: _webOptions,
  );

  /// Lưu trữ một cặp key-value
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Giá trị cần lưu trữ
  ///
  /// Throws [Exception] nếu có lỗi xảy ra trong quá trình lưu trữ
  Future<void> write({
    required String key,
    required String value,
  }) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw Exception('Lỗi khi lưu trữ dữ liệu với key "$key": $e');
    }
  }

  /// Đọc giá trị từ storage theo key
  ///
  /// [key] - Khóa cần đọc
  ///
  /// Returns giá trị string hoặc null nếu không tìm thấy
  /// Throws [Exception] nếu có lỗi xảy ra trong quá trình đọc
  Future<String?> read({required String key}) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw Exception('Lỗi khi đọc dữ liệu với key "$key": $e');
    }
  }

  /// Xóa một item theo key
  ///
  /// [key] - Khóa cần xóa
  ///
  /// Throws [Exception] nếu có lỗi xảy ra trong quá trình xóa
  Future<void> delete({required String key}) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw Exception('Lỗi khi xóa dữ liệu với key "$key": $e');
    }
  }

  /// Xóa tất cả dữ liệu trong storage
  ///
  /// Throws [Exception] nếu có lỗi xảy ra trong quá trình xóa
  Future<void> deleteAll() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw Exception('Lỗi khi xóa tất cả dữ liệu: $e');
    }
  }

  /// Kiểm tra xem một key có tồn tại hay không
  ///
  /// [key] - Khóa cần kiểm tra
  ///
  /// Returns true nếu key tồn tại, false nếu không
  Future<bool> containsKey({required String key}) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw Exception('Lỗi khi kiểm tra key "$key": $e');
    }
  }

  /// Lấy tất cả các key có trong storage
  ///
  /// Returns `Map<String, String>` chứa tất cả key-value pairs
  Future<Map<String, String>> readAll() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      throw Exception('Lỗi khi đọc tất cả dữ liệu: $e');
    }
  }

  /// Lấy danh sách tất cả các key
  ///
  /// Returns `Set<String>` chứa tất cả các key
  Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _storage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      throw Exception('Lỗi khi lấy danh sách key: $e');
    }
  }

  // ========== HELPER METHODS FOR COMMON DATA TYPES ==========

  /// Lưu trữ một số nguyên
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Giá trị số nguyên cần lưu trữ
  Future<void> writeInt({
    required String key,
    required int value,
  }) async {
    await write(key: key, value: value.toString());
  }

  /// Đọc một số nguyên từ storage
  ///
  /// [key] - Khóa cần đọc
  /// [defaultValue] - Giá trị mặc định nếu không tìm thấy
  ///
  /// Returns giá trị int hoặc defaultValue nếu không tìm thấy
  Future<int> readInt({
    required String key,
    int defaultValue = 0,
  }) async {
    final value = await read(key: key);
    if (value == null) return defaultValue;
    return int.tryParse(value) ?? defaultValue;
  }

  /// Lưu trữ một số thực
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Giá trị số thực cần lưu trữ
  Future<void> writeDouble({
    required String key,
    required double value,
  }) async {
    await write(key: key, value: value.toString());
  }

  /// Đọc một số thực từ storage
  ///
  /// [key] - Khóa cần đọc
  /// [defaultValue] - Giá trị mặc định nếu không tìm thấy
  ///
  /// Returns giá trị double hoặc defaultValue nếu không tìm thấy
  Future<double> readDouble({
    required String key,
    double defaultValue = 0.0,
  }) async {
    final value = await read(key: key);
    if (value == null) return defaultValue;
    return double.tryParse(value) ?? defaultValue;
  }

  /// Lưu trữ một giá trị boolean
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Giá trị boolean cần lưu trữ
  Future<void> writeBool({
    required String key,
    required bool value,
  }) async {
    await write(key: key, value: value.toString());
  }

  /// Đọc một giá trị boolean từ storage
  ///
  /// [key] - Khóa cần đọc
  /// [defaultValue] - Giá trị mặc định nếu không tìm thấy
  ///
  /// Returns giá trị bool hoặc defaultValue nếu không tìm thấy
  Future<bool> readBool({
    required String key,
    bool defaultValue = false,
  }) async {
    final value = await read(key: key);
    if (value == null) return defaultValue;
    return value.toLowerCase() == 'true';
  }

  /// Lưu trữ một danh sách string
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Danh sách string cần lưu trữ
  Future<void> writeStringList({
    required String key,
    required List<String> value,
  }) async {
    await write(key: key, value: jsonEncode(value));
  }

  /// Đọc một danh sách string từ storage
  ///
  /// [key] - Khóa cần đọc
  ///
  /// Returns `List<String>` hoặc danh sách rỗng nếu không tìm thấy
  Future<List<String>> readStringList({required String key}) async {
    final value = await read(key: key);
    if (value == null) return [];

    try {
      final List<dynamic> decoded = jsonDecode(value);
      return decoded.cast<String>();
    } catch (e) {
      throw Exception('Lỗi khi decode danh sách string với key "$key": $e');
    }
  }

  /// Lưu trữ một Map dưới dạng JSON
  ///
  /// [key] - Khóa để lưu trữ
  /// [value] - Map cần lưu trữ
  Future<void> writeMap({
    required String key,
    required Map<String, dynamic> value,
  }) async {
    await write(key: key, value: jsonEncode(value));
  }

  /// Đọc một Map từ storage
  ///
  /// [key] - Khóa cần đọc
  ///
  /// Returns `Map<String, dynamic>` hoặc map rỗng nếu không tìm thấy
  Future<Map<String, dynamic>> readMap({required String key}) async {
    final value = await read(key: key);
    if (value == null) return {};

    try {
      return Map<String, dynamic>.from(jsonDecode(value));
    } catch (e) {
      throw Exception('Lỗi khi decode map với key "$key": $e');
    }
  }
}
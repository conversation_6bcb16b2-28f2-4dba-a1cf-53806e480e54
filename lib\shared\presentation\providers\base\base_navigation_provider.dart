import '../../../core/base/base_provider.dart';

/// Navigation state enum
enum NavigationState {
  idle,
  navigating,
  error,
}

/// Base navigation provider that can be extended by mobile and terminal apps
/// 
/// Provides common navigation functionality including:
/// - Navigation state management
/// - Route history tracking
/// - Deep link handling
/// - Navigation guards
abstract class BaseNavigationProvider extends BaseProvider {
  
  // ============================================================================
  // STATE VARIABLES
  // ============================================================================
  
  NavigationState _navigationState = NavigationState.idle;
  String _currentRoute = '/';
  List<String> _routeHistory = ['/'];
  Map<String, dynamic> _routeParams = {};
  String? _pendingDeepLink;

  // ============================================================================
  // GETTERS
  // ============================================================================
  
  /// Current navigation state
  NavigationState get navigationState => _navigationState;
  
  /// Current route path
  String get currentRoute => _currentRoute;
  
  /// Route history stack
  List<String> get routeHistory => List.unmodifiable(_routeHistory);
  
  /// Current route parameters
  Map<String, dynamic> get routeParams => Map.unmodifiable(_routeParams);
  
  /// Pending deep link to process
  String? get pendingDeepLink => _pendingDeepLink;
  
  /// Check if navigation is in progress
  bool get isNavigating => _navigationState == NavigationState.navigating;
  
  /// Check if can go back
  bool get canGoBack => _routeHistory.length > 1;

  // ============================================================================
  // ABSTRACT METHODS (TO BE IMPLEMENTED BY SUBCLASSES)
  // ============================================================================
  
  /// Get app-specific route configuration
  Map<String, String> getRouteMap();
  
  /// Handle app-specific navigation
  Future<bool> performNavigation(String route, {Map<String, dynamic>? params});
  
  /// Handle app-specific deep link
  Future<bool> handleDeepLink(String deepLink);
  
  /// Check if route requires authentication
  bool requiresAuth(String route);
  
  /// Handle navigation guard failure
  void onNavigationGuardFailed(String route, String reason);
  
  /// Handle navigation success
  void onNavigationSuccess(String route);
  
  /// Handle navigation error
  void onNavigationError(String route, String error);

  // ============================================================================
  // NAVIGATION METHODS
  // ============================================================================
  
  /// Navigate to a route
  Future<bool> navigateTo(
    String route, {
    Map<String, dynamic>? params,
    bool replace = false,
    bool clearHistory = false,
  }) async {
    // Check navigation guards
    if (!await _checkNavigationGuards(route)) {
      return false;
    }
    
    _setNavigationState(NavigationState.navigating);
    
    try {
      // Perform app-specific navigation
      final success = await performNavigation(route, params: params);
      
      if (success) {
        _updateNavigationState(route, params, replace, clearHistory);
        onNavigationSuccess(route);
        _setNavigationState(NavigationState.idle);
        return true;
      } else {
        _setNavigationState(NavigationState.error);
        onNavigationError(route, 'Navigation failed');
        return false;
      }
    } catch (error) {
      _setNavigationState(NavigationState.error);
      onNavigationError(route, error.toString());
      return false;
    }
  }
  
  /// Go back to previous route
  Future<bool> goBack() async {
    if (!canGoBack) return false;
    
    // Remove current route from history
    _routeHistory.removeLast();
    final previousRoute = _routeHistory.last;
    
    return await navigateTo(previousRoute, replace: true);
  }
  
  /// Navigate to home/dashboard
  Future<bool> goHome() async {
    final homeRoute = getHomeRoute();
    return await navigateTo(homeRoute, clearHistory: true);
  }
  
  /// Process deep link
  Future<bool> processDeepLink(String deepLink) async {
    _pendingDeepLink = deepLink;
    
    try {
      final success = await handleDeepLink(deepLink);
      
      if (success) {
        _pendingDeepLink = null;
        return true;
      } else {
        return false;
      }
    } catch (error) {
      onNavigationError(deepLink, 'Deep link processing failed: $error');
      return false;
    }
  }
  
  /// Clear pending deep link
  void clearPendingDeepLink() {
    _pendingDeepLink = null;
    notifyListeners();
  }

  // ============================================================================
  // ROUTE UTILITIES
  // ============================================================================
  
  /// Get home route for the app
  String getHomeRoute() {
    final routes = getRouteMap();
    return routes['home'] ?? '/';
  }
  
  /// Get login route for the app
  String getLoginRoute() {
    final routes = getRouteMap();
    return routes['login'] ?? '/login';
  }
  
  /// Check if current route is home
  bool isCurrentRouteHome() {
    return _currentRoute == getHomeRoute();
  }
  
  /// Check if current route is login
  bool isCurrentRouteLogin() {
    return _currentRoute == getLoginRoute();
  }
  
  /// Get route display name
  String getRouteDisplayName(String route) {
    // Subclasses can override this for custom route names
    return route.replaceAll('/', '').replaceAll('-', ' ').toUpperCase();
  }

  // ============================================================================
  // NAVIGATION GUARDS
  // ============================================================================
  
  /// Check navigation guards before navigation
  Future<bool> _checkNavigationGuards(String route) async {
    // Check authentication requirement
    if (requiresAuth(route)) {
      if (!await _checkAuthentication()) {
        onNavigationGuardFailed(route, 'Authentication required');
        return false;
      }
    }
    
    // Add more guard checks here as needed
    return true;
  }
  
  /// Check if user is authenticated
  Future<bool> _checkAuthentication() async {
    // Subclasses should implement authentication check
    return true;
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// Set navigation state
  void _setNavigationState(NavigationState state) {
    if (isDisposed) return;
    
    _navigationState = state;
    notifyListeners();
  }
  
  /// Update navigation state after successful navigation
  void _updateNavigationState(
    String route,
    Map<String, dynamic>? params,
    bool replace,
    bool clearHistory,
  ) {
    _currentRoute = route;
    _routeParams = params ?? {};
    
    if (clearHistory) {
      _routeHistory = [route];
    } else if (replace && _routeHistory.isNotEmpty) {
      _routeHistory[_routeHistory.length - 1] = route;
    } else {
      _routeHistory.add(route);
    }
    
    // Limit history size to prevent memory issues
    if (_routeHistory.length > 50) {
      _routeHistory = _routeHistory.sublist(_routeHistory.length - 50);
    }
    
    notifyListeners();
  }

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================
  
  /// Initialize navigation provider
  Future<void> initialize() async {
    // Subclasses can override for initialization logic
  }
  
  @override
  void dispose() {
    _routeHistory.clear();
    _routeParams.clear();
    _pendingDeepLink = null;
    super.dispose();
  }
  
  @override
  Future<void> retry() async {
    if (_navigationState == NavigationState.error) {
      _setNavigationState(NavigationState.idle);
    }
  }
  
  @override
  void reset() {
    super.reset();
    _navigationState = NavigationState.idle;
    _currentRoute = '/';
    _routeHistory = ['/'];
    _routeParams.clear();
    _pendingDeepLink = null;
    notifyListeners();
  }
}

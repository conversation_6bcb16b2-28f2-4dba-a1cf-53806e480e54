#!/usr/bin/env python3
"""
<PERSON>ript to download real face recognition models and replace mock models.
This script downloads official models from various sources and places them
in the correct locations for the face recognition package.
"""

import os
import sys
import requests
import hashlib
from pathlib import Path
from typing import Dict, Optional
import argparse

# Model configurations with download URLs and checksums
MODELS_CONFIG = {
    "ultraface_320.tflite": {
        "url": "https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB/raw/master/models/onnx/version-RFB/RFB-320.onnx",
        "description": "UltraFace RFB-320 face detection model",
        "size_mb": 1.1,
        "convert_from": "onnx",
        "input_shape": [1, 3, 240, 320],
        "output_names": ["boxes", "scores"]
    },
    "mobilefacenet.tflite": {
        "url": "https://github.com/deepinsight/insightface/releases/download/v0.7/buffalo_l.zip",
        "description": "MobileFaceNet from InsightFace buffalo_l model",
        "size_mb": 5.2,
        "convert_from": "onnx",
        "extract_file": "buffalo_l/2d106det.onnx",
        "input_shape": [1, 3, 192, 192],
        "output_names": ["output"]
    },
    "mediapipe_face.tflite": {
        "url": "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/030_BlazeFace/01_float32/face_detection_front_128x128_float32.tflite",
        "description": "MediaPipe BlazeFace front face detection model",
        "size_mb": 0.2,
        "convert_from": "tflite",
        "input_shape": [1, 128, 128, 3],
        "output_names": ["regressors", "classificators"]
    }
}

class ModelDownloader:
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
    def download_file(self, url: str, filepath: Path, expected_size_mb: Optional[float] = None) -> bool:
        """Download a file from URL with progress tracking."""
        try:
            print(f"Downloading {filepath.name} from {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\rProgress: {progress:.1f}%", end='', flush=True)
            
            print(f"\nDownloaded {filepath.name} ({downloaded_size / 1024 / 1024:.1f} MB)")
            
            # Verify file size if expected
            if expected_size_mb:
                actual_size_mb = downloaded_size / 1024 / 1024
                if abs(actual_size_mb - expected_size_mb) > expected_size_mb * 0.1:  # 10% tolerance
                    print(f"Warning: File size mismatch. Expected ~{expected_size_mb}MB, got {actual_size_mb:.1f}MB")
            
            return True
            
        except Exception as e:
            print(f"Error downloading {url}: {e}")
            return False
    
    def extract_zip(self, zip_path: Path, extract_file: str) -> Optional[Path]:
        """Extract specific file from zip archive."""
        try:
            import zipfile
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Extract specific file
                extracted_path = self.models_dir / Path(extract_file).name
                with zip_ref.open(extract_file) as source, open(extracted_path, 'wb') as target:
                    target.write(source.read())
                
                print(f"Extracted {extract_file} to {extracted_path}")
                return extracted_path
                
        except Exception as e:
            print(f"Error extracting {extract_file} from {zip_path}: {e}")
            return None
    
    def download_model(self, model_name: str, config: Dict) -> bool:
        """Download and prepare a single model."""
        print(f"\n{'='*60}")
        print(f"Downloading {model_name}")
        print(f"Description: {config['description']}")
        print(f"Expected size: {config['size_mb']} MB")
        print(f"{'='*60}")
        
        # Determine download path
        if config['url'].endswith('.zip'):
            download_path = self.models_dir / f"{model_name}.zip"
        else:
            download_path = self.models_dir / f"{model_name}.tmp"
        
        # Download file
        if not self.download_file(config['url'], download_path, config['size_mb']):
            return False
        
        # Handle zip extraction
        if config['url'].endswith('.zip') and 'extract_file' in config:
            extracted_path = self.extract_zip(download_path, config['extract_file'])
            if not extracted_path:
                return False
            
            # Clean up zip file
            download_path.unlink()
            download_path = extracted_path
        
        # Move to final location if needed
        final_path = self.models_dir / model_name
        if download_path != final_path:
            if final_path.exists():
                final_path.unlink()
            download_path.rename(final_path)
        
        print(f"✓ Successfully downloaded {model_name}")
        return True
    
    def download_all_models(self, force: bool = False) -> bool:
        """Download all configured models."""
        success_count = 0
        total_count = len(MODELS_CONFIG)
        
        print(f"Starting download of {total_count} models to {self.models_dir}")
        
        for model_name, config in MODELS_CONFIG.items():
            model_path = self.models_dir / model_name
            
            if model_path.exists() and not force:
                print(f"Skipping {model_name} (already exists, use --force to overwrite)")
                success_count += 1
                continue
            
            if self.download_model(model_name, config):
                success_count += 1
            else:
                print(f"✗ Failed to download {model_name}")
        
        print(f"\n{'='*60}")
        print(f"Download Summary: {success_count}/{total_count} models downloaded successfully")
        
        if success_count == total_count:
            print("✓ All models downloaded successfully!")
            return True
        else:
            print(f"✗ {total_count - success_count} models failed to download")
            return False

def main():
    parser = argparse.ArgumentParser(description="Download face recognition models")
    parser.add_argument(
        "--models-dir", 
        default="lib/packages/face_recognition/assets/models",
        help="Directory to save models (default: lib/packages/face_recognition/assets/models)"
    )
    parser.add_argument(
        "--force", 
        action="store_true",
        help="Force download even if files already exist"
    )
    parser.add_argument(
        "--model",
        choices=list(MODELS_CONFIG.keys()),
        help="Download specific model only"
    )
    
    args = parser.parse_args()
    
    # Resolve models directory relative to script location
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / args.models_dir
    
    downloader = ModelDownloader(str(models_dir))
    
    if args.model:
        # Download specific model
        config = MODELS_CONFIG[args.model]
        success = downloader.download_model(args.model, config)
        sys.exit(0 if success else 1)
    else:
        # Download all models
        success = downloader.download_all_models(args.force)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

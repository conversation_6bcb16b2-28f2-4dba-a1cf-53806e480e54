import 'package:flutter/material.dart';
import '../../core/config/flavor_config.dart';

/// Widget có thể hiển thị khác nhau tùy theo flavor
class FlavorAwareWidget extends StatelessWidget {
  final Widget? mobileWidget;
  final Widget? terminalWidget;
  final Widget? defaultWidget;

  const FlavorAwareWidget({
    super.key,
    this.mobileWidget,
    this.terminalWidget,
    this.defaultWidget,
  });

  @override
  Widget build(BuildContext context) {
    final flavor = FlavorConfig.instance.flavor;
    
    switch (flavor) {
      case Flavor.mobile:
        return mobileWidget ?? defaultWidget ?? const SizedBox.shrink();
      case Flavor.terminal:
        return terminalWidget ?? defaultWidget ?? const SizedBox.shrink();
    }
  }
}

/// Mixin để check flavor trong các widget
mixin FlavorAwareMixin {
  bool get isMobile => FlavorConfig.instance.isMobile;
  bool get isTerminal => FlavorConfig.instance.isTerminal;
  Flavor get currentFlavor => FlavorConfig.instance.flavor;
}

import '../../../../core/base/base_data_source.dart';
import '../../../../core/storage/secure_storage_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../models/user/user_model.dart';
import 'user_local_data_source.dart';

/// Implementation of UserLocalDataSource using BaseLocalDataSource
/// 
/// Provides local caching for user data with expiration and statistics
class UserLocalDataSourceImpl extends BaseLocalDataSource 
    with CachingMixin 
    implements UserLocalDataSource {
  
  final SecureStorageService _secureStorage;
  final AppConfig _appConfig = AppConfig();

  UserLocalDataSourceImpl({
    required SecureStorageService secureStorage,
  }) : _secureStorage = secureStorage;

  // Cache keys
  static const String _usersListPrefix = 'users_list_';
  static const String _userPrefix = 'user_';
  static const String _searchPrefix = 'search_';
  static const String _filtersKey = 'user_filters';
  static const String _sortPreferencesKey = 'user_sort_preferences';
  static const String _cacheTimestampPrefix = 'cache_timestamp_';

  @override
  Duration get cacheDuration => _appConfig.cacheUserDuration;

  @override
  Future<void> cacheUsers(List<UserModel> users, {String? cacheKey}) async {
    return await executeStorageOperation(() async {
      final key = cacheKey ?? '${_usersListPrefix}default';
      final usersJson = users.map((user) => user.toJson()).toList();
      
      await _secureStorage.writeObject(key, {'users': usersJson});
      await _saveCacheTimestamp(key);
    });
  }

  @override
  Future<List<UserModel>?> getCachedUsers({String? cacheKey}) async {
    return await executeStorageOperation(() async {
      final key = cacheKey ?? '${_usersListPrefix}default';
      
      if (await isCacheExpiredForKey(key)) {
        await _secureStorage.delete(key);
        return null;
      }
      
      final usersData = await _secureStorage.readObject(key);
      if (usersData == null) return null;

      final usersList = usersData['users'] as List<dynamic>;
      return usersList
          .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
          .toList();
    });
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    return await executeStorageOperation(() async {
      final key = '$_userPrefix${user.id}';
      await _secureStorage.writeObject(key, user.toJson());
      await _saveCacheTimestamp(key);
    });
  }

  @override
  Future<UserModel?> getCachedUser(String userId) async {
    return await executeStorageOperation(() async {
      final key = '$_userPrefix$userId';
      
      if (await isCacheExpiredForKey(key)) {
        await _secureStorage.delete(key);
        return null;
      }

      final userJson = await _secureStorage.readObject(key);
      if (userJson == null) return null;

      return UserModel.fromJson(userJson);
    });
  }

  @override
  Future<void> cacheSearchResults(String query, List<UserModel> results) async {
    return await executeStorageOperation(() async {
      final key = '$_searchPrefix${query.toLowerCase()}';
      final resultsJson = results.map((user) => user.toJson()).toList();

      await _secureStorage.writeObject(key, {'results': resultsJson});
      await _saveCacheTimestamp(key);
    });
  }

  @override
  Future<List<UserModel>?> getCachedSearchResults(String query) async {
    return await executeStorageOperation(() async {
      final key = '$_searchPrefix${query.toLowerCase()}';

      if (await isCacheExpiredForKey(key)) {
        await _secureStorage.delete(key);
        return null;
      }
      
      final resultsData = await _secureStorage.readObject(key);
      if (resultsData == null) return null;

      final resultsList = resultsData['results'] as List<dynamic>;
      return resultsList
          .map((json) => UserModel.fromJson(json as Map<String, dynamic>))
          .toList();
    });
  }

  @override
  Future<void> saveUserFilters(Map<String, dynamic> filters) async {
    return await executeStorageOperation(() async {
      await _secureStorage.writeObject(_filtersKey, filters);
    });
  }

  @override
  Future<Map<String, dynamic>?> getUserFilters() async {
    return await executeStorageOperation(() async {
      return await _secureStorage.readObject(_filtersKey);
    });
  }

  @override
  Future<void> saveUserSortPreferences(String sortBy, String sortDirection) async {
    return await executeStorageOperation(() async {
      final preferences = {
        'sortBy': sortBy,
        'sortDirection': sortDirection,
        'timestamp': DateTime.now().toIso8601String(),
      };
      await _secureStorage.writeObject(_sortPreferencesKey, preferences);
    });
  }

  @override
  Future<Map<String, String>?> getUserSortPreferences() async {
    return await executeStorageOperation(() async {
      final preferences = await _secureStorage.readObject(_sortPreferencesKey);
      if (preferences == null) return null;
      
      return {
        'sortBy': preferences['sortBy'] as String? ?? 'name',
        'sortDirection': preferences['sortDirection'] as String? ?? 'asc',
      };
    });
  }

  @override
  Future<void> clearUserCache() async {
    return await executeStorageOperation(() async {
      final allKeys = await _secureStorage.getAllKeys();
      final userKeys = allKeys.where((key) => 
          key.startsWith(_usersListPrefix) ||
          key.startsWith(_userPrefix) ||
          key.startsWith(_searchPrefix) ||
          key.startsWith(_cacheTimestampPrefix) ||
          key == _filtersKey ||
          key == _sortPreferencesKey
      ).toList();
      
      for (final key in userKeys) {
        await _secureStorage.delete(key);
      }
    });
  }

  @override
  Future<void> clearExpiredCache() async {
    return await executeStorageOperation(() async {
      final allKeys = await _secureStorage.getAllKeys();
      final cacheKeys = allKeys.where((key) => 
          key.startsWith(_usersListPrefix) ||
          key.startsWith(_userPrefix) ||
          key.startsWith(_searchPrefix)
      ).toList();
      
      for (final key in cacheKeys) {
        if (await isCacheExpiredForKey(key)) {
          await _secureStorage.delete(key);
          await _secureStorage.delete('$_cacheTimestampPrefix$key');
        }
      }
    });
  }

  @override
  Future<Map<String, dynamic>> getCacheStats() async {
    return await executeStorageOperation(() async {
      final allKeys = await _secureStorage.getAllKeys();
      
      final usersListKeys = allKeys.where((key) => key.startsWith(_usersListPrefix)).length;
      final userKeys = allKeys.where((key) => key.startsWith(_userPrefix)).length;
      final searchKeys = allKeys.where((key) => key.startsWith(_searchPrefix)).length;
      
      int expiredCount = 0;
      for (final key in allKeys) {
        if ((key.startsWith(_usersListPrefix) ||
             key.startsWith(_userPrefix) ||
             key.startsWith(_searchPrefix)) &&
            await isCacheExpiredForKey(key)) {
          expiredCount++;
        }
      }
      
      return {
        'users_lists_cached': usersListKeys,
        'individual_users_cached': userKeys,
        'search_results_cached': searchKeys,
        'total_cache_entries': usersListKeys + userKeys + searchKeys,
        'expired_entries': expiredCount,
        'cache_duration_minutes': cacheDuration.inMinutes,
        'has_filters': await _secureStorage.containsKey(_filtersKey),
        'has_sort_preferences': await _secureStorage.containsKey(_sortPreferencesKey),
      };
    });
  }

  /// Check if cache is expired for a specific key
  Future<bool> isCacheExpiredForKey(String cacheKey) async {
    return await executeStorageOperation(() async {
      final timestampKey = '$_cacheTimestampPrefix$cacheKey';
      final timestampStr = await _secureStorage.read(timestampKey);

      if (timestampStr == null) return true;

      final timestamp = DateTime.parse(timestampStr);
      return super.isCacheExpired(timestamp);
    });
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  Future<void> _saveCacheTimestamp(String cacheKey) async {
    final timestampKey = '$_cacheTimestampPrefix$cacheKey';
    await _secureStorage.write(timestampKey, DateTime.now().toIso8601String());
  }

  // ============================================================================
  // ENHANCED METHODS
  // ============================================================================

  /// Cache users with pagination info
  Future<void> cacheUsersWithPagination(
    List<UserModel> users,
    int page,
    int limit, {
    String? unitId,
    String? memberRoleId,
    String? search,
  }) async {
    return await executeStorageOperation(() async {
      final cacheKey = generateCacheKey('users_page', {
        'page': page,
        'limit': limit,
        'unitId': unitId ?? '',
        'memberRoleId': memberRoleId ?? '',
        'search': search ?? '',
      });
      
      await cacheUsers(users, cacheKey: cacheKey);
    });
  }

  /// Get cached users with pagination info
  Future<List<UserModel>?> getCachedUsersWithPagination(
    int page,
    int limit, {
    String? unitId,
    String? memberRoleId,
    String? search,
  }) async {
    return await executeStorageOperation(() async {
      final cacheKey = generateCacheKey('users_page', {
        'page': page,
        'limit': limit,
        'unitId': unitId ?? '',
        'memberRoleId': memberRoleId ?? '',
        'search': search ?? '',
      });
      
      return await getCachedUsers(cacheKey: cacheKey);
    });
  }

  /// Invalidate cache for specific filters
  Future<void> invalidateCacheForFilters({
    String? unitId,
    String? memberRoleId,
  }) async {
    return await executeStorageOperation(() async {
      final allKeys = await _secureStorage.getAllKeys();
      final keysToDelete = <String>[];
      
      for (final key in allKeys) {
        if (key.startsWith(_usersListPrefix)) {
          // Check if key contains the filters we want to invalidate
          if ((unitId != null && key.contains('unitId=$unitId')) ||
              (memberRoleId != null && key.contains('memberRoleId=$memberRoleId'))) {
            keysToDelete.add(key);
            keysToDelete.add('$_cacheTimestampPrefix$key');
          }
        }
      }
      
      for (final key in keysToDelete) {
        await _secureStorage.delete(key);
      }
    });
  }

  /// Preload frequently accessed users
  Future<void> preloadFrequentUsers(List<String> userIds) async {
    return await executeStorageOperation(() async {
      // This would typically be called with a list of frequently accessed user IDs
      // to ensure they're cached for quick access
      for (final userId in userIds) {
        final cachedUser = await getCachedUser(userId);
        if (cachedUser == null) {
          // Mark for background loading
          await _secureStorage.write('preload_$userId', 'pending');
        }
      }
    });
  }
}

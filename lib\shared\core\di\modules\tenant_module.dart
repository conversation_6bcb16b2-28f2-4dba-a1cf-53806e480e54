import 'package:get_it/get_it.dart';
import '../../../data/data_sources/remote/tenant_remote_data_source.dart';
import '../../../data/repositories/tenant_repository_impl.dart';
import '../../../domain/repositories/tenant_repository.dart';
import '../../../domain/use_cases/tenant/get_user_tenants_use_case.dart';
import '../../../domain/use_cases/tenant/create_tenant_use_case.dart';
import '../../../domain/use_cases/tenant/switch_tenant_context_use_case.dart';
import '../../../domain/use_cases/tenant/get_units_use_case.dart';
import '../../../data/services/units_api_service.dart';
import '../../network/api_client.dart';

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến Tenant Management
void registerTenantDependencies() {
  // ============================================================================
  // DATA SOURCES
  // ============================================================================

  // Remote Data Sources
  getIt.registerLazySingleton<TenantRemoteDataSource>(
    () => TenantRemoteDataSourceImpl(
      apiClient: getIt<ApiClient>(),
    ),
  );

  // ============================================================================
  // REPOSITORIES
  // ============================================================================

  getIt.registerLazySingleton<TenantRepository>(
    () => TenantRepositoryImpl(
      remoteDataSource: getIt<TenantRemoteDataSource>(),
    ),
  );

  // ============================================================================
  // USE CASES
  // ============================================================================

  getIt.registerLazySingleton<GetUserTenantsUseCase>(
    () => GetUserTenantsUseCase(
      getIt<TenantRepository>(),
    ),
  );

  getIt.registerLazySingleton<CreateTenantUseCase>(
    () => CreateTenantUseCase(
      getIt<TenantRepository>(),
    ),
  );

  getIt.registerLazySingleton<SwitchTenantContextUseCase>(
    () => SwitchTenantContextUseCase(
      getIt<TenantRepository>(),
    ),
  );

  getIt.registerLazySingleton<GetUnitsUseCase>(
    () => GetUnitsUseCase(
      getIt<TenantRepository>(),
    ),
  );

  // ============================================================================
  // SERVICES
  // ============================================================================

  getIt.registerLazySingleton<UnitsApiService>(
    () => UnitsApiService(),
  );
}

/// Unregister tenant dependencies (for testing or cleanup)
void unregisterTenantDependencies() {
  if (getIt.isRegistered<UnitsApiService>()) {
    getIt.unregister<UnitsApiService>();
  }
  if (getIt.isRegistered<GetUnitsUseCase>()) {
    getIt.unregister<GetUnitsUseCase>();
  }
  if (getIt.isRegistered<SwitchTenantContextUseCase>()) {
    getIt.unregister<SwitchTenantContextUseCase>();
  }
  if (getIt.isRegistered<CreateTenantUseCase>()) {
    getIt.unregister<CreateTenantUseCase>();
  }
  if (getIt.isRegistered<GetUserTenantsUseCase>()) {
    getIt.unregister<GetUserTenantsUseCase>();
  }
  if (getIt.isRegistered<TenantRepository>()) {
    getIt.unregister<TenantRepository>();
  }
  if (getIt.isRegistered<TenantRemoteDataSource>()) {
    getIt.unregister<TenantRemoteDataSource>();
  }
}

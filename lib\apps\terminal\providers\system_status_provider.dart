import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../../shared/core/network/network_info.dart' as network;
import '../../../shared/database/database_config.dart';

/// Enum for different status types
enum SystemStatusType {
  database,
  network,
  server,
  sync,
}

/// Enum for status states
enum StatusState {
  active,    // Green - working properly
  inactive,  // Red - not working or error
  warning,   // Yellow - working but with issues
  unknown,   // Gray - status unknown
}

/// System status data model
class SystemStatus {
  final SystemStatusType type;
  final StatusState state;
  final String message;
  final DateTime lastUpdated;

  const SystemStatus({
    required this.type,
    required this.state,
    required this.message,
    required this.lastUpdated,
  });

  SystemStatus copyWith({
    SystemStatusType? type,
    StatusState? state,
    String? message,
    DateTime? lastUpdated,
  }) {
    return SystemStatus(
      type: type ?? this.type,
      state: state ?? this.state,
      message: message ?? this.message,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// Provider for monitoring system status
class SystemStatusProvider extends ChangeNotifier {
  // Status tracking
  final Map<SystemStatusType, SystemStatus> _statuses = {};
  
  // Services
  network.NetworkInfo? _networkInfo;
  
  // Timers for periodic checks
  Timer? _statusCheckTimer;
  Timer? _serverPingTimer;
  
  // Streams
  StreamSubscription? _connectivitySubscription;
  
  // Configuration
  static const Duration _statusCheckInterval = Duration(minutes: 1);
  static const Duration _serverPingInterval = Duration(seconds: 30);

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Get status for a specific type
  SystemStatus? getStatus(SystemStatusType type) => _statuses[type];

  /// Get all statuses
  Map<SystemStatusType, SystemStatus> get allStatuses => Map.unmodifiable(_statuses);

  /// Check if a specific status is active
  bool isStatusActive(SystemStatusType type) {
    final status = _statuses[type];
    return status?.state == StatusState.active;
  }

  /// Get database status
  SystemStatus? get databaseStatus => _statuses[SystemStatusType.database];

  /// Get network status
  SystemStatus? get networkStatus => _statuses[SystemStatusType.network];

  /// Get server status
  SystemStatus? get serverStatus => _statuses[SystemStatusType.server];

  /// Get sync status
  SystemStatus? get syncStatus => _statuses[SystemStatusType.sync];

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  /// Initialize the system status provider
  Future<void> initialize({
    network.NetworkInfo? networkInfo,
  }) async {
    _networkInfo = networkInfo;

    // Initialize all statuses as unknown
    _initializeStatuses();

    // Start monitoring
    await _startMonitoring();

    // Perform initial status check
    await _performFullStatusCheck();
  }

  /// Initialize all statuses with unknown state
  void _initializeStatuses() {
    final now = DateTime.now();
    for (final type in SystemStatusType.values) {
      _statuses[type] = SystemStatus(
        type: type,
        state: StatusState.unknown,
        message: 'Đang kiểm tra...',
        lastUpdated: now,
      );
    }
    notifyListeners();
  }

  /// Start monitoring services
  Future<void> _startMonitoring() async {
    // Start periodic status checks
    _statusCheckTimer = Timer.periodic(_statusCheckInterval, (_) {
      _performFullStatusCheck();
    });

    // Start server ping checks
    _serverPingTimer = Timer.periodic(_serverPingInterval, (_) {
      _checkServerStatus();
    });

    // Listen to connectivity changes
    if (_networkInfo != null) {
      _connectivitySubscription = _networkInfo!.onConnectivityChanged.listen(
        (connectivityResult) {
          _checkNetworkStatus();
        },
      );
    }
  }

  // ============================================================================
  // STATUS CHECKS
  // ============================================================================

  /// Perform full status check for all systems
  Future<void> _performFullStatusCheck() async {
    await Future.wait([
      _checkDatabaseStatus(),
      _checkNetworkStatus(),
      _checkServerStatus(),
      _checkSyncStatus(),
    ]);
  }

  /// Check database connection status
  Future<void> _checkDatabaseStatus() async {
    try {
      final db = await DatabaseConfig.database;
      
      // Test database with a simple query
      await db.rawQuery('SELECT 1');
      
      _updateStatus(SystemStatusType.database, SystemStatus(
        type: SystemStatusType.database,
        state: StatusState.active,
        message: 'Kết nối cơ sở dữ liệu bình thường',
        lastUpdated: DateTime.now(),
      ));
    } catch (e) {
      _updateStatus(SystemStatusType.database, SystemStatus(
        type: SystemStatusType.database,
        state: StatusState.inactive,
        message: 'Lỗi kết nối cơ sở dữ liệu: ${e.toString()}',
        lastUpdated: DateTime.now(),
      ));
    }
  }

  /// Check network connectivity status
  Future<void> _checkNetworkStatus() async {
    try {
      if (_networkInfo == null) {
        _updateStatus(SystemStatusType.network, SystemStatus(
          type: SystemStatusType.network,
          state: StatusState.unknown,
          message: 'Không thể kiểm tra kết nối mạng',
          lastUpdated: DateTime.now(),
        ));
        return;
      }

      final isConnected = await _networkInfo!.isConnected;
      final connectionType = await _networkInfo!.connectionType;

      if (isConnected) {
        String connectionMessage = 'Kết nối mạng bình thường';
        if (connectionType == ConnectivityResult.wifi) {
          connectionMessage = 'Kết nối WiFi bình thường';
        } else if (connectionType == ConnectivityResult.mobile) {
          connectionMessage = 'Kết nối dữ liệu di động bình thường';
        }

        _updateStatus(SystemStatusType.network, SystemStatus(
          type: SystemStatusType.network,
          state: StatusState.active,
          message: connectionMessage,
          lastUpdated: DateTime.now(),
        ));
      } else {
        _updateStatus(SystemStatusType.network, SystemStatus(
          type: SystemStatusType.network,
          state: StatusState.inactive,
          message: 'Không có kết nối mạng',
          lastUpdated: DateTime.now(),
        ));
      }
    } catch (e) {
      _updateStatus(SystemStatusType.network, SystemStatus(
        type: SystemStatusType.network,
        state: StatusState.inactive,
        message: 'Lỗi kiểm tra kết nối mạng: ${e.toString()}',
        lastUpdated: DateTime.now(),
      ));
    }
  }

  /// Check server connection status
  Future<void> _checkServerStatus() async {
    try {
      // For now, simulate server status based on network connectivity
      // TODO: Implement actual server ping when API client is available
      final networkOk = isStatusActive(SystemStatusType.network);

      if (networkOk) {
        _updateStatus(SystemStatusType.server, SystemStatus(
          type: SystemStatusType.server,
          state: StatusState.active,
          message: 'Kết nối server bình thường',
          lastUpdated: DateTime.now(),
        ));
      } else {
        _updateStatus(SystemStatusType.server, SystemStatus(
          type: SystemStatusType.server,
          state: StatusState.inactive,
          message: 'Không có kết nối mạng để kiểm tra server',
          lastUpdated: DateTime.now(),
        ));
      }
    } catch (e) {
      _updateStatus(SystemStatusType.server, SystemStatus(
        type: SystemStatusType.server,
        state: StatusState.inactive,
        message: 'Lỗi kiểm tra kết nối server: ${e.toString()}',
        lastUpdated: DateTime.now(),
      ));
    }
  }

  /// Check sync status (placeholder for now)
  Future<void> _checkSyncStatus() async {
    try {
      // TODO: Implement actual sync status check
      // For now, we'll simulate sync status based on network and server status
      final networkOk = isStatusActive(SystemStatusType.network);
      final serverOk = isStatusActive(SystemStatusType.server);

      if (networkOk && serverOk) {
        _updateStatus(SystemStatusType.sync, SystemStatus(
          type: SystemStatusType.sync,
          state: StatusState.active,
          message: 'Đồng bộ dữ liệu bình thường',
          lastUpdated: DateTime.now(),
        ));
      } else {
        _updateStatus(SystemStatusType.sync, SystemStatus(
          type: SystemStatusType.sync,
          state: StatusState.inactive,
          message: 'Không thể đồng bộ dữ liệu',
          lastUpdated: DateTime.now(),
        ));
      }
    } catch (e) {
      _updateStatus(SystemStatusType.sync, SystemStatus(
        type: SystemStatusType.sync,
        state: StatusState.inactive,
        message: 'Lỗi đồng bộ dữ liệu: ${e.toString()}',
        lastUpdated: DateTime.now(),
      ));
    }
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Update a specific status
  void _updateStatus(SystemStatusType type, SystemStatus status) {
    _statuses[type] = status;
    notifyListeners();
  }

  /// Force refresh all statuses
  Future<void> refreshAllStatuses() async {
    await _performFullStatusCheck();
  }

  /// Force refresh a specific status
  Future<void> refreshStatus(SystemStatusType type) async {
    switch (type) {
      case SystemStatusType.database:
        await _checkDatabaseStatus();
        break;
      case SystemStatusType.network:
        await _checkNetworkStatus();
        break;
      case SystemStatusType.server:
        await _checkServerStatus();
        break;
      case SystemStatusType.sync:
        await _checkSyncStatus();
        break;
    }
  }

  // ============================================================================
  // DISPOSAL
  // ============================================================================

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    _serverPingTimer?.cancel();
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}

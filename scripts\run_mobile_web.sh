#!/bin/bash

echo "🌐 Running C-Face Mobile App on Web..."
echo "====================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Run mobile app on web
echo "🏃 Starting mobile app on web..."
echo "📱 Target: lib/apps/mobile/main_mobile.dart"
echo "🌐 Platform: Chrome Web Browser"
echo "🔧 Mode: Debug (hot reload enabled)"
echo ""
echo "Press 'r' to hot reload, 'R' to hot restart, 'q' to quit"
echo ""

flutter run --target lib/apps/mobile/main_mobile.dart -d chrome --web-port 8080 --flavor mobile

echo ""
echo "🌐 Mobile app web session ended."

#!/usr/bin/env python3
"""
Check if current models are mock/placeholder files or real models.
This script analyzes the model files to determine their authenticity.
"""

import os
import sys
from pathlib import Path
import argparse

def analyze_file(filepath: Path) -> dict:
    """Analyze a model file to determine if it's real or mock."""
    if not filepath.exists():
        return {
            'status': 'missing',
            'size_kb': 0,
            'size_mb': 0,
            'is_mock': True,
            'reason': 'File does not exist'
        }
    
    size_bytes = filepath.stat().st_size
    size_kb = size_bytes / 1024
    size_mb = size_bytes / 1024 / 1024
    
    # Check file size - mock files are typically very small
    is_mock = False
    reason = ""
    
    if size_kb < 100:  # Less than 100KB is likely mock
        is_mock = True
        reason = f"File too small ({size_kb:.1f} KB) - likely mock/placeholder"
    elif 50 <= size_kb <= 52:  # Specific range that matches current mock files
        is_mock = True
        reason = f"File size ({size_kb:.1f} KB) matches known mock file pattern"
    
    # Check file content for mock patterns
    try:
        with open(filepath, 'rb') as f:
            header = f.read(100)  # Read first 100 bytes
            
        # Look for text patterns that indicate mock files
        header_str = header.decode('utf-8', errors='ignore').lower()
        
        if any(word in header_str for word in ['mock', 'placeholder', 'dummy', 'test']):
            is_mock = True
            reason = "File contains mock/placeholder text"
        elif header.startswith(b'This is a mock') or header.startswith(b'MOCK'):
            is_mock = True
            reason = "File starts with mock identifier"
        elif len(set(header)) < 10:  # Very low entropy (repeated bytes)
            is_mock = True
            reason = "File has low entropy - likely generated/mock data"
    
    except Exception:
        pass  # Ignore read errors
    
    # Check for valid TFLite signature if not already identified as mock
    if not is_mock:
        try:
            with open(filepath, 'rb') as f:
                header = f.read(8)
            
            # Check for TFLite magic bytes
            has_tflite_signature = False
            if len(header) >= 4:
                if (header[:4] in [b'TFL3', b'\x18\x00\x00\x00'] or 
                    header[4:8] == b'TFL3' or
                    b'TFL' in header):
                    has_tflite_signature = True
            
            if not has_tflite_signature and size_kb < 1000:
                is_mock = True
                reason = "No valid TFLite signature and small size"
            elif has_tflite_signature:
                reason = "Valid TFLite signature detected"
        
        except Exception:
            pass
    
    status = 'mock' if is_mock else 'real'
    if not reason:
        reason = "Appears to be a real model file"
    
    return {
        'status': status,
        'size_kb': size_kb,
        'size_mb': size_mb,
        'is_mock': is_mock,
        'reason': reason
    }

def check_models(models_dir: Path) -> dict:
    """Check all model files in the directory."""
    model_files = [
        "ultraface_320.tflite",
        "mobilefacenet.tflite", 
        "mediapipe_face.tflite"
    ]
    
    results = {}
    
    print(f"Checking models in: {models_dir}")
    print("="*80)
    
    for model_file in model_files:
        model_path = models_dir / model_file
        analysis = analyze_file(model_path)
        results[model_file] = analysis
        
        # Print results
        status_icon = "✗" if analysis['is_mock'] else "✓"
        status_text = "MOCK" if analysis['is_mock'] else "REAL"
        
        print(f"{status_icon} {model_file:<25} {analysis['size_kb']:>8.1f} KB  {status_text}")
        print(f"   Reason: {analysis['reason']}")
        print()
    
    return results

def print_summary(results: dict):
    """Print summary of analysis."""
    total_models = len(results)
    mock_models = sum(1 for r in results.values() if r['is_mock'])
    real_models = total_models - mock_models
    
    print("="*80)
    print("SUMMARY")
    print("="*80)
    print(f"Total models: {total_models}")
    print(f"Real models:  {real_models}")
    print(f"Mock models:  {mock_models}")
    
    if mock_models > 0:
        print(f"\n⚠ WARNING: {mock_models} mock/placeholder models detected!")
        print("These models will not work for actual face recognition.")
        print("\nTo download real models, run:")
        print("  python scripts/download_real_models.py")
        print("  python scripts/setup_models.py")
    else:
        print("\n✓ All models appear to be real!")
        print("Your face recognition setup should work correctly.")

def compare_with_expected():
    """Show expected vs actual model sizes."""
    expected_sizes = {
        "ultraface_320.tflite": {"min_kb": 80, "typical_kb": 120, "max_kb": 500},
        "mobilefacenet.tflite": {"min_kb": 400, "typical_kb": 500, "max_kb": 2000},
        "mediapipe_face.tflite": {"min_kb": 150, "typical_kb": 200, "max_kb": 1000}
    }
    
    print("\n" + "="*80)
    print("EXPECTED MODEL SIZES")
    print("="*80)
    print(f"{'Model':<25} {'Min Size':<12} {'Typical':<12} {'Max Size':<12}")
    print("-" * 65)
    
    for model, sizes in expected_sizes.items():
        print(f"{model:<25} {sizes['min_kb']:>8} KB  {sizes['typical_kb']:>8} KB  {sizes['max_kb']:>8} KB")

def main():
    parser = argparse.ArgumentParser(description="Check if models are real or mock")
    parser.add_argument(
        "--models-dir",
        default="lib/packages/face_recognition/assets/models",
        help="Directory containing models to check"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed analysis"
    )
    
    args = parser.parse_args()
    
    # Resolve models directory
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / args.models_dir
    
    if not models_dir.exists():
        print(f"✗ Models directory not found: {models_dir}")
        sys.exit(1)
    
    print("Face Recognition Model Checker")
    print("="*80)
    
    # Check models
    results = check_models(models_dir)
    
    # Print summary
    print_summary(results)
    
    # Show expected sizes
    if args.verbose:
        compare_with_expected()
    
    # Exit with error code if any mocks found
    has_mocks = any(r['is_mock'] for r in results.values())
    sys.exit(1 if has_mocks else 0)

if __name__ == "__main__":
    main()

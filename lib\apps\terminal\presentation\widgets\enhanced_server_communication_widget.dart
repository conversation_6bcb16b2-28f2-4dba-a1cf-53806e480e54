import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../models/secure_comm_models.dart';
import '../../providers/device_registration_provider.dart';
import '../../../../shared/core/config/config_helper.dart';
import '../../../../shared/core/config/ui/quick_config_widget.dart';

/// Enhanced server communication widget with device registration configuration
class EnhancedServerCommunicationWidget extends StatefulWidget {
  final SecureComm? secureComm;
  final DeviceRegistrationProvider provider;
  final VoidCallback? onRefresh;
  final Function(String serverUrl, String port, String deviceId, String deviceName)? onConnect;

  const EnhancedServerCommunicationWidget({
    super.key,
    this.secureComm,
    required this.provider,
    this.onRefresh,
    this.onConnect,
  });

  @override
  State<EnhancedServerCommunicationWidget> createState() => _EnhancedServerCommunicationWidgetState();
}

class _EnhancedServerCommunicationWidgetState extends State<EnhancedServerCommunicationWidget> 
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Connection state
  bool _isConnected = false;
  bool _isConnecting = false;
  String _connectionStatus = 'Disconnected';
  String _lastPingTime = 'Never';
  String _relayStatus = 'Unknown';
  List<String> _commandHistory = [];
  bool _isLoading = false;
  String? _lastError;

  // Controllers
  final _serverController = TextEditingController(text: 'http://10.161.80.12');
  final _portController = TextEditingController(text: '');
  final _deviceIdController = TextEditingController(text: 'terminal_001');
  final _deviceNameController = TextEditingController(text: 'Terminal Device');

  // Timers
  Timer? _pingTimer;
  Timer? _statusTimer;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _updateConnectionStatus();
    _startPeriodicTasks();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pingTimer?.cancel();
    _statusTimer?.cancel();
    _serverController.dispose();
    _portController.dispose();
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    super.dispose();
  }

  void _updateConnectionStatus() {
    final connected = widget.secureComm?.isAuthenticated ?? false;
    if (mounted) {
      setState(() {
        _isConnected = connected;
        _connectionStatus = connected ? 'Connected' : 'Disconnected';
      });
    }
  }

  void _startPeriodicTasks() {
    _pingTimer?.cancel();
    _statusTimer?.cancel();
    
    if (_isConnected) {
      _pingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        _sendPing();
      });
      
      _statusTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
        _getRelayStatus();
      });
    }
  }

  Future<void> _connectToServer() async {
    if (_isConnecting) return;

    if (mounted) {
      setState(() {
        _isConnecting = true;
        _connectionStatus = 'Connecting...';
      });
    }

    try {
      final serverInput = _serverController.text.trim();
      final portInput = _portController.text.trim();
      final deviceId = _deviceIdController.text.trim();
      final deviceName = _deviceNameController.text.trim();

      // Parse server URL intelligently
      final parsed = _parseServerUrl(serverInput, portInput);
      final serverUrl = parsed['serverUrl']!;
      final port = parsed['port']!;

      if (widget.onConnect != null) {
        await widget.onConnect!(serverUrl, port, deviceId, deviceName);
        await Future.delayed(const Duration(milliseconds: 1000));

        if (widget.secureComm != null && widget.secureComm!.isAuthenticated) {
          if (mounted) {
            setState(() {
              _isConnected = true;
              _connectionStatus = 'Connected to $serverUrl:$port';
              _lastError = null;
            });
          }
          _addToHistory('CONNECTION', 'Success', 'Connected to server successfully');
          _startPeriodicTasks();
          _getRelayStatus();
        } else {
          throw Exception('Failed to authenticate with server');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionStatus = 'Connection failed: ${e.toString()}';
          _lastError = e.toString();
        });
      }
      _addToHistory('CONNECTION', 'Error', 'Connection failed: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  Future<void> _disconnectFromServer() async {
    try {
      _pingTimer?.cancel();
      _statusTimer?.cancel();
      
      await widget.provider.unregisterDevice();
      
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectionStatus = 'Disconnected';
          _relayStatus = 'Unknown';
        });
      }
      
      _addToHistory('CONNECTION', 'Info', 'Disconnected from server');
    } catch (e) {
      _addToHistory('CONNECTION', 'Error', 'Disconnection error: $e');
    }
  }

  Map<String, String> _parseServerUrl(String serverInput, String portInput) {
    String serverUrl = serverInput;
    String port = portInput;

    if (serverUrl.startsWith('http://') || serverUrl.startsWith('https://')) {
      if (port.isNotEmpty) {
        final uri = Uri.parse(serverUrl);
        serverUrl = '${uri.scheme}://${uri.host}:$port${uri.path}';
        port = '';
      }
    } else {
      if (port.isEmpty) {
        port = '3000';
      }
      serverUrl = 'http://$serverUrl';
    }

    return {'serverUrl': serverUrl, 'port': port};
  }

  Future<void> _sendPing() async {
    if (!_isConnected || widget.secureComm == null) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'ping',
        payload: {'timestamp': DateTime.now().toIso8601String()},
      );

      if (mounted) {
        setState(() {
          _lastPingTime = DateTime.now().toString().substring(11, 19);
        });
      }

      _addToHistory('PING', 'Success', 'Pong received');
    } catch (e) {
      _addToHistory('PING', 'Error', 'Ping failed: $e');
    }
  }

  Future<void> _getRelayStatus() async {
    if (!_isConnected || widget.secureComm == null) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'get_relay_status',
        payload: {},
      );

      if (mounted) {
        setState(() {
          _relayStatus = response.data?['status'] ?? 'Unknown';
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to get relay status: $e');
      }
    }
  }

  void _addToHistory(String type, String status, String message) {
    if (mounted) {
      setState(() {
        _commandHistory.insert(0, 
          '${DateTime.now().toString().substring(11, 19)} [$type] $status: $message'
        );
        if (_commandHistory.length > 20) {
          _commandHistory.removeLast();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.router,
              color: _isConnected ? theme.colorScheme.primary : theme.colorScheme.error,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              'Server Communication',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (widget.onRefresh != null)
              IconButton(
                onPressed: widget.onRefresh,
                icon: Icon(Icons.refresh, color: theme.colorScheme.primary),
              ),
          ],
        ),
        const SizedBox(height: 8), // Reduced from 16 to 8

        // Connection Status
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _isConnected 
                ? theme.colorScheme.primaryContainer 
                : theme.colorScheme.errorContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Status: $_connectionStatus',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: _isConnected 
                      ? theme.colorScheme.onPrimaryContainer 
                      : theme.colorScheme.onErrorContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (_lastError != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Error: $_lastError',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                  ),
                ),
              ],
              if (_isConnected) ...[
                const SizedBox(height: 4),
                Text(
                  'Last Ping: $_lastPingTime | Relay: $_relayStatus',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 8), // Reduced from 16 to 8

        // Tabs
        TabBar(
          controller: _tabController,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
          indicatorColor: theme.colorScheme.primary,
          tabs: const [
            Tab(text: 'Connection'),
            Tab(text: 'Device Config'),
            Tab(text: 'Network Config'),
            Tab(text: 'Commands'),
          ],
        ),
        const SizedBox(height: 8),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildConnectionTab(),
              _buildDeviceConfigTab(),
              _buildNetworkConfigTab(),
              _buildCommandsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!_isConnected) ...[
            Text(
              'Server Configuration',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInputField('Server Address', _serverController, 
                'localhost or https://api.example.com'),
            const SizedBox(height: 8),
            _buildInputField('Port', _portController, 
                '3000 (leave empty for domains)'),
            const SizedBox(height: 8),
            _buildInputField('Device ID', _deviceIdController, 
                'terminal_001'),
            const SizedBox(height: 8),
            _buildInputField('Device Name', _deviceNameController, 
                'Terminal Device'),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isConnecting ? null : _connectToServer,
                child: _isConnecting 
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Connecting...'),
                        ],
                      )
                    : const Text('Connect'),
              ),
            ),
          ],

          if (_isConnected) ...[
            Text(
              'Connection Controls',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _sendPing,
                    child: const Text('Send Ping'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _getRelayStatus,
                    child: const Text('Get Status'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _disconnectFromServer,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Disconnect'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceConfigTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Device Registration Configuration',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Device information
          _buildInfoCard('Device Information', [
            'Device ID: ${ConfigHelper.deviceId}',
            'Device Type: Terminal',
            'Registration Status: ${widget.provider.status.name}',
            'Last Heartbeat: ${widget.provider.lastHeartbeat?.toString().substring(11, 19) ?? 'Never'}',
          ]),
          
          const SizedBox(height: 16),
          
          // Device registration constants
          QuickConfigWidget(
            title: 'Device Registration Settings',
            configKeys: const [
              'network.device_id',
              'network.base_url',
              'network.request_timeout',
              'network.max_retry_attempts',
              'network.retry_delay',
            ],
            onChanged: () => setState(() {}),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkConfigTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Network & Communication Configuration',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Network configuration
          QuickConfigWidget(
            title: 'Network Settings',
            configKeys: const [
              'network.base_api_url',
              'network.connect_timeout',
              'network.receive_timeout',
              'network.send_timeout',
            ],
            onChanged: () => setState(() {}),
          ),
          
          const SizedBox(height: 16),
          
          // Server URLs configuration
          QuickConfigWidget(
            title: 'Server URLs',
            configKeys: const [
              'network.dev_base_url',
              'network.staging_base_url',
              'network.prod_base_url',
              'network.current_base_url',
            ],
            onChanged: () => setState(() {}),
          ),
        ],
      ),
    );
  }

  Widget _buildCommandsTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Command History',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        if (_commandHistory.isEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'No commands executed yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ] else ...[
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _commandHistory.length,
                itemBuilder: (context, index) {
                  final command = _commandHistory[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    child: Text(
                      command,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
        
        const SizedBox(height: 12),
        
        // Clear history button
        if (_commandHistory.isNotEmpty)
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _commandHistory.clear();
                });
              },
              child: const Text('Clear History'),
            ),
          ),
      ],
    );
  }

  Widget _buildInputField(String label, TextEditingController controller, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            border: const OutlineInputBorder(),
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildInfoCard(String title, List<String> items) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              item,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          )),
        ],
      ),
    );
  }
} 
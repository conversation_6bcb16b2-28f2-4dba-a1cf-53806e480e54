# Flexible Configuration System Documentation

## Overview

The Flexible Configuration System provides a comprehensive, type-safe, and runtime-configurable solution for managing application parameters. It supports multiple configuration sources with priority-based loading, real-time updates, and validation.

## Architecture

### Core Components

1. **ConfigurationManager**: Central coordinator for all configuration sources
2. **ConfigProvider**: Interface for different configuration sources
3. **ConfigParametersRegistry**: Registry of all available parameters with validation
4. **ConfigHelper**: Convenient static access to configuration values

### Configuration Sources (Priority Order)

1. **Runtime** (Highest Priority) - Admin UI changes, hot reload
2. **Environment Variables** - Deployment-specific settings
3. **Local Files** - App-specific configurations
4. **Remote Server** - Centrally managed settings
5. **Hardcoded Defaults** (Lowest Priority) - Fallback values

## Quick Start

### 1. Initialize the System

```dart
// For Terminal App
await ConfigInitializer.initializeForTerminal(
  deviceId: 'terminal_001',
  enableRemoteConfig: true,
  remoteServerUrl: 'https://api.ccam.com',
);

// For Mobile App
await ConfigInitializer.initializeForMobile(
  deviceId: 'mobile_device_001',
);

// For Development
await ConfigInitializer.initializeForDevelopment();
```

### 2. Access Configuration Values

```dart
// Easy access through ConfigHelper
final quality = ConfigHelper.minFaceQualityForDetection;
final timeout = ConfigHelper.recognitionThrottleDuration;
final color = ConfigHelper.primaryColor;

// Direct access with custom defaults
final value = ConfigHelper.getValue<double>(
  'face_detection.min_quality_detection',
  0.4, // default value
);
```

### 3. Runtime Configuration Changes

```dart
// Change configuration at runtime
await ConfigHelper.setValue(
  'face_detection.min_quality_detection', 
  0.6
);

// Listen to configuration changes
ConfigHelper.changeStream.listen((event) {
  print('Config ${event.key} changed from ${event.oldValue} to ${event.newValue}');
});
```

## Configuration Categories

### Face Detection Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `face_detection.min_quality_detection` | double | 0.4 | Minimum face quality for detection (0.0-1.0) |
| `face_detection.min_quality_recognition` | double | 0.4 | Minimum face quality for recognition (0.0-1.0) |
| `face_detection.recognition_throttle_duration` | Duration | 8s | Throttle duration between recognition requests |
| `face_detection.frame_skip_count` | int | 1 | Frames to skip between processing |
| `face_detection.detection_timeout` | Duration | 80ms | Timeout for detection per frame |

### Network Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `network.base_api_url` | String | http://************:1081/api/v3.1 | Base API URL |
| `network.request_timeout` | Duration | 30s | HTTP request timeout |
| `network.max_retry_attempts` | int | 3 | Maximum retry attempts |
| `network.retry_delay` | Duration | 2s | Delay between retries |
| `network.device_id` | String | terminal_001 | Unique device identifier |

### UI Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `ui.primary_color` | Color | 0xFF2196F3 | Primary UI color |
| `ui.success_color` | Color | 0xFF4CAF50 | Success color |
| `ui.error_color` | Color | 0xFFF44336 | Error color |
| `ui.avatar_size` | double | 120.0 | Avatar display size |
| `ui.animation_duration` | Duration | 300ms | Default animation duration |

### Performance Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `performance.normal_frame_rate` | int | 30 | Normal operation FPS |
| `performance.optimized_frame_rate` | int | 15 | Optimized operation FPS |
| `performance.power_saving_delay` | Duration | 30s | Power saving mode delay |

## Environment Variables

Set environment variables with the `CCAM_` prefix:

```bash
# Face Detection
export CCAM_FACE_MIN_QUALITY_DETECTION=0.5
export CCAM_FACE_RECOGNITION_THROTTLE_MS=5000

# Network
export CCAM_API_BASE_URL=https://production-api.ccam.com
export CCAM_DEVICE_ID=terminal_main_entrance

# Performance
export CCAM_PERF_NORMAL_FPS=25
export CCAM_PERF_POWER_SAVING_DELAY_MS=20000
```

## Configuration Files

### Local Configuration File

Create `assets/config/app_config.json`:

```json
{
  "face_detection": {
    "min_quality_detection": 0.5,
    "recognition_throttle_duration": 5000
  },
  "network": {
    "base_api_url": "https://api.ccam.com/api/v3.1",
    "request_timeout": 25000
  },
  "ui": {
    "primary_color": "0xFF1976D2",
    "avatar_size": 100.0
  }
}
```

### Environment-Specific Files

- `app_config_dev.json` - Development environment
- `app_config_staging.json` - Staging environment
- `app_config_prod.json` - Production environment

## Remote Configuration

### Server Setup

```dart
// Start configuration server
final server = RemoteConfigServer(port: 3000);
await server.start();
```

### Client Configuration

```dart
await ConfigInitializer.initializeForTerminal(
  enableRemoteConfig: true,
  remoteServerUrl: 'http://config-server:3000',
  deviceId: 'terminal_001',
  authToken: 'your-auth-token',
);
```

### API Endpoints

- `GET /config/{deviceId}` - Get device configuration
- `PUT /config/{deviceId}` - Update device configuration
- `POST /config` - Create new device configuration
- `DELETE /config/{deviceId}` - Delete device configuration
- `GET /config/{deviceId}/info` - Get configuration info
- `GET /config/{deviceId}/history` - Get configuration history

## Admin Interface

### Full Admin Screen

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => AdminConfigScreen(),
  ),
);
```

### Quick Configuration Dashboard

```dart
Widget build(BuildContext context) {
  return ConfigDashboard(
    onConfigChanged: () {
      // Handle configuration changes
      setState(() {});
    },
  );
}
```

### Configuration Status Widget

```dart
Widget build(BuildContext context) {
  return ConfigStatusWidget(
    showDetails: true,
    onTap: () {
      // Navigate to full configuration
    },
  );
}
```

## Validation and Error Handling

### Parameter Validation

```dart
// Parameters are automatically validated
final parameter = ConfigParameter<double>(
  key: 'face_detection.min_quality',
  type: ConfigValueType.double,
  defaultValue: 0.4,
  minValue: 0.0,
  maxValue: 1.0,
  validator: (value) => value >= 0.0 && value <= 1.0,
);
```

### Error Handling

```dart
try {
  await ConfigHelper.setValue('invalid_key', 'invalid_value');
} catch (e) {
  if (e is ArgumentError) {
    print('Invalid configuration parameter: $e');
  }
}
```

### Configuration Validation

```dart
// Validate all configuration
final errors = ConfigInitializer.validate();
if (errors.isNotEmpty) {
  print('Configuration errors: ${errors.join(', ')}');
}
```

## Best Practices

### 1. Use Type-Safe Access

```dart
// Good: Type-safe access
final quality = ConfigHelper.minFaceQualityForDetection;

// Avoid: Generic access without type safety
final quality = ConfigHelper.getValue('face_detection.min_quality_detection', 0.4);
```

### 2. Handle Configuration Changes

```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  StreamSubscription<ConfigChangeEvent>? _configSubscription;

  @override
  void initState() {
    super.initState();
    _configSubscription = ConfigHelper.changeStream.listen((event) {
      if (event.key.startsWith('ui.')) {
        setState(() {}); // Rebuild UI when UI config changes
      }
    });
  }

  @override
  void dispose() {
    _configSubscription?.cancel();
    super.dispose();
  }
}
```

### 3. Validate Before Deployment

```dart
void main() async {
  await ConfigInitializer.initializeForProduction();
  
  final errors = ConfigInitializer.validate();
  if (errors.isNotEmpty) {
    throw Exception('Configuration validation failed: ${errors.join(', ')}');
  }
  
  runApp(MyApp());
}
```

### 4. Use Environment-Specific Initialization

```dart
Future<void> initializeApp() async {
  if (kDebugMode) {
    await ConfigInitializer.initializeForDevelopment();
  } else if (isStaging) {
    await ConfigInitializer.initializeForStaging();
  } else {
    await ConfigInitializer.initializeForProduction();
  }
}
```

## Troubleshooting

### Common Issues

1. **Configuration not loading**: Check initialization order
2. **Values not updating**: Ensure you're listening to change events
3. **Validation errors**: Check parameter constraints and types
4. **Remote config failing**: Verify server connectivity and authentication

### Debug Information

```dart
// Get configuration statistics
final stats = ConfigInitializer.getStatistics();
print('Config stats: $stats');

// Export current configuration
final config = ConfigHelper.export();
print('Current config: $config');

// Check validation status
final errors = ConfigHelper.validate();
print('Validation errors: $errors');
```

## Migration Guide

### From Hardcoded Constants

1. Replace constant classes with ConfigHelper calls
2. Add parameters to ConfigParametersRegistry
3. Update initialization code
4. Test with different configuration sources

### Example Migration

```dart
// Before
class Constants {
  static const double minFaceQuality = 0.4;
}

// After
// Use ConfigHelper.minFaceQualityForDetection
// Or ConfigHelper.getValue('face_detection.min_quality_detection', 0.4)
```

## Performance Considerations

- Configuration access is optimized for frequent reads
- Changes are batched and validated efficiently
- Remote configuration includes caching and fallback
- Memory usage scales with number of parameters (typically < 1MB)

## Security

- Secure parameters are excluded from exports by default
- Environment variables support sensitive configuration
- Remote configuration supports authentication tokens
- Configuration changes are logged for audit trails

import 'dart:async';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';

import 'detection_engine.dart';
import 'engines/ultraface_engine.dart';
import 'engines/mediapipe_engine.dart';
import 'engines/mock_ultraface_engine.dart';

/// Hybrid face detection system that manages multiple detection engines
/// Optimized for Telpo F8 RK3399 with intelligent engine switching
class HybridDetector {
  final List<DetectionEngine> _engines = [];
  DetectionEngine? _primaryEngine;
  DetectionEngine? _fallbackEngine;
  
  bool _isInitialized = false;
  bool _isDetecting = false;
  
  // Performance monitoring
  int _totalDetections = 0;
  int _primaryEngineUsage = 0;
  int _fallbackEngineUsage = 0;
  double _averageDetectionTime = 0.0;
  DateTime _lastDetectionTime = DateTime.now();
  
  // Configuration
  double _confidenceThreshold = 0.7;
  int _maxFaces = 3;
  bool _enableFallback = true;
  Duration _engineSwitchTimeout = const Duration(seconds: 2);
  
  /// Create hybrid detector for Telpo F8
  static Future<HybridDetector> createForTelpoF8({
    double confidenceThreshold = 0.7,
    int maxFaces = 3,
    bool enableFallback = true,
  }) async {
    final detector = HybridDetector._();
    
    await detector._initializeForTelpoF8(
      confidenceThreshold: confidenceThreshold,
      maxFaces: maxFaces,
      enableFallback: enableFallback,
    );
    
    return detector;
  }
  
  HybridDetector._();
  
  /// Initialize for Telpo F8 device
  Future<void> _initializeForTelpoF8({
    required double confidenceThreshold,
    required int maxFaces,
    required bool enableFallback,
  }) async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing Hybrid Detector for Telpo F8...');
      }
      
      _confidenceThreshold = confidenceThreshold;
      _maxFaces = maxFaces;
      _enableFallback = enableFallback;
      
      // Initialize UltraFace as primary engine (BlazeFace model)
      final ultraFaceEngine = UltraFaceEngine();
      ultraFaceEngine.configure(
        confidenceThreshold: confidenceThreshold,
        maxFaces: maxFaces,
      );
      await ultraFaceEngine.initialize();

      _engines.add(ultraFaceEngine);
      _primaryEngine = ultraFaceEngine;

      if (kDebugMode) {
        print('✅ Primary engine (UltraFace/BlazeFace) initialized');
      }

      // Initialize MediaPipe as fallback engine
      if (enableFallback) {
        final mediaPipeEngine = MediaPipeEngine();
        mediaPipeEngine.configure(
          confidenceThreshold: confidenceThreshold * 0.8, // Slightly lower threshold for fallback
          maxFaces: maxFaces,
          enableLandmarks: true, // Enable landmarks for MediaPipe
        );
        await mediaPipeEngine.initialize();
        _engines.add(mediaPipeEngine);
        _fallbackEngine = mediaPipeEngine;

        if (kDebugMode) {
          print('✅ Fallback engine (MediaPipe BlazeFace + Landmarks) initialized');
        }
      }
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Hybrid Detector initialized successfully');
        print('📊 Engines: ${_engines.length}, Primary: ${_primaryEngine?.name}, Fallback: ${_fallbackEngine?.name ?? 'None'}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Hybrid Detector: $e');
      }
      rethrow;
    }
  }
  
  /// Detect faces using hybrid engine system
  Future<List<DetectedFace>> detectFaces(CameraImage image) async {
    if (!_isInitialized || _isDetecting) {
      return [];
    }
    
    _isDetecting = true;
    final startTime = DateTime.now();
    
    try {
      List<DetectedFace> faces = [];
      
      // Try primary engine first
      if (_primaryEngine != null) {
        try {
          faces = await _primaryEngine!.detectFaces(image).timeout(_engineSwitchTimeout);
          _primaryEngineUsage++;
          
          if (kDebugMode && faces.isNotEmpty) {
            print('🎯 Primary engine detected ${faces.length} faces');
          }
          
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Primary engine failed: $e');
          }
          
          // Try fallback engine if enabled
          if (_enableFallback && _fallbackEngine != null) {
            try {
              faces = await _fallbackEngine!.detectFaces(image).timeout(_engineSwitchTimeout);
              _fallbackEngineUsage++;
              
              if (kDebugMode) {
                print('🔄 Fallback engine detected ${faces.length} faces');
              }
              
            } catch (fallbackError) {
              if (kDebugMode) {
                print('❌ Fallback engine also failed: $fallbackError');
              }
            }
          }
        }
      }
      
      // Post-process results
      faces = _postProcessResults(faces);
      
      // Update performance stats
      _updatePerformanceStats(startTime);
      
      return faces;
      
    } finally {
      _isDetecting = false;
    }
  }
  
  /// Detect faces from raw bytes
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (!_isInitialized || _isDetecting) {
      return [];
    }
    
    _isDetecting = true;
    final startTime = DateTime.now();
    
    try {
      List<DetectedFace> faces = [];
      
      // Try primary engine first
      if (_primaryEngine != null) {
        try {
          faces = await _primaryEngine!.detectFacesFromBytes(bytes, width, height)
              .timeout(_engineSwitchTimeout);
          _primaryEngineUsage++;
          
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Primary engine failed: $e');
          }
          
          // Try fallback engine if enabled
          if (_enableFallback && _fallbackEngine != null) {
            try {
              faces = await _fallbackEngine!.detectFacesFromBytes(bytes, width, height)
                  .timeout(_engineSwitchTimeout);
              _fallbackEngineUsage++;
              
            } catch (fallbackError) {
              if (kDebugMode) {
                print('❌ Fallback engine also failed: $fallbackError');
              }
            }
          }
        }
      }
      
      // Post-process results
      faces = _postProcessResults(faces);
      
      // Update performance stats
      _updatePerformanceStats(startTime);
      
      return faces;
      
    } finally {
      _isDetecting = false;
    }
  }
  
  /// Dispose all engines
  Future<void> dispose() async {
    for (final engine in _engines) {
      await engine.dispose();
    }
    
    _engines.clear();
    _primaryEngine = null;
    _fallbackEngine = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ Hybrid Detector disposed');
    }
  }
  
  /// Get comprehensive performance stats
  HybridDetectorStats getStats() {
    final engineStats = _engines.map((engine) => engine.getStats()).toList();
    
    return HybridDetectorStats(
      totalDetections: _totalDetections,
      primaryEngineUsage: _primaryEngineUsage,
      fallbackEngineUsage: _fallbackEngineUsage,
      averageDetectionTime: _averageDetectionTime,
      engineStats: engineStats,
      lastDetectionTime: _lastDetectionTime,
    );
  }
  
  /// Configure detection parameters
  void configure({
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableFallback,
    Duration? engineSwitchTimeout,
  }) {
    if (confidenceThreshold != null) {
      _confidenceThreshold = confidenceThreshold;
      // Update all engines
      for (final engine in _engines) {
        if (engine is UltraFaceEngine) {
          engine.configure(confidenceThreshold: confidenceThreshold);
        } else if (engine is MediaPipeEngine) {
          engine.configure(confidenceThreshold: confidenceThreshold);
        }
      }
    }

    if (maxFaces != null) {
      _maxFaces = maxFaces;
      // Update all engines
      for (final engine in _engines) {
        if (engine is UltraFaceEngine) {
          engine.configure(maxFaces: maxFaces);
        } else if (engine is MediaPipeEngine) {
          engine.configure(maxFaces: maxFaces);
        }
      }
    }
    
    if (enableFallback != null) {
      _enableFallback = enableFallback;
    }
    
    if (engineSwitchTimeout != null) {
      _engineSwitchTimeout = engineSwitchTimeout;
    }
  }
  
  /// Switch primary and fallback engines
  void switchEngines() {
    if (_engines.length >= 2) {
      final temp = _primaryEngine;
      _primaryEngine = _fallbackEngine;
      _fallbackEngine = temp;
      
      if (kDebugMode) {
        print('🔄 Switched engines: Primary=${_primaryEngine?.name}, Fallback=${_fallbackEngine?.name}');
      }
    }
  }
  
  /// Get current engine information
  Map<String, String> getEngineInfo() {
    return {
      'primary': _primaryEngine?.name ?? 'None',
      'fallback': _fallbackEngine?.name ?? 'None',
      'total_engines': _engines.length.toString(),
      'is_initialized': _isInitialized.toString(),
    };
  }
  
  // Private helper methods
  
  List<DetectedFace> _postProcessResults(List<DetectedFace> faces) {
    // Apply confidence threshold filter
    faces = faces.where((face) => face.confidence >= _confidenceThreshold).toList();
    
    // Sort by confidence (highest first)
    faces.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    // Limit to max faces
    if (faces.length > _maxFaces) {
      faces = faces.take(_maxFaces).toList();
    }
    
    // Apply Non-Maximum Suppression (NMS) to remove overlapping detections
    faces = _applyNMS(faces, iouThreshold: 0.5);
    
    return faces;
  }
  
  List<DetectedFace> _applyNMS(List<DetectedFace> faces, {double iouThreshold = 0.5}) {
    if (faces.length <= 1) return faces;
    
    final selected = <DetectedFace>[];
    final remaining = List<DetectedFace>.from(faces);
    
    while (remaining.isNotEmpty) {
      // Take the face with highest confidence
      final best = remaining.removeAt(0);
      selected.add(best);
      
      // Remove faces with high overlap
      remaining.removeWhere((face) {
        final iou = best.boundingBox.iou(face.boundingBox);
        return iou > iouThreshold;
      });
    }
    
    return selected;
  }
  
  void _updatePerformanceStats(DateTime startTime) {
    final detectionTime = DateTime.now().difference(startTime).inMicroseconds / 1000.0;
    
    _totalDetections++;
    _averageDetectionTime = ((_averageDetectionTime * (_totalDetections - 1)) + detectionTime) / _totalDetections;
    _lastDetectionTime = DateTime.now();
  }
}

/// Hybrid detector performance statistics
class HybridDetectorStats {
  final int totalDetections;
  final int primaryEngineUsage;
  final int fallbackEngineUsage;
  final double averageDetectionTime;
  final List<EngineStats> engineStats;
  final DateTime lastDetectionTime;
  
  const HybridDetectorStats({
    required this.totalDetections,
    required this.primaryEngineUsage,
    required this.fallbackEngineUsage,
    required this.averageDetectionTime,
    required this.engineStats,
    required this.lastDetectionTime,
  });
  
  double get primaryEngineUsagePercent {
    if (totalDetections == 0) return 0.0;
    return (primaryEngineUsage / totalDetections) * 100.0;
  }
  
  double get fallbackEngineUsagePercent {
    if (totalDetections == 0) return 0.0;
    return (fallbackEngineUsage / totalDetections) * 100.0;
  }
  
  Map<String, dynamic> toMap() {
    return {
      'totalDetections': totalDetections,
      'primaryEngineUsage': primaryEngineUsage,
      'fallbackEngineUsage': fallbackEngineUsage,
      'primaryEngineUsagePercent': primaryEngineUsagePercent,
      'fallbackEngineUsagePercent': fallbackEngineUsagePercent,
      'averageDetectionTime': averageDetectionTime,
      'engineStats': engineStats.map((stats) => stats.toMap()).toList(),
      'lastDetectionTime': lastDetectionTime.toIso8601String(),
    };
  }
}

#!/usr/bin/env python3
"""
Setup script to install dependencies and download/convert face recognition models.
This script automates the entire process of setting up real models.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Optional

class ModelSetup:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.models_dir = self.project_root / "lib" / "packages" / "face_recognition" / "assets" / "models"
        
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("Error: Python 3.8 or higher is required")
            print(f"Current version: {version.major}.{version.minor}.{version.micro}")
            return False
        
        print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_dependencies(self) -> bool:
        """Install required Python dependencies."""
        print("\n" + "="*60)
        print("Installing Python Dependencies")
        print("="*60)
        
        requirements_file = self.script_dir / "requirements.txt"
        
        if not requirements_file.exists():
            print(f"Error: Requirements file not found: {requirements_file}")
            return False
        
        try:
            # Upgrade pip first
            print("Upgrading pip...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True)
            
            # Install requirements
            print("Installing requirements...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, capture_output=True, text=True)
            
            print("✓ Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Error installing dependencies: {e}")
            if e.stdout:
                print("STDOUT:", e.stdout)
            if e.stderr:
                print("STDERR:", e.stderr)
            return False
    
    def check_existing_models(self) -> List[str]:
        """Check which models already exist."""
        model_files = [
            "ultraface_320.tflite",
            "mobilefacenet.tflite", 
            "mediapipe_face.tflite"
        ]
        
        existing_models = []
        for model_file in model_files:
            model_path = self.models_dir / model_file
            if model_path.exists() and model_path.stat().st_size > 1000:  # More than 1KB
                existing_models.append(model_file)
        
        return existing_models
    
    def backup_existing_models(self) -> bool:
        """Backup existing models before replacement."""
        existing_models = self.check_existing_models()
        
        if not existing_models:
            return True
        
        print(f"\nFound {len(existing_models)} existing models:")
        for model in existing_models:
            print(f"  - {model}")
        
        backup_dir = self.models_dir / "backup"
        backup_dir.mkdir(exist_ok=True)
        
        try:
            for model in existing_models:
                src = self.models_dir / model
                dst = backup_dir / f"{model}.backup"
                
                if dst.exists():
                    dst.unlink()
                
                src.rename(dst)
                print(f"✓ Backed up {model}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error backing up models: {e}")
            return False
    
    def download_models(self, force: bool = False) -> bool:
        """Download models using the download script."""
        print("\n" + "="*60)
        print("Downloading Face Recognition Models")
        print("="*60)
        
        download_script = self.script_dir / "download_models.py"
        
        if not download_script.exists():
            print(f"Error: Download script not found: {download_script}")
            return False
        
        try:
            cmd = [sys.executable, str(download_script), "--models-dir", str(self.models_dir)]
            if force:
                cmd.append("--force")
            
            result = subprocess.run(cmd, check=True, text=True)
            print("✓ Models downloaded successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Error downloading models: {e}")
            return False
    
    def convert_models(self, force: bool = False) -> bool:
        """Convert models using the conversion script."""
        print("\n" + "="*60)
        print("Converting Models to TensorFlow Lite")
        print("="*60)
        
        convert_script = self.script_dir / "convert_models.py"
        
        if not convert_script.exists():
            print(f"Error: Convert script not found: {convert_script}")
            return False
        
        try:
            cmd = [sys.executable, str(convert_script), "--models-dir", str(self.models_dir)]
            if force:
                cmd.append("--force")
            
            result = subprocess.run(cmd, check=True, text=True)
            print("✓ Models converted successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Error converting models: {e}")
            return False
    
    def verify_setup(self) -> bool:
        """Verify that all models are properly set up."""
        print("\n" + "="*60)
        print("Verifying Model Setup")
        print("="*60)
        
        required_models = [
            "ultraface_320.tflite",
            "mobilefacenet.tflite",
            "mediapipe_face.tflite"
        ]
        
        success_count = 0
        
        for model_name in required_models:
            model_path = self.models_dir / model_name
            
            if not model_path.exists():
                print(f"✗ {model_name}: Not found")
                continue
            
            size_mb = model_path.stat().st_size / 1024 / 1024
            
            if size_mb < 0.1:  # Less than 100KB is probably a mock file
                print(f"✗ {model_name}: File too small ({size_mb:.1f} MB)")
                continue
            
            print(f"✓ {model_name}: {size_mb:.1f} MB")
            success_count += 1
        
        print(f"\nVerification: {success_count}/{len(required_models)} models valid")
        return success_count == len(required_models)
    
    def run_setup(self, force: bool = False, skip_deps: bool = False) -> bool:
        """Run the complete setup process."""
        print("Face Recognition Model Setup")
        print("="*60)
        print(f"Project root: {self.project_root}")
        print(f"Models directory: {self.models_dir}")
        print(f"Platform: {platform.system()} {platform.machine()}")
        
        # Check Python version
        if not self.check_python_version():
            return False
        
        # Install dependencies
        if not skip_deps:
            if not self.install_dependencies():
                return False
        else:
            print("Skipping dependency installation")
        
        # Backup existing models
        if not force:
            if not self.backup_existing_models():
                return False
        
        # Download models (this will handle direct downloads)
        if not self.download_models(force):
            return False
        
        # Convert models (this will handle ONNX to TFLite conversion)
        if not self.convert_models(force):
            return False
        
        # Verify setup
        if not self.verify_setup():
            print("\n✗ Setup completed with errors")
            return False
        
        print("\n" + "="*60)
        print("✓ Setup completed successfully!")
        print("All face recognition models are now ready for use.")
        print("="*60)
        
        return True

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Setup face recognition models")
    parser.add_argument(
        "--force",
        action="store_true", 
        help="Force download/conversion even if files exist"
    )
    parser.add_argument(
        "--skip-deps",
        action="store_true",
        help="Skip dependency installation"
    )
    
    args = parser.parse_args()
    
    setup = ModelSetup()
    success = setup.run_setup(force=args.force, skip_deps=args.skip_deps)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

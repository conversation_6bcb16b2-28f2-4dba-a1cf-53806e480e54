# Task {TASK-ID}: {Task Title}

## 📋 Task Information

| Field | Value |
|:------|:------|
| **Task ID** | {TASK-ID} |
| **Title** | {Task Title} |
| **Category** | {Category} |
| **Priority** | {High/Medium/Low} |
| **Estimate** | {X hours} |
| **Status** | {Status} |
| **Dependencies** | {Dependency Task IDs} |
| **Assignee** | {Team Member} |
| **Start Date** | {YYYY-MM-DD} |
| **Completion Date** | {YYYY-MM-DD or TBD} |

## 🎯 Objective

{Clear description of what this task aims to achieve}

## 📋 Requirements

### Functional Requirements
- [ ] {Requirement 1}
- [ ] {Requirement 2}
- [ ] {Requirement 3}

### Non-Functional Requirements
- [ ] {Performance requirement}
- [ ] {Quality requirement}
- [ ] {Maintainability requirement}

## 🚨 Problems/Challenges Identified

### 1. {Problem Title}
{Description of the problem}

### 2. {Problem Title}
{Description of the problem}

## ✅ Solutions Implemented

### 1. {Solution Title}
{Detailed description of the solution}

```bash
# Code examples or commands
{example code}
```

### 2. {Solution Title}
{Detailed description of the solution}

```dart
// Code examples
{example code}
```

## 🧪 Testing & Verification

### Test Cases
1. **{Test Case Name}**
   - **Input**: {Test input}
   - **Expected**: {Expected result}
   - **Actual**: {Actual result}
   - **Status**: ✅ Pass / ❌ Fail

### Verification Checklist
- [ ] {Verification item 1}
- [ ] {Verification item 2}
- [ ] {Verification item 3}

## 📁 Files Modified

### Files Created
- `{file_path}` - {Description}
- `{file_path}` - {Description}

### Files Modified
- `{file_path}` - {Changes made}
- `{file_path}` - {Changes made}

### Files Deleted
- `{file_path}` - {Reason for deletion}

## 📊 Impact Assessment

### ✅ Positive Impacts
- **{Impact Category}**: {Description}
- **{Impact Category}**: {Description}

### ⚠️ Potential Risks
- **{Risk Category}**: {Description and mitigation}
- **{Risk Category}**: {Description and mitigation}

### 📈 Metrics
- **{Metric Name}**: {Before} → {After}
- **{Metric Name}**: {Before} → {After}

## 🔗 Dependencies

### Upstream Dependencies (Required Before This Task)
- **{TASK-ID}**: {Brief description}
- **{TASK-ID}**: {Brief description}

### Downstream Dependencies (Blocked by This Task)
- **{TASK-ID}**: {Brief description}
- **{TASK-ID}**: {Brief description}

## 🔮 Future Considerations

### Potential Enhancements
1. **{Enhancement Title}**: {Description}
2. **{Enhancement Title}**: {Description}

### Maintenance Notes
- {Maintenance consideration 1}
- {Maintenance consideration 2}

## 📝 Lessons Learned

### What Went Well
- {Positive learning 1}
- {Positive learning 2}

### What Could Be Improved
- {Improvement area 1}
- {Improvement area 2}

### Key Takeaways
- {Key takeaway 1}
- {Key takeaway 2}

## 📚 References

### Documentation
- [Related Doc 1](link) - {Description}
- [Related Doc 2](link) - {Description}

### External Resources
- [External Resource 1](link) - {Description}
- [External Resource 2](link) - {Description}

## 📸 Screenshots/Diagrams

{Include relevant screenshots, diagrams, or visual aids}

---

**Task Status**: {Current Status}  
**Last Updated**: {YYYY-MM-DD}  
**Reviewed By**: {Reviewer Name}  
**Next Steps**: {Brief description of next actions}

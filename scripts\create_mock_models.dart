#!/usr/bin/env dart

import 'dart:io';
import 'dart:typed_data';

/// Create mock TensorFlow Lite model files for testing
/// These are minimal valid TFLite files that can be loaded by the interpreter
void main() async {
  print('🔧 Creating mock TensorFlow Lite models...');
  
  final creator = MockModelCreator();
  await creator.createMockModels();
  
  print('✅ Mock models created successfully!');
}

class MockModelCreator {
  static const String modelsDir = 'lib/packages/face_recognition/assets/models';
  
  Future<void> createMockModels() async {
    // Create models directory if it doesn't exist
    final directory = Directory(modelsDir);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    // Create mock UltraFace model
    await _createMockUltraFace();
    
    // Create mock MobileFaceNet model
    await _createMockMobileFaceNet();
    
    // Create mock MediaPipe model
    await _createMockMediaPipe();
    
    // Create configuration file
    await _createConfigFile();
  }
  
  /// Create a minimal valid TensorFlow Lite model file
  Future<void> _createMockUltraFace() async {
    print('  📦 Creating UltraFace mock model...');
    
    // Create a minimal TFLite file structure
    final modelData = _createMinimalTFLiteModel(
      inputShape: [1, 320, 240, 3], // Input: 320x240 RGB image
      outputShape: [1, 4420, 2],    // Output: detections with confidence
      modelName: 'ultraface_320',
    );
    
    final file = File('$modelsDir/ultraface_320.tflite');
    await file.writeAsBytes(modelData);
    
    print('    ✅ ultraface_320.tflite (${(modelData.length / 1024).toStringAsFixed(1)}KB)');
  }
  
  /// Create mock MobileFaceNet model
  Future<void> _createMockMobileFaceNet() async {
    print('  📦 Creating MobileFaceNet mock model...');
    
    final modelData = _createMinimalTFLiteModel(
      inputShape: [1, 112, 112, 3], // Input: 112x112 face crop
      outputShape: [1, 128],         // Output: 128-dim embedding
      modelName: 'mobilefacenet',
    );
    
    final file = File('$modelsDir/mobilefacenet.tflite');
    await file.writeAsBytes(modelData);
    
    print('    ✅ mobilefacenet.tflite (${(modelData.length / 1024).toStringAsFixed(1)}KB)');
  }
  
  /// Create mock MediaPipe model
  Future<void> _createMockMediaPipe() async {
    print('  📦 Creating MediaPipe mock model...');
    
    final modelData = _createMinimalTFLiteModel(
      inputShape: [1, 128, 128, 3], // Input: 128x128 RGB image
      outputShape: [1, 896, 16],     // Output: face detections with landmarks
      modelName: 'mediapipe_face',
    );
    
    final file = File('$modelsDir/mediapipe_face.tflite');
    await file.writeAsBytes(modelData);
    
    print('    ✅ mediapipe_face.tflite (${(modelData.length / 1024).toStringAsFixed(1)}KB)');
  }
  
  /// Create a minimal valid TensorFlow Lite model
  Uint8List _createMinimalTFLiteModel({
    required List<int> inputShape,
    required List<int> outputShape,
    required String modelName,
  }) {
    // This creates a minimal TFLite file structure
    // In a real implementation, you would use actual trained models
    
    final buffer = BytesBuilder();
    
    // TFLite file header (simplified)
    buffer.add([0x54, 0x46, 0x4C, 0x33]); // "TFL3" magic number
    
    // Model metadata (simplified)
    final metadata = {
      'name': modelName,
      'version': '1.0.0',
      'input_shape': inputShape,
      'output_shape': outputShape,
      'created': DateTime.now().toIso8601String(),
    };
    
    // Add some dummy model data to make it a reasonable size
    final dummyWeights = List.generate(1024 * 50, (i) => i % 256); // 50KB of dummy weights
    buffer.add(dummyWeights);
    
    // Add metadata as JSON (for debugging)
    final metadataJson = '''
{
  "model_info": {
    "name": "$modelName",
    "input_shape": $inputShape,
    "output_shape": $outputShape,
    "type": "mock_model",
    "created": "${DateTime.now().toIso8601String()}"
  }
}
''';
    
    buffer.add(metadataJson.codeUnits);
    
    return buffer.toBytes();
  }
  
  /// Create configuration file
  Future<void> _createConfigFile() async {
    print('  ⚙️ Creating configuration file...');
    
    final configContent = '''
# UltraFace Configuration
# Auto-generated mock configuration

models:
  ultraface:
    path: "lib/packages/face_recognition/assets/models/ultraface_320.tflite"
    input_size: [320, 240]
    confidence_threshold: 0.7
    nms_threshold: 0.4
    max_faces: 5
    type: "mock"
    
  mobilefacenet:
    path: "lib/packages/face_recognition/assets/models/mobilefacenet.tflite"
    input_size: [112, 112]
    embedding_size: 128
    type: "mock"
    
  mediapipe:
    path: "lib/packages/face_recognition/assets/models/mediapipe_face.tflite"
    input_size: [128, 128]
    confidence_threshold: 0.5
    type: "mock"

performance:
  target_fps: 45
  memory_limit_mb: 150
  enable_gpu_acceleration: true
  enable_nnapi: false  # Disabled for mock models

mock_mode:
  enabled: true
  simulate_detections: true
  mock_confidence: 0.85
  mock_face_count: 1

created: ${DateTime.now().toIso8601String()}
version: "1.0.0-mock"
''';
    
    final configFile = File('$modelsDir/ultraface_config.yaml');
    await configFile.writeAsString(configContent);
    
    print('    ✅ ultraface_config.yaml created');
  }
}

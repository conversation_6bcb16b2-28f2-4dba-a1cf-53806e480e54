/// Relay Configuration Parameters
/// 
/// This file contains relay-specific configuration parameter definitions
/// that integrate with the flexible configuration system.

import '../../../packages/relay_controller/lib/relay_controller.dart';
import 'flexible_config_system.dart';

/// Relay configuration categories
class RelayConfigCategories {
  static const String relay = 'relay';
  static const String relayDevice = 'relay_device';
  static const String relayNetwork = 'relay_network';
  static const String relayTiming = 'relay_timing';
}

/// Relay configuration keys
class RelayConfigKeys {
  // Relay Device Configuration
  static const String relayEnabled = 'relay.enabled';
  static const String relayDeviceId = 'relay.device_id';
  static const String relayDeviceName = 'relay.device_name';
  static const String relayCount = 'relay.relay_count';
  static const String relayBaudRate = 'relay.baud_rate';
  static const String relayConnectionType = 'relay.connection_type';
  static const String relayAutoConnect = 'relay.auto_connect';
  static const String relayCommandTerminator = 'relay.command_terminator';
  
  // Device Profile Configuration
  static const String relayDeviceProfile = 'relay.device_profile';
  static const String relayCustomProfileName = 'relay.custom_profile_name';
  static const String relayRelayOnTemplate = 'relay.relay_on_template';
  static const String relayRelayOffTemplate = 'relay.relay_off_template';
  static const String relayRelayToggleTemplate = 'relay.relay_toggle_template';
  static const String relayRelayTimedTemplate = 'relay.relay_timed_template';
  static const String relayAllRelaysOnTemplate = 'relay.all_relays_on_template';
  static const String relayAllRelaysOffTemplate = 'relay.all_relays_off_template';
  static const String relayAllRelaysToggleTemplate = 'relay.all_relays_toggle_template';
  static const String relayCustomCommandTerminator = 'relay.custom_command_terminator';
  
  // Individual Relay Configuration
  static const String relay0Name = 'relay.relay_0_name';
  static const String relay1Name = 'relay.relay_1_name';
  static const String relay2Name = 'relay.relay_2_name';
  static const String relay3Name = 'relay.relay_3_name';
  
  static const String relay0Enabled = 'relay.relay_0_enabled';
  static const String relay1Enabled = 'relay.relay_1_enabled';
  static const String relay2Enabled = 'relay.relay_2_enabled';
  static const String relay3Enabled = 'relay.relay_3_enabled';
  
  static const String relay0DefaultTimeout = 'relay.relay_0_default_timeout';
  static const String relay1DefaultTimeout = 'relay.relay_1_default_timeout';
  static const String relay2DefaultTimeout = 'relay.relay_2_default_timeout';
  static const String relay3DefaultTimeout = 'relay.relay_3_default_timeout';
  
  // Network/Server Configuration
  static const String relayServerUrl = 'relay.server_url';
  static const String relayServerAuthToken = 'relay.server_auth_token';
  static const String relayRegistrationEnabled = 'relay.registration_enabled';
  static const String relayRegistrationInterval = 'relay.registration_interval';
  
  // Timing Configuration
  static const String relayConnectionTimeout = 'relay.connection_timeout';
  static const String relayCommandTimeout = 'relay.command_timeout';
  static const String relayRetryAttempts = 'relay.retry_attempts';
  static const String relayRetryDelay = 'relay.retry_delay';
  
  // Advanced Configuration
  static const String relayDebugLogging = 'relay.debug_logging';
  static const String relayStatusMonitoring = 'relay.status_monitoring';
  static const String relayStatusUpdateInterval = 'relay.status_update_interval';
}

/// Register relay configuration parameters
class RelayConfigParametersRegistry {
  /// Smart Duration parser for relay parameters
  static Duration _parseDuration(dynamic value) {
    final str = value.toString().trim();
    if (str.isEmpty) throw FormatException('Vui lòng nhập giá trị thời gian');

    try {
      final seconds = double.parse(str);
      if (seconds < 0) {
        throw FormatException('Thời gian phải là số dương');
      }
      return Duration(milliseconds: (seconds * 1000).round());
    } catch (e) {
      if (e is FormatException && e.message.contains('Thời gian phải là số dương')) {
        rethrow;
      }
      throw FormatException('Định dạng thời gian không hợp lệ. Vui lòng nhập số giây (ví dụ: 5 hoặc 0.5)');
    }
  }

  /// Baud rate parser
  static int _parseBaudRate(dynamic value) {
    final rate = int.parse(value.toString());
    const validRates = [9600, 19200, 38400, 57600, 115200, 230400, 460800];
    if (!validRates.contains(rate)) {
      throw FormatException('Baud rate phải là một trong: ${validRates.join(', ')}');
    }
    return rate;
  }

  /// Relay connection type parser
  static String _parseConnectionType(dynamic value) {
    const validTypes = ['usb_serial', 'bluetooth', 'wifi', 'mqtt'];
    final type = value.toString().toLowerCase();
    if (!validTypes.contains(type)) {
      throw FormatException('Loại kết nối phải là một trong: ${validTypes.join(', ')}');
    }
    return type;
  }

  /// Register all relay configuration parameters
  static List<ConfigParameter> getRelayParameters() {
    return [
      // Main Relay Configuration
      ConfigParameter<bool>(
        key: RelayConfigKeys.relayEnabled,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt tính năng điều khiển relay',
        type: ConfigValueType.boolean,
        defaultValue: false,
        environmentKey: 'RELAY_ENABLED',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayDeviceId,
        category: RelayConfigCategories.relayDevice,
        description: 'ID duy nhất của thiết bị relay (ESP32)',
        type: ConfigValueType.string,
        defaultValue: 'esp32_relay_001',
        isRequired: true,
        environmentKey: 'RELAY_DEVICE_ID',
        validator: (value) => value.isNotEmpty && value.length <= 50,
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayDeviceName,
        category: RelayConfigCategories.relayDevice,
        description: 'Tên hiển thị của thiết bị relay',
        type: ConfigValueType.string,
        defaultValue: 'ESP32 Relay Controller',
        environmentKey: 'RELAY_DEVICE_NAME',
        validator: (value) => value.isNotEmpty && value.length <= 100,
      ),

      ConfigParameter<int>(
        key: RelayConfigKeys.relayCount,
        category: RelayConfigCategories.relayDevice,
        description: 'Số lượng relay có sẵn trên thiết bị',
        type: ConfigValueType.integer,
        defaultValue: 4,
        minValue: 1,
        maxValue: 16,
        allowedValues: [1, 2, 4, 8, 16],
        environmentKey: 'RELAY_COUNT',
      ),

      ConfigParameter<int>(
        key: RelayConfigKeys.relayBaudRate,
        category: RelayConfigCategories.relayDevice,
        description: 'Tốc độ truyền dữ liệu USB Serial (bps)',
        type: ConfigValueType.integer,
        defaultValue: 115200,
        allowedValues: [9600, 19200, 38400, 57600, 115200, 230400, 460800],
        environmentKey: 'RELAY_BAUD_RATE',
        parser: _parseBaudRate,
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayConnectionType,
        category: RelayConfigCategories.relayDevice,
        description: 'Loại kết nối đến thiết bị relay',
        type: ConfigValueType.string,
        defaultValue: 'usb_serial',
        allowedValues: ['usb_serial', 'bluetooth', 'wifi', 'mqtt'],
        environmentKey: 'RELAY_CONNECTION_TYPE',
        parser: _parseConnectionType,
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relayAutoConnect,
        category: RelayConfigCategories.relayDevice,
        description: 'Tự động kết nối đến thiết bị relay đầu tiên tìm thấy',
        type: ConfigValueType.boolean,
        defaultValue: true,
        environmentKey: 'RELAY_AUTO_CONNECT',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayCommandTerminator,
        category: RelayConfigCategories.relayDevice,
        description: 'Ký tự kết thúc lệnh (để trống cho ESP32)',
        type: ConfigValueType.string,
        defaultValue: '',
        environmentKey: 'RELAY_COMMAND_TERMINATOR',
      ),

      // Device Profile Configuration
      ConfigParameter<String>(
        key: RelayConfigKeys.relayDeviceProfile,
        category: RelayConfigCategories.relayDevice,
        description: 'Loại thiết bị relay (định dạng lệnh)',
        type: ConfigValueType.string,
        defaultValue: 'esp32',
        allowedValues: ['esp32', 'arduino', 'simple', 'custom'],
        environmentKey: 'RELAY_DEVICE_PROFILE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayCustomProfileName,
        category: RelayConfigCategories.relayDevice,
        description: 'Tên profile tùy chỉnh (khi dùng custom profile)',
        type: ConfigValueType.string,
        defaultValue: 'Custom Device',
        environmentKey: 'RELAY_CUSTOM_PROFILE_NAME',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayRelayOnTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh bật relay (dùng {relay} cho số relay)',
        type: ConfigValueType.string,
        defaultValue: 'R{relay}:1',
        environmentKey: 'RELAY_ON_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayRelayOffTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh tắt relay (dùng {relay} cho số relay)',
        type: ConfigValueType.string,
        defaultValue: 'R{relay}:0',
        environmentKey: 'RELAY_OFF_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayRelayToggleTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh toggle relay (dùng {relay} cho số relay)',
        type: ConfigValueType.string,
        defaultValue: 'R{relay}:TOGGLE',
        environmentKey: 'RELAY_TOGGLE_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayRelayTimedTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh bật relay theo thời gian (dùng {relay} và {duration})',
        type: ConfigValueType.string,
        defaultValue: 'R{relay}:{duration}',
        environmentKey: 'RELAY_TIMED_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayAllRelaysOnTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh bật tất cả relay',
        type: ConfigValueType.string,
        defaultValue: 'ALL:1',
        environmentKey: 'RELAY_ALL_ON_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayAllRelaysOffTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh tắt tất cả relay',
        type: ConfigValueType.string,
        defaultValue: 'ALL:0',
        environmentKey: 'RELAY_ALL_OFF_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayAllRelaysToggleTemplate,
        category: RelayConfigCategories.relayDevice,
        description: 'Template lệnh toggle tất cả relay',
        type: ConfigValueType.string,
        defaultValue: 'ALL:TOGGLE',
        environmentKey: 'RELAY_ALL_TOGGLE_TEMPLATE',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayCustomCommandTerminator,
        category: RelayConfigCategories.relayDevice,
        description: 'Ký tự kết thúc lệnh cho custom profile',
        type: ConfigValueType.string,
        defaultValue: '',
        environmentKey: 'RELAY_CUSTOM_TERMINATOR',
      ),

      // Individual Relay Names
      ConfigParameter<String>(
        key: RelayConfigKeys.relay0Name,
        category: RelayConfigCategories.relay,
        description: 'Tên hiển thị cho Relay 0',
        type: ConfigValueType.string,
        defaultValue: 'Cửa chính',
        environmentKey: 'RELAY_0_NAME',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relay1Name,
        category: RelayConfigCategories.relay,
        description: 'Tên hiển thị cho Relay 1',
        type: ConfigValueType.string,
        defaultValue: 'Cửa phụ',
        environmentKey: 'RELAY_1_NAME',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relay2Name,
        category: RelayConfigCategories.relay,
        description: 'Tên hiển thị cho Relay 2',
        type: ConfigValueType.string,
        defaultValue: 'Đèn báo',
        environmentKey: 'RELAY_2_NAME',
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relay3Name,
        category: RelayConfigCategories.relay,
        description: 'Tên hiển thị cho Relay 3',
        type: ConfigValueType.string,
        defaultValue: 'Còi báo',
        environmentKey: 'RELAY_3_NAME',
      ),

      // Individual Relay Enable Status
      ConfigParameter<bool>(
        key: RelayConfigKeys.relay0Enabled,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt Relay 0',
        type: ConfigValueType.boolean,
        defaultValue: true,
        environmentKey: 'RELAY_0_ENABLED',
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relay1Enabled,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt Relay 1',
        type: ConfigValueType.boolean,
        defaultValue: true,
        environmentKey: 'RELAY_1_ENABLED',
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relay2Enabled,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt Relay 2',
        type: ConfigValueType.boolean,
        defaultValue: false,
        environmentKey: 'RELAY_2_ENABLED',
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relay3Enabled,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt Relay 3',
        type: ConfigValueType.boolean,
        defaultValue: false,
        environmentKey: 'RELAY_3_ENABLED',
      ),

      // Individual Relay Default Timeouts
      ConfigParameter<Duration>(
        key: RelayConfigKeys.relay0DefaultTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Thời gian mặc định bật Relay 0 (giây, 0 = vĩnh viễn)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 5),
        minValue: Duration.zero,
        maxValue: Duration(minutes: 10),
        environmentKey: 'RELAY_0_DEFAULT_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relay1DefaultTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Thời gian mặc định bật Relay 1 (giây, 0 = vĩnh viễn)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 3),
        minValue: Duration.zero,
        maxValue: Duration(minutes: 10),
        environmentKey: 'RELAY_1_DEFAULT_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relay2DefaultTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Thời gian mặc định bật Relay 2 (giây, 0 = vĩnh viễn)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 1),
        minValue: Duration.zero,
        maxValue: Duration(minutes: 10),
        environmentKey: 'RELAY_2_DEFAULT_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relay3DefaultTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Thời gian mặc định bật Relay 3 (giây, 0 = vĩnh viễn)',
        type: ConfigValueType.duration,
        defaultValue: Duration(milliseconds: 500),
        minValue: Duration.zero,
        maxValue: Duration(minutes: 10),
        environmentKey: 'RELAY_3_DEFAULT_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      // Server/Network Configuration
      ConfigParameter<String>(
        key: RelayConfigKeys.relayServerUrl,
        category: RelayConfigCategories.relayNetwork,
        description: 'URL server để đăng ký thiết bị relay',
        type: ConfigValueType.string,
        defaultValue: 'http://************:1081/api/v3.1',
        environmentKey: 'RELAY_SERVER_URL',
        validator: (value) => value.isEmpty || Uri.tryParse(value) != null,
      ),

      ConfigParameter<String>(
        key: RelayConfigKeys.relayServerAuthToken,
        category: RelayConfigCategories.relayNetwork,
        description: 'Token xác thực cho server (để trống nếu không cần)',
        type: ConfigValueType.string,
        defaultValue: '',
        isSecure: true,
        environmentKey: 'RELAY_SERVER_AUTH_TOKEN',
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relayRegistrationEnabled,
        category: RelayConfigCategories.relayNetwork,
        description: 'Bật/tắt đăng ký thiết bị với server',
        type: ConfigValueType.boolean,
        defaultValue: false,
        environmentKey: 'RELAY_REGISTRATION_ENABLED',
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relayRegistrationInterval,
        category: RelayConfigCategories.relayNetwork,
        description: 'Khoảng thời gian đăng ký lại với server (giây)',
        type: ConfigValueType.duration,
        defaultValue: Duration(minutes: 10),
        minValue: Duration(minutes: 1),
        maxValue: Duration(hours: 24),
        environmentKey: 'RELAY_REGISTRATION_INTERVAL_MS',
        parser: _parseDuration,
      ),

      // Timing Configuration
      ConfigParameter<Duration>(
        key: RelayConfigKeys.relayConnectionTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Timeout kết nối đến thiết bị relay (giây)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 10),
        minValue: Duration(seconds: 1),
        maxValue: Duration(minutes: 2),
        environmentKey: 'RELAY_CONNECTION_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relayCommandTimeout,
        category: RelayConfigCategories.relayTiming,
        description: 'Timeout gửi lệnh đến relay (giây)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 5),
        minValue: Duration(milliseconds: 500),
        maxValue: Duration(seconds: 30),
        environmentKey: 'RELAY_COMMAND_TIMEOUT_MS',
        parser: _parseDuration,
      ),

      ConfigParameter<int>(
        key: RelayConfigKeys.relayRetryAttempts,
        category: RelayConfigCategories.relayTiming,
        description: 'Số lần thử lại khi lệnh thất bại',
        type: ConfigValueType.integer,
        defaultValue: 3,
        minValue: 0,
        maxValue: 10,
        environmentKey: 'RELAY_RETRY_ATTEMPTS',
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relayRetryDelay,
        category: RelayConfigCategories.relayTiming,
        description: 'Thời gian chờ giữa các lần thử lại (giây)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 1),
        minValue: Duration(milliseconds: 100),
        maxValue: Duration(seconds: 10),
        environmentKey: 'RELAY_RETRY_DELAY_MS',
        parser: _parseDuration,
      ),

      // Advanced Configuration
      ConfigParameter<bool>(
        key: RelayConfigKeys.relayDebugLogging,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt debug logging cho relay operations',
        type: ConfigValueType.boolean,
        defaultValue: false,
        environmentKey: 'RELAY_DEBUG_LOGGING',
      ),

      ConfigParameter<bool>(
        key: RelayConfigKeys.relayStatusMonitoring,
        category: RelayConfigCategories.relay,
        description: 'Bật/tắt giám sát trạng thái relay liên tục',
        type: ConfigValueType.boolean,
        defaultValue: true,
        environmentKey: 'RELAY_STATUS_MONITORING',
      ),

      ConfigParameter<Duration>(
        key: RelayConfigKeys.relayStatusUpdateInterval,
        category: RelayConfigCategories.relayTiming,
        description: 'Khoảng thời gian cập nhật trạng thái relay (giây)',
        type: ConfigValueType.duration,
        defaultValue: Duration(seconds: 30),
        minValue: Duration(seconds: 5),
        maxValue: Duration(minutes: 10),
        environmentKey: 'RELAY_STATUS_UPDATE_INTERVAL_MS',
        parser: _parseDuration,
      ),
    ];
  }
} 
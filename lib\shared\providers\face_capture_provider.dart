import 'dart:async';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import '../models/camera_config.dart';

enum CameraStatus { initial, loading, ready, error, permissionDenied }

class FaceCaptureProvider extends ChangeNotifier with WidgetsBindingObserver {
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  CameraStatus _status = CameraStatus.initial;
  String? _errorMessage;
  bool _isInitializing = false;
  bool _isStreamingEnabled = false;
  bool _appIsActive = true;
  bool _isDisposing = false;
  bool _isDisposed = false;
  CameraConfig _currentConfig = CameraConfig.faceCapture;
  int _consecutiveStreamErrors = 0;

  // Camera image stream callback
  Function(CameraImage, CameraDescription)? _onImageAvailable;

  // Getters
  CameraController? get cameraController => _cameraController;
  List<CameraDescription> get cameras => _cameras;
  CameraStatus get status => _status;
  String? get errorMessage => _errorMessage;
  bool get isInitializing => _isInitializing;
  bool get isCameraReady =>
      _status == CameraStatus.ready && _cameraController != null;
  bool get isStreamingEnabled => _isStreamingEnabled;
  bool get isDisposing => _isDisposing;
  bool get isDisposed => _isDisposed;
  CameraDescription? get currentCamera => _cameraController?.description;
  CameraConfig get currentConfig => _currentConfig;

  FaceCaptureProvider() {
    WidgetsBinding.instance.addObserver(this);
  }

  // Set image stream callback
  void setImageStreamCallback(
    Function(CameraImage, CameraDescription)? callback,
  ) {
    _onImageAvailable = callback;
    if (_isStreamingEnabled && callback == null) {
      _stopImageStream();
    } else if (!_isStreamingEnabled && callback != null && isCameraReady) {
      _startImageStream();
    }
  }

  // Initialize camera with configuration
  Future<void> initializeCamera([CameraConfig? config]) async {
    if (_isInitializing || _isDisposing || _isDisposed) return;

    final cameraConfig = config ?? _currentConfig;

    // Prevent re-initialization if already ready with same config
    if (_status == CameraStatus.ready && _currentConfig == cameraConfig) {
      debugPrint('Camera already initialized with same config, skipping');
      return;
    }

    _currentConfig = cameraConfig;

    _isInitializing = true;
    _setStatus(CameraStatus.loading);
    _clearError();

    try {
      // Add delay to prevent rapid re-initialization
      await Future.delayed(const Duration(milliseconds: 200));

      if (_isDisposed) return;
      // Check camera permission
      if (!await _checkCameraPermission()) {
        return;
      }

      // Get available cameras
      if (!await _getAvailableCameras()) {
        return;
      }

      // Find and initialize camera
      final camera = _findCamera(cameraConfig.preferredLensDirection);
      if (camera == null) {
        _setError('Preferred camera not available');
        _setStatus(CameraStatus.error);
        return;
      }

      await _initializeCameraController(camera, cameraConfig);

      // Start image stream if requested and callback is set
      if (cameraConfig.enableImageStream && _onImageAvailable != null) {
        await _startImageStream();
      }

      _setStatus(CameraStatus.ready);
    } catch (e) {
      _setError('Failed to initialize camera: $e');
      _setStatus(CameraStatus.error);
      if (kDebugMode) {
        print('Camera initialization error: $e');
      }
    } finally {
      _isInitializing = false;
    }
  }

  // Check camera permission
  Future<bool> _checkCameraPermission() async {
    final permission = await Permission.camera.request();
    if (permission != PermissionStatus.granted) {
      _setStatus(CameraStatus.permissionDenied);
      _setError('Camera permission is required');
      return false;
    }
    return true;
  }

  // Get available cameras
  Future<bool> _getAvailableCameras() async {
    _cameras = await availableCameras();
    if (_cameras.isEmpty) {
      _setError('No cameras available');
      _setStatus(CameraStatus.error);
      return false;
    }
    return true;
  }

  // Find camera by lens direction
  CameraDescription? _findCamera(CameraLensDirection preferredDirection) {
    try {
      return _cameras.firstWhere(
        (camera) => camera.lensDirection == preferredDirection,
      );
    } catch (e) {
      // If preferred camera not found, use the first available
      return _cameras.isNotEmpty ? _cameras.first : null;
    }
  }

  // Initialize camera controller
  Future<void> _initializeCameraController(
    CameraDescription camera,
    CameraConfig config,
  ) async {
    try {
      // Dispose existing controller first to free graphics buffers
      if (_cameraController != null) {
        await _cameraController!.dispose();
        _cameraController = null;
        // Wait for graphics buffers to be freed
        await Future.delayed(const Duration(milliseconds: 500));
      }

      _cameraController = CameraController(
        camera,
        config.resolution,
        enableAudio: config.enableAudio,
        imageFormatGroup: config.platformImageFormat,
      );

      // Initialize with retry mechanism for graphics buffer issues
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          await _cameraController!.initialize();

          // Verify controller is properly initialized
          if (!_cameraController!.value.isInitialized) {
            throw Exception('Camera controller initialization failed - not initialized');
          }

          // Additional wait to ensure ImageReader is properly initialized
          // This helps prevent the ImageReader null reference error
          await Future.delayed(const Duration(milliseconds: 600));

          // Double-check that controller is still valid after delay
          if (_cameraController == null || !_cameraController!.value.isInitialized) {
            throw Exception('Camera controller became invalid after initialization');
          }

          debugPrint('✅ Camera controller initialized successfully');
          break;
        } catch (e) {
          retryCount++;
          debugPrint('❌ Camera initialization attempt $retryCount failed: $e');

          if (retryCount < maxRetries) {
            // Wait before retry with exponential backoff
            await Future.delayed(Duration(milliseconds: 500 * retryCount));

            // Dispose and recreate controller for graphics buffer issues
            if (_cameraController != null) {
              try {
                await _cameraController!.dispose();
              } catch (disposeError) {
                debugPrint('⚠️ Error disposing camera controller: $disposeError');
              }
              _cameraController = null;
              await Future.delayed(const Duration(milliseconds: 300));
            }

            _cameraController = CameraController(
              camera,
              config.resolution,
              enableAudio: config.enableAudio,
              imageFormatGroup: config.platformImageFormat,
            );
          } else {
            rethrow;
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize camera controller after retries: $e');
      rethrow;
    }
  }

  // Start image stream for real-time processing with enhanced error handling
  Future<void> _startImageStream() async {
    if (!isCameraReady || _isStreamingEnabled || _onImageAvailable == null) {
      debugPrint('⚠️ Cannot start image stream - Camera ready: $isCameraReady, Streaming: $_isStreamingEnabled, Callback: ${_onImageAvailable != null}');
      return;
    }

    // Additional safety check for concurrent stream operations
    if (_isStreamingEnabled) {
      debugPrint('⚠️ Stream already enabled, stopping first');
      await _stopImageStream();
      await Future.delayed(const Duration(milliseconds: 200));
    }

    // Retry mechanism for image stream start
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        // Progressive wait to ensure camera is fully ready
        final waitTime = 300 + (retryCount * 300); // 300ms, 600ms, 900ms
        await Future.delayed(Duration(milliseconds: waitTime));

        // Triple-check controller state before starting stream
        if (!isCameraReady || _onImageAvailable == null || _isStreamingEnabled) {
          debugPrint('⚠️ Camera not ready for image stream after delay (attempt ${retryCount + 1})');
          debugPrint('   - Camera ready: $isCameraReady');
          debugPrint('   - Callback available: ${_onImageAvailable != null}');
          debugPrint('   - Already streaming: $_isStreamingEnabled');
          if (retryCount == maxRetries - 1) return;
          retryCount++;
          continue;
        }

        // Check if camera is in valid state for streaming
        if (!_isCameraValidForStreaming()) {
          debugPrint('⚠️ Camera not valid for streaming (attempt ${retryCount + 1})');
          if (retryCount == maxRetries - 1) return;
          retryCount++;
          continue;
        }

        debugPrint('🎥 Starting image stream (attempt ${retryCount + 1})...');
        
        await _cameraController!.startImageStream((CameraImage image) {
          // Critical null safety checks to prevent AndroidCamera null check errors
          if (!_appIsActive || _onImageAvailable == null || _isDisposing) {
            return;
          }

          // Additional safety checks for camera controller state
          final controller = _cameraController;
          if (controller == null || !controller.value.isInitialized) {
            debugPrint('⚠️ Camera controller not initialized, skipping frame');
            return;
          }

          try {
            // Verify image data integrity before processing
            if (image.planes.isEmpty) {
              debugPrint('⚠️ Empty image planes, skipping frame');
              return;
            }

            // Additional check for streaming state
            if (!controller.value.isStreamingImages) {
              debugPrint('⚠️ Camera not streaming, skipping frame');
              return;
            }

            // Safe callback execution with additional null checks
            final callback = _onImageAvailable;
            if (callback != null) {
              callback(image, controller.description);
              _consecutiveStreamErrors = 0; // Reset on success
            }

          } catch (e) {
            debugPrint('❌ Error in image callback: $e');
            _consecutiveStreamErrors++;

            // Check for specific AndroidCamera errors
            if (e.toString().contains('Null check operator') ||
                e.toString().contains('null value')) {
              debugPrint('🚨 AndroidCamera null check error detected');
              _handleCameraStreamError();
            } else if (_consecutiveStreamErrors > 5) {
              debugPrint('🚨 Too many stream errors, stopping image stream');
              _stopImageStream();
            }
          }
        });
        
        _isStreamingEnabled = true;
        _consecutiveStreamErrors = 0; // Reset error counter on successful start
        debugPrint('✅ Image stream started successfully (attempt ${retryCount + 1})');
        return; // Success, exit retry loop

      } catch (e) {
        retryCount++;
        debugPrint('❌ Failed to start image stream (attempt $retryCount): $e');

        // Handle specific errors
        if (e.toString().contains('ImageReader') || e.toString().contains('getSurface')) {
          debugPrint('🔧 ImageReader null reference detected');
          
          if (retryCount >= maxRetries) {
            debugPrint('🔧 Max retries reached - attempting camera reinit');
            await _handleImageReaderError();
            return;
          } else {
            debugPrint('🔧 Retrying image stream start after ImageReader error...');
            // Longer wait for ImageReader errors
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
          }
        } else if (e.toString().contains('already streaming') || e.toString().contains('A camera has started streaming')) {
          debugPrint('🔧 Stream already active - stopping first');
          
          try {
            await _stopImageStream();
            await Future.delayed(const Duration(milliseconds: 300));
          } catch (stopError) {
            debugPrint('❌ Error stopping existing stream: $stopError');
          }
          
          if (retryCount >= maxRetries) {
            debugPrint('❌ Max retries reached for streaming conflict');
            return;
          }
        } else if (retryCount >= maxRetries) {
          // Other errors, no more retries
          debugPrint('❌ Max retries reached for image stream start');
          return;
        }
      }
    }
  }

  /// Check if camera is in a valid state for streaming
  bool _isCameraValidForStreaming() {
    if (_cameraController == null) {
      debugPrint('❌ Camera controller is null');
      return false;
    }
    
    if (!_cameraController!.value.isInitialized) {
      debugPrint('❌ Camera controller not initialized');
      return false;
    }
    
    if (_cameraController!.value.hasError) {
      debugPrint('❌ Camera controller has error: ${_cameraController!.value.errorDescription}');
      return false;
    }
    
    if (_isDisposing || _isDisposed) {
      debugPrint('❌ Camera is disposing or disposed');
      return false;
    }
    
    return true;
  }

  /// Handle ImageReader null reference error by reinitializing camera
  Future<void> _handleImageReaderError() async {
    try {
      debugPrint('🔄 Handling ImageReader error - reinitializing camera...');

      // Store current config and callback
      final currentConfig = _currentConfig;
      final wasStreaming = _isStreamingEnabled;
      final currentCallback = _onImageAvailable;

      // Clear callback to prevent issues during disposal
      _onImageAvailable = null;
      _isStreamingEnabled = false;

      // Force dispose camera controller
      if (_cameraController != null) {
        try {
          await _cameraController!.dispose();
        } catch (e) {
          debugPrint('⚠️ Error disposing camera during ImageReader recovery: $e');
        }
        _cameraController = null;
      }

      // Longer wait for Android Camera2 API cleanup
      await Future.delayed(const Duration(milliseconds: 1000));

      // Reinitialize camera without image stream first
      final configWithoutStream = currentConfig.copyWith(
        enableImageStream: false,
      );

      await initializeCamera(configWithoutStream);

      // Restore callback and start stream if needed
      if (wasStreaming && currentCallback != null) {
        _onImageAvailable = currentCallback;
        
        // Additional delay before starting stream
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Start stream with enhanced error handling
        await _startImageStream();
      }

      debugPrint('✅ Camera reinitialized after ImageReader error');
    } catch (e) {
      debugPrint('❌ Failed to handle ImageReader error: $e');
      _setError('Camera initialization failed after ImageReader error: $e');
      _setStatus(CameraStatus.error);
    }
  }

  // Stop image stream with enhanced error handling
  Future<void> _stopImageStream() async {
    if (!_isStreamingEnabled || _cameraController == null) {
      debugPrint('⚠️ Cannot stop image stream - Streaming: $_isStreamingEnabled, Controller: ${_cameraController != null}');
      return;
    }

    try {
      debugPrint('⏹️ Stopping image stream...');
      
      // Set flag first to prevent new callbacks
      _isStreamingEnabled = false;
      
      // Stop stream with timeout to prevent hanging
      await Future.any([
        _cameraController!.stopImageStream(),
        Future.delayed(const Duration(seconds: 2), () {
          throw TimeoutException('Stop image stream timeout', const Duration(seconds: 2));
        }),
      ]);
      
      // Add delay to ensure all pending callbacks are processed
      await Future.delayed(const Duration(milliseconds: 200));
      
      debugPrint('✅ Image stream stopped successfully');
    } catch (e) {
      debugPrint('❌ Failed to stop image stream: $e');
      
      // Force flag to false even if stop failed
      _isStreamingEnabled = false;
      
      // If timeout or other error, we may need to reinitialize camera
      if (e is TimeoutException || e.toString().contains('timeout')) {
        debugPrint('🔧 Stream stop timeout - camera may need reinitialization');
        _setError('Image stream stop timeout - camera may need restart');
      }
    }
  }

  // Enable/disable image stream
  Future<void> toggleImageStream(bool enable) async {
    if (enable && !_isStreamingEnabled && _onImageAvailable != null) {
      await _startImageStream();
    } else if (!enable && _isStreamingEnabled) {
      await _stopImageStream();
    }
  }

  // Reload camera (for retaking photos)
  Future<void> reloadCamera() async {
    final currentLensDirection =
        _cameraController?.description.lensDirection ??
        _currentConfig.preferredLensDirection;
    final wasStreamingEnabled = _isStreamingEnabled;

    final reloadConfig = _currentConfig.copyWith(
      preferredLensDirection: currentLensDirection,
      enableImageStream: wasStreamingEnabled,
    );

    await disposeCamera();
    await initializeCamera(reloadConfig);
  }

  // Take picture with enhanced options
  Future<XFile?> takePicture({bool? pauseStreamForCapture}) async {
    if (!isCameraReady) {
      _setError('Camera is not ready');
      return null;
    }

    final shouldPause = pauseStreamForCapture ?? _currentConfig.pauseStreamForCapture;

    try {
      // Temporarily pause stream for better capture quality
      bool wasStreaming = false;
      if (shouldPause && _isStreamingEnabled) {
        wasStreaming = true;
        await _stopImageStream();
        // Small delay to ensure stream is fully stopped
        await Future.delayed(const Duration(milliseconds: 100));
      }

      final image = await _cameraController!.takePicture();

      // Resume stream if it was active
      if (wasStreaming && _onImageAvailable != null) {
        await _startImageStream();
      }

      return image;
    } catch (e) {
      _setError('Failed to take picture: $e');
      return null;
    }
  }

  // Switch camera (front/back) with improved resource management
  Future<void> switchCamera() async {
    if (_cameras.length < 2 || !isCameraReady || _isInitializing || _isDisposing) return;

    debugPrint('🔄 Starting camera switch...');
    _isInitializing = true; // Prevent concurrent operations

    try {
      _setStatus(CameraStatus.loading);

      final currentLensDirection = _cameraController!.description.lensDirection;
      final newLensDirection = currentLensDirection == CameraLensDirection.front
          ? CameraLensDirection.back
          : CameraLensDirection.front;

      final newCamera = _cameras.firstWhere(
        (camera) => camera.lensDirection == newLensDirection,
      );

      final wasStreaming = _isStreamingEnabled;

      // Step 1: Stop image stream gracefully with proper cleanup
      if (_isStreamingEnabled) {
        debugPrint('⏹️ Stopping image stream for camera switch...');
        await _stopImageStream();
        // Wait for stream to fully stop and callbacks to complete
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // Step 2: Clear callback to prevent race conditions
      final originalCallback = _onImageAvailable;
      _onImageAvailable = null;

      // Step 3: Dispose current controller with proper resource cleanup
      if (_cameraController != null) {
        debugPrint('🧹 Disposing current camera controller...');
        final controllerToDispose = _cameraController!;
        _cameraController = null; // Clear reference immediately

        try {
          // Dispose with timeout to prevent hanging
          await Future.any([
            controllerToDispose.dispose(),
            Future.delayed(const Duration(seconds: 3), () {
              throw TimeoutException('Camera disposal timeout', const Duration(seconds: 3));
            }),
          ]);
        } catch (e) {
          debugPrint('⚠️ Error disposing camera controller during switch: $e');
          // Continue anyway - don't let disposal errors block camera switching
        }

        // Wait for Camera2 API resources to be fully released
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Step 4: Initialize new camera controller
      debugPrint('📷 Initializing new camera: ${newCamera.lensDirection}');
      await _initializeCameraController(newCamera, _currentConfig);

      // Step 5: Update current config
      _currentConfig = _currentConfig.copyWith(
        preferredLensDirection: newLensDirection,
      );

      // Step 6: Restore callback and restart stream if it was active
      _onImageAvailable = originalCallback;
      if (wasStreaming && _onImageAvailable != null) {
        debugPrint('▶️ Restarting image stream after camera switch...');
        await Future.delayed(const Duration(milliseconds: 100)); // Brief delay for stability
        await _startImageStream();
      }

      _setStatus(CameraStatus.ready);
      debugPrint('✅ Camera switch completed successfully');

      // Notify about camera switch completion
      if (_onCameraSwitched != null) {
        _onCameraSwitched!();
      }
    } catch (e) {
      debugPrint('❌ Failed to switch camera: $e');
      _setError('Failed to switch camera: $e');
      _setStatus(CameraStatus.error);
    } finally {
      _isInitializing = false;
    }
  }

  // Set camera resolution
  Future<void> setCameraResolution(ResolutionPreset resolution) async {
    if (!isCameraReady) return;

    final currentCamera = _cameraController!.description;
    final wasStreaming = _isStreamingEnabled;

    final newConfig = _currentConfig.copyWith(
      preferredLensDirection: currentCamera.lensDirection,
      resolution: resolution,
      enableImageStream: wasStreaming,
    );

    // Reinitialize with new resolution
    await disposeCamera();
    await initializeCamera(newConfig);
  }

  // Update camera configuration
  Future<void> updateConfiguration(CameraConfig newConfig) async {
    if (newConfig == _currentConfig) return;

    final wasReady = isCameraReady;
    final wasStreaming = _isStreamingEnabled;

    if (wasReady) {
      await disposeCamera();
    }

    _currentConfig = newConfig;

    if (wasReady) {
      final configWithStream = newConfig.copyWith(
        enableImageStream: wasStreaming,
      );
      await initializeCamera(configWithStream);
    }
  }

  // Get available resolutions for current camera
  List<ResolutionPreset> getAvailableResolutions() {
    return [
      ResolutionPreset.low,
      ResolutionPreset.medium,
      ResolutionPreset.high,
      ResolutionPreset.veryHigh,
      ResolutionPreset.ultraHigh,
    ];
  }

  // Get camera lens directions available
  List<CameraLensDirection> getAvailableLensDirections() {
    return _cameras.map((camera) => camera.lensDirection).toSet().toList();
  }

  // Check if camera with specific lens direction is available
  bool isCameraAvailable(CameraLensDirection direction) {
    return _cameras.any((camera) => camera.lensDirection == direction);
  }

  // Check if camera switching is currently possible
  bool get canSwitchCamera {
    return _cameras.length >= 2 &&
           isCameraReady &&
           !_isInitializing &&
           !_isDisposing;
  }

  // Callback for camera switch events
  Function()? _onCameraSwitched;

  // Set callback for camera switch events
  void setOnCameraSwitchedCallback(Function()? callback) {
    _onCameraSwitched = callback;
  }



  // Dispose camera with optimized timing for smooth modal closing
  Future<void> disposeCamera() async {
    if (_isDisposing || _isDisposed) return;

    _isDisposing = true;

    try {
      debugPrint('🧹 Starting optimized camera disposal...');

      // Stop image stream quickly without long waits
      if (_isStreamingEnabled) {
        debugPrint('⏹️ Quick stopping image stream...');
        await _stopImageStream();
        // Minimal delay for essential cleanup
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Clear callback to prevent race conditions
      _onImageAvailable = null;
      _consecutiveStreamErrors = 0; // Reset error counter

      // Dispose camera controller quickly with enhanced safety
      if (_cameraController != null) {
        debugPrint('📷 Quick disposing camera controller...');
        final controllerToDispose = _cameraController;
        _cameraController = null; // Clear reference immediately to prevent race conditions

        try {
          // Use timeout to prevent hanging on disposal
          await Future.any([
            controllerToDispose!.dispose(),
            Future.delayed(const Duration(seconds: 2)), // Timeout after 2 seconds
          ]);
        } catch (e) {
          debugPrint('⚠️ Error disposing camera controller: $e');
        }

        // Minimal wait for essential buffer cleanup
        await Future.delayed(const Duration(milliseconds: 100));
      }

      _setStatus(CameraStatus.initial);
      debugPrint('✅ Camera disposed quickly');
    } catch (e) {
      debugPrint('❌ Error disposing camera: $e');
    } finally {
      _isDisposing = false;
    }
  }

  // App lifecycle handling for battery optimization
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    switch (state) {
      case AppLifecycleState.paused:
        _appIsActive = false;
        if (_isStreamingEnabled) {
          _stopImageStream();
        }
        break;
      case AppLifecycleState.resumed:
        _appIsActive = true;
        if (_onImageAvailable != null && !_isStreamingEnabled) {
          _startImageStream();
        }
        break;
      case AppLifecycleState.detached:
        disposeCamera();
        break;
      default:
        break;
    }
  }

  // Private methods
  void _setStatus(CameraStatus status) {
    if (_isDisposed) return; // Don't update status if disposed
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    if (_isDisposed) return; // Don't update error if disposed
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    if (_isDisposed) return; // Don't clear error if disposed
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    if (_isDisposed) return;

    debugPrint('🧹 Starting FaceCaptureProvider disposal...');
    _isDisposed = true;
    WidgetsBinding.instance.removeObserver(this);

    // Clear callback first to prevent any new calls
    _onImageAvailable = null;

    // Stop streaming synchronously to avoid async issues
    if (_isStreamingEnabled) {
      _isStreamingEnabled = false;
      debugPrint('⏹️ Image streaming stopped');
    }

    // Dispose camera controller synchronously
    try {
      _cameraController?.dispose();
      _cameraController = null;
      debugPrint('📷 Camera controller disposed');
    } catch (e) {
      debugPrint('⚠️ Error disposing camera controller in dispose: $e');
    }

    debugPrint('✅ FaceCaptureProvider disposed successfully');
    super.dispose();
  }

  /// Handle camera stream errors with recovery
  void _handleCameraStreamError() {
    debugPrint('🔄 Handling camera stream error...');

    // Use a timer to avoid blocking the image stream callback
    Timer(const Duration(milliseconds: 100), () async {
      try {
        // Stop current stream
        await _stopImageStream();

        // Wait a bit for cleanup
        await Future.delayed(const Duration(milliseconds: 500));

        // Try to restart the stream if camera is still active
        if (_cameraController != null &&
            _cameraController!.value.isInitialized &&
            _appIsActive &&
            !_isDisposing) {

          debugPrint('🔄 Attempting to restart camera stream...');
          await _startImageStream();
        }
      } catch (e) {
        debugPrint('❌ Failed to recover camera stream: $e');
        // If recovery fails, mark as not streaming
        _isStreamingEnabled = false;
      }
    });
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/priority_face_processing_provider.dart';
import '../services/face_cropping_service.dart';
import '../core/constants/face_cropping_constants.dart';

/// Widget floating hiển thị hình ảnh face đã crop ở góc dưới bên phải
class FloatingCroppedFaceWidget extends StatefulWidget {
  const FloatingCroppedFaceWidget({super.key});

  @override
  State<FloatingCroppedFaceWidget> createState() => _FloatingCroppedFaceWidgetState();
}

class _FloatingCroppedFaceWidgetState extends State<FloatingCroppedFaceWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  
  String? _lastCroppedImagePath;
  bool _isVisible = false;
  bool _isDragging = false;
  
  // Position state
  double _xPosition = 0;
  double _yPosition = 0;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializePosition();
  }
  
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // Start pulse animation
    _pulseController.repeat(reverse: true);
  }
  
  void _initializePosition() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final size = MediaQuery.of(context).size;
      setState(() {
        _xPosition = size.width - 80; // 80 = widget width + padding
        _yPosition = size.height - 160; // 160 = widget height + bottom padding
      });
    });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }
  
  void _showCroppedImage(String imagePath) {
    if (_lastCroppedImagePath != imagePath) {
      setState(() {
        _lastCroppedImagePath = imagePath;
        _isVisible = true;
      });
      _animationController.forward();
      
      // Auto hide after 5 seconds
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted && !_isDragging) {
          _hideCroppedImage();
        }
      });
    }
  }
  
  void _hideCroppedImage() {
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isVisible = false;
          _lastCroppedImagePath = null;
        });
      }
    });
  }
  
  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
  }
  
  void _onPanUpdate(DragUpdateDetails details) {
    final size = MediaQuery.of(context).size;
    setState(() {
      _xPosition = (_xPosition + details.delta.dx).clamp(0.0, size.width - 70);
      _yPosition = (_yPosition + details.delta.dy).clamp(0.0, size.height - 70);
    });
  }
  
  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
    
    // Snap to edges
    final size = MediaQuery.of(context).size;
    final centerX = size.width / 2;
    
    setState(() {
      if (_xPosition < centerX) {
        _xPosition = 16; // Snap to left
      } else {
        _xPosition = size.width - 86; // Snap to right (70 + 16 padding)
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<PriorityFaceProcessingProvider>(
      builder: (context, provider, child) {
        // Listen for new cropped images
        if (provider.lastProcessedResult != null &&
            provider.lastProcessedResult!.croppedImages.isNotEmpty) {
          final croppedImagePath = provider.lastProcessedResult!.croppedImages.values.first;
          if (croppedImagePath != _lastCroppedImagePath) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showCroppedImage(croppedImagePath);
            });
          }
        }
        
        if (!_isVisible || _lastCroppedImagePath == null) {
          return const SizedBox.shrink();
        }
        
        return Positioned(
          left: _xPosition,
          top: _yPosition,
          child: GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            onTap: () => _showImageDetails(),
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _isDragging ? 1.1 : _pulseAnimation.value,
                        child: _buildFloatingWidget(),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildFloatingWidget() {
    return Container(
      width: 70,
      height: 70,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Main image (mock or real)
          ClipRRect(
            borderRadius: BorderRadius.circular(35),
            child: _buildImageWidget(),
          ),
          
          // Border and quality indicator
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(35),
              border: Border.all(
                color: FaceCroppingConstants.successColor,
                width: 3,
              ),
            ),
          ),
          
          // Quality badge
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: FaceCroppingConstants.successColor,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
          
          // Close button
          Positioned(
            top: -5,
            left: -5,
            child: GestureDetector(
              onTap: _hideCroppedImage,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildImageWidget() {
    if (_lastCroppedImagePath == null) {
      return Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(35),
        ),
        child: const Icon(
          Icons.face,
          color: Colors.grey,
          size: 30,
        ),
      );
    }

    // Check if it's a mock path
    if (_lastCroppedImagePath!.startsWith('/mock/')) {
      return Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              FaceCroppingConstants.successColor,
              FaceCroppingConstants.successColor.withValues(alpha: 0.7),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(35),
        ),
        child: const Icon(
          Icons.face,
          color: Colors.white,
          size: 30,
        ),
      );
    }

    // Try to load real image file
    return Image.file(
      File(_lastCroppedImagePath!),
      width: 70,
      height: 70,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: FaceCroppingConstants.errorColor,
            borderRadius: BorderRadius.circular(35),
          ),
          child: const Icon(
            Icons.error,
            color: Colors.white,
            size: 30,
          ),
        );
      },
    );
  }

  void _showImageDetails() {
    if (_lastCroppedImagePath == null) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Face Cropped Image',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildDetailImageWidget(),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _hideCroppedImage();
                    },
                    child: const Text('Remove'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailImageWidget() {
    if (_lastCroppedImagePath!.startsWith('/mock/')) {
      return Container(
        width: 200,
        height: 200,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              FaceCroppingConstants.successColor,
              FaceCroppingConstants.successColor.withValues(alpha: 0.7),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.face,
              color: Colors.white,
              size: 60,
            ),
            const SizedBox(height: 8),
            Text(
              'Mock Face\nCropped Image',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }

    return Image.file(
      File(_lastCroppedImagePath!),
      width: 200,
      height: 200,
      fit: BoxFit.cover,
    );
  }
}

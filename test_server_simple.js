const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Store connected devices
const devices = new Map();

// Serve static files
app.use(express.static(path.join(__dirname, 'lib/packages/relay_controller/test_server/public')));
app.use(express.json());

// Device registration endpoint
app.post('/api/register', (req, res) => {
  const { deviceId, deviceName, type } = req.body;
  
  if (!deviceId) {
    return res.status(400).json({ success: false, error: 'deviceId is required' });
  }
  
  const device = {
    id: deviceId,
    name: deviceName || `Device ${deviceId}`,
    state: false,
    lastUpdated: new Date().toISOString(),
    type: type || 'relay',
    connected: false
  };
  
  devices.set(deviceId, device);
  console.log(`Device registered: ${deviceId} (${deviceName})`);
  
  // Broadcast device list update
  io.emit('deviceStateChanged', { deviceId, device });
  
  res.json({ success: true, device });
});

// Get device list
app.get('/api/devices', (req, res) => {
  const deviceList = Array.from(devices.values());
  res.json({ devices: deviceList });
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Send current device list to new client
  const deviceList = Array.from(devices.values());
  socket.emit('deviceList', deviceList);
  
  // Handle device registration via WebSocket
  socket.on('registerDevice', (data) => {
    const { deviceId, deviceName, type } = data;
    
    if (!deviceId) {
      socket.emit('registerResult', { success: false, error: 'deviceId is required' });
      return;
    }
    
    const device = {
      id: deviceId,
      name: deviceName || `Device ${deviceId}`,
      state: false,
      lastUpdated: new Date().toISOString(),
      type: type || 'relay',
      socketId: socket.id,
      connected: true
    };
    
    devices.set(deviceId, device);
    console.log(`Device registered via WebSocket: ${deviceId} (${deviceName})`);
    
    socket.emit('registerResult', { success: true, device });
    
    // Broadcast device list update to all clients
    io.emit('deviceStateChanged', { deviceId, device });
  });
  
  // Handle device commands from dashboard
  socket.on('deviceCommand', (data) => {
    const { deviceId, type, payload, timestamp } = data;
    
    console.log(`Command received for device ${deviceId}:`, { type, payload });
    
    // Find target device
    const device = devices.get(deviceId);
    if (!device) {
      socket.emit('deviceCommandResponse', {
        deviceId,
        success: false,
        error: 'Device not found'
      });
      return;
    }
    
    // Forward command to device if it's connected
    if (device.socketId && device.connected) {
      // Find device socket
      const deviceSocket = io.sockets.sockets.get(device.socketId);
      if (deviceSocket) {
        deviceSocket.emit('serverCommand', {
          type,
          payload,
          timestamp,
          commandId: `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        });
        
        // Send acknowledgment to dashboard
        socket.emit('deviceCommandResponse', {
          deviceId,
          success: true,
          data: { message: 'Command sent to device', type, payload }
        });
      } else {
        socket.emit('deviceCommandResponse', {
          deviceId,
          success: false,
          error: 'Device socket not found'
        });
      }
    } else {
      socket.emit('deviceCommandResponse', {
        deviceId,
        success: false,
        error: 'Device not connected'
      });
    }
  });
  
  // Handle device command responses
  socket.on('deviceCommandResponse', (data) => {
    const { commandId, deviceId, success, result, error } = data;
    
    console.log(`Device ${deviceId} response:`, { commandId, success, result, error });
    
    // Broadcast response to all dashboard clients
    io.emit('deviceCommandResponse', {
      deviceId,
      commandId,
      success,
      data: result,
      error
    });
    
    // Update device state if needed
    if (success && result && result.state !== undefined) {
      const device = devices.get(deviceId);
      if (device) {
        device.state = result.state;
        device.lastUpdated = new Date().toISOString();
        devices.set(deviceId, device);
        
        io.emit('deviceStateChanged', { deviceId, device });
      }
    }
  });
  
  // Handle device messages
  socket.on('message', (data) => {
    console.log('Message from device:', data);
    
    // Broadcast to all clients
    io.emit('deviceMessage', {
      socketId: socket.id,
      data: data,
      timestamp: new Date().toISOString()
    });
  });
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
    
    // Find and mark device as disconnected
    for (const [deviceId, device] of devices.entries()) {
      if (device.socketId === socket.id) {
        device.connected = false;
        device.lastUpdated = new Date().toISOString();
        devices.set(deviceId, device);
        
        console.log(`Device ${deviceId} disconnected`);
        
        // Broadcast device state change
        io.emit('deviceStateChanged', { deviceId, device });
        break;
      }
    }
  });
});

server.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  console.log(`📱 Dashboard: http://localhost:${PORT}`);
  console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
  console.log(`📡 API: http://localhost:${PORT}/api`);
});

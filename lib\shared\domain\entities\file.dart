class FileEntity {
  final String id;
  final String bucketId;
  final String originalName;
  final String storedName;
  final String filePath;
  final int fileSize;
  final String mimeType;
  final String fileExtension;
  final String? checksum;
  final Map<String, dynamic>? metadata;
  final List<String>? tags;
  final bool isPublic;
  final int downloadCount;
  final DateTime? lastAccessedAt;
  final DateTime? expiresAt;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  FileEntity({
    required this.id,
    required this.bucketId,
    required this.originalName,
    required this.storedName,
    required this.filePath,
    required this.fileSize,
    required this.mimeType,
    required this.fileExtension,
    this.checksum,
    this.metadata,
    this.tags,
    required this.isPublic,
    required this.downloadCount,
    this.lastAccessedAt,
    this.expiresAt,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });
}

import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// Cryptographic utilities for secure communication
class CryptoUtils {
  /// Generate a secure random string
  static String generateSecureToken({int length = 32}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Generate a device ID with optional prefix
  static String generateDeviceId({String? prefix}) {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    final deviceId = base64Url.encode(bytes).replaceAll('=', '');
    return prefix != null ? '$prefix-$deviceId' : deviceId;
  }

  /// Generate hardware hash (simulated for Flutter apps)
  static String generateHardwareHash() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return sha256.convert(bytes).toString();
  }

  /// Create HMAC-SHA256 signature
  static String createHmacSignature({
    required String secretKey,
    required String message,
  }) {
    final key = utf8.encode(secretKey);
    final messageBytes = utf8.encode(message);
    
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(messageBytes);
    
    return digest.toString();
  }

  /// Verify HMAC-SHA256 signature
  static bool verifyHmacSignature({
    required String secretKey,
    required String signature,
    required String message,
  }) {
    final expectedSignature = createHmacSignature(
      secretKey: secretKey,
      message: message,
    );
    
    return _timingSafeEquals(signature, expectedSignature);
  }

  /// Timing-safe string comparison to prevent timing attacks
  static bool _timingSafeEquals(String a, String b) {
    if (a.length != b.length) return false;
    
    int result = 0;
    for (int i = 0; i < a.length; i++) {
      result |= a.codeUnitAt(i) ^ b.codeUnitAt(i);
    }
    
    return result == 0;
  }

  /// Check if timestamp is within acceptable range (prevent replay attacks)
  static bool isTimestampValid(int timestamp, {int toleranceSeconds = 300}) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final diff = (now - timestamp).abs();
    return diff <= toleranceSeconds;
  }

  /// Create device fingerprint
  static String createDeviceFingerprint({
    required String deviceId,
    required String deviceType,
    String? hardwareHash,
    String? appVersion,
    Map<String, dynamic>? additionalData,
  }) {
    final data = {
      'device_id': deviceId,
      'device_type': deviceType,
      if (hardwareHash != null) 'hardware_hash': hardwareHash,
      if (appVersion != null) 'app_version': appVersion,
      if (additionalData != null) ...additionalData,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    final message = _canonicalizeData(data);
    return sha256.convert(utf8.encode(message)).toString();
  }

  /// Canonicalize data for consistent signing
  static String _canonicalizeData(Map<String, dynamic> data) {
    final sortedKeys = data.keys.toList()..sort();
    final parts = <String>[];
    
    for (final key in sortedKeys) {
      final value = data[key];
      if (value != null) {
        String valueStr;
        if (value is Map) {
          valueStr = jsonEncode(value);
        } else if (value is List) {
          valueStr = jsonEncode(value);
        } else {
          valueStr = value.toString();
        }
        parts.add('$key=$valueStr');
      }
    }
    
    return parts.join('&');
  }

  /// Parse JWT payload (basic parsing, not verification)
  static Map<String, dynamic>? parseJwtPayload(String jwt) {
    try {
      final parts = jwt.split('.');
      if (parts.length != 3) return null;
      
      final payload = parts[1];
      // Add padding if needed
      final paddedPayload = payload + '=' * (4 - payload.length % 4);
      final decoded = base64Url.decode(paddedPayload);
      final jsonString = utf8.decode(decoded);
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Check if JWT is expired
  static bool isJwtExpired(String jwt) {
    final payload = parseJwtPayload(jwt);
    if (payload == null) return true;
    
    final exp = payload['exp'] as int?;
    if (exp == null) return false;
    
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= exp;
  }

  /// Extract device ID from JWT
  static String? getDeviceIdFromJwt(String jwt) {
    final payload = parseJwtPayload(jwt);
    return payload?['device_id'] as String?;
  }

  /// Generate a message ID
  static String generateMessageId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure().nextInt(0xFFFFFF);
    return '${timestamp.toRadixString(36)}-${random.toRadixString(36)}';
  }

  /// Hash data with SHA-256
  static String sha256Hash(String data) {
    return sha256.convert(utf8.encode(data)).toString();
  }

  /// Hash data with MD5 (for non-security purposes only)
  static String md5Hash(String data) {
    return md5.convert(utf8.encode(data)).toString();
  }

  /// Generate a nonce for one-time use
  static String generateNonce({int length = 16}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Validate message format
  static bool isValidMessageFormat(Map<String, dynamic> message) {
    final requiredFields = ['device_id', 'type', 'payload', 'timestamp'];
    
    for (final field in requiredFields) {
      if (!message.containsKey(field)) return false;
    }
    
    // Validate types
    if (message['device_id'] is! String) return false;
    if (message['type'] is! String) return false;
    if (message['payload'] is! Map) return false;
    if (message['timestamp'] is! int) return false;
    
    return true;
  }

  /// Sanitize string for safe transmission
  static String sanitizeString(String input) {
    // Remove or escape potentially dangerous characters
    return input
        .replaceAll(RegExp(r'[<>"' "']"), '')
        .replaceAll(RegExp(r'[\x00-\x1F\x7F]'), ''); // Remove control characters
  }

  /// Validate device ID format
  static bool isValidDeviceId(String deviceId) {
    // Device ID should be alphanumeric with hyphens, 3-64 characters
    final regex = RegExp(r'^[a-zA-Z0-9\-_]{3,64}$');
    return regex.hasMatch(deviceId);
  }

  /// Validate message type format
  static bool isValidMessageType(String type) {
    // Message type should be alphanumeric with underscores, 1-32 characters
    final regex = RegExp(r'^[a-zA-Z0-9_]{1,32}$');
    return regex.hasMatch(type);
  }
}

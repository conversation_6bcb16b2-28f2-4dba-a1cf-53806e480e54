import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../core/constants/app_colors.dart';

/// Custom icons được sử dụng trong ứng dụng
class AppIcons {
  AppIcons._();

  /// Icon thêm mới (plus)
  static Widget add({double size = 14, Color? color}) {
    return CustomPaint(
      size: Size(size, size),
      painter: _AddIconPainter(color: color ?? AppColors.primary),
    );
  }

  /// Icon search
  static Widget search({double size = 16, Color? color}) {
    return CustomPaint(
      size: Size(size, size),
      painter: _SearchIconPainter(color: color ?? AppColors.textSecondary),
    );
  }

  /// Icon eye (visibility)
  static Widget eye({double size = 16, Color? color}) {
    return CustomPaint(
      size: Size(size, size),
      painter: _EyeIconPainter(color: color ?? AppColors.eyeIcon),
    );
  }

  /// Icon success (check circle)
  static Widget success({double size = 18, Color? color}) {
    return CustomPaint(
      size: Size(size, size),
      painter: _SuccessIconPainter(color: color ?? AppColors.success),
    );
  }

  // ===== TAB ICONS =====

  /// Tab icon: Home
  static Widget tabHome({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_homeIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  /// Tab icon: Tools
  static Widget tabTools({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_toolsIconSvg, color ?? AppColors.textTertiary),
      ),
    );
  }

  /// Tab icon: Notifications
  static Widget tabNotifications({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_notificationIconSvg, color ?? AppColors.textTertiary),
      ),
    );
  }

  /// Tab icon: Account
  static Widget tabAccount({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_accountIconSvg, color ?? AppColors.textTertiary),
      ),
    );
  }

  // ===== TOOL ICONS =====

  /// Tool icon: Attendance (Chấm công)
  static Widget toolAttendance({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_attendanceIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  /// Tool icon: Access Control (Kiểm soát ra vào)
  static Widget toolAccessControl({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_accessControlIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  /// Tool icon: Security (Giám sát an ninh)
  static Widget toolSecurity({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_securityIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  /// Tool icon: System Admin (Quản trị hệ thống)
  static Widget toolSystemAdmin({double size = 18, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_systemAdminIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  /// Face registration icon
  static Widget faceRegistration({double size = 20, Color? color}) {
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        _updateSvgColor(_faceRegistrationIconSvg, color ?? AppColors.primary),
      ),
    );
  }

  // ===== HELPER METHODS =====

  /// Update SVG color dynamically
  static String _updateSvgColor(String svgData, Color color) {
    final colorHex =
        '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
    return svgData
        .replaceAll('#008FD3', colorHex)
        .replaceAll('#9CA5B3', colorHex);
  }

  /// Icon check mark cho notifications
  static Widget notificationCheck({double size = 14, Color? color}) {
    return SvgPicture.string(
      _notificationCheckIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon camera cho notifications
  static Widget notificationCamera({double size = 14, Color? color}) {
    return SvgPicture.string(
      _notificationCameraIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon device cho notifications
  static Widget notificationDevice({double size = 14, Color? color}) {
    return SvgPicture.string(
      _notificationDeviceIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon fire cho notifications
  static Widget notificationFire({double size = 14, Color? color}) {
    return SvgPicture.string(
      _notificationFireIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon arrow left
  static Widget arrowLeft({
    double size = 24,
    Color? color,
  }) {
    return SvgPicture.string(
      _arrowLeftIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon arrow right
  static Widget arrowRight({
    double size = 14,
    Color? color,
  }) {
    return SvgPicture.string(
      _arrowRightIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon close
  static Widget close({
    double size = 24,
    Color? color,
  }) {
    return SvgPicture.string(
      _closeIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }

  /// Icon check
  static Widget check({
    double size = 14,
    Color? color,
  }) {
    return SvgPicture.string(
      _checkIconSvg,
      width: size,
      height: size,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
    );
  }
}

class _AddIconPainter extends CustomPainter {
  final Color color;

  _AddIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);

    // Vertical line
    canvas.drawLine(
      Offset(center.dx, size.height * 0.2),
      Offset(center.dx, size.height * 0.8),
      paint,
    );

    // Horizontal line
    canvas.drawLine(
      Offset(size.width * 0.2, center.dy),
      Offset(size.width * 0.8, center.dy),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _SearchIconPainter extends CustomPainter {
  final Color color;

  _SearchIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    // Circle
    canvas.drawCircle(
      Offset(size.width * 0.458, size.height * 0.458),
      size.width * 0.375,
      paint,
    );

    // Search line
    canvas.drawLine(
      Offset(size.width * 0.729, size.height * 0.729),
      Offset(size.width * 0.917, size.height * 0.917),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _EyeIconPainter extends CustomPainter {
  final Color color;

  _EyeIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Eye shape
    final path = Path();
    path.moveTo(size.width * 0.9, size.height * 0.463);
    path.cubicTo(
      size.width * 0.9,
      size.height * 0.463,
      size.width * 0.844,
      size.height * 0.537,
      size.width * 0.5,
      size.height * 0.537,
    );
    path.cubicTo(
      size.width * 0.156,
      size.height * 0.537,
      size.width * 0.1,
      size.height * 0.463,
      size.width * 0.1,
      size.height * 0.463,
    );
    path.cubicTo(
      size.width * 0.1,
      size.height * 0.463,
      size.width * 0.156,
      size.height * 0.389,
      size.width * 0.5,
      size.height * 0.389,
    );
    path.cubicTo(
      size.width * 0.844,
      size.height * 0.389,
      size.width * 0.9,
      size.height * 0.463,
      size.width * 0.9,
      size.height * 0.463,
    );

    canvas.drawPath(path, paint);

    // Pupil
    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.463),
      size.width * 0.125,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _SuccessIconPainter extends CustomPainter {
  final Color color;

  _SuccessIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Circle background
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      paint,
    );

    // Check mark
    final checkPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    final checkPath = Path();
    checkPath.moveTo(size.width * 0.333, size.height * 0.531);
    checkPath.lineTo(size.width * 0.433, size.height * 0.625);
    checkPath.lineTo(size.width * 0.667, size.height * 0.406);

    canvas.drawPath(checkPath, checkPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// ===== SVG CONSTANTS =====

const String _homeIconSvg =
    '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.124 16.5L6.93602 13.8683C6.83496 12.4535 7.95554 11.25 9.374 11.25C10.7925 11.25 11.913 12.4535 11.812 13.8683L11.624 16.5" stroke="#008FD3" stroke-width="1.5"/>
<path d="M2.13854 9.91009C1.87378 8.18718 1.7414 7.32573 2.06712 6.56203C2.39285 5.79834 3.11552 5.27582 4.56085 4.2308L5.64074 3.45C7.43872 2.15 8.33771 1.5 9.375 1.5C10.4123 1.5 11.3113 2.15 13.1093 3.45L14.1891 4.2308C15.6345 5.27582 16.3571 5.79834 16.6829 6.56203C17.0086 7.32573 16.8762 8.18718 16.6115 9.91009L16.3857 11.3793C16.0103 13.8217 15.8227 15.0429 14.9468 15.7714C14.0708 16.5 12.7903 16.5 10.2291 16.5H8.52089C5.95975 16.5 4.67918 16.5 3.80325 15.7714C2.92732 15.0429 2.73965 13.8217 2.36432 11.3793L2.13854 9.91009Z" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
</svg>''';

const String _toolsIconSvg =
    '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_424_6058)">
<path d="M1.625 13.5C1.625 12.3447 1.625 11.767 1.88505 11.3426C2.03056 11.1052 2.2302 10.9056 2.46765 10.76C2.89201 10.5 3.46967 10.5 4.625 10.5C5.78033 10.5 6.35799 10.5 6.78235 10.76C7.0198 10.9056 7.21944 11.1052 7.36495 11.3426C7.625 11.767 7.625 12.3447 7.625 13.5C7.625 14.6553 7.625 15.233 7.36495 15.6574C7.21944 15.8948 7.0198 16.0944 6.78235 16.24C6.35799 16.5 5.78033 16.5 4.625 16.5C3.46967 16.5 2.89201 16.5 2.46765 16.24C2.2302 16.0944 2.03056 15.8948 1.88505 15.6574C1.625 15.233 1.625 14.6553 1.625 13.5Z" stroke="#9CA5B3" stroke-width="1.5"/>
<path d="M10.625 13.5C10.625 12.3447 10.625 11.767 10.885 11.3426C11.0306 11.1052 11.2302 10.9056 11.4676 10.76C11.892 10.5 12.4697 10.5 13.625 10.5C14.7803 10.5 15.358 10.5 15.7824 10.76C16.0198 10.9056 16.2194 11.1052 16.365 11.3426C16.625 11.767 16.625 12.3447 16.625 13.5C16.625 14.6553 16.625 15.233 16.365 15.6574C16.2194 15.8948 16.0198 16.0944 15.7824 16.24C15.358 16.5 14.7803 16.5 13.625 16.5C12.4697 16.5 11.892 16.5 11.4676 16.24C11.2302 16.0944 11.0306 15.8948 10.885 15.6574C10.625 15.233 10.625 14.6553 10.625 13.5Z" stroke="#9CA5B3" stroke-width="1.5"/>
<path d="M1.625 4.5C1.625 3.34467 1.625 2.76701 1.88505 2.34265C2.03056 2.1052 2.2302 1.90556 2.46765 1.76005C2.89201 1.5 3.46967 1.5 4.625 1.5C5.78033 1.5 6.35799 1.5 6.78235 1.76005C7.0198 1.90556 7.21944 2.1052 7.36495 2.34265C7.625 2.76701 7.625 3.34467 7.625 4.5C7.625 5.65533 7.625 6.23299 7.36495 6.65735C7.21944 6.8948 7.0198 7.09444 6.78235 7.23995C6.35799 7.5 5.78033 7.5 4.625 7.5C3.46967 7.5 2.89201 7.5 2.46765 7.23995C2.2302 7.09444 2.03056 6.8948 1.88505 6.65735C1.625 6.23299 1.625 5.65533 1.625 4.5Z" stroke="#9CA5B3" stroke-width="1.5"/>
<path d="M10.625 4.5C10.625 3.34467 10.625 2.76701 10.885 2.34265C11.0306 2.1052 11.2302 1.90556 11.4676 1.76005C11.892 1.5 12.4697 1.5 13.625 1.5C14.7803 1.5 15.358 1.5 15.7824 1.76005C16.0198 1.90556 16.2194 2.1052 16.365 2.34265C16.625 2.76701 16.625 3.34467 16.625 4.5C16.625 5.65533 16.625 6.23299 16.365 6.65735C16.2194 6.8948 16.0198 7.09444 15.7824 7.23995C15.358 7.5 14.7803 7.5 13.625 7.5C12.4697 7.5 11.892 7.5 11.4676 7.23995C11.2302 7.09444 11.0306 6.8948 10.885 6.65735C10.625 6.23299 10.625 5.65533 10.625 4.5Z" stroke="#9CA5B3" stroke-width="1.5"/>
</g>
<defs>
<clipPath id="clip0_424_6058">
<rect width="18" height="18" fill="white" transform="translate(0.125)"/>
</clipPath>
</defs>
</svg>''';

const String _notificationIconSvg =
    '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.77244 10.7955C2.61295 11.8103 3.326 12.5146 4.19904 12.8657C7.54611 14.2114 12.2039 14.2114 15.551 12.8657C16.424 12.5146 17.1371 11.8103 16.9776 10.7955C16.8795 10.1719 16.3949 9.6526 16.0358 9.1455C15.5655 8.47314 15.5187 7.73979 15.5187 6.95956C15.5187 3.94433 12.9919 1.5 9.875 1.5C6.7581 1.5 4.23134 3.94433 4.23134 6.95956C4.23128 7.73979 4.18454 8.47314 3.71421 9.1455C3.35513 9.6526 2.87046 10.1719 2.77244 10.7955Z" stroke="#9CA5B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.625 15.75C8.2221 16.2164 9.0106 16.5 9.875 16.5C10.7394 16.5 11.5279 16.2164 12.125 15.75" stroke="#9CA5B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _accountIconSvg =
    '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.55818 11.6112C4.4971 12.243 1.71502 13.5331 3.4095 15.1474C4.23723 15.936 5.15912 16.5 6.31815 16.5H12.9318C14.0909 16.5 15.0128 15.936 15.8405 15.1474C17.535 13.5331 14.7529 12.243 13.6918 11.6112C11.2036 10.1296 8.04639 10.1296 5.55818 11.6112Z" stroke="#9CA5B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13 4.875C13 6.73896 11.489 8.25 9.625 8.25C7.76104 8.25 6.25 6.73896 6.25 4.875C6.25 3.01104 7.76104 1.5 9.625 1.5C11.489 1.5 13 3.01104 13 4.875Z" stroke="#9CA5B3" stroke-width="1.5"/>
</svg>''';

// ===== TOOL ICONS SVG =====

const String _attendanceIconSvg =
    '''<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.875 9C1.875 5.64124 1.875 3.96186 2.91843 2.91843C3.96186 1.875 5.64124 1.875 9 1.875C12.3588 1.875 14.0381 1.875 15.0816 2.91843C16.125 3.96186 16.125 5.64124 16.125 9C16.125 12.3588 16.125 14.0381 15.0816 15.0816C14.0381 16.125 12.3588 16.125 9 16.125C5.64124 16.125 3.96186 16.125 2.91843 15.0816C1.875 14.0381 1.875 12.3588 1.875 9Z" stroke="#008FD3" stroke-width="1.5"/>
<path d="M5.625 12.75C7.37378 10.9184 10.6074 10.8321 12.375 12.75M10.8713 7.125C10.8713 8.16053 10.0307 9 8.99365 9C7.95664 9 7.11598 8.16053 7.11598 7.125C7.11598 6.08947 7.95664 5.25 8.99365 5.25C10.0307 5.25 10.8713 6.08947 10.8713 7.125Z" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
</svg>''';

const String _accessControlIconSvg =
    '''<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_194_1537)">
<path d="M5.25 1.5C3.17893 1.5 1.5 3.17893 1.5 5.25C1.5 6.63803 2.25412 7.84992 3.375 8.49832V13.3824C3.375 13.9955 3.375 14.302 3.48918 14.5777C3.60336 14.8534 3.82013 15.0701 4.25368 15.5037L5.25 16.5L6.83115 14.9189C6.90407 14.8459 6.94055 14.8094 6.9708 14.7699C7.05023 14.666 7.10112 14.5431 7.11841 14.4135C7.125 14.3641 7.125 14.3126 7.125 14.2094C7.125 14.126 7.125 14.0842 7.12058 14.0437C7.10898 13.9373 7.07478 13.8347 7.02026 13.7427C6.99947 13.7076 6.97443 13.6742 6.92434 13.6075L6 12.375L6.525 11.675C6.82237 11.2785 6.97105 11.0803 7.04802 10.8493C7.125 10.6184 7.125 10.3706 7.125 9.875V8.49832C8.24588 7.84992 9 6.63803 9 5.25C9 3.17893 7.32107 1.5 5.25 1.5Z" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M5.25 5.25H5.25674" stroke="#008FD3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.75 10.5H14.25C14.9489 10.5 15.2984 10.5 15.574 10.6142C15.9416 10.7664 16.2336 11.0584 16.3858 11.426C16.5 11.7016 16.5 12.0511 16.5 12.75C16.5 13.4489 16.5 13.7984 16.3858 14.074C16.2336 14.4416 15.9416 14.7336 15.574 14.8858C15.2984 15 14.9489 15 14.25 15H9.75" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11.25 3.75H14.25C14.9489 3.75 15.2984 3.75 15.574 3.86418C15.9416 4.01642 16.2336 4.30843 16.3858 4.67597C16.5 4.95163 16.5 5.30109 16.5 6C16.5 6.69891 16.5 7.04837 16.3858 7.32403C16.2336 7.69157 15.9416 7.98358 15.574 8.13582C15.2984 8.25 14.9489 8.25 14.25 8.25H11.25" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
</g>
<defs>
<clipPath id="clip0_194_1537">
<rect width="18" height="18" fill="white"/>
</clipPath>
</defs>
</svg>''';

const String _securityIconSvg =
    '''<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_194_1550)">
<path d="M11.25 9.75V10.5C11.25 12.6213 11.25 13.682 11.909 14.341C12.568 15 13.6287 15 15.75 15" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M15.5186 13.5L15.5186 16.5" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.75 8.12186L6.34711 8.16666C5.74435 8.23369 5.44297 8.2672 5.20288 8.24117C4.03108 8.11414 3.10748 7.07577 3.00612 5.77145C2.98535 5.50421 3.0188 5.16947 3.0857 4.5" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M10.2686 5.25H10.2753" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.75 5.25L16.125 5.25" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.6845 3.16811C1.51799 2.49549 1.43473 2.15917 1.56136 1.90882C1.60443 1.82368 1.66323 1.74763 1.73458 1.68478C1.94437 1.5 2.28765 1.5 2.9742 1.5H12.4153C14.6712 1.5 16.5 3.34683 16.5 5.625C16.5 7.90317 14.6712 9.75 12.4153 9.75H8.85386C8.24195 9.75 7.93599 9.75 7.67316 9.64522C7.47048 9.56442 7.28789 9.43959 7.13834 9.27956C6.94439 9.07203 6.83076 8.78516 6.6035 8.2114L6.20042 7.19376C5.96732 6.60526 5.85077 6.31101 5.63519 6.0933C5.41961 5.8756 5.12824 5.7579 4.54549 5.5225L3.27645 5.00987C2.73139 4.7897 2.45886 4.67961 2.25431 4.48566C2.1826 4.41766 2.11775 4.34264 2.06073 4.2617C1.89807 4.03082 1.82688 3.74325 1.6845 3.16811Z" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_194_1550">
<rect width="18" height="18" fill="white"/>
</clipPath>
</defs>
</svg>''';

const String _systemAdminIconSvg =
    '''<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.625 9C11.625 10.4497 10.4497 11.625 9 11.625C7.55025 11.625 6.375 10.4497 6.375 9C6.375 7.55025 7.55025 6.375 9 6.375C10.4497 6.375 11.625 7.55025 11.625 9Z" stroke="#008FD3" stroke-width="1.5"/>
<path d="M15.5201 10.6483C16.0537 10.4513 16.3205 10.3528 16.4102 10.224C16.5 10.0952 16.5 9.88849 16.5 9.47515V8.52493C16.5 8.11155 16.5 7.90486 16.4102 7.77603C16.3204 7.64719 16.0536 7.54873 15.5201 7.35179C14.2775 6.89318 13.4987 5.59325 13.7055 4.29902C13.7971 3.72516 13.843 3.43823 13.7752 3.29724C13.7075 3.15625 13.5272 3.05391 13.1667 2.84924L12.3081 2.36171C11.9524 2.15975 11.7745 2.05877 11.6188 2.07278C11.4631 2.0868 11.2482 2.26623 10.8186 2.62509C9.77658 3.49551 8.2246 3.49548 7.18255 2.62502C6.75294 2.26616 6.53814 2.08673 6.38242 2.07271C6.22669 2.05869 6.04884 2.15967 5.69313 2.36163L4.83445 2.84916C4.474 3.05382 4.29377 3.15615 4.22601 3.29711C4.15825 3.43808 4.20404 3.72504 4.29564 4.29897C4.50219 5.59325 3.72282 6.89323 2.47995 7.35182C1.94637 7.5487 1.67958 7.64714 1.58979 7.77598C1.5 7.90482 1.5 8.11152 1.5 8.52493L1.5 9.47515C1.5 9.88852 1.5 10.0952 1.58978 10.224C1.67956 10.3529 1.94635 10.4513 2.47994 10.6483C3.72253 11.1069 4.50128 12.4068 4.29453 13.7011C4.20285 14.2749 4.15701 14.5618 4.22477 14.7028C4.29253 14.8438 4.47277 14.9462 4.83327 15.1508L5.69194 15.6384C6.04769 15.8403 6.22556 15.9413 6.38131 15.9273C6.53705 15.9133 6.7518 15.7338 7.1813 15.3749C8.22398 14.5036 9.7772 14.5036 10.8199 15.3749C11.2494 15.7338 11.4641 15.9132 11.6199 15.9272C11.7756 15.9413 11.9535 15.8403 12.3092 15.6383L13.1679 15.1508C13.5284 14.9461 13.7087 14.8437 13.7765 14.7027C13.8442 14.5617 13.7983 14.2748 13.7066 13.701C13.4996 12.4068 14.2778 11.1069 15.5201 10.6483Z" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
</svg>''';

const String _faceRegistrationIconSvg =
    '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.11063 0.833252C4.88874 0.883878 3.59095 1.09538 2.70423 1.97902C1.92817 2.75239 1.66875 3.83955 1.58203 5.58325M11.8868 0.833252C14.1087 0.883878 15.4064 1.09538 16.2932 1.97902C17.0692 2.75239 17.3286 3.83955 17.4154 5.58325M11.8868 16.6666C14.1087 16.616 15.4064 16.4045 16.2932 15.5208C17.0692 14.7474 17.3286 13.6603 17.4154 11.9166M7.11063 16.6666C4.88874 16.616 3.59095 16.4045 2.70423 15.5208C1.92817 14.7474 1.66875 13.6603 1.58203 11.9166" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.16406 12.9166C7.69611 10.7538 11.263 10.6357 12.8307 12.9166M11.5807 6.66659C11.5807 7.81718 10.648 8.74992 9.4974 8.74992C8.3468 8.74992 7.41406 7.81718 7.41406 6.66659C7.41406 5.51599 8.3468 4.58325 9.4974 4.58325C10.648 4.58325 11.5807 5.51599 11.5807 6.66659Z" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
</svg>''';

const String _notificationCheckIconSvg =
    '''<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.91699 8.45801C2.91699 8.45801 3.79199 8.45801 4.95866 10.4997C4.95866 10.4997 8.20131 5.15245 11.0837 4.08301" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _notificationCameraIconSvg =
    '''<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_206_2440)">
<path d="M8.75 7.58301V8.16634C8.75 9.81626 8.75 10.6412 9.26256 11.1538C9.77513 11.6663 10.6001 11.6663 12.25 11.6663" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M12.0703 10.5L12.0703 12.8333" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.24967 6.317L4.93631 6.35185C4.46751 6.40398 4.23309 6.43004 4.04636 6.4098C3.13496 6.311 2.4166 5.50338 2.33777 4.4889C2.32162 4.28105 2.34763 4.0207 2.39966 3.5" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M7.98633 4.08301H7.99157" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.91699 4.08301L12.542 4.08301" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1.31049 2.46441C1.18098 1.94126 1.11623 1.67968 1.21472 1.48497C1.24822 1.41874 1.29395 1.35959 1.34944 1.31071C1.51261 1.16699 1.77961 1.16699 2.31359 1.16699H9.65667C11.4113 1.16699 12.8337 2.60341 12.8337 4.37533C12.8337 6.14724 11.4113 7.58366 9.65667 7.58366H6.88666C6.41073 7.58366 6.17276 7.58366 5.96834 7.50216C5.8107 7.43932 5.66869 7.34223 5.55237 7.21776C5.40152 7.05635 5.31314 6.83322 5.13638 6.38697L4.82287 5.59547C4.64158 5.13775 4.55093 4.90889 4.38325 4.73956C4.21558 4.57023 3.98895 4.47869 3.53571 4.2956L2.54868 3.89689C2.12474 3.72565 1.91277 3.64002 1.75367 3.48917C1.6979 3.43629 1.64747 3.37794 1.60312 3.31498C1.4766 3.13541 1.42123 2.91175 1.31049 2.46441Z" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_206_2440">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>''';

const String _notificationDeviceIconSvg =
    '''<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.6663 8.16699V5.83366C11.6663 3.63377 11.6663 2.53383 11.0231 1.85041C10.3799 1.16699 9.34467 1.16699 7.27419 1.16699L6.72517 1.16699C4.65468 1.16699 3.61944 1.16699 2.97622 1.85041C2.33301 2.53383 2.33301 3.63377 2.33301 5.83366L2.33301 8.16699C2.33301 10.3669 2.33301 11.4668 2.97622 12.1502C3.61944 12.8337 4.65468 12.8337 6.72516 12.8337H7.27418C9.34467 12.8337 10.3799 12.8337 11.0231 12.1502C11.6663 11.4668 11.6663 10.3669 11.6663 8.16699Z" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round"/>
<path d="M9.33301 10.5H9.33825" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M2.33301 8.75L11.6663 8.75" stroke="#008FD3" stroke-width="1.5" stroke-linejoin="round"/>
<path d="M4.66699 3.5L5.83366 3.5" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M4.66699 5.25L5.83366 5.25" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _notificationFireIconSvg =
    '''<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.182 10.2488L12.9256 10.1514L12.182 10.2488ZM11.3419 9.44652L11.3773 8.69736L11.3419 9.44652ZM11.3419 12.807L11.3773 13.5562L11.3419 12.807ZM12.182 12.0048L11.4383 11.9074L12.182 12.0048ZM9.07545 9.44652L9.11087 10.1957L9.07545 9.44652ZM8.23534 10.2488L8.979 10.3461L8.23534 10.2488ZM9.07545 12.807L9.11087 12.0578L9.07545 12.807ZM8.23534 12.0048L8.979 11.9074L8.23534 12.0048ZM8.31022 9.42051C8.31022 9.83472 8.64601 10.1705 9.06022 10.1705C9.47443 10.1705 9.81022 9.83472 9.81022 9.42051H8.31022ZM10.6071 9.42051C10.6071 9.83472 10.9429 10.1705 11.3571 10.1705C11.7713 10.1705 12.1071 9.83472 12.1071 9.42051H10.6071ZM10.2087 12.833L10.2087 12.083C9.81569 12.083 9.46026 12.0744 9.11087 12.0578L9.07545 12.807L9.04002 13.5562C9.4137 13.5738 9.79277 13.583 10.2087 13.583V12.833ZM11.3419 12.807L11.3064 12.0578C10.9571 12.0744 10.6016 12.083 10.2087 12.083L10.2087 12.833V13.583C10.6245 13.583 11.0036 13.5738 11.3773 13.5562L11.3419 12.807ZM12.182 12.0048L12.9256 12.1021C12.9644 11.8059 13.0003 11.4745 13.0003 11.1268L12.2503 11.1268H11.5003C11.5003 11.3763 11.4745 11.6313 11.4383 11.9074L12.182 12.0048ZM12.2503 11.1268L13.0003 11.1268C13.0003 10.779 12.9644 10.4476 12.9256 10.1514L12.182 10.2488L11.4383 10.3461C11.4745 10.6222 11.5003 10.8773 11.5003 11.1268H12.2503ZM10.2087 9.42051V10.1705C10.6016 10.1705 10.9571 10.1792 11.3064 10.1957L11.3419 9.44652L11.3773 8.69736C11.0036 8.67969 10.6245 8.67051 10.2087 8.67051V9.42051ZM9.07545 9.44652L9.11087 10.1957C9.46026 10.1792 9.81569 10.1705 10.2087 10.1705V9.42051V8.67051C9.79277 8.67051 9.4137 8.67969 9.04002 8.69736L9.07545 9.44652ZM8.23534 10.2488L7.49168 10.1514C7.45291 10.4476 7.41699 10.779 7.41699 11.1268H8.16699H8.91699C8.91699 10.8773 8.94286 10.6222 8.979 10.3461L8.23534 10.2488ZM8.16699 11.1268H7.41699C7.41699 11.4745 7.45291 11.8059 7.49168 12.1021L8.23534 12.0048L8.979 11.9074C8.94286 11.6313 8.91699 11.3763 8.91699 11.1268H8.16699ZM12.182 10.2488L12.9256 10.1514C12.8247 9.38062 12.1969 8.73611 11.3773 8.69736L11.3419 9.44652L11.3064 10.1957C11.3279 10.1967 11.3547 10.2056 11.3821 10.2318C11.4101 10.2588 11.4321 10.2985 11.4383 10.3461L12.182 10.2488ZM11.3419 12.807L11.3773 13.5562C12.1969 13.5174 12.8247 12.8729 12.9256 12.1021L12.182 12.0048L11.4383 11.9074C11.4321 11.955 11.4101 11.9948 11.3821 12.0217C11.3547 12.0479 11.3279 12.0568 11.3064 12.0578L11.3419 12.807ZM9.07545 9.44652L9.04002 8.69736C8.22039 8.73611 7.59258 9.38062 7.49168 10.1514L8.23534 10.2488L8.979 10.3461C8.98522 10.2985 9.0072 10.2588 9.03526 10.2318C9.0626 10.2056 9.08942 10.1967 9.11087 10.1957L9.07545 9.44652ZM9.07545 12.807L9.11087 12.0578C9.08942 12.0568 9.0626 12.0479 9.03526 12.0217C9.0072 11.9948 8.98522 11.955 8.979 11.9074L8.23534 12.0048L7.49168 12.1021C7.59258 12.8729 8.2204 13.5174 9.04002 13.5562L9.07545 12.807ZM9.06022 9.42051H9.81022V8.76426H9.06022H8.31022V9.42051H9.06022ZM11.3571 8.76426H10.6071V9.42051H11.3571H12.1071V8.76426H11.3571ZM10.2087 7.58301V8.33301C10.4089 8.33301 10.6071 8.50603 10.6071 8.76426H11.3571H12.1071C12.1071 7.71771 11.2769 6.83301 10.2087 6.83301V7.58301ZM9.06022 8.76426H9.81022C9.81022 8.50603 10.0084 8.33301 10.2087 8.33301V7.58301V6.83301C9.1404 6.83301 8.31022 7.71771 8.31022 8.76426H9.06022Z" fill="#FF9800"/>
<path d="M6.3846 12.8337C3.82498 12.8337 1.75 10.7443 1.75 8.16699C1.75 6.95145 2.309 5.73592 3.03873 4.66699C3.18848 5.54199 3.83557 7.23366 5.22595 7.00033C3.69541 3.79199 6.3846 1.16699 6.3846 1.16699C6.3846 1.16699 6.67427 3.20866 9.28123 5.25033C9.52262 5.43937 9.73335 5.63419 9.91667 5.83366" stroke="#FF9800" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _arrowLeftIconSvg = '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15 6L9.70711 11.2929C9.37377 11.6262 9.20711 11.7929 9.20711 12C9.20711 12.2071 9.37377 12.3738 9.70711 12.7071L15 18" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _arrowRightIconSvg = '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.25 10.625L8.04289 7.83211C8.37623 7.49877 8.54289 7.33211 8.54289 7.125C8.54289 6.91789 8.37623 6.75123 8.04289 6.41789L5.25 3.625" stroke="#8F959E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _closeIconSvg = '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.0007 7.00488L7.00488 16.9945" stroke="#1F2329" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.0042 17L7 7" stroke="#1F2329" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

const String _checkIconSvg = '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.91699 8.58301C2.91699 8.58301 3.79199 8.58301 4.95866 10.6247C4.95866 10.6247 8.20131 5.27745 11.0837 4.20801" stroke="#008FD3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>''';

import 'dart:typed_data';

/// Abstract interface for face recognition services
abstract class FaceRecognitionService {
  /// Initialize the recognition service
  Future<void> initialize();
  
  /// Recognize face from image bytes
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes);
  
  /// Check if service is throttled
  bool get isThrottled;
  
  /// Set online/offline mode
  void setOnlineMode(bool isOnline);
  
  /// Get service name
  String get serviceName;
  
  /// Check if service is initialized
  bool get isInitialized;
  
  /// Dispose of resources
  Future<void> dispose();
}

/// Face recognition result
class RecognitionResult {
  final String userId;
  final String userName;
  final double confidence;
  final String source; // 'online' or 'offline'
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  
  const RecognitionResult({
    required this.userId,
    required this.userName,
    required this.confidence,
    required this.source,
    required this.timestamp,
    this.metadata = const {},
  });
  
  @override
  String toString() {
    return 'RecognitionResult(user: $userName, confidence: ${(confidence * 100).toStringAsFixed(1)}%, source: $source)';
  }
}

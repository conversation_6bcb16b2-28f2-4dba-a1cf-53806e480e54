import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import '../../core/config/flavor_config.dart';
import '../../core/config/app_config.dart';
import '../../shared/presentation/themes/index.dart';
import '../../shared/providers/face_capture_provider.dart';
import '../../shared/providers/face_detection_provider.dart';
import 'core/app_initializer.dart';
import 'routes/index.dart';
import 'presentation/providers/auth_provider.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Khởi tạo flavor config cho mobile app (synchronous)
  FlavorConfig.initialize(
    flavor: Flavor.mobile,
    appName: 'C-CAM Mobile Temp',
    appId: 'com.ccam.mobile.temp',
    appVersion: '1.0.0',
    isDebug: true,
    values: AppConfig.mobileConfig,
  );

  // Start app immediately - heavy initialization will happen in background
  runApp(const MobileApp());
}

class MobileApp extends StatefulWidget {
  const MobileApp({super.key});

  @override
  State<MobileApp> createState() => _MobileAppState();
}

class _MobileAppState extends State<MobileApp> {
  bool _isInitialized = false;
  AuthProvider? _authProvider;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Start background initialization immediately
    unawaited(AppInitializer().initialize().then((_) async {
      if (mounted) {
        // Initialize AuthProvider after services are ready
        _authProvider = GetIt.instance<AuthProvider>();
        await _authProvider!.initialize();

        setState(() {
          _isInitialized = true;
        });
      }
    }));
  }

  // Helper to avoid unawaited_futures warning
  void unawaited(Future<void> future) {}

  @override
  Widget build(BuildContext context) {
    // Show splash screen while initializing or if authProvider is not ready
    if (!_isInitialized || _authProvider == null) {
      return MaterialApp(
        title: FlavorConfig.instance.appName,
        debugShowCheckedModeBanner: false,
        home: const _SplashScreen(),
      );
    }

    // Show main app after initialization
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _authProvider!),
        ChangeNotifierProvider(create: (_) => FaceCaptureProvider()),
        ChangeNotifierProvider(create: (_) => FaceDetectionProvider()),
      ],
      child: MaterialApp.router(
        title: FlavorConfig.instance.appName,
        debugShowCheckedModeBanner: FlavorConfig.instance.isDebug,
        theme: MobileTheme.lightTheme,
        darkTheme: MobileTheme.darkTheme,
        themeMode: ThemeMode.light,
        routerConfig: MobileRouter.createRouter(),
      ),
    );
  }
}

class _SplashScreen extends StatefulWidget {
  const _SplashScreen();

  @override
  State<_SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<_SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App logo or icon
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.shade200,
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.mobile_friendly,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),
                    // App name
                    Text(
                      FlavorConfig.instance.appName,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Loading indicator
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.blue.shade600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Loading text
                    const Text(
                      'Đang khởi tạo ứng dụng...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

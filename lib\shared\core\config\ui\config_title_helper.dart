/// Configuration Title Helper
/// 
/// Provides Vietnamese translations for configuration parameter keys
/// to make them more user-friendly and understandable.

class ConfigTitleHelper {
  static const Map<String, String> _keyTitles = {
    // Face Detection Parameters
    'face_detection.min_quality_detection': 'Chất lượng phát hiện tối thiểu (%)',
    'face_detection.min_quality_recognition': 'Chất lượng nhận diện tối thiểu (%)',
    'face_detection.min_quality_avatar_capture': 'Chất lượng chụp avatar tối thiểu (%)',
    'face_detection.significant_quality_change': 'Ngưỡng thay đổi chất lượ<PERSON> (%)',
    'face_detection.recognition_throttle_duration': 'Thời gian giãn cách nhận diện (giây)',
    'face_detection.user_display_timeout': 'Thời gian hiển thị thông tin người dùng (giây)',
    'face_detection.avatar_capture_throttle': 'Thời gian giãn cách chụp avatar (giây)',
    'face_detection.avatar_display_duration': 'Thời gian hiển thị avatar (giây)',
    'face_detection.frame_skip_count': 'Số khung hình bỏ qua',
    'face_detection.detection_timeout': 'Thời gian chờ phát hiện (giây)',
    'face_detection.avatar_padding_factor': 'Hệ số đệm avatar',
    'face_detection.avatar_target_size': 'Kích thước avatar mục tiêu (px)',
    'face_detection.avatar_image_quality': 'Chất lượng ảnh avatar (0-100)',
    'face_detection.recognition_image_quality': 'Chất lượng ảnh nhận diện (0-100)',
    'face_detection.max_recognition_retries': 'Số lần thử lại nhận diện tối đa',
    'face_detection.recognition_timeout': 'Thời gian chờ nhận diện (giây)',
    'face_detection.max_image_size_bytes': 'Kích thước ảnh tối đa (bytes)',
    'face_detection.compression_quality': 'Chất lượng nén ảnh (0-100)',
    'face_detection.image_format': 'Định dạng ảnh',
    'face_detection.source_identifier': 'Định danh nguồn',

    // Network Parameters
    'network.base_api_url': 'URL API cơ sở',
    'network.base_url': 'URL cơ sở',
    'network.api_version': 'Phiên bản API',
    'network.request_timeout': 'Thời gian chờ request (giây)',
    'network.max_retry_attempts': 'Số lần thử lại tối đa',
    'network.retry_delay': 'Độ trễ thử lại (giây)',
    'network.device_id': 'ID thiết bị',
    'network.auth_token': 'Token xác thực',
    'network.use_https': 'Sử dụng HTTPS',
    'network.verify_ssl': 'Xác minh SSL',
    'network.connection_pool_size': 'Kích thước pool kết nối',
    'network.heartbeat_interval': 'Khoảng thời gian heartbeat (giây)',
    'network.ping_interval': 'Khoảng thời gian ping (giây)',
    'network.server_health_check_interval': 'Khoảng thời gian kiểm tra server (giây)',
    'network.auto_reconnect': 'Tự động kết nối lại',
    'network.max_reconnect_attempts': 'Số lần thử kết nối lại tối đa',
    'network.reconnect_delay': 'Độ trễ kết nối lại (giây)',

    // UI Parameters
    'ui.primary_color': 'Màu chính',
    'ui.secondary_color': 'Màu phụ',
    'ui.success_color': 'Màu thành công',
    'ui.error_color': 'Màu lỗi',
    'ui.warning_color': 'Màu cảnh báo',
    'ui.info_color': 'Màu thông tin',
    'ui.avatar_size': 'Kích thước avatar (px)',
    'ui.animation_duration': 'Thời gian animation (ms)',
    'ui.theme_mode': 'Chế độ giao diện',
    'ui.font_size': 'Kích thước font (px)',
    'ui.button_height': 'Chiều cao nút (px)',

    // Performance Parameters
    'performance.normal_frame_rate': 'Tốc độ khung hình bình thường (fps)',
    'performance.optimized_frame_rate': 'Tốc độ khung hình tối ưu (fps)',
    'performance.power_saving_delay': 'Độ trễ tiết kiệm pin (giây)',
    'performance.resource_optimization_delay': 'Thời gian tối ưu tài nguyên (giây)',
    'performance.extreme_power_saving_delay': 'Thời gian tiết kiệm pin cực đại (giây)',
    'performance.face_absence_for_optimization': 'Thời gian không có mặt để tối ưu (giây)',
    'performance.face_absence_for_extreme_saving': 'Thời gian không có mặt để tiết kiệm pin (giây)',
    'performance.max_brightness': 'Độ sáng màn hình tối đa',
    'performance.min_brightness': 'Độ sáng màn hình tối thiểu',
    'performance.power_saving_brightness': 'Độ sáng tiết kiệm pin',
    'performance.memory_threshold': 'Ngưỡng bộ nhớ',
    'performance.cpu_threshold': 'Ngưỡng CPU',
    'performance.battery_threshold': 'Ngưỡng pin',
    'performance.thermal_threshold': 'Ngưỡng nhiệt độ',

    // Camera Parameters
    'camera.default_resolution': 'Độ phân giải mặc định',
    'camera.max_face_captures': 'Số lượng chụp khuôn mặt tối đa',
    'camera.auto_focus': 'Tự động lấy nét',
    'camera.flash_mode': 'Chế độ đèn flash',
    'camera.exposure_compensation': 'Bù trừ độ phơi sáng',
    'camera.white_balance': 'Cân bằng trắng',
    'camera.iso_sensitivity': 'Độ nhạy ISO',

    // Security Parameters
    'security.encryption_enabled': 'Bật mã hóa',
    'security.token_expiry': 'Thời gian hết hạn token',
    'security.max_login_attempts': 'Số lần đăng nhập tối đa',
    'security.session_timeout': 'Thời gian chờ phiên',
    'security.password_min_length': 'Độ dài mật khẩu tối thiểu',
    'security.require_2fa': 'Yêu cầu xác thực 2 lớp',

    // Cache Parameters
    'cache.max_size_mb': 'Kích thước cache tối đa (MB)',
    'cache.ttl_seconds': 'Thời gian sống cache (giây)',
    'cache.cleanup_interval': 'Khoảng thời gian dọn dẹp',
    'cache.compression_enabled': 'Bật nén cache',
    'cache.memory_cache_size': 'Kích thước cache bộ nhớ',
    'cache.disk_cache_size': 'Kích thước cache đĩa',

    // Kiosk Parameters
    'kiosk.auto_timeout': 'Thời gian chờ tự động',
    'kiosk.screensaver_delay': 'Độ trễ screensaver',
    'kiosk.admin_pin': 'Mã PIN quản trị',
    'kiosk.fullscreen_mode': 'Chế độ toàn màn hình',
    'kiosk.hide_cursor': 'Ẩn con trỏ',
    'kiosk.disable_keyboard': 'Vô hiệu hóa bàn phím',

    // Debug Parameters
    'debug.enable_face_detection_logs': 'Bật log phát hiện khuôn mặt',
    'debug.enable_performance_monitoring': 'Bật giám sát hiệu suất',
    'debug.enable_network_logs': 'Bật log mạng',
    'debug.enable_cache_logs': 'Bật log cache',
    'debug.log_level': 'Mức độ log',
    'debug.crash_reporting': 'Báo cáo lỗi',
    'debug.analytics_enabled': 'Bật phân tích',
  };

  /// Detailed descriptions for configuration parameters
  static const Map<String, String> _keyDescriptions = {
    // Face Detection Parameters
    'face_detection.min_quality_detection': 'Ngưỡng chất lượng tối thiểu để hệ thống có thể phát hiện khuôn mặt trong khung hình. Giá trị từ 0.0 đến 1.0, càng cao càng chính xác nhưng có thể bỏ sót khuôn mặt có chất lượng thấp.',
    'face_detection.min_quality_recognition': 'Ngưỡng chất lượng tối thiểu để hệ thống có thể nhận diện và xác định danh tính khuôn mặt. Giá trị từ 0.0 đến 1.0, nên cao hơn min_quality_detection để đảm bảo độ chính xác.',
    'face_detection.min_quality_avatar_capture': 'Ngưỡng chất lượng tối thiểu để chụp và lưu ảnh avatar của người dùng. Giá trị từ 0.0 đến 1.0, nên cao nhất để đảm bảo ảnh avatar có chất lượng tốt.',
    'face_detection.significant_quality_change': 'Ngưỡng thay đổi chất lượng đáng kể để kích hoạt xử lý lại. Giá trị từ 0.0 đến 1.0, giúp tránh xử lý không cần thiết khi chất lượng thay đổi nhỏ.',
    'face_detection.recognition_throttle_duration': 'Thời gian tối thiểu giữa các lần nhận diện liên tiếp (tính bằng giây). Giúp tránh spam request và tiết kiệm tài nguyên hệ thống.',
    'face_detection.user_display_timeout': 'Thời gian hiển thị thông tin người dùng trên màn hình sau khi nhận diện thành công (tính bằng giây). Sau thời gian này, thông tin sẽ tự động ẩn.',
    'face_detection.avatar_capture_throttle': 'Thời gian tối thiểu giữa các lần chụp avatar liên tiếp (tính bằng giây). Giúp tránh chụp quá nhiều ảnh không cần thiết.',
    'face_detection.avatar_display_duration': 'Thời gian hiển thị avatar trên màn hình (tính bằng giây). Thời gian avatar được hiển thị cho người dùng xem.',
    'face_detection.frame_skip_count': 'Số khung hình bỏ qua giữa các lần xử lý. Giá trị cao hơn giúp tiết kiệm tài nguyên nhưng có thể giảm độ nhạy của hệ thống.',
    'face_detection.detection_timeout': 'Thời gian chờ tối đa cho quá trình phát hiện khuôn mặt (tính bằng giây). Nếu vượt quá thời gian này, quá trình sẽ bị hủy.',
    'face_detection.avatar_padding_factor': 'Hệ số đệm xung quanh khuôn mặt khi cắt ảnh avatar. Giá trị từ 0.0 đến 1.0, giúp đảm bảo avatar có đủ không gian xung quanh.',
    'face_detection.avatar_target_size': 'Kích thước mục tiêu của ảnh avatar (pixel). Ảnh avatar sẽ được resize về kích thước này sau khi cắt.',
    'face_detection.avatar_image_quality': 'Chất lượng nén ảnh avatar (0-100). Giá trị cao hơn cho chất lượng tốt hơn nhưng file size lớn hơn.',
    'face_detection.recognition_image_quality': 'Chất lượng ảnh gửi lên server để nhận diện (0-100). Cân bằng giữa chất lượng và tốc độ truyền tải.',
    'face_detection.max_recognition_retries': 'Số lần thử lại tối đa khi nhận diện thất bại. Giúp tăng tỷ lệ thành công nhưng có thể làm chậm hệ thống.',
    'face_detection.recognition_timeout': 'Thời gian chờ tối đa cho quá trình nhận diện (tính bằng giây). Nếu vượt quá thời gian này, nhận diện sẽ bị hủy.',
    'face_detection.max_image_size_bytes': 'Kích thước tối đa của ảnh gửi lên server (bytes). Ảnh lớn hơn sẽ được nén hoặc từ chối.',
    'face_detection.compression_quality': 'Chất lượng nén ảnh chung (0-100). Áp dụng cho tất cả ảnh được xử lý trong hệ thống.',
    'face_detection.image_format': 'Định dạng ảnh sử dụng (jpg, png, webp). Ảnh hưởng đến chất lượng và kích thước file.',
    'face_detection.source_identifier': 'Mã định danh nguồn thiết bị. Giúp server phân biệt các thiết bị khác nhau.',

    // Network Parameters
    'network.base_api_url': 'URL cơ sở của API server. Tất cả các request sẽ được gửi đến địa chỉ này.',
    'network.base_url': 'URL cơ sở của hệ thống. Được sử dụng cho các request không phải API.',
    'network.api_version': 'Phiên bản API đang sử dụng. Giúp server xử lý request đúng cách.',
    'network.request_timeout': 'Thời gian chờ tối đa cho một request (tính bằng giây). Request sẽ bị hủy nếu vượt quá thời gian này.',
    'network.max_retry_attempts': 'Số lần thử lại tối đa khi request thất bại. Giúp tăng độ ổn định kết nối.',
    'network.retry_delay': 'Thời gian chờ giữa các lần thử lại (tính bằng giây). Tránh spam server khi có lỗi.',
    'network.device_id': 'ID duy nhất của thiết bị. Được sử dụng để xác định thiết bị trên server.',
    'network.auth_token': 'Token xác thực để truy cập API. Cần thiết cho các request bảo mật.',
    'network.use_https': 'Sử dụng HTTPS cho tất cả request. Bật để đảm bảo bảo mật kết nối.',
    'network.verify_ssl': 'Xác minh chứng chỉ SSL. Bật để tăng cường bảo mật, tắt nếu có vấn đề với certificate.',
    'network.connection_pool_size': 'Số lượng kết nối tối đa trong pool. Giá trị cao hơn cho hiệu suất tốt hơn nhưng tốn tài nguyên.',
    'network.heartbeat_interval': 'Khoảng thời gian gửi heartbeat để duy trì kết nối server (tính bằng giây). Giá trị thấp hơn giúp phát hiện mất kết nối nhanh hơn.',
    'network.ping_interval': 'Khoảng thời gian ping server để kiểm tra kết nối (tính bằng giây). Giúp đánh giá độ trễ và tình trạng kết nối.',
    'network.server_health_check_interval': 'Khoảng thời gian kiểm tra tình trạng server (tính bằng giây). Giúp phát hiện sớm các vấn đề về server.',
    'network.auto_reconnect': 'Tự động kết nối lại khi mất kết nối với server. Bật để tăng độ ổn định kết nối.',
    'network.max_reconnect_attempts': 'Số lần thử kết nối lại tối đa khi mất kết nối. Sau khi vượt quá sẽ dừng thử kết nối.',
    'network.reconnect_delay': 'Thời gian chờ giữa các lần thử kết nối lại (tính bằng giây). Tránh spam server khi có lỗi kết nối.',

    // UI Parameters
    'ui.primary_color': 'Màu chính của giao diện. Được sử dụng cho các thành phần UI quan trọng.',
    'ui.secondary_color': 'Màu phụ của giao diện. Được sử dụng cho các thành phần UI phụ.',
    'ui.success_color': 'Màu hiển thị cho thông báo thành công. Thường là màu xanh lá.',
    'ui.error_color': 'Màu hiển thị cho thông báo lỗi. Thường là màu đỏ.',
    'ui.warning_color': 'Màu hiển thị cho thông báo cảnh báo. Thường là màu vàng hoặc cam.',
    'ui.info_color': 'Màu hiển thị cho thông báo thông tin. Thường là màu xanh dương.',
    'ui.avatar_size': 'Kích thước hiển thị avatar trên giao diện (pixel). Ảnh hưởng đến layout và hiệu suất.',
    'ui.font_size': 'Kích thước font chữ mặc định. Ảnh hưởng đến khả năng đọc và giao diện.',
    'ui.animation_duration': 'Thời gian animation mặc định (milliseconds). Ảnh hưởng đến trải nghiệm người dùng.',
    'ui.theme_mode': 'Chế độ theme (light/dark/auto). Ảnh hưởng đến giao diện tổng thể.',
    'ui.language': 'Ngôn ngữ hiển thị. Thay đổi ngôn ngữ của toàn bộ giao diện.',
    'ui.show_debug_info': 'Hiển thị thông tin debug trên giao diện. Hữu ích cho việc troubleshooting.',

    // Performance Parameters
    'performance.normal_frame_rate': 'Tốc độ khung hình bình thường (FPS). Số khung hình xử lý mỗi giây trong chế độ hoạt động thường.',
    'performance.optimized_frame_rate': 'Tốc độ khung hình tối ưu (FPS). Số khung hình xử lý mỗi giây trong chế độ tối ưu hóa.',
    'performance.extreme_power_saving_frame_rate': 'Tốc độ khung hình tiết kiệm pin cực đại (FPS). Số khung hình xử lý mỗi giây trong chế độ tiết kiệm pin.',
    'performance.power_saving_delay': 'Thời gian trước khi bật chế độ tiết kiệm pin (giây). Khoảng thời gian chờ trước khi hệ thống chuyển sang chế độ tiết kiệm pin.',
    'performance.resource_optimization_delay': 'Thời gian trước khi tối ưu tài nguyên (giây). Khoảng thời gian không có hoạt động trước khi hệ thống bắt đầu tối ưu hóa tài nguyên.',
    'performance.extreme_power_saving_delay': 'Thời gian trước khi tiết kiệm pin cực đại (giây). Khoảng thời gian không có hoạt động trước khi hệ thống chuyển sang chế độ tiết kiệm pin cực đại.',
    'performance.face_absence_for_optimization': 'Thời gian không phát hiện mặt để tối ưu (giây). Khoảng thời gian không phát hiện khuôn mặt trước khi bắt đầu tối ưu hóa.',
    'performance.face_absence_for_extreme_saving': 'Thời gian không phát hiện mặt để tiết kiệm pin (giây). Khoảng thời gian không phát hiện khuôn mặt trước khi chuyển sang tiết kiệm pin cực đại.',
    'performance.max_brightness': 'Độ sáng màn hình tối đa (0.0-1.0). Mức độ sáng tối đa của màn hình khi hoạt động bình thường.',
    'performance.min_brightness': 'Độ sáng màn hình tối thiểu (0.0-1.0). Mức độ sáng tối thiểu của màn hình có thể đặt.',
    'performance.power_saving_brightness': 'Độ sáng tiết kiệm pin (0.0-1.0). Mức độ sáng màn hình khi ở chế độ tiết kiệm pin.',
    'performance.brightness_transition_duration': 'Thời gian chuyển đổi độ sáng (ms). Thời gian animation khi thay đổi độ sáng màn hình.',
    'performance.max_concurrent_requests': 'Số request tối đa có thể xử lý đồng thời. Giá trị cao hơn cho hiệu suất tốt hơn nhưng tốn tài nguyên.',
    'performance.cache_size_mb': 'Kích thước cache tối đa (MB). Cache lớn hơn cho hiệu suất tốt hơn nhưng tốn bộ nhớ.',
    'performance.memory_warning_threshold': 'Ngưỡng cảnh báo sử dụng bộ nhớ (%). Hệ thống sẽ cảnh báo khi vượt ngưỡng này.',
    'performance.cpu_usage_limit': 'Giới hạn sử dụng CPU (%). Hệ thống sẽ giảm hiệu suất khi vượt giới hạn.',
    'performance.enable_performance_monitoring': 'Bật giám sát hiệu suất. Thu thập dữ liệu về hiệu suất hệ thống.',

    // Camera Parameters
    'camera.resolution_width': 'Độ phân giải camera theo chiều ngang (pixel). Ảnh hưởng đến chất lượng và hiệu suất.',
    'camera.resolution_height': 'Độ phân giải camera theo chiều dọc (pixel). Ảnh hưởng đến chất lượng và hiệu suất.',
    'camera.fps': 'Số khung hình trên giây. Giá trị cao hơn cho video mượt hơn nhưng tốn tài nguyên.',
    'camera.auto_focus': 'Tự động lấy nét. Bật để camera tự động điều chỉnh focus.',
    'camera.flash_mode': 'Chế độ flash (auto/on/off). Điều khiển đèn flash của camera.',
    'camera.exposure_compensation': 'Bù trừ độ phơi sáng. Điều chỉnh độ sáng của ảnh.',

    // Security Parameters
    'security.enable_encryption': 'Bật mã hóa dữ liệu. Mã hóa tất cả dữ liệu nhạy cảm.',
    'security.encryption_key': 'Khóa mã hóa. Được sử dụng để mã hóa/giải mã dữ liệu.',
    'security.session_timeout': 'Thời gian hết hạn phiên (giây). Phiên sẽ tự động hết hạn sau thời gian này.',
    'security.max_login_attempts': 'Số lần đăng nhập thất bại tối đa. Tài khoản sẽ bị khóa sau số lần này.',
    'security.password_min_length': 'Độ dài mật khẩu tối thiểu. Mật khẩu phải có ít nhất số ký tự này.',

    // Cache Parameters
    'cache.enable_cache': 'Bật cache. Lưu trữ dữ liệu tạm thời để tăng hiệu suất.',
    'cache.cache_duration': 'Thời gian lưu cache (giây). Dữ liệu sẽ bị xóa khỏi cache sau thời gian này.',
    'cache.max_cache_entries': 'Số lượng mục cache tối đa. Giới hạn số lượng dữ liệu trong cache.',

    // Kiosk Parameters
    'kiosk.enable_kiosk_mode': 'Bật chế độ kiosk. Hạn chế người dùng chỉ sử dụng ứng dụng này.',
    'kiosk.screensaver_timeout': 'Thời gian chờ screensaver (giây). Screensaver sẽ bật sau thời gian không hoạt động.',
    'kiosk.auto_restart_time': 'Thời gian tự động khởi động lại (giờ). Hệ thống sẽ tự động restart định kỳ.',
    'kiosk.auto_timeout': 'Thời gian chờ tự động (giây). Hệ thống sẽ tự động thực hiện hành động sau thời gian này.',
    'kiosk.screensaver_delay': 'Độ trễ screensaver (giây). Thời gian chờ trước khi bật screensaver.',
    'kiosk.admin_pin': 'Mã PIN quản trị. Được sử dụng để truy cập chức năng quản trị.',
    'kiosk.fullscreen_mode': 'Chế độ toàn màn hình. Ứng dụng sẽ chạy ở chế độ fullscreen.',
    'kiosk.hide_cursor': 'Ẩn con trỏ chuột. Con trỏ sẽ được ẩn khi không sử dụng.',
    'kiosk.disable_keyboard': 'Vô hiệu hóa bàn phím. Ngăn người dùng sử dụng bàn phím.',

    // Debug Parameters
    'debug.enable_face_detection_logs': 'Bật log chi tiết cho quá trình phát hiện khuôn mặt. Hữu ích cho việc debug và tối ưu hóa.',
    'debug.enable_performance_monitoring': 'Bật giám sát hiệu suất chi tiết. Thu thập dữ liệu về CPU, memory, và network.',
    'debug.enable_network_logs': 'Bật log cho tất cả request/response network. Giúp debug vấn đề kết nối.',
    'debug.enable_cache_logs': 'Bật log cho hoạt động cache. Giúp debug vấn đề về cache và hiệu suất.',
    'debug.log_level': 'Mức độ chi tiết của log (error/warning/info/debug). Mức cao hơn sẽ ghi nhiều thông tin hơn.',
    'debug.crash_reporting': 'Tự động gửi báo cáo khi ứng dụng crash. Giúp developer cải thiện ứng dụng.',
    'debug.analytics_enabled': 'Bật thu thập dữ liệu phân tích. Thu thập thông tin sử dụng để cải thiện ứng dụng.',
  };

  /// Get Vietnamese title for a configuration key
  static String getTitle(String key) {
    return _keyTitles[key] ?? _generateFallbackTitle(key);
  }

  /// Generate fallback title if key not found in mapping
  static String _generateFallbackTitle(String key) {
    // Remove prefix and convert to readable format
    final parts = key.split('.');
    final lastPart = parts.last;
    
    // Convert snake_case to Title Case
    final words = lastPart.split('_');
    final titleCase = words
        .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1)}' : '')
        .join(' ');
    
    return titleCase;
  }

  /// Get short title for quick config (without category prefix)
  static String getShortTitle(String key) {
    final fullTitle = getTitle(key);
    
    // For some common patterns, provide shorter versions
    return fullTitle
        .replaceAll('Chất lượng ', 'CL ')
        .replaceAll('Thời gian ', 'TG ')
        .replaceAll('Kích thước ', 'KT ')
        .replaceAll('Số lượng ', 'SL ')
        .replaceAll('Tốc độ ', 'TD ')
        .replaceAll('tối thiểu', 'TT')
        .replaceAll('tối đa', 'TĐ');
  }

  /// Check if key exists in mapping
  static bool hasTitle(String key) {
    return _keyTitles.containsKey(key);
  }

  /// Get all available keys
  static List<String> getAllKeys() {
    return _keyTitles.keys.toList();
  }

  /// Get title with category prefix
  static String getTitleWithCategory(String key) {
    final title = getTitle(key);
    final parts = key.split('.');
    
    if (parts.length > 1) {
      final category = _getCategoryName(parts[0]);
      return '$category: $title';
    }
    
    return title;
  }

  /// Get detailed description for a configuration key
  static String getDescription(String key) {
    return _keyDescriptions[key] ?? 'Chưa có mô tả chi tiết cho tham số này.';
  }

  /// Check if a configuration key has a detailed description
  static bool hasDescription(String key) {
    return _keyDescriptions.containsKey(key);
  }

  /// Get usage example for a configuration key
  static String getUsageExample(String key) {
    // Return usage examples based on parameter type
    switch (key) {
      // Duration parameters (in seconds)
      case 'face_detection.recognition_throttle_duration':
      case 'face_detection.user_display_timeout':
      case 'face_detection.avatar_capture_throttle':
      case 'face_detection.avatar_display_duration':
      case 'face_detection.detection_timeout':
      case 'face_detection.recognition_timeout':
      case 'network.request_timeout':
      case 'network.retry_delay':
      case 'network.heartbeat_interval':
      case 'network.ping_interval':
      case 'network.server_health_check_interval':
      case 'network.reconnect_delay':
      case 'cache.cache_duration':
      case 'security.session_timeout':
      case 'kiosk.screensaver_timeout':
      case 'kiosk.auto_timeout':
      case 'kiosk.screensaver_delay':
        return 'Ví dụ: 5 (5 giây), 30 (30 giây), 300 (5 phút)';
      
      // Quality parameters (0.0 to 1.0)
      case 'face_detection.min_quality_detection':
      case 'face_detection.min_quality_recognition':
      case 'face_detection.min_quality_avatar_capture':
      case 'face_detection.significant_quality_change':
      case 'face_detection.avatar_padding_factor':
        return 'Ví dụ: 0.5 (50%), 0.7 (70%), 0.9 (90%)';
      
      // Image quality parameters (0-100)
      case 'face_detection.avatar_image_quality':
      case 'face_detection.recognition_image_quality':
      case 'face_detection.compression_quality':
        return 'Ví dụ: 80 (chất lượng tốt), 90 (chất lượng cao), 95 (chất lượng rất cao)';
      
      // Size parameters (pixels)
      case 'face_detection.avatar_target_size':
      case 'ui.avatar_size':
      case 'camera.resolution_width':
      case 'camera.resolution_height':
        return 'Ví dụ: 256 (256px), 512 (512px), 1024 (1024px)';
      
      // Count parameters
      case 'face_detection.frame_skip_count':
      case 'face_detection.max_recognition_retries':
      case 'network.max_retry_attempts':
      case 'network.max_reconnect_attempts':
      case 'performance.max_concurrent_requests':
      case 'cache.max_cache_entries':
      case 'security.max_login_attempts':
      case 'security.password_min_length':
        return 'Ví dụ: 1, 3, 5, 10';
      
      // URL parameters
      case 'network.base_api_url':
      case 'network.base_url':
        return 'Ví dụ: https://api.example.com, http://localhost:8080';
      
      // Boolean parameters
      case 'network.use_https':
      case 'network.verify_ssl':
      case 'network.auto_reconnect':
      case 'ui.show_debug_info':
      case 'camera.auto_focus':
      case 'security.enable_encryption':
      case 'cache.enable_cache':
      case 'kiosk.enable_kiosk_mode':
      case 'kiosk.fullscreen_mode':
      case 'kiosk.hide_cursor':
      case 'kiosk.disable_keyboard':
      case 'debug.enable_face_detection_logs':
      case 'debug.enable_performance_monitoring':
      case 'debug.enable_network_logs':
      case 'debug.enable_cache_logs':
      case 'debug.crash_reporting':
      case 'debug.analytics_enabled':
        return 'Ví dụ: true (bật), false (tắt)';
      
      // Color parameters
      case 'ui.primary_color':
      case 'ui.secondary_color':
      case 'ui.success_color':
      case 'ui.error_color':
      case 'ui.warning_color':
      case 'ui.info_color':
        return 'Ví dụ: #FF0000 (đỏ), #00FF00 (xanh lá), #0000FF (xanh dương)';
      
      // Percentage parameters
      case 'performance.memory_warning_threshold':
      case 'performance.cpu_usage_limit':
        return 'Ví dụ: 70 (70%), 80 (80%), 90 (90%)';
      
      // File size parameters
      case 'face_detection.max_image_size_bytes':
      case 'performance.cache_size_mb':
        return 'Ví dụ: 1048576 (1MB), 5242880 (5MB), 10485760 (10MB)';
      
      // String parameters
      case 'face_detection.image_format':
        return 'Ví dụ: jpg, png, webp';
      case 'camera.flash_mode':
        return 'Ví dụ: auto, on, off';
      case 'ui.theme_mode':
        return 'Ví dụ: light, dark, auto';
      case 'ui.language':
        return 'Ví dụ: vi, en, ja';
      case 'debug.log_level':
        return 'Ví dụ: error, warning, info, debug';
      
      default:
        return 'Vui lòng tham khảo tài liệu hoặc liên hệ quản trị viên.';
    }
  }

  /// Get category name in Vietnamese
  static String _getCategoryName(String category) {
    switch (category) {
      case 'face_detection':
        return 'Nhận diện khuôn mặt';
      case 'network':
        return 'Mạng';
      case 'ui':
        return 'Giao diện';
      case 'performance':
        return 'Hiệu suất';
      case 'camera':
        return 'Camera';
      case 'security':
        return 'Bảo mật';
      case 'cache':
        return 'Cache';
      case 'kiosk':
        return 'Kiosk';
      case 'debug':
        return 'Debug';
      default:
        return category.toUpperCase();
    }
  }
} 
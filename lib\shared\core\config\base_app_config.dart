import 'package:flutter/foundation.dart';
import '../../services/api_endpoints.dart';

/// Base Application Configuration
/// 
/// This abstract class defines the common configuration interface
/// that both mobile and terminal apps must implement. It provides
/// shared configuration properties while allowing app-specific customization.
abstract class BaseAppConfig {
  // ============================================================================
  // ENVIRONMENT CONFIGURATION
  // ============================================================================
  
  /// Current app environment
  AppEnvironment get environment {
    if (kDebugMode) {
      return AppEnvironment.development;
    } else if (kProfileMode) {
      return AppEnvironment.staging;
    } else {
      return AppEnvironment.production;
    }
  }

  /// Check if app is in development mode
  bool get isDevelopment => environment == AppEnvironment.development;
  
  /// Check if app is in staging mode
  bool get isStaging => environment == AppEnvironment.staging;
  
  /// Check if app is in production mode
  bool get isProduction => environment == AppEnvironment.production;

  // ============================================================================
  // API CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Base API URL based on environment
  String get baseApiUrl {
    switch (environment) {
      case AppEnvironment.development:
        return '${ApiEndpoints.devBaseUrl}${ApiEndpoints.apiVersion}';
      case AppEnvironment.staging:
        return '${ApiEndpoints.stagingBaseUrl}${ApiEndpoints.apiVersion}';
      case AppEnvironment.production:
        return '${ApiEndpoints.prodBaseUrl}${ApiEndpoints.apiVersion}';
    }
  }

  /// API timeout duration
  Duration get apiTimeout => const Duration(seconds: 30);
  
  /// API retry attempts
  int get apiRetryAttempts => 3;
  
  /// API retry delay
  Duration get apiRetryDelay => const Duration(seconds: 2);

  // ============================================================================
  // SECURITY CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Token refresh threshold
  Duration get tokenRefreshThreshold => const Duration(minutes: 5);
  
  /// Session timeout duration
  Duration get sessionTimeout => const Duration(hours: 8);
  
  /// Maximum login attempts
  int get maxLoginAttempts => 5;
  
  /// Login attempt lockout duration
  Duration get loginLockoutDuration => const Duration(minutes: 15);

  // ============================================================================
  // LOGGING CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Enable debug logging
  bool get enableDebugLogging => isDevelopment;
  
  /// Enable API request/response logging
  bool get enableApiLogging => isDevelopment;
  
  /// Enable error reporting
  bool get enableErrorReporting => isProduction;
  
  /// Enable analytics
  bool get enableAnalytics => isProduction;

  // ============================================================================
  // CACHE CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Cache duration for API responses
  Duration get cacheApiDuration => const Duration(minutes: 5);
  
  /// Cache duration for user data
  Duration get cacheUserDuration => const Duration(hours: 1);
  
  /// Maximum cache size in MB
  int get maxCacheSizeMB => 50;

  // ============================================================================
  // FACE DETECTION CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Face detection performance mode
  FaceDetectionMode get faceDetectionMode {
    return isDevelopment 
        ? FaceDetectionMode.accurate 
        : FaceDetectionMode.fast;
  }
  
  /// Face detection timeout
  Duration get faceDetectionTimeout => const Duration(milliseconds: 100);
  
  /// Frame skip count for face detection
  int get faceDetectionFrameSkip => 3;
  
  /// Minimum face size for detection
  double get minFaceSize => 0.1;
  
  /// Face quality threshold
  double get faceQualityThreshold => 0.7;

  // ============================================================================
  // VALIDATION CONFIGURATION (SHARED)
  // ============================================================================
  
  /// Minimum password length
  int get minPasswordLength => 6;
  
  /// Maximum password length
  int get maxPasswordLength => 128;
  
  /// Username minimum length
  int get minUsernameLength => 3;
  
  /// Username maximum length
  int get maxUsernameLength => 50;
  
  /// Maximum file upload size in MB
  int get maxFileUploadSizeMB => 10;

  // ============================================================================
  // APP-SPECIFIC ABSTRACT METHODS
  // ============================================================================
  
  /// App type identifier
  AppType get appType;
  
  /// App-specific name
  String get appName;
  
  /// App-specific version
  String get appVersion;
  
  /// App-specific build number
  String get buildNumber;
  
  /// App-specific UI configuration
  UIConfig get uiConfig;
  
  /// App-specific feature flags
  FeatureFlags get featureFlags;
  
  /// App-specific camera configuration
  CameraConfig get cameraConfig;

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Get configuration as map for debugging
  Map<String, dynamic> toMap() {
    return {
      'appType': appType.name,
      'appName': appName,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
      'environment': environment.name,
      'baseApiUrl': baseApiUrl,
      'apiTimeout': apiTimeout.inSeconds,
      'faceDetectionMode': faceDetectionMode.name,
      'enableDebugLogging': enableDebugLogging,
      'enableAnalytics': enableAnalytics,
      'minPasswordLength': minPasswordLength,
      'maxFileUploadSizeMB': maxFileUploadSizeMB,
      'uiConfig': uiConfig.toMap(),
      'featureFlags': featureFlags.toMap(),
      'cameraConfig': cameraConfig.toMap(),
    };
  }
  
  /// Validate configuration
  bool validateConfig() {
    try {
      // Basic validation
      if (appName.isEmpty) return false;
      if (appVersion.isEmpty) return false;
      if (buildNumber.isEmpty) return false;
      if (baseApiUrl.isEmpty) return false;
      if (minPasswordLength <= 0) return false;
      if (maxPasswordLength <= minPasswordLength) return false;
      
      // App-specific validation
      return validateAppSpecificConfig();
    } catch (e) {
      if (kDebugMode) {
        print('Configuration validation error: $e');
      }
      return false;
    }
  }
  
  /// App-specific configuration validation
  bool validateAppSpecificConfig();
}

/// App type enumeration
enum AppType {
  mobile,
  terminal,
}

/// App environment enumeration
enum AppEnvironment {
  development,
  staging,
  production,
}

/// Face detection mode enumeration
enum FaceDetectionMode {
  fast,
  accurate,
}

/// UI Configuration interface
abstract class UIConfig {
  Duration get defaultAnimationDuration;
  Duration get pageTransitionDuration;
  Duration get snackbarDuration;
  Duration get loadingIndicatorDelay;
  
  Map<String, dynamic> toMap();
}

/// Feature Flags interface
abstract class FeatureFlags {
  bool get enableFaceCapture;
  bool get enableUserManagement;
  bool get enableOfflineMode;
  bool get enableBiometricAuth;
  bool get enablePushNotifications;
  
  Map<String, dynamic> toMap();
}

/// Camera Configuration interface
abstract class CameraConfig {
  CameraResolution get defaultCameraResolution;
  double get cameraAspectRatio;
  Duration get cameraCaptureTimeout;
  int get maxFaceCaptures;
  Duration get cacheFaceDuration;
  
  Map<String, dynamic> toMap();
}

/// Camera resolution enumeration
enum CameraResolution {
  low,
  medium,
  high,
  veryHigh,
}

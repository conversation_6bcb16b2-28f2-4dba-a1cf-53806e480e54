# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added
- Initial release of relay_controller library
- `RelayController` abstract base class with `triggerOn()` and `triggerOff()` methods
- `BluetoothRelayController` for Bluetooth Classic communication using flutter_bluetooth_serial
- `HttpRelayController` for HTTP-based relay control with support for GET, POST, PUT, PATCH methods
- `MqttRelayController` for MQTT-based relay control with configurable QoS and retain settings
- `UsbRelayController` for USB Serial communication using usb_serial
- Comprehensive error handling with specific exception types for each controller
- Support for custom commands, timeouts, and connection parameters
- Authentication support for HTTP controllers (Basic Auth)
- Auto-connect functionality for USB controllers
- Simplified controller variants (SimpleHttpRelayController, AuthenticatedHttpRelayController, AutoConnectUsbRelayController)
- Complete example application demonstrating all controller types
- Comprehensive documentation and README

### Features
- Unified interface across all communication methods
- Flexible configuration options for each controller type
- Proper resource management with dispose() methods
- Connection status monitoring
- Device discovery for Bluetooth and USB controllers
- Secure MQTT connections with TLS/SSL support
- Custom message formats and commands
- Timeout handling for all operations

### Dependencies
- flutter_bluetooth_serial: ^0.4.0
- http: ^1.1.0
- mqtt_client: ^10.0.0
- usb_serial: ^0.5.0

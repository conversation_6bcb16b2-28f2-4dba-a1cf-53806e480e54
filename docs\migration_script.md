# Migration Script - Component <PERSON><PERSON><PERSON><PERSON> Đổi

## Tổng Quan
Script này hướng dẫn từng bước để chuyển đổi components từ `@lib\shared\presentation\widgets\common` sang `@lib\shared\components`.

## Phase 1: TabsBar Migration (EASIEST - START HERE)

### Files cần chỉnh sửa:
1. `lib/apps/mobile/presentation/screens/dashboard.dart`
2. `lib/apps/mobile/presentation/screens/notifications_screen.dart`
3. `lib/apps/mobile/presentation/screens/profile_screen.dart`
4. `lib/apps/mobile/presentation/screens/tools_screen/tools_screen.dart`

### Changes Required:

#### 1. Import Statement
```dart
// OLD
import '../../../../shared/presentation/widgets/common/c_tabs_bar.dart';

// NEW  
import '../../../../shared/components/tabs_bar.dart';
```

#### 2. Component Usage
```dart
// OLD
TabsBar(
  currentIndex: _selectedIndex,
  onTap: (index) {
    setState(() {
      _selectedIndex = index;
    });
  },
)

// NEW
TabsBar(
  selectedIndex: _selectedIndex,
  onTabSelected: (index) {
    setState(() {
      _selectedIndex = index;
    });
  },
)
```

### Validation Steps:
1. Run `flutter analyze` to check for compilation errors
2. Test navigation between tabs
3. Verify tab highlighting works correctly

## Phase 2: CTextField Migration (COMPLEX - REQUIRES CAREFUL TESTING)

### Files cần chỉnh sửa:
1. `lib/apps/mobile/presentation/screens/login_screen.dart` (3 usages)
2. `lib/apps/mobile/presentation/screens/tenant_create_screen.dart` (2 usages)
3. `lib/apps/mobile/presentation/screens/forgot_password/enter_email_screen.dart` (1 usage)

### Changes Required:

#### 1. Import Statement
```dart
// OLD
import '../../../../shared/presentation/widgets/common/c_text_field.dart';

// NEW
import '../../../../shared/components/app_input_field.dart';
```

#### 2. Component Usage
```dart
// OLD
CTextField(
  controller: _usernameController,
  label: 'Tên đăng nhập',
  hintText: 'Nhập tên đăng nhập',
  height: 38,
  isRequired: true,
  validator: (value) => _validationService.validateUserName(value),
)

// NEW
AppInputField(
  controller: _usernameController,
  label: 'Tên đăng nhập',
  placeholder: 'Nhập tên đăng nhập',
  height: 38,
  isRequired: true,
  validator: (value) => _validationService.validateUserName(value),
)
```

#### 3. Password Fields
```dart
// OLD
CTextField(
  controller: _passwordController,
  label: 'Mật khẩu',
  hintText: 'Nhập mật khẩu của bạn',
  height: 38,
  isPassword: true,
  obscureText: _obscurePassword,
  onToggleObscure: () => setState(() {
    _obscurePassword = !_obscurePassword;
  }),
  isRequired: true,
  validator: (value) => _validationService.validatePassword(value),
)

// NEW
AppInputField(
  controller: _passwordController,
  label: 'Mật khẩu',
  placeholder: 'Nhập mật khẩu của bạn',
  height: 38,
  isPassword: true,
  isRequired: true,
  validator: (value) => _validationService.validatePassword(value),
)
```

**Note**: AppInputField handles password visibility toggle internally, so `obscureText` and `onToggleObscure` are not needed.

### Validation Steps:
1. Test all form validations
2. Test password visibility toggle
3. Test form submission
4. Verify styling matches previous design

## Phase 3: ErrorScreen Migration

### Action Required:
Move `ErrorScreen` and `NotFoundScreen` from `widgets/common` to `shared/components`

### Files to move:
1. `lib/shared/presentation/widgets/common/error_screen.dart` → `lib/shared/components/error_screen.dart`
2. `lib/shared/presentation/widgets/common/not_found_screen.dart` → `lib/shared/components/not_found_screen.dart`

### Files cần cập nhật import:
1. `lib/apps/mobile/routes/mobile_router.dart`
2. `lib/apps/terminal/routes/terminal_router.dart`

### Changes Required:
```dart
// OLD
import '../../../shared/presentation/widgets/common/error_screen.dart';
import '../../../shared/presentation/widgets/common/not_found_screen.dart';

// NEW
import '../../../shared/components/error_screen.dart';
import '../../../shared/components/not_found_screen.dart';
```

## Phase 4: EnhancedErrorMessage Migration

### File cần chỉnh sửa:
`lib/apps/mobile/presentation/screens/login_screen.dart`

### Analysis Required:
1. Check if `AppNotification` supports all features of `EnhancedErrorMessage.auth()`
2. Compare API and functionality
3. Create custom component if needed

### Potential Changes:
```dart
// OLD
EnhancedErrorMessage.auth(
  message: authProvider.getUserFriendlyError(authProvider.failure!),
  failure: authProvider.failure,
  onDismiss: () => authProvider.clearError(),
  onRetry: authProvider.isErrorRetryable(authProvider.failure!)
      ? () => _handleLogin()
      : null,
),

// NEW (if AppNotification supports similar features)
AppNotification(
  message: authProvider.getUserFriendlyError(authProvider.failure!),
  type: NotificationType.error,
  onDismiss: () => authProvider.clearError(),
  // May need to handle retry differently
)
```

## Testing Checklist

### After Each Phase:
- [ ] Run `flutter analyze` - no errors
- [ ] Run `./scripts/run_mobile.sh` - app starts successfully
- [ ] Test affected screens manually
- [ ] Verify UI/UX matches previous behavior
- [ ] Test form validations (for CTextField migration)
- [ ] Test navigation (for TabsBar migration)
- [ ] Test error handling (for ErrorScreen migration)

### Final Validation:
- [ ] All imports updated
- [ ] No references to old components remain
- [ ] All screens function correctly
- [ ] Performance is not degraded
- [ ] UI styling is consistent

## Rollback Plan

If issues occur during migration:
1. Revert import statements to old components
2. Revert component usage to old API
3. Test to ensure functionality is restored
4. Analyze and fix issues before retrying migration

## Notes

- Keep old components until migration is complete and tested
- Consider creating a feature branch for this migration
- Test thoroughly on both mobile and terminal apps if applicable
- Document any API differences discovered during migration

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses HTTP bridge to communicate with Bluetooth devices.
///
/// This controller sends HTTP requests to a bridge server that handles
/// Bluetooth Classic communication. This is useful when direct Bluetooth
/// access is problematic or when you want to centralize Bluetooth management.
///
/// Bridge server should expose endpoints:
/// - POST /bluetooth/connect
/// - POST /bluetooth/disconnect  
/// - POST /bluetooth/send
/// - GET /bluetooth/devices
/// - GET /bluetooth/status
///
/// Example usage:
/// ```dart
/// final controller = BluetoothBridgeController(
///   bridgeUrl: 'http://*************:8080',
///   deviceAddress: '00:11:22:33:44:55',
/// );
///
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class BluetoothBridgeController extends RelayController {
  /// The bridge server base URL.
  final String bridgeUrl;

  /// The Bluetooth device address (MAC address).
  final String deviceAddress;

  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  /// Optional authentication token for bridge server.
  final String? authToken;

  bool _isConnected = false;
  late http.Client _httpClient;

  /// Creates a new [BluetoothBridgeController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [bridgeUrl] is the base URL of the bridge server.
  /// [deviceAddress] is the MAC address of the Bluetooth device.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  /// [authToken] is optional authentication token for bridge server.
  BluetoothBridgeController({
    required super.deviceId,
    required this.bridgeUrl,
    required this.deviceAddress,
    super.deviceName = 'Bluetooth Bridge Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.timeoutSeconds = 10,
    this.authToken,
  }) {
    _httpClient = http.Client();
  }

  /// Gets HTTP headers with optional authentication.
  Map<String, String> get _headers {
    final headers = <String, String>{
      'Content-Type': 'application/json',
    };
    
    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }
    
    return headers;
  }

  /// Connects to the Bluetooth device via bridge server.
  /// 
  /// This method must be called before using [triggerOn] or [triggerOff].
  /// 
  /// Throws [BluetoothRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      final response = await _httpClient
          .post(
            Uri.parse('$bridgeUrl/bluetooth/connect'),
            headers: _headers,
            body: jsonEncode({
              'deviceAddress': deviceAddress,
              'timeout': timeoutSeconds,
            }),
          )
          .timeout(Duration(seconds: timeoutSeconds));

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        if (result['success'] == true) {
          _isConnected = true;
          return;
        } else {
          throw BluetoothRelayException(
            result['error'] ?? 'Failed to connect to Bluetooth device'
          );
        }
      } else {
        throw BluetoothRelayException(
          'Bridge server error: ${response.statusCode} - ${response.body}'
        );
      }
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to connect to Bluetooth device', e);
    }
  }

  /// Disconnects from the Bluetooth device via bridge server.
  Future<void> disconnect() async {
    try {
      if (_isConnected) {
        final response = await _httpClient
            .post(
              Uri.parse('$bridgeUrl/bluetooth/disconnect'),
              headers: _headers,
              body: jsonEncode({
                'deviceAddress': deviceAddress,
              }),
            )
            .timeout(Duration(seconds: 5));

        if (response.statusCode == 200) {
          final result = jsonDecode(response.body);
          if (result['success'] == true) {
            _isConnected = false;
          }
        }
      }
    } catch (e) {
      // Don't throw on disconnect errors, just log
      _isConnected = false;
    }
  }

  /// Sends a command to the Bluetooth device via bridge server.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected) {
        throw const BluetoothRelayException('Not connected to Bluetooth device');
      }

      final response = await _httpClient
          .post(
            Uri.parse('$bridgeUrl/bluetooth/send'),
            headers: _headers,
            body: jsonEncode({
              'deviceAddress': deviceAddress,
              'data': command,
            }),
          )
          .timeout(Duration(seconds: 5));

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        if (result['success'] != true) {
          throw BluetoothRelayException(
            result['error'] ?? 'Failed to send command'
          );
        }
      } else {
        throw BluetoothRelayException(
          'Bridge server error: ${response.statusCode} - ${response.body}'
        );
      }
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    _httpClient.close();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets available Bluetooth devices via bridge server.
  /// 
  /// Returns a list of available Bluetooth devices.
  Future<List<Map<String, String>>> getAvailableDevices() async {
    try {
      final response = await _httpClient
          .get(
            Uri.parse('$bridgeUrl/bluetooth/devices'),
            headers: _headers,
          )
          .timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        if (result['success'] == true) {
          final List<dynamic> devices = result['devices'] ?? [];
          return devices.cast<Map<String, String>>();
        } else {
          throw BluetoothRelayException(
            result['error'] ?? 'Failed to get available devices'
          );
        }
      } else {
        throw BluetoothRelayException(
          'Bridge server error: ${response.statusCode} - ${response.body}'
        );
      }
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to get available devices', e);
    }
  }

  /// Checks bridge server status and Bluetooth availability.
  Future<bool> checkBridgeStatus() async {
    try {
      final response = await _httpClient
          .get(
            Uri.parse('$bridgeUrl/bluetooth/status'),
            headers: _headers,
          )
          .timeout(Duration(seconds: 5));

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        return result['bluetoothEnabled'] == true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}

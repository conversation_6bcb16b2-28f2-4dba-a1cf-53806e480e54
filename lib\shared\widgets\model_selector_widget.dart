import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// Widget for selecting face detection model/engine
/// Allows switching between UltraFace, MediaPipe, and ML Kit
class ModelSelectorWidget extends StatefulWidget {
  final DetectionEngineType currentEngine;
  final Function(DetectionEngineType) onEngineChanged;
  final bool enabled;
  
  const ModelSelectorWidget({
    Key? key,
    required this.currentEngine,
    required this.onEngineChanged,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<ModelSelectorWidget> createState() => _ModelSelectorWidgetState();
}

class _ModelSelectorWidgetState extends State<ModelSelectorWidget> {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.psychology, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Face Detection Engine',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Engine Selection
            _buildEngineSelector(),
            
            const SizedBox(height: 16),
            
            // Current Engine Info
            _buildEngineInfo(),
            
            const SizedBox(height: 16),
            
            // Performance Comparison
            _buildPerformanceComparison(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEngineSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Detection Engine:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        
        // UltraFace Option
        _buildEngineOption(
          engine: DetectionEngineType.ultraface,
          title: 'UltraFace TensorFlow Lite',
          subtitle: 'Best performance for Telpo F8 (45+ FPS)',
          icon: Icons.flash_on,
          color: Colors.orange,
          isRecommended: true,
        ),
        
        // MediaPipe Option
        _buildEngineOption(
          engine: DetectionEngineType.mediapipe,
          title: 'MediaPipe TensorFlow Lite',
          subtitle: 'Best accuracy with landmarks (30+ FPS)',
          icon: Icons.precision_manufacturing,
          color: Colors.green,
        ),
        
        // ML Kit Option
        _buildEngineOption(
          engine: DetectionEngineType.mlkit,
          title: 'Google ML Kit',
          subtitle: 'Fallback option (15 FPS)',
          icon: Icons.cloud,
          color: Colors.blue,
        ),
      ],
    );
  }
  
  Widget _buildEngineOption({
    required DetectionEngineType engine,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    bool isRecommended = false,
  }) {
    final isSelected = widget.currentEngine == engine;
    final isAvailable = _isEngineAvailable(engine);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: widget.enabled && isAvailable ? () {
            widget.onEngineChanged(engine);
          } : null,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? color : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isAvailable ? color : Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: isAvailable ? null : Colors.grey,
                            ),
                          ),
                          if (isRecommended) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Text(
                                'RECOMMENDED',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: isAvailable ? Colors.grey.shade600 : Colors.grey,
                        ),
                      ),
                      if (!isAvailable)
                        Text(
                          _getUnavailableReason(engine),
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.red,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: color,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildEngineInfo() {
    final info = _getEngineInfo(widget.currentEngine);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700, size: 16),
              const SizedBox(width: 6),
              Text(
                'Current Engine: ${info['name']}',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            info['description'],
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPerformanceComparison() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Comparison:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(2),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1),
            3: FlexColumnWidth(1),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(color: Colors.grey.shade100),
              children: [
                _buildTableCell('Engine', isHeader: true),
                _buildTableCell('FPS', isHeader: true),
                _buildTableCell('Memory', isHeader: true),
                _buildTableCell('Accuracy', isHeader: true),
              ],
            ),
            TableRow(
              children: [
                _buildTableCell('UltraFace'),
                _buildTableCell('45+', color: Colors.green),
                _buildTableCell('50MB', color: Colors.green),
                _buildTableCell('95%', color: Colors.orange),
              ],
            ),
            TableRow(
              children: [
                _buildTableCell('MediaPipe'),
                _buildTableCell('30+', color: Colors.orange),
                _buildTableCell('80MB', color: Colors.orange),
                _buildTableCell('98%', color: Colors.green),
              ],
            ),
            TableRow(
              children: [
                _buildTableCell('ML Kit'),
                _buildTableCell('15', color: Colors.red),
                _buildTableCell('120MB', color: Colors.red),
                _buildTableCell('92%', color: Colors.orange),
              ],
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildTableCell(String text, {bool isHeader = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: isHeader ? 12 : 11,
          color: color,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  bool _isEngineAvailable(DetectionEngineType engine) {
    switch (engine) {
      case DetectionEngineType.ultraface:
        // Check if TensorFlow Lite is available and models are downloaded
        return false; // Currently disabled
      case DetectionEngineType.mediapipe:
        return false; // Currently disabled
      case DetectionEngineType.mlkit:
        return true; // Always available
      case DetectionEngineType.custom:
        return false;
    }
  }
  
  String _getUnavailableReason(DetectionEngineType engine) {
    switch (engine) {
      case DetectionEngineType.ultraface:
        return 'TensorFlow Lite dependency disabled, models not downloaded';
      case DetectionEngineType.mediapipe:
        return 'TensorFlow Lite dependency disabled, models not downloaded';
      case DetectionEngineType.mlkit:
        return '';
      case DetectionEngineType.custom:
        return 'Custom engine not implemented';
    }
  }
  
  Map<String, String> _getEngineInfo(DetectionEngineType engine) {
    switch (engine) {
      case DetectionEngineType.ultraface:
        return {
          'name': 'UltraFace TensorFlow Lite',
          'description': 'Lightweight face detection optimized for Telpo F8 RK3399. Provides best performance with 45+ FPS and low memory usage.',
        };
      case DetectionEngineType.mediapipe:
        return {
          'name': 'MediaPipe TensorFlow Lite',
          'description': 'Google MediaPipe face detection with landmarks. Balanced performance and accuracy for mobile devices.',
        };
      case DetectionEngineType.mlkit:
        return {
          'name': 'Google ML Kit',
          'description': 'Google ML Kit face detection. Reliable fallback option with cloud-based processing capabilities.',
        };
      case DetectionEngineType.custom:
        return {
          'name': 'Custom Engine',
          'description': 'Custom face detection implementation.',
        };
    }
  }
}

/// Detection engine types
enum DetectionEngineType {
  ultraface,
  mediapipe,
  mlkit,
  custom,
}

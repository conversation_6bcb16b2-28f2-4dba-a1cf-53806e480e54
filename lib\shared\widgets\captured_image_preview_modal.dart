import 'dart:io';
import 'package:flutter/material.dart';
import '../core/constants/face_capture_constants.dart';
import '../providers/face_detection_provider.dart';

/// Modal để xem ảnh đã capture với tùy chọn chụp lại
class CapturedImagePreviewModal extends StatelessWidget {
  final String imagePath;
  final FaceDirection direction;
  final VoidCallback? onRetake;
  final VoidCallback? onClose;

  const CapturedImagePreviewModal({
    super.key,
    required this.imagePath,
    required this.direction,
    this.onRetake,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.9),
      child: SafeArea(
        child: Stack(
          children: [
            // Background tap to close
            Positioned.fill(
              child: GestureDetector(
                onTap: onClose,
                child: Container(color: Colors.transparent),
              ),
            ),
            
            // Main content
            Center(
              child: Container(
                margin: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _getDirectionIcon(direction),
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Ảnh ${_getDirectionName(direction)}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: onClose,
                            icon: const Icon(
                              Icons.close,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Image preview
                    Container(
                      constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height * 0.6,
                        maxWidth: MediaQuery.of(context).size.width * 0.8,
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                        child: Image.file(
                          File(imagePath),
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              height: 200,
                              color: Colors.grey[200],
                              child: const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      size: 48,
                                      color: Colors.grey,
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Không thể tải ảnh',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    
                    // Action buttons
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: onClose,
                              icon: const Icon(Icons.check),
                              label: const Text('Giữ ảnh này'),
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                side: const BorderSide(color: Colors.green),
                                foregroundColor: Colors.green,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: onRetake,
                              icon: const Icon(Icons.camera_alt),
                              label: const Text('Chụp lại'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDirectionName(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return 'chính diện';
      case FaceDirection.left:
        return 'trái';
      case FaceDirection.right:
        return 'phải';
      case FaceDirection.top:
        return 'lên trên';
      case FaceDirection.bottom:
        return 'xuống dưới';
      case FaceDirection.unknown:
        return 'không xác định';
    }
  }

  IconData _getDirectionIcon(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return Icons.face;
      case FaceDirection.left:
        return Icons.arrow_back;
      case FaceDirection.right:
        return Icons.arrow_forward;
      case FaceDirection.top:
        return Icons.arrow_upward;
      case FaceDirection.bottom:
        return Icons.arrow_downward;
      case FaceDirection.unknown:
        return Icons.help;
    }
  }

  /// Static method để hiển thị modal
  static Future<void> show({
    required BuildContext context,
    required String imagePath,
    required FaceDirection direction,
    VoidCallback? onRetake,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return CapturedImagePreviewModal(
          imagePath: imagePath,
          direction: direction,
          onRetake: () {
            Navigator.of(context).pop();
            onRetake?.call();
          },
          onClose: () => Navigator.of(context).pop(),
        );
      },
    );
  }
}

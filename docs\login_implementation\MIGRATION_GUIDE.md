# Hướng Dẫn Migration - <PERSON><PERSON><PERSON> Năng Đăng Nhập

## 📋 Tổng Quan

Hướng dẫn này mô tả các bước cụ thể để migration từ implementation hiệ<PERSON> tạ<PERSON> sang implementation mới với đầy đủ chức năng đăng nhập.

## 🎯 Migration Goals

1. **<PERSON><PERSON> thế mock login** bằng real API integration
2. **Implement dynamic base URL** configuration
3. **Integrate auth provider** với login screen
4. **Add comprehensive error handling**
5. **Ensure backward compatibility**

## 📊 Current vs Target State

### Current State Analysis

#### Login Screen (`lib/apps/mobile/presentation/screens/login_screen.dart`)
```dart
// Current implementation - Mock login
void _handleLogin() {
  if (_formKey.currentState?.validate() ?? false) {
    setState(() {
      _isLoading = true;
    });

    // Simulate login process
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        goToDashboard();
      }
    });
  }
}
```

#### Auth Provider (`lib/apps/mobile/providers/mobile_auth_provider.dart`)
```dart
// Current implementation - Mock authentication
Future<bool> login(String email, String password) async {
  _setAuthStatus(AuthStatus.loading);
  
  try {
    // Simulate login API call
    await Future.delayed(const Duration(seconds: 1));
    
    // For demo purposes, accept any non-empty credentials
    if (email.isNotEmpty && password.isNotEmpty) {
      _setAuthStatus(AuthStatus.authenticated);
      _isAuthenticated = true;
      _errorMessage = null;
      notifyListeners();
      return true;
    } else {
      _setError('Invalid credentials');
      return false;
    }
  } catch (error) {
    _setError(error.toString());
    return false;
  }
}
```

### Target State

#### Enhanced Login Screen
- Real API integration
- Dynamic base URL support
- Proper error handling
- Loading states management

#### Enhanced Auth Provider
- Repository pattern integration
- Token management
- Comprehensive error handling
- State management

## 🚀 Step-by-Step Migration

### Step 1: Update API Endpoints (15 phút)

#### File: `lib/shared/services/api_endpoints.dart`

**Before:**
```dart
static const String login = '/auth/login';
```

**After:**
```dart
static const String login = '/api/v3.1/identity/login';

// Add dynamic base URL support
static String getBaseUrlByEnvironment(AppEnvironment environment) {
  switch (environment) {
    case AppEnvironment.development:
      return 'http://192.168.137.1:5000';
    case AppEnvironment.staging:
      return 'https://staging-api.c-faces.com';
    case AppEnvironment.production:
      return 'https://api.c-faces.com';
  }
}
```

### Step 2: Enhance Service Locator (30 phút)

#### File: `lib/shared/services/service_locator.dart`

**Add method:**
```dart
/// Update base URL for API client
Future<void> updateBaseUrl(String newBaseUrl) async {
  // Update HTTP client service
  await _httpClientService.updateBaseUrl(newBaseUrl);
  
  // Update API client in auth service
  _authService.updateApiClient(_httpClientService.apiClient);
  
  print('Base URL updated to: $newBaseUrl');
}

/// Get base URL by environment
String getBaseUrlByEnvironment(AppEnvironment environment) {
  return ApiEndpoints.getBaseUrlByEnvironment(environment);
}
```

### Step 3: Update Auth Provider (45 phút)

#### File: `lib/apps/mobile/presentation/providers/auth_provider.dart`

**Replace entire content:**
```dart
import 'package:flutter/foundation.dart';
import '../../../shared/presentation/providers/base/base_auth_provider.dart';
import '../../../shared/domain/use_cases/auth/login_use_case.dart';
import '../../../shared/domain/use_cases/auth/logout_use_case.dart';

class AuthProvider extends BaseAuthProvider {
  AuthProvider({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
  }) : super(
    loginUseCase: loginUseCase,
    logoutUseCase: logoutUseCase,
  );

  /// Login with username and password
  Future<bool> login({
    required String userName,
    required String password,
  }) async {
    setAuthStatus(AuthStatus.loading);
    
    try {
      final result = await loginUseCase(LoginParams(
        userName: userName,
        password: password,
      ));
      
      return result.fold(
        (failure) {
          handleAuthError(failure);
          return false;
        },
        (authResult) {
          handleLoginSuccess(authResult);
          return true;
        },
      );
    } catch (e) {
      setError('Lỗi không xác định: ${e.toString()}');
      setAuthStatus(AuthStatus.unauthenticated);
      return false;
    }
  }
  
  /// Get user-friendly error message
  String getUserFriendlyError(String errorCode) {
    switch (errorCode) {
      case 'INVALID_CREDENTIALS':
        return 'Tên đăng nhập hoặc mật khẩu không đúng';
      case 'NETWORK_ERROR':
        return 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';
      case 'VALIDATION_ERROR':
        return 'Thông tin đăng nhập không hợp lệ';
      case 'INTERNAL_ERROR':
        return 'Lỗi server. Vui lòng thử lại sau.';
      default:
        return 'Đã xảy ra lỗi. Vui lòng thử lại.';
    }
  }
}
```

### Step 4: Update Login Screen (60 phút)

#### File: `lib/apps/mobile/presentation/screens/login_screen.dart`

**Replace `_handleLogin` method:**
```dart
void _handleLogin() async {
  if (!_formKey.currentState!.validate()) return;
  
  try {
    // Determine base URL based on toggle selection
    String baseUrl;
    if (_selectedToggle == 'On Cloud') {
      final environment = AppConfig.environment;
      baseUrl = ApiEndpoints.getBaseUrlByEnvironment(environment);
    } else {
      baseUrl = _serverAddressController.text.trim();
      
      // Validate server address format
      if (!_isValidUrl(baseUrl)) {
        _showErrorMessage('Địa chỉ server không hợp lệ');
        return;
      }
    }
    
    // Update service locator with new base URL
    await ServiceLocator().updateBaseUrl(baseUrl);
    
    // Perform login using auth provider
    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.login(
      userName: _usernameController.text.trim(),
      password: _passwordController.text,
    );
    
    if (success) {
      if (mounted) {
        // Clear form
        _usernameController.clear();
        _passwordController.clear();

        // Navigate to dashboard using the specified method
        goToDashboard();
      }
    } else {
      // Error handling is done by auth provider
      // UI will react to provider state changes
    }
  } catch (e) {
    _showErrorMessage('Lỗi không xác định: ${e.toString()}');
  }
}

bool _isValidUrl(String url) {
  try {
    final uri = Uri.parse(url);
    return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
  } catch (e) {
    return false;
  }
}

void _showErrorMessage(String message) {
  if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

// Navigation helper method for post-login redirect
static void goToDashboard() => context?.go(MobileRouteNames.dashboard);
```

**Add Consumer widget for auth provider:**
```dart
// In build method, wrap login button with Consumer
Consumer<AuthProvider>(
  builder: (context, authProvider, child) {
    return Column(
      children: [
        // Show error message if any
        if (authProvider.errorMessage != null)
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    authProvider.errorMessage!,
                    style: TextStyle(color: Colors.red.shade700),
                  ),
                ),
              ],
            ),
          ),
        
        // Login button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: authProvider.authStatus == AuthStatus.loading
                ? null
                : _handleLogin,
            child: authProvider.authStatus == AuthStatus.loading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Đăng nhập'),
          ),
        ),
      ],
    );
  },
),
```

### Step 5: Update Provider Registration (15 phút)

#### File: `lib/apps/mobile/main_mobile.dart`

**Add auth provider to MultiProvider:**
```dart
MultiProvider(
  providers: [
    // Existing providers...
    
    ChangeNotifierProvider<AuthProvider>(
      create: (context) => AuthProvider(
        loginUseCase: serviceLocator.get<LoginUseCase>(),
        logoutUseCase: serviceLocator.get<LogoutUseCase>(),
      ),
    ),
  ],
  child: MaterialApp.router(
    // App configuration...
  ),
)
```

### Step 6: Add Input Validation (20 phút)

#### Add validation methods to login screen:
```dart
String? _validateUsername(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'Vui lòng nhập tên đăng nhập';
  }
  if (value.trim().length < 3) {
    return 'Tên đăng nhập phải có ít nhất 3 ký tự';
  }
  return null;
}

String? _validatePassword(String? value) {
  if (value == null || value.isEmpty) {
    return 'Vui lòng nhập mật khẩu';
  }
  if (value.length < 6) {
    return 'Mật khẩu phải có ít nhất 6 ký tự';
  }
  return null;
}

String? _validateServerAddress(String? value) {
  if (_selectedToggle == 'On Premise') {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập địa chỉ server';
    }
    if (!_isValidUrl(value.trim())) {
      return 'Địa chỉ server không hợp lệ';
    }
  }
  return null;
}
```

**Update form fields:**
```dart
TextFormField(
  controller: _usernameController,
  validator: _validateUsername,
  decoration: const InputDecoration(
    labelText: 'Tên đăng nhập',
    prefixIcon: Icon(Icons.person),
  ),
),

TextFormField(
  controller: _passwordController,
  validator: _validatePassword,
  obscureText: true,
  decoration: const InputDecoration(
    labelText: 'Mật khẩu',
    prefixIcon: Icon(Icons.lock),
  ),
),

if (_selectedToggle == 'On Premise')
  TextFormField(
    controller: _serverAddressController,
    validator: _validateServerAddress,
    decoration: const InputDecoration(
      labelText: 'Địa chỉ server',
      prefixIcon: Icon(Icons.dns),
      hintText: 'https://your-server.com',
    ),
  ),
```

## ✅ Verification Steps

### 1. Functional Testing
```bash
# Test On Cloud mode
1. Select "On Cloud" toggle
2. Enter valid credentials
3. Verify login success and navigation

# Test On Premise mode
1. Select "On Premise" toggle
2. Enter server address
3. Enter valid credentials
4. Verify login success

# Test error scenarios
1. Invalid credentials
2. Network errors
3. Invalid server address
4. Empty fields
```

### 2. Code Quality Check
```bash
# Run Flutter analyze
flutter analyze

# Run tests
flutter test

# Check for any compilation errors
flutter build apk --debug
```

### 3. Integration Verification
- [ ] Auth provider properly integrated
- [ ] API endpoints updated
- [ ] Error handling working
- [ ] Loading states functional
- [ ] Navigation working
- [ ] Token storage working

## 🔄 Rollback Plan

Nếu migration gặp vấn đề, có thể rollback bằng cách:

1. **Revert login screen changes**
2. **Restore original auth provider**
3. **Revert API endpoint changes**
4. **Remove new dependencies**

### Rollback Commands
```bash
# Restore from git
git checkout HEAD~1 -- lib/apps/mobile/presentation/screens/login_screen.dart
git checkout HEAD~1 -- lib/apps/mobile/providers/mobile_auth_provider.dart
git checkout HEAD~1 -- lib/shared/services/api_endpoints.dart
```

## 📋 Post-Migration Checklist

- [ ] All tests passing
- [ ] No compilation errors
- [ ] Login functionality working in both modes
- [ ] Error handling comprehensive
- [ ] UI responsive and user-friendly
- [ ] Documentation updated
- [ ] Code reviewed and approved
- [ ] Performance acceptable
- [ ] Security considerations addressed
- [ ] Backward compatibility maintained

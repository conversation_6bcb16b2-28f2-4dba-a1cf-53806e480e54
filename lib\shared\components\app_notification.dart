import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

enum NotificationType {
  success,
  error,
  warning,
  info,
}

/// Notification component để hiển thị thông báo
class AppNotification extends StatelessWidget {
  final String message;
  final NotificationType type;
  final VoidCallback? onDismiss;
  final bool showIcon;
  final Duration? duration;
  final bool showRetryButton;
  final VoidCallback? onRetry;
  final String? details;
  final bool showDetails;

  const AppNotification({
    super.key,
    required this.message,
    this.type = NotificationType.info,
    this.onDismiss,
    this.showIcon = true,
    this.duration,
    this.showRetryButton = false,
    this.onRetry,
    this.details,
    this.showDetails = false,
  });

  /// Factory constructor for authentication errors
  factory AppNotification.auth({
    required String message,
    VoidCallback? onDismiss,
    VoidCallback? onRetry,
    String? details,
  }) {
    return AppNotification(
      message: message,
      type: NotificationType.error,
      onDismiss: onDismiss,
      onRetry: onRetry,
      details: details,
      showRetryButton: true,
      showDetails: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _getBorderColor(),
          width: AppDimensions.borderNormal,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (showIcon) ...[
                _buildIcon(),
                SizedBox(width: AppDimensions.spacing12),
              ],
              Expanded(
                child: Text(
                  message,
                  style: AppTextStyles.success.copyWith(
                    color: _getTextColor(),
                  ),
                ),
              ),
              if (onDismiss != null) ...[
                SizedBox(width: AppDimensions.spacing8),
                GestureDetector(
                  onTap: onDismiss,
                  child: Icon(
                    Icons.close,
                    size: AppDimensions.iconS,
                    color: _getIconColor(),
                  ),
                ),
              ],
            ],
          ),
          if (showDetails && details != null) ...[
            SizedBox(height: AppDimensions.spacing8),
            Text(
              details!,
              style: AppTextStyles.caption.copyWith(
                color: _getTextColor().withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
          if (showRetryButton && onRetry != null) ...[
            SizedBox(height: AppDimensions.spacing12),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: onRetry,
                style: TextButton.styleFrom(
                  foregroundColor: _getTextColor(),
                  padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
                    vertical: AppDimensions.paddingXS,
                  ),
                ),
                child: const Text('Thử lại'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildIcon() {
    IconData iconData;
    switch (type) {
      case NotificationType.success:
        iconData = Icons.check_circle;
        break;
      case NotificationType.error:
        iconData = Icons.error;
        break;
      case NotificationType.warning:
        iconData = Icons.warning;
        break;
      case NotificationType.info:
        iconData = Icons.info;
        break;
    }

    return Container(
      width: 18,
      height: 18,
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        size: 12,
        color: _getIconColor(),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case NotificationType.success:
        return AppColors.successBackground;
      case NotificationType.error:
        return AppColors.error.withValues(alpha: 0.1);
      case NotificationType.warning:
        return AppColors.warning.withValues(alpha: 0.1);
      case NotificationType.info:
        return AppColors.info.withValues(alpha: 0.1);
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case NotificationType.success:
        return AppColors.success;
      case NotificationType.error:
        return AppColors.error;
      case NotificationType.warning:
        return AppColors.warning;
      case NotificationType.info:
        return AppColors.info;
    }
  }

  Color _getTextColor() {
    switch (type) {
      case NotificationType.success:
        return AppColors.textPrimary;
      case NotificationType.error:
        return AppColors.error;
      case NotificationType.warning:
        return AppColors.warning;
      case NotificationType.info:
        return AppColors.info;
    }
  }

  Color _getIconColor() {
    switch (type) {
      case NotificationType.success:
        return AppColors.textOnPrimary;
      case NotificationType.error:
        return AppColors.textOnPrimary;
      case NotificationType.warning:
        return AppColors.textOnPrimary;
      case NotificationType.info:
        return AppColors.textOnPrimary;
    }
  }

  Color _getIconBackgroundColor() {
    switch (type) {
      case NotificationType.success:
        return AppColors.success;
      case NotificationType.error:
        return AppColors.error;
      case NotificationType.warning:
        return AppColors.warning;
      case NotificationType.info:
        return AppColors.info;
    }
  }

  /// Hiển thị notification dưới dạng SnackBar
  static void show(
    BuildContext context, {
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: AppNotification(
          message: message,
          type: type,
          showIcon: true,
        ),
        duration: duration,
        backgroundColor: Colors.transparent,
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(AppDimensions.paddingM),
      ),
    );
  }
}

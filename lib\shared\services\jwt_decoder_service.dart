import 'dart:convert';
import 'dart:typed_data';
import '../data/models/auth/jwt_payload_model.dart';

/// Service để decode JWT token và extract thông tin
class JwtDecoderService {
  static const JwtDecoderService _instance = JwtDecoderService._internal();
  
  factory JwtDecoderService() => _instance;
  
  const JwtDecoderService._internal();

  /// Decode JWT token và trả về payload
  JwtPayloadModel? decodeToken(String token) {
    try {
      // JWT có format: header.payload.signature
      final parts = token.split('.');
      if (parts.length != 3) {
        throw Exception('Invalid JWT format');
      }

      // Decode payload (phần thứ 2)
      final payload = _decodeBase64(parts[1]);
      final payloadJson = jsonDecode(payload) as Map<String, dynamic>;

      // Debug logging to see the actual structure
      print('🔍 JWT Payload structure:');
      payloadJson.forEach((key, value) {
        print('  $key: ${value.runtimeType} = ${value.toString().length > 100 ? '${value.toString().substring(0, 100)}...' : value}');
      });

      return JwtPayloadModel.fromJson(payloadJson);
    } catch (e) {
      // Log error nhưng không throw để app không crash
      print('Error decoding JWT token: $e');
      return null;
    }
  }

  /// Decode Base64 URL-safe string
  String _decodeBase64(String str) {
    // Thêm padding nếu cần
    String normalized = str.replaceAll('-', '+').replaceAll('_', '/');
    
    // Thêm padding '=' nếu cần
    switch (normalized.length % 4) {
      case 2:
        normalized += '==';
        break;
      case 3:
        normalized += '=';
        break;
    }

    // Decode base64
    final bytes = base64Decode(normalized);
    return utf8.decode(bytes);
  }

  /// Kiểm tra token có hết hạn không
  bool isTokenExpired(String token) {
    final payload = decodeToken(token);
    if (payload?.exp == null) return true;

    final expiryTime = DateTime.fromMillisecondsSinceEpoch(payload!.exp! * 1000);
    return DateTime.now().isAfter(expiryTime);
  }

  /// Extract tenant info từ token
  TenantInfo? extractTenantInfo(String token) {
    final payload = decodeToken(token);
    return payload?.currentTenant;
  }

  /// Extract user info từ token
  UserInfo? extractUserInfo(String token) {
    final payload = decodeToken(token);
    if (payload == null) return null;

    return UserInfo(
      sub: payload.sub,
      username: payload.username,
      role: payload.role,
    );
  }

  /// Extract permissions từ token
  List<EntityPermission> extractPermissions(String token) {
    final payload = decodeToken(token);
    return payload?.entityPermissions ?? [];
  }

  /// Validate token format (không decode, chỉ check format)
  bool isValidTokenFormat(String token) {
    try {
      final parts = token.split('.');
      return parts.length == 3 && 
             parts.every((part) => part.isNotEmpty);
    } catch (e) {
      return false;
    }
  }
}

/// Helper classes cho extracted info
class UserInfo {
  final String? sub;
  final String? username;
  final RoleInfo? role;

  UserInfo({
    this.sub,
    this.username,
    this.role,
  });
}

import 'package:dartz/dartz.dart';
import '../../entities/auth/auth_result.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/errors/failures.dart';

class RefreshTokenUseCase {
  final AuthRepository repository;

  RefreshTokenUseCase(this.repository);

  Future<Either<Failure, AuthResult>> call(String refreshToken) async {
    // Validate refresh token
    if (refreshToken.isEmpty) {
      return Left(ValidationFailure('Refresh token cannot be empty'));
    }

    // Call repository to refresh token
    return await repository.refreshToken(refreshToken);
  }
}

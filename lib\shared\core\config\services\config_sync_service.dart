/// Configuration Synchronization Service
/// 
/// Handles synchronization of configuration between local and remote sources
/// with conflict resolution and offline support.

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../configuration_manager.dart';
import '../flexible_config_system.dart';
import '../providers/remote_config_provider.dart';

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  conflict,
}

class ConfigSyncResult {
  final SyncStatus status;
  final String? message;
  final Map<String, dynamic>? conflicts;
  final DateTime timestamp;

  ConfigSyncResult({
    required this.status,
    this.message,
    this.conflicts,
    required this.timestamp,
  });
}

class ConfigSyncService {
  static ConfigSyncService? _instance;
  static ConfigSyncService get instance {
    _instance ??= ConfigSyncService._internal();
    return _instance!;
  }
  
  ConfigSyncService._internal();

  final StreamController<ConfigSyncResult> _syncController = 
      StreamController<ConfigSyncResult>.broadcast();
  
  Timer? _autoSyncTimer;
  SyncStatus _currentStatus = SyncStatus.idle;
  DateTime? _lastSyncTime;
  bool _isAutoSyncEnabled = false;
  Duration _autoSyncInterval = const Duration(minutes: 5);

  /// Stream of sync results
  Stream<ConfigSyncResult> get syncStream => _syncController.stream;

  /// Current sync status
  SyncStatus get currentStatus => _currentStatus;

  /// Last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Initialize sync service
  Future<void> initialize({
    bool enableAutoSync = false,
    Duration autoSyncInterval = const Duration(minutes: 5),
  }) async {
    _isAutoSyncEnabled = enableAutoSync;
    _autoSyncInterval = autoSyncInterval;

    if (_isAutoSyncEnabled) {
      _startAutoSync();
    }

    debugPrint('🔄 Config Sync Service initialized');
  }

  /// Start automatic synchronization
  void _startAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = Timer.periodic(_autoSyncInterval, (timer) async {
      if (_currentStatus != SyncStatus.syncing) {
        await syncWithRemote();
      }
    });
    
    debugPrint('⏰ Auto-sync enabled with interval: ${_autoSyncInterval.inMinutes}m');
  }

  /// Stop automatic synchronization
  void stopAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = null;
    _isAutoSyncEnabled = false;
    
    debugPrint('⏹️ Auto-sync disabled');
  }

  /// Manually trigger sync with remote
  Future<ConfigSyncResult> syncWithRemote({
    bool forceSync = false,
    ConflictResolution resolution = ConflictResolution.preferLocal,
  }) async {
    if (_currentStatus == SyncStatus.syncing && !forceSync) {
      return ConfigSyncResult(
        status: SyncStatus.error,
        message: 'Sync already in progress',
        timestamp: DateTime.now(),
      );
    }

    _updateStatus(SyncStatus.syncing);

    try {
      final manager = ConfigurationManager.instance;
      final remoteProvider = _getRemoteProvider();
      
      if (remoteProvider == null) {
        return _createResult(
          SyncStatus.error,
          'No remote configuration provider available',
        );
      }

      // Get local and remote configurations
      final localConfig = manager.exportConfiguration();
      final remoteConfig = await remoteProvider.loadConfiguration();

      // Check for conflicts
      final conflicts = _detectConflicts(localConfig, remoteConfig);
      
      if (conflicts.isNotEmpty) {
        return await _resolveConflicts(
          localConfig,
          remoteConfig,
          conflicts,
          resolution,
        );
      }

      // No conflicts, merge configurations
      final mergedConfig = _mergeConfigurations(localConfig, remoteConfig);
      
      // Apply merged configuration locally
      await manager.importConfiguration(mergedConfig);
      
      // Update remote with any local changes
      await remoteProvider.saveConfiguration(mergedConfig);

      _lastSyncTime = DateTime.now();
      
      return _createResult(
        SyncStatus.success,
        'Configuration synchronized successfully',
      );

    } catch (e) {
      debugPrint('❌ Sync failed: $e');
      return _createResult(
        SyncStatus.error,
        'Sync failed: $e',
      );
    } finally {
      _updateStatus(SyncStatus.idle);
    }
  }

  /// Push local configuration to remote
  Future<ConfigSyncResult> pushToRemote() async {
    _updateStatus(SyncStatus.syncing);

    try {
      final manager = ConfigurationManager.instance;
      final remoteProvider = _getRemoteProvider();
      
      if (remoteProvider == null) {
        return _createResult(
          SyncStatus.error,
          'No remote configuration provider available',
        );
      }

      final localConfig = manager.exportConfiguration();
      await remoteProvider.saveConfiguration(localConfig);

      _lastSyncTime = DateTime.now();
      
      return _createResult(
        SyncStatus.success,
        'Configuration pushed to remote successfully',
      );

    } catch (e) {
      debugPrint('❌ Push failed: $e');
      return _createResult(
        SyncStatus.error,
        'Push failed: $e',
      );
    } finally {
      _updateStatus(SyncStatus.idle);
    }
  }

  /// Pull configuration from remote
  Future<ConfigSyncResult> pullFromRemote({
    bool overwriteLocal = false,
  }) async {
    _updateStatus(SyncStatus.syncing);

    try {
      final manager = ConfigurationManager.instance;
      final remoteProvider = _getRemoteProvider();
      
      if (remoteProvider == null) {
        return _createResult(
          SyncStatus.error,
          'No remote configuration provider available',
        );
      }

      final remoteConfig = await remoteProvider.loadConfiguration();
      
      if (!overwriteLocal) {
        final localConfig = manager.exportConfiguration();
        final conflicts = _detectConflicts(localConfig, remoteConfig);
        
        if (conflicts.isNotEmpty) {
          return _createResult(
            SyncStatus.conflict,
            'Conflicts detected. Use overwriteLocal=true to force pull.',
            conflicts: conflicts,
          );
        }
      }

      await manager.importConfiguration(remoteConfig);

      _lastSyncTime = DateTime.now();
      
      return _createResult(
        SyncStatus.success,
        'Configuration pulled from remote successfully',
      );

    } catch (e) {
      debugPrint('❌ Pull failed: $e');
      return _createResult(
        SyncStatus.error,
        'Pull failed: $e',
      );
    } finally {
      _updateStatus(SyncStatus.idle);
    }
  }

  /// Detect conflicts between local and remote configurations
  Map<String, dynamic> _detectConflicts(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
  ) {
    final conflicts = <String, dynamic>{};
    
    for (final key in {...local.keys, ...remote.keys}) {
      final localValue = local[key];
      final remoteValue = remote[key];
      
      if (localValue != null && remoteValue != null && localValue != remoteValue) {
        conflicts[key] = {
          'local': localValue,
          'remote': remoteValue,
        };
      }
    }
    
    return conflicts;
  }

  /// Resolve conflicts based on resolution strategy
  Future<ConfigSyncResult> _resolveConflicts(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
    Map<String, dynamic> conflicts,
    ConflictResolution resolution,
  ) async {
    final manager = ConfigurationManager.instance;
    Map<String, dynamic> resolvedConfig;

    switch (resolution) {
      case ConflictResolution.preferLocal:
        resolvedConfig = {...remote, ...local};
        break;
      case ConflictResolution.preferRemote:
        resolvedConfig = {...local, ...remote};
        break;
      case ConflictResolution.manual:
        return _createResult(
          SyncStatus.conflict,
          'Manual conflict resolution required',
          conflicts: conflicts,
        );
    }

    // Apply resolved configuration
    await manager.importConfiguration(resolvedConfig);
    
    // Update remote with resolved configuration
    final remoteProvider = _getRemoteProvider();
    if (remoteProvider != null) {
      await remoteProvider.saveConfiguration(resolvedConfig);
    }

    _lastSyncTime = DateTime.now();
    
    return _createResult(
      SyncStatus.success,
      'Conflicts resolved using ${resolution.name} strategy',
    );
  }

  /// Merge configurations without conflicts
  Map<String, dynamic> _mergeConfigurations(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
  ) {
    // Simple merge - in a real implementation, you might want more sophisticated merging
    return {...remote, ...local};
  }

  /// Get remote configuration provider
  RemoteConfigProvider? _getRemoteProvider() {
    final manager = ConfigurationManager.instance;
    
    for (final provider in manager.activeProviders) {
      if (provider is RemoteConfigProvider) {
        return provider;
      }
    }
    
    return null;
  }

  /// Update sync status and notify listeners
  void _updateStatus(SyncStatus status) {
    _currentStatus = status;
  }

  /// Create sync result and notify listeners
  ConfigSyncResult _createResult(
    SyncStatus status,
    String message, {
    Map<String, dynamic>? conflicts,
  }) {
    final result = ConfigSyncResult(
      status: status,
      message: message,
      conflicts: conflicts,
      timestamp: DateTime.now(),
    );
    
    _syncController.add(result);
    return result;
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStatistics() {
    return {
      'current_status': _currentStatus.name,
      'last_sync_time': _lastSyncTime?.toIso8601String(),
      'auto_sync_enabled': _isAutoSyncEnabled,
      'auto_sync_interval_minutes': _autoSyncInterval.inMinutes,
      'remote_provider_available': _getRemoteProvider() != null,
    };
  }

  /// Dispose sync service
  Future<void> dispose() async {
    _autoSyncTimer?.cancel();
    await _syncController.close();
    debugPrint('🔄 Config Sync Service disposed');
  }
}

/// Conflict resolution strategies
enum ConflictResolution {
  preferLocal,
  preferRemote,
  manual,
}

import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Mobile UI Module
/// 
/// This module registers mobile-specific UI dependencies
/// including theme providers, UI services, and mobile-specific
/// UI components and controllers.
/// 
/// Note: This module will be fully implemented when mobile
/// presentation layer is migrated.
void registerMobileUIDependencies() {
  // TODO: Implement when mobile UI components are migrated
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<MobileThemeProvider>(
  //   () => MobileThemeProvider(),
  // );
  // 
  // getIt.registerLazySingleton<MobileDialogService>(
  //   () => MobileDialogServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<MobileSnackbarService>(
  //   () => MobileSnackbarServiceImpl(),
  // );
}

/// Unregister mobile UI dependencies (for testing)
void unregisterMobileUIDependencies() {
  // TODO: Implement when UI dependencies are added
  // if (getIt.isRegistered<MobileThemeProvider>()) {
  //   getIt.unregister<MobileThemeProvider>();
  // }
  // if (getIt.isRegistered<MobileDialogService>()) {
  //   getIt.unregister<MobileDialogService>();
  // }
  // if (getIt.isRegistered<MobileSnackbarService>()) {
  //   getIt.unregister<MobileSnackbarService>();
  // }
}

/// Reset mobile UI module (clear và re-register)
void resetMobileUIModule() {
  unregisterMobileUIDependencies();
  registerMobileUIDependencies();
}

/// Check if mobile UI dependencies are registered
bool areMobileUIDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<MobileThemeProvider>() &&
  //        getIt.isRegistered<MobileDialogService>() &&
  //        getIt.isRegistered<MobileSnackbarService>();
  return true; // Placeholder
}

/// Get mobile UI dependencies for debugging
Map<String, bool> getMobileUIDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'MobileThemeProvider': getIt.isRegistered<MobileThemeProvider>(),
    // 'MobileDialogService': getIt.isRegistered<MobileDialogService>(),
    // 'MobileSnackbarService': getIt.isRegistered<MobileSnackbarService>(),
  };
}

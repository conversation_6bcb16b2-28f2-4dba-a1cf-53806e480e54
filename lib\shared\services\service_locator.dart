import 'package:flutter/foundation.dart';
import 'http_client_service.dart';
import 'auth_service.dart';
import 'flutter_secure_storage.dart';
import 'device_manager_service.dart';
import 'api_endpoints.dart';
import 'flavor_config.dart';
import '../core/config/app_config.dart';
import '../core/di/modules/auth_module.dart';


/// Service Locator để quản lý và khởi tạo tất cả các services
/// Sử dụng Singleton pattern để đảm bảo chỉ có một instance
class ServiceLocator {
  static ServiceLocator? _instance;

  // Services instances
  late HttpClientService _httpClientService;
  late AuthService _authService;
  late SecureStorageService _secureStorageService;
  late DeviceManagerService _deviceManagerService;

  bool _isInitialized = false;

  ServiceLocator._internal();

  /// Singleton factory
  factory ServiceLocator() {
    _instance ??= ServiceLocator._internal();
    return _instance!;
  }

  /// Getters cho các services
  HttpClientService get httpClient => _httpClientService;
  AuthService get auth => _authService;
  SecureStorageService get secureStorage => _secureStorageService;
  DeviceManagerService get deviceManager => _deviceManagerService;

  /// Kiểm tra xem đã khởi tạo chưa
  bool get isInitialized => _isInitialized;

  /// Khởi tạo tất cả các services
  Future<void> initialize({
    String? customBaseUrl,
    bool enableLogging = true,
    bool enableRetry = true,
    int maxRetries = 3,
    int connectTimeout = 30000,
    int receiveTimeout = 30000,
    int sendTimeout = 30000,
  }) async {
    if (_isInitialized) {
      return;
    }

    try {
      // 1. Khởi tạo Secure Storage Service
      _secureStorageService = SecureStorageService();

      // 2. Khởi tạo Device Manager Service
      _deviceManagerService = DeviceManagerService();

      // 3. Khởi tạo HTTP Client Service
      _httpClientService = HttpClientService();

      // Xác định base URL
      String baseUrl;
      if (customBaseUrl != null) {
        baseUrl = customBaseUrl;
      } else {
        // Lấy base URL từ flavor config hoặc environment
        final environment = FlavorConfig.instance.flavor.name;
        baseUrl = ApiEndpoints.getBaseUrlByEnvironment(environment);
      }

      // Cấu hình HTTP client
      final httpConfig = HttpClientConfig(
        baseUrl: ApiEndpoints.createFullUrl(baseUrl, ''),
        enableLogging: enableLogging,
        enableRetry: enableRetry,
        maxRetries: maxRetries,
        connectTimeout: connectTimeout,
        receiveTimeout: receiveTimeout,
        sendTimeout: sendTimeout,
        defaultHeaders: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-App-Version': FlavorConfig.instance.appVersion,
          'X-Platform': 'flutter',
        },
      );

      _httpClientService.initialize(httpConfig);

      // 4. Khởi tạo Auth Service
      _authService = AuthService();
      await _authService.initialize();

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ ServiceLocator initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing ServiceLocator: $e');
      }
      rethrow;
    }
  }

  /// Reset tất cả services (dùng cho testing hoặc logout)
  Future<void> reset() async {
    try {
      if (_isInitialized) {
        // Clear auth data
        await _authService.logout();

        // Clear instance references
        AuthService.clearInstance();

        _isInitialized = false;
        if (kDebugMode) {
          print('✅ ServiceLocator reset successfully');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resetting ServiceLocator: $e');
      }
    }
  }

  /// Cập nhật base URL (ví dụ khi chuyển environment)
  Future<void> updateBaseUrl(String newBaseUrl) async {
    if (_isInitialized) {
      final fullUrl = ApiEndpoints.createFullUrl(newBaseUrl, '');
      _httpClientService.updateBaseUrl(fullUrl);

      // HTTP client đã được update với base URL mới

      if (kDebugMode) {
        print('✅ Base URL updated to: $fullUrl');
      }
    }
  }

  /// Cập nhật base URL theo AppEnvironment (for On Cloud mode)
  Future<void> updateBaseUrlByEnvironment(AppEnvironment environment) async {
    final baseUrl = ApiEndpoints.getBaseUrlByAppEnvironment(environment);
    await updateBaseUrl(baseUrl);

    if (kDebugMode) {
      print('✅ Base URL updated by environment ${environment.name} to: $baseUrl');
    }
  }

  /// Switch base URL cho On Cloud/On Premise mode
  Future<void> switchBaseUrlMode({
    required bool isOnCloudMode,
    AppEnvironment? environment,
    String? onPremiseUrl,
  }) async {
    if (!_isInitialized) {
      throw Exception('ServiceLocator chưa được khởi tạo');
    }

    String newBaseUrl;

    if (isOnCloudMode) {
      // On Cloud mode - sử dụng environment-based URL
      if (environment == null) {
        // Fallback to current flavor environment
        final currentEnvironment = FlavorConfig.instance.flavor.name;
        newBaseUrl = ApiEndpoints.getBaseUrlByEnvironment(currentEnvironment);
      } else {
        newBaseUrl = ApiEndpoints.getBaseUrlByAppEnvironment(environment);
      }
    } else {
      // On Premise mode - sử dụng user-provided URL
      if (onPremiseUrl == null || onPremiseUrl.trim().isEmpty) {
        throw ArgumentError('On Premise URL không được để trống');
      }

      // Validate URL format
      if (!_isValidUrl(onPremiseUrl.trim())) {
        throw ArgumentError('On Premise URL không hợp lệ: $onPremiseUrl');
      }

      newBaseUrl = onPremiseUrl.trim();
    }

    await updateBaseUrl(newBaseUrl);

    // Also update ApiClient in GetIt (used by auth module)
    updateAuthApiClientBaseUrl(newBaseUrl);

    if (kDebugMode) {
      final mode = isOnCloudMode ? 'On Cloud' : 'On Premise';
      print('✅ Switched to $mode mode with base URL: $newBaseUrl');
    }
  }

  /// Validate URL format
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get current base URL
  String? get currentBaseUrl {
    if (!_isInitialized) return null;
    return _httpClientService.config.baseUrl;
  }

  /// Thêm header mặc định cho tất cả requests
  void addGlobalHeader(String key, String value) {
    if (_isInitialized) {
      _httpClientService.addDefaultHeader(key, value);
      if (kDebugMode) {
        print('✅ Global header added: $key = $value');
      }
    }
  }

  /// Xóa header mặc định
  void removeGlobalHeader(String key) {
    if (_isInitialized) {
      _httpClientService.removeDefaultHeader(key);
      if (kDebugMode) {
        print('✅ Global header removed: $key');
      }
    }
  }

  /// Lấy thông tin về tất cả services
  Map<String, dynamic> getServicesInfo() {
    return {
      'isInitialized': _isInitialized,
      'httpClient': {
        'baseUrl': _isInitialized
            ? _httpClientService.config.baseUrl
            : null,
        'headers': _isInitialized
            ? _httpClientService.config.defaultHeaders
            : null,
      },
      'auth': {
        'isAuthenticated': _isInitialized
            ? _authService.isAuthenticated
            : false,
        'authStatus': _isInitialized
            ? _authService.authStatus.toString()
            : 'unknown',
        'currentUser': _isInitialized
            ? _authService.currentUser?.toMap()
            : null,
      },
      'deviceManager': {'available': _isInitialized},
      'secureStorage': {'available': _isInitialized},
    };
  }

  /// Clear instance (dùng cho testing)
  static void clearInstance() {
    _instance = null;
  }
}

/// Extension methods để dễ dàng truy cập services
extension ServiceLocatorExtension on ServiceLocator {
  /// Quick access methods
  Future<bool> get isLoggedIn async => isInitialized && auth.isAuthenticated;

  Future<String?> get currentUserId async => auth.currentUser?.id;

  Future<String?> get currentUserEmail async => auth.currentUser?.email;

  Future<String?> get currentUserName async => auth.currentUser?.fullName;
}

/// Global instance để dễ dàng truy cập
final serviceLocator = ServiceLocator();

/// Helper functions để dễ dàng sử dụng
Future<void> initializeServices({
  String? customBaseUrl,
  bool enableLogging = true,
  bool enableRetry = true,
  int maxRetries = 3,
  int connectTimeout = 30000,
  int receiveTimeout = 30000,
  int sendTimeout = 30000,
}) async {
  await serviceLocator.initialize(
    customBaseUrl: customBaseUrl,
    enableLogging: enableLogging,
    enableRetry: enableRetry,
    maxRetries: maxRetries,
    connectTimeout: connectTimeout,
    receiveTimeout: receiveTimeout,
    sendTimeout: sendTimeout,
  );
}

/// Quick access to services
HttpClientService get httpClient => serviceLocator.httpClient;
AuthService get authService => serviceLocator.auth;
SecureStorageService get secureStorage => serviceLocator.secureStorage;
DeviceManagerService get deviceManager => serviceLocator.deviceManager;

/// Quick access to common operations
Future<bool> get isLoggedIn => serviceLocator.isLoggedIn;
Future<String?> get currentUserId => serviceLocator.currentUserId;
Future<String?> get currentUserEmail => serviceLocator.currentUserEmail;
Future<String?> get currentUserName => serviceLocator.currentUserName;

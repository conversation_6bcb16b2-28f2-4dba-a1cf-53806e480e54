import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:flutter/foundation.dart';
import '../data/models/face_recognition_v3_request.dart';
import '../data/models/face_recognition_v3_response.dart';
import 'image_format_service.dart';

/// Face recognition service for API v3.1
class FaceRecognitionV3Service {
  static FaceRecognitionV3Service? _instance;
  static FaceRecognitionV3Service get instance {
    _instance ??= FaceRecognitionV3Service._();
    return _instance!;
  }
  
  FaceRecognitionV3Service._();

  // API configuration
  String _baseUrl = 'http://************';
  String _apiPath = '/api/v3.1/face/processing/recognize';
  String? _authToken;
  
  // HTTP client with timeout
  final http.Client _httpClient = http.Client();
  static const Duration _requestTimeout = Duration(seconds: 30);

  /// Initialize service with configuration
  void initialize({
    required String baseUrl,
    String? authToken,
    String? apiPath,
  }) {
    _baseUrl = baseUrl.replaceAll(RegExp(r'/$'), ''); // Remove trailing slash
    _authToken = authToken;
    if (apiPath != null) {
      _apiPath = apiPath;
    }
    
    if (kDebugMode) {
      print('🔍 FaceRecognitionV3Service initialized');
      print('   Base URL: $_baseUrl');
      print('   API Path: $_apiPath');
      print('   Auth Token: ${_authToken != null ? 'Set' : 'Not set'}');
    }
  }

  /// Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
    
    if (kDebugMode) {
      print('🔑 Face recognition auth token updated');
    }
  }

  /// Recognize face using API v3.1
  Future<FaceRecognitionV3Response> recognizeFace(FaceRecognitionV3Request request) async {
    try {
      final url = Uri.parse('$_baseUrl$_apiPath');
      
      if (kDebugMode) {
        print('🔍 Face recognition request to: $url');
        print('   Device ID: ${request.deviceId}');
        print('   Camera ID: ${request.cameraId}');
        print('   Tracking ID: ${request.trackingId}');
        print('   Image file: ${request.detectedFace.path}');
        print('   Image size: ${await request.detectedFace.length()} bytes');
        print('   Request timestamp: ${DateTime.now().toIso8601String()}');
      }

      // Create multipart request
      final multipartRequest = http.MultipartRequest('POST', url);
      
      // Add headers
      multipartRequest.headers.addAll(_buildHeaders());
      
      // Add form fields
      multipartRequest.fields['device_id'] = request.deviceId;
      multipartRequest.fields['camera_id'] = request.cameraId;
      multipartRequest.fields['camera_ip'] = request.cameraIp;
      multipartRequest.fields['tracking_id'] = request.trackingId;
      
      // Validate and convert image format if needed
      final validatedFile = await _validateAndConvertImageFormat(request.detectedFace);

      // Add image file with proper MIME type
      final filename = _getValidatedFilename(validatedFile);
      final mimeType = _getMimeTypeFromFilename(filename);

      final imageFile = await http.MultipartFile.fromPath(
        'detected_face',
        validatedFile.path,
        filename: filename,
        contentType: MediaType.parse(mimeType),
      );
      multipartRequest.files.add(imageFile);

      if (kDebugMode) {
        print('📎 Image file details:');
        print('   Filename: $filename');
        print('   MIME Type: $mimeType');
        print('   File size: ${await validatedFile.length()} bytes');
      }

      // Send request with timeout
      final streamedResponse = await _httpClient.send(multipartRequest)
          .timeout(_requestTimeout);

      // Get response
      final response = await http.Response.fromStream(streamedResponse);

      // Clean up temporary file if conversion was needed
      if (validatedFile.path != request.detectedFace.path) {
        try {
          await validatedFile.delete();
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Failed to delete temporary converted file: $e');
          }
        }
      }
      
      if (kDebugMode) {
        print('📥 Face recognition response: ${response.statusCode}');
        print('   Response headers: ${response.headers}');
        print('   Response body length: ${response.body.length} chars');
        if (response.statusCode != 200) {
          print('   ERROR Response body: ${response.body}');
        } else {
          print('   SUCCESS Response body: ${response.body.length > 500 ? '${response.body.substring(0, 500)}...' : response.body}');
        }
        print('   Response timestamp: ${DateTime.now().toIso8601String()}');
      }

      // Parse response
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final recognitionResponse = FaceRecognitionV3Response.fromJson(jsonData);
        
        if (kDebugMode) {
          print('✅ Face recognition successful');
          print('   Recognized: ${recognitionResponse.isUserRecognized}');
          print('   User: ${recognitionResponse.recognizedUserName}');
          print('   Confidence: ${(recognitionResponse.confidence * 100).toStringAsFixed(1)}%');
        }
        
        return recognitionResponse;
      } else {
        throw FaceRecognitionException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          statusCode: response.statusCode,
        );
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition error: $e');
      }
      
      if (e is FaceRecognitionException) {
        rethrow;
      } else {
        throw FaceRecognitionException('Face recognition failed: $e');
      }
    }
  }

  /// Recognize face from image file (convenience method)
  Future<FaceRecognitionV3Response> recognizeFaceFromFile({
    required File imageFile,
    String? deviceId,
    String? cameraId,
    String? cameraIp,
    String? trackingId,
  }) async {
    final request = FaceRecognitionV3Request.fromFaceImage(
      faceImage: imageFile,
      deviceId: deviceId,
      cameraId: cameraId,
      cameraIp: cameraIp,
      trackingId: trackingId,
    );
    
    return recognizeFace(request);
  }

  /// Build HTTP headers
  Map<String, String> _buildHeaders() {
    final headers = <String, String>{
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Pragma': 'no-cache',
      'User-Agent': 'CCAM-Terminal/1.0',
    };
    
    if (_authToken != null) {
      headers['authorization'] = 'Bearer $_authToken';
    }
    
    return headers;
  }

  /// Test connection to face recognition service via /health endpoint
  Future<bool> testConnection() async {
    try {
      final url = Uri.parse('$_baseUrl/health');

      if (kDebugMode) {
        print('🏥 Testing face recognition service health: $url');
      }

      final response = await _httpClient.get(
        url,
        headers: _buildHeaders(),
      ).timeout(const Duration(seconds: 10));

      final isHealthy = response.statusCode == 200;

      if (kDebugMode) {
        print('🏥 Face recognition service health check: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}');
        print('   Status Code: ${response.statusCode}');
        print('   Response: ${response.body.length > 100 ? '${response.body.substring(0, 100)}...' : response.body}');
      }

      return isHealthy;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition service health check failed: $e');
      }
      return false;
    }
  }

  /// Get service status
  Map<String, dynamic> getServiceStatus() {
    return {
      'baseUrl': _baseUrl,
      'apiPath': _apiPath,
      'hasAuthToken': _authToken != null,
      'isConfigured': _baseUrl.isNotEmpty && _apiPath.isNotEmpty,
    };
  }

  /// Validate and convert image format if needed
  Future<File> _validateAndConvertImageFormat(File imageFile) async {
    try {
      // Read image bytes
      final imageBytes = await imageFile.readAsBytes();

      // Check and convert format if needed
      final conversionResult = await ImageFormatService.ensureSupportedFormat(
        imageBytes,
        preferredFormat: 'jpg', // Server prefers JPEG
        quality: 85,
      );

      if (!conversionResult.success) {
        if (kDebugMode) {
          print('⚠️ Image format validation failed: ${conversionResult.error}');
          print('   Using original file as fallback');
        }
        return imageFile; // Return original file as fallback
      }

      // If no conversion was needed, return original file
      if (!conversionResult.converted) {
        if (kDebugMode) {
          print('✅ Image format already supported: ${conversionResult.format}');
        }
        return imageFile;
      }

      // Create temporary file with converted image
      final tempDir = imageFile.parent;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = ImageFormatService.getFileExtension(conversionResult.format!);
      final tempFile = File('${tempDir.path}/converted_face_$timestamp$extension');

      await tempFile.writeAsBytes(conversionResult.imageBytes!);

      if (kDebugMode) {
        print('✅ Image converted for server compatibility');
        print('   Original: ${conversionResult.originalFormat} (${conversionResult.originalSize} bytes)');
        print('   Converted: ${conversionResult.format} (${conversionResult.newSize} bytes)');
        print('   Temp file: ${tempFile.path}');
      }

      return tempFile;

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image format: $e');
        print('   Using original file as fallback');
      }
      return imageFile; // Return original file as fallback
    }
  }

  /// Get validated filename with proper extension
  String _getValidatedFilename(File imageFile) {
    try {
      final path = imageFile.path;
      final filename = path.split('/').last;

      // If filename already has proper extension, use it
      final lowerFilename = filename.toLowerCase();
      if (lowerFilename.endsWith('.jpg') ||
          lowerFilename.endsWith('.jpeg') ||
          lowerFilename.endsWith('.png')) {
        return filename;
      }

      // Otherwise, create filename with timestamp and .jpg extension
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'face_$timestamp.jpg';

    } catch (e) {
      // Fallback filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'face_$timestamp.jpg';
    }
  }

  /// Get MIME type from filename extension
  String _getMimeTypeFromFilename(String filename) {
    final lowerFilename = filename.toLowerCase();

    if (lowerFilename.endsWith('.png')) {
      return 'image/png';
    } else if (lowerFilename.endsWith('.jpg') || lowerFilename.endsWith('.jpeg')) {
      return 'image/jpeg';
    } else {
      // Default to JPEG for unknown extensions
      return 'image/jpeg';
    }
  }

  /// Dispose service
  void dispose() {
    _httpClient.close();

    if (kDebugMode) {
      print('🔄 FaceRecognitionV3Service disposed');
    }
  }
}

/// Face recognition exception
class FaceRecognitionException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const FaceRecognitionException(
    this.message, {
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    if (statusCode != null) {
      return 'FaceRecognitionException($statusCode): $message';
    }
    return 'FaceRecognitionException: $message';
  }
}

/// Migration helper to convert old response format to new format
class FaceRecognitionMigrationHelper {
  /// Convert v3.1 response to legacy format for backward compatibility
  static Map<String, dynamic> convertToLegacyFormat(FaceRecognitionV3Response v3Response) {
    if (!v3Response.isSuccess || v3Response.data == null) {
      return {
        'success': false,
        'message': v3Response.message,
        'user': null,
        'confidence': 0.0,
        'reason': v3Response.message,
      };
    }

    final data = v3Response.data!;
    final recognition = data.recognition;
    
    return {
      'success': true,
      'message': 'Recognition successful',
      'user': recognition.isRecognized ? {
        'id': recognition.recognizeId,
        'name': recognition.recognizeName,
      } : null,
      'confidence': recognition.confidence,
      'reason': recognition.isRecognized 
          ? 'Access granted: User recognized'
          : 'Access denied: User not recognized',
      'qualityPass': recognition.qualityPass,
      'maskDetected': recognition.maskDetected,
      'eventId': data.eventId,
      'trackingId': data.trackingId,
    };
  }
}

#!/usr/bin/env python3
"""
Script to convert face recognition models from various formats to TensorFlow Lite.
This script handles conversion from ONNX, PyTorch, and other formats to TFLite.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse

try:
    import tensorflow as tf
    import onnx
    from onnx_tf.backend import prepare
    import numpy as np
except ImportError as e:
    print(f"Missing required dependencies: {e}")
    print("Please install: pip install tensorflow onnx onnx-tf")
    sys.exit(1)

class ModelConverter:
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        self.temp_dir = Path(tempfile.mkdtemp())
        
    def __del__(self):
        """Clean up temporary directory."""
        import shutil
        if hasattr(self, 'temp_dir') and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def convert_onnx_to_tflite(self, onnx_path: Path, output_path: Path, 
                              input_shape: List[int], quantize: bool = True) -> bool:
        """Convert ONNX model to TensorFlow Lite."""
        try:
            print(f"Converting {onnx_path.name} to TensorFlow Lite...")
            
            # Load ONNX model
            onnx_model = onnx.load(str(onnx_path))
            
            # Convert to TensorFlow
            tf_rep = prepare(onnx_model)
            
            # Create temporary SavedModel
            saved_model_dir = self.temp_dir / "saved_model"
            tf_rep.export_graph(str(saved_model_dir))
            
            # Convert to TensorFlow Lite
            converter = tf.lite.TFLiteConverter.from_saved_model(str(saved_model_dir))
            
            # Set optimization flags
            converter.optimizations = [tf.lite.Optimize.DEFAULT]
            
            if quantize:
                # Enable quantization
                converter.representative_dataset = self._create_representative_dataset(input_shape)
                converter.target_spec.supported_ops = [tf.lite.OpsSet.TFLITE_BUILTINS_INT8]
                converter.inference_input_type = tf.uint8
                converter.inference_output_type = tf.uint8
            
            # Convert
            tflite_model = converter.convert()
            
            # Save TFLite model
            with open(output_path, 'wb') as f:
                f.write(tflite_model)
            
            print(f"✓ Successfully converted to {output_path}")
            return True
            
        except Exception as e:
            print(f"✗ Error converting {onnx_path.name}: {e}")
            return False
    
    def _create_representative_dataset(self, input_shape: List[int]):
        """Create representative dataset for quantization."""
        def representative_data_gen():
            for _ in range(100):
                # Generate random data matching input shape
                data = np.random.random(input_shape).astype(np.float32)
                yield [data]
        return representative_data_gen
    
    def download_and_convert_ultraface(self) -> bool:
        """Download and convert UltraFace model."""
        print("\n" + "="*60)
        print("Processing UltraFace RFB-320 Model")
        print("="*60)
        
        # Download ONNX model
        onnx_url = "https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB/raw/master/models/onnx/version-RFB/RFB-320.onnx"
        onnx_path = self.temp_dir / "ultraface_320.onnx"
        
        if not self._download_file(onnx_url, onnx_path):
            return False
        
        # Convert to TFLite
        output_path = self.models_dir / "ultraface_320.tflite"
        return self.convert_onnx_to_tflite(
            onnx_path, output_path, 
            input_shape=[1, 3, 240, 320]
        )
    
    def download_and_convert_mobilefacenet(self) -> bool:
        """Download and convert MobileFaceNet model."""
        print("\n" + "="*60)
        print("Processing MobileFaceNet Model")
        print("="*60)
        
        # Download InsightFace buffalo_l model
        zip_url = "https://github.com/deepinsight/insightface/releases/download/v0.7/buffalo_l.zip"
        zip_path = self.temp_dir / "buffalo_l.zip"
        
        if not self._download_file(zip_url, zip_path):
            return False
        
        # Extract ONNX model
        import zipfile
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extract("buffalo_l/2d106det.onnx", self.temp_dir)
        
        onnx_path = self.temp_dir / "buffalo_l" / "2d106det.onnx"
        
        # Convert to TFLite
        output_path = self.models_dir / "mobilefacenet.tflite"
        return self.convert_onnx_to_tflite(
            onnx_path, output_path,
            input_shape=[1, 3, 192, 192]
        )
    
    def download_blazeface_model(self) -> bool:
        """Download BlazeFace model (already in TFLite format)."""
        print("\n" + "="*60)
        print("Processing MediaPipe BlazeFace Model")
        print("="*60)
        
        # Download TFLite model directly
        tflite_url = "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/030_BlazeFace/01_float32/face_detection_front_128x128_float32.tflite"
        output_path = self.models_dir / "mediapipe_face.tflite"
        
        return self._download_file(tflite_url, output_path)
    
    def _download_file(self, url: str, filepath: Path) -> bool:
        """Download a file from URL."""
        try:
            import requests
            
            print(f"Downloading {filepath.name} from {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\rProgress: {progress:.1f}%", end='', flush=True)
            
            print(f"\nDownloaded {filepath.name} ({downloaded_size / 1024 / 1024:.1f} MB)")
            return True
            
        except Exception as e:
            print(f"Error downloading {url}: {e}")
            return False
    
    def verify_model(self, model_path: Path) -> bool:
        """Verify that a TFLite model is valid."""
        try:
            interpreter = tf.lite.Interpreter(model_path=str(model_path))
            interpreter.allocate_tensors()
            
            input_details = interpreter.get_input_details()
            output_details = interpreter.get_output_details()
            
            print(f"✓ Model {model_path.name} is valid")
            print(f"  Input shape: {input_details[0]['shape']}")
            print(f"  Output shape: {output_details[0]['shape']}")
            
            return True
            
        except Exception as e:
            print(f"✗ Model {model_path.name} is invalid: {e}")
            return False
    
    def convert_all_models(self, force: bool = False) -> bool:
        """Convert all models."""
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        total_count = 3
        
        # Convert UltraFace
        ultraface_path = self.models_dir / "ultraface_320.tflite"
        if not ultraface_path.exists() or force:
            if self.download_and_convert_ultraface():
                if self.verify_model(ultraface_path):
                    success_count += 1
        else:
            print("Skipping UltraFace (already exists)")
            success_count += 1
        
        # Convert MobileFaceNet
        mobilefacenet_path = self.models_dir / "mobilefacenet.tflite"
        if not mobilefacenet_path.exists() or force:
            if self.download_and_convert_mobilefacenet():
                if self.verify_model(mobilefacenet_path):
                    success_count += 1
        else:
            print("Skipping MobileFaceNet (already exists)")
            success_count += 1
        
        # Download BlazeFace
        blazeface_path = self.models_dir / "mediapipe_face.tflite"
        if not blazeface_path.exists() or force:
            if self.download_blazeface_model():
                if self.verify_model(blazeface_path):
                    success_count += 1
        else:
            print("Skipping BlazeFace (already exists)")
            success_count += 1
        
        print(f"\n{'='*60}")
        print(f"Conversion Summary: {success_count}/{total_count} models processed successfully")
        
        return success_count == total_count

def main():
    parser = argparse.ArgumentParser(description="Convert face recognition models to TensorFlow Lite")
    parser.add_argument(
        "--models-dir",
        default="lib/packages/face_recognition/assets/models",
        help="Directory to save models"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force conversion even if files already exist"
    )
    
    args = parser.parse_args()
    
    # Resolve models directory relative to script location
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / args.models_dir
    
    converter = ModelConverter(str(models_dir))
    
    success = converter.convert_all_models(args.force)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

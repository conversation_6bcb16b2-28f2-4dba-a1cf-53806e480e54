import 'package:flutter/foundation.dart';
import '../core/errors/failures.dart';
import '../core/errors/exceptions.dart';

/// Service for handling and formatting errors throughout the application
class ErrorHandlerService {
  static const ErrorHandlerService _instance = ErrorHandlerService._internal();
  factory ErrorHandlerService() => _instance;
  const ErrorHandlerService._internal();

  /// Convert exception to user-friendly message
  String getErrorMessage(dynamic error) {
    if (error is Failure) {
      return _getFailureMessage(error);
    } else if (error is AppException) {
      return _getExceptionMessage(error);
    } else {
      return 'Đã xảy ra lỗi không xác định. Vui lòng thử lại.';
    }
  }

  /// Get user-friendly message for authentication errors
  String getAuthErrorMessage(Failure failure) {
    if (failure is AuthFailure) {
      switch (failure.code) {
        case 'INVALID_CREDENTIALS':
        case 'UNAUTHORIZED':
          return 'Tên đăng nhập hoặc mật khẩu không đúng. Vui lòng kiểm tra lại.';
        case 'ACCOUNT_LOCKED':
          return 'Tài khoản đã bị khóa. Vui lòng liên hệ quản trị viên.';
        case 'ACCOUNT_DISABLED':
          return 'Tài khoản đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên.';
        case 'TOKEN_EXPIRED':
          return 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
        case 'FORBIDDEN':
          return 'Bạn không có quyền truy cập. Vui lòng liên hệ quản trị viên.';
        default:
          return failure.message.isNotEmpty 
              ? failure.message 
              : 'Đăng nhập thất bại. Vui lòng thử lại.';
      }
    }
    return _getFailureMessage(failure);
  }

  /// Get user-friendly message for network errors
  String getNetworkErrorMessage(Failure failure) {
    if (failure is NetworkFailure) {
      switch (failure.code) {
        case 'CONNECTION_TIMEOUT':
          return 'Kết nối bị timeout. Vui lòng kiểm tra mạng và thử lại.';
        case 'CONNECTION_REFUSED':
          return 'Không thể kết nối đến server. Vui lòng kiểm tra địa chỉ server.';
        case 'NO_INTERNET':
          return 'Không có kết nối internet. Vui lòng kiểm tra kết nối mạng.';
        case 'DNS_ERROR':
          return 'Không thể phân giải tên miền. Vui lòng kiểm tra địa chỉ server.';
        default:
          return failure.message.isNotEmpty 
              ? failure.message 
              : 'Lỗi kết nối mạng. Vui lòng thử lại.';
      }
    }
    return _getFailureMessage(failure);
  }

  /// Get user-friendly message for validation errors
  String getValidationErrorMessage(ValidationFailure failure) {
    if (failure.fieldErrors != null && failure.fieldErrors!.isNotEmpty) {
      final errors = <String>[];
      for (final entry in failure.fieldErrors!.entries) {
        final fieldName = _getFieldDisplayName(entry.key);
        errors.add('$fieldName: ${entry.value.join(', ')}');
      }
      return errors.join('\n');
    }
    
    switch (failure.code) {
      case 'REQUIRED_FIELD':
        return 'Vui lòng điền đầy đủ thông tin bắt buộc.';
      case 'INVALID_FORMAT':
        return 'Định dạng dữ liệu không hợp lệ.';
      case 'INVALID_LENGTH':
        return 'Độ dài dữ liệu không hợp lệ.';
      default:
        return failure.message.isNotEmpty 
            ? failure.message 
            : 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.';
    }
  }

  /// Get user-friendly message for server errors
  String getServerErrorMessage(ServerFailure failure) {
    switch (failure.statusCode) {
      case 400:
        return 'Yêu cầu không hợp lệ. Vui lòng kiểm tra dữ liệu đầu vào.';
      case 401:
        return 'Bạn cần đăng nhập để thực hiện thao tác này.';
      case 403:
        return 'Bạn không có quyền thực hiện thao tác này.';
      case 404:
        return 'Không tìm thấy tài nguyên yêu cầu.';
      case 409:
        return 'Dữ liệu bị xung đột. Vui lòng làm mới và thử lại.';
      case 422:
        return 'Dữ liệu không thể xử lý. Vui lòng kiểm tra lại.';
      case 429:
        return 'Quá nhiều yêu cầu. Vui lòng thử lại sau ít phút.';
      case 500:
        return 'Lỗi server nội bộ. Vui lòng thử lại sau.';
      case 502:
        return 'Server tạm thời không khả dụng. Vui lòng thử lại sau.';
      case 503:
        return 'Dịch vụ tạm thời không khả dụng. Vui lòng thử lại sau.';
      default:
        return failure.message.isNotEmpty 
            ? failure.message 
            : 'Lỗi server. Vui lòng thử lại sau.';
    }
  }

  /// Check if error is retryable
  bool isRetryable(dynamic error) {
    if (error is NetworkFailure) {
      return true; // Network errors are usually retryable
    }
    
    if (error is ServerFailure) {
      // Only retry for certain server errors
      return error.statusCode == null || 
             error.statusCode! >= 500 || 
             error.statusCode == 429;
    }
    
    if (error is AuthFailure) {
      // Only retry for token expiration
      return error.code == 'TOKEN_EXPIRED';
    }
    
    return false;
  }

  /// Log error for debugging (only in debug mode)
  void logError(dynamic error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('🔴 Error: $error');
      if (stackTrace != null) {
        print('📍 Stack trace: $stackTrace');
      }
    }
  }

  /// Get error severity level
  ErrorSeverity getErrorSeverity(dynamic error) {
    if (error is ValidationFailure) {
      return ErrorSeverity.info;
    }
    
    if (error is NetworkFailure) {
      return ErrorSeverity.warning;
    }
    
    if (error is AuthFailure) {
      return error.code == 'FORBIDDEN' 
          ? ErrorSeverity.critical 
          : ErrorSeverity.error;
    }
    
    if (error is ServerFailure) {
      if (error.statusCode != null && error.statusCode! >= 500) {
        return ErrorSeverity.critical;
      }
      return ErrorSeverity.error;
    }
    
    return ErrorSeverity.error;
  }

  /// Private helper methods
  String _getFailureMessage(Failure failure) {
    if (failure is AuthFailure) {
      return getAuthErrorMessage(failure);
    } else if (failure is NetworkFailure) {
      return getNetworkErrorMessage(failure);
    } else if (failure is ValidationFailure) {
      return getValidationErrorMessage(failure);
    } else if (failure is ServerFailure) {
      return getServerErrorMessage(failure);
    } else {
      return failure.message.isNotEmpty 
          ? failure.message 
          : 'Đã xảy ra lỗi. Vui lòng thử lại.';
    }
  }

  String _getExceptionMessage(AppException exception) {
    if (exception is AuthException) {
      return getAuthErrorMessage(AuthFailure(exception.message, code: exception.code));
    } else if (exception is NetworkException) {
      return getNetworkErrorMessage(NetworkFailure(exception.message, code: exception.code));
    } else if (exception is ValidationException) {
      return getValidationErrorMessage(ValidationFailure(
        exception.message, 
        code: exception.code,
        fieldErrors: exception.fieldErrors,
      ));
    } else if (exception is ServerException) {
      return getServerErrorMessage(ServerFailure(
        exception.message, 
        code: exception.code,
        statusCode: exception.statusCode,
      ));
    } else {
      return exception.message.isNotEmpty 
          ? exception.message 
          : 'Đã xảy ra lỗi. Vui lòng thử lại.';
    }
  }

  String _getFieldDisplayName(String fieldName) {
    switch (fieldName.toLowerCase()) {
      case 'username':
      case 'userName':
        return 'Tên đăng nhập';
      case 'password':
        return 'Mật khẩu';
      case 'email':
        return 'Email';
      case 'phone':
        return 'Số điện thoại';
      case 'name':
        return 'Họ tên';
      case 'serverAddress':
        return 'Địa chỉ server';
      default:
        return fieldName;
    }
  }
}

enum ErrorSeverity { info, warning, error, critical }

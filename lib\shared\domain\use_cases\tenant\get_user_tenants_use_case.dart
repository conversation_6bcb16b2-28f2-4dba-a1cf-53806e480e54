import 'package:dartz/dartz.dart';
import '../../entities/tenant/tenant.dart';
import '../../repositories/tenant_repository.dart';
import '../../../core/errors/failures.dart';

class GetUserTenantsUseCase {
  final TenantRepository repository;

  GetUserTenantsUseCase(this.repository);

  Future<Either<Failure, List<Tenant>>> call(GetUserTenantsParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to get user tenants
    return await repository.getUserTenants(
      page: params.page,
      size: params.size,
    );
  }

  ValidationFailure? _validateParams(GetUserTenantsParams params) {
    final errors = <String, List<String>>{};

    // Validate page
    if (params.page < 1) {
      errors['page'] = ['Page must be greater than 0'];
    }

    // Validate size
    if (params.size < 1) {
      errors['size'] = ['Size must be greater than 0'];
    }

    if (params.size > 100) {
      errors['size'] = ['Size must not exceed 100'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Invalid parameters',
        code: 'VALIDATION_ERROR',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class GetUserTenantsParams {
  final int page;
  final int size;

  const GetUserTenantsParams({
    this.page = 1,
    this.size = 10,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GetUserTenantsParams &&
        other.page == page &&
        other.size == size;
  }

  @override
  int get hashCode => page.hashCode ^ size.hashCode;

  @override
  String toString() {
    return 'GetUserTenantsParams(page: $page, size: $size)';
  }
}

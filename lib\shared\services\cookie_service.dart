import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cookie_jar/cookie_jar.dart';
import '../core/constants/api_constants.dart';

/// Service để quản lý cookies (memory-only, không persist)
class CookieService {
  static final CookieService _instance = CookieService._internal();
  factory CookieService() => _instance;
  CookieService._internal();

  CookieJar? _cookieJar;
  bool _initialized = false;

  // Custom storage for preserving original cookie values
  final Map<String, Map<String, String>> _rawCookieValues = {};

  /// Initialize cookie service (memory-only)
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize memory-only cookie jar (không lưu files)
      _cookieJar = CookieJar();

      _initialized = true;
      print('🍪 CookieService initialized successfully (memory-only)');
    } catch (e) {
      print('🔴 Failed to initialize CookieService: $e');
      _cookieJar = CookieJar();
      _initialized = true;
    }
  }

  /// Set refresh token cookie (memory-only, không persist)
  Future<void> setRefreshTokenCookie(String refreshToken, String domain) async {
    await _ensureInitialized();

    try {
      final uri = Uri.parse('https://$domain');

      // Store the original refresh token value in our custom storage to preserve encoding
      _rawCookieValues[domain] = {'refresh_token': refreshToken};

      // Also store in cookie jar for compatibility
      final cookie = Cookie('refresh_token', refreshToken);

      // Set cookie properties
      cookie.domain = domain;
      cookie.path = '/';
      cookie.httpOnly = true;
      cookie.secure = true;

      // Set expiry to 30 days from now (chỉ trong memory)
      cookie.expires = DateTime.now().add(const Duration(days: 30));

      await _cookieJar!.saveFromResponse(uri, [cookie]);
      print('🍪 Refresh token cookie set for domain: $domain (memory-only)');
      print('🍪 Original refresh token length: ${refreshToken.length}');
      print('🍪 Raw storage preserved: ${_rawCookieValues[domain]!['refresh_token']!.length} chars');

      // Verify the cookie was stored correctly
      final storedCookies = await _cookieJar!.loadForRequest(uri);
      final storedRefreshToken = storedCookies
          .where((c) => c.name == 'refresh_token')
          .firstOrNull?.value;

      if (storedRefreshToken != null) {
        print('🍪 CookieJar stored length: ${storedRefreshToken.length}');
        if (storedRefreshToken != refreshToken) {
          print('⚠️ Cookie value was modified by CookieJar - using raw storage instead');
          print('🍪 Original: ${refreshToken.substring(0, 50)}...');
          print('🍪 CookieJar: ${storedRefreshToken.substring(0, 50)}...');
        }
      }
    } catch (e) {
      print('🔴 Failed to set refresh token cookie: $e');
    }
  }

  /// Get refresh token cookie (từ memory)
  Future<String?> getRefreshTokenCookie(String domain) async {
    await _ensureInitialized();

    try {
      // First check raw storage for preserved original value
      if (_rawCookieValues.containsKey(domain) &&
          _rawCookieValues[domain]!.containsKey(ApiConstants.refreshTokenCookieName)) {
        final rawValue = _rawCookieValues[domain]![ApiConstants.refreshTokenCookieName]!;
        print('🍪 Refresh token found in raw storage for domain: $domain (${rawValue.length} chars)');
        return rawValue;
      }

      // Fallback to cookie jar if raw storage doesn't have it
      final uri = Uri.parse('https://$domain');
      final cookies = await _cookieJar!.loadForRequest(uri);

      for (final cookie in cookies) {
        if (cookie.name == ApiConstants.refreshTokenCookieName) {
          print('🍪 Refresh token cookie found in cookie jar for domain: $domain (${cookie.value.length} chars)');
          return cookie.value;
        }
      }

      print('🍪 No refresh token cookie found for domain: $domain');
      return null;
    } catch (e) {
      print('🔴 Failed to get refresh token cookie: $e');
      return null;
    }
  }

  /// Clear all cookies (từ memory)
  Future<void> clearAllCookies() async {
    await _ensureInitialized();

    try {
      await _cookieJar!.deleteAll();

      // Also clear all raw storage
      _rawCookieValues.clear();

      print('🍪 All cookies cleared from memory and raw storage');
    } catch (e) {
      print('🔴 Failed to clear cookies: $e');
    }
  }

  /// Clear cookies for specific domain (từ memory)
  Future<void> clearCookiesForDomain(String domain) async {
    await _ensureInitialized();

    try {
      final uri = Uri.parse('https://$domain');
      await _cookieJar!.delete(uri);

      // Also clear from raw storage
      _rawCookieValues.remove(domain);

      print('🍪 Cookies cleared for domain: $domain (from memory and raw storage)');
    } catch (e) {
      print('🔴 Failed to clear cookies for domain $domain: $e');
    }
  }

  /// Get all cookies for domain (for debugging)
  Future<List<Cookie>> getCookiesForDomain(String domain) async {
    await _ensureInitialized();

    try {
      final uri = Uri.parse('https://$domain');
      final cookies = await _cookieJar!.loadForRequest(uri);

      // Create a list to hold the corrected cookies
      final correctedCookies = <Cookie>[];

      for (final cookie in cookies) {
        print('🍪 Retrieved cookie: ${cookie.name}=${cookie.value}');
        print('🍪 Cookie value length: ${cookie.value.length}');

        // Check if we have a raw value stored for this cookie
        if (_rawCookieValues.containsKey(domain) &&
            _rawCookieValues[domain]!.containsKey(cookie.name)) {
          final rawValue = _rawCookieValues[domain]![cookie.name]!;
          print('🍪 Using raw stored value for ${cookie.name} (length: ${rawValue.length})');

          // Create a new cookie with the raw value
          final correctedCookie = Cookie(cookie.name, rawValue);
          correctedCookie.domain = cookie.domain;
          correctedCookie.path = cookie.path;
          correctedCookie.httpOnly = cookie.httpOnly;
          correctedCookie.secure = cookie.secure;
          correctedCookie.expires = cookie.expires;
          correctedCookies.add(correctedCookie);
        } else {
          // Use the original cookie
          correctedCookies.add(cookie);
        }

        if (cookie.name == 'refresh_token') {
          final displayValue = correctedCookies.last.value;
          print('🍪 Final refresh token first 50 chars: ${displayValue.substring(0, displayValue.length > 50 ? 50 : displayValue.length)}');
        }
      }

      return correctedCookies;
    } catch (e) {
      print('🔴 Failed to get cookies for domain $domain: $e');
      return [];
    }
  }

  /// Ensure cookie service is initialized
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
  }

  /// Get cookie jar for HTTP client integration
  CookieJar? get cookieJar => _cookieJar;

  /// Store raw cookie value to preserve original encoding
  void storeRawCookieValue(String domain, String name, String value) {
    if (!_rawCookieValues.containsKey(domain)) {
      _rawCookieValues[domain] = {};
    }
    _rawCookieValues[domain]![name] = value;

    if (kDebugMode) {
      print('🍪 Raw cookie stored: $name=${value.length} chars for domain $domain');
    }
  }
}

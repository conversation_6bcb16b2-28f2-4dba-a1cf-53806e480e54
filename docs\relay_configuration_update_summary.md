# Relay Configuration Update - USB-TTL Baud Rate & Settings

## 🎯 **Task Completed**

Successfully updated relay configuration system with:
- **Default baud rate**: Changed from 115200 to 9600 for USB-TTL relay modules
- **Configurable settings**: Added RelayConfigService for runtime configuration
- **Import conflicts**: Fixed RelayAction enum conflicts between packages

## 🔧 **Key Changes**

### 1. Default Baud Rate Update

#### Before (ESP32/Arduino Default)
```dart
baudRate: 115200, // Standard baud rate for ESP32/Arduino
```

#### After (USB-TTL Relay Default)
```dart
baudRate: 9600, // Default baud rate for USB-TTL relay modules
```

### 2. New RelayConfigService

**File**: `lib/shared/services/relay_config_service.dart`

#### Core Features
```dart
class RelayConfigService {
  // Default values for USB-TTL relay modules
  static const int _defaultBaudRate = 9600;
  static const String _defaultDeviceName = 'USB-TTL Relay';
  static const int _defaultRelayCount = 4;
  static const bool _defaultAutoConnect = true;

  // Configurable settings
  int get baudRate;
  Future<void> setBaudRate(int baudRate);
  String get deviceName;
  Future<void> setDeviceName(String name);
  int get relayCount;
  Future<void> setRelayCount(int count);
  bool get autoConnect;
  Future<void> setAutoConnect(bool enabled);
}
```

#### Supported Baud Rates
```dart
static List<int> get commonBaudRates => [
  1200,
  2400,
  4800,
  9600,   // Most common for USB-TTL relay modules
  19200,
  38400,
  57600,
  115200, // Common for ESP32/Arduino
];
```

#### Baud Rate Descriptions
```dart
static String getBaudRateDescription(int baudRate) {
  switch (baudRate) {
    case 9600:
      return '9600 (USB-TTL Relay Default)';
    case 115200:
      return '115200 (ESP32/Arduino)';
    // ... other rates
  }
}
```

### 3. Updated Services Integration

#### Stream Screen Manual Initialization
```dart
Future<void> _manuallyInitializeRelayService() async {
  final relayConfigService = RelayConfigService.instance;
  await relayConfigService.initialize();
  
  // Create device configuration from settings
  final deviceConfig = relayConfigService.createDeviceConfig(
    customDeviceId: 'manual-usb-ttl-relay',
  );
  
  print('   Baud Rate: ${deviceConfig.baudRate} (${RelayConfigService.getBaudRateDescription(deviceConfig.baudRate)})');
}
```

#### Face Event Trigger Service
```dart
final deviceConfig = RelayDeviceConfig(
  deviceId: 'auto-usb-ttl-relay',
  deviceName: 'Auto USB-TTL Relay',
  relayCount: 4,
  baudRate: 9600, // Updated default
);
```

### 4. Fixed Import Conflicts

#### Problem
```
Error: The argument type 'RelayAction/*1*/' can't be assigned to the parameter type 'RelayAction/*2*/'.
```

#### Solution - Added Aliases
```dart
// In stream_screen.dart and relay_trigger_service.dart
import 'package:relay_controller/relay_controller.dart' as relay;

// Usage
await relayService.controlRelay(0, relay.RelayAction.on);
await relayService.controlRelay(0, relay.RelayAction.off);
```

## 📱 **Configuration Options**

### Baud Rate Settings
- **1200 bps**: Ultra Low Speed
- **2400 bps**: Very Low Speed  
- **4800 bps**: Low Speed
- **9600 bps**: **USB-TTL Relay Default** ⭐
- **19200 bps**: Medium Speed
- **38400 bps**: Medium Speed
- **57600 bps**: High Speed
- **115200 bps**: ESP32/Arduino Standard

### Device Configuration
- **Device Name**: Customizable display name
- **Relay Count**: Number of relays (default: 4)
- **Auto Connect**: Enable/disable automatic connection
- **Device ID**: Unique identifier for device

## 🔧 **Technical Implementation**

### Persistent Storage
```dart
// Settings stored in SharedPreferences
static const String _keyBaudRate = 'relay_baud_rate';
static const String _keyDeviceName = 'relay_device_name';
static const String _keyRelayCount = 'relay_count';
static const String _keyAutoConnect = 'relay_auto_connect';
```

### Configuration Creation
```dart
RelayDeviceConfig createDeviceConfig({String? customDeviceId}) {
  return RelayDeviceConfig(
    deviceId: customDeviceId ?? 'usb-ttl-relay-${DateTime.now().millisecondsSinceEpoch}',
    deviceName: deviceName,
    relayCount: relayCount,
    baudRate: baudRate,
  );
}
```

### Validation
```dart
static bool isValidBaudRate(int baudRate) {
  return commonBaudRates.contains(baudRate);
}
```

## 🚀 **Usage Examples**

### Basic Configuration
```dart
final relayConfig = RelayConfigService.instance;
await relayConfig.initialize();

// Set baud rate for USB-TTL relay
await relayConfig.setBaudRate(9600);

// Set device name
await relayConfig.setDeviceName('Terminal Door Relay');

// Enable auto connect
await relayConfig.setAutoConnect(true);
```

### Get Current Settings
```dart
final settings = relayConfig.getAllSettings();
print('Current settings: $settings');
// Output: {baudRate: 9600, deviceName: USB-TTL Relay, relayCount: 4, autoConnect: true}
```

### Reset to Defaults
```dart
await relayConfig.resetToDefaults();
// Resets all settings to USB-TTL relay defaults
```

## ✅ **Benefits**

### 1. Hardware Compatibility
- **USB-TTL Relay Modules**: Optimized for 9600 baud rate
- **ESP32/Arduino**: Still supports 115200 via configuration
- **Flexible**: Can adjust for different hardware types

### 2. User Experience
- **Configurable**: Settings can be changed without code modification
- **Persistent**: Settings saved across app restarts
- **Descriptive**: Clear descriptions for each baud rate option

### 3. Development
- **Clean Code**: Centralized configuration management
- **Maintainable**: Easy to add new configuration options
- **Debuggable**: Clear logging of configuration changes

## 🔄 **Migration Path**

### Existing Installations
1. **Automatic**: Default baud rate changes to 9600 on next initialization
2. **Manual**: Users can adjust baud rate in settings if needed
3. **Backward Compatible**: 115200 still available for ESP32/Arduino setups

### New Installations
1. **Default**: Uses 9600 baud rate for USB-TTL compatibility
2. **Auto-detect**: Could be enhanced to detect device type
3. **User Choice**: Settings screen allows customization

## 🎯 **Next Steps**

### Immediate
- ✅ **Default baud rate**: Updated to 9600
- ✅ **Configuration service**: Implemented and integrated
- ✅ **Import conflicts**: Resolved with aliases

### Future Enhancements
- [ ] **Settings UI**: Add relay configuration screen
- [ ] **Auto-detection**: Detect device type and set appropriate baud rate
- [ ] **Device profiles**: Predefined configurations for common devices
- [ ] **Connection testing**: Test different baud rates automatically

## 📋 **Summary**

Successfully updated relay configuration system with:
- ✅ **USB-TTL Default**: Baud rate changed to 9600 for better compatibility
- ✅ **Configurable Settings**: RelayConfigService for runtime configuration
- ✅ **Import Fixes**: Resolved RelayAction enum conflicts
- ✅ **Persistent Storage**: Settings saved in SharedPreferences
- ✅ **Validation**: Proper validation for configuration values

The relay system now defaults to USB-TTL relay module standards while maintaining flexibility for other hardware configurations.

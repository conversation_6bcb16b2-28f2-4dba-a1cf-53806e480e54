/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server-related exceptions
class ServerException extends AppException {
  final int? statusCode;

  const ServerException(
    super.message, {
    super.code,
    this.statusCode,
  });

  @override
  String toString() => 'ServerException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});

  @override
  String toString() => 'NetworkException: $message';
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code});

  @override
  String toString() => 'AuthException: $message';
}

/// Validation-related exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException(
    super.message, {
    super.code,
    this.fieldErrors,
  });

  @override
  String toString() {
    String baseMessage = 'ValidationException: $message';
    if (fieldErrors != null && fieldErrors!.isNotEmpty) {
      String fieldErrorsString = fieldErrors!.entries
          .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
          .join('; ');
      baseMessage += ' (Field Errors: $fieldErrorsString)';
    }
    return baseMessage;
  }
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code});

  @override
  String toString() => 'CacheException: $message';
}

/// File-related exceptions
class FileException extends AppException {
  const FileException(super.message, {super.code});

  @override
  String toString() => 'FileException: $message';
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code});

  @override
  String toString() => 'PermissionException: $message';
}

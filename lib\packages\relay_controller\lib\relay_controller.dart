/// A Flutter library for controlling relays through various communication methods.
/// 
/// This library provides a unified interface for controlling relays using different
/// communication protocols including Bluetooth, HTTP, MQTT, and USB Serial.
/// 
/// Supports configurable device profiles for different relay controller types:
/// - ESP32 format: R0:1, R0:0, R1:TOGGLE, R2:500, ALL:1, ALL:0
/// - Arduino format: REL_0_ON, REL_0_OFF, REL_ALL_ON, etc.
/// - Simple format: 01, 00, 1T, 1P500, A1, A0
/// - Custom formats: Define your own command templates
library relay_controller;

export 'src/relay_controller_base.dart';
export 'src/bluetooth_controller.dart'; // Re-enabled with modern replacement
export 'src/modern_bluetooth_controller.dart';
export 'src/flutter_blue_classic_controller.dart';
export 'src/native_bluetooth_controller.dart';
export 'src/bluetooth_bridge_controller.dart';
export 'src/http_controller.dart';
export 'src/mqtt_controller.dart';
export 'src/usb_controller.dart';
export 'src/usb_ttl_relay_controller.dart';
export 'src/secure_http_controller.dart';
export 'src/enhanced_relay_manager.dart';
export 'src/exceptions.dart';
export 'src/security/device_auth.dart';

// Legacy support - ESP32 controller is now deprecated
// Use UsbTtlRelayController with DeviceProfile.esp32() instead
@Deprecated('Use UsbTtlRelayController with DeviceProfile.esp32() instead')
export 'src/esp32_relay_controller.dart';

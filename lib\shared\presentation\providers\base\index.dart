/// Base Providers Index - Export all base provider classes
///
/// This file exports all base provider classes that provide common functionality
/// for authentication, user management, theme management, and navigation.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/presentation/providers/base/index.dart';
///
/// // Extend base providers in your app
/// class MobileAuthProvider extends BaseAuthProvider {
///   // Mobile-specific authentication logic
/// }
///
/// class TerminalUserProvider extends BaseUserProvider {
///   // Terminal-specific user management logic
/// }
/// ```
library;

// ============================================================================
// BASE PROVIDER EXPORTS
// ============================================================================

/// Base authentication provider with login/logout functionality
export 'base_auth_provider.dart';

/// Base user management provider with CRUD operations and pagination
export 'base_user_provider.dart';

/// Base theme provider with theme mode and color scheme management
export 'base_theme_provider.dart';

/// Base navigation provider with route management and deep linking
export 'base_navigation_provider.dart';

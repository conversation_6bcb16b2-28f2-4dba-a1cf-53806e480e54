import 'dart:convert';
import 'dart:typed_data';
import 'package:bluetooth_classic/bluetooth_classic.dart';
import 'package:bluetooth_classic/models/device.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses modern Bluetooth Classic communication.
///
/// This controller uses the `bluetooth_classic` package which is actively
/// maintained and compatible with newer Android versions.
///
/// Example usage:
/// ```dart
/// final controller = ModernBluetoothController(
///   deviceAddress: '00:11:22:33:44:55',
/// );
///
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class ModernBluetoothController extends RelayController {
  /// The Bluetooth device address (MAC address).
  final String deviceAddress;

  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  /// Serial Port Profile UUID for Bluetooth Classic communication.
  final String serialUuid;

  static final BluetoothClassic _bluetoothClassic = BluetoothClassic();
  bool _isConnected = false;
  bool _isInitialized = false;

  /// Creates a new [ModernBluetoothController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceAddress] is the MAC address of the Bluetooth device.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  /// [serialUuid] is the UUID for serial communication (default: SPP UUID).
  ModernBluetoothController({
    required super.deviceId,
    required this.deviceAddress,
    super.deviceName = 'Modern Bluetooth Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.timeoutSeconds = 10,
    this.serialUuid = '00001101-0000-1000-8000-00805f9b34fb', // SPP UUID
  });

  /// Initializes Bluetooth permissions and adapter.
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize permissions
      await _bluetoothClassic.initPermissions();
      _isInitialized = true;
    } catch (e) {
      throw BluetoothRelayException('Failed to initialize Bluetooth', e);
    }
  }

  /// Connects to the Bluetooth device.
  /// 
  /// This method must be called before using [triggerOn] or [triggerOff].
  /// 
  /// Throws [BluetoothRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      await _initialize();

      // Note: bluetooth_classic doesn't have isSupported/isEnabled methods
      // We'll try to connect directly and handle errors

      // Connect to the device
      await _bluetoothClassic.connect(deviceAddress, serialUuid);
      _isConnected = true;
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to connect to Bluetooth device', e);
    }
  }

  /// Disconnects from the Bluetooth device.
  Future<void> disconnect() async {
    try {
      if (_isConnected) {
        await _bluetoothClassic.disconnect();
        _isConnected = false;
      }
    } catch (e) {
      throw BluetoothRelayException('Failed to disconnect from Bluetooth device', e);
    }
  }

  /// Sends a command to the Bluetooth device.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected) {
        throw const BluetoothRelayException('Not connected to Bluetooth device');
      }

      await _bluetoothClassic.write(command);
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets available Bluetooth devices.
  /// 
  /// Returns a list of paired (bonded) Bluetooth devices.
  static Future<List<Device>> getAvailableDevices() async {
    try {
      await _bluetoothClassic.initPermissions();
      return await _bluetoothClassic.getPairedDevices();
    } catch (e) {
      throw BluetoothRelayException('Failed to get available devices', e);
    }
  }

  /// Scans for nearby Bluetooth devices.
  /// 
  /// Returns a stream of discovered devices.
  static Stream<Device> scanForDevices() {
    return _bluetoothClassic.onDeviceDiscovered();
  }

  /// Starts scanning for Bluetooth devices.
  static Future<void> startScan() async {
    try {
      await _bluetoothClassic.initPermissions();
      await _bluetoothClassic.startScan();
    } catch (e) {
      throw BluetoothRelayException('Failed to start scan', e);
    }
  }

  /// Stops scanning for Bluetooth devices.
  static Future<void> stopScan() async {
    try {
      await _bluetoothClassic.stopScan();
    } catch (e) {
      throw BluetoothRelayException('Failed to stop scan', e);
    }
  }

  /// Checks if Bluetooth is supported on this device.
  /// Note: bluetooth_classic doesn't provide this method, so we assume it's supported.
  static Future<bool> isBluetoothSupported() async {
    return true; // Assume supported on Android
  }

  /// Checks if Bluetooth is enabled on the device.
  /// Note: bluetooth_classic doesn't provide this method directly.
  static Future<bool> isBluetoothEnabled() async {
    try {
      // Try to get paired devices as a way to check if Bluetooth is enabled
      await _bluetoothClassic.initPermissions();
      await _bluetoothClassic.getPairedDevices();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Requests to enable Bluetooth.
  /// Note: bluetooth_classic doesn't provide this method.
  static Future<void> requestEnableBluetooth() async {
    throw BluetoothRelayException('Enable Bluetooth manually in device settings');
  }

  /// Creates a bond (pair) with a Bluetooth device.
  /// Note: bluetooth_classic doesn't provide this method.
  static Future<void> bondDevice(String deviceAddress) async {
    throw BluetoothRelayException('Pair device manually in device settings');
  }

  /// Gets the current Bluetooth adapter state.
  /// Note: bluetooth_classic doesn't provide this method.
  static Future<String> getAdapterState() async {
    return 'unknown';
  }

  /// Stream of Bluetooth adapter state changes.
  /// Note: bluetooth_classic doesn't provide this method.
  static Stream<String> get adapterStateStream {
    return Stream.value('unknown');
  }

  /// Checks if currently scanning for devices.
  /// Note: bluetooth_classic doesn't provide this method.
  static Future<bool> isScanning() async {
    return false; // Cannot determine scanning state
  }

  /// Stream of scanning state changes.
  /// Note: bluetooth_classic doesn't provide this method.
  static Stream<bool> get scanningStateStream {
    return Stream.value(false);
  }
}

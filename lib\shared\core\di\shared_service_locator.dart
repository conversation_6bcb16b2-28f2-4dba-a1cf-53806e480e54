import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;

// Modular DI
import 'modules/core_module.dart';
import 'modules/network_module.dart';
import 'modules/auth_module.dart';
import 'modules/user_module.dart';
import 'modules/tenant_module.dart';
import 'modules/face_module.dart';

// Configuration
import '../config/app_config_factory.dart';
import '../config/base_app_config.dart';

/// Shared Service Locator for Multi-App Architecture
/// 
/// This class manages shared dependencies that are common across
/// both mobile and terminal applications. It provides a centralized
/// way to register and manage shared services.
/// 
/// Usage:
/// ```dart
/// // In app-specific service locators
/// await SharedServiceLocator.setupSharedDependencies();
/// ```
class SharedServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  static bool _isInitialized = false;

  /// Setup shared dependencies for multi-app architecture
  /// 
  /// This method registers all shared services that are common
  /// across mobile and terminal applications.
  static Future<void> setupSharedDependencies() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Shared dependencies already initialized');
      }
      return;
    }

    if (kDebugMode) {
      print('🚀 Setting up shared dependencies...');
    }

    // ============================================================================
    // REGISTER SHARED MODULES IN ORDER
    // ============================================================================

    // 0. Configuration - Must be first (provides app configuration)
    _registerConfiguration();

    // 1. Core Module - Must be first (provides basic services)
    registerCoreDependencies();

    // 2. Network Module - Depends on core
    registerNetworkDependencies();

    // 3. Auth Module - Shared authentication logic
    registerAuthDependencies();

    // 4. User Module - Shared user management
    registerUserDependencies();

    // 5. Tenant Module - Shared tenant management
    registerTenantDependencies();

    // 6. Face Module - Shared face detection logic
    registerFaceDependencies();

    _isInitialized = true;

    // Log successful setup
    _logSharedDependencyStatus();
  }

  /// Reset shared dependencies (useful for testing)
  static Future<void> resetSharedDependencies() async {
    if (kDebugMode) {
      print('🔄 Resetting shared dependencies...');
    }

    // Unregister modules in reverse order
    unregisterFaceDependencies();
    unregisterTenantDependencies();
    unregisterUserDependencies();
    unregisterAuthDependencies();
    unregisterNetworkDependencies();
    unregisterCoreDependencies();

    _isInitialized = false;

    if (kDebugMode) {
      print('✅ Shared dependencies reset complete');
    }
  }

  /// Check if shared dependencies are initialized
  static bool get isInitialized => _isInitialized;

  /// Get shared dependency status for debugging
  static Map<String, bool> getSharedDependencyStatus() {
    return {
      'initialized': _isInitialized,
      'core': areCoreDependenciesRegistered(),
      'network': areNetworkDependenciesRegistered(),
      'auth': areAuthDependenciesRegistered(),
      'user': areUserDependenciesRegistered(),
      'face': areFaceDependenciesRegistered(),
    };
  }

  /// Log shared dependency registration status for debugging
  static void _logSharedDependencyStatus() {
    if (kDebugMode) {
      print('🚀 Shared Service Locator Setup Complete');
      print('📊 Shared Dependency Status:');

      final status = getSharedDependencyStatus();
      status.forEach((key, value) {
        final icon = value ? '✅' : '❌';
        if (kDebugMode) {
          print('  ${key.toUpperCase()}: $icon');
        }
      });
    }
  }

  /// Clean up shared resources when app is disposed
  static Future<void> disposeSharedDependencies() async {
    if (kDebugMode) {
      print('🧹 Disposing shared dependencies...');
    }

    try {
      // Dispose HTTP client if registered
      if (_getIt.isRegistered<http.Client>()) {
        final httpClient = _getIt<http.Client>();
        httpClient.close();
      }

      // Reset GetIt instance
      await _getIt.reset();
      _isInitialized = false;

      if (kDebugMode) {
        print('✅ Shared dependencies disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error disposing shared dependencies: $e');
      }
      rethrow;
    }
  }

  /// Validate that all required shared dependencies are registered
  static bool validateSharedDependencies() {
    final status = getSharedDependencyStatus();
    final allRegistered = status.values.every((isRegistered) => isRegistered);

    if (kDebugMode) {
      if (allRegistered) {
        print('✅ All shared dependencies validated successfully');
      } else {
        print('❌ Some shared dependencies are missing:');
        status.forEach((key, value) {
          if (!value) {
            if (kDebugMode) {
              print('  - $key: MISSING');
            }
          }
        });
      }
    }

    return allRegistered;
  }

  /// Get GetIt instance for shared dependencies
  static GetIt get instance => _getIt;

  /// Register configuration dependencies
  static void _registerConfiguration() {
    // Note: Configuration should be initialized by the app before calling this
    // We register it here for easy access throughout the app
    if (AppConfigFactory.isInitialized) {
      _getIt.registerSingleton<BaseAppConfig>(AppConfigFactory.current);

      if (kDebugMode) {
        print('✅ Configuration registered: ${AppConfigFactory.currentAppType.name}');
      }
    } else {
      if (kDebugMode) {
        print('⚠️ Configuration not initialized, skipping registration');
      }
    }
  }
}

/// Shared dependency configuration
class SharedDependencyConfig {
  /// Environment-specific configuration
  static bool get enableDebugLogging => kDebugMode;
  
  /// Shared service timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration cacheTimeout = Duration(minutes: 5);
  
  /// Shared service retry configuration
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
}

/// Mixin for classes that need access to shared dependencies
mixin SharedDependencyMixin {
  /// Get shared GetIt instance
  GetIt get sharedDI => SharedServiceLocator.instance;
  
  /// Check if shared dependencies are ready
  bool get areSharedDependenciesReady => SharedServiceLocator.isInitialized;
  
  /// Validate shared dependencies before use
  void ensureSharedDependenciesReady() {
    if (!areSharedDependenciesReady) {
      throw StateError(
        'Shared dependencies not initialized. Call SharedServiceLocator.setupSharedDependencies() first.',
      );
    }
  }
}

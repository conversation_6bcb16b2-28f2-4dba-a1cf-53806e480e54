import 'package:flutter/foundation.dart';

/// Simple logger utility for the application
class Logger {
  // Private constructor to prevent instantiation
  Logger._();

  /// Log debug message
  static void d(String tag, String message) {
    if (kDebugMode) {
      print('🐛 [$tag] $message');
    }
  }

  /// Log info message
  static void i(String tag, String message) {
    if (kDebugMode) {
      print('ℹ️ [$tag] $message');
    }
  }

  /// Log warning message
  static void w(String tag, String message) {
    if (kDebugMode) {
      print('⚠️ [$tag] $message');
    }
  }

  /// Log error message
  static void e(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('❌ [$tag] $message');
      if (error != null) {
        print('Error: $error');
      }
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// Log verbose message
  static void v(String tag, String message) {
    if (kDebugMode) {
      print('📝 [$tag] $message');
    }
  }
}

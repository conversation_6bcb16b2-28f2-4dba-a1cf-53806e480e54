import 'dart:ui';

/// Comprehensive configuration for face recognition system
class FaceRecognitionConfig {
  // Detection settings
  final DetectionEngine detectionEngine;
  final int minFaceSize;
  final int maxFaceSize;
  final double minConfidence;
  final double minFaceQuality;
  final int maxFaces;
  final bool requireFrontalFace;
  
  // Recognition settings
  final RecognitionMode recognitionMode;
  final String? onlineEndpoint;
  final String? apiKey;
  final Duration recognitionTimeout;
  final Duration recognitionThrottle;
  
  // Performance settings
  final PerformanceProfile performanceProfile;
  final int targetFPS;
  final int processingThreads;
  final int memoryLimit; // MB
  final bool enableGPUAcceleration;
  
  // Platform-specific settings
  final PlatformType platformType;
  final DeviceOptimizations? deviceOptimizations;
  final bool batteryOptimization;
  final bool backgroundProcessing;
  
  // Monitoring settings
  final bool enablePerformanceMonitoring;
  final bool enableDebugLogs;
  final Duration metricsInterval;

  // Additional settings
  final bool cropFaces;
  final int cropSize;
  
  const FaceRecognitionConfig({
    // Detection defaults
    this.detectionEngine = DetectionEngine.ultraface,
    this.minFaceSize = 40,
    this.maxFaceSize = 0,
    this.minConfidence = 0.7,
    this.minFaceQuality = 0.5,
    this.maxFaces = 5,
    this.requireFrontalFace = false,
    
    // Recognition defaults
    this.recognitionMode = RecognitionMode.hybrid,
    this.onlineEndpoint,
    this.apiKey,
    this.recognitionTimeout = const Duration(seconds: 5),
    this.recognitionThrottle = const Duration(milliseconds: 500),
    
    // Performance defaults
    this.performanceProfile = PerformanceProfile.balanced,
    this.targetFPS = 30,
    this.processingThreads = 1,
    this.memoryLimit = 150,
    this.enableGPUAcceleration = true,
    
    // Platform defaults
    this.platformType = PlatformType.mobile,
    this.deviceOptimizations,
    this.batteryOptimization = true,
    this.backgroundProcessing = false,
    
    // Monitoring defaults
    this.enablePerformanceMonitoring = true,
    this.enableDebugLogs = false,
    this.metricsInterval = const Duration(seconds: 1),

    // Additional defaults
    this.cropFaces = true,
    this.cropSize = 112,
  });
  
  /// Create configuration optimized for terminal devices
  factory FaceRecognitionConfig.forTerminal({
    TerminalDeviceType deviceType = TerminalDeviceType.generic,
    PerformanceProfile performanceProfile = PerformanceProfile.maxPerformance,
    String? onlineEndpoint,
    String? apiKey,
  }) {
    final optimizations = _getTerminalOptimizations(deviceType);
    
    return FaceRecognitionConfig(
      // Detection optimized for terminals
      detectionEngine: DetectionEngine.ultraface,
      minFaceSize: 50,
      minConfidence: 0.6, // Lower for better detection
      minFaceQuality: 0.4, // Lower for embedded devices
      maxFaces: 3, // Limit for performance
      requireFrontalFace: true, // Terminals expect frontal faces
      
      // Recognition settings
      recognitionMode: RecognitionMode.hybrid,
      onlineEndpoint: onlineEndpoint,
      apiKey: apiKey,
      recognitionTimeout: const Duration(seconds: 3),
      recognitionThrottle: const Duration(milliseconds: 200),
      
      // High performance for terminals
      performanceProfile: performanceProfile,
      targetFPS: 45,
      processingThreads: 2,
      memoryLimit: 200,
      enableGPUAcceleration: true,
      
      // Terminal platform settings
      platformType: PlatformType.terminal,
      deviceOptimizations: optimizations,
      batteryOptimization: false, // Terminals are plugged in
      backgroundProcessing: true,
      
      // Enhanced monitoring for terminals
      enablePerformanceMonitoring: true,
      enableDebugLogs: true,
      metricsInterval: const Duration(milliseconds: 500),
    );
  }
  
  /// Create configuration optimized for mobile devices
  factory FaceRecognitionConfig.forMobile({
    MobileDeviceType deviceType = MobileDeviceType.android,
    PerformanceProfile performanceProfile = PerformanceProfile.balanced,
    String? onlineEndpoint,
    String? apiKey,
  }) {
    final optimizations = _getMobileOptimizations(deviceType);
    
    return FaceRecognitionConfig(
      // Detection optimized for mobile
      detectionEngine: DetectionEngine.mediapipe,
      minFaceSize: 30,
      minConfidence: 0.8, // Higher for accuracy
      minFaceQuality: 0.6,
      maxFaces: 2, // Limit for battery
      requireFrontalFace: false, // More flexible for mobile
      
      // Recognition settings
      recognitionMode: RecognitionMode.online, // Prefer cloud for mobile
      onlineEndpoint: onlineEndpoint,
      apiKey: apiKey,
      recognitionTimeout: const Duration(seconds: 10),
      recognitionThrottle: const Duration(seconds: 1),
      
      // Balanced performance for mobile
      performanceProfile: performanceProfile,
      targetFPS: 30,
      processingThreads: 1,
      memoryLimit: 100,
      enableGPUAcceleration: true,
      
      // Mobile platform settings
      platformType: PlatformType.mobile,
      deviceOptimizations: optimizations,
      batteryOptimization: true,
      backgroundProcessing: false,
      
      // Basic monitoring for mobile
      enablePerformanceMonitoring: false,
      enableDebugLogs: false,
      metricsInterval: const Duration(seconds: 5),
    );
  }
  
  /// Get terminal-specific optimizations
  static DeviceOptimizations _getTerminalOptimizations(TerminalDeviceType deviceType) {
    switch (deviceType) {
      case TerminalDeviceType.telpoF8:
        return TelpoF8Optimizations();
      case TerminalDeviceType.androidTerminal:
        return AndroidTerminalOptimizations();
      case TerminalDeviceType.generic:
        return GenericTerminalOptimizations();
    }
  }
  
  /// Get mobile-specific optimizations
  static DeviceOptimizations _getMobileOptimizations(MobileDeviceType deviceType) {
    switch (deviceType) {
      case MobileDeviceType.android:
        return AndroidMobileOptimizations();
      case MobileDeviceType.ios:
        return IOSMobileOptimizations();
    }
  }
  
  /// Create a copy with updated values
  FaceRecognitionConfig copyWith({
    DetectionEngine? detectionEngine,
    int? minFaceSize,
    int? maxFaceSize,
    double? minConfidence,
    double? minFaceQuality,
    int? maxFaces,
    bool? requireFrontalFace,
    RecognitionMode? recognitionMode,
    String? onlineEndpoint,
    String? apiKey,
    Duration? recognitionTimeout,
    Duration? recognitionThrottle,
    PerformanceProfile? performanceProfile,
    int? targetFPS,
    int? processingThreads,
    int? memoryLimit,
    bool? enableGPUAcceleration,
    PlatformType? platformType,
    DeviceOptimizations? deviceOptimizations,
    bool? batteryOptimization,
    bool? backgroundProcessing,
    bool? enablePerformanceMonitoring,
    bool? enableDebugLogs,
    Duration? metricsInterval,
    bool? cropFaces,
    int? cropSize,
  }) {
    return FaceRecognitionConfig(
      detectionEngine: detectionEngine ?? this.detectionEngine,
      minFaceSize: minFaceSize ?? this.minFaceSize,
      maxFaceSize: maxFaceSize ?? this.maxFaceSize,
      minConfidence: minConfidence ?? this.minConfidence,
      minFaceQuality: minFaceQuality ?? this.minFaceQuality,
      maxFaces: maxFaces ?? this.maxFaces,
      requireFrontalFace: requireFrontalFace ?? this.requireFrontalFace,
      recognitionMode: recognitionMode ?? this.recognitionMode,
      onlineEndpoint: onlineEndpoint ?? this.onlineEndpoint,
      apiKey: apiKey ?? this.apiKey,
      recognitionTimeout: recognitionTimeout ?? this.recognitionTimeout,
      recognitionThrottle: recognitionThrottle ?? this.recognitionThrottle,
      performanceProfile: performanceProfile ?? this.performanceProfile,
      targetFPS: targetFPS ?? this.targetFPS,
      processingThreads: processingThreads ?? this.processingThreads,
      memoryLimit: memoryLimit ?? this.memoryLimit,
      enableGPUAcceleration: enableGPUAcceleration ?? this.enableGPUAcceleration,
      platformType: platformType ?? this.platformType,
      deviceOptimizations: deviceOptimizations ?? this.deviceOptimizations,
      batteryOptimization: batteryOptimization ?? this.batteryOptimization,
      backgroundProcessing: backgroundProcessing ?? this.backgroundProcessing,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableDebugLogs: enableDebugLogs ?? this.enableDebugLogs,
      metricsInterval: metricsInterval ?? this.metricsInterval,
      cropFaces: cropFaces ?? this.cropFaces,
      cropSize: cropSize ?? this.cropSize,
    );
  }
}

/// Detection engine types
enum DetectionEngine {
  ultraface,
  mediapipe,
  mlKit,
}

/// Recognition modes
enum RecognitionMode {
  online,
  offline,
  hybrid,
}

/// Performance profiles
enum PerformanceProfile {
  maxPerformance,
  balanced,
  powerSaver,
}

/// Platform types
enum PlatformType {
  terminal,
  mobile,
}

/// Terminal device types
enum TerminalDeviceType {
  telpoF8,
  androidTerminal,
  generic,
}

/// Mobile device types
enum MobileDeviceType {
  android,
  ios,
}

/// Base class for device-specific optimizations
abstract class DeviceOptimizations {
  Size get preferredImageSize;
  int get preferredThreadCount;
  bool get useNNAPI;
  Map<String, dynamic> get customSettings;
}

/// Telpo F8 specific optimizations
class TelpoF8Optimizations extends DeviceOptimizations {
  @override
  Size get preferredImageSize => const Size(320, 240);
  
  @override
  int get preferredThreadCount => 2;
  
  @override
  bool get useNNAPI => true;
  
  @override
  Map<String, dynamic> get customSettings => {
    'camera_fps': 30,
    'processing_interval': 33, // ms
    'memory_pool_size': 50, // MB
    'enable_frame_skipping': true,
  };
}

/// Generic terminal optimizations
class GenericTerminalOptimizations extends DeviceOptimizations {
  @override
  Size get preferredImageSize => const Size(320, 240);
  
  @override
  int get preferredThreadCount => 1;
  
  @override
  bool get useNNAPI => false;
  
  @override
  Map<String, dynamic> get customSettings => {
    'camera_fps': 24,
    'processing_interval': 50, // ms
    'memory_pool_size': 30, // MB
    'enable_frame_skipping': true,
  };
}

/// Android terminal optimizations
class AndroidTerminalOptimizations extends DeviceOptimizations {
  @override
  Size get preferredImageSize => const Size(480, 360);
  
  @override
  int get preferredThreadCount => 2;
  
  @override
  bool get useNNAPI => true;
  
  @override
  Map<String, dynamic> get customSettings => {
    'camera_fps': 30,
    'processing_interval': 33, // ms
    'memory_pool_size': 80, // MB
    'enable_frame_skipping': false,
  };
}

/// Android mobile optimizations
class AndroidMobileOptimizations extends DeviceOptimizations {
  @override
  Size get preferredImageSize => const Size(640, 480);
  
  @override
  int get preferredThreadCount => 1;
  
  @override
  bool get useNNAPI => true;
  
  @override
  Map<String, dynamic> get customSettings => {
    'camera_fps': 30,
    'processing_interval': 100, // ms
    'memory_pool_size': 60, // MB
    'enable_frame_skipping': true,
  };
}

/// iOS mobile optimizations
class IOSMobileOptimizations extends DeviceOptimizations {
  @override
  Size get preferredImageSize => const Size(640, 480);
  
  @override
  int get preferredThreadCount => 1;
  
  @override
  bool get useNNAPI => false; // Use Core ML instead
  
  @override
  Map<String, dynamic> get customSettings => {
    'camera_fps': 30,
    'processing_interval': 100, // ms
    'memory_pool_size': 60, // MB
    'enable_frame_skipping': true,
    'use_core_ml': true,
  };
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/face_detection_provider.dart';
import '../../../../packages/face_recognition/src/config/face_detection_config.dart';

/// Configuration screen for face recognition side effects and device management
class FaceRecognitionConfigScreen extends StatefulWidget {
  const FaceRecognitionConfigScreen({super.key});

  @override
  State<FaceRecognitionConfigScreen> createState() => _FaceRecognitionConfigScreenState();
}

class _FaceRecognitionConfigScreenState extends State<FaceRecognitionConfigScreen> 
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Recognition Configuration'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'Side Effects'),
            Tab(icon: Icon(Icons.devices), text: 'Devices'),
            Tab(icon: Icon(Icons.security), text: 'Access Control'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSideEffectsTab(),
          _buildDevicesTab(),
          _buildAccessControlTab(),
        ],
      ),
    );
  }

  Widget _buildSideEffectsTab() {
    return Consumer<FaceRecognitionSideEffectsProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main toggle
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Side Effects Configuration',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Configure what happens after face recognition results are received from server',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Enable Side Effects'),
                        subtitle: const Text('Master switch for all side effects'),
                        value: provider.isEnabled,
                        onChanged: provider.updateSideEffectsEnabled,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Relay Control
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Relay Control',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: const Text('Trigger Relay on Access Granted'),
                        subtitle: const Text('Automatically trigger relay when server grants access'),
                        value: provider.triggerRelayOnAccess,
                        onChanged: provider.isEnabled ? provider.updateTriggerRelayOnAccess : null,
                      ),
                      const SizedBox(height: 8),
                      ListTile(
                        title: const Text('Default Relay Device'),
                        subtitle: Text(provider.defaultRelayDeviceId),
                        trailing: const Icon(Icons.edit),
                        onTap: provider.isEnabled ? () => _editRelayDevice(provider) : null,
                      ),
                      ListTile(
                        title: const Text('Trigger Duration'),
                        subtitle: Text('${provider.relayTriggerDuration} seconds'),
                        trailing: const Icon(Icons.timer),
                        onTap: provider.isEnabled ? () => _editTriggerDuration(provider) : null,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Notifications
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notifications',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: const Text('Show Success Notifications'),
                        subtitle: const Text('Display notification when access is granted'),
                        value: provider.showSuccessNotification,
                        onChanged: provider.isEnabled 
                            ? (value) => provider.updateNotificationSettings(showSuccess: value)
                            : null,
                      ),
                      SwitchListTile(
                        title: const Text('Show Denied Notifications'),
                        subtitle: const Text('Display notification when access is denied'),
                        value: provider.showDeniedNotification,
                        onChanged: provider.isEnabled 
                            ? (value) => provider.updateNotificationSettings(showDenied: value)
                            : null,
                      ),
                      ListTile(
                        title: const Text('Notification Duration'),
                        subtitle: Text('${provider.notificationDuration} seconds'),
                        trailing: const Icon(Icons.schedule),
                        onTap: provider.isEnabled ? () => _editNotificationDuration(provider) : null,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Logging & Storage
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Logging & Storage',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: const Text('Log All Attempts'),
                        subtitle: const Text('Keep logs of all face recognition attempts'),
                        value: provider.logAllAttempts,
                        onChanged: provider.isEnabled 
                            ? (value) => provider.updateSideEffectsEnabled(value)
                            : null,
                      ),
                      SwitchListTile(
                        title: const Text('Save Recognition Images'),
                        subtitle: const Text('Save face images for audit purposes'),
                        value: provider.saveRecognitionImages,
                        onChanged: provider.isEnabled 
                            ? (value) => provider.updateSideEffectsEnabled(value)
                            : null,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDevicesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Device Auto-Registration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  const Text('Device auto-registration will be implemented when services are ready'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Feature coming soon!')),
                      );
                    },
                    child: const Text('Configure Devices'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevicesTabOriginal() {
    return Consumer<DeviceAutoRegistrationProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Auto Registration
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Auto Device Registration',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Automatically register relay devices when terminal connects to server',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Enable Auto Registration'),
                        subtitle: const Text('Register relay devices automatically'),
                        value: provider.autoRegisterEnabled,
                        onChanged: (value) => provider.updateAutoRegistrationSettings(enabled: value),
                      ),
                      ListTile(
                        title: const Text('Default Relay Count'),
                        subtitle: Text('${provider.defaultRelayCount} relays'),
                        trailing: const Icon(Icons.edit),
                        onTap: () => _editRelayCount(provider),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Terminal Info
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Terminal Device',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ListTile(
                        title: const Text('Terminal ID'),
                        subtitle: Text(provider.terminalDeviceId ?? 'Not registered'),
                        leading: Icon(
                          provider.terminalDeviceId != null ? Icons.check_circle : Icons.error,
                          color: provider.terminalDeviceId != null ? Colors.green : Colors.red,
                        ),
                      ),
                      if (provider.isRegistering)
                        const LinearProgressIndicator(),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Registered Devices
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Registered Relay Devices',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Spacer(),
                          Text(
                            '${provider.registeredRelayDevices.length} devices',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (provider.registeredRelayDevices.isEmpty)
                        const ListTile(
                          leading: Icon(Icons.info),
                          title: Text('No relay devices registered'),
                          subtitle: Text('Enable auto registration or add devices manually'),
                        )
                      else
                        ...provider.registeredRelayDevices.map((deviceId) {
                          final deviceInfo = provider.getRelayDeviceInfo(deviceId);
                          return ListTile(
                            leading: const Icon(Icons.electrical_services),
                            title: Text(deviceId),
                            subtitle: Text(deviceInfo?['device_name'] ?? 'Unknown'),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () => _confirmUnregisterDevice(provider, deviceId),
                            ),
                          );
                        }).toList(),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: provider.terminalDeviceId != null && !provider.isRegistering
                                  ? () => _triggerAutoRegistration(provider)
                                  : null,
                              icon: const Icon(Icons.refresh),
                              label: const Text('Re-register All'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _addManualDevice(provider),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Manual'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccessControlTab() {
    return Consumer<FaceRecognitionSideEffectsProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Access Control Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Access Control Settings',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Require Server Approval'),
                        subtitle: const Text('Always require server to approve access'),
                        value: provider.requireServerApproval,
                        onChanged: (value) => provider.updateAccessControlSettings(
                          requireServerApproval: value,
                        ),
                      ),
                      ListTile(
                        title: const Text('Minimum Confidence Threshold'),
                        subtitle: Text('${(provider.minimumConfidenceThreshold * 100).toInt()}%'),
                        trailing: const Icon(Icons.tune),
                        onTap: () => _editConfidenceThreshold(provider),
                      ),
                      ListTile(
                        title: const Text('Allowed Access Levels'),
                        subtitle: Text(provider.allowedAccessLevels.join(', ')),
                        trailing: const Icon(Icons.edit),
                        onTap: () => _editAccessLevels(provider),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Configuration Summary
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Configuration Summary',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...provider.getConfigurationSummary().entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  entry.key.replaceAll('_', ' ').toUpperCase(),
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                              Text(
                                entry.value.toString(),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Reset Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _confirmResetToDefaults(provider),
                  icon: const Icon(Icons.restore),
                  label: const Text('Reset to Defaults'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper methods for editing values
  void _editRelayDevice(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement relay device selection dialog
  }

  void _editTriggerDuration(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement duration picker dialog
  }

  void _editNotificationDuration(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement duration picker dialog
  }

  void _editRelayCount(DeviceAutoRegistrationProvider provider) {
    // TODO: Implement relay count picker dialog
  }

  void _editConfidenceThreshold(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement confidence threshold slider dialog
  }

  void _editAccessLevels(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement access levels selection dialog
  }

  void _triggerAutoRegistration(DeviceAutoRegistrationProvider provider) {
    // TODO: Implement auto registration trigger
  }

  void _addManualDevice(DeviceAutoRegistrationProvider provider) {
    // TODO: Implement manual device addition dialog
  }

  void _confirmUnregisterDevice(DeviceAutoRegistrationProvider provider, String deviceId) {
    // TODO: Implement unregister confirmation dialog
  }

  void _confirmResetToDefaults(FaceRecognitionSideEffectsProvider provider) {
    // TODO: Implement reset confirmation dialog
  }
}

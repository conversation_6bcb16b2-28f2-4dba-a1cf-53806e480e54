import 'dart:async';
import 'package:flutter/foundation.dart';

import 'models/secure_message.dart';
import 'models/device_registration.dart';
import 'message_builder.dart';
import 'crypto_utils.dart';
import 'transport/transport_interface.dart';
import 'exceptions.dart';

/// Main secure communication class for device-server communication
/// 
/// This class provides a unified interface for secure communication that is
/// not tied to any specific data type or use case. It handles:
/// - Device registration and authentication
/// - Message signing and verification
/// - Transport layer abstraction
/// - Credential management
/// 
/// Example usage:
/// ```dart
/// final comm = SecureComm(
///   deviceId: 'terminal-001',
///   deviceType: 'face_terminal',
///   transport: HttpTransport('https://api.example.com'),
/// );
/// 
/// // Register device
/// await comm.registerDevice();
/// 
/// // Send face authentication
/// await comm.sendMessage(
///   type: 'face_auth',
///   payload: {'face_image': base64Image},
/// );
/// 
/// // Send relay control
/// await comm.sendMessage(
///   type: 'relay_control', 
///   payload: {'action': 'unlock'},
/// );
/// ```
class SecureComm {
  /// Device identifier
  final String deviceId;

  /// Device type (e.g., 'face_terminal', 'relay_controller', 'mobile_app')
  final String deviceType;

  /// Human-readable device name
  final String deviceName;

  /// Transport layer for communication
  final TransportInterface transport;

  /// Device capabilities
  final List<String> capabilities;

  /// Additional device metadata
  final Map<String, dynamic> metadata;

  /// Hardware hash for device fingerprinting
  final String? hardwareHash;

  /// Application version
  final String? appVersion;

  /// Whether to use plain text communication (no encryption/signing)
  final bool usePlainText;

  // Internal state
  DeviceCredentials? _credentials;
  MessageBuilder? _messageBuilder;
  Timer? _heartbeatTimer;

  /// Whether device is currently registered and authenticated
  bool get isAuthenticated => _credentials != null && !_credentials!.isExpired;

  /// Current device credentials
  DeviceCredentials? get credentials => _credentials;

  /// Message builder instance
  MessageBuilder? get messageBuilder => _messageBuilder;

  /// Device scopes/permissions
  List<String> get scopes => _credentials?.scopes ?? [];

  /// Creates a new SecureComm instance
  SecureComm({
    required this.deviceId,
    required this.deviceType,
    required this.transport,
    this.deviceName = '',
    this.capabilities = const [],
    this.metadata = const {},
    this.hardwareHash,
    this.appVersion,
    this.usePlainText = false,
  }) {
    // Validate device ID
    if (!CryptoUtils.isValidDeviceId(deviceId)) {
      throw ConfigurationException('Invalid device ID format: $deviceId');
    }
  }

  /// Register device with server and obtain credentials
  Future<void> registerDevice({
    Map<String, dynamic>? extraMetadata,
  }) async {
    if (usePlainText) {
      // In plain text mode, skip actual registration and create mock credentials
      _credentials = DeviceCredentials(
        deviceId: deviceId,
        accessToken: 'plain_text_token',
        secretKey: 'plain_text_secret',
        expiresAt: DateTime.now().add(Duration(days: 365)), // Long expiry for plain text
        scopes: capabilities,
        endpoints: {},
      );

      // No message builder needed for plain text mode
      _messageBuilder = null;
      return;
    }

    try {
      final request = DeviceRegistrationRequest(
        deviceId: deviceId,
        deviceType: deviceType,
        deviceName: deviceName.isNotEmpty ? deviceName : 'Device $deviceId',
        hardwareHash: hardwareHash ?? CryptoUtils.generateHardwareHash(),
        appVersion: appVersion,
        capabilities: capabilities,
        metadata: {...metadata, if (extraMetadata != null) ...extraMetadata},
      );

      final response = await transport.registerDevice(request);

      _credentials = DeviceCredentials.fromRegistration(
        deviceId: deviceId,
        response: response,
      );

      _messageBuilder = MessageBuilder(
        deviceId: deviceId,
        secretKey: _credentials!.secretKey,
      );

      // Start heartbeat if supported
      _startHeartbeat();

    } catch (e) {
      throw DeviceRegistrationException('Failed to register device', e);
    }
  }

  /// Refresh access token if needed
  Future<void> refreshTokenIfNeeded() async {
    if (_credentials == null) {
      throw CredentialsException('Device not registered');
    }

    if (!_credentials!.isExpired) {
      return; // Token is still valid
    }

    if (_credentials!.refreshToken == null) {
      throw CredentialsException('No refresh token available');
    }

    try {
      final newToken = await transport.refreshToken(_credentials!.refreshToken!);
      
      _credentials = _credentials!.withNewToken(
        accessToken: newToken['access_token'] as String,
        expiresIn: newToken['expires_in'] as int,
      );

    } catch (e) {
      throw AuthenticationException('Failed to refresh token', e);
    }
  }

  /// Send a secure message to server
  Future<SecureResponse> sendMessage({
    required String type,
    required Map<String, dynamic> payload,
    String? messageId,
    int? priority,
    bool? encrypted,
  }) async {
    if (usePlainText) {
      // Plain text mode - send message without encryption/signing
      return await _sendPlainTextMessage(
        type: type,
        payload: payload,
        messageId: messageId,
        priority: priority,
      );
    }

    await _ensureAuthenticated();

    if (_messageBuilder == null) {
      throw CredentialsException('Message builder not initialized');
    }

    try {
      // Build and sign message
      final message = _messageBuilder!.buildMessage(
        type: type,
        payload: payload,
        messageId: messageId,
        priority: priority,
        encrypted: encrypted,
      );

      // Send via transport
      final response = await transport.sendMessage(
        message: message,
        accessToken: _credentials!.accessToken,
      );

      return response;

    } catch (e) {
      throw TransportException('Failed to send message', e);
    }
  }

  /// Send face authentication message
  Future<SecureResponse> sendFaceAuth({
    required String faceImageBase64,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureAuthenticated();

    final message = _messageBuilder!.buildFaceAuthMessage(
      faceImageBase64: faceImageBase64,
      userId: userId,
      metadata: metadata,
    );

    return await transport.sendMessage(
      message: message,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Send relay control message
  Future<SecureResponse> sendRelayControl({
    required String action,
    String? relayId,
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureAuthenticated();

    final message = _messageBuilder!.buildRelayControlMessage(
      action: action,
      relayId: relayId,
      metadata: metadata,
    );

    return await transport.sendMessage(
      message: message,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Send status request
  Future<SecureResponse> sendStatusRequest({
    String? component,
    Map<String, dynamic>? filters,
  }) async {
    await _ensureAuthenticated();

    final message = _messageBuilder!.buildStatusMessage(
      component: component,
      filters: filters,
    );

    return await transport.sendMessage(
      message: message,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Send log message
  Future<SecureResponse> sendLog({
    required String level,
    required String message,
    String? category,
    Map<String, dynamic>? context,
  }) async {
    await _ensureAuthenticated();

    final logMessage = _messageBuilder!.buildLogMessage(
      level: level,
      message: message,
      category: category,
      context: context,
    );

    return await transport.sendMessage(
      message: logMessage,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Send image upload
  Future<SecureResponse> sendImageUpload({
    required String imageBase64,
    required String imageType,
    String? filename,
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureAuthenticated();

    final message = _messageBuilder!.buildImageUploadMessage(
      imageBase64: imageBase64,
      imageType: imageType,
      filename: filename,
      metadata: metadata,
    );

    return await transport.sendMessage(
      message: message,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Send heartbeat/ping
  Future<SecureResponse> sendHeartbeat({
    Map<String, dynamic>? systemInfo,
  }) async {
    await _ensureAuthenticated();

    final message = _messageBuilder!.buildHeartbeatMessage(
      systemInfo: systemInfo,
    );

    return await transport.sendMessage(
      message: message,
      accessToken: _credentials!.accessToken,
    );
  }

  /// Verify a received message
  bool verifyMessage(SecureMessage message) {
    if (_messageBuilder == null) return false;
    return _messageBuilder!.verifyMessage(message);
  }

  /// Check if device has specific scope/permission
  bool hasScope(String scope) {
    return _credentials?.hasScope(scope) ?? false;
  }

  /// Get endpoint URL for specific operation
  String? getEndpoint(String operation) {
    return _credentials?.getEndpoint(operation);
  }

  /// Revoke device credentials
  Future<void> revokeCredentials() async {
    if (_credentials != null) {
      try {
        await transport.revokeCredentials(_credentials!.accessToken);
      } catch (e) {
        // Continue with local cleanup even if server revocation fails
      }
    }

    _credentials = null;
    _messageBuilder = null;
    _stopHeartbeat();
  }

  /// Dispose resources
  Future<void> dispose() async {
    _stopHeartbeat();
    await transport.dispose();
  }

  /// Send plain text message without encryption/signing
  Future<SecureResponse> _sendPlainTextMessage({
    required String type,
    required Map<String, dynamic> payload,
    String? messageId,
    int? priority,
  }) async {
    try {
      // Create simple message structure for plain text mode
      final plainMessage = {
        'type': type,
        'payload': payload,
        'message_id': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        'device_id': deviceId,
        'device_type': deviceType,
        'timestamp': DateTime.now().toIso8601String(),
        if (priority != null) 'priority': priority,
      };

      // Send via transport without authentication
      final response = await transport.sendPlainTextMessage(plainMessage);
      return response;

    } catch (e) {
      throw TransportException('Failed to send plain text message', e);
    }
  }

  /// Ensure device is authenticated
  Future<void> _ensureAuthenticated() async {
    if (_credentials == null) {
      throw CredentialsException('Device not registered. Call registerDevice() first.');
    }

    if (_credentials!.isExpired) {
      await refreshTokenIfNeeded();
    }
  }

  /// Start periodic heartbeat
  void _startHeartbeat() {
    if (!hasScope('heartbeat')) return;

    _heartbeatTimer = Timer.periodic(Duration(minutes: 5), (timer) async {
      try {
        await sendHeartbeat();
      } catch (e) {
        // Heartbeat failure is not critical, just log it
        if (kDebugMode) {
          print('Heartbeat failed: $e');
        }
      }
    });
  }

  /// Stop heartbeat timer
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }
}

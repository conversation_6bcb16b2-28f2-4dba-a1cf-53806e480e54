import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:camera/camera.dart';
import '../../../../../shared/providers/face_capture_provider.dart';
import '../../../../../shared/providers/face_detection_provider.dart';
import '../../../../../shared/widgets/camera_preview_widget.dart';
import '../../../../../shared/core/constants/face_capture_constants.dart';
import '../../../../../shared/core/constants/face_capture_ui_config.dart';
import '../../../../../shared/widgets/face_capture_guide.dart' as guide;
import '../../../../../shared/widgets/captured_image_preview_modal.dart';
import '../../../../../shared/utils/performance_monitor.dart' as perf;
import '../../../../../shared/models/snapdragon_optimized_config.dart';
import '../../../../../shared/models/face_capture_result.dart';

/// Màn hình đăng ký dữ liệu khuôn mặt
class UserFaceRegisterScreen extends StatefulWidget {
  final Function(dynamic)? onExit;

  const UserFaceRegisterScreen({
    super.key,
    this.onExit,
  });

  @override
  State<UserFaceRegisterScreen> createState() => _UserFaceRegisterScreenState();
}

class _UserFaceRegisterScreenState extends State<UserFaceRegisterScreen>
    with TickerProviderStateMixin {
  Face? _bestFace;
  String _captureStatus = FaceCaptureConstants.defaultStatusMessage;
  bool _isCapturing = false;
  int _detectedFacesCount = 0;
  double _bestFaceQuality = 0.0;

  // Auto capture state
  final Map<FaceDirection, String?> _capturedImages = {};
  FaceDirection? _currentDirection;
  DateTime? _lastCaptureTime;
  static const Duration _captureDelay = FaceCaptureConstants.autoCaptureDelay;

  // Success message state
  String? _successMessage;
  Timer? _successMessageTimer;

  // Initialization state
  bool _isInitializing = true;
  String _initializationStep = 'Starting...';
  bool _hasInitializationError = false;
  String _initializationError = '';
  List<String> _failedSteps = [];

  // Animation controllers for smooth disposal
  late AnimationController _exitAnimationController;
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;



  // Exit state management
  bool _isExiting = false;
  bool _animationsInitialized = false;

  @override
  void initState() {
    super.initState();

    // Initialize animations for smooth disposal
    _initializeAnimations();

    // Set full-screen mode
    _setFullScreenMode();



    // Providers are now managed by FaceProvidersWrapper
    // Initialize components step by step to reduce lag
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeStepByStep();
    });
  }

  /// Initialize animation controllers for smooth disposal
  void _initializeAnimations() {
    // Main exit animation controller
    _exitAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200), // Faster exit
      vsync: this,
    );

    // Fade animation controller for UI elements
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    // Fade animation for overall opacity
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    // Scale animation for smooth shrinking effect
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _exitAnimationController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for smooth exit
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, 0.1),
    ).animate(CurvedAnimation(
      parent: _exitAnimationController,
      curve: Curves.easeInOut,
    ));



    _animationsInitialized = true;
    debugPrint('✨ Exit animations initialized');
  }

  /// Set full-screen immersive mode
  void _setFullScreenMode() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // Hide all system UI
    );

    // Set preferred orientations to portrait only
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  /// Restore normal system UI mode
  void _restoreSystemUI() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values, // Show all system UI
    );

    // Reset preferred orientations
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Initialize components step by step to reduce lag and improve smoothness
  Future<void> _initializeStepByStep() async {
    debugPrint('🚀 Starting step-by-step initialization for smooth experience...');

    // Early exit if already disposing
    if (_isExiting || !mounted) {
      debugPrint('🚫 Skipping initialization - widget is exiting or disposed');
      return;
    }

    int retryCount = 0;
    const maxRetries = 3;
    _failedSteps.clear();

    while (retryCount < maxRetries && !_isExiting && mounted) {
      try {
        // Step 1: Wait for camera to be ready first (most important for smooth preview)
        if (mounted && !_isExiting) {
          setState(() {
            _initializationStep = retryCount > 0
                ? 'Retrying camera initialization... (${retryCount + 1}/$maxRetries)'
                : 'Initializing camera...';
            _hasInitializationError = false;
          });
        }

        debugPrint('📷 Step 1: Waiting for camera to be ready... (attempt ${retryCount + 1})');
        try {
          await _waitForCameraReadyWithRetry();
        } catch (e) {
          _failedSteps.add('Camera Initialization: $e');
          throw Exception('Camera initialization failed: $e');
        }

        // Step 2: Extended delay to let camera preview fully stabilize
        if (mounted) {
          setState(() {
            _initializationStep = 'Stabilizing camera preview...';
          });
        }
        await Future.delayed(const Duration(milliseconds: 1000)); // Increased delay
        debugPrint('✅ Camera preview stabilized');

        // Step 3: Initialize performance monitoring (lightweight)
        if (mounted) {
          setState(() {
            _initializationStep = 'Setting up performance monitoring...';
          });
        }
        debugPrint('📊 Step 3: Initializing performance monitoring...');
        try {
          await _initializePerformanceMonitoringWithRetry();
        } catch (e) {
          _failedSteps.add('Performance Monitoring: $e');
          debugPrint('⚠️ Performance monitoring failed, continuing: $e');
          // Non-critical, continue
        }
        await Future.delayed(const Duration(milliseconds: 500));

        // Step 4: Enable face detection (more intensive)
        if (mounted) {
          setState(() {
            _initializationStep = 'Enabling face detection...';
          });
        }
        debugPrint('🎯 Step 4: Enabling face detection...');
        try {
          await _enableFaceDetectionWithRetry();
        } catch (e) {
          _failedSteps.add('Face Detection: $e');
          throw Exception('Face detection failed: $e');
        }
        await Future.delayed(const Duration(milliseconds: 800)); // Longer delay for face detection

        // Step 5: Apply Snapdragon optimizations (only if mounted)
        if (mounted) {
          setState(() {
            _initializationStep = 'Applying optimizations...';
          });
          debugPrint('⚡ Step 5: Applying Snapdragon optimizations...');
          try {
            await _applySnapdragonOptimizationsWithRetry();
          } catch (e) {
            _failedSteps.add('Snapdragon Optimizations: $e');
            debugPrint('⚠️ Optimizations failed, continuing: $e');
            // Non-critical, continue
          }
        }

        // Step 6: Final verification
        if (mounted) {
          setState(() {
            _initializationStep = 'Verifying system readiness...';
          });
          try {
            await _verifySystemReadiness();
          } catch (e) {
            _failedSteps.add('System Verification: $e');
            debugPrint('⚠️ Verification failed, continuing: $e');
            // Non-critical, continue
          }
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // Initialization complete
        if (mounted) {
          setState(() {
            _isInitializing = false;
            _initializationStep = 'Ready!';
            _hasInitializationError = false;
          });
        }

        debugPrint('✅ All initialization steps completed successfully!');
        if (_failedSteps.isNotEmpty) {
          debugPrint('⚠️ Some non-critical steps failed: ${_failedSteps.join(', ')}');
        }
        return; // Success, exit retry loop

      } catch (e) {
        retryCount++;
        debugPrint('❌ Initialization attempt $retryCount failed: $e');

        if (retryCount < maxRetries) {
          // Wait before retry
          await Future.delayed(Duration(milliseconds: 1000 * retryCount));
          debugPrint('🔄 Retrying initialization...');
        } else {
          // All retries failed, show error details
          debugPrint('❌ All initialization attempts failed');
          await _showInitializationError();
          break;
        }
      }
    }
  }

  /// Wait for camera to be ready with retry mechanism
  Future<void> _waitForCameraReadyWithRetry() async {
    final cameraProvider = context.read<FaceCaptureProvider>();

    // Wait up to 7 seconds for camera to be ready (reduced from 10s)
    for (int i = 0; i < 70; i++) {
      if (!mounted) throw Exception('Widget unmounted during camera initialization');

      if (cameraProvider.isCameraReady) {
        debugPrint('✅ Camera is ready after ${i * 100}ms');
        return;
      }
      await Future.delayed(const Duration(milliseconds: 100));
    }

    throw Exception('Camera not ready after 7 seconds');
  }

  /// Wait for camera to be ready before proceeding (legacy method)
  Future<void> _waitForCameraReady() async {
    try {
      await _waitForCameraReadyWithRetry();
    } catch (e) {
      debugPrint('⚠️ Camera initialization failed: $e');
    }
  }

  /// Initialize performance monitoring with retry mechanism
  Future<void> _initializePerformanceMonitoringWithRetry() async {
    if (!mounted) throw Exception('Widget unmounted during performance monitoring initialization');

    final faceDetectionProvider = context.read<FaceDetectionProvider>();

    // Enable performance monitoring based on constant
    faceDetectionProvider.setPerformanceMonitoring(FaceCaptureConstants.showPerformanceProfiling);

    // Start the performance monitor explicitly
    if (FaceCaptureConstants.showPerformanceProfiling) {
      final monitor = faceDetectionProvider.getPerformanceMonitor();
      monitor.startMonitoring();
      debugPrint('📊 Performance monitoring started');

      // Print performance summary every 10 seconds in debug mode
      Timer.periodic(const Duration(seconds: 10), (timer) {
        if (mounted) {
          faceDetectionProvider.printPerformanceSummary();
        } else {
          timer.cancel();
        }
      });
    }
  }

  /// Initialize performance monitoring for Snapdragon 8 Gen 2 (legacy method)
  Future<void> _initializePerformanceMonitoring() async {
    try {
      await _initializePerformanceMonitoringWithRetry();
    } catch (e) {
      debugPrint('❌ Failed to initialize performance monitoring: $e');
    }
  }

  /// Enable face detection with retry mechanism
  Future<void> _enableFaceDetectionWithRetry() async {
    if (!mounted) throw Exception('Widget unmounted during face detection initialization');

    // Get provider before async gap
    final faceDetectionProvider = context.read<FaceDetectionProvider>();

    // Ensure face detection is active
    _ensureFaceDetectionActive();

    // Wait for face detection to be properly initialized
    await Future.delayed(const Duration(milliseconds: 300));

    // Verify face detection is working (check mounted again)
    if (!mounted) throw Exception('Widget unmounted during face detection verification');

    if (faceDetectionProvider.isDisposed) {
      throw Exception('Face detection provider is disposed');
    }

    debugPrint('✅ Face detection enabled successfully');
  }

  /// Apply Snapdragon optimizations with retry mechanism
  Future<void> _applySnapdragonOptimizationsWithRetry() async {
    if (!mounted) throw Exception('Widget unmounted during optimization');

    final faceDetectionProvider = context.read<FaceDetectionProvider>();

    // Apply optimizations with a slight delay to avoid blocking UI
    await Future.delayed(const Duration(milliseconds: 100));

    if (!mounted) throw Exception('Widget unmounted before applying optimizations');

    await faceDetectionProvider.applyMobileOptimization(
      performanceLevel: PerformanceLevel.balanced,
    );

    debugPrint('⚡ Mobile optimizations applied');
  }

  /// Verify system readiness
  Future<void> _verifySystemReadiness() async {
    if (!mounted) throw Exception('Widget unmounted during verification');

    final cameraProvider = context.read<FaceCaptureProvider>();
    final faceDetectionProvider = context.read<FaceDetectionProvider>();

    // Check camera is ready
    if (!cameraProvider.isCameraReady) {
      throw Exception('Camera is not ready');
    }

    // Check face detection is not disposed
    if (faceDetectionProvider.isDisposed) {
      throw Exception('Face detection provider is disposed');
    }

    debugPrint('✅ System verification passed');
  }

  /// Fallback initialization when all retries fail
  Future<void> _fallbackInitialization() async {
    try {
      debugPrint('🔄 Starting fallback initialization...');

      if (mounted) {
        setState(() {
          _initializationStep = 'Starting in safe mode...';
        });
      }

      // Basic face detection without optimizations
      _ensureFaceDetectionActive();
      await Future.delayed(const Duration(milliseconds: 1000));

      if (mounted) {
        setState(() {
          _isInitializing = false;
          _initializationStep = 'Ready (safe mode)';
        });
      }

      debugPrint('✅ Fallback initialization completed');
    } catch (e) {
      debugPrint('❌ Even fallback initialization failed: $e');

      if (mounted) {
        setState(() {
          _isInitializing = false;
          _initializationStep = 'Ready (basic mode)';
        });
      }
    }
  }

  /// Show detailed initialization error
  Future<void> _showInitializationError() async {
    if (!mounted) return;

    setState(() {
      _isInitializing = false;
      _hasInitializationError = true;
      _initializationError = 'Initialization failed after 3 attempts';
      _initializationStep = 'Failed - Tap for details';
    });

    debugPrint('❌ === INITIALIZATION FAILURE REPORT ===');
    debugPrint('Failed steps: ${_failedSteps.length}');
    for (int i = 0; i < _failedSteps.length; i++) {
      debugPrint('  ${i + 1}. ${_failedSteps[i]}');
    }
    debugPrint('=====================================');
  }

  /// Show error details dialog
  void _showErrorDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Initialization Failed',
          style: TextStyle(color: Colors.red),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'The following steps failed during initialization:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              if (_failedSteps.isEmpty)
                const Text('No specific error details available.')
              else
                ...(_failedSteps.asMap().entries.map((entry) {
                  final index = entry.key;
                  final step = entry.value;
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${index + 1}. ',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            step,
                            style: const TextStyle(fontSize: 13),
                          ),
                        ),
                      ],
                    ),
                  );
                })),
              const SizedBox(height: 16),
              const Text(
                'Please check:\n'
                '• Camera permissions\n'
                '• Device compatibility\n'
                '• Available memory\n'
                '• Network connection',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _retryInitialization();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Retry initialization
  void _retryInitialization() {
    // Don't retry if already exiting or disposed
    if (_isExiting || !mounted) {
      debugPrint('🚫 Skipping retry - widget is exiting or disposed');
      return;
    }

    setState(() {
      _isInitializing = true;
      _hasInitializationError = false;
      _initializationError = '';
      _initializationStep = 'Retrying initialization...';
      _failedSteps.clear();
    });

    // Start initialization again
    _initializeStepByStep();
  }

  /// Apply Snapdragon optimizations after everything else is ready (legacy method)
  Future<void> _applySnapdragonOptimizations() async {
    try {
      await _applySnapdragonOptimizationsWithRetry();
    } catch (e) {
      debugPrint('❌ Failed to apply Snapdragon optimizations: $e');
    }
  }

  @override
  void dispose() {
    debugPrint('🧹 Disposing UserFaceRegisterScreen with animation cleanup...');

    // Mark as exiting to prevent further operations
    _isExiting = true;

    // Restore system UI before disposing
    _restoreSystemUI();

    // Dispose animation controllers
    if (_animationsInitialized) {
      try {
        _exitAnimationController.dispose();
        _fadeAnimationController.dispose();
        debugPrint('✅ Animation controllers disposed');
      } catch (e) {
        debugPrint('⚠️ Error disposing animation controllers: $e');
      }
    }

    // Cancel success message timer
    _successMessageTimer?.cancel();

    // Cleanup is now handled by FaceProvidersWrapper
    // No need to manually dispose providers
    super.dispose();
  }



  /// Handle back button press with confirmation (PopScope callback)
  void _onPopInvokedWithResult(bool didPop, dynamic result) async {
    debugPrint('🔄 UserFaceRegisterScreen PopScope callback: didPop=$didPop, result=$result');
    if (didPop) {
      debugPrint('✅ Already popped, nothing to do');
      return; // Already popped, nothing to do
    }

    debugPrint('🔀 Handling back button with confirmation logic');

    // Handle confirmation logic directly here to properly block navigation
    // If still initializing or has error, exit immediately without dialog
    if (_isInitializing || _hasInitializationError) {
      debugPrint('🚪 Exiting during initialization phase');
      _exitScreen();
      return;
    }

    // Determine behavior based on capture progress
    final hasImages = _hasAnyCapturedImages();
    final capturedCount = _capturedImages.values.where((path) => path != null).length;
    debugPrint('🔍 Back button state: hasImages=$hasImages, capturedCount=$capturedCount, currentDirection=$_currentDirection');
    debugPrint('🔍 Captured images: $_capturedImages');

    if (hasImages) {
      // User has captured some images - show confirmation
      debugPrint('⚠️ Face capture incomplete but has images, showing confirmation dialog');
      debugPrint('🔔 About to call _showExitConfirmationDialog()');
      final shouldExit = await _showExitConfirmationDialog();
      debugPrint('🔔 Dialog result: $shouldExit');
      if (shouldExit == true) {
        debugPrint('👍 User confirmed exit');
        _exitScreen();
      } else {
        debugPrint('👎 User cancelled exit - staying in screen');
        // Do nothing, stay in screen
      }
    } else if (_currentDirection != null) {
      // User is actively capturing but no images saved yet
      debugPrint('📸 User is in capture mode, showing quick confirmation');
      final shouldExit = await _showQuickExitConfirmationDialog();
      if (shouldExit == true) {
        debugPrint('👍 User confirmed exit from capture mode');
        _exitScreen();
      } else {
        debugPrint('👎 User cancelled exit from capture mode - staying in screen');
        // Do nothing, stay in screen
      }
    } else {
      debugPrint('🚪 No capture progress, exiting directly');
      _exitScreen();
    }
  }

  /// Check if all required face angles have been captured
  bool _isCaptureComplete() {
    // Check if all required directions are captured
    return _isAllDirectionsCaptured();
  }

  /// Show confirmation dialog before exiting
  Future<bool?> _showExitConfirmationDialog() async {
    debugPrint('🔔 Showing exit confirmation dialog');
    debugPrint('🔔 Context mounted: $mounted');

    if (!mounted) {
      debugPrint('❌ Context not mounted, cannot show dialog');
      return false;
    }

    // Count captured images for better user feedback
    final capturedCount = _capturedImages.values.where((path) => path != null).length;
    final totalCount = FaceCaptureConstants.totalDirectionsCount;

    debugPrint('🔔 About to call showDialog with capturedCount=$capturedCount');

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        debugPrint('🔔 Dialog builder called');
        return AlertDialog(
          title: const Text('Thoát đăng ký khuôn mặt?'),
          content: Text(
            'Bạn đã chụp $capturedCount/$totalCount góc khuôn mặt. '
            'Nếu thoát bây giờ, dữ liệu sẽ không được lưu.\n\n'
            'Bạn có chắc chắn muốn thoát?'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Tiếp tục chụp'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Thoát'),
            ),
          ],
        );
      },
    );

    debugPrint('🔔 Dialog completed with result: $result');
    return result;
  }

  /// Show quick confirmation dialog when user is in capture mode but no images saved
  Future<bool?> _showQuickExitConfirmationDialog() async {
    debugPrint('🔔 Showing quick exit confirmation dialog');

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thoát đăng ký khuôn mặt?'),
          content: const Text(
            'Bạn đang trong quá trình chụp khuôn mặt. '
            'Bạn có muốn thoát không?'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Tiếp tục'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange,
              ),
              child: const Text('Thoát'),
            ),
          ],
        );
      },
    );
  }

  /// Exit screen with smooth animated disposal
  void _exitScreen({dynamic result}) async {
    // Prevent multiple exit calls
    if (_isExiting) {
      debugPrint('🚫 Exit already in progress, ignoring duplicate call');
      return;
    }

    _isExiting = true;
    debugPrint('🎬 Starting smooth animated exit...');

    try {
      // Skip animations if still initializing or has error - exit immediately
      if (_isInitializing || _hasInitializationError) {
        debugPrint('⚡ Fast exit during initialization phase');
        _restoreSystemUI();
        await Future.delayed(const Duration(milliseconds: 50)); // Minimal delay

        if (mounted) {
          if (widget.onExit != null) {
            widget.onExit!(result);
          } else {
            Navigator.of(context).pop(result);
          }
        }
        return;
      }

      // Start exit animations if initialized and not in init phase
      if (_animationsInitialized && mounted) {
        debugPrint('✨ Starting exit animations...');

        // Start both animations simultaneously for smooth effect
        _fadeAnimationController.forward();
        _exitAnimationController.forward();

        // Wait for animations to complete with timeout safety
        try {
          await Future.wait([
            _fadeAnimationController.forward(),
            _exitAnimationController.forward(),
          ]).timeout(const Duration(milliseconds: 500)); // Safety timeout

          debugPrint('✅ Exit animations completed');
        } catch (e) {
          debugPrint('⚠️ Animation timeout or error: $e - proceeding with exit');
        }
      }

      // Step 1: Restore system UI first
      debugPrint('🔧 Restoring system UI...');
      _restoreSystemUI();
      await Future.delayed(const Duration(milliseconds: 50));

      // Step 2: Stop image streaming smoothly (only if not in init phase)
      if (mounted && !_isInitializing && !_hasInitializationError) {
        try {
          final cameraProvider = context.read<FaceCaptureProvider>();
          final faceProvider = context.read<FaceDetectionProvider>();

          if (cameraProvider.isStreamingEnabled) {
            debugPrint('📹 Stopping image stream...');
            await cameraProvider.toggleImageStream(false);
            await Future.delayed(const Duration(milliseconds: 100));
          }

          // Step 3: Clear face detection
          debugPrint('🧹 Clearing face detection...');
          faceProvider.clearFaces();
          await Future.delayed(const Duration(milliseconds: 50));
        } catch (e) {
          debugPrint('⚠️ Error accessing providers during cleanup: $e');
          // Continue with exit even if provider cleanup fails
        }
      }

      // Step 4: Additional cleanup delay for smooth disposal
      debugPrint('⏳ Final cleanup delay...');
      await Future.delayed(const Duration(milliseconds: 100));

      // Step 5: Safe exit
      debugPrint('🚪 Performing final exit...');
      if (mounted) {
        if (widget.onExit != null) {
          // Use callback for modal mode
          widget.onExit!(result);
        } else {
          // Use navigation for normal mode
          Navigator.of(context).pop(result);
        }
      }
    } catch (e) {
      debugPrint('❌ Error during animated exit: $e');
      // Force exit even if cleanup fails, but still with some delay for smoothness
      await Future.delayed(const Duration(milliseconds: 100));
      if (mounted) {
        if (widget.onExit != null) {
          widget.onExit!(result);
        } else {
          Navigator.of(context).pop(result);
        }
      }
    } finally {
      _isExiting = false;
    }
  }

  void _ensureFaceDetectionActive() {
    try {
      final cameraProvider = context.read<FaceCaptureProvider>();
      if (cameraProvider.isCameraReady && !cameraProvider.isStreamingEnabled) {
        cameraProvider.toggleImageStream(true);
        debugPrint('🎯 Face detection image stream activated');
      }
    } catch (e) {
      // Providers might not be ready yet, ignore
      debugPrint('Face detection not ready yet: $e');
    }
  }

  /// Restart face detection pipeline (useful after camera operations)
  Future<void> _restartFaceDetectionPipeline() async {
    try {
      final faceDetectionProvider = context.read<FaceDetectionProvider>();
      final cameraProvider = context.read<FaceCaptureProvider>();

      debugPrint('🔄 Restarting face detection pipeline...');

      // Restart the pipeline
      await faceDetectionProvider.restartPipeline();

      // Ensure camera stream is active
      if (cameraProvider.isCameraReady) {
        _ensureFaceDetectionActive();
      }

      debugPrint('✅ Face detection pipeline restarted successfully');
    } catch (e) {
      debugPrint('❌ Error restarting face detection pipeline: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Wrap entire screen with smooth exit animations
    Widget screenContent = PopScope(
      canPop: false, // Block navigation, handle in onPopInvokedWithResult
      onPopInvokedWithResult: (didPop, result) {
        debugPrint('🔄 UserFaceRegisterScreen PopScope CALLED: didPop=$didPop, result=$result');
        _onPopInvokedWithResult(didPop, result);
      },
      child: GestureDetector(
        // Absorb all gestures to prevent interference
        onTap: () {},
        child: Container(
          color: Colors.black,
          width: double.infinity,
          height: double.infinity,
          child: Scaffold(
            backgroundColor: Colors.black,
            body: SafeArea(
              child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Stack(
        children: [
          // Camera preview as background
          Positioned.fill(
            child: Consumer<FaceDetectionProvider>(
              builder: (context, faceProvider, child) {
                return perf.PerformanceOverlay(
                  showOverlay: FaceCaptureConstants.showPerformanceProfiling, // Use constant to control profiling
                  monitor: faceProvider.getPerformanceMonitor(), // Use provider's monitor
                  child: CameraPreviewWidget(
                    borderRadius: BorderRadius.zero,
                    enableFaceDetection: true,
                    showBoundingBoxes: true,
                    showQualityInfo: true,
                    onBestFaceChanged: _onBestFaceChanged,
                    overlayWidget: _buildCaptureOverlay(),
                  ),
                );
              },
            ),
          ),

          // Initialization overlay
          if (_isInitializing || _hasInitializationError)
            Container(
              color: Colors.black.withValues(alpha: 0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (!_hasInitializationError) ...[
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _initializationStep,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Optimizing...',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ] else ...[
                      // Error state
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _initializationError,
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      GestureDetector(
                        onTap: _showErrorDetails,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red),
                          ),
                          child: Text(
                            _initializationStep,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _retryInitialization,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ],
                ),
              ),
            ),

          // Main UI overlay
          if (!_isInitializing && !_hasInitializationError)
            SafeArea(
              child: Column(
                children: [
                  // Top section with step indicators
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 40.0, 16.0, 16.0), // Thêm padding top 40px
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: guide.FaceCaptureGuide(
                          capturedImages: _capturedImages,
                          currentDirection: _currentDirection,
                          onImageTap: _onImageTap,
                          onRetakeImage: _onRetakeImage,
                        ),
                      ),
                    ),
                  ),

                  // Bottom section with controls
                  _buildBottomSection(context),
                ],
              ),
            ),
        ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // Apply smooth exit animations if initialized
    if (_animationsInitialized) {
      return AnimatedBuilder(
        animation: Listenable.merge([_fadeAnimationController, _exitAnimationController]),
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: screenContent,
              ),
            ),
          );
        },
      );
    }

    // Return without animations if not initialized yet
    return screenContent;
  }

  void _onBestFaceChanged(Face? bestFace) {
    // Ensure face detection is always enabled
    _ensureFaceDetectionActive();

    setState(() {
      _bestFace = bestFace;
      final faceDetectionProvider = context.read<FaceDetectionProvider>();
      _detectedFacesCount = faceDetectionProvider.faces.length;

      if (bestFace != null) {
        _bestFaceQuality = faceDetectionProvider.getFaceQuality(bestFace);

        // Detect face direction and auto capture
        _detectFaceDirectionAndCapture(bestFace);

        _updateCaptureStatus();
      } else {
        _bestFaceQuality = 0.0;
        _currentDirection = null;
        _updateCaptureStatus();
      }
    });
  }

  void _updateCaptureStatus() {
    // Show success message if available (takes priority)
    if (_successMessage != null) {
      _captureStatus = _successMessage!;
      return;
    }

    // Check if all directions are captured first
    if (_isAllDirectionsCaptured()) {
      _captureStatus = 'Thành công! Đã chụp đủ tất cả hướng';
      return;
    }

    // Handle multiple faces detected
    if (_detectedFacesCount > 1) {
      _captureStatus = FaceCaptureConstants.multipleFacesMessage.replaceAll(
        '{count}',
        '$_detectedFacesCount',
      );
      return;
    }

    // Handle no face detected
    if (_bestFace == null || _detectedFacesCount == 0) {
      _captureStatus = FaceCaptureConstants.defaultStatusMessage;
      return;
    }

    // Check if face is in guide frame
    if (!_isFaceInGuideFrame(_bestFace!)) {
      _captureStatus = FaceCaptureConstants.outOfFrameMessage;
      return;
    }

    // Handle single face with quality assessment and direction guidance
    final faceDetectionProvider = context.read<FaceDetectionProvider>();
    final qualityLevel = faceDetectionProvider.getFaceQualityLevel(_bestFace!);

    // Get current direction and capture status
    final direction = _currentDirection;
    final capturedCount = _capturedImages.values
        .where((path) => path != null)
        .length;

    if (direction != null) {
      final directionName = _getDirectionName(direction);
      final isAlreadyCaptured = _capturedImages[direction] != null;
      final requiredQuality = _getRequiredQuality(direction);

      if (isAlreadyCaptured) {
        _captureStatus = FaceCaptureConstants.capturedMessage
            .replaceAll('{direction}', directionName)
            .replaceAll('{count}', '$capturedCount')
            .replaceAll(
              '{total}',
              '${FaceCaptureConstants.totalDirectionsCount}',
            );
      } else if (_bestFaceQuality >= requiredQuality) {
        _captureStatus = FaceCaptureConstants.capturingMessage.replaceAll(
          '{direction}',
          directionName,
        );
      } else {
        _captureStatus = FaceCaptureConstants.qualityImprovementMessage
            .replaceAll('{direction}', directionName)
            .replaceAll('{current}', '${(_bestFaceQuality * 100).toInt()}')
            .replaceAll('{required}', '${(requiredQuality * 100).toInt()}');
      }
    } else {
      switch (qualityLevel) {
        case 'Excellent':
        case 'Good':
          _captureStatus = FaceCaptureConstants.rotateFaceMessage
              .replaceAll('{count}', '$capturedCount')
              .replaceAll(
                '{total}',
                '${FaceCaptureConstants.totalDirectionsCount}',
              );
          break;
        case 'Fair':
          _captureStatus = 'Khá tốt, điều chỉnh tư thế';
          break;
        case 'Acceptable':
          _captureStatus = 'Chấp nhận được, cải thiện ánh sáng';
          break;
        default:
          _captureStatus = 'Chất lượng thấp, điều chỉnh vị trí';
      }
    }
  }

  /// Show temporary success message in the status area
  void _showSuccessMessage(String message) {
    setState(() {
      _successMessage = message;
    });

    // Clear any existing timer
    _successMessageTimer?.cancel();

    // Set timer to clear success message after 2 seconds
    _successMessageTimer = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _successMessage = null;
          _updateCaptureStatus(); // Refresh status after clearing success message
        });
      }
    });

    // Update status immediately
    _updateCaptureStatus();
  }

  void _detectFaceDirectionAndCapture(Face face) {
    if (!_isFaceInGuideFrame(face)) {
      _currentDirection = null;
      return;
    }

    final direction = _getFaceDirection(face);



    _currentDirection = direction;

    // Different quality thresholds for different directions
    double requiredQuality = _getRequiredQuality(direction);

    if (_bestFaceQuality < requiredQuality) {
      return;
    }

    // Check if we should auto capture for this direction
    if (_shouldAutoCapture(direction)) {
      // Add delay to avoid rapid captures
      Future.delayed(FaceCaptureConstants.capturePreDelay, () {
        if (mounted && _shouldAutoCapture(direction)) {
          _autoCapture(direction);
        }
      });
    }
  }

  double _getRequiredQuality(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return FaceCaptureConstants.frontQualityThreshold;
      case FaceDirection.top:
      case FaceDirection.bottom:
        return FaceCaptureConstants.verticalQualityThreshold;
      case FaceDirection.left:
      case FaceDirection.right:
        return FaceCaptureConstants.profileQualityThreshold;
      case FaceDirection.unknown:
        return FaceCaptureConstants.frontQualityThreshold;
    }
  }

  FaceDirection _getFaceDirection(Face face) {
    final eulerX = face.headEulerAngleX ?? 0.0;
    final eulerY = face.headEulerAngleY ?? 0.0;

    // Define thresholds for direction detection
    const double upThreshold = FaceCaptureConstants.upAngleThreshold;
    const double downThreshold = FaceCaptureConstants.downAngleThreshold;
    const double leftThreshold = FaceCaptureConstants.leftAngleThreshold;
    const double rightThreshold = FaceCaptureConstants.rightAngleThreshold;

    // Check vertical directions first (more specific)
    // When looking UP → capture for BOTTOM icon position
    if (eulerX < upThreshold) {
      return FaceDirection.bottom;
    }
    // When looking DOWN → capture for TOP icon position
    else if (eulerX > downThreshold) {
      return FaceDirection.top;
    }

    // Check horizontal directions
    FaceDirection horizontalDirection = FaceDirection.front;

    // When turning LEFT → capture for RIGHT icon position
    if (eulerY > leftThreshold) {
      horizontalDirection = FaceDirection.right;
    }
    // When turning RIGHT → capture for LEFT icon position
    else if (eulerY < rightThreshold) {
      horizontalDirection = FaceDirection.left;
    }

    // Apply mirroring for front camera if enabled
    if (horizontalDirection != FaceDirection.front &&
        FaceCaptureUIConfig.mirrorDirectionsForFrontCamera) {
      final cameraProvider = context.read<FaceCaptureProvider>();
      if (cameraProvider.isCameraReady &&
          cameraProvider.cameraController?.description.lensDirection == CameraLensDirection.front) {
        horizontalDirection = _mirrorDirection(horizontalDirection);
      }
    }

    if (horizontalDirection != FaceDirection.front) {
      return horizontalDirection;
    }

    // Default to front
    return FaceDirection.front;
  }

  /// Mirror horizontal directions for front camera
  FaceDirection _mirrorDirection(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.left:
        return FaceDirection.right;
      case FaceDirection.right:
        return FaceDirection.left;
      default:
        return direction; // No change for vertical or center directions
    }
  }



  /// Switch between front and back camera with improved error handling
  Future<void> _switchCamera() async {
    // Prevent multiple concurrent camera switches
    if (_isCapturing) {
      debugPrint('🚫 Camera switch already in progress, ignoring request');
      return;
    }

    try {
      final cameraProvider = context.read<FaceCaptureProvider>();

      if (!cameraProvider.isCameraReady) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Camera chưa sẵn sàng'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Check if multiple cameras are available
      if (cameraProvider.getAvailableLensDirections().length < 2) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Chỉ có một camera khả dụng'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      debugPrint('🔄 Starting camera switch...');

      // Show loading indicator
      if (mounted) {
        setState(() {
          _isCapturing = true;
        });
      }

      // Temporarily stop face detection to prevent conflicts
      final faceDetectionProvider = context.read<FaceDetectionProvider>();
      faceDetectionProvider.clearFaces();

      // Switch camera with timeout protection
      await Future.any([
        cameraProvider.switchCamera(),
        Future.delayed(const Duration(seconds: 10), () {
          throw TimeoutException('Camera switch timeout', const Duration(seconds: 10));
        }),
      ]);

      // Wait a moment for camera to stabilize
      await Future.delayed(const Duration(milliseconds: 300));

      // Restart face detection pipeline after camera switch
      if (mounted) {
        await faceDetectionProvider.restartPipeline();

        // Ensure face detection is active
        _ensureFaceDetectionActive();
      }

      // Reset capture state after successful camera switch
      if (mounted) {
        setState(() {
          _capturedImages.clear();
          _currentDirection = null;
          _bestFace = null;
          _bestFaceQuality = 0.0;
          _isCapturing = false;
        });

        // Show success message in status area
        _showSuccessMessage('Đã chuyển camera');
      }

      debugPrint('✅ Camera switch completed successfully');

    } on TimeoutException catch (e) {
      debugPrint('⏰ Camera switch timeout: $e');
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chuyển camera quá lâu, vui lòng thử lại'),
            duration: Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Camera switch error: $e');
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi chuyển camera: ${e.toString().length > 50 ? 'Lỗi hệ thống' : e}'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _shouldAutoCapture(FaceDirection direction) {
    // Don't capture if already captured for this direction
    if (_capturedImages[direction] != null) {
      return false;
    }

    // Don't capture if currently capturing
    if (_isCapturing) {
      return false;
    }

    // Don't capture too frequently
    if (_lastCaptureTime != null) {
      final timeSinceLastCapture = DateTime.now().difference(_lastCaptureTime!);
      if (timeSinceLastCapture < _captureDelay) {
        return false;
      }
    }

    return true;
  }

  Future<void> _autoCapture(FaceDirection direction) async {
    if (_isCapturing || !mounted) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      final cameraProvider = context.read<FaceCaptureProvider>();

      // Check if camera is ready before capturing
      if (!cameraProvider.isCameraReady) {
        throw Exception(FaceCaptureConstants.cameraNotReadyMessage);
      }

      // Add small delay to ensure camera stability
      await Future.delayed(FaceCaptureConstants.cameraStabilityDelay);

      if (!mounted || _isCapturing == false) return;

      final image = await cameraProvider.takePicture(
        pauseStreamForCapture: FaceCaptureConstants.pauseStreamForCapture,
      );

      if (image != null && mounted) {
        setState(() {
          _capturedImages[direction] = image.path;
          _lastCaptureTime = DateTime.now();
        });

        // Show success message in status area
        if (mounted) {
          final successMessage = FaceCaptureConstants.successCaptureMessage.replaceAll(
            '{direction}',
            _getDirectionNameForToast(direction), // Use mirrored direction name for toast
          );
          _showSuccessMessage(successMessage);
        }
      }
    } catch (e) {
      debugPrint('Auto capture error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(FaceCaptureConstants.errorCaptureMessage),
            backgroundColor: FaceCaptureConstants.errorNotificationColor,
            duration: FaceCaptureConstants.errorNotificationDuration,
          ),
        );
      }
    } finally {
      if (mounted) {
        // Add delay before allowing next capture
        await Future.delayed(FaceCaptureConstants.postCaptureDelay);
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  String _getDirectionName(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return FaceCaptureConstants.frontDirectionName;
      case FaceDirection.top:
        return FaceCaptureConstants.topDirectionName;
      case FaceDirection.bottom:
        return FaceCaptureConstants.bottomDirectionName;
      case FaceDirection.left:
        return FaceCaptureConstants.leftDirectionName;
      case FaceDirection.right:
        return FaceCaptureConstants.rightDirectionName;
      case FaceDirection.unknown:
        return 'Unknown';
    }
  }

  /// Get direction name for toast notifications with front camera mirroring
  String _getDirectionNameForToast(FaceDirection direction) {
    // Check if we're using front camera
    final cameraProvider = context.read<FaceCaptureProvider>();
    final isFrontCamera = cameraProvider.isCameraReady &&
        cameraProvider.cameraController?.description.lensDirection == CameraLensDirection.front;

    // For front camera, mirror the direction names in toast notifications only
    if (isFrontCamera && FaceCaptureUIConfig.mirrorDirectionsForFrontCamera) {
      final originalName = _getDirectionName(direction);
      String mirroredName;

      switch (direction) {
        case FaceDirection.left:
          mirroredName = FaceCaptureConstants.rightDirectionName; // Show "phải" when actually turning left
          break;
        case FaceDirection.right:
          mirroredName = FaceCaptureConstants.leftDirectionName; // Show "trái" when actually turning right
          break;
        case FaceDirection.top:
          mirroredName = FaceCaptureConstants.bottomDirectionName; // Show "xuống" when actually looking up
          break;
        case FaceDirection.bottom:
          mirroredName = FaceCaptureConstants.topDirectionName; // Show "lên" when actually looking down
          break;
        case FaceDirection.front:
          mirroredName = FaceCaptureConstants.frontDirectionName;
          break;
        case FaceDirection.unknown:
          mirroredName = 'Unknown';
          break;
      }

      debugPrint('📱 Front camera toast mirroring: ${direction.toString().split('.').last} ($originalName) → $mirroredName');
      return mirroredName;
    }

    // For back camera or when mirroring is disabled, use normal direction names
    return _getDirectionName(direction);
  }

  Widget _buildCaptureOverlay() {
    return Stack(
      children: [
        // Circular guide frame with blur background
        Positioned.fill(
          child: CustomPaint(
            painter: CircularGuidePainter(
              guideColor: _getBorderColor(),
              screenSize: MediaQuery.of(context).size,
              currentDirection: _currentDirection,
              capturedImages: _capturedImages,
            ),
          ),
        ),

        // Camera switch button with improved state management
        Positioned(
          top: 60,
          right: 20,
          child: Consumer<FaceCaptureProvider>(
            builder: (context, cameraProvider, child) {
              final canSwitch = cameraProvider.canSwitchCamera && !_isCapturing;

              return Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: canSwitch ? 0.6 : 0.3),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  onPressed: canSwitch ? _switchCamera : null,
                  icon: _isCapturing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(
                          Icons.flip_camera_ios,
                          color: canSwitch ? Colors.white : Colors.white54,
                          size: 24,
                        ),
                  tooltip: canSwitch ? 'Chuyển camera' : 'Không thể chuyển camera',
                ),
              );
            },
          ),
        ),

        // Capture instructions
        Positioned(
          bottom: 200,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: _successMessage != null
                    ? Colors.green.withValues(alpha: 0.8) // Green background for success messages
                    : Colors.black.withValues(alpha: 0.7), // Default black background
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _captureStatus,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: _successMessage != null
                      ? FontWeight.w600 // Bold for success messages
                      : FontWeight.w500, // Normal weight for regular status
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getBorderColor() {
    // Multiple faces detected - show warning color
    if (_detectedFacesCount > 1) {
      return FaceCaptureConstants.multipleFacesColor;
    }

    // No face detected
    if (_bestFace == null || _detectedFacesCount == 0) {
      return FaceCaptureConstants.noFaceColor.withValues(
        alpha: FaceCaptureConstants.noFaceOpacity,
      );
    }

    // Face detected but not in guide frame
    if (!_isFaceInGuideFrame(_bestFace!)) {
      return FaceCaptureConstants.outOfFrameColor;
    }

    // Single face in guide frame - show quality-based color
    if (_bestFaceQuality >= 0.9) {
      return FaceCaptureConstants.excellentQualityColor;
    }
    if (_bestFaceQuality >= 0.75) return FaceCaptureConstants.goodQualityColor;
    if (_bestFaceQuality >= 0.6) return FaceCaptureConstants.fairQualityColor;
    if (_bestFaceQuality >= 0.5) {
      return FaceCaptureConstants.acceptableQualityColor;
    }
    return FaceCaptureConstants.poorQualityColor;
  }

  Widget _buildBottomSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B).withValues(alpha: 0.9),
        border: const Border(top: BorderSide(color: Color(0xFF30415E))),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Auto capture progress indicator
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'Tiến độ chụp ảnh tự động',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value:
                      _capturedImages.values
                          .where((path) => path != null)
                          .length /
                      FaceCaptureConstants.totalDirectionsCount,
                  backgroundColor: FaceCaptureConstants.progressTrackColor
                      .withValues(
                        alpha: FaceCaptureConstants.progressTrackOpacity,
                      ),
                  valueColor: const AlwaysStoppedAnimation(
                    FaceCaptureConstants.progressFillColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_capturedImages.values.where((path) => path != null).length}/${FaceCaptureConstants.totalDirectionsCount} hướng đã chụp',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),

          // Bottom row with controls
          Row(
            children: [
              // Profile preview
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.withValues(alpha: 0.3),
                  border: Border.all(
                    color: _isAllDirectionsCaptured()
                        ? FaceCaptureConstants.successButtonColor
                        : _bestFace != null
                            ? _getBorderColor()
                            : Colors.grey,
                    width: 2,
                  ),
                ),
                child: _buildProfilePreviewContent(),
              ),

              const SizedBox(width: 8),

              // Cancel button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _handleCancel(context),
                  label: const Text('Huỷ', style: TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 4),

              // Reset button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _hasAnyCapturedImages()
                      ? () => _handleReset()
                      : null,
                  icon: Icon(
                    Icons.refresh,
                    size: 14,
                    color: _hasAnyCapturedImages()
                        ? Colors.orange
                        : Colors.white.withValues(alpha: 0.6),
                  ),
                  label: Text(
                    'Lấy lại',
                    style: TextStyle(
                      fontSize: 12,
                      color: _hasAnyCapturedImages()
                          ? Colors.orange
                          : Colors.white.withValues(alpha: 0.6),
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: _hasAnyCapturedImages()
                          ? Colors.orange
                          : Colors.white.withValues(alpha: 0.3),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 4),

              // Continue button
              Expanded(
                child: ElevatedButton(
                  onPressed: _isAllDirectionsCaptured()
                      ? () => _handleContinue(context)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isAllDirectionsCaptured()
                        ? FaceCaptureConstants.successButtonColor
                        : FaceCaptureConstants.disabledButtonColor.withValues(
                            alpha: FaceCaptureConstants.disabledButtonOpacity,
                          ),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Tiếp tục',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  bool _isFaceInGuideFrame(Face face) {
    // Get screen size
    final screenSize = MediaQuery.of(context).size;

    // Circular guide parameters with 15% increase
    final center = Offset(screenSize.width / 2, screenSize.height / 2);
    final radius = (FaceCaptureConstants.guideFrameWidth / 2) * 1.15;

    // Transform face bounding box to screen coordinates
    final cameraProvider = context.read<FaceCaptureProvider>();

    if (!cameraProvider.isCameraReady) return false;

    final controller = cameraProvider.cameraController!;
    final previewSize = controller.value.previewSize!;

    // Calculate scale factors
    final scaleX =
        screenSize.width / previewSize.height; // Note: swapped for camera
    final scaleY = screenSize.height / previewSize.width;

    // Transform face bounding box
    double faceLeft = face.boundingBox.left * scaleX;
    double faceTop = face.boundingBox.top * scaleY;
    double faceRight = face.boundingBox.right * scaleX;
    double faceBottom = face.boundingBox.bottom * scaleY;

    // Handle front camera mirroring
    if (controller.description.lensDirection == CameraLensDirection.front) {
      final temp = faceLeft;
      faceLeft = screenSize.width - faceRight;
      faceRight = screenSize.width - temp;
    }

    // Calculate face center
    final faceCenter = Offset(
      (faceLeft + faceRight) / 2,
      (faceTop + faceBottom) / 2,
    );

    // Check if face center is within circular guide with tolerance
    const tolerance = FaceCaptureConstants.guideFrameTolerance;
    final distance = (faceCenter - center).distance;

    return distance <= (radius + tolerance);
  }

  bool _isAllDirectionsCaptured() {
    return _capturedImages.values
            .where((path) => path != null)
            .length ==
        FaceCaptureConstants.totalDirectionsCount;
  }

  bool _hasAnyCapturedImages() {
    final hasImages = _capturedImages.values.any((path) => path != null);
    debugPrint('🔍 _hasAnyCapturedImages: $hasImages, images: $_capturedImages');
    return hasImages;
  }

  Widget _buildProfilePreviewContent() {
    // If all directions are captured, show the front-facing image
    if (_isAllDirectionsCaptured()) {
      final frontImagePath = _capturedImages[FaceDirection.front];
      if (frontImagePath != null) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Image.file(
            File(frontImagePath),
            width: 46,
            height: 46,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.face, color: Colors.white, size: 24);
            },
          ),
        );
      }
    }

    // Default state - show icon based on face detection
    if (_bestFace != null) {
      return const Icon(Icons.face, color: Colors.white, size: 24);
    } else {
      return const Icon(
        Icons.face_outlined,
        color: Colors.grey,
        size: 24,
      );
    }
  }

  void _handleContinue(BuildContext context) {
    // Handle continue action - navigate back with captured images
    final result = FaceCaptureResult.success(_capturedImages);
    _exitScreen(result: result);
  }

  void _handleCancel(BuildContext context) async {
    // Show confirmation if not all directions captured
    if (!_isCaptureComplete() && _hasAnyCapturedImages()) {
      final shouldExit = await _showExitConfirmationDialog();
      if (shouldExit == true) {
        _exitScreen(result: FaceCaptureResult.failure());
      }
    } else {
      // No images captured or all complete, exit directly
      _exitScreen(result: FaceCaptureResult.failure());
    }
  }

  void _handleReset() {
    // Clear all captured images and reset state
    setState(() {
      _capturedImages.clear();
      _currentDirection = FaceDirection.front;
    });

    // Show confirmation message in status area
    _showSuccessMessage('Đã xóa tất cả ảnh đã chụp. Bắt đầu chụp lại.');
  }

  // ============================================================================
  // IMAGE PREVIEW AND RETAKE FUNCTIONALITY
  // ============================================================================

  /// Handle tap on captured image to show preview modal
  void _onImageTap(FaceDirection direction) {
    final imagePath = _capturedImages[direction];
    if (imagePath == null) return;

    CapturedImagePreviewModal.show(
      context: context,
      imagePath: imagePath,
      direction: direction,
      onRetake: () => _onRetakeImage(direction),
    );
  }

  /// Handle retake image request
  void _onRetakeImage(FaceDirection direction) {
    setState(() {
      // Clear the captured image for this direction
      _capturedImages[direction] = null;

      // Reset capture status to allow new capture
      _lastCaptureTime = null;

      // Update UI status
      _updateCaptureStatus();
    });

    // Show feedback to user in status area
    _showSuccessMessage('Đã xóa ảnh ${_getDirectionNameForToast(direction)}. Hãy chụp lại.');
  }

}

// Custom painter for circular face capture guide with blur background
class CircularGuidePainter extends CustomPainter {
  final Color guideColor;
  final Size screenSize;
  final FaceDirection? currentDirection;
  final Map<FaceDirection, String?> capturedImages;

  CircularGuidePainter({
    required this.guideColor,
    required this.screenSize,
    this.currentDirection,
    this.capturedImages = const {},
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate circle parameters using UI config
    final center = Offset(
      size.width / 2,
      (size.height / 2) + FaceCaptureUIConfig.circleVerticalOffset,
    );
    final radius = (FaceCaptureConstants.guideFrameWidth / 2) *
        FaceCaptureUIConfig.circleSizeMultiplier;

    // Create path for the circular cutout
    final circlePath = Path()
      ..addOval(Rect.fromCircle(center: center, radius: radius));

    // Create path for the full screen
    final fullScreenPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Create the overlay path (full screen minus circle)
    final overlayPath = Path.combine(
      PathOperation.difference,
      fullScreenPath,
      circlePath,
    );

    // Draw overlay based on UI configuration
    if (FaceCaptureUIConfig.shouldDrawOverlay()) {
      final overlayPaint = Paint()
        ..color = Color(FaceCaptureUIConfig.getOverlayColor()).withValues(
          alpha: FaceCaptureUIConfig.getOverlayOpacity(),
        )
        ..style = PaintingStyle.fill;

      canvas.drawPath(overlayPath, overlayPaint);
    }

    // Draw guide circle border
    final guidePaint = Paint()
      ..color = guideColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = FaceCaptureUIConfig.guideBorderWidth;

    canvas.drawCircle(center, radius, guidePaint);

    // Draw center crosshair if enabled
    if (FaceCaptureUIConfig.showCrosshair) {
      final crosshairPaint = Paint()
        ..color = guideColor.withValues(alpha: FaceCaptureUIConfig.crosshairOpacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      const crosshairLength = FaceCaptureUIConfig.crosshairLength;

      // Horizontal line
      canvas.drawLine(
        Offset(center.dx - crosshairLength, center.dy),
        Offset(center.dx + crosshairLength, center.dy),
        crosshairPaint,
      );

      // Vertical line
      canvas.drawLine(
        Offset(center.dx, center.dy - crosshairLength),
        Offset(center.dx, center.dy + crosshairLength),
        crosshairPaint,
      );
    }



    // Draw direction indicators on circle border
    if (FaceCaptureUIConfig.showDirectionIndicators) {
      _drawDirectionIndicators(canvas, center, radius);
    }
  }

  /// Draw direction indicators around the circle
  void _drawDirectionIndicators(Canvas canvas, Offset center, double radius) {
    // Define positions for each direction (in radians)
    final Map<FaceDirection, double> directionAngles = {
      FaceDirection.top: -math.pi / 2,      // Top (270°)
      FaceDirection.right: 0,               // Right (0°)
      FaceDirection.bottom: math.pi / 2,    // Bottom (90°)
      FaceDirection.left: math.pi,          // Left (180°)
      FaceDirection.front: 0,               // Front (will be handled separately)
    };

    for (final entry in directionAngles.entries) {
      final direction = entry.key;
      final angle = entry.value;

      // Skip front direction for circle indicators (it's in the center)
      if (direction == FaceDirection.front) continue;

      // Calculate indicator position
      final indicatorRadius = radius + FaceCaptureUIConfig.indicatorOffset;
      final indicatorPosition = Offset(
        center.dx + indicatorRadius * math.cos(angle),
        center.dy + indicatorRadius * math.sin(angle),
      );

      // Determine indicator color based on state
      Color indicatorColor;
      if (capturedImages[direction] != null) {
        // Already captured - blue
        indicatorColor = Color(FaceCaptureUIConfig.capturedIndicatorColor);
      } else if (currentDirection == direction) {
        // Current active direction - green
        indicatorColor = Color(FaceCaptureUIConfig.activeIndicatorColor);
      } else {
        // Inactive direction - gray
        indicatorColor = Color(FaceCaptureUIConfig.inactiveIndicatorColor);
      }

      // Draw indicator based on style
      switch (FaceCaptureUIConfig.indicatorStyle) {
        case IndicatorStyle.dots:
          _drawDotIndicator(canvas, indicatorPosition, indicatorColor);
          break;
        case IndicatorStyle.arrows:
          _drawArrowIndicator(canvas, indicatorPosition, angle, indicatorColor);
          break;
        case IndicatorStyle.icons:
          _drawIconIndicator(canvas, indicatorPosition, direction, indicatorColor);
          break;
        case IndicatorStyle.iosDashes:
          // iOS dashes are drawn separately, not per direction
          break;
      }
    }
  }

  /// Draw dot indicator
  void _drawDotIndicator(Canvas canvas, Offset position, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(position, FaceCaptureUIConfig.indicatorSize / 2, paint);
  }

  /// Draw arrow indicator pointing toward center
  void _drawArrowIndicator(Canvas canvas, Offset position, double angle, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final size = FaceCaptureUIConfig.indicatorSize;

    // Create arrow path pointing inward (toward center)
    final path = Path();

    // Arrow pointing toward center (opposite direction of angle)
    final arrowAngle = angle + math.pi; // Point inward

    // Arrow tip
    final tipX = position.dx + (size / 2) * math.cos(arrowAngle);
    final tipY = position.dy + (size / 2) * math.sin(arrowAngle);

    // Arrow base corners
    final baseAngle1 = arrowAngle + 2.5; // ~143°
    final baseAngle2 = arrowAngle - 2.5; // ~37°

    final base1X = position.dx + (size / 3) * math.cos(baseAngle1);
    final base1Y = position.dy + (size / 3) * math.sin(baseAngle1);

    final base2X = position.dx + (size / 3) * math.cos(baseAngle2);
    final base2Y = position.dy + (size / 3) * math.sin(baseAngle2);

    path.moveTo(tipX, tipY);
    path.lineTo(base1X, base1Y);
    path.lineTo(base2X, base2Y);
    path.close();

    canvas.drawPath(path, paint);
  }

  /// Draw icon indicator (simple geometric shapes representing directions)
  void _drawIconIndicator(Canvas canvas, Offset position, FaceDirection direction, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final size = FaceCaptureUIConfig.indicatorSize;

    switch (direction) {
      case FaceDirection.top:
      case FaceDirection.bottom:
        // Draw vertical line for top/bottom
        canvas.drawRect(
          Rect.fromCenter(
            center: position,
            width: size / 4,
            height: size,
          ),
          paint,
        );
        break;
      case FaceDirection.left:
      case FaceDirection.right:
        // Draw horizontal line for left/right
        canvas.drawRect(
          Rect.fromCenter(
            center: position,
            width: size,
            height: size / 4,
          ),
          paint,
        );
        break;
      case FaceDirection.front:
        // Draw small circle for front
        canvas.drawCircle(position, size / 3, paint);
        break;
      case FaceDirection.unknown:
        // Draw question mark shape (simple circle with dot)
        canvas.drawCircle(position, size / 3, paint);
        break;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
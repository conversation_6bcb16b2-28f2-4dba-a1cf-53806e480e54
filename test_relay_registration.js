// Manual test script to register relay devices with server
// Run this to test if server receives relay registrations

const axios = require('axios');

const SERVER_URL = 'http://************';
const TERMINAL_ID = 'T-A3B4';

async function testRelayRegistration() {
    console.log('🚀 Testing Relay Device Registration');
    console.log('Server URL:', SERVER_URL);
    console.log('Terminal ID:', TERMINAL_ID);
    console.log('');

    // First, check current devices on server
    console.log('📊 Checking current devices on server...');
    try {
        const response = await axios.get(`${SERVER_URL}/devices`);
        console.log(`Found ${response.data.length} devices on server:`);
        
        const terminalDevices = response.data.filter(d => d.id.startsWith('T-') && !d.id.includes('-R'));
        const relayDevices = response.data.filter(d => d.id.includes('-R'));
        
        console.log(`  🖥️ Terminal devices: ${terminalDevices.length}`);
        console.log(`  🔌 Relay devices: ${relayDevices.length}`);
        
        terminalDevices.forEach(d => console.log(`    Terminal: ${d.id} - ${d.name}`));
        relayDevices.forEach(d => console.log(`    Relay: ${d.id} - ${d.name}`));
        
    } catch (error) {
        console.log('❌ Failed to check current devices:', error.message);
    }
    
    console.log('');

    // Register terminal device first (if not already registered)
    console.log('🖥️ Registering terminal device...');
    try {
        const terminalResponse = await axios.post(`${SERVER_URL}/register`, {
            deviceId: TERMINAL_ID,
            deviceName: 'Test Terminal Device',
            type: 'terminal',
            hardwareHash: `terminal_${Date.now()}`,
            appVersion: '1.0.0'
        });
        
        console.log('✅ Terminal registration response:', terminalResponse.status);
        
    } catch (error) {
        console.log('⚠️ Terminal registration failed (might already exist):', error.response?.status || error.message);
    }
    
    console.log('');

    // Register relay devices
    console.log('🔌 Registering relay devices...');
    const relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];
    
    for (let i = 1; i <= 4; i++) {
        const relayId = `${TERMINAL_ID}-R${i.toString().padStart(2, '0')}`;
        const relayName = `Terminal Relay ${i}`;
        const profile = relayProfiles[i - 1];
        
        console.log(`📡 Registering relay: ${relayId} (${profile})`);
        
        try {
            const relayResponse = await axios.post(`${SERVER_URL}/register`, {
                deviceId: relayId,
                deviceName: relayName,
                type: 'relay',
                terminalId: TERMINAL_ID,
                profile: profile,
                relayCount: 1,
                baudRate: 115200,
                autoRegistered: true,
                hardwareHash: `relay_${Date.now()}_${i}`,
                appVersion: '1.0.0'
            });
            
            console.log(`✅ Successfully registered: ${relayId} (Status: ${relayResponse.status})`);
            
        } catch (error) {
            console.log(`❌ Failed to register ${relayId}:`, error.response?.status || error.message);
            if (error.response?.data) {
                console.log('   Error details:', error.response.data);
            }
        }
        
        // Small delay between registrations
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('');

    // Check devices again after registration
    console.log('📊 Checking devices after registration...');
    try {
        const response = await axios.get(`${SERVER_URL}/devices`);
        console.log(`Total devices on server: ${response.data.length}`);
        
        const terminalDevices = response.data.filter(d => d.id.startsWith('T-') && !d.id.includes('-R'));
        const relayDevices = response.data.filter(d => d.id.includes('-R'));
        const ourRelays = relayDevices.filter(d => d.id.startsWith(TERMINAL_ID));
        
        console.log(`  🖥️ Terminal devices: ${terminalDevices.length}`);
        console.log(`  🔌 Total relay devices: ${relayDevices.length}`);
        console.log(`  🎯 Our relay devices: ${ourRelays.length}`);
        
        console.log('\nOur registered relays:');
        ourRelays.forEach(d => {
            console.log(`    ✅ ${d.id} - ${d.name} (${d.profile || 'no profile'})`);
        });
        
        if (ourRelays.length === 4) {
            console.log('\n🎉 SUCCESS: All 4 relay devices registered successfully!');
        } else {
            console.log(`\n⚠️ WARNING: Expected 4 relays, but found ${ourRelays.length}`);
        }
        
    } catch (error) {
        console.log('❌ Failed to check devices after registration:', error.message);
    }
}

// Run the test
testRelayRegistration().catch(console.error);

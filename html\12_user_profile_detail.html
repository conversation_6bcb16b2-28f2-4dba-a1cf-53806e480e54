<div
  class="flex flex-col justify-start items-center w-[375px] h-[800px] relative overflow-hidden bg-white"
>
  <div
    class="self-stretch flex-grow-0 flex-shrink-0 h-[151px] relative bg-[#008fd3]"
  >
    <div
      class="flex flex-col justify-start items-start w-[375px] absolute left-0 top-0 bg-gradient-to-b from-black/[0.15] to-black/0"
    >
      <div
        class="self-stretch flex-grow-0 flex-shrink-0 h-11 relative overflow-hidden"
      >
        <div class="w-[375px] h-[30px] absolute left-0 top-0"></div>
        <div
          class="flex justify-start items-center absolute left-[292px] top-4 gap-1"
        >
          <svg
            width="20"
            height="14"
            viewBox="0 0 20 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="flex-grow-0 flex-shrink-0 w-5 h-3.5 relative"
            preserveAspectRatio="none"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M12 4H13C13.5523 4 14 4.44772 14 5V11C14 11.5523 13.5523 12 13 12H12C11.4477 12 11 11.5523 11 11V5C11 4.44772 11.4477 4 12 4Z"
              fill="#D1D1D6"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M7.5 6H8.5C9.05228 6 9.5 6.44772 9.5 7V11C9.5 11.5523 9.05228 12 8.5 12H7.5C6.94772 12 6.5 11.5523 6.5 11V7C6.5 6.44772 6.94772 6 7.5 6Z"
              fill="#D1D1D6"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3 7.5H4C4.55228 7.5 5 7.94772 5 8.5V11C5 11.5523 4.55228 12 4 12H3C2.44772 12 2 11.5523 2 11V8.5C2 7.94772 2.44772 7.5 3 7.5Z"
              fill="#D1D1D6"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M16.5 2H17.5C18.0523 2 18.5 2.44772 18.5 3V11C18.5 11.5523 18.0523 12 17.5 12H16.5C15.9477 12 15.5 11.5523 15.5 11V3C15.5 2.44772 15.9477 2 16.5 2Z"
              fill="#1F2329"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M12 4H13C13.5523 4 14 4.44772 14 5V11C14 11.5523 13.5523 12 13 12H12C11.4477 12 11 11.5523 11 11V5C11 4.44772 11.4477 4 12 4Z"
              fill="#1F2329"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M7.5 6H8.5C9.05228 6 9.5 6.44772 9.5 7V11C9.5 11.5523 9.05228 12 8.5 12H7.5C6.94772 12 6.5 11.5523 6.5 11V7C6.5 6.44772 6.94772 6 7.5 6Z"
              fill="#1F2329"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3 7.5H4C4.55228 7.5 5 7.94772 5 8.5V11C5 11.5523 4.55228 12 4 12H3C2.44772 12 2 11.5523 2 11V8.5C2 7.94772 2.44772 7.5 3 7.5Z"
              fill="#1F2329"
            ></path></svg
          ><svg
            width="16"
            height="14"
            viewBox="0 0 16 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="flex-grow-0 flex-shrink-0 w-4 h-3.5 relative"
            preserveAspectRatio="none"
          >
            <path
              d="M8.13295 8.93945C8.77245 8.93945 9.39384 9.1075 9.94135 9.42792L10.1632 9.55775C10.3314 9.65617 10.3609 9.88652 10.2231 10.024L8.32897 11.9129C8.21263 12.0289 8.02401 12.0289 7.90767 11.9129L6.02576 10.0362C5.88847 9.89927 5.91709 9.67009 6.08385 9.57094L6.3032 9.44051C6.85574 9.11198 7.48501 8.93945 8.13295 8.93945Z"
              fill="#1F2329"
            ></path>
            <path
              d="M8.13267 5.46973C9.7235 5.46973 11.2509 5.99747 12.4952 6.97479L12.6711 7.11291C12.8113 7.22303 12.8236 7.43052 12.6974 7.55636L11.5666 8.684C11.462 8.7883 11.2965 8.80028 11.1779 8.71214L11.0401 8.60969C10.1997 7.98511 9.18633 7.65008 8.13267 7.65008C7.07251 7.65008 6.05323 7.98928 5.21005 8.62103L5.07207 8.72441C4.95346 8.81327 4.78737 8.80157 4.68247 8.69696L3.5521 7.56974C3.42618 7.44417 3.43813 7.2372 3.57769 7.12687L3.75254 6.98864C4.99979 6.00261 6.53416 5.46973 8.13267 5.46973Z"
              fill="#1F2329"
            ></path>
            <path
              d="M8.13272 2C10.6574 2 13.0717 2.89057 14.9828 4.52294L15.1459 4.66228C15.2777 4.77488 15.2855 4.97558 15.1627 5.09797L14.0356 6.22195C13.9264 6.33084 13.7519 6.33847 13.6336 6.23952L13.494 6.12283C11.9894 4.86472 10.1035 4.18035 8.13272 4.18035C6.15517 4.18035 4.26327 4.86943 2.75641 6.13541L2.6168 6.2527C2.4985 6.3521 2.32359 6.34466 2.2142 6.23557L1.08726 5.11176C0.964693 4.98954 0.972236 4.78918 1.10365 4.67646L1.26614 4.53708C3.17953 2.89589 5.60056 2 8.13272 2Z"
              fill="#1F2329"
            ></path></svg
          ><svg
            width="25"
            height="14"
            viewBox="0 0 25 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="flex-grow-0 flex-shrink-0 w-[25px] h-3.5 relative"
            preserveAspectRatio="none"
          >
            <path
              d="M24 5V5C24.5523 5 25 5.44772 25 6V8C25 8.55228 24.5523 9 24 9V9V5Z"
              fill="#1F2329"
            ></path>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3 1H20C21.6569 1 23 2.34315 23 4V10C23 11.6569 21.6569 13 20 13H3C1.34315 13 0 11.6569 0 10V4C0 2.34315 1.34315 1 3 1ZM3 2C1.89543 2 1 2.89543 1 4V10C1 11.1046 1.89543 12 3 12H20C21.1046 12 22 11.1046 22 10V4C22 2.89543 21.1046 2 20 2H3Z"
              fill="#1F2329"
            ></path>
            <rect
              x="2"
              y="3"
              width="19"
              height="8"
              rx="1"
              fill="#1F2329"
            ></rect>
          </svg>
        </div>
        <div class="w-1.5 h-1.5 absolute left-[298px] top-2"></div>
        <svg
          width="54"
          height="21"
          viewBox="0 0 54 21"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="w-[54px] h-[21px] absolute left-8 top-3"
          preserveAspectRatio="none"
        >
          <path
            d="M5.86719 16.0889C8.55518 16.0889 10.1519 13.9868 10.1519 10.4272C10.1519 9.08691 9.89551 7.95898 9.40479 7.0874C8.69434 5.73242 7.47119 5 5.92578 5C3.62598 5 2 6.54541 2 8.71338C2 10.7495 3.46484 12.229 5.479 12.229C6.7168 12.229 7.72021 11.6504 8.21826 10.647H8.24023C8.24023 10.647 8.26953 10.647 8.27686 10.647C8.2915 10.647 8.34277 10.647 8.34277 10.647C8.34277 13.064 7.42725 14.5068 5.88184 14.5068C4.97363 14.5068 4.27051 14.0088 4.02881 13.2104H2.14648C2.46143 14.9463 3.93359 16.0889 5.86719 16.0889ZM5.93311 10.7275C4.71729 10.7275 3.85303 9.86328 3.85303 8.65479C3.85303 7.47559 4.76123 6.57471 5.94043 6.57471C7.11963 6.57471 8.02783 7.49023 8.02783 8.68408C8.02783 9.86328 7.1416 10.7275 5.93311 10.7275Z"
            fill="#1F2329"
          ></path>
          <path
            d="M13.243 15.9863C13.9388 15.9863 14.4148 15.4883 14.4148 14.8291C14.4148 14.1626 13.9388 13.6719 13.243 13.6719C12.5545 13.6719 12.0711 14.1626 12.0711 14.8291C12.0711 15.4883 12.5545 15.9863 13.243 15.9863ZM13.243 10.4932C13.9388 10.4932 14.4148 10.0024 14.4148 9.34326C14.4148 8.67676 13.9388 8.18604 13.243 8.18604C12.5545 8.18604 12.0711 8.67676 12.0711 9.34326C12.0711 10.0024 12.5545 10.4932 13.243 10.4932Z"
            fill="#1F2329"
          ></path>
          <path
            d="M21.2706 15.8325H23.0797V13.8623H24.5079V12.2656H23.0797V5.26367H20.4137C18.546 8.07617 17.0592 10.4272 16.107 12.1777V13.8623H21.2706V15.8325ZM17.8575 12.1997C19.088 10.0317 20.1866 8.2959 21.1974 6.80176H21.2999V12.3096H17.8575V12.1997Z"
            fill="#1F2329"
          ></path>
          <path
            d="M28.5365 15.8325H30.4262V5.26367H28.5438L25.7826 7.19727V9.01367L28.412 7.16797H28.5365V15.8325Z"
            fill="#1F2329"
          ></path>
        </svg>
      </div>
      <div
        class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[375px] h-11 gap-6 px-4"
      >
        <div
          class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-3"
        >
          <svg
            width="7"
            height="12"
            viewBox="0 0 7 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="flex-grow-0 flex-shrink-0 w-[5px] h-2.5"
            preserveAspectRatio="xMidYMid meet"
          >
            <path
              d="M6 1L1.70711 5.29289C1.37377 5.62623 1.20711 5.79289 1.20711 6C1.20711 6.20711 1.37377 6.37377 1.70711 6.70711L6 11"
              stroke="white"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
          <div
            class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-white"
            >
              Chi tiết thành viên
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-3 px-4"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-1 pt-[62px]"
    >
      <div
        class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0"
      >
        <div
          class="flex justify-center items-start flex-grow-0 flex-shrink-0 gap-4"
        >
          <div
            class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-2xl font-semibold text-left text-[#1f2329]"
            >
              Trần Thanh Long
            </p>
          </div>
        </div>
        <div
          class="flex justify-start items-center flex-grow-0 flex-shrink-0 h-5 relative gap-1.5"
        >
          <p class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#85888c]">
            <EMAIL>
          </p>
        </div>
      </div>
    </div>
    <div
      class="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3"
    >
      <div
        class="flex justify-center items-center flex-grow relative gap-2.5 px-6 py-2.5 rounded-lg bg-[#008fd3]"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
          preserveAspectRatio="xMidYMid meet"
        >
          <g clip-path="url(#clip0_206_2329)">
            <path
              d="M9.38215 2.58998C9.87894 2.05173 10.1273 1.78261 10.3913 1.62563C11.0282 1.24685 11.8124 1.23507 12.4599 1.59456C12.7283 1.74354 12.9843 2.00509 13.4964 2.52818C14.0084 3.05128 14.2645 3.31282 14.4103 3.58696C14.7622 4.24842 14.7507 5.04954 14.3799 5.70014C14.2262 5.96978 13.9628 6.22352 13.4359 6.73101L7.16676 12.7692C6.16826 13.7309 5.66901 14.2118 5.04505 14.4555C4.42109 14.6992 3.73514 14.6813 2.36325 14.6454L2.17659 14.6405C1.75894 14.6296 1.55012 14.6241 1.42873 14.4864C1.30734 14.3486 1.32392 14.1359 1.35706 13.7105L1.37506 13.4794C1.46835 12.282 1.51499 11.6833 1.74881 11.1452C1.98263 10.607 2.38596 10.17 3.19263 9.29601L9.38215 2.58998Z"
              stroke="white"
              stroke-width="1.5"
              stroke-linejoin="round"
            ></path>
            <path
              d="M8.66699 2.66699L13.3337 7.33366"
              stroke="white"
              stroke-width="1.5"
              stroke-linejoin="round"
            ></path>
            <path
              d="M9.33301 14.667L14.6663 14.667"
              stroke="white"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </g>
          <defs>
            <clipPath id="clip0_206_2329">
              <rect width="16" height="16" fill="white"></rect>
            </clipPath>
          </defs>
        </svg>
        <p
          class="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-white"
        >
          Chỉnh sửa
        </p>
      </div>
      <div
        class="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-10 relative gap-2 px-4 rounded-lg bg-white border border-[#e5e6e7]"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
          preserveAspectRatio="xMidYMid meet"
        >
          <rect
            x="12"
            y="7"
            width="2"
            height="2"
            rx="1"
            stroke="#1F2329"
            stroke-width="1.5"
          ></rect>
          <rect
            x="7"
            y="7"
            width="2"
            height="2"
            rx="1"
            stroke="#1F2329"
            stroke-width="1.5"
          ></rect>
          <rect
            x="2"
            y="7"
            width="2"
            height="2"
            rx="1"
            stroke="#1F2329"
            stroke-width="1.5"
          ></rect>
        </svg>
      </div>
    </div>
    <div
      class="flex justify-start items-center flex-grow-0 flex-shrink-0 absolute left-3 top-[-46px] gap-2.5"
    >
      <img
        src="img_7877-2.png"
        class="flex-grow-0 flex-shrink-0 w-[92px] h-[92px] rounded-[100px] object-none border-2 border-white"
      />
      <div
        class="flex justify-start items-start flex-grow-0 flex-shrink-0 w-4 h-4 absolute left-[66px] top-[73px]"
      >
        <div
          class="flex justify-start items-center self-stretch flex-grow w-4 h-4 gap-1 px-1.5 rounded-[100px] bg-[#40bf24]"
        ></div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow gap-4 py-4"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
    >
      <div
        class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2"
      >
        <div
          class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative pb-3 border-t-0 border-r-0 border-b-[1.5px] border-l-0 border-[#008fd3]"
        >
          <p
            class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-[#008fd3]"
          >
            Thông tin cơ bản
          </p>
        </div>
        <div
          class="flex-grow-0 flex-shrink-0 w-[343px] h-px bg-[#1f2329]/[0.15]"
        ></div>
      </div>
      <div
        class="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden gap-4 px-4 py-3.5 rounded-lg bg-white border border-[#e5e6e7]"
      >
        <div
          class="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 gap-4"
        >
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Mã nhân viên:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              E000025
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Email:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              <EMAIL>
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Số điện thoại:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              0936161309
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Tổ chức:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              CMC TS
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Đơn vị:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              Phòng PD
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Vai trò:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              Product Executive
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Ngày sinh:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              19/10/2000
            </p>
          </div>
          <div
            class="flex justify-between items-start self-stretch flex-grow-0 flex-shrink-0 relative"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#73787e]"
            >
              Giới tính:
            </p>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#1f2329]"
            >
              Nam
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

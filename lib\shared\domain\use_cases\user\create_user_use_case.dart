import 'package:dartz/dartz.dart';
import '../../entities/user/user.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';

class CreateUserUseCase {
  final UserRepository repository;

  CreateUserUseCase(this.repository);

  Future<Either<Failure, User>> call(CreateUserParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to create user
    return await repository.createUser(
      username: params.username,
      password: params.password,
      name: params.name,
      unitId: params.unitId,
      email: params.email,
      phone: params.phone,
      dob: params.dob,
      gender: params.gender,
      memberRoleId: params.memberRoleId,
    );
  }

  ValidationFailure? _validateParams(CreateUserParams params) {
    final errors = <String, List<String>>{};

    // Validate required fields
    if (params.username.trim().isEmpty) {
      errors['username'] = ['Username is required'];
    }

    if (params.password.trim().isEmpty) {
      errors['password'] = ['Password is required'];
    } else if (params.password.length < 6) {
      errors['password'] = ['Password must be at least 6 characters'];
    }

    if (params.name.trim().isEmpty) {
      errors['name'] = ['Name is required'];
    }

    if (params.unitId.trim().isEmpty) {
      errors['unitId'] = ['Unit ID is required'];
    }

    // Validate email format if provided
    if (params.email != null && params.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(params.email!)) {
        errors['email'] = ['Invalid email format'];
      }
    }

    // Validate phone format if provided
    if (params.phone != null && params.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[0-9]{10,15}$');
      if (!phoneRegex.hasMatch(params.phone!.replaceAll(RegExp(r'[\s-]'), ''))) {
        errors['phone'] = ['Invalid phone format'];
      }
    }

    // Validate date format if provided
    if (params.dob != null && params.dob!.isNotEmpty) {
      try {
        DateTime.parse(params.dob!);
      } catch (e) {
        errors['dob'] = ['Invalid date format (YYYY-MM-DD)'];
      }
    }

    // Validate gender if provided
    if (params.gender != null && params.gender!.isNotEmpty) {
      if (!['male', 'female', 'other'].contains(params.gender!.toLowerCase())) {
        errors['gender'] = ['Gender must be male, female, or other'];
      }
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Validation failed',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class CreateUserParams {
  final String username;
  final String password;
  final String name;
  final String unitId;
  final String? email;
  final String? phone;
  final String? dob;
  final String? gender;
  final String? memberRoleId;

  CreateUserParams({
    required this.username,
    required this.password,
    required this.name,
    required this.unitId,
    this.email,
    this.phone,
    this.dob,
    this.gender,
    this.memberRoleId,
  });
}

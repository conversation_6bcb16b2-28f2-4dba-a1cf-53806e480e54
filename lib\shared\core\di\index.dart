/// Dependency Injection Index - Export all DI components
///
/// This file exports all dependency injection related components including
/// the service locator and all DI modules.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/di/index.dart';
/// 
/// // Setup service locator
/// await setupServiceLocator();
/// 
/// // Use dependencies
/// final config = getIt<AppConfig>();
/// final storage = getIt<SecureStorageService>();
/// final networkInfo = getIt<NetworkInfo>();
/// 
/// // Check status
/// final isReady = areCoreDependenciesRegistered();
/// ```

// ============================================================================
// DEPENDENCY INJECTION EXPORTS
// ============================================================================

/// Main service locator setup and management (legacy)
export 'service_locator.dart';

/// Shared service locator for multi-app architecture
export 'shared_service_locator.dart';

/// Core dependencies module
export 'modules/core_module.dart' hide getIt;

/// Network dependencies module
export 'modules/network_module.dart' hide getIt;

/// Authentication dependencies module
export 'modules/auth_module.dart' hide getIt;

/// User management dependencies module
export 'modules/user_module.dart' hide getIt;

/// Face detection dependencies module
export 'modules/face_module.dart' hide getIt;

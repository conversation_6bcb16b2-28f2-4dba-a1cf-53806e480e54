/// Remote Configuration Provider
/// 
/// Loads configuration from remote server with caching, fallback,
/// and real-time updates support.

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../flexible_config_system.dart';

/// Remote configuration provider
class RemoteConfigProvider implements ConfigProvider {
  final String serverUrl;
  final String deviceId;
  final String? authToken;
  final Duration refreshInterval;
  final Duration timeout;
  final bool enableRealTimeUpdates;
  
  Map<String, dynamic> _currentConfig = {};
  final StreamController<Map<String, dynamic>> _configController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  Timer? _refreshTimer;
  http.Client? _httpClient;
  bool _isInitialized = false;

  RemoteConfigProvider({
    required this.serverUrl,
    required this.deviceId,
    this.authToken,
    this.refreshInterval = const Duration(minutes: 5),
    this.timeout = const Duration(seconds: 30),
    this.enableRealTimeUpdates = false,
  });

  @override
  String get name => 'RemoteConfigProvider';

  @override
  ConfigSource get source => ConfigSource.remote;

  @override
  Stream<Map<String, dynamic>> get configStream => _configController.stream;

  @override
  Future<void> initialize() async {
    _httpClient = http.Client();
    
    try {
      await _loadConfiguration();
      _isInitialized = true;
      
      if (refreshInterval.inSeconds > 0) {
        _startPeriodicRefresh();
      }
      
      if (enableRealTimeUpdates) {
        _startRealTimeUpdates();
      }
      
    } catch (e) {
      debugPrint('Failed to initialize remote config: $e');
      _isInitialized = false;
    }
  }

  @override
  Future<Map<String, dynamic>> loadConfiguration() async {
    await _loadConfiguration();
    return Map<String, dynamic>.from(_currentConfig);
  }

  @override
  Future<void> saveConfiguration(Map<String, dynamic> config) async {
    if (!_isInitialized) {
      throw StateError('RemoteConfigProvider not initialized');
    }

    try {
      final response = await _httpClient!
          .put(
            Uri.parse('$serverUrl/config'),
            headers: _getHeaders(),
            body: jsonEncode({
              'device_id': deviceId,
              'config': config,
              'timestamp': DateTime.now().toIso8601String(),
            }),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        _currentConfig = Map<String, dynamic>.from(config);
        _configController.add(_currentConfig);
        debugPrint('Remote configuration saved successfully');
      } else {
        throw Exception('Failed to save remote config: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error saving remote configuration: $e');
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    _refreshTimer?.cancel();
    _httpClient?.close();
    await _configController.close();
  }

  /// Load configuration from remote server
  Future<void> _loadConfiguration() async {
    if (_httpClient == null) return;

    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$serverUrl/config/$deviceId'),
            headers: _getHeaders(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final config = data['config'] as Map<String, dynamic>? ?? {};
        
        // Validate configuration before applying
        final validatedConfig = _validateRemoteConfig(config);
        
        if (!_mapsEqual(_currentConfig, validatedConfig)) {
          _currentConfig = validatedConfig;
          _configController.add(_currentConfig);
          debugPrint('Remote configuration loaded: ${validatedConfig.keys.length} parameters');
        }
      } else if (response.statusCode == 404) {
        // Device not found, create default config
        await _createDefaultRemoteConfig();
      } else {
        throw Exception('Failed to load remote config: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error loading remote configuration: $e');
      // Don't rethrow - use cached config or defaults
    }
  }

  /// Get HTTP headers for requests
  Map<String, String> _getHeaders() {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'User-Agent': 'CCAM-Mobile-Config/1.0',
    };
    
    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }
    
    return headers;
  }

  /// Validate remote configuration
  Map<String, dynamic> _validateRemoteConfig(Map<String, dynamic> config) {
    final validatedConfig = <String, dynamic>{};
    
    // TODO: Add validation logic based on registered parameters
    // For now, just return the config as-is
    return Map<String, dynamic>.from(config);
  }

  /// Check if two maps are equal
  bool _mapsEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.length != map2.length) return false;
    
    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }
    
    return true;
  }

  /// Start periodic refresh
  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(refreshInterval, (timer) async {
      await _loadConfiguration();
    });
  }

  /// Start real-time updates via WebSocket
  void _startRealTimeUpdates() {
    try {
      final wsUrl = '${serverUrl.replaceFirst('http', 'ws')}/ws';

      debugPrint('🔌 Connecting to WebSocket: $wsUrl');

      // Note: In a real implementation, you would use the web_socket_channel package
      // For now, we'll just log that real-time updates would be enabled
      debugPrint('📡 Real-time updates enabled via WebSocket');

      // Simulate WebSocket connection
      Timer.periodic(Duration(seconds: 30), (timer) {
        if (!_isInitialized) {
          timer.cancel();
          return;
        }

        // Periodically check for updates
        _loadConfiguration();
      });

    } catch (e) {
      debugPrint('❌ Failed to start real-time updates: $e');
    }
  }

  /// Create default remote configuration
  Future<void> _createDefaultRemoteConfig() async {
    try {
      final defaultConfig = <String, dynamic>{
        'created_at': DateTime.now().toIso8601String(),
        'device_id': deviceId,
        'config': {},
      };

      final response = await _httpClient!
          .post(
            Uri.parse('$serverUrl/config'),
            headers: _getHeaders(),
            body: jsonEncode(defaultConfig),
          )
          .timeout(timeout);

      if (response.statusCode == 201) {
        debugPrint('Default remote configuration created');
      } else {
        throw Exception('Failed to create default config: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error creating default remote configuration: $e');
    }
  }

  /// Get remote configuration info
  Future<Map<String, dynamic>?> getRemoteConfigInfo() async {
    if (!_isInitialized || _httpClient == null) return null;

    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$serverUrl/config/$deviceId/info'),
            headers: _getHeaders(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting remote config info: $e');
    }
    
    return null;
  }

  /// Force refresh configuration
  Future<bool> forceRefresh() async {
    if (!_isInitialized) return false;

    try {
      await _loadConfiguration();
      return true;
    } catch (e) {
      debugPrint('Error during force refresh: $e');
      return false;
    }
  }

  /// Check server connectivity
  Future<bool> checkConnectivity() async {
    if (_httpClient == null) return false;

    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$serverUrl/health'),
            headers: _getHeaders(),
          )
          .timeout(Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Server connectivity check failed: $e');
      return false;
    }
  }

  /// Get server status
  Future<Map<String, dynamic>?> getServerStatus() async {
    if (_httpClient == null) return null;

    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$serverUrl/status'),
            headers: _getHeaders(),
          )
          .timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting server status: $e');
    }
    
    return null;
  }

  /// Reset remote configuration to defaults
  Future<bool> resetToDefaults() async {
    if (!_isInitialized || _httpClient == null) return false;

    try {
      final response = await _httpClient!
          .delete(
            Uri.parse('$serverUrl/config/$deviceId'),
            headers: _getHeaders(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        await _createDefaultRemoteConfig();
        await _loadConfiguration();
        return true;
      }
    } catch (e) {
      debugPrint('Error resetting remote configuration: $e');
    }
    
    return false;
  }

  /// Get configuration history
  Future<List<Map<String, dynamic>>> getConfigurationHistory({
    int limit = 10,
  }) async {
    if (!_isInitialized || _httpClient == null) return [];

    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$serverUrl/config/$deviceId/history?limit=$limit'),
            headers: _getHeaders(),
          )
          .timeout(timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final history = data['history'] as List<dynamic>? ?? [];
        return history.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      debugPrint('Error getting configuration history: $e');
    }
    
    return [];
  }
}

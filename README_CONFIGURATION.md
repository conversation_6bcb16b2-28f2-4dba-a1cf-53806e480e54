# 🔧 Flexible Configuration System

A comprehensive, type-safe, and runtime-configurable solution for managing application parameters with support for multiple configuration sources, real-time updates, and validation.

## ✨ Features

- **🎯 Type-Safe Configuration**: Strongly typed parameters with automatic validation
- **🔄 Multiple Sources**: Runtime, Environment Variables, Files, Remote Server
- **⚡ Real-Time Updates**: Live configuration changes with change notifications
- **🛡️ Validation**: Comprehensive parameter validation with constraints
- **🌐 Remote Management**: Centralized configuration management via REST API
- **📱 Admin Interface**: Built-in UI for configuration management
- **🔍 Monitoring**: Configuration status monitoring and error reporting
- **💾 Persistence**: Automatic persistence with backup/restore capabilities

## 🚀 Quick Start

### 1. Installation

Add the configuration system to your app by initializing it in your `main()` function:

```dart
import 'package:ccam_mobile_2/shared/core/config/config_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize configuration system
  await ConfigInitializer.initializeForTerminal(
    deviceId: 'terminal_001',
    enableRemoteConfig: true,
    remoteServerUrl: 'https://config.ccam.com',
  );
  
  runApp(MyApp());
}
```

### 2. Basic Usage

```dart
import 'package:ccam_mobile_2/shared/core/config/config_helper.dart';

// Get configuration values
final faceQuality = ConfigHelper.minFaceQualityForDetection;
final apiUrl = ConfigHelper.baseApiUrl;
final primaryColor = ConfigHelper.primaryColor;

// Set configuration values at runtime
await ConfigHelper.setValue(
  'face_detection.min_quality_detection', 
  0.6
);

// Listen to configuration changes
ConfigHelper.changeStream.listen((event) {
  print('${event.key} changed from ${event.oldValue} to ${event.newValue}');
});
```

## 📋 Configuration Categories

### 🎭 Face Detection
- Quality thresholds for detection and recognition
- Timing parameters for throttling and timeouts
- Image processing settings
- Performance optimization parameters

### 🌐 Network
- API endpoints and base URLs
- Timeout and retry configurations
- Device identification
- Connection parameters

### 🎨 UI/UX
- Colors and themes
- Dimensions and spacing
- Animation durations
- Layout parameters

### ⚡ Performance
- Frame rates for different modes
- Power saving configurations
- Resource optimization settings
- Brightness controls

### 📷 Camera
- Resolution and aspect ratio
- Capture settings
- Processing parameters

### 🔒 Security
- Authentication settings
- Password requirements
- Biometric configuration

## 🔧 Configuration Sources

### 1. Runtime Configuration (Highest Priority)
```dart
// Change values at runtime
await ConfigHelper.setValue('ui.primary_color', 0xFF1976D2);
```

### 2. Environment Variables
```bash
export CCAM_FACE_MIN_QUALITY_DETECTION=0.5
export CCAM_API_BASE_URL=https://production-api.ccam.com
export CCAM_DEVICE_ID=terminal_main_entrance
```

### 3. Configuration Files
```json
// assets/config/app_config.json
{
  "face_detection": {
    "min_quality_detection": 0.5,
    "recognition_throttle_duration": 5000
  },
  "network": {
    "base_api_url": "https://api.ccam.com/api/v3.1"
  },
  "ui": {
    "primary_color": "0xFF1976D2"
  }
}
```

### 4. Remote Configuration
```dart
// Server-managed configuration
await ConfigInitializer.initializeForTerminal(
  enableRemoteConfig: true,
  remoteServerUrl: 'https://config-server.ccam.com',
  authToken: 'your-auth-token',
);
```

## 🖥️ Admin Interface

### Full Configuration Screen
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => AdminConfigScreen(),
  ),
);
```

### Quick Configuration Dashboard
```dart
Widget build(BuildContext context) {
  return Scaffold(
    body: ConfigDashboard(
      onConfigChanged: () => setState(() {}),
    ),
  );
}
```

### Configuration Status Widget
```dart
AppBar(
  actions: [
    ConfigStatusIndicator(
      onTap: () => _showConfigStatus(),
    ),
  ],
)
```

## 🌐 Remote Configuration Server

### Start Server
```dart
final server = RemoteConfigServer(port: 3000);
await server.start();
```

### API Endpoints
- `GET /config/{deviceId}` - Get device configuration
- `PUT /config/{deviceId}` - Update device configuration
- `POST /config` - Create new device configuration
- `DELETE /config/{deviceId}` - Delete device configuration
- `GET /health` - Server health check
- `GET /status` - Server status and statistics

### WebSocket Support
Real-time configuration updates via WebSocket connection at `/ws`.

## 🔄 Synchronization

### Manual Sync
```dart
import 'package:ccam_mobile_2/shared/core/config/services/config_sync_service.dart';

final syncService = ConfigSyncService.instance;

// Sync with remote
final result = await syncService.syncWithRemote();

// Push local changes to remote
await syncService.pushToRemote();

// Pull remote changes
await syncService.pullFromRemote();
```

### Auto Sync
```dart
await syncService.initialize(
  enableAutoSync: true,
  autoSyncInterval: Duration(minutes: 5),
);
```

## ✅ Testing

### Unit Tests
```bash
flutter test test/shared/core/config/
```

### Integration Tests
```dart
// Test configuration in different environments
await ConfigInitializer.initializeForTesting(
  testConfig: {
    'face_detection.min_quality_detection': 0.8,
    'network.request_timeout': 15000,
  },
);
```

## 📊 Monitoring

### Configuration Status
```dart
// Get system statistics
final stats = ConfigInitializer.getStatistics();
print('Total parameters: ${stats['total_parameters']}');
print('Active providers: ${stats['active_providers']}');

// Validate configuration
final errors = ConfigInitializer.validate();
if (errors.isNotEmpty) {
  print('Configuration errors: ${errors.join(', ')}');
}
```

### Real-time Monitoring
```dart
// Monitor configuration changes
ConfigHelper.changeStream.listen((event) {
  logConfigChange(event.key, event.oldValue, event.newValue);
});

// Monitor sync status
ConfigSyncService.instance.syncStream.listen((result) {
  handleSyncResult(result);
});
```

## 🛠️ Development Tools

### Configuration Export/Import
```dart
// Export current configuration
final config = ConfigHelper.export();
await Clipboard.setData(ClipboardData(text: jsonEncode(config)));

// Import configuration
final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
final config = jsonDecode(clipboardData!.text!);
await ConfigHelper.import(config);
```

### Environment Template Generation
```dart
final envProvider = EnvironmentConfigProvider();
final template = envProvider.generateEnvironmentTemplate();
print(template); // Copy to .env file
```

### Backup and Restore
```dart
// Create backup
final backup = ConfigHelper.export();

// Restore from backup
await ConfigHelper.import(backup);

// Reset to defaults
await ConfigInitializer.resetToDefaults();
```

## 🔧 Advanced Usage

### Custom Configuration Parameters
```dart
// Register new parameter
ConfigParametersRegistry.register(
  ConfigParameter<String>(
    key: 'custom.my_parameter',
    category: 'custom',
    description: 'My custom parameter',
    type: ConfigValueType.string,
    defaultValue: 'default_value',
    validator: (value) => value.isNotEmpty,
  ),
);
```

### Custom Configuration Provider
```dart
class MyConfigProvider implements ConfigProvider {
  @override
  String get name => 'MyConfigProvider';
  
  @override
  ConfigSource get source => ConfigSource.remote;
  
  // Implement required methods...
}
```

## 📚 Documentation

- [Full Documentation](docs/configuration_system.md)
- [API Reference](docs/api_reference.md)
- [Migration Guide](docs/migration_guide.md)
- [Best Practices](docs/best_practices.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This configuration system is part of the CCAM Mobile project and follows the same license terms.

## 🆘 Support

For issues and questions:
- Check the [documentation](docs/configuration_system.md)
- Review [common issues](docs/troubleshooting.md)
- Create an issue in the repository

---

**Made with ❤️ for flexible and maintainable configuration management**

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Mixin for handling power management functionality in StreamScreen
/// This separates power saving logic from the main screen
mixin StreamScreenPowerManagementMixin {
  // Abstract getters that must be implemented by the using class
  bool get isResourceOptimized;
  bool get isExtremePowerSaving;
  DateTime? get lastFaceDetectedTime;
  Timer? get clockTimer;
  
  // Abstract setters/methods that must be implemented by the using class
  void setResourceOptimized(bool value);
  void setExtremePowerSaving(bool value);
  void initializeClock();

  /// Apply resource optimization when no face is detected for a while
  void applyResourceOptimization() {
    if (isResourceOptimized) return;

    setResourceOptimized(true);

    if (kDebugMode) {
      final timeSinceLastFace = lastFaceDetectedTime != null
          ? DateTime.now().difference(lastFaceDetectedTime!).inSeconds
          : 0;
      print('⚡ Applying resource optimization after ${timeSinceLastFace}s without face');
      print('🔋 Reducing CPU usage and frame rate');
    }

    // Reduce face detection frequency
    // Reduce UI update frequency
    // Lower camera resolution if possible
    // Pause non-essential animations
  }

  /// Apply extreme power saving mode (screen off)
  void applyExtremePowerSaving() {
    if (isExtremePowerSaving) return;

    setResourceOptimized(true); // Also apply regular optimization
    setExtremePowerSaving(true);

    if (kDebugMode) {
      final timeSinceLastFace = lastFaceDetectedTime != null
          ? DateTime.now().difference(lastFaceDetectedTime!).inSeconds
          : 0;
      print('🌙 Applying extreme power saving - screen off mode after ${timeSinceLastFace}s without face');
      print('🔍 Face detection will continue for wake-up capability');
    }

    // Reduce screen brightness to minimum for power saving
    setScreenBrightness(0.0);

    // Reduce all UI updates
    // Pause non-essential timers
    clockTimer?.cancel();

    // Keep only camera and face detection running for wake-up
    // Face detection callback will still work to detect wake-up
  }

  /// Resume from resource optimization when face is detected
  void resumeFromResourceOptimization() {
    bool wasExtremePowerSaving = isExtremePowerSaving;

    if (!isResourceOptimized && !isExtremePowerSaving) return;

    setResourceOptimized(false);
    setExtremePowerSaving(false);

    if (kDebugMode) {
      if (wasExtremePowerSaving) {
        print('🌅 Resuming from extreme power saving - face detected');
      } else {
        print('⚡ Resuming from resource optimization - face detected');
      }
    }

    // Restore screen brightness to maximum when exiting power saving mode
    if (wasExtremePowerSaving) {
      setScreenBrightness(1.0); // Restore to maximum brightness
      initializeClock(); // Restart clock
      
      if (kDebugMode) {
        print('💡 Screen brightness restored to maximum');
      }
    }

    // Restore normal face detection frequency
    // Note: This is a simplified approach. In production, you might want to:
    // - Restore camera resolution
    // - Restore frame rate
    // - Resume face detection
    // - Use normal quality settings

    // For now, we'll just log the restoration
    // Future enhancement: implement actual resource restoration
  }

  /// Set screen brightness (0.0 to 1.0)
  void setScreenBrightness(double brightness) {
    try {
      // Clamp brightness value between 0.0 and 1.0
      final clampedBrightness = brightness.clamp(0.0, 1.0);
      
      // Set system brightness
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.black,
          statusBarColor: Colors.black,
        ),
      );
      
      // Note: For actual brightness control, you would need to use a plugin like
      // screen_brightness or implement platform-specific brightness control
      // For now, we'll just log the brightness change
      
      if (kDebugMode) {
        print('💡 Setting screen brightness to ${(clampedBrightness * 100).toStringAsFixed(0)}%');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set screen brightness: $e');
      }
    }
  }

  /// Check if power saving should be applied based on time without face
  void checkPowerSavingConditions() {
    if (lastFaceDetectedTime == null) return;

    final timeSinceLastFace = DateTime.now().difference(lastFaceDetectedTime!);

    // Apply resource optimization after 10 seconds without face
    if (timeSinceLastFace.inSeconds >= 10 && !isResourceOptimized) {
      applyResourceOptimization();
    }

    // Apply extreme power saving after 30 seconds without face
    if (timeSinceLastFace.inSeconds >= 30 && !isExtremePowerSaving) {
      applyExtremePowerSaving();
    }
  }

  /// Get power saving status for debugging
  Map<String, dynamic> getPowerSavingStatus() {
    return {
      'isResourceOptimized': isResourceOptimized,
      'isExtremePowerSaving': isExtremePowerSaving,
      'lastFaceDetectedTime': lastFaceDetectedTime?.toIso8601String(),
      'timeSinceLastFace': lastFaceDetectedTime != null
          ? DateTime.now().difference(lastFaceDetectedTime!).inSeconds
          : null,
    };
  }

  /// Log power saving status for debugging
  void logPowerSavingStatus() {
    if (!kDebugMode) return;

    final status = getPowerSavingStatus();
    print('🔋 Power Saving Status:');
    print('   Resource Optimized: ${status['isResourceOptimized']}');
    print('   Extreme Power Saving: ${status['isExtremePowerSaving']}');
    print('   Time Since Last Face: ${status['timeSinceLastFace']}s');
  }
}

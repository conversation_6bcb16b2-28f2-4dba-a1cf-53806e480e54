library;

/// Configurable API constants
///
/// This class now uses the flexible configuration system instead of hardcoded values.
/// All API endpoints and configurations can be configured through environment variables,
/// configuration files, or runtime changes through the admin interface.

import '../config/config_helper.dart';

class ApiConstants {
  // Base URLs
  static String get baseUrl => ConfigHelper.getValue('network.base_url', 'http://192.168.137.1:5000');
  static String get apiVersion => ConfigHelper.getValue('network.api_version', '/api');
  static String get apiBaseUrl => '$baseUrl$apiVersion';

  // Identity Management Endpoints
  static String get identityBaseUrl => '$apiBaseUrl/identity';
  static String get loginEndpoint => '$identityBaseUrl/login';
  static String get logoutEndpoint => '$identityBaseUrl/logout';
  static String get logoutAllEndpoint => '$identityBaseUrl/logout-all';
  static String get refreshTokenEndpoint => '$identityBaseUrl/refresh';
  static String get verifyTokenEndpoint => '$identityBaseUrl/verify-token';
  static String get profileEndpoint => '$identityBaseUrl/me';
  static String get sessionsEndpoint => '$identityBaseUrl/sessions';

  // User Management Endpoints
  static String get usersEndpoint => '$apiBaseUrl/users';

  // Face Detection Endpoints
  static String get faceDetectionEndpoint => '$apiBaseUrl/face/detect';
  static String get faceValidationEndpoint => '$apiBaseUrl/face/validate';
  static String get faceCaptureEndpoint => '$apiBaseUrl/face/capture';

  // Headers
  static String get contentTypeHeader => 'Content-Type';
  static String get authorizationHeader => 'Authorization';
  static String get acceptHeader => 'Accept';

  // Header Values
  static String get applicationJson => 'application/json';
  static String get bearerPrefix => 'Bearer ';

  // Timeouts
  static int get connectTimeout => ConfigHelper.getValue('network.connect_timeout', 30000);
  static int get receiveTimeout => ConfigHelper.getValue('network.receive_timeout', 30000);
  static int get sendTimeout => ConfigHelper.getValue('network.send_timeout', 30000);

  // Status Codes
  static const int successCode = 200;
  static const int createdCode = 201;
  static const int badRequestCode = 400;
  static const int unauthorizedCode = 401;
  static const int forbiddenCode = 403;
  static const int notFoundCode = 404;
  static const int internalServerErrorCode = 500;

  // Authentication Configuration
  /// Determines where to get refresh token from
  /// true: Get refresh token from HTTP cookies (default)
  /// false: Get refresh token from response body
  static const bool useRefreshTokenFromCookie = true;

  /// Cookie name for refresh token
  static const String refreshTokenCookieName = 'refresh_token';
}

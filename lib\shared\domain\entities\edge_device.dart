class EdgeDevice {
  final String id;
  final String cameraId;
  final String name;
  final String type;
  final String ipAddress;
  final String macAddress;
  final String firmwareVersion;
  final bool isAttendanceDevice;
  final String attendanceMode;
  final String preferredStreamMode;
  final String status;
  final DateTime createdAt;
  final String createdBy;

  EdgeDevice({
    required this.id,
    required this.cameraId,
    required this.name,
    required this.type,
    required this.ipAddress,
    required this.macAddress,
    required this.firmwareVersion,
    required this.isAttendanceDevice,
    required this.attendanceMode,
    required this.preferredStreamMode,
    required this.status,
    required this.createdAt,
    required this.createdBy,
  });
}

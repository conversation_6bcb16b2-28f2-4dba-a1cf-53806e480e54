import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_registration_provider.dart';
import '../presentation/screens/terminal_screen.dart';

/// Example demonstrating the complete terminal application
void main() {
  runApp(const TerminalAppExample());
}

class TerminalAppExample extends StatelessWidget {
  const TerminalAppExample({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => DeviceRegistrationProvider(),
        ),
      ],
      child: MaterialApp(
        title: 'Terminal App Example',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            primary: Colors.blue,
          ),
          useMaterial3: true,
          fontFamily: 'System',
        ),
        home: const TerminalAppExampleHome(),
      ),
    );
  }
}

class TerminalAppExampleHome extends StatefulWidget {
  const TerminalAppExampleHome({super.key});

  @override
  State<TerminalAppExampleHome> createState() => _TerminalAppExampleHomeState();
}

class _TerminalAppExampleHomeState extends State<TerminalAppExampleHome> {
  @override
  void initState() {
    super.initState();
    _showWelcomeDialog();
  }

  void _showWelcomeDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('🔐 Terminal App Example'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome to the comprehensive Flutter Terminal Application!',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('This example demonstrates:'),
              SizedBox(height: 8),
              Text('✅ Complete device registration flow'),
              Text('✅ Secure communication with server'),
              Text('✅ Command handling architecture'),
              Text('✅ Real-time status monitoring'),
              Text('✅ Manual command testing'),
              Text('✅ Command history tracking'),
              SizedBox(height: 16),
              Text(
                'Make sure the test server is running:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text('cd lib/packages/relay_controller/test_server'),
              Text('npm install && npm start'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Get Started'),
            ),
          ],
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return const TerminalScreen();
  }
}

/// Standalone example for testing individual components
class ComponentTestScreen extends StatelessWidget {
  const ComponentTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Component Tests'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTestSection(
              'Device Registration Provider',
              'Test device registration states and flows',
              () => _testDeviceRegistration(context),
            ),
            const SizedBox(height: 16),
            _buildTestSection(
              'Command Handler',
              'Test command execution and history',
              () => _testCommandHandler(context),
            ),
            const SizedBox(height: 16),
            _buildTestSection(
              'Secure Communication',
              'Test secure message sending',
              () => _testSecureComm(context),
            ),
            const SizedBox(height: 16),
            _buildTestSection(
              'UI Components',
              'Test individual UI widgets',
              () => _testUIComponents(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection(String title, String description, VoidCallback onTap) {
    return Card(
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _testDeviceRegistration(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Registration Test'),
        content: const Text(
          'This would test:\n'
          '• Registration flow states\n'
          '• Error handling\n'
          '• Credential storage\n'
          '• Token refresh\n'
          '• Heartbeat mechanism',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _testCommandHandler(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Command Handler Test'),
        content: const Text(
          'This would test:\n'
          '• Command execution\n'
          '• Relay control integration\n'
          '• Face auth simulation\n'
          '• Status requests\n'
          '• Command history\n'
          '• Error handling',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _testSecureComm(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Secure Communication Test'),
        content: const Text(
          'This would test:\n'
          '• Message signing\n'
          '• Transport protocols\n'
          '• Authentication\n'
          '• Message verification\n'
          '• Network error handling',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _testUIComponents(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('UI Components Test'),
        content: const Text(
          'This would test:\n'
          '• Device status widget\n'
          '• Command history widget\n'
          '• Manual command widget\n'
          '• Registration screen\n'
          '• Status indicators',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Example showing how to integrate with existing apps
class IntegrationExample extends StatelessWidget {
  const IntegrationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Integration Example'),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Integration with Existing Apps',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'To integrate the terminal functionality into your existing app:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text('1. Add providers to your main app:'),
            SizedBox(height: 8),
            CodeBlock(
              code: '''
MultiProvider(
  providers: [
    ChangeNotifierProvider(
      create: (context) => DeviceRegistrationProvider(),
    ),
    // Your existing providers...
  ],
  child: YourApp(),
)''',
            ),
            SizedBox(height: 16),
            Text('2. Use terminal components in your screens:'),
            SizedBox(height: 8),
            CodeBlock(
              code: '''
// Show device status
DeviceStatusWidget()

// Manual command testing
ManualCommandWidget()

// Command history
CommandHistoryWidget()''',
            ),
            SizedBox(height: 16),
            Text('3. Initialize command handler:'),
            SizedBox(height: 8),
            CodeBlock(
              code: '''
final commandHandler = DeviceCommandHandler();
await commandHandler.initialize(secureComm);''',
            ),
          ],
        ),
      ),
    );
  }
}

class CodeBlock extends StatelessWidget {
  final String code;

  const CodeBlock({super.key, required this.code});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(
        code,
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 12,
        ),
      ),
    );
  }
}

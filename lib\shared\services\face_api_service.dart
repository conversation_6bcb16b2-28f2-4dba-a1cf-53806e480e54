import 'dart:io';
import 'dart:convert';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import '../core/network/api_client.dart';
import '../core/config/app_config.dart';
import 'api_endpoints.dart';
import '../models/face_capture_result.dart';
import '../providers/face_detection_provider.dart';

/// Service để xử lý face registration thông qua Update User API
class FaceApiService {
  final ApiClient _apiClient = GetIt.instance<ApiClient>();

  /// Đăng ký khuôn mặt cho user thông qua Update User API
  ///
  /// [userId] - ID của user cần đăng ký khuôn mặt
  /// [faceCaptureResult] - Kết quả face capture chứa danh sách hình ảnh
  ///
  /// Returns: Map chứa thông tin response từ API
  Future<Map<String, dynamic>> registerUserFace({
    required String userId,
    required FaceCaptureResult faceCaptureResult,
  }) async {
    try {
      // Sử dụng Update User API để upload face images
      return await updateUserWithFaceImages(
        userId: userId,
        faceCaptureResult: faceCaptureResult,
      );
    } catch (e) {
      throw Exception('Lỗi khi đăng ký khuôn mặt: $e');
    }
  }

  /// Cập nhật user với face images thông qua Update User API
  ///
  /// [userId] - ID của user cần cập nhật
  /// [faceCaptureResult] - Kết quả face capture chứa danh sách hình ảnh
  /// [additionalFields] - Các field khác cần cập nhật (optional)
  ///
  /// Returns: Map chứa thông tin response từ API
  Future<Map<String, dynamic>> updateUserWithFaceImages({
    required String userId,
    required FaceCaptureResult faceCaptureResult,
    Map<String, dynamic>? additionalFields,
  }) async {
    try {
      // Chuẩn bị fields cho update user request
      final Map<String, dynamic> fields = additionalFields ?? <String, dynamic>{};

      // Chuẩn bị danh sách face image files
      final List<MapEntry<String, File>> files = [];

      for (final direction in faceCaptureResult.capturedDirections) {
        final imagePath = faceCaptureResult.getImagePath(direction);
        if (imagePath != null && File(imagePath).existsSync()) {
          final file = File(imagePath);
          // Map direction names to API field names (front, left, right, top, bottom)
          final fieldName = _mapDirectionToFieldName(direction);
          files.add(MapEntry(fieldName, file));
        }
      }

      if (files.isEmpty) {
        throw Exception('Không có hình ảnh hợp lệ để upload');
      }

      // Sử dụng Update User API endpoint
      final endpoint = ApiEndpoints.updateUser(userId);

      final response = await _uploadMultipleFiles(
        endpoint: endpoint,
        fields: fields,
        files: files,
      );

      return response;
    } catch (e) {
      throw Exception('Lỗi khi cập nhật user với face images: $e');
    }
  }

  /// Xóa khuôn mặt đã đăng ký
  Future<Map<String, dynamic>> deleteFace(String faceId) async {
    try {
      final response = await _apiClient.delete(
        ApiEndpoints.deleteFace(faceId),
      );

      return response;
    } catch (e) {
      throw Exception('Lỗi khi xóa khuôn mặt: $e');
    }
  }

  /// Lấy danh sách khuôn mặt đã đăng ký của user
  Future<Map<String, dynamic>> getRegisteredFaces({String? userId}) async {
    try {
      final queryParams = userId != null ? {'user_id': userId} : null;

      final response = await _apiClient.get(
        ApiEndpoints.registeredFaces,
        queryParameters: queryParams,
      );

      return response;
    } catch (e) {
      throw Exception('Lỗi khi lấy danh sách khuôn mặt: $e');
    }
  }

  /// Helper method để upload multiple files với Update User API
  Future<Map<String, dynamic>> _uploadMultipleFiles({
    required String endpoint,
    required Map<String, dynamic> fields,
    required List<MapEntry<String, File>> files,
  }) async {
    try {
      // Tạo custom multipart request với PUT method
      return await _createMultipartPutRequest(
        endpoint: endpoint,
        fields: fields,
        files: files,
      );
    } catch (e) {
      throw Exception('Lỗi khi upload files: $e');
    }
  }

  /// Tạo custom multipart PUT request cho Update User API
  Future<Map<String, dynamic>> _createMultipartPutRequest({
    required String endpoint,
    required Map<String, dynamic> fields,
    required List<MapEntry<String, File>> files,
  }) async {
    try {
      // Build URI từ endpoint sử dụng config
      final appConfig = AppConfig();
      final baseUrl = appConfig.baseApiUrl;
      final uri = Uri.parse('$baseUrl$endpoint');

      // Tạo multipart request với PUT method
      final request = http.MultipartRequest('PUT', uri);

      // Thêm headers từ ApiClient
      request.headers['Content-Type'] = 'multipart/form-data';
      if (_apiClient.accessToken != null) {
        request.headers['Authorization'] = 'Bearer ${_apiClient.accessToken}';
      }

      // Thêm data fields
      fields.forEach((key, value) {
        request.fields[key] = value.toString();
      });

      // Thêm tất cả face image files
      for (final fileEntry in files) {
        final fieldName = fileEntry.key;
        final file = fileEntry.value;

        final fileName = file.path.split('/').last;

        // Detect MIME type from file extension
        final mimeType = lookupMimeType(file.path) ?? 'image/jpeg';

        final multipartFile = await http.MultipartFile.fromPath(
          fieldName,
          file.path,
          filename: fileName,
          contentType: MediaType.parse(mimeType),
        );
        request.files.add(multipartFile);
      }

      // Gửi request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Parse response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = response.body.isNotEmpty
            ? jsonDecode(response.body) as Map<String, dynamic>
            : <String, dynamic>{};
        return responseData;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi khi tạo multipart request: $e');
    }
  }

  /// Validate face capture result trước khi gửi API
  bool validateFaceCaptureResult(FaceCaptureResult result) {
    // Kiểm tra có ít nhất 1 hình ảnh
    if (!result.hasImages) {
      return false;
    }

    // Kiểm tra tất cả files có tồn tại không
    if (!result.validateImages()) {
      return false;
    }

    // Kiểm tra có ít nhất hình front (bắt buộc)
    if (!result.hasDirection(FaceDirection.front)) {
      return false;
    }

    return true;
  }

  /// Tạo summary cho face registration
  Map<String, dynamic> createFaceRegistrationSummary(FaceCaptureResult result) {
    return {
      'total_images': result.imageCount,
      'directions': result.capturedDirections.map((d) => d.name).toList(),
      'is_complete': result.isComplete,
      'has_front': result.hasDirection(FaceDirection.front),
      'timestamp': result.timestamp.toIso8601String(),
      'quality_summary': {
        'all_valid': result.validateImages(),
        'representative_image': result.representativeImagePath,
      },
    };
  }

  /// Map FaceDirection to API field names (now they match directly)
  String _mapDirectionToFieldName(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return 'front';
      case FaceDirection.left:
        return 'left';
      case FaceDirection.right:
        return 'right';
      case FaceDirection.top:
        return 'top';
      case FaceDirection.bottom:
        return 'bottom';
      case FaceDirection.unknown:
        return 'unknown';
    }
  }

  /// Lấy URL để stream/download face image
  ///
  /// [imageUrl] - Image URL từ response API (ví dụ: "687070110e881343cf79ab3b")
  ///
  /// Returns: Full URL để lấy image
  String getFaceImageUrl(String imageUrl) {
    final appConfig = AppConfig();
    final baseUrl = appConfig.baseApiUrl;
    final endpoint = ApiEndpoints.imageStream(imageUrl);
    return '$baseUrl$endpoint';
  }

  /// Lấy face image data từ imageUrl
  ///
  /// [imageUrl] - Image URL từ response API
  ///
  /// Returns: Image data as bytes
  Future<List<int>?> getFaceImageData(String imageUrl) async {
    try {
      final url = getFaceImageUrl(imageUrl);
      final uri = Uri.parse(url);

      // Tạo request với authorization header
      final request = http.Request('GET', uri);
      if (_apiClient.accessToken != null) {
        request.headers['Authorization'] = 'Bearer ${_apiClient.accessToken}';
      }

      // Gửi request
      final streamedResponse = await request.send();

      if (streamedResponse.statusCode >= 200 && streamedResponse.statusCode < 300) {
        // Đọc image data
        final bytes = await streamedResponse.stream.toBytes();
        return bytes;
      } else {
        throw Exception('HTTP ${streamedResponse.statusCode}: Failed to load image');
      }
    } catch (e) {
      throw Exception('Lỗi khi lấy face image: $e');
    }
  }
}

library;

/// Configurable constants for face detection and recognition in terminal app
///
/// This class now uses the flexible configuration system instead of hardcoded values.
/// All values can be configured through environment variables, configuration files,
/// or runtime changes through the admin interface.

import '../../../shared/core/config/config_helper.dart';

class FaceDetectionConstants {
  // Face Quality Thresholds
  static double get minFaceQualityForDetection => ConfigHelper.getValue(
    'face_detection.min_quality_detection', 0.4);

  static double get minFaceQualityForRecognition => ConfigHelper.getValue(
    'face_detection.min_quality_recognition', 0.4);

  static double get minFaceQualityForAvatarCapture => ConfigHelper.getValue(
    'face_detection.min_quality_avatar_capture', 0.4);

  static double get significantQualityChange => ConfigHelper.getValue(
    'face_detection.significant_quality_change', 0.1);

  // Timing Constants
  static Duration get recognitionThrottleDuration => ConfigHelper.getValue(
    'face_detection.recognition_throttle_duration', Duration(seconds: 8));

  static Duration get userDisplayTimeout => ConfigHelper.getValue(
    'face_detection.user_display_timeout', Duration(seconds: 3));

  static Duration get avatarCaptureThrottle => ConfigHelper.getValue(
    'face_detection.avatar_capture_throttle', Duration(seconds: 2));

  static Duration get avatarDisplayDuration => ConfigHelper.getValue(
    'face_detection.avatar_display_duration', Duration(seconds: 10));

  // Power Saving Constants
  static Duration get resourceOptimizationDelay => ConfigHelper.getValue(
    'performance.resource_optimization_delay', Duration(seconds: 10));

  static Duration get extremePowerSavingDelay => ConfigHelper.getValue(
    'performance.extreme_power_saving_delay', Duration(seconds: 30));

  // Face Capture Constants
  static double get avatarPaddingFactor => ConfigHelper.getValue(
    'face_detection.avatar_padding_factor', 0.3);

  static int get avatarTargetSize => ConfigHelper.getValue(
    'face_detection.avatar_target_size', 200);

  static int get avatarImageQuality => ConfigHelper.getValue(
    'face_detection.avatar_image_quality', 85);

  static int get recognitionImageQuality => ConfigHelper.getValue(
    'face_detection.recognition_image_quality', 85);

  // Recognition Constants
  static int get maxRecognitionRetries => ConfigHelper.getValue(
    'face_detection.max_recognition_retries', 3);

  static Duration get recognitionTimeout => ConfigHelper.getValue(
    'face_detection.recognition_timeout', Duration(seconds: 10));

  // UI Constants
  static double get faceGuideFrameMargin => ConfigHelper.getValue(
    'ui.guide_frame_margin', 50.0);

  static double get qualityIndicatorSize => ConfigHelper.getValue(
    'ui.quality_indicator_size', 24.0);

  static double get qualityIndicatorFontSize => ConfigHelper.getValue(
    'ui.quality_indicator_font_size', 8.0);

  // Debug Constants
  static bool get enableFaceDetectionLogs => ConfigHelper.getValue(
    'debug.enable_face_detection_logs', true);

  static bool get enableRecognitionLogs => ConfigHelper.getValue(
    'debug.enable_face_detection_logs', true); // Reuse same setting

  static bool get enableAvatarCaptureLogs => ConfigHelper.getValue(
    'debug.enable_face_detection_logs', true); // Reuse same setting

  static bool get enablePowerSavingLogs => ConfigHelper.getValue(
    'debug.enable_performance_monitoring', true);
}

/// Configurable constants for face recognition service
class FaceRecognitionConstants {
  // Image Processing
  static int get maxImageSizeBytes => ConfigHelper.getValue(
    'face_detection.max_image_size_bytes', 5 * 1024 * 1024);

  static int get compressionQuality => ConfigHelper.getValue(
    'face_detection.compression_quality', 85);

  static String get imageFormat => ConfigHelper.getValue(
    'face_detection.image_format', 'jpeg');

  // Server Communication
  static Duration get requestTimeout => ConfigHelper.getValue(
    'network.request_timeout', Duration(seconds: 15));

  static int get maxRetries => ConfigHelper.getValue(
    'network.max_retry_attempts', 3);

  static Duration get retryDelay => ConfigHelper.getValue(
    'network.retry_delay', Duration(seconds: 2));

  // Metadata
  static String get defaultDeviceId => ConfigHelper.getValue(
    'network.device_id', 'terminal_001');

  static String get sourceIdentifier => ConfigHelper.getValue(
    'face_detection.source_identifier', 'terminal_camera_stream');
}

/// Configurable constants for power management
class PowerManagementConstants {
  // Brightness Levels
  static double get maxBrightness => ConfigHelper.getValue(
    'performance.max_brightness', 1.0);

  static double get minBrightness => ConfigHelper.getValue(
    'performance.min_brightness', 0.0);

  static double get powerSavingBrightness => ConfigHelper.getValue(
    'performance.power_saving_brightness', 0.1);

  // Timing
  static Duration get faceAbsenceForOptimization => ConfigHelper.getValue(
    'performance.face_absence_for_optimization', Duration(seconds: 10));

  static Duration get faceAbsenceForExtremeSaving => ConfigHelper.getValue(
    'performance.face_absence_for_extreme_saving', Duration(seconds: 30));

  static Duration get brightnessTransitionDuration => ConfigHelper.getValue(
    'performance.brightness_transition_duration', Duration(milliseconds: 500));

  // Frame Rate Optimization
  static int get normalFrameRate => ConfigHelper.getValue(
    'performance.normal_frame_rate', 30);

  static int get optimizedFrameRate => ConfigHelper.getValue(
    'performance.optimized_frame_rate', 15);

  static int get extremePowerSavingFrameRate => ConfigHelper.getValue(
    'performance.extreme_power_saving_frame_rate', 5);
}

/// Configurable constants for UI elements
class TerminalUIConstants {
  // Avatar Display
  static double get avatarSize => ConfigHelper.getValue(
    'ui.avatar_size', 120.0);

  static double get avatarBorderRadius => ConfigHelper.getValue(
    'ui.avatar_border_radius', 8.0);

  static double get qualityBadgeSize => ConfigHelper.getValue(
    'ui.quality_badge_size', 24.0);

  // Face Guide Frame
  static double get guideFrameStrokeWidth => ConfigHelper.getValue(
    'ui.guide_frame_stroke_width', 2.0);

  static double get guideFrameCornerRadius => ConfigHelper.getValue(
    'ui.guide_frame_corner_radius', 12.0);

  static double get guideFrameMargin => ConfigHelper.getValue(
    'ui.guide_frame_margin', 50.0);

  // Progress Indicators
  static double get progressIndicatorStrokeWidth => ConfigHelper.getValue(
    'ui.progress_indicator_stroke_width', 3.0);

  static Duration get progressAnimationDuration => ConfigHelper.getValue(
    'ui.progress_animation_duration', Duration(milliseconds: 300));

  // Colors (as hex values to avoid import issues)
  static int get primaryColorValue => ConfigHelper.getValue(
    'ui.primary_color', 0xFF2196F3);

  static int get successColorValue => ConfigHelper.getValue(
    'ui.success_color', 0xFF4CAF50);

  static int get warningColorValue => ConfigHelper.getValue(
    'ui.warning_color', 0xFFFF9800);

  static int get errorColorValue => ConfigHelper.getValue(
    'ui.error_color', 0xFFF44336);

  static int get backgroundColorValue => ConfigHelper.getValue(
    'ui.background_color', 0xFF000000);

  // Face Detection UI Colors
  static int get faceDetectionProgressColorValue => ConfigHelper.getValue(
    'ui.success_color', 0xFF4CAF50); // Green instead of blue/yellow
}

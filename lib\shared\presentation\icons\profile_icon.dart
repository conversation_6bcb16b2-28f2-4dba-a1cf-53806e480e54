import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Profile icon component for TabsBar
class ProfileIcon extends StatelessWidget {
  final bool isActive;
  final double size;

  const ProfileIcon({
    super.key,
    required this.isActive,
    this.size = 18.0,
  });

  @override
  Widget build(BuildContext context) {
    final color = isActive ? '#008FD3' : '#9CA5B3';
    
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.55818 11.6112C4.4971 12.243 1.71502 13.5331 3.4095 15.1474C4.23723 15.936 5.15912 16.5 6.31815 16.5H12.9318C14.0909 16.5 15.0128 15.936 15.8405 15.1474C17.535 13.5331 14.7529 12.243 13.6918 11.6112C11.2036 10.1296 8.04639 10.1296 5.55818 11.6112Z"
            stroke="$color"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M13 4.875C13 6.73896 11.489 8.25 9.625 8.25C7.76104 8.25 6.25 6.73896 6.25 4.875C6.25 3.01104 7.76104 1.5 9.625 1.5C11.489 1.5 13 3.01104 13 4.875Z"
            stroke="$color"
            stroke-width="1.5"
          />
        </svg>''',
        width: size,
        height: size,
      ),
    );
  }
}

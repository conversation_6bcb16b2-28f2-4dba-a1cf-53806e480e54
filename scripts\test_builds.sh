#!/bin/bash

echo "🧪 Testing C-Face Multi-App Builds..."
echo "====================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Function to test build
test_build() {
    local app_name=$1
    local target_file=$2
    local description=$3
    
    echo ""
    echo "🔍 Testing $app_name build..."
    echo "Target: $target_file"
    echo "Description: $description"
    echo "----------------------------------------"
    
    # Check if target file exists
    if [ ! -f "$target_file" ]; then
        echo "❌ Target file not found: $target_file"
        return 1
    fi
    
    # Test build (analyze only)
    flutter analyze "$target_file"
    
    if [ $? -eq 0 ]; then
        echo "✅ $app_name build test passed!"
        return 0
    else
        echo "❌ $app_name build test failed!"
        return 1
    fi
}

# Clean and get dependencies
echo "🧹 Preparing for tests..."
flutter clean
flutter pub get

# Test mobile app build
test_build "Mobile App" "lib/apps/mobile/main_mobile.dart" "Mobile application with responsive UI"
MOBILE_TEST=$?

# Test terminal app build
test_build "Terminal App" "lib/apps/terminal/main_terminal.dart" "Terminal/Kiosk application with large UI"
TERMINAL_TEST=$?

# Test results summary
echo ""
echo "📊 Build Test Results:"
echo "======================"

if [ $MOBILE_TEST -eq 0 ]; then
    echo "✅ Mobile App:   PASS"
else
    echo "❌ Mobile App:   FAIL"
fi

if [ $TERMINAL_TEST -eq 0 ]; then
    echo "✅ Terminal App: PASS"
else
    echo "❌ Terminal App: FAIL"
fi

# Overall result
if [ $MOBILE_TEST -eq 0 ] && [ $TERMINAL_TEST -eq 0 ]; then
    echo ""
    echo "🎉 All build tests passed!"
    echo "Multi-app configuration is working correctly."
    exit 0
else
    echo ""
    echo "❌ Some build tests failed!"
    echo "Please check the configuration and try again."
    exit 1
fi

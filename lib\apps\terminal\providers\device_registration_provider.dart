import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/secure_comm_models.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../services/system_info_service.dart';

/// Device registration status enumeration
enum DeviceRegistrationStatus {
  unregistered,
  registering,
  registered,
  error,
  disconnected,
}

/// Device information model
class DeviceInfo {
  final String deviceId;
  final String deviceName;
  final String deviceType;
  final String location;
  final String serverUrl;
  final DateTime? registrationTime;
  final List<String> capabilities;
  final Map<String, dynamic> metadata;

  const DeviceInfo({
    required this.deviceId,
    required this.deviceName,
    required this.deviceType,
    required this.location,
    required this.serverUrl,
    this.registrationTime,
    this.capabilities = const [],
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'deviceType': deviceType,
      'location': location,
      'serverUrl': serverUrl,
      'registrationTime': registrationTime?.toIso8601String(),
      'capabilities': capabilities,
      'metadata': metadata,
    };
  }

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      deviceType: json['deviceType'] as String,
      location: json['location'] as String,
      serverUrl: json['serverUrl'] as String,
      registrationTime: json['registrationTime'] != null
          ? DateTime.parse(json['registrationTime'] as String)
          : null,
      capabilities: List<String>.from(json['capabilities'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  DeviceInfo copyWith({
    String? deviceId,
    String? deviceName,
    String? deviceType,
    String? location,
    String? serverUrl,
    DateTime? registrationTime,
    List<String>? capabilities,
    Map<String, dynamic>? metadata,
  }) {
    return DeviceInfo(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      location: location ?? this.location,
      serverUrl: serverUrl ?? this.serverUrl,
      registrationTime: registrationTime ?? this.registrationTime,
      capabilities: capabilities ?? this.capabilities,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Device registration provider for managing device registration and connection
class DeviceRegistrationProvider extends ChangeNotifier {
  static const String _deviceInfoKey = 'device_info';
  static const String _credentialsKey = 'device_credentials';

  final Logger _logger = Logger();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  DeviceRegistrationStatus _status = DeviceRegistrationStatus.unregistered;
  DeviceInfo? _deviceInfo;
  SecureComm? _secureComm;
  String? _errorMessage;
  DateTime? _lastHeartbeat;
  List<String> _availableScopes = [];
  String _registrationProgress = '';

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Current registration status
  DeviceRegistrationStatus get status => _status;

  /// Current device information
  DeviceInfo? get deviceInfo => _deviceInfo;

  /// Whether device is registered
  bool get isRegistered => _status == DeviceRegistrationStatus.registered;

  /// Whether device is connected
  bool get isConnected => _secureComm?.isAuthenticated ?? false;

  /// Current error message
  String? get errorMessage => _errorMessage;

  /// Last heartbeat time
  DateTime? get lastHeartbeat => _lastHeartbeat;

  /// Available device scopes/capabilities
  List<String> get availableScopes => _availableScopes;

  /// Registration progress message
  String get registrationProgress => _registrationProgress;

  /// SecureComm instance
  SecureComm? get secureComm => _secureComm;

  /// Connection status string
  String get connectionStatus {
    switch (_status) {
      case DeviceRegistrationStatus.unregistered:
        return 'Not Registered';
      case DeviceRegistrationStatus.registering:
        return 'Registering...';
      case DeviceRegistrationStatus.registered:
        return isConnected ? 'Connected' : 'Disconnected';
      case DeviceRegistrationStatus.error:
        return 'Error';
      case DeviceRegistrationStatus.disconnected:
        return 'Disconnected';
    }
  }

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  /// Initialize the provider
  Future<void> initialize() async {
    _logger.i('Initializing device registration provider');
    
    try {
      await _loadStoredDeviceInfo();
      await _loadStoredCredentials();
      
      if (_deviceInfo != null && _secureComm != null) {
        await _attemptReconnection();
      }
    } catch (e) {
      _logger.e('Failed to initialize device registration provider', error: e);
      _setError('Failed to initialize: $e');
    }
  }

  /// Load stored device information
  Future<void> _loadStoredDeviceInfo() async {
    try {
      final deviceInfoJson = await _secureStorage.read(key: _deviceInfoKey);

      if (deviceInfoJson != null) {
        _logger.i('Found stored device info');
        final deviceInfoMap = jsonDecode(deviceInfoJson) as Map<String, dynamic>;
        _deviceInfo = DeviceInfo.fromJson(deviceInfoMap);
        _logger.i('Loaded stored device info: ${_deviceInfo?.deviceId}');
      }
    } catch (e) {
      _logger.w('Failed to load stored device info', error: e);
    }
  }

  /// Load stored credentials
  Future<void> _loadStoredCredentials() async {
    try {
      final credentials = await _loadCredentials();
      if (credentials != null && _deviceInfo != null) {
        _logger.i('Found stored credentials for device: ${_deviceInfo?.deviceId}');
      }
    } catch (e) {
      _logger.w('Failed to load stored credentials', error: e);
    }
  }

  /// Load credentials from storage and return them
  Future<DeviceCredentials?> _loadCredentials() async {
    try {
      final credentialsJson = await _secureStorage.read(key: _credentialsKey);

      if (credentialsJson != null && _deviceInfo != null) {
        final credentialsMap = jsonDecode(credentialsJson) as Map<String, dynamic>;
        return DeviceCredentials.fromJson(credentialsMap);
      }
      return null;
    } catch (e) {
      _logger.w('Failed to load credentials from storage', error: e);
      return null;
    }
  }

  /// Attempt to reconnect with stored credentials
  Future<void> _attemptReconnection() async {
    if (_deviceInfo == null) return;

    const maxRetries = 3;
    const retryDelay = Duration(seconds: 5);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        _setStatus(DeviceRegistrationStatus.registering);
        _setRegistrationProgress('Reconnecting... (attempt $attempt/$maxRetries)');

        // Load stored credentials
        final storedCredentials = await _loadCredentials();
        if (storedCredentials == null) {
          _logger.w('No stored credentials found for reconnection');
          _setError('No stored credentials found');
          return;
        }

        // Create transport and secure communication with face recognition endpoint mapping
        final transport = CustomHttpTransport(
          _deviceInfo!.serverUrl,
          endpoints: {
            'face_recognition': '/api/face/recognize',
            'register': '/api/device/register',
            'message': '/api/message',
          },
        );
        final secureComm = SecureComm(
          deviceId: _deviceInfo!.deviceId,
          deviceType: _deviceInfo!.deviceType,
          transport: transport,
          deviceName: _deviceInfo!.deviceName,
          capabilities: _deviceInfo!.capabilities,
          metadata: _deviceInfo!.metadata,
        );

        // Try to refresh token to validate connection
        if (storedCredentials.refreshToken != null) {
          _setRegistrationProgress('Validating credentials...');

          try {
            // Try to refresh the access token
            final refreshResponse = await transport.refreshToken(storedCredentials.refreshToken!);

            // Update credentials with new access token
            final newCredentials = storedCredentials.withNewToken(
              accessToken: refreshResponse['access_token'] as String,
              expiresIn: refreshResponse['expires_in'] as int,
            );

            // Save updated credentials
            await _saveCredentials(newCredentials);

            // Update state
            _secureComm = secureComm;
            _availableScopes = newCredentials.scopes;
            _setStatus(DeviceRegistrationStatus.registered);
            _setRegistrationProgress('');
            _lastHeartbeat = DateTime.now();

            _logger.i('Successfully reconnected device: ${_deviceInfo?.deviceId}');
            return;
          } catch (refreshError) {
            _logger.w('Token refresh failed, credentials may be expired', error: refreshError);
            throw Exception('Token refresh failed: $refreshError');
          }
        } else {
          throw Exception('No refresh token available');
        }

      } catch (e) {
        _logger.w('Reconnection attempt $attempt failed', error: e);

        if (attempt == maxRetries) {
          _logger.e('All reconnection attempts failed', error: e);
          _setError('Failed to reconnect after $maxRetries attempts: $e');
          return;
        }

        // Wait before next attempt
        _setRegistrationProgress('Retrying in ${retryDelay.inSeconds} seconds...');
        await Future.delayed(retryDelay);
      }
    }
  }

  // ============================================================================
  // REGISTRATION METHODS
  // ============================================================================

  /// Register device with server
  Future<bool> registerDevice({
    required String deviceId,
    required String deviceName,
    required String deviceType,
    required String location,
    String serverUrl = 'http://localhost:3000',
    List<String> capabilities = const [],
    Map<String, dynamic> metadata = const {},
    bool usePlainText = false,
  }) async {
    _logger.i('Init regis provider: $serverUrl');
    _logger.i('Starting device registration: $deviceId');
    
    try {
      _setStatus(DeviceRegistrationStatus.registering);
      _setRegistrationProgress('Connecting to server...');
      
      // Create device info
      final deviceInfo = DeviceInfo(
        deviceId: deviceId,
        deviceName: deviceName,
        deviceType: deviceType,
        location: location,
        serverUrl: serverUrl,
        capabilities: capabilities,
        metadata: metadata,
      );

      // Create transport and secure communication with face recognition endpoint mapping
      final transport = CustomHttpTransport(
        serverUrl,
        endpoints: {
          'face_recognition': '/api/face/recognize',
          'register': '/api/device/register',
          'message': '/api/message',
        },
      );
      final secureComm = SecureComm(
        deviceId: deviceId,
        deviceType: deviceType,
        transport: transport,
        deviceName: deviceName,
        capabilities: capabilities,
        metadata: {
          'location': location,
          ...metadata,
        },
        usePlainText: usePlainText,
      );

      _setRegistrationProgress('Registering with server...');
      
      // Register device
      await secureComm.registerDevice();
      
      _setRegistrationProgress('Saving credentials...');
      
      // Save device info and credentials
      await _saveDeviceInfo(deviceInfo.copyWith(
        registrationTime: DateTime.now(),
      ));
      await _saveCredentials(secureComm.credentials);
      
      // Update state
      _secureComm = secureComm;
      _availableScopes = secureComm.scopes;
      _setStatus(DeviceRegistrationStatus.registered);
      _setRegistrationProgress('');
      _lastHeartbeat = DateTime.now();
      
      _logger.i('Device registered successfully: $deviceId');

      // Auto-register as group device if this is a terminal device
      if (deviceType == 'terminal') {
        _logger.i('Registering terminal as group device with relays: $deviceId');
        await _registerTerminalGroupDevice(deviceId, serverUrl);
      }

      return true;
      
    } catch (e) {
      _logger.e('Device registration failed', error: e);
      _setError('Registration failed: $e');
      return false;
    }
  }

  /// Register terminal as group device with integrated relays
  Future<void> _registerTerminalGroupDevice(String terminalId, String serverUrl) async {
    try {
      _logger.i('🚀 Registering terminal as group device: $terminalId');
      _logger.i('Server URL: $serverUrl');

      final relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];

      // Create group device registration payload
      final groupDeviceData = {
        'deviceId': terminalId,
        'deviceName': _deviceInfo?.deviceName ?? 'Terminal Device',
        'type': 'terminal_group',
        'groupType': 'terminal_with_relays',
        'parentDevice': terminalId,
        'relayCount': 4,
        'relayProfiles': relayProfiles,
        'relayConfiguration': {
          for (int i = 1; i <= 4; i++)
            'relay_$i': {
              'index': i,
              'name': 'Terminal Relay $i',
              'profile': relayProfiles[i - 1],
              'enabled': true,
              'defaultState': 'off',
            }
        },
        'capabilities': [
          'face_recognition',
          'relay_control',
          'group_management',
          'multi_relay_control'
        ],
        'metadata': {
          'registration_type': 'group_device',
          'device_count': 5, // 1 terminal + 4 relays
          'auto_registered': true,
          'hardware_hash': 'group_${DateTime.now().millisecondsSinceEpoch}',
          'app_version': '1.0.0',
          'group_id': terminalId,
        },
      };

      _logger.i('📡 Registering group device with ${relayProfiles.length} relays');

      final uri = Uri.parse('$serverUrl/api/device/register-group');
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(groupDeviceData),
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        _logger.i('✅ Successfully registered terminal group device: $terminalId');
        _logger.i('   Group ID: ${responseData['group_id'] ?? terminalId}');
        _logger.i('   Device Count: ${responseData['device_count'] ?? 5}');
        _logger.i('   Relay Count: ${responseData['relay_count'] ?? 4}');

        // Log relay configuration
        for (int i = 1; i <= 4; i++) {
          _logger.i('   - Relay $i: ${relayProfiles[i - 1]}');
        }

        // Verify group registration
        await _verifyGroupRegistration(terminalId, serverUrl);

      } else if (response.statusCode == 404) {
        // Fallback to legacy individual device registration
        _logger.w('⚠️ Group registration endpoint not available, falling back to individual device registration');
        await _autoRegisterRelayDevicesLegacy(terminalId, serverUrl);

      } else {
        _logger.w('❌ Failed to register group device: HTTP ${response.statusCode}');
        _logger.w('Response: ${response.body}');

        // Fallback to legacy registration
        _logger.i('🔄 Falling back to legacy individual device registration');
        await _autoRegisterRelayDevicesLegacy(terminalId, serverUrl);
      }

    } catch (e) {
      _logger.e('❌ Group device registration failed', error: e);

      // Fallback to legacy registration
      _logger.i('🔄 Falling back to legacy individual device registration');
      await _autoRegisterRelayDevicesLegacy(terminalId, serverUrl);
    }
  }

  /// Legacy auto-register relay devices for terminal (fallback)
  Future<void> _autoRegisterRelayDevicesLegacy(String terminalId, String serverUrl) async {
    try {
      _logger.i('🔌 Starting legacy auto-registration of relay devices for terminal: $terminalId');
      _logger.i('Server URL: $serverUrl');

      // Generate relay device IDs following T-xxx-Rxx convention (same as debug widget)
      final relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];
      final relayDevices = <String>[];

      _logger.i('Generating ${relayProfiles.length} relay devices...');

      for (int i = 1; i <= 4; i++) {
        final relayId = '$terminalId-R${i.toString().padLeft(2, '0')}';
        final relayName = 'Terminal Relay $i';
        final profile = relayProfiles[i - 1];

        _logger.i('📡 Registering relay: $relayId ($relayName) - Profile: $profile');

        try {
          final success = await _registerRelayDevice(
            relayId: relayId,
            relayName: relayName,
            terminalId: terminalId,
            profile: profile,
            serverUrl: serverUrl,
          );

          if (success) {
            relayDevices.add(relayId);
          }

          // Small delay between registrations to avoid overwhelming server
          await Future.delayed(const Duration(milliseconds: 300));

        } catch (e) {
          _logger.e('Error registering relay $relayId', error: e);
        }
      }

      _logger.i('🎉 Legacy auto-registration completed. Successfully registered ${relayDevices.length}/4 relay devices');

      if (relayDevices.length == 4) {
        _logger.i('✅ All relay devices registered successfully:');
        for (final relayId in relayDevices) {
          _logger.i('  - $relayId');
        }
      } else {
        _logger.w('⚠️ Only ${relayDevices.length}/4 relay devices were registered successfully');
      }

      // Verify registration by checking server devices
      await _verifyRelayRegistration(terminalId, serverUrl);

    } catch (e) {
      _logger.e('❌ Legacy auto-registration of relay devices failed', error: e);
    }
  }

  /// Register single relay device using optimized format
  Future<bool> _registerRelayDevice({
    required String relayId,
    required String relayName,
    required String terminalId,
    required String profile,
    required String serverUrl,
  }) async {
    try {
      _logger.i('Registering relay device: $relayId with profile: $profile');

      // Use same format as debug widget test relay registration
      final uri = Uri.parse('$serverUrl/register');
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'deviceId': relayId,
          'deviceName': relayName,
          'type': 'relay',
          'terminalId': terminalId,
          'profile': profile,
          'relayCount': 1,
          'baudRate': 115200,
          'autoRegistered': true,
          'hardwareHash': 'auto_${DateTime.now().millisecondsSinceEpoch}',
        }),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        _logger.i('✅ Successfully registered relay: $relayId');
        return true;
      } else {
        _logger.w('❌ Failed to register relay $relayId: HTTP ${response.statusCode}');
        return false;
      }

    } catch (e) {
      _logger.e('Failed to register relay device $relayId', error: e);
      return false;
    }
  }

  /// Verify group device registration by checking server
  Future<void> _verifyGroupRegistration(String terminalId, String serverUrl) async {
    try {
      _logger.i('🔍 Verifying group device registration on server...');

      final uri = Uri.parse('$serverUrl/api/device/group/$terminalId');
      final response = await http.get(uri).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final groupData = json.decode(response.body);

        _logger.i('✅ Group verification completed:');
        _logger.i('  - Group ID: ${groupData['group_id']}');
        _logger.i('  - Device Count: ${groupData['device_count']}');
        _logger.i('  - Relay Count: ${groupData['relay_count']}');
        _logger.i('  - Registration Type: ${groupData['registration_type']}');

      } else if (response.statusCode == 404) {
        // Fallback to legacy verification
        _logger.w('⚠️ Group endpoint not available, using legacy verification');
        await _verifyRelayRegistration(terminalId, serverUrl);

      } else {
        _logger.w('❌ Failed to verify group registration: HTTP ${response.statusCode}');
      }

    } catch (e) {
      _logger.e('Failed to verify group registration', error: e);
      // Fallback to legacy verification
      await _verifyRelayRegistration(terminalId, serverUrl);
    }
  }

  /// Verify relay registration by checking server devices (legacy)
  Future<void> _verifyRelayRegistration(String terminalId, String serverUrl) async {
    try {
      _logger.i('🔍 Verifying relay registration on server...');

      final uri = Uri.parse('$serverUrl/devices');
      final response = await http.get(uri).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final devices = json.decode(response.body) as List;

        // Filter relay devices for this terminal
        final ourRelays = devices.where((d) =>
            d['id']?.toString().startsWith(terminalId) == true &&
            d['id']?.toString().contains('-R') == true).toList();

        _logger.i('📊 Server verification results:');
        _logger.i('  Total devices on server: ${devices.length}');
        _logger.i('  Our relay devices: ${ourRelays.length}/4');

        if (ourRelays.length == 4) {
          _logger.i('✅ All relay devices confirmed on server:');
          for (final relay in ourRelays) {
            final profile = relay['profile'] ?? 'unknown';
            _logger.i('  - ${relay['id']} (${relay['name']}) - $profile');
          }
        } else {
          _logger.w('⚠️ Expected 4 relay devices, but server shows ${ourRelays.length}');
          for (final relay in ourRelays) {
            _logger.i('  Found: ${relay['id']} - ${relay['name']}');
          }
        }

      } else {
        _logger.w('❌ Failed to verify registration: HTTP ${response.statusCode}');
      }

    } catch (e) {
      _logger.e('Failed to verify relay registration', error: e);
    }
  }

  /// Manually register relay devices for current terminal
  Future<bool> registerRelayDevices() async {
    if (_deviceInfo == null) {
      _logger.w('Cannot register relay devices: No terminal device info available');
      return false;
    }

    if (_deviceInfo!.deviceType != 'terminal') {
      _logger.w('Cannot register relay devices: Device is not a terminal');
      return false;
    }

    _logger.i('Manually triggering relay device registration...');

    try {
      await _registerTerminalGroupDevice(_deviceInfo!.deviceId, _deviceInfo!.serverUrl);
      return true;
    } catch (e) {
      _logger.e('Manual relay registration failed', error: e);
      return false;
    }
  }

  /// Unregister device
  Future<void> unregisterDevice() async {
    _logger.i('Unregistering device: ${_deviceInfo?.deviceId}');
    
    try {
      if (_secureComm != null) {
        await _secureComm!.revokeCredentials();
        _secureComm!.dispose();
      }
      
      // Clear stored data
      await _clearStoredData();
      
      // Reset state
      _secureComm = null;
      _deviceInfo = null;
      _availableScopes = [];
      _lastHeartbeat = null;
      _setStatus(DeviceRegistrationStatus.unregistered);
      _setRegistrationProgress('');
      
      _logger.i('Device unregistered successfully');
    } catch (e) {
      _logger.e('Failed to unregister device', error: e);
      _setError('Failed to unregister: $e');
    }
  }

  /// Send heartbeat to server
  Future<void> sendHeartbeat() async {
    if (_secureComm == null || !isConnected) return;

    try {
      // Get real system information
      final systemInfoService = SystemInfoService();
      final systemInfo = await systemInfoService.getSystemInfo();

      // Add device-specific info
      systemInfo['status'] = 'active';
      systemInfo['uptime'] = DateTime.now().difference(
        _deviceInfo?.registrationTime ?? DateTime.now()
      ).inSeconds;
      systemInfo['device_id'] = _deviceInfo?.deviceId;
      systemInfo['device_type'] = _deviceInfo?.deviceType;
      systemInfo['location'] = _deviceInfo?.location;

      await _secureComm!.sendHeartbeat(systemInfo: systemInfo);

      _lastHeartbeat = DateTime.now();
      notifyListeners();
    } catch (e) {
      _logger.w('Heartbeat failed', error: e);
    }
  }

  // ============================================================================
  // STORAGE METHODS
  // ============================================================================

  /// Save device information to storage
  Future<void> _saveDeviceInfo(DeviceInfo deviceInfo) async {
    try {
      final json = jsonEncode(deviceInfo.toJson());
      await _secureStorage.write(key: _deviceInfoKey, value: json);
      _deviceInfo = deviceInfo;
      _logger.d('Device info saved');
    } catch (e) {
      _logger.e('Failed to save device info', error: e);
    }
  }

  /// Save credentials to storage
  Future<void> _saveCredentials(DeviceCredentials? credentials) async {
    if (credentials == null) return;

    try {
      final json = jsonEncode(credentials.toJson());
      await _secureStorage.write(key: _credentialsKey, value: json);
      _logger.d('Credentials saved');
    } catch (e) {
      _logger.e('Failed to save credentials', error: e);
    }
  }

  /// Clear all stored data
  Future<void> _clearStoredData() async {
    try {
      await _secureStorage.delete(key: _deviceInfoKey);
      await _secureStorage.delete(key: _credentialsKey);
      _logger.d('Stored data cleared');
    } catch (e) {
      _logger.e('Failed to clear stored data', error: e);
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /// Set registration status
  void _setStatus(DeviceRegistrationStatus status) {
    _status = status;
    if (status != DeviceRegistrationStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error state
  void _setError(String message) {
    _status = DeviceRegistrationStatus.error;
    _errorMessage = message;
    _registrationProgress = '';
    notifyListeners();
  }

  /// Set registration progress
  void _setRegistrationProgress(String progress) {
    _registrationProgress = progress;
    notifyListeners();
  }

  /// Clear error
  void clearError() {
    if (_status == DeviceRegistrationStatus.error) {
      _status = _deviceInfo != null 
          ? DeviceRegistrationStatus.registered 
          : DeviceRegistrationStatus.unregistered;
      _errorMessage = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _secureComm?.dispose();
    super.dispose();
  }
}

#!/usr/bin/env python3
"""
Direct download script for pre-converted TensorFlow Lite models.
This script downloads models that are already in TFLite format.
"""

import os
import sys
import requests
from pathlib import Path
from typing import Dict
import argparse

# Direct TFLite model URLs (real pre-trained models)
DIRECT_MODELS = {
    "ultraface_320.tflite": {
        "url": "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/030_BlazeFace/01_float32/face_detection_front_128x128_float32.tflite",
        "description": "BlazeFace front face detection (UltraFace alternative)",
        "size_mb": 0.2,
        "min_size_kb": 150  # Minimum expected size to verify it's not a mock
    },
    "mobilefacenet.tflite": {
        "url": "https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task",
        "description": "MediaPipe Face Landmarker (MobileFaceNet alternative)",
        "size_mb": 11.2,
        "min_size_kb": 1000
    },
    "mediapipe_face.tflite": {
        "url": "https://storage.googleapis.com/mediapipe-models/face_detector/blaze_face_short_range/float16/1/blaze_face_short_range.tflite",
        "description": "MediaPipe BlazeFace short range official model",
        "size_mb": 0.3,
        "min_size_kb": 200
    }
}

# Alternative models with better URLs for real models
ALTERNATIVE_MODELS = {
    "ultraface_320.tflite": {
        "url": "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/030_BlazeFace/03_integer_quantization/face_detection_front_128x128_integer_quant.tflite",
        "description": "BlazeFace quantized model (smaller, faster)",
        "size_mb": 0.1,
        "min_size_kb": 80
    },
    "mobilefacenet.tflite": {
        "url": "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/043_face_landmark/03_integer_quantization/face_landmark_68_integer_quant.tflite",
        "description": "Face landmark quantized model",
        "size_mb": 0.5,
        "min_size_kb": 400
    },
    "mediapipe_face.tflite": {
        "url": "https://github.com/PINTO0309/PINTO_model_zoo/raw/main/030_BlazeFace/03_integer_quantization/face_detection_front_128x128_integer_quant.tflite",
        "description": "BlazeFace quantized (same as ultraface for consistency)",
        "size_mb": 0.1,
        "min_size_kb": 80
    }
}

class DirectDownloader:
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
    
    def download_file(self, url: str, filepath: Path, expected_size_mb: float = None) -> bool:
        """Download a file with progress tracking."""
        try:
            print(f"Downloading {filepath.name}...")
            print(f"URL: {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\rProgress: {progress:.1f}%", end='', flush=True)
            
            actual_size_mb = downloaded_size / 1024 / 1024
            actual_size_kb = downloaded_size / 1024
            print(f"\n✓ Downloaded {filepath.name} ({actual_size_mb:.1f} MB)")

            # Validate against expected minimum size
            if expected_size_mb and 'min_size_kb' in expected_size_mb:
                min_size_kb = expected_size_mb.get('min_size_kb', 100)
                if actual_size_kb < min_size_kb:
                    print(f"✗ File too small: {actual_size_kb:.1f} KB (expected >{min_size_kb} KB)")
                    print("This might be a placeholder or error page, not the actual model")
                    return False

            # Basic validation - must be at least 50KB for real models
            if downloaded_size < 50000:  # Less than 50KB
                print(f"✗ File seems too small ({actual_size_kb:.1f} KB) - likely a mock/placeholder")
                return False

            return True
            
        except requests.exceptions.RequestException as e:
            print(f"\n✗ Network error downloading {url}: {e}")
            return False
        except Exception as e:
            print(f"\n✗ Error downloading {url}: {e}")
            return False
    
    def verify_tflite_model(self, model_path: Path) -> bool:
        """Basic verification that file is a valid TFLite model."""
        try:
            # Check file signature (TFLite files start with specific bytes)
            with open(model_path, 'rb') as f:
                header = f.read(8)
                
            # TFLite files typically start with these patterns
            if len(header) >= 4:
                # Check for FlatBuffer signature or TFLite magic numbers
                if header[:4] in [b'TFL3', b'\x18\x00\x00\x00'] or header[4:8] == b'TFL3':
                    print(f"✓ {model_path.name} appears to be a valid TFLite model")
                    return True
            
            print(f"? {model_path.name} may not be a valid TFLite model (unknown format)")
            return True  # Don't fail, just warn
            
        except Exception as e:
            print(f"? Could not verify {model_path.name}: {e}")
            return True  # Don't fail verification
    
    def download_model(self, model_name: str, config: Dict, use_alternative: bool = False) -> bool:
        """Download a single model."""
        print(f"\n{'='*60}")
        print(f"Downloading {model_name}")
        print(f"Description: {config['description']}")
        print(f"Expected size: ~{config['size_mb']} MB")
        print(f"{'='*60}")
        
        output_path = self.models_dir / model_name
        
        # Download the file
        if not self.download_file(config['url'], output_path, config['size_mb']):
            return False
        
        # Verify it's a TFLite model
        if not self.verify_tflite_model(output_path):
            print(f"Warning: {model_name} may not be a valid TFLite model")
        
        return True
    
    def download_all_models(self, force: bool = False, use_alternative: bool = False) -> bool:
        """Download all models."""
        models_to_use = ALTERNATIVE_MODELS if use_alternative else DIRECT_MODELS
        
        success_count = 0
        total_count = len(models_to_use)
        
        print(f"Downloading {total_count} models to {self.models_dir}")
        if use_alternative:
            print("Using alternative model sources")
        
        for model_name, config in models_to_use.items():
            model_path = self.models_dir / model_name
            
            if model_path.exists() and not force:
                print(f"\nSkipping {model_name} (already exists, use --force to overwrite)")
                success_count += 1
                continue
            
            if self.download_model(model_name, config, use_alternative):
                success_count += 1
        
        print(f"\n{'='*60}")
        print(f"Download Summary: {success_count}/{total_count} models downloaded")
        
        if success_count == total_count:
            print("✓ All models downloaded successfully!")
            print("\nNext steps:")
            print("1. Test the models in your Flutter app")
            print("2. If models don't work as expected, try the conversion script")
            return True
        else:
            print(f"✗ {total_count - success_count} models failed to download")
            return False

def main():
    parser = argparse.ArgumentParser(description="Download pre-converted TFLite face recognition models")
    parser.add_argument(
        "--models-dir",
        default="lib/packages/face_recognition/assets/models", 
        help="Directory to save models"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force download even if files exist"
    )
    parser.add_argument(
        "--alternative",
        action="store_true", 
        help="Use alternative model sources"
    )
    parser.add_argument(
        "--model",
        choices=list(DIRECT_MODELS.keys()),
        help="Download specific model only"
    )
    
    args = parser.parse_args()
    
    # Resolve models directory
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / args.models_dir
    
    downloader = DirectDownloader(str(models_dir))
    
    if args.model:
        # Download specific model
        models_to_use = ALTERNATIVE_MODELS if args.alternative else DIRECT_MODELS
        config = models_to_use[args.model]
        success = downloader.download_model(args.model, config, args.alternative)
        sys.exit(0 if success else 1)
    else:
        # Download all models
        success = downloader.download_all_models(args.force, args.alternative)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

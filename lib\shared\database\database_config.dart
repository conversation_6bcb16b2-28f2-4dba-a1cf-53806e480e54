import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

/// C<PERSON><PERSON> hình cơ sở dữ liệu SQLite được chia sẻ giữa mobile và terminal apps
class DatabaseConfig {
  static const String _databaseName = 'face_terminal.db';
  static const int _databaseVersion = 1;
  
  static Database? _database;
  
  /// Singleton instance của database
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  /// Khởi tạo database
  static Future<Database> _initDatabase() async {
    // Lấy đường dẫn thư mục documents
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onConfigure: _onConfigure,
    );
  }
  
  /// Cấ<PERSON> hình database (enable foreign keys)
  static Future<void> _onConfigure(Database db) async {
    await db.execute('PRAGMA foreign_keys = ON');
  }
  
  /// Tạo các bảng khi database được tạo lần đầu
  static Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }
  
  /// Xử lý upgrade database khi version thay đổi
  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Implement migration logic here
    if (oldVersion < newVersion) {
      // Add migration scripts here
      await _createTables(db);
    }
  }
  
  /// Tạo tất cả các bảng
  static Future<void> _createTables(Database db) async {
    // Bảng tenants (khách hàng/tổ chức)
    await db.execute('''
      CREATE TABLE tenants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT UNIQUE NOT NULL,
        address TEXT,
        phone TEXT,
        email TEXT,
        status INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
    
    // Bảng users (người dùng)
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tenant_id INTEGER NOT NULL,
        employee_code TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        department TEXT,
        position TEXT,
        status INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (tenant_id) REFERENCES tenants (id) ON DELETE CASCADE,
        UNIQUE(tenant_id, employee_code)
      )
    ''');
    
    // Bảng face_data (dữ liệu khuôn mặt)
    await db.execute('''
      CREATE TABLE face_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        face_encoding TEXT NOT NULL,
        image_path TEXT,
        confidence REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');
    
    // Bảng attendance (chấm công)
    await db.execute('''
      CREATE TABLE attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        tenant_id INTEGER NOT NULL,
        check_in_time TEXT,
        check_out_time TEXT,
        date TEXT NOT NULL,
        status TEXT DEFAULT 'present',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (tenant_id) REFERENCES tenants (id) ON DELETE CASCADE,
        UNIQUE(user_id, date)
      )
    ''');
    
    // Bảng settings (cài đặt ứng dụng)
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
    
    // Tạo indexes để tối ưu hiệu suất
    await _createIndexes(db);
    
    // Thêm dữ liệu mặc định
    await _insertDefaultData(db);
  }
  
  /// Tạo các indexes
  static Future<void> _createIndexes(Database db) async {
    await db.execute('CREATE INDEX idx_users_tenant_id ON users(tenant_id)');
    await db.execute('CREATE INDEX idx_users_employee_code ON users(employee_code)');
    await db.execute('CREATE INDEX idx_face_data_user_id ON face_data(user_id)');
    await db.execute('CREATE INDEX idx_attendance_user_id ON attendance(user_id)');
    await db.execute('CREATE INDEX idx_attendance_date ON attendance(date)');
    await db.execute('CREATE INDEX idx_settings_key ON settings(key)');
  }
  
  /// Thêm dữ liệu mặc định
  static Future<void> _insertDefaultData(Database db) async {
    final now = DateTime.now().toIso8601String();
    
    // Thêm settings mặc định
    await db.insert('settings', {
      'key': 'app_version',
      'value': '1.0.0',
      'description': 'Phiên bản ứng dụng',
      'created_at': now,
      'updated_at': now,
    });
    
    await db.insert('settings', {
      'key': 'face_detection_threshold',
      'value': '0.8',
      'description': 'Ngưỡng nhận diện khuôn mặt',
      'created_at': now,
      'updated_at': now,
    });
    
    await db.insert('settings', {
      'key': 'auto_backup_enabled',
      'value': 'true',
      'description': 'Tự động sao lưu dữ liệu',
      'created_at': now,
      'updated_at': now,
    });
  }
  
  /// Đóng database
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
  
  /// Xóa database (chỉ dùng cho testing)
  static Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
  
  /// Backup database
  static Future<String> backupDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String sourcePath = join(documentsDirectory.path, _databaseName);
    String backupPath = join(documentsDirectory.path, 'backup_${DateTime.now().millisecondsSinceEpoch}_$_databaseName');
    
    File sourceFile = File(sourcePath);
    if (await sourceFile.exists()) {
      await sourceFile.copy(backupPath);
      return backupPath;
    }
    throw Exception('Database file not found');
  }
}

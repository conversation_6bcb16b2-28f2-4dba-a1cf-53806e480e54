import 'flavor_config.dart';

/// Configuration cho từng flavor của ứng dụng
class AppConfig {
  AppConfig._();

  /// Mobile app configuration
  static const Map<String, dynamic> mobileConfig = {
    'baseUrl': 'https://api.mobile.ccam.com',
    'apiVersion': 'v1',
    'enableAnalytics': true,
    'enableCrashlytics': true,
    'maxRetryAttempts': 3,
    'requestTimeout': 30000,
    'features': {
      'faceRecognition': true,
      'biometricAuth': true,
      'offlineMode': true,
      'pushNotifications': true,
      'locationTracking': true,
    },
    'ui': {
      'showTabBar': true,
      'enableDarkMode': true,
      'defaultTheme': 'light',
    },
  };

  /// Terminal app configuration
  static const Map<String, dynamic> terminalConfig = {
    'baseUrl': 'https://api.terminal.ccam.com',
    'apiVersion': 'v1',
    'enableAnalytics': false,
    'enableCrashlytics': true,
    'maxRetryAttempts': 5,
    'requestTimeout': 60000,
    'features': {
      'faceRecognition': true,
      'biometricAuth': false,
      'offlineMode': false,
      'pushNotifications': false,
      'locationTracking': false,
      'kioskMode': true,
      'autoLogout': true,
    },
    'ui': {
      'showTabBar': false,
      'enableDarkMode': false,
      'defaultTheme': 'light',
      'fullScreen': true,
      'hideSystemUI': true,
    },
  };

  /// Lấy configuration hiện tại dựa trên flavor
  static Map<String, dynamic> get currentConfig {
    final flavor = FlavorConfig.instance.flavor;
    switch (flavor) {
      case Flavor.mobile:
        return mobileConfig;
      case Flavor.terminal:
        return terminalConfig;
    }
  }

  /// Lấy base URL cho API
  static String get baseUrl => currentConfig['baseUrl'] as String;

  /// Lấy API version
  static String get apiVersion => currentConfig['apiVersion'] as String;

  /// Kiểm tra feature có được enable không
  static bool isFeatureEnabled(String featureName) {
    final features = currentConfig['features'] as Map<String, dynamic>;
    return features[featureName] as bool? ?? false;
  }

  /// Lấy UI configuration
  static Map<String, dynamic> get uiConfig => 
      currentConfig['ui'] as Map<String, dynamic>;

  /// Kiểm tra có hiển thị tab bar không
  static bool get showTabBar => uiConfig['showTabBar'] as bool? ?? true;

  /// Kiểm tra có enable dark mode không
  static bool get enableDarkMode => uiConfig['enableDarkMode'] as bool? ?? true;

  /// Lấy default theme
  static String get defaultTheme => uiConfig['defaultTheme'] as String? ?? 'light';

  /// Kiểm tra có phải full screen mode không (cho terminal)
  static bool get isFullScreen => uiConfig['fullScreen'] as bool? ?? false;

  /// Kiểm tra có ẩn system UI không (cho terminal)
  static bool get hideSystemUI => uiConfig['hideSystemUI'] as bool? ?? false;

  /// Lấy timeout cho request
  static int get requestTimeout => currentConfig['requestTimeout'] as int? ?? 30000;

  /// Lấy số lần retry tối đa
  static int get maxRetryAttempts => currentConfig['maxRetryAttempts'] as int? ?? 3;
}

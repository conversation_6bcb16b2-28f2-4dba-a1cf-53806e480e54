/// Base exception for secure communication errors
class SecureCommException implements Exception {
  /// Error message
  final String message;

  /// Optional underlying cause
  final Object? cause;

  const SecureCommException(this.message, [this.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'SecureCommException: $message (caused by: $cause)';
    }
    return 'SecureCommException: $message';
  }
}

/// Exception thrown when device registration fails
class DeviceRegistrationException extends SecureCommException {
  const DeviceRegistrationException(super.message, [super.cause]);
}

/// Exception thrown when authentication fails
class AuthenticationException extends SecureCommException {
  const AuthenticationException(super.message, [super.cause]);
}

/// Exception thrown when message signing/verification fails
class CryptographyException extends SecureCommException {
  const CryptographyException(super.message, [super.cause]);
}

/// Exception thrown when transport layer fails
class TransportException extends SecureCommException {
  const TransportException(super.message, [super.cause]);
}

/// Exception thrown when message format is invalid
class MessageFormatException extends SecureCommException {
  const MessageFormatException(super.message, [super.cause]);
}

/// Exception thrown when device credentials are invalid or expired
class CredentialsException extends SecureCommException {
  const CredentialsException(super.message, [super.cause]);
}

/// Exception thrown when server response is invalid
class ServerResponseException extends SecureCommException {
  const ServerResponseException(super.message, [super.cause]);
}

/// Exception thrown when network communication fails
class NetworkException extends SecureCommException {
  const NetworkException(super.message, [super.cause]);
}

/// Exception thrown when timeout occurs
class TimeoutException extends SecureCommException {
  const TimeoutException(super.message, [super.cause]);
}

/// Exception thrown when rate limiting is applied
class RateLimitException extends SecureCommException {
  const RateLimitException(super.message, [super.cause]);
}

/// Exception thrown when device is not authorized for operation
class AuthorizationException extends SecureCommException {
  const AuthorizationException(super.message, [super.cause]);
}

/// Exception thrown when configuration is invalid
class ConfigurationException extends SecureCommException {
  const ConfigurationException(super.message, [super.cause]);
}

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../models/enhanced_face_capture_result.dart';
import '../services/enhanced_face_capture_service.dart';
import '../core/constants/face_cropping_constants.dart';

/// Provider for enhanced face capture with cropping functionality
class EnhancedFaceCaptureProvider extends ChangeNotifier {
  static const String _logTag = '🎯 EnhancedFaceCaptureProvider';
  
  final EnhancedFaceCaptureService _service = EnhancedFaceCaptureService();
  
  // State management
  bool _isProcessing = false;
  EnhancedFaceCaptureResult? _lastResult;
  String? _processingStatus;
  double _processingProgress = 0.0;
  
  // Configuration
  ProcessingMode _processingMode = ProcessingMode.synchronous;
  List<SideEffectType> _enabledSideEffects = FaceCroppingConstants.defaultEnabledSideEffects;
  double _cropPadding = FaceCroppingConstants.defaultPadding;
  int _outputQuality = FaceCroppingConstants.defaultOutputQuality;
  Map<String, dynamic> _context = {};
  
  // Getters
  bool get isProcessing => _isProcessing;
  EnhancedFaceCaptureResult? get lastResult => _lastResult;
  String? get processingStatus => _processingStatus;
  double get processingProgress => _processingProgress;
  ProcessingMode get processingMode => _processingMode;
  List<SideEffectType> get enabledSideEffects => _enabledSideEffects;
  double get cropPadding => _cropPadding;
  int get outputQuality => _outputQuality;
  Map<String, dynamic> get context => _context;
  
  /// Set processing mode
  void setProcessingMode(ProcessingMode mode) {
    if (_processingMode != mode) {
      _processingMode = mode;
      debugPrint('$_logTag Processing mode changed to: ${mode.name}');
      notifyListeners();
    }
  }
  
  /// Set enabled side effects
  void setEnabledSideEffects(List<SideEffectType> sideEffects) {
    _enabledSideEffects = sideEffects;
    debugPrint('$_logTag Enabled side effects: ${sideEffects.map((e) => e.name).join(', ')}');
    notifyListeners();
  }
  
  /// Set crop padding
  void setCropPadding(double padding) {
    if (padding >= FaceCroppingConstants.minPadding && 
        padding <= FaceCroppingConstants.maxPadding) {
      _cropPadding = padding;
      debugPrint('$_logTag Crop padding set to: $padding');
      notifyListeners();
    } else {
      debugPrint('$_logTag ⚠️ Invalid crop padding: $padding');
    }
  }
  
  /// Set output quality
  void setOutputQuality(int quality) {
    if (quality >= 1 && quality <= 100) {
      _outputQuality = quality;
      debugPrint('$_logTag Output quality set to: $quality');
      notifyListeners();
    } else {
      debugPrint('$_logTag ⚠️ Invalid output quality: $quality');
    }
  }
  
  /// Set context data
  void setContext(Map<String, dynamic> context) {
    _context = context;
    debugPrint('$_logTag Context updated with ${context.keys.length} keys');
    notifyListeners();
  }
  
  /// Add context data
  void addContext(String key, dynamic value) {
    _context[key] = value;
    debugPrint('$_logTag Context key added: $key');
    notifyListeners();
  }
  
  /// Process captured faces with cropping and API integration
  Future<EnhancedFaceCaptureResult> processCapture({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    ProcessingMode? processingMode,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
  }) async {
    if (_isProcessing) {
      throw Exception('Processing already in progress');
    }
    
    _isProcessing = true;
    _processingProgress = 0.0;
    _processingStatus = 'Starting enhanced face capture processing...';
    notifyListeners();
    
    try {
      debugPrint('$_logTag Starting enhanced face capture processing');
      
      // Update progress
      _processingProgress = 0.1;
      _processingStatus = 'Preparing for processing...';
      notifyListeners();
      
      final result = await _service.processWithCropping(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
        processingMode: processingMode ?? _processingMode,
        enabledSideEffects: enabledSideEffects ?? _enabledSideEffects,
        context: {
          ..._context,
          ...?context,
          'provider_session_id': DateTime.now().millisecondsSinceEpoch.toString(),
        },
        cropPadding: _cropPadding,
        outputQuality: _outputQuality,
      );
      
      _lastResult = result;
      _processingProgress = 1.0;
      
      if (result.success) {
        _processingStatus = 'Processing completed successfully';
        debugPrint('$_logTag ✅ Enhanced face capture processing completed successfully');
      } else {
        _processingStatus = 'Processing failed: ${result.error}';
        debugPrint('$_logTag ❌ Enhanced face capture processing failed: ${result.error}');
      }
      
      notifyListeners();
      return result;
      
    } catch (e, stackTrace) {
      _processingStatus = 'Processing error: $e';
      _processingProgress = 0.0;
      
      debugPrint('$_logTag ❌ Enhanced face capture processing error: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      
      final errorResult = EnhancedFaceCaptureResult.failure(error: e.toString());
      _lastResult = errorResult;
      
      notifyListeners();
      return errorResult;
      
    } finally {
      _isProcessing = false;
      notifyListeners();
    }
  }
  
  /// Process with synchronous mode
  Future<EnhancedFaceCaptureResult> processSynchronous({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
  }) async {
    return processCapture(
      capturedImages: capturedImages,
      detectedFaces: detectedFaces,
      processingMode: ProcessingMode.synchronous,
      enabledSideEffects: enabledSideEffects,
      context: context,
    );
  }
  
  /// Process with asynchronous mode
  Future<EnhancedFaceCaptureResult> processAsynchronous({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
  }) async {
    return processCapture(
      capturedImages: capturedImages,
      detectedFaces: detectedFaces,
      processingMode: ProcessingMode.asynchronous,
      enabledSideEffects: enabledSideEffects,
      context: context,
    );
  }
  
  /// Process with queue-based mode
  Future<EnhancedFaceCaptureResult> processQueueBased({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
  }) async {
    return processCapture(
      capturedImages: capturedImages,
      detectedFaces: detectedFaces,
      processingMode: ProcessingMode.queueBased,
      enabledSideEffects: enabledSideEffects,
      context: context,
    );
  }
  
  /// Get queue status (for queue-based processing)
  Map<String, dynamic> getQueueStatus() {
    return _service.getQueueStatus();
  }
  
  /// Reset processing state
  void reset() {
    _isProcessing = false;
    _lastResult = null;
    _processingStatus = null;
    _processingProgress = 0.0;
    debugPrint('$_logTag Provider state reset');
    notifyListeners();
  }
  
  /// Clear last result
  void clearLastResult() {
    _lastResult = null;
    debugPrint('$_logTag Last result cleared');
    notifyListeners();
  }
  
  /// Get processing configuration summary
  Map<String, dynamic> getConfigurationSummary() {
    return {
      'processing_mode': _processingMode.name,
      'enabled_side_effects': _enabledSideEffects.map((e) => e.name).toList(),
      'crop_padding': _cropPadding,
      'output_quality': _outputQuality,
      'context_keys': _context.keys.toList(),
      'is_processing': _isProcessing,
      'processing_progress': _processingProgress,
      'processing_status': _processingStatus,
      'has_last_result': _lastResult != null,
    };
  }
  
  @override
  void dispose() {
    debugPrint('$_logTag Disposing enhanced face capture provider');
    super.dispose();
  }
}

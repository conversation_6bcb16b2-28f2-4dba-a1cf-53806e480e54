import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

import '../../../../shared/providers/face_capture_provider.dart';
import '../../../../shared/providers/face_detection_provider.dart';
import '../../../../shared/providers/captured_face_provider.dart';
import '../../../../shared/services/camera_image_converter.dart';
import '../../../../shared/services/face_cropping_service.dart';
import '../../../../core/constants/app_colors.dart';

/// Floating overlay widget positioned at bottom-right corner of terminal app screens
/// to display cropped face images
///
/// Server load management parameters (optimized for typical 3-5s user interaction):
/// - _processingThrottle: Time between processing attempts (1.5s)
/// - _faceAbsenceThreshold: Time to consider face as absent (2s)
/// - _recognitionCooldown: Time after successful recognition before next attempt (7s)
/// - _securityReCheckSeconds: Periodic security check interval (15s)
/// - _majorMovementThreshold: Position change % to trigger processing (40%)
/// - _majorSizeThreshold: Size change % to trigger processing (30%)
///
/// These parameters can be adjusted to balance security vs server load
class TerminalFaceCropOverlay extends StatefulWidget {
  final Face? detectedFace;
  final FaceCaptureProvider? cameraProvider;
  final FaceDetectionProvider? faceDetectionProvider;
  final CapturedFaceProvider capturedFaceProvider;
  final bool isVisible;
  final Function(Uint8List imageBytes, double quality)? onFaceCropped;
  final Function()? onSuccessfulRecognition; // Callback for successful recognition

  const TerminalFaceCropOverlay({
    super.key,
    this.detectedFace,
    this.cameraProvider,
    this.faceDetectionProvider,
    required this.capturedFaceProvider,
    this.isVisible = true,
    this.onFaceCropped,
    this.onSuccessfulRecognition,
  });

  @override
  State<TerminalFaceCropOverlay> createState() => _TerminalFaceCropOverlayState();
}

class _TerminalFaceCropOverlayState extends State<TerminalFaceCropOverlay> {
  Uint8List? _lastCapturedImageBytes; // Store image bytes for display
  bool _isProcessing = false;
  DateTime? _lastProcessTime;
  Face? _lastProcessedFace;
  double _lastFaceQuality = 0.0;

  // Balanced throttle for server load management
  static const Duration _processingThrottle = Duration(milliseconds: 1500); // 1.5 seconds between requests

  // Quality threshold for face capture
  static const double _qualityThreshold = 0.4; // Lowered from 0.6 to capture more faces
  // Face absence detection - ensure old face is gone before processing new one
  static const Duration _faceAbsenceThreshold = Duration(seconds: 2); // 2 seconds to avoid false triggers

  // Face cropping configuration for avatar
  static const double _avatarPaddingFactor = 0.3; // 30% padding around face
  static const int _avatarTargetSize = 200; // 200x200px square avatar

  DateTime? _lastFaceDetectedTime;
  DateTime? _lastFaceAbsenceTime;
  bool _faceExitDetected = false; // Track when face exits screen

  // Server load management - Configurable parameters
  DateTime? _lastSuccessfulRecognition; // Track last successful recognition
  static const Duration _recognitionCooldown = Duration(seconds: 7); // Cooldown after successful recognition (reduced from 10s)

  // Performance tuning constants
  static const double _majorMovementThreshold = 40.0; // % position change for major movement
  static const double _majorSizeThreshold = 30.0; // % size change for major movement
  static const int _securityReCheckSeconds = 15; // Security re-check interval

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(TerminalFaceCropOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    final now = DateTime.now();

    // Track face presence/absence for exit detection
    if (widget.detectedFace != null) {
      _lastFaceDetectedTime = now;
      _lastFaceAbsenceTime = null;

      // If face was previously absent, mark as returned
      if (_faceExitDetected) {
        _faceExitDetected = false;
        if (kDebugMode) {
          debugPrint('👁️ Face returned after exit - will trigger server check');
        }
      }
    } else {
      _lastFaceAbsenceTime ??= now; // Set absence time only once when face disappears

      // Check if we should mark face as exited
      if (_lastFaceDetectedTime != null && !_faceExitDetected) {
        final timeSinceLastFace = now.difference(_lastFaceDetectedTime!);
        if (timeSinceLastFace >= _faceAbsenceThreshold) {
          _faceExitDetected = true;
          if (kDebugMode) {
            debugPrint('🚪 Face exit detected after ${timeSinceLastFace.inMilliseconds}ms - next face will trigger server check');
            debugPrint('📊 Timing: Face was present for ${_lastFaceDetectedTime != null ? DateTime.now().difference(_lastFaceDetectedTime!).inSeconds : 0}s total');
          }
        }
      }
    }

    // Process face capture when new high-quality face is detected
    if (widget.detectedFace != null &&
        widget.cameraProvider != null &&
        !_isProcessing &&
        _shouldProcessFace() &&
        _isHighQualityFace(widget.detectedFace!) &&
        _shouldProcessBasedOnTracking(widget.detectedFace!, _lastProcessedFace)) {
      _processFaceCropping();
    } else if (widget.detectedFace != null) {
      // Debug why face was not processed
      final reasons = <String>[];
      if (widget.cameraProvider == null) reasons.add('No camera provider');
      if (_isProcessing) reasons.add('Already processing');
      if (!_shouldProcessFace()) reasons.add('Time throttle (${_processingThrottle.inMilliseconds}ms)');
      if (!_isHighQualityFace(widget.detectedFace!)) reasons.add('Low quality');
      if (!_shouldProcessBasedOnTracking(widget.detectedFace!, _lastProcessedFace)) reasons.add('Face tracking: no significant movement');

      if (reasons.isNotEmpty) {
        debugPrint('⏸️ Face processing skipped: ${reasons.join(', ')}');
      }
    }
  }

  /// Check if we should process face cropping (throttling)
  bool _shouldProcessFace() {
    if (_lastProcessTime == null) return true;

    final now = DateTime.now();
    return now.difference(_lastProcessTime!) >= _processingThrottle;
  }

  /// Check if we should process a new face (ensure old face has been absent)
  bool _shouldProcessNewFace() {
    // If no previous face was processed, allow processing
    if (_lastProcessedFace == null) return true;

    // If face absence time is not set, don't process (face hasn't disappeared yet)
    if (_lastFaceAbsenceTime == null) return false;

    // Check if enough time has passed since face disappeared
    final now = DateTime.now();
    final absenceDuration = now.difference(_lastFaceAbsenceTime!);

    if (absenceDuration >= _faceAbsenceThreshold) {
      // Reset absence time after successful check
      _lastFaceAbsenceTime = null;
      return true;
    }

    return false;
  }

  /// Check if face has high quality for capture using face detection provider
  bool _isHighQualityFace(Face face) {
    if (widget.faceDetectionProvider == null) return false;
    final quality = widget.faceDetectionProvider!.getFaceQuality(face);
    return quality >= _qualityThreshold;
  }

  /// Get face quality from face detection provider
  double _getFaceQuality(Face face) {
    if (widget.faceDetectionProvider == null) return 0.0;
    return widget.faceDetectionProvider!.getFaceQuality(face);
  }



  /// Check if we should process face based on tracking state (not identity comparison)
  /// This method only tracks face presence/absence, NOT identity
  bool _shouldProcessBasedOnTracking(Face currentFace, Face? lastFace) {
    // Always process if no previous face
    if (lastFace == null) {
      if (kDebugMode) {
        debugPrint('🎯 Processing: First face detected');
      }
      return true;
    }

    // Always process if face returned after exit (security requirement)
    if (_faceExitDetected) {
      if (kDebugMode) {
        debugPrint('🎯 Processing: Face returned after exit - SECURITY CHECK REQUIRED');
      }
      return true;
    }

    // Check recognition cooldown to avoid server overload
    if (_lastSuccessfulRecognition != null) {
      final timeSinceSuccess = DateTime.now().difference(_lastSuccessfulRecognition!);
      if (timeSinceSuccess < _recognitionCooldown) {
        if (kDebugMode) {
          debugPrint('🎯 Skipping: Recognition cooldown (${(_recognitionCooldown.inSeconds - timeSinceSuccess.inSeconds)}s remaining)');
        }
        return false;
      }
    }

    // Check if face has been absent and now returned (legacy check)
    if (_shouldProcessNewFace()) {
      if (kDebugMode) {
        debugPrint('🎯 Processing: Face returned after absence period');
      }
      return true;
    }

    // For security apps: Process periodically even for stable faces
    // This ensures we don't miss gradual face changes or spoofing attempts
    // But with balanced interval to avoid server overload
    final timeSinceLastProcess = _lastProcessTime != null
        ? DateTime.now().difference(_lastProcessTime!)
        : Duration.zero;

    // Process every X seconds for stable faces (balanced security/performance)
    final securityReCheckInterval = Duration(seconds: _securityReCheckSeconds);
    if (timeSinceLastProcess >= securityReCheckInterval) {
      if (kDebugMode) {
        debugPrint('🎯 Processing: Security re-check interval (${securityReCheckInterval.inSeconds}s)');
      }
      return true;
    }

    // Optional: Check for significant movement (less important now)
    final currentBox = currentFace.boundingBox;
    final lastBox = lastFace.boundingBox;

    final positionDiff = ((currentBox.left - lastBox.left).abs() +
                         (currentBox.top - lastBox.top).abs()) / 2;
    final sizeDiff = ((currentBox.width - lastBox.width).abs() +
                     (currentBox.height - lastBox.height).abs()) / 2;

    // Calculate movement percentage
    final positionPercent = (positionDiff / currentBox.width) * 100;
    final sizePercent = (sizeDiff / currentBox.width) * 100;

    // More conservative thresholds to reduce server load
    // Only trigger on major movements that likely indicate different person
    final hasSignificantMovement = positionPercent > _majorMovementThreshold ||
                                  sizePercent > _majorSizeThreshold;

    if (kDebugMode) {
      debugPrint('🎯 Face tracking analysis:');
      debugPrint('   Position movement: ${positionDiff.toStringAsFixed(1)}px (${positionPercent.toStringAsFixed(1)}%)');
      debugPrint('   Size change: ${sizeDiff.toStringAsFixed(1)}px (${sizePercent.toStringAsFixed(1)}%)');
      debugPrint('   Time since last process: ${timeSinceLastProcess.inSeconds}s');
      debugPrint('   Face exit detected: $_faceExitDetected');
      debugPrint('   Decision: ${hasSignificantMovement ? "PROCESS (movement)" : "SKIP (stable)"}');
    }

    return hasSignificantMovement;
  }

  /// Update successful recognition timestamp for cooldown management
  void updateSuccessfulRecognition() {
    _lastSuccessfulRecognition = DateTime.now();
    if (kDebugMode) {
      debugPrint('✅ Recognition successful - cooldown started (${_recognitionCooldown.inSeconds}s)');
      debugPrint('📊 Timing: Next recognition possible at ${DateTime.now().add(_recognitionCooldown).toIso8601String().substring(11, 19)}');
    }
  }

  /// Reset overlay state when new face detected (security measure)
  void resetForNewFace() {
    _lastProcessedFace = null;
    _lastCapturedImageBytes = null;
    _lastFaceQuality = 0.0;
    _lastProcessTime = null;
    _lastSuccessfulRecognition = null;
    _faceExitDetected = false;

    if (kDebugMode) {
      debugPrint('🔄 Face crop overlay reset for new face detection');
    }
  }





  /// Get color based on face quality
  Color _getQualityColor() {
    if (_lastFaceQuality >= 0.8) {
      return const Color(0xFF34D38D); // Green for excellent
    } else if (_lastFaceQuality >= 0.6) {
      return const Color(0xFFFFA500); // Orange for good
    } else {
      return const Color(0xFFFF6B6B); // Red for poor
    }
  }

  /// Process face capture from current camera image (no additional capture needed)
  Future<void> _processFaceCropping() async {
    if (widget.detectedFace == null || widget.faceDetectionProvider == null) return;

    final currentFace = widget.detectedFace!;
    final faceQuality = _getFaceQuality(currentFace);

    // Get latest camera image from face detection provider
    final cameraImage = widget.faceDetectionProvider!.latestCameraImage;
    final cameraDescription = widget.faceDetectionProvider!.latestCameraDescription;

    if (cameraImage == null || cameraDescription == null) {
      debugPrint('❌ No camera image available for face recognition');
      return;
    }

    setState(() {
      _isProcessing = true;
      _lastProcessTime = DateTime.now();
    });

    try {
      debugPrint('🎯 Processing face from camera stream (quality: ${faceQuality.toStringAsFixed(2)})');

      // Crop face using detection bounds for better avatar display
      final croppedFaceBytes = await FaceCroppingService.cropFaceFromCameraImage(
        cameraImage: cameraImage,
        cameraDescription: cameraDescription,
        face: currentFace,
        paddingFactor: _avatarPaddingFactor, // Configurable padding
        targetSize: _avatarTargetSize, // Configurable target size
      );

      // Also get full image for recognition (if needed)
      final fullImageBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
        quality: 85, // Good quality for face recognition
      );

      if (croppedFaceBytes != null && fullImageBytes != null && mounted) {
        setState(() {
          _lastCapturedImageBytes = croppedFaceBytes; // Store cropped face for display
          _lastProcessedFace = currentFace;
          _lastFaceQuality = faceQuality;
        });

        // Update captured face provider for avatar display with cropped face
        widget.capturedFaceProvider.updateCapturedFace(
          imageBytes: croppedFaceBytes, // Use cropped face for avatar
          face: currentFace,
          quality: faceQuality,
        );

        debugPrint('✅ Face cropped: ${croppedFaceBytes.length} bytes (200x200px)');
        debugPrint('✅ Full image: ${fullImageBytes.length} bytes');

        // Notify parent about face crop if quality is good enough
        // Use full image for recognition, cropped face is for avatar display
        if (faceQuality >= 0.4 && widget.onFaceCropped != null) {
          debugPrint('✅ Sending full image for recognition (quality: ${(faceQuality * 100).toStringAsFixed(1)}%)');
          widget.onFaceCropped!(fullImageBytes, faceQuality); // Use full image for recognition
        } else {
          debugPrint('❌ Face NOT sent for recognition:');
          debugPrint('   Quality: ${(faceQuality * 100).toStringAsFixed(1)}% (need >= 40%)');
          debugPrint('   Callback available: ${widget.onFaceCropped != null}');
        }
      } else {
        debugPrint('❌ Failed to process face images');
        if (croppedFaceBytes == null) debugPrint('   - Cropped face failed');
        if (fullImageBytes == null) debugPrint('   - Full image failed');
      }
    } catch (e) {
      debugPrint('❌ Error processing camera image: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    return Positioned(
      bottom: 100, // Position above bottom UI elements
      right: 20,   // 20px from right edge
      child: SizedBox(
        width: 120,
        height: 120,
        child: _buildFaceCropWidget(),
      ),
    );
  }

  /// Build the face crop display widget
  Widget _buildFaceCropWidget() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B).withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(11),
        child: _buildContent(),
      ),
    );
  }

  /// Build widget content based on current state
  Widget _buildContent() {
    if (_isProcessing) {
      return _buildProcessingIndicator();
    }
    
    if (_lastCapturedImageBytes != null) {
      return _buildCapturedFaceImage();
    }
    
    if (widget.detectedFace != null) {
      return _buildFaceDetectedIndicator();
    }
    
    return _buildNoFaceIndicator();
  }

  /// Build processing indicator
  Widget _buildProcessingIndicator() {
    final currentQuality = widget.detectedFace != null
        ? _getFaceQuality(widget.detectedFace!)
        : 0.0;

    return Container(
      color: const Color(0xFF1E293B),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 2,
                value: currentQuality, // Show quality as progress
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Capturing...',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Q: ${(currentQuality * 100).round()}%',
              style: TextStyle(
                color: _getQualityColorForValue(currentQuality),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get quality color for a specific value
  Color _getQualityColorForValue(double quality) {
    if (quality >= 0.8) {
      return const Color(0xFF34D38D); // Green for excellent
    } else if (quality >= 0.6) {
      return const Color(0xFFFFA500); // Orange for good
    } else {
      return const Color(0xFFFF6B6B); // Red for poor
    }
  }

  /// Build captured face image display
  Widget _buildCapturedFaceImage() {
    return Stack(
      children: [
        // Captured face image from camera stream
        Image.memory(
          _lastCapturedImageBytes!,
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorIndicator();
          },
        ),
        
        // Quality indicator overlay
        Positioned(
          top: 4,
          right: 4,
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _getQualityColor(),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Center(
              child: Text(
                '${(_lastFaceQuality * 100).round()}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        
        // Info overlay at bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(11),
                bottomRight: Radius.circular(11),
              ),
            ),
            child: const Text(
              'Face Captured',
              style: TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  /// Build face detected indicator (before capturing)
  Widget _buildFaceDetectedIndicator() {
    final currentQuality = widget.detectedFace != null
        ? _getFaceQuality(widget.detectedFace!)
        : 0.0;
    final isHighQuality = currentQuality >= _qualityThreshold;

    return Container(
      color: const Color(0xFF1E293B),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isHighQuality ? Icons.face : Icons.face_retouching_natural,
              color: isHighQuality ? AppColors.primary : Colors.orange,
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              isHighQuality ? 'Ready' : 'Adjusting...',
              style: TextStyle(
                color: isHighQuality ? Colors.white : Colors.orange,
                fontSize: 9,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              'Q: ${(currentQuality * 100).round()}%',
              style: TextStyle(
                color: _getQualityColorForValue(currentQuality),
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build no face indicator
  Widget _buildNoFaceIndicator() {
    return Container(
      color: const Color(0xFF1E293B),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.face_retouching_off,
              color: Colors.grey[400],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'No Face',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build error indicator
  Widget _buildErrorIndicator() {
    return Container(
      color: const Color(0xFF1E293B),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 32,
            ),
            SizedBox(height: 8),
            Text(
              'Error',
              style: TextStyle(
                color: Colors.red,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../models/enhanced_face_capture_result.dart';
import '../core/constants/face_cropping_constants.dart';
import 'face_cropping_service.dart';
import 'face_cropping_api_service.dart';

/// Enhanced face capture service that includes face cropping functionality
class EnhancedFaceCaptureService {
  static const String _logTag = '📸 EnhancedFaceCapture';
  
  final FaceCroppingApiService _apiService = FaceCroppingApiService();
  
  /// Process captured face images with cropping and API integration
  /// 
  /// [capturedImages] - Map of captured images by direction
  /// [detectedFaces] - Map of detected faces by direction
  /// [processingMode] - API processing mode to use
  /// [enabledSideEffects] - List of side effects to execute
  /// [context] - Additional context data
  /// [cropPadding] - Padding around face region
  /// [outputQuality] - JPEG quality for cropped images
  /// 
  /// Returns: Enhanced face capture result with cropped images and API results
  Future<EnhancedFaceCaptureResult> processWithCropping({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    ProcessingMode processingMode = ProcessingMode.synchronous,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
    double cropPadding = FaceCroppingConstants.defaultPadding,
    int outputQuality = FaceCroppingConstants.defaultOutputQuality,
  }) async {
    try {
      debugPrint('$_logTag Starting enhanced face capture processing');
      debugPrint('$_logTag Processing mode: ${processingMode.name}');
      debugPrint('$_logTag Images to process: ${capturedImages.length}');
      
      // Step 1: Crop all captured faces
      final croppedImages = await _cropAllFaces(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
        cropPadding: cropPadding,
        outputQuality: outputQuality,
      );
      
      debugPrint('$_logTag Successfully cropped ${croppedImages.length} faces');
      
      // Step 2: Process with selected API method
      switch (processingMode) {
        case ProcessingMode.synchronous:
          final syncResults = await _processSynchronously(
            capturedImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            enabledSideEffects: enabledSideEffects,
            context: context,
            cropPadding: cropPadding,
            outputQuality: outputQuality,
          );
          
          return EnhancedFaceCaptureResult.success(
            originalImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            syncResults: syncResults,
          );
          
        case ProcessingMode.asynchronous:
          final asyncResults = await _processAsynchronously(
            capturedImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            enabledSideEffects: enabledSideEffects,
            context: context,
            cropPadding: cropPadding,
            outputQuality: outputQuality,
          );
          
          return EnhancedFaceCaptureResult.success(
            originalImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            asyncResults: asyncResults,
          );
          
        case ProcessingMode.queueBased:
          final queueIds = await _processWithQueue(
            capturedImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            enabledSideEffects: enabledSideEffects,
            context: context,
            cropPadding: cropPadding,
            outputQuality: outputQuality,
          );
          
          return EnhancedFaceCaptureResult.success(
            originalImages: capturedImages,
            croppedImages: croppedImages,
            detectedFaces: detectedFaces,
            queueOperationIds: queueIds,
          );
      }
      
    } catch (e, stackTrace) {
      debugPrint('$_logTag ❌ Enhanced face capture processing failed: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      
      return EnhancedFaceCaptureResult.failure(error: e.toString());
    }
  }
  
  /// Crop all captured faces
  Future<Map<FaceDirection, String?>> _cropAllFaces({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    required double cropPadding,
    required int outputQuality,
  }) async {
    final croppedImages = <FaceDirection, String?>{};
    
    for (final entry in capturedImages.entries) {
      final direction = entry.key;
      final imagePath = entry.value;
      
      if (imagePath == null) continue;
      
      final face = detectedFaces[direction];
      if (face == null) {
        debugPrint('$_logTag ⚠️ No detected face for direction: ${direction.name}');
        continue;
      }
      
      try {
        final croppedPath = await FaceCroppingService.cropFaceFromImage(
          imagePath: imagePath,
          face: face,
          padding: cropPadding,
          outputQuality: outputQuality,
        );
        
        if (croppedPath != null) {
          croppedImages[direction] = croppedPath;
          debugPrint('$_logTag ✅ Cropped face for ${direction.name}: $croppedPath');
        } else {
          debugPrint('$_logTag ❌ Failed to crop face for ${direction.name}');
        }
        
      } catch (e) {
        debugPrint('$_logTag ❌ Error cropping face for ${direction.name}: $e');
      }
    }
    
    return croppedImages;
  }
  
  /// Process faces synchronously
  Future<Map<FaceDirection, FaceCroppingSyncResult>> _processSynchronously({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, String?> croppedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
    required double cropPadding,
    required int outputQuality,
  }) async {
    final results = <FaceDirection, FaceCroppingSyncResult>{};
    
    for (final entry in capturedImages.entries) {
      final direction = entry.key;
      final imagePath = entry.value;
      
      if (imagePath == null) continue;
      
      final face = detectedFaces[direction];
      if (face == null) continue;
      
      try {
        final result = await _apiService.processFaceCroppingSynchronous(
          imagePath: imagePath,
          face: face,
          context: {
            ...?context,
            'direction': direction.name,
            'processing_mode': 'synchronous',
          },
          enabledSideEffects: enabledSideEffects,
          cropPadding: cropPadding,
          outputQuality: outputQuality,
        );
        
        results[direction] = result;
        
        if (result.success) {
          debugPrint('$_logTag ✅ Synchronous processing completed for ${direction.name}');
        } else {
          debugPrint('$_logTag ❌ Synchronous processing failed for ${direction.name}: ${result.error}');
        }
        
      } catch (e) {
        debugPrint('$_logTag ❌ Error in synchronous processing for ${direction.name}: $e');
      }
    }
    
    return results;
  }
  
  /// Process faces asynchronously
  Future<Map<FaceDirection, FaceCroppingAsyncResult>> _processAsynchronously({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, String?> croppedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
    required double cropPadding,
    required int outputQuality,
  }) async {
    final results = <FaceDirection, FaceCroppingAsyncResult>{};
    final completers = <FaceDirection, Completer<FaceCroppingAsyncResult>>{};
    
    // Start all async operations
    for (final entry in capturedImages.entries) {
      final direction = entry.key;
      final imagePath = entry.value;
      
      if (imagePath == null) continue;
      
      final face = detectedFaces[direction];
      if (face == null) continue;
      
      final completer = Completer<FaceCroppingAsyncResult>();
      completers[direction] = completer;
      
      try {
        await _apiService.processFaceCroppingAsynchronous(
          imagePath: imagePath,
          face: face,
          onProgress: (progress, status) {
            debugPrint('$_logTag ${direction.name} progress: ${(progress * 100).toInt()}% - $status');
          },
          onComplete: (result) {
            results[direction] = result;
            completer.complete(result);
          },
          onError: (error) {
            debugPrint('$_logTag ❌ Async processing error for ${direction.name}: $error');
            completer.completeError(error);
          },
          context: {
            ...?context,
            'direction': direction.name,
            'processing_mode': 'asynchronous',
          },
          enabledSideEffects: enabledSideEffects,
          cropPadding: cropPadding,
          outputQuality: outputQuality,
        );
        
      } catch (e) {
        debugPrint('$_logTag ❌ Error starting async processing for ${direction.name}: $e');
        completer.completeError(e);
      }
    }
    
    // Wait for all operations to complete
    await Future.wait(
      completers.values.map((completer) => completer.future),
      eagerError: false,
    );
    
    debugPrint('$_logTag ✅ All asynchronous operations completed');
    
    return results;
  }
  
  /// Process faces with queue
  Future<List<String>> _processWithQueue({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, String?> croppedImages,
    required Map<FaceDirection, Face> detectedFaces,
    List<SideEffectType>? enabledSideEffects,
    Map<String, dynamic>? context,
    required double cropPadding,
    required int outputQuality,
  }) async {
    final queueIds = <String>[];
    
    for (final entry in capturedImages.entries) {
      final direction = entry.key;
      final imagePath = entry.value;
      
      if (imagePath == null) continue;
      
      final face = detectedFaces[direction];
      if (face == null) continue;
      
      try {
        final queueId = await _apiService.addToFaceCroppingQueue(
          imagePath: imagePath,
          face: face,
          priority: _getDirectionPriority(direction),
          context: {
            ...?context,
            'direction': direction.name,
            'processing_mode': 'queue_based',
          },
          enabledSideEffects: enabledSideEffects,
          cropPadding: cropPadding,
          outputQuality: outputQuality,
        );
        
        queueIds.add(queueId);
        debugPrint('$_logTag ✅ Added ${direction.name} to queue: $queueId');
        
      } catch (e) {
        debugPrint('$_logTag ❌ Error adding ${direction.name} to queue: $e');
      }
    }
    
    debugPrint('$_logTag ✅ Added ${queueIds.length} items to processing queue');
    
    return queueIds;
  }
  
  /// Get processing priority for face direction
  int _getDirectionPriority(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return 10; // Highest priority
      case FaceDirection.left:
      case FaceDirection.right:
        return 5; // Medium priority
      case FaceDirection.top:
      case FaceDirection.bottom:
        return 3; // Lower priority
      case FaceDirection.unknown:
        return 1; // Lowest priority
    }
  }
  
  /// Get queue status
  Map<String, dynamic> getQueueStatus() {
    return _apiService.getQueueStatus();
  }
}

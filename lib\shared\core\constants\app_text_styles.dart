import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Heading styles
  static const TextStyle heading = TextStyle(
    fontSize: 20.0,
    fontWeight: FontWeight.w600,
    color: AppColors.textHeading,
  );
  
  // Subtitle styles
  static const TextStyle subtitle = TextStyle(
    fontSize: 14.0,
    color: AppColors.textSubtle,
  );
  
  // Label styles
  static const TextStyle label = TextStyle(
    fontSize: 13.0,
    fontWeight: FontWeight.w500,
    color: AppColors.textLabel,
  );
  
  // Input text styles
  static const TextStyle inputText = TextStyle(
    fontSize: 13.0,
    color: AppColors.textLabel,
  );
  
  // Placeholder styles
  static const TextStyle placeholder = TextStyle(
    fontSize: 13.0,
    color: AppColors.textPlaceholder,
  );
  
  // Button text styles
  static const TextStyle buttonText = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );
  
  // Link text styles
  static const TextStyle linkText = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
  );
  
  // Required asterisk style
  static const TextStyle requiredAsterisk = TextStyle(
    fontSize: 14.0,
    color: AppColors.errorRed,
  );
}

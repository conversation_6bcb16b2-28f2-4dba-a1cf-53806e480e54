import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dashboard.dart';
import 'profile_screen.dart';
import 'tools_screen/tools_screen.dart';
import 'tools_screen/system_management_screen.dart';
import 'users_screen.dart';
import 'notifications_screen.dart';
import '../../../../shared/components/tabs_bar.dart';
import '../../../../shared/presentation/widgets/navigation/back_navigation_wrapper.dart';
import '../../../../shared/services/back_navigation_service.dart';
import '../../routes/mobile_route_names.dart';

/// Main screen với nested navigation cho mỗi tab
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedTabIndex = 0;

  // Navigator keys cho từng tab để quản lý nested navigation
  final List<GlobalKey<NavigatorState>> _navigatorKeys = [
    GlobalKey<NavigatorState>(), // Dashboard
    GlobalKey<NavigatorState>(), // Tools
    GlobalKey<NavigatorState>(), // Notifications
    GlobalKey<NavigatorState>(), // Profile
  ];

  @override
  void initState() {
    super.initState();
    // Set initial tab based on current route
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setInitialTabFromRoute();
    });
  }

  void _setInitialTabFromRoute() {
    final currentRoute = GoRouterState.of(context).uri.path;
    setState(() {
      switch (currentRoute) {
        case MobileRouteNames.dashboard:
          _selectedTabIndex = 0;
          break;
        case MobileRouteNames.tools:
          _selectedTabIndex = 1;
          break;
        case MobileRouteNames.notifications:
          _selectedTabIndex = 2;
          break;
        case MobileRouteNames.profile:
          _selectedTabIndex = 3;
          break;
        default:
          _selectedTabIndex = 0; // Default to dashboard
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackNavigationWrapper(
      isRootScreen: true,
      onExitConfirmation: _handleExitConfirmation,
      child: Scaffold(
        extendBody: true,
        body: IndexedStack(
          index: _selectedTabIndex,
          children: [
            _buildTabNavigator(0, const DashboardScreen()),
            _buildTabNavigator(1, const ToolsScreen()),
            _buildTabNavigator(2, const NotificationsScreen()),
            _buildTabNavigator(3, const ProfileScreen()),
          ],
        ),
        bottomNavigationBar: SafeArea(
          child: TabsBar(
            selectedIndex: _selectedTabIndex,
            onTabSelected: _onTabSelected,
          ),
        ),
      ),
    );
  }

  Widget _buildTabNavigator(int tabIndex, Widget initialScreen) {
    return Navigator(
      key: _navigatorKeys[tabIndex],
      onGenerateRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => _getScreenForRoute(tabIndex, settings.name ?? '/'),
          settings: settings,
        );
      },
    );
  }

  Widget _getScreenForRoute(int tabIndex, String routeName) {
    switch (tabIndex) {
      case 0: // Home tab
        switch (routeName) {
          case '/':
            return const DashboardScreen();
          default:
            return const DashboardScreen();
        }
      case 1: // Tools tab
        switch (routeName) {
          case '/':
            return const ToolsScreen();
          case '/system-management':
            return const SystemManagementScreen();
          case '/users':
            return const UsersScreen();
          default:
            return const ToolsScreen();
        }
      case 2: // Notifications tab
        switch (routeName) {
          case '/':
            return const NotificationsScreen();
          default:
            return const NotificationsScreen();
        }
      case 3: // Account tab
        switch (routeName) {
          case '/':
            return const ProfileScreen();
          default:
            return const ProfileScreen();
        }
      default:
        return const DashboardScreen();
    }
  }

  void _onTabSelected(int index) {
    if (_selectedTabIndex == index) {
      // Nếu tap vào tab hiện tại, pop về root của tab đó
      _navigatorKeys[index].currentState?.popUntil((route) => route.isFirst);
    } else {
      // Chuyển tab
      setState(() => _selectedTabIndex = index);
    }
  }

  void _handleExitConfirmation() {
    // Kiểm tra xem có thể pop trong tab hiện tại không
    final currentNavigator = _navigatorKeys[_selectedTabIndex].currentState;
    if (currentNavigator != null && currentNavigator.canPop()) {
      // Nếu có thể pop trong tab hiện tại, pop về màn hình trước
      currentNavigator.pop();
    } else if (_selectedTabIndex == 0) {
      // Nếu đang ở tab đầu tiên (Dashboard) và không thể pop, hiển thị exit confirmation
      BackNavigationService().handleTabBackNavigation(context, _selectedTabIndex);
    } else {
      // Chuyển về tab đầu tiên (Dashboard)
      setState(() {
        _selectedTabIndex = 0;
      });
    }
  }
}

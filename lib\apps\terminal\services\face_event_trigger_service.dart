import 'dart:async';
import 'package:flutter/foundation.dart';
import 'relay_trigger_service.dart';
import '../models/relay_trigger_config.dart';
import '../../../shared/services/relay_management_service.dart';
import '../../../shared/services/relay_config_service.dart';
import '../../../shared/services/relay_throttle_service.dart';

/// Centralized service for handling all face detection and recognition events
/// that trigger relay actions and other side effects.
/// 
/// This service acts as a bridge between face detection/recognition events
/// and the actual relay control, making it easy to configure and extend
/// with additional side effects in the future.
class FaceEventTriggerService {
  static final FaceEventTriggerService _instance = FaceEventTriggerService._internal();
  factory FaceEventTriggerService() => _instance;
  FaceEventTriggerService._internal();

  final RelayTriggerService _relayTriggerService = RelayTriggerService();
  final RelayManagementService _relayManagementService = RelayManagementService.instance;
  bool _isInitialized = false;

  // Configuration for different trigger behaviors
  FaceEventTriggerConfig _config = FaceEventTriggerConfig.defaultConfig;

  // Face detection state tracking (no debouncing - keep real-time response)
  bool? _lastFaceState;
  
  bool get isInitialized => _isInitialized;
  FaceEventTriggerConfig get config => _config;

  /// Initialize the service with configuration
  Future<void> initialize({FaceEventTriggerConfig? config}) async {
    if (config != null) {
      _config = config;
    }

    // Initialize relay throttle service
    await RelayThrottleService.instance.initialize();

    // Initialize relay trigger service
    await _relayTriggerService.initialize(
      config: _config.relayTriggerConfig,
    );

    // Auto-connect to USB-TTL relay device if enabled
    if (_config.enableAutoUsbTtlConnect) {
      await _initializeAutoUsbTtlConnect();
    }

    _isInitialized = true;

    if (kDebugMode) {
      print('🔧 FaceEventTriggerService initialized');
      print('   Face detection triggers: ${_config.enableFaceDetectionTriggers}');
      print('   Face recognition triggers: ${_config.enableFaceRecognitionTriggers}');
      print('   Auto USB-TTL connect: ${_config.enableAutoUsbTtlConnect}');
      print('   Relay throttling: enabled');
    }
  }

  /// Update configuration
  void updateConfig(FaceEventTriggerConfig newConfig) {
    _config = newConfig;
    _relayTriggerService.updateConfig(_config.relayTriggerConfig);
    
    if (kDebugMode) {
      print('🔧 FaceEventTriggerService configuration updated');
    }
  }

  /// Handle face detection state change with intelligent LED control (real-time, no debouncing)
  void onFaceDetectionStateChanged(bool hasFace) {
    if (!_isInitialized || !_config.enableFaceDetectionTriggers) {
      return;
    }

    if (kDebugMode) {
      print('👁️ Face detection state changed: ${hasFace ? 'DETECTED' : 'NOT DETECTED'}');
    }

    try {
      if (hasFace) {
        // Face detected -> Always trigger LED ON immediately (real-time response)
        _triggerFaceDetected();
      } else {
        // Face NOT detected -> Let LED timer handle timeout naturally
        // Don't force LED OFF immediately to avoid rapid on/off switching
        if (kDebugMode) {
          print('👁️ Face lost - letting LED timer handle timeout naturally');
        }
        // Note: We don't call _triggerFaceNotDetected() here
        // The LED will turn OFF automatically after its timer expires
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling face detection state change: $e');
      }
    }
  }

  /// Handle face recognition result
  void onFaceRecognitionResult(dynamic result) {
    if (!_isInitialized || !_config.enableFaceRecognitionTriggers) {
      return;
    }

    if (result == null) {
      return;
    }

    try {
      final bool isRecognized = result.recognized ?? false;
      final bool accessGranted = result.allowAccess ?? false;
      
      if (isRecognized && accessGranted) {
        // Access granted -> Trigger R1 (Terminal Door)
        _triggerAccessGranted(result);
      } else {
        // Access denied -> Trigger warning
        _triggerAccessDenied(result);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling face recognition result: $e');
      }
    }
  }

  /// Handle face recognition start event
  void onFaceRecognitionStart(double faceQuality) {
    if (!_isInitialized || !_config.enableFaceRecognitionTriggers) {
      return;
    }

    try {
      _triggerFaceRecognitionStart(faceQuality);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling face recognition start: $e');
      }
    }
  }

  /// Handle no face timeout event
  void onNoFaceTimeout() {
    if (!_isInitialized || !_config.enableFaceDetectionTriggers) {
      return;
    }

    try {
      _triggerNoFaceTimeout();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling no face timeout: $e');
      }
    }
  }

  // Private trigger methods
  void _triggerFaceDetected() {
    // Use ONLY throttled LED control for face detection
    RelayThrottleService.instance.controlLed(faceDetected: true);

    // Log metadata for debugging (without triggering legacy service)
    if (kDebugMode) {
      final metadata = {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'face_detected',
        'relay_target': 'R0_LED',
      };
      print('🔌 Face detected -> R0 (LED) with throttling');
      print('   Metadata: $metadata');
    }
  }

  void _triggerFaceNotDetected() {
    // Use ONLY throttled LED control for face not detected
    RelayThrottleService.instance.controlLed(faceDetected: false);

    // Log metadata for debugging (without triggering legacy service)
    if (kDebugMode) {
      final metadata = {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'face_not_detected',
        'relay_target': 'R0_LED',
      };
      print('🔌 Face not detected -> R0 (LED) with throttling');
      print('   Metadata: $metadata');
    }
  }

  void _triggerAccessGranted(dynamic result) {
    // Use ONLY throttled door control for access granted
    RelayThrottleService.instance.controlDoor(accessGranted: true);

    // Log metadata for debugging (without triggering legacy service)
    if (kDebugMode) {
      final throttleConfig = RelayThrottleService.instance.config;
      final metadata = {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'access_granted',
        'relay_target': 'R1_TERMINAL',
        'user_name': result.user?.name ?? 'Unknown',
        'user_id': result.user?.id ?? 'Unknown',
        'confidence': result.confidence ?? 0.0,
        'access_reason': result.reason ?? 'Authorized',
      };
      print('🔌 Access granted -> R1 (Terminal Door) for ${throttleConfig.doorUnlockDuration}ms with throttling');
      print('   User: ${result.user?.name ?? 'Unknown'}');
      print('   Confidence: ${((result.confidence ?? 0.0) * 100).toStringAsFixed(1)}%');
      print('   Metadata: $metadata');
    }
  }

  void _triggerAccessDenied(dynamic result) {
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.accessDenied,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'access_denied',
        'relay_target': 'R0_LED_WARNING',
        'user_name': result.user?.name ?? 'Unknown',
        'user_id': result.user?.id ?? 'Unknown',
        'confidence': result.confidence ?? 0.0,
        'access_reason': result.reason ?? 'Access denied',
      },
    );

    if (kDebugMode) {
      print('🔌 Triggered: Access denied -> R0 (LED Warning)');
      print('   User: ${result.user?.name ?? 'Unknown'}');
      print('   Reason: ${result.reason ?? 'Access denied'}');
    }
  }

  void _triggerFaceRecognitionStart(double faceQuality) {
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.faceRecognitionStart,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'face_recognition_start',
        'relay_target': 'R2_R3_BACKUP',
        'face_quality': faceQuality,
      },
    );

    if (kDebugMode) {
      print('🔌 Triggered: Face recognition start -> R2, R3 (Backup sensors)');
      print('   Face quality: ${(faceQuality * 100).toStringAsFixed(1)}%');
    }
  }

  void _triggerNoFaceTimeout() {
    _relayTriggerService.triggerScenario(
      RelayTriggerScenario.noFaceTimeout,
      metadata: {
        'timestamp': DateTime.now().toIso8601String(),
        'trigger_source': 'face_event_trigger_service',
        'action': 'no_face_timeout',
        'relay_target': 'R0_LED_OFF',
        'reason': 'Power saving mode',
      },
    );

    if (kDebugMode) {
      print('🔌 Triggered: No face timeout -> R0 (LED) OFF (Power saving)');
    }
  }

  /// Emergency stop all relays
  Future<void> emergencyStopAllRelays() async {
    await _relayTriggerService.emergencyStop();
    if (kDebugMode) {
      print('🚨 Emergency stop - All relays turned off immediately');
    }
  }

  /// Get relay trigger service for direct access if needed
  RelayTriggerService get relayTriggerService => _relayTriggerService;

  /// Initialize auto USB-TTL connection
  Future<void> _initializeAutoUsbTtlConnect() async {
    try {
      if (kDebugMode) {
        print('🔌 Initializing auto USB-TTL connection...');
      }

      // Create default relay device configuration
      final deviceConfig = RelayDeviceConfig(
        deviceId: _config.relayTriggerConfig.deviceId,
        deviceName: 'Auto USB-TTL Relay',
        relayCount: 4, // Default 4 relays (R0-R3)
        baudRate: 9600, // Default baud rate for USB-TTL relay modules
      );

      // Initialize relay management service with auto-connect enabled
      await _relayManagementService.initialize(
        config: deviceConfig,
        autoConnect: true, // Enable auto-connect to first available USB-TTL device
      );

      if (_relayManagementService.isConnected) {
        if (kDebugMode) {
          print('✅ Auto USB-TTL connection successful');
          print('   Device: ${deviceConfig.deviceName}');
          print('   Relay count: ${deviceConfig.relayCount}');
          print('   Baud rate: ${deviceConfig.baudRate}');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Auto USB-TTL connection failed - no compatible devices found');
          print('   Supported devices: FTDI FT232, Prolific PL2303, Silicon Labs CP210x, CH340/CH341');
          print('   Make sure USB-TTL relay device is connected and drivers are installed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during auto USB-TTL connection: $e');
        print('   Relay triggers will work without USB-TTL connection');
        print('   You can manually connect via relay settings');
      }
    }
  }

  /// Dispose the service
  void dispose() {
    _isInitialized = false;
    if (kDebugMode) {
      print('🔧 FaceEventTriggerService disposed');
    }
  }
}

/// Configuration for face event triggers
class FaceEventTriggerConfig {
  final bool enableFaceDetectionTriggers;
  final bool enableFaceRecognitionTriggers;
  final bool enableAutoUsbTtlConnect;
  final RelayTriggerConfig relayTriggerConfig;

  const FaceEventTriggerConfig({
    this.enableFaceDetectionTriggers = true,
    this.enableFaceRecognitionTriggers = true,
    this.enableAutoUsbTtlConnect = true,
    required this.relayTriggerConfig,
  });

  /// Default configuration
  static FaceEventTriggerConfig get defaultConfig => FaceEventTriggerConfig(
    enableFaceDetectionTriggers: true,
    enableFaceRecognitionTriggers: true,
    enableAutoUsbTtlConnect: true,
    relayTriggerConfig: RelayTriggerConfig.defaultConfig,
  );

  FaceEventTriggerConfig copyWith({
    bool? enableFaceDetectionTriggers,
    bool? enableFaceRecognitionTriggers,
    bool? enableAutoUsbTtlConnect,
    RelayTriggerConfig? relayTriggerConfig,
  }) => FaceEventTriggerConfig(
    enableFaceDetectionTriggers: enableFaceDetectionTriggers ?? this.enableFaceDetectionTriggers,
    enableFaceRecognitionTriggers: enableFaceRecognitionTriggers ?? this.enableFaceRecognitionTriggers,
    enableAutoUsbTtlConnect: enableAutoUsbTtlConnect ?? this.enableAutoUsbTtlConnect,
    relayTriggerConfig: relayTriggerConfig ?? this.relayTriggerConfig,
  );
}

<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<item android:state_enabled="false" android:drawable="@color/gray_blue"></item>
    <item android:state_pressed="true"> <!-- pressed the button status -->
<shape>

            <!-- 开始颜色="" 结束颜色="" 颜色渐变="" -->
            <gradient android:angle="270" android:endColor="#bdeadc" android:startColor="#bdeadc" />
            <!-- //渐变方向     按钮边缘="" 边缘宽="" -->
            <stroke android:width="1sp" android:color="#000000" />
            <!-- //边缘颜色   按钮四个圆角="" -->
            <corners android:radius="0dp" />
            <!-- //半径    内边距="" 按钮文字和边缘距离="" -->
           <!-- <padding android:bottom="10dp" android:left="10dp" android:right="10dp" android:top="10dp" /> -->
        </shape></item>
    <item android:state_focused="true"> <!-- get the focus button sttaus -->
<shape>
            <gradient android:angle="270" android:endColor="#bdeadc" android:startColor="#bdeadc" />

            <stroke android:width="1sp" android:color="#000000" />

            <corners android:radius="0dp" />

            <padding android:bottom="10dp" android:left="10dp" android:right="10dp" android:top="10dp" />
        </shape></item>
    <item> <!-- no focus the button status(the normal status button) -->
<shape>
            <gradient android:angle="270" android:endColor="#3caf8b" android:startColor="#3caf8b" />

            <stroke android:width="1sp" android:color="#000000" />

            <corners android:radius="0dp" />

            <padding android:bottom="10dp" android:left="10dp" android:right="10dp" android:top="10dp" />
        </shape></item>

</selector>
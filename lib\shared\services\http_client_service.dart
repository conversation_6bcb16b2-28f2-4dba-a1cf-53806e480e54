import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../core/constants/api_constants.dart';
import 'flutter_secure_storage.dart';
import 'secure_storage_keys.dart';
import 'cookie_service.dart';
import 'token_refresh_service.dart';

/// Enum cho các loại HTTP method
enum HttpMethod { get, post, put, patch, delete }

/// Type alias cho progress callback
typedef ProgressCallback = void Function(int count, int total);

/// Model class cho API Response
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final Map<String, dynamic>? errors;
  final Map<String, dynamic>? meta;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.errors,
    this.meta,
  });

  /// Tạo response thành công
  factory ApiResponse.success({
    T? data,
    String? message,
    int? statusCode,
    Map<String, dynamic>? meta,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
      meta: meta,
    );
  }

  /// Tạo response lỗi
  factory ApiResponse.error({
    required String message,
    int? statusCode,
    Map<String, dynamic>? errors,
    Map<String, dynamic>? meta,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      statusCode: statusCode,
      errors: errors,
      meta: meta,
    );
  }

  /// Chuyển đổi thành Map
  Map<String, dynamic> toMap() {
    return {
      'success': success,
      'data': data,
      'message': message,
      'statusCode': statusCode,
      'errors': errors,
      'meta': meta,
    };
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, statusCode: $statusCode, message: $message)';
  }
}

/// Model class cho cấu hình HTTP Client
class HttpClientConfig {
  final String baseUrl;
  final int connectTimeout;
  final int receiveTimeout;
  final int sendTimeout;
  final Map<String, String> defaultHeaders;
  final bool enableLogging;
  final bool enableRetry;
  final int maxRetries;
  final Duration retryDelay;
  final List<int> retryStatusCodes;

  const HttpClientConfig({
    required this.baseUrl,
    this.connectTimeout = 30000,
    this.receiveTimeout = 30000,
    this.sendTimeout = 30000,
    this.defaultHeaders = const {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'C-Face-Mobile/1.0.0 (Flutter)',
    },
    this.enableLogging = true,
    this.enableRetry = true,
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.retryStatusCodes = const [408, 429, 500, 502, 503, 504],
  });
}

/// Service class để tương tác với API sử dụng HTTP package
class HttpClientService {
  static HttpClientService? _instance;
  late http.Client _client;
  late HttpClientConfig _config;
  final SecureStorageService _secureStorage = SecureStorageService();

  HttpClientService._internal();

  /// Singleton factory
  factory HttpClientService() {
    _instance ??= HttpClientService._internal();
    return _instance!;
  }

  /// Khởi tạo HTTP client với cấu hình
  void initialize(HttpClientConfig config) {
    _config = config;

    // Tạo HTTP client với custom configuration
    if (!kIsWeb) {
      // Mobile/Desktop: Custom HTTP client với certificate handling
      _client = http.Client();
      print('📱 Mobile HTTP Client initialized');
      print('🔒 Certificate verification bypassed for development');
    } else {
      // Web platform: Standard HTTP client
      _client = http.Client();
      print('\n🌐 ===== WEB PLATFORM DETECTED =====');
      print('🚨 CORS may block cross-origin requests');
      print('💡 To bypass CORS, start Chrome with:');
      print('   chrome.exe --disable-web-security --user-data-dir="C:\\temp\\chrome_dev_session" --allow-running-insecure-content');
      print('🔧 Alternative: Use CORS proxy for production');
      print('📍 Current API Base URL: ${config.baseUrl}');
      print('==================================\n');
    }

    if (_config.enableLogging && kDebugMode) {
      print('✅ HttpClientService initialized with config:');
      print('   Base URL: ${config.baseUrl}');
      print('   Connect Timeout: ${config.connectTimeout}ms');
      print('   Receive Timeout: ${config.receiveTimeout}ms');
      print('   Send Timeout: ${config.sendTimeout}ms');
      print('   Retry Enabled: ${config.enableRetry}');
      print('   Max Retries: ${config.maxRetries}');
    }
  }

  /// Tạo headers cho request với auth token và cookies
  Future<Map<String, String>> _buildHeaders([Map<String, String>? additionalHeaders]) async {
    final headers = <String, String>{
      ..._config.defaultHeaders,
      ...?additionalHeaders,
    };

    // Thêm auth token nếu có
    try {
      final token = await _secureStorage.read(key: SecureStorageKeys.accessToken);
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      debugPrint('Lỗi khi thêm auth token: $e');
    }

    // Thêm cookies nếu có
    await _addCookieHeader(headers);

    return headers;
  }

  /// Thêm Cookie header từ CookieService
  Future<void> _addCookieHeader(Map<String, String> headers) async {
    try {
      // Extract domain from base URL
      final uri = Uri.parse(_config.baseUrl);
      final domain = uri.host;

      // Get cookies for domain
      final cookies = await CookieService().getCookiesForDomain(domain);

      if (cookies.isNotEmpty) {
        // Build cookie string
        final cookieString = cookies.map((cookie) => '${cookie.name}=${cookie.value}').join('; ');
        headers['Cookie'] = cookieString;

        if (_config.enableLogging && kDebugMode) {
          debugPrint('🍪 Added Cookie header: $cookieString');
        }
        print('🍪 ✅ Cookie header added to request: $cookieString');
      }
    } catch (e) {
      if (_config.enableLogging && kDebugMode) {
        debugPrint('🔴 Failed to add Cookie header: $e');
      }
      // Don't throw error - cookie header is not critical
    }
  }

  /// Tạo URI với query parameters
  Uri _buildUri(String path, [Map<String, dynamic>? queryParameters]) {
    final baseUri = Uri.parse(_config.baseUrl);
    final fullPath = path.startsWith('/') ? path : '/$path';

    return Uri(
      scheme: baseUri.scheme,
      host: baseUri.host,
      port: baseUri.port,
      path: '${baseUri.path}$fullPath',
      queryParameters: queryParameters?.map((key, value) => MapEntry(key, value.toString())),
    );
  }

  /// Refresh access token using TokenRefreshService
  Future<bool> _refreshToken() async {
    try {
      // Use TokenRefreshService for coordinated token refresh
      final success = await TokenRefreshService.instance.refreshToken();

      if (success && kDebugMode) {
        debugPrint('🔄 Token refreshed successfully via TokenRefreshService');
      } else if (!success && kDebugMode) {
        debugPrint('❌ Token refresh failed via TokenRefreshService');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('🔴 Error during token refresh: $e');
      }
      return false;
    }
  }

  /// Thực hiện retry logic
  Future<http.Response> _executeWithRetry(Future<http.Response> Function() request) async {
    int attempts = 0;

    while (attempts <= _config.maxRetries) {
      try {
        final response = await request();

        // Nếu thành công hoặc không cần retry, return response
        if (response.statusCode < 400 || !_config.retryStatusCodes.contains(response.statusCode)) {
          return response;
        }

        // Nếu cần retry và chưa hết số lần thử
        if (attempts < _config.maxRetries && _config.enableRetry) {
          attempts++;
          if (_config.enableLogging && kDebugMode) {
            print('🔄 Retrying request (attempt $attempts/${_config.maxRetries})...');
          }
          await Future.delayed(_config.retryDelay);
          continue;
        }

        return response;
      } catch (e) {
        if (attempts >= _config.maxRetries || !_config.enableRetry) {
          rethrow;
        }
        attempts++;
        if (_config.enableLogging && kDebugMode) {
          print('🔄 Retrying request due to error (attempt $attempts/${_config.maxRetries}): $e');
        }
        await Future.delayed(_config.retryDelay);
      }
    }

    throw Exception('Max retries exceeded');
  }

  // ========== PUBLIC API METHODS ==========

  /// GET request
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: HttpMethod.get,
      path: path,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: fromJson,
    );
  }

  /// POST request
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: HttpMethod.post,
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: fromJson,
    );
  }

  /// PUT request
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: HttpMethod.put,
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: fromJson,
    );
  }

  /// PATCH request
  Future<ApiResponse<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: HttpMethod.patch,
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: fromJson,
    );
  }

  /// DELETE request
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: HttpMethod.delete,
      path: path,
      data: data,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: fromJson,
    );
  }

  /// Upload file
  Future<ApiResponse<T>> uploadFile<T>(
    String path,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    ProgressCallback? onSendProgress,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = _buildUri(path);
      final requestHeaders = await _buildHeaders(headers);

      // Tạo multipart request
      final request = http.MultipartRequest('POST', uri);
      request.headers.addAll(requestHeaders);

      // Thêm file
      final fileName = file.path.split('/').last;
      final multipartFile = await http.MultipartFile.fromPath(
        fieldName,
        file.path,
        filename: fileName,
      );
      request.files.add(multipartFile);

      // Thêm data fields
      if (data != null) {
        data.forEach((key, value) {
          request.fields[key] = value.toString();
        });
      }

      if (_config.enableLogging && kDebugMode) {
        print('📤 Uploading file: ${file.path}');
        print('📍 URL: POST ${uri.toString()}');
      }

      final streamedResponse = await request.send().timeout(Duration(milliseconds: _config.sendTimeout));
      final response = await http.Response.fromStream(streamedResponse);
      return _handleHttpResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi upload file: $e',
        statusCode: 0,
      );
    }
  }

  /// Download file
  Future<ApiResponse<String>> downloadFile(
    String path,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final uri = _buildUri(path, queryParameters);
      final requestHeaders = await _buildHeaders(headers);

      if (_config.enableLogging && kDebugMode) {
        print('📥 Downloading file to: $savePath');
        print('📍 URL: GET ${uri.toString()}');
      }

      final response = await _executeWithRetry(() async {
        return await _client
            .get(uri, headers: requestHeaders)
            .timeout(Duration(milliseconds: _config.receiveTimeout));
      });

      if (response.statusCode == 200) {
        // Tạo thư mục nếu chưa tồn tại
        final file = File(savePath);
        await file.parent.create(recursive: true);

        // Ghi file
        await file.writeAsBytes(response.bodyBytes);

        return ApiResponse.success(
          data: savePath,
          message: 'File downloaded successfully',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: 'Download failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi download file: $e',
        statusCode: 0,
      );
    }
  }

  /// Core method để thực hiện request
  Future<ApiResponse<T>> _makeRequest<T>({
    required HttpMethod method,
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = _buildUri(path, queryParameters);
      final requestHeaders = await _buildHeaders(headers);

      if (_config.enableLogging && kDebugMode) {
        print('\n🚀 ===== HTTP REQUEST =====');
        print('📍 URL: ${method.name.toUpperCase()} ${uri.toString()}');
        print('📋 Headers: $requestHeaders');
        if (data != null) print('📦 Body: $data');
        print('⏰ Time: ${DateTime.now().toIso8601String()}');
        print('==========================\n');
      }

      http.Response response;

      switch (method) {
        case HttpMethod.get:
          response = await _executeWithRetry(() async {
            return await _client
                .get(uri, headers: requestHeaders)
                .timeout(Duration(milliseconds: _config.receiveTimeout));
          });
          break;
        case HttpMethod.post:
          final body = data != null ? jsonEncode(data) : null;
          response = await _executeWithRetry(() async {
            return await _client
                .post(uri, headers: requestHeaders, body: body)
                .timeout(Duration(milliseconds: _config.sendTimeout));
          });
          break;
        case HttpMethod.put:
          final body = data != null ? jsonEncode(data) : null;
          response = await _executeWithRetry(() async {
            return await _client
                .put(uri, headers: requestHeaders, body: body)
                .timeout(Duration(milliseconds: _config.sendTimeout));
          });
          break;
        case HttpMethod.patch:
          final body = data != null ? jsonEncode(data) : null;
          response = await _executeWithRetry(() async {
            return await _client
                .patch(uri, headers: requestHeaders, body: body)
                .timeout(Duration(milliseconds: _config.sendTimeout));
          });
          break;
        case HttpMethod.delete:
          final body = data != null ? jsonEncode(data) : null;
          response = await _executeWithRetry(() async {
            return await _client
                .delete(uri, headers: requestHeaders, body: body)
                .timeout(Duration(milliseconds: _config.sendTimeout));
          });
          break;
      }

      // Handle 401 with token refresh
      if (response.statusCode == 401) {
        final refreshed = await _refreshToken();
        if (refreshed) {
          // Retry request với token mới
          final newHeaders = await _buildHeaders(headers);
          switch (method) {
            case HttpMethod.get:
              response = await _client
                  .get(uri, headers: newHeaders)
                  .timeout(Duration(milliseconds: _config.receiveTimeout));
              break;
            case HttpMethod.post:
              final body = data != null ? jsonEncode(data) : null;
              response = await _client
                  .post(uri, headers: newHeaders, body: body)
                  .timeout(Duration(milliseconds: _config.sendTimeout));
              break;
            case HttpMethod.put:
              final body = data != null ? jsonEncode(data) : null;
              response = await _client
                  .put(uri, headers: newHeaders, body: body)
                  .timeout(Duration(milliseconds: _config.sendTimeout));
              break;
            case HttpMethod.patch:
              final body = data != null ? jsonEncode(data) : null;
              response = await _client
                  .patch(uri, headers: newHeaders, body: body)
                  .timeout(Duration(milliseconds: _config.sendTimeout));
              break;
            case HttpMethod.delete:
              final body = data != null ? jsonEncode(data) : null;
              response = await _client
                  .delete(uri, headers: newHeaders, body: body)
                  .timeout(Duration(milliseconds: _config.sendTimeout));
              break;
          }
        }
      }

      return _handleHttpResponse<T>(response, fromJson);
    } catch (e) {
      return _handleHttpError<T>(e);
    }
  }

  /// Xử lý HTTP response thành công
  ApiResponse<T> _handleHttpResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      final statusCode = response.statusCode;

      // Process Set-Cookie headers to preserve original values
      _processSetCookieHeaders(response);

      if (_config.enableLogging && kDebugMode) {
        print('\n📥 ===== HTTP RESPONSE =====');
        print('📊 Status: $statusCode');
        print('📋 Headers: ${response.headers}');
        print('📦 Body: ${response.body.length > 500 ? '${response.body.substring(0, 500)}...' : response.body}');
        print('⏰ Time: ${DateTime.now().toIso8601String()}');
        print('===========================\n');
      }

      if (statusCode >= 200 && statusCode < 300) {
        T? data;
        dynamic responseData;

        // Parse JSON response
        if (response.body.isNotEmpty) {
          try {
            responseData = jsonDecode(response.body);
          } catch (e) {
            responseData = response.body;
          }
        }

        if (fromJson != null && responseData != null) {
          data = fromJson(responseData);
        } else {
          data = responseData as T?;
        }

        // Xử lý response format chuẩn
        if (responseData is Map<String, dynamic>) {
          return ApiResponse.success(
            data: data,
            message: responseData['message'] as String?,
            statusCode: statusCode,
            meta: responseData['meta'] as Map<String, dynamic>?,
          );
        }

        return ApiResponse.success(data: data, statusCode: statusCode);
      } else {
        // Parse error response
        String errorMessage = 'Request failed with status: $statusCode';
        Map<String, dynamic>? errors;

        if (response.body.isNotEmpty) {
          try {
            final errorData = jsonDecode(response.body);
            if (errorData is Map<String, dynamic>) {
              errorMessage = errorData['message'] as String? ??
                  errorData['error'] as String? ??
                  errorData['detail'] as String? ??
                  errorMessage;
              errors = errorData['errors'] as Map<String, dynamic>? ??
                  errorData['validation_errors'] as Map<String, dynamic>?;
            }
          } catch (e) {
            // Ignore JSON parsing errors for error responses
          }
        }

        return ApiResponse.error(
          message: errorMessage,
          statusCode: statusCode,
          errors: errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Error parsing response: $e',
        statusCode: response.statusCode,
      );
    }
  }

  /// Xử lý HTTP errors
  ApiResponse<T> _handleHttpError<T>(dynamic error) {
    String message;
    int? statusCode;
    Map<String, dynamic>? errors;

    if (error is SocketException) {
      message = 'No internet connection';
      statusCode = 0;
    } else if (error is TimeoutException) {
      message = 'Request timeout';
      statusCode = 408;
    } else if (error is FormatException) {
      message = 'Invalid response format';
      statusCode = 0;
    } else if (error is http.ClientException) {
      message = 'Client error: ${error.message}';
      statusCode = 0;
    } else {
      message = 'Unknown error: $error';
      statusCode = 0;
    }

    if (_config.enableLogging && kDebugMode) {
      print('\n❌ ===== HTTP ERROR =====');
      print('🚨 Error: $message');
      print('📊 Status: $statusCode');
      print('⏰ Time: ${DateTime.now().toIso8601String()}');
      print('========================\n');
    }

    return ApiResponse.error(
      message: message,
      statusCode: statusCode,
      errors: errors,
    );
  }

  // ========== UTILITY METHODS ==========

  /// Process Set-Cookie headers from HTTP response
  void _processSetCookieHeaders(http.Response response) {
    try {
      final setCookieHeaders = response.headers['set-cookie'];
      if (setCookieHeaders != null && setCookieHeaders.isNotEmpty) {
        if (_config.enableLogging && kDebugMode) {
          debugPrint('🍪 Found Set-Cookie header: $setCookieHeaders');
        }

        // Parse and store cookies while preserving original values
        _parseAndStoreCookies(setCookieHeaders);
      }
    } catch (e) {
      if (_config.enableLogging && kDebugMode) {
        debugPrint('🔴 Error processing Set-Cookie headers: $e');
      }
    }
  }

  /// Parse Set-Cookie header and store cookies with preserved values
  void _parseAndStoreCookies(String setCookieHeader) {
    try {
      // Split multiple cookies if they exist
      final cookieStrings = setCookieHeader.split(',');

      for (final cookieString in cookieStrings) {
        final parts = cookieString.trim().split(';');
        if (parts.isNotEmpty) {
          final nameValue = parts[0].trim();
          final equalIndex = nameValue.indexOf('=');

          if (equalIndex > 0) {
            final name = nameValue.substring(0, equalIndex).trim();
            final value = nameValue.substring(equalIndex + 1).trim();

            if (_config.enableLogging && kDebugMode) {
              debugPrint('🍪 Parsed cookie: $name=$value (length: ${value.length})');
            }

            // Store the cookie with original value preserved
            if (name == ApiConstants.refreshTokenCookieName) {
              final uri = Uri.parse(_config.baseUrl);
              final domain = uri.host;

              // Use our custom storage to preserve the original value
              CookieService().storeRawCookieValue(domain, name, value);

              if (_config.enableLogging && kDebugMode) {
                debugPrint('🍪 Stored ${ApiConstants.refreshTokenCookieName} in raw storage: ${value.length} chars');
              }
            }
          }
        }
      }
    } catch (e) {
      if (_config.enableLogging && kDebugMode) {
        debugPrint('🔴 Error parsing Set-Cookie header: $e');
      }
    }
  }

  /// Lưu auth tokens
  Future<void> saveAuthTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    await _secureStorage.write(
      key: SecureStorageKeys.accessToken,
      value: accessToken,
    );

    if (refreshToken != null) {
      await _secureStorage.write(
        key: SecureStorageKeys.refreshToken,
        value: refreshToken,
      );
    }
  }

  /// Xóa auth tokens
  Future<void> clearAuthTokens() async {
    await _secureStorage.delete(key: SecureStorageKeys.accessToken);
    await _secureStorage.delete(key: SecureStorageKeys.refreshToken);
  }

  /// Kiểm tra xem có token hay không
  Future<bool> hasAuthToken() async {
    final token = await _secureStorage.read(key: SecureStorageKeys.accessToken);
    return token != null && token.isNotEmpty;
  }

  /// Lấy current access token
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: SecureStorageKeys.accessToken);
  }

  /// Cập nhật base URL
  void updateBaseUrl(String newBaseUrl) {
    _config = HttpClientConfig(
      baseUrl: newBaseUrl,
      connectTimeout: _config.connectTimeout,
      receiveTimeout: _config.receiveTimeout,
      sendTimeout: _config.sendTimeout,
      defaultHeaders: _config.defaultHeaders,
      enableLogging: _config.enableLogging,
      enableRetry: _config.enableRetry,
      maxRetries: _config.maxRetries,
      retryDelay: _config.retryDelay,
      retryStatusCodes: _config.retryStatusCodes,
    );
  }

  /// Thêm header mặc định
  void addDefaultHeader(String key, String value) {
    _config = HttpClientConfig(
      baseUrl: _config.baseUrl,
      connectTimeout: _config.connectTimeout,
      receiveTimeout: _config.receiveTimeout,
      sendTimeout: _config.sendTimeout,
      defaultHeaders: {..._config.defaultHeaders, key: value},
      enableLogging: _config.enableLogging,
      enableRetry: _config.enableRetry,
      maxRetries: _config.maxRetries,
      retryDelay: _config.retryDelay,
      retryStatusCodes: _config.retryStatusCodes,
    );
  }

  /// Xóa header mặc định
  void removeDefaultHeader(String key) {
    final newHeaders = Map<String, String>.from(_config.defaultHeaders);
    newHeaders.remove(key);
    _config = HttpClientConfig(
      baseUrl: _config.baseUrl,
      connectTimeout: _config.connectTimeout,
      receiveTimeout: _config.receiveTimeout,
      sendTimeout: _config.sendTimeout,
      defaultHeaders: newHeaders,
      enableLogging: _config.enableLogging,
      enableRetry: _config.enableRetry,
      maxRetries: _config.maxRetries,
      retryDelay: _config.retryDelay,
      retryStatusCodes: _config.retryStatusCodes,
    );
  }

  /// Lấy HTTP client instance (để sử dụng advanced features)
  http.Client get httpClient => _client;

  /// Lấy current config
  HttpClientConfig get config => _config;
}

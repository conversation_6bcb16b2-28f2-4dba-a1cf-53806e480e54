# UltraFace Configuration
# Auto-generated mock configuration

models:
  ultraface:
    path: "lib/packages/face_recognition/assets/models/ultraface_320.tflite"
    input_size: [320, 240]
    confidence_threshold: 0.7
    nms_threshold: 0.4
    max_faces: 5
    type: "mock"
    
  mobilefacenet:
    path: "lib/packages/face_recognition/assets/models/mobilefacenet.tflite"
    input_size: [112, 112]
    embedding_size: 128
    type: "mock"
    
  mediapipe:
    path: "lib/packages/face_recognition/assets/models/mediapipe_face.tflite"
    input_size: [128, 128]
    confidence_threshold: 0.5
    type: "mock"

performance:
  target_fps: 45
  memory_limit_mb: 150
  enable_gpu_acceleration: true
  enable_nnapi: false  # Disabled for mock models

mock_mode:
  enabled: true
  simulate_detections: true
  mock_confidence: 0.85
  mock_face_count: 1

created: 2025-07-25T16:43:06.075780
version: "1.0.0-mock"

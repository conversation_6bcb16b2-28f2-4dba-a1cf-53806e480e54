# Plan Chuyển Đổi Components từ @lib\shared\presentation\widgets\common sang @lib\shared\components

## Tổng Quan

Dự án hiện tại đang sử dụng components từ hai thư mục khác nhau:
- **Cũ**: `@lib\shared\presentation\widgets\common` 
- **Mới**: `@lib\shared\components`

Mục tiêu là chuyển đổi tất cả các file đang sử dụng components cũ sang sử dụng components mới để thống nhất codebase.

## Phân Tích Hiện Trạng

### Files Đang Sử Dụng Components Cũ

Dựa trên kết quả tìm kiếm và phân tích chi tiết, các file sau đang import từ `shared/presentation/widgets/common`:

1. **lib/apps/mobile/presentation/screens/dashboard.dart**
   - Import: `c_tabs_bar.dart`
   - Usage: `CTabsBar` component (class name: `TabsBar`)

2. **lib/apps/mobile/presentation/screens/login_screen.dart**
   - Import: `c_text_field.dart`, `enhanced_error_message.dart`
   - Usage: `CTextField` (3 instances), `EnhancedErrorMessage.auth()`

3. **lib/apps/mobile/presentation/screens/tenant_create_screen.dart**
   - Import: `c_text_field.dart`
   - Usage: `CTextField` (2 instances)

4. **lib/apps/mobile/presentation/screens/forgot_password/enter_email_screen.dart**
   - Import: `c_text_field.dart`
   - Usage: `CTextField` (1 instance)

5. **lib/apps/mobile/presentation/screens/notifications_screen.dart**
   - Import: `c_tabs_bar.dart`
   - Usage: `CTabsBar` component (class name: `TabsBar`)

6. **lib/apps/mobile/presentation/screens/profile_screen.dart**
   - Import: `c_tabs_bar.dart`
   - Usage: `CTabsBar` component (class name: `TabsBar`)

7. **lib/apps/mobile/presentation/screens/tools_screen/tools_screen.dart**
   - Import: `c_tabs_bar.dart`
   - Usage: `CTabsBar` component (class name: `TabsBar`)

8. **lib/apps/mobile/routes/mobile_router.dart**
   - Import: `error_screen.dart`
   - Usage: `ErrorScreen` (1 instance)

9. **lib/apps/terminal/routes/terminal_router.dart**
   - Import: `error_screen.dart`, `not_found_screen.dart`
   - Usage: `ErrorScreen` (2 instances), `NotFoundScreen` (1 instance)

### Components Có Sẵn trong @lib\shared\components

Danh sách components hiện có trong thư mục mới:
- `app_button.dart` → Thay thế cho `CButton`
- `app_input_field.dart` → Thay thế cho `CTextField`
- `app_list_controller.dart`
- `app_list_view.dart`
- `app_loading.dart` → Thay thế cho `CLoadingWidget`
- `app_logo.dart` → Thay thế cho `CFacesLogo`
- `app_notification.dart` → Thay thế cho `ErrorMessage`
- `app_search_field.dart`
- `app_toggle_button.dart`
- `flavor_aware_widget.dart`
- `otp_input_field.dart`
- `tabs_bar.dart` → Thay thế cho `CTabsBar`
- `tenant_list_item.dart`
- `tenants_empty_state.dart` → Thay thế cho `CEmptyState`
- `users_filter_popup.dart`

## Mapping Table: Old Components → New Components

| Old Component (widgets/common) | New Component (shared/components) | Status | API Differences | Priority |
|-------------------------------|-----------------------------------|---------|-----------------|----------|
| `CTextField` | `AppInputField` | ✅ Available | ⚠️ **Major differences** - See details below | **HIGH** (6 usages) |
| `TabsBar` (from c_tabs_bar.dart) | `TabsBar` | ✅ Available | ⚠️ **API differences** - See details below | **HIGH** (4 usages) |
| `ErrorScreen` | - | ❌ Missing | Need to create or move | **MEDIUM** (3 usages) |
| `EnhancedErrorMessage` | `AppNotification` | ⚠️ Partial | ⚠️ Enhanced features may be missing | **MEDIUM** (1 usage) |
| `NotFoundScreen` | - | ❌ Missing | Need to create or move | **LOW** (1 usage) |
| `CButton` | `AppButton` | ✅ Available | ✅ API tương tự | **LOW** (0 current usages) |
| `CLoadingWidget` | `AppLoading` | ✅ Available | ✅ API tương tự | **LOW** (0 current usages) |
| `CFacesLogo` | `AppLogo` | ✅ Available | ✅ API tương tự | **LOW** (0 current usages) |
| `ErrorMessage` | `AppNotification` | ✅ Available | ⚠️ Need to check API differences | **LOW** (0 current usages) |
| `CEmptyState` | `TenantsEmptyState` | ⚠️ Partial | ⚠️ Only tenant-specific available | **LOW** (0 current usages) |
| `CCard` | - | ❌ Missing | Need to create or use Material Card | **LOW** (0 current usages) |
| `CConfirmationDialog` | - | ❌ Missing | Need to create | **LOW** (0 current usages) |
| `CDatePicker` | - | ❌ Missing | Need to create | **LOW** (0 current usages) |
| `CLabel` | - | ❌ Missing | Can integrate into AppInputField | **LOW** (0 current usages) |
| `CListScroll` | `AppListView` | ✅ Available | ⚠️ Need to check API differences | **LOW** (0 current usages) |

### API Differences Chi Tiết

#### CTextField → AppInputField
**Major API Changes:**
- **Constructor**: `CTextField` requires `label` and `hintText`, `AppInputField` uses optional `label` and `placeholder`
- **Properties**:
  - `CTextField.hintText` → `AppInputField.placeholder`
  - `CTextField.isRequired` → `AppInputField.isRequired` (same)
  - `CTextField.height` → `AppInputField.height` (same, default 38)
  - `CTextField.validator` → `AppInputField.validator` (same)
- **Password handling**: Both support `isPassword` and `obscureText`
- **Label**: `CTextField` uses separate `CLabel` component, `AppInputField` has built-in label

#### TabsBar (c_tabs_bar.dart) → TabsBar (tabs_bar.dart)
**API Changes:**
- **Constructor**:
  - Old: `TabsBar({required int currentIndex, required ValueChanged<int> onTap})`
  - New: `TabsBar({required int selectedIndex, required Function(int) onTabSelected})`
- **Property names**: `currentIndex` → `selectedIndex`, `onTap` → `onTabSelected`
- **Icons**: Old uses custom `tab_icons.dart`, New uses `AppIcons` from `app_icons.dart`

## Kế Hoạch Thực Hiện

### Phase 1: Phân Tích Chi Tiết (ĐANG THỰC HIỆN)
- [x] Tìm tất cả files sử dụng old components
- [ ] Kiểm tra chi tiết usage trong từng file
- [ ] Phân tích API differences giữa old và new components
- [ ] Xác định components còn thiếu cần tạo mới

### Phase 2: Chuẩn Bị Components
- [ ] Tạo missing components trong shared/components
- [ ] Đảm bảo API compatibility hoặc tạo migration guide
- [ ] Test các new components

### Phase 3: Migration
- [ ] Chuyển đổi import statements
- [ ] Cập nhật component usage theo new API
- [ ] Fix compilation errors
- [ ] Test functionality

### Phase 4: Cleanup & Documentation
- [ ] Remove unused old components (nếu không còn sử dụng)
- [ ] Update documentation
- [ ] Create migration guide

## Ưu Tiên Thực Hiện

### Phase 1: HIGH Priority (Cần thực hiện ngay)
1. **CTextField → AppInputField** (6 usages total)
   - `login_screen.dart` (3 usages)
   - `tenant_create_screen.dart` (2 usages)
   - `enter_email_screen.dart` (1 usage)
   - **Challenge**: Major API differences require careful migration

2. **TabsBar migration** (4 usages total)
   - `dashboard.dart`, `notifications_screen.dart`, `profile_screen.dart`, `tools_screen.dart`
   - **Challenge**: Property name changes (`currentIndex` → `selectedIndex`, `onTap` → `onTabSelected`)

### Phase 2: MEDIUM Priority
3. **ErrorScreen** (3 usages total)
   - `mobile_router.dart` (1 usage)
   - `terminal_router.dart` (2 usages)
   - **Action**: Move component to shared/components or create new

4. **EnhancedErrorMessage → AppNotification** (1 usage)
   - `login_screen.dart`
   - **Challenge**: Check if enhanced features are available in AppNotification

### Phase 3: LOW Priority
5. **NotFoundScreen** (1 usage)
   - `terminal_router.dart`
   - **Action**: Move component to shared/components or create new

## Rủi Ro & Lưu Ý

1. **API Breaking Changes**: New components có thể có API khác với old components
2. **Missing Components**: Một số components chưa có equivalent trong shared/components
3. **Testing**: Cần test kỹ lưỡng sau khi migration để đảm bảo UI/UX không bị ảnh hưởng
4. **Dependencies**: Cần kiểm tra dependencies giữa các components

## Migration Strategy

### Approach 1: Gradual Migration (Recommended)
1. **Start with TabsBar** (easier API changes)
2. **Then CTextField** (more complex but high impact)
3. **Move ErrorScreen and NotFoundScreen** to shared/components
4. **Handle EnhancedErrorMessage** last

### Approach 2: Component-by-Component
1. Create missing components first
2. Migrate all usages of one component at a time
3. Test thoroughly before moving to next component

## Detailed Migration Steps

### Step 1: TabsBar Migration
```dart
// OLD (c_tabs_bar.dart)
TabsBar(
  currentIndex: _selectedIndex,
  onTap: (index) => setState(() => _selectedIndex = index),
)

// NEW (tabs_bar.dart)
TabsBar(
  selectedIndex: _selectedIndex,
  onTabSelected: (index) => setState(() => _selectedIndex = index),
)
```

### Step 2: CTextField Migration
```dart
// OLD (c_text_field.dart)
CTextField(
  controller: _controller,
  label: 'Tên đăng nhập',
  hintText: 'Nhập tên đăng nhập',
  isRequired: true,
  validator: (value) => _validate(value),
)

// NEW (app_input_field.dart)
AppInputField(
  controller: _controller,
  label: 'Tên đăng nhập',
  placeholder: 'Nhập tên đăng nhập',
  isRequired: true,
  validator: (value) => _validate(value),
)
```

## Next Steps

1. ✅ **COMPLETED**: Phân tích chi tiết tất cả files và components
2. ✅ **COMPLETED**: So sánh API giữa old và new components
3. 🔄 **IN PROGRESS**: Tạo mapping table chi tiết
4. ⏳ **NEXT**: Bắt đầu migration với TabsBar (easiest)
5. ⏳ **THEN**: Migration CTextField (most complex)
6. ⏳ **FINALLY**: Move ErrorScreen và NotFoundScreen

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../core/models/face_recognition_config.dart';

/// Online face recognition service that communicates with server
class OnlineRecognitionService {
  final FaceRecognitionConfig _config;
  final http.Client _httpClient;
  
  // Caching and throttling
  final Map<String, RecognitionResult> _cache = {};
  final Map<String, DateTime> _requestTimestamps = {};
  Timer? _cacheCleanupTimer;
  
  // Request tracking
  int _totalRequests = 0;
  int _successfulRequests = 0;
  int _failedRequests = 0;
  int _cachedRequests = 0;
  
  bool _isInitialized = false;
  
  OnlineRecognitionService({
    required FaceRecognitionConfig config,
    http.Client? httpClient,
  }) : _config = config,
       _httpClient = httpClient ?? http.Client();
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Start cache cleanup timer
      _cacheCleanupTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _cleanupCache(),
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('🌐 OnlineRecognitionService initialized');
        print('   Endpoint: ${_config.onlineEndpoint}');
        print('   Timeout: ${_config.recognitionTimeout.inSeconds}s');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize OnlineRecognitionService: $e');
      }
      rethrow;
    }
  }
  
  /// Recognize face using online service
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes) async {
    if (!_isInitialized) {
      throw StateError('OnlineRecognitionService not initialized');
    }
    
    if (_config.onlineEndpoint == null) {
      throw StateError('Online endpoint not configured');
    }
    
    _totalRequests++;
    
    try {
      // Check cache first
      final imageHash = _calculateImageHash(faceImageBytes);
      final cachedResult = _getCachedResult(imageHash);
      if (cachedResult != null) {
        _cachedRequests++;
        return cachedResult;
      }
      
      // Check throttling
      if (_isThrottled()) {
        if (kDebugMode) {
          print('⏸️ Request throttled');
        }
        return null;
      }
      
      // Make recognition request
      final result = await _makeRecognitionRequest(faceImageBytes);
      
      // Cache successful result
      if (result != null) {
        _cacheResult(imageHash, result);
        _successfulRequests++;
      } else {
        _failedRequests++;
      }
      
      return result;
      
    } catch (e) {
      _failedRequests++;
      if (kDebugMode) {
        print('❌ Online recognition failed: $e');
      }
      return null;
    }
  }
  
  /// Make HTTP request to recognition endpoint
  Future<RecognitionResult?> _makeRecognitionRequest(Uint8List faceImageBytes) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // Prepare request body
      final requestBody = {
        'image': base64Encode(faceImageBytes),
        'timestamp': DateTime.now().toIso8601String(),
        'device_id': _getDeviceId(),
        'confidence_threshold': _config.minConfidence,
      };
      
      // Prepare headers
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
      
      if (_config.apiKey != null) {
        headers['Authorization'] = 'Bearer ${_config.apiKey}';
      }
      
      // Make request with retry logic
      http.Response? response;
      int retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount < maxRetries) {
        try {
          response = await _httpClient
              .post(
                Uri.parse(_config.onlineEndpoint!),
                headers: headers,
                body: jsonEncode(requestBody),
              )
              .timeout(_config.recognitionTimeout);
          
          break; // Success, exit retry loop
          
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            rethrow;
          }
          
          // Exponential backoff
          await Future.delayed(Duration(milliseconds: 100 * math.pow(2, retryCount).toInt()));
        }
      }
      
      if (response == null) {
        throw Exception('Failed to get response after $maxRetries retries');
      }
      
      stopwatch.stop();
      
      // Parse response
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        
        if (kDebugMode) {
          print('✅ Online recognition completed in ${stopwatch.elapsedMilliseconds}ms');
        }
        
        return _parseRecognitionResponse(responseData);
      } else {
        if (kDebugMode) {
          print('❌ Recognition request failed: ${response.statusCode} ${response.body}');
        }
        return null;
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Recognition request error: $e');
      }
      rethrow;
    }
  }
  
  /// Parse recognition response from server
  RecognitionResult? _parseRecognitionResponse(Map<String, dynamic> responseData) {
    try {
      if (responseData['success'] == true && responseData['user'] != null) {
        final userData = responseData['user'] as Map<String, dynamic>;
        
        return RecognitionResult(
          userId: userData['id'] as String,
          userName: userData['name'] as String,
          confidence: (responseData['confidence'] as num).toDouble(),
          source: 'online',
          accessLevel: _parseAccessLevel(userData['access_level'] as String?),
          hasAccess: userData['has_access'] as bool? ?? true,
          timestamp: DateTime.now(),
          metadata: {
            'project_id': userData['project_id'],
            'department': userData['department'],
            'response_time': responseData['response_time'],
          },
        );
      } else {
        // No match found
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to parse recognition response: $e');
      }
      return null;
    }
  }
  
  /// Parse access level from string
  AccessLevel _parseAccessLevel(String? accessLevel) {
    switch (accessLevel?.toLowerCase()) {
      case 'admin':
        return AccessLevel.admin;
      case 'user':
        return AccessLevel.user;
      case 'guest':
        return AccessLevel.guest;
      case 'denied':
        return AccessLevel.denied;
      default:
        return AccessLevel.user;
    }
  }
  
  /// Calculate simple hash for image caching
  String _calculateImageHash(Uint8List imageBytes) {
    // Simple hash based on image size and first/last bytes
    final size = imageBytes.length;
    final firstBytes = imageBytes.take(16).toList();
    final lastBytes = imageBytes.skip(size - 16).toList();
    
    return '$size-${firstBytes.join('')}-${lastBytes.join('')}';
  }
  
  /// Get cached recognition result
  RecognitionResult? _getCachedResult(String imageHash) {
    final result = _cache[imageHash];
    if (result != null) {
      // Check if cache entry is still valid (5 minutes)
      final age = DateTime.now().difference(result.timestamp);
      if (age.inMinutes < 5) {
        return result;
      } else {
        _cache.remove(imageHash);
      }
    }
    return null;
  }
  
  /// Cache recognition result
  void _cacheResult(String imageHash, RecognitionResult result) {
    _cache[imageHash] = result;
    
    // Limit cache size
    if (_cache.length > 100) {
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
    }
  }
  
  /// Check if requests are being throttled
  bool _isThrottled() {
    final now = DateTime.now();
    final deviceId = _getDeviceId();
    final lastRequest = _requestTimestamps[deviceId];
    
    if (lastRequest != null) {
      final timeSinceLastRequest = now.difference(lastRequest);
      if (timeSinceLastRequest < _config.recognitionThrottle) {
        return true;
      }
    }
    
    _requestTimestamps[deviceId] = now;
    return false;
  }
  
  /// Get device identifier
  String _getDeviceId() {
    // This would typically come from device info
    return 'device_${_config.platformType.name}';
  }
  
  /// Clean up old cache entries and timestamps
  void _cleanupCache() {
    final now = DateTime.now();
    
    // Clean up cache (remove entries older than 10 minutes)
    _cache.removeWhere((key, result) {
      return now.difference(result.timestamp).inMinutes > 10;
    });
    
    // Clean up request timestamps (remove entries older than 1 hour)
    _requestTimestamps.removeWhere((key, timestamp) {
      return now.difference(timestamp).inHours > 1;
    });
    
    if (kDebugMode) {
      print('🧹 Cache cleanup completed. Cache size: ${_cache.length}');
    }
  }
  
  /// Get service statistics
  OnlineRecognitionStats getStats() {
    return OnlineRecognitionStats(
      totalRequests: _totalRequests,
      successfulRequests: _successfulRequests,
      failedRequests: _failedRequests,
      cachedRequests: _cachedRequests,
      cacheSize: _cache.length,
      successRate: _totalRequests > 0 ? _successfulRequests / _totalRequests : 0.0,
    );
  }
  
  /// Dispose of resources
  void dispose() {
    _cacheCleanupTimer?.cancel();
    _cache.clear();
    _requestTimestamps.clear();
    _httpClient.close();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ OnlineRecognitionService disposed');
    }
  }
}

/// Recognition result from online service
class RecognitionResult {
  final String userId;
  final String userName;
  final double confidence;
  final String source;
  final AccessLevel accessLevel;
  final bool hasAccess;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  
  const RecognitionResult({
    required this.userId,
    required this.userName,
    required this.confidence,
    required this.source,
    required this.accessLevel,
    required this.hasAccess,
    required this.timestamp,
    this.metadata = const {},
  });
  
  @override
  String toString() {
    return 'RecognitionResult(user: $userName, confidence: ${(confidence * 100).toStringAsFixed(1)}%, source: $source)';
  }
}

/// Access levels for recognition results
enum AccessLevel {
  admin,
  user,
  guest,
  denied,
}

/// Statistics for online recognition service
class OnlineRecognitionStats {
  final int totalRequests;
  final int successfulRequests;
  final int failedRequests;
  final int cachedRequests;
  final int cacheSize;
  final double successRate;
  
  const OnlineRecognitionStats({
    required this.totalRequests,
    required this.successfulRequests,
    required this.failedRequests,
    required this.cachedRequests,
    required this.cacheSize,
    required this.successRate,
  });
  
  @override
  String toString() {
    return 'OnlineRecognitionStats('
        'total: $totalRequests, '
        'success: $successfulRequests, '
        'failed: $failedRequests, '
        'cached: $cachedRequests, '
        'success_rate: ${(successRate * 100).toStringAsFixed(1)}%'
        ')';
  }
}

// Import math for exponential backoff
import 'dart:math' as math;

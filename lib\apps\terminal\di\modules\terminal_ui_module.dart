import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Terminal UI Module
/// 
/// This module registers terminal-specific UI dependencies
/// including terminal theme providers, fullscreen UI services,
/// terminal-specific UI components, and large screen layouts.
/// 
/// Note: This module will be fully implemented when terminal
/// presentation layer is developed.
void registerTerminalUIDependencies() {
  // TODO: Implement when terminal UI components are developed
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<TerminalThemeProvider>(
  //   () => TerminalThemeProvider(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalFullscreenService>(
  //   () => TerminalFullscreenServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalLayoutService>(
  //   () => TerminalLayoutServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<TerminalTimeoutService>(
  //   () => TerminalTimeoutServiceImpl(),
  // );
}

/// Unregister terminal UI dependencies (for testing)
void unregisterTerminalUIDependencies() {
  // TODO: Implement when UI dependencies are added
  // if (getIt.isRegistered<TerminalThemeProvider>()) {
  //   getIt.unregister<TerminalThemeProvider>();
  // }
  // if (getIt.isRegistered<TerminalFullscreenService>()) {
  //   getIt.unregister<TerminalFullscreenService>();
  // }
  // if (getIt.isRegistered<TerminalLayoutService>()) {
  //   getIt.unregister<TerminalLayoutService>();
  // }
  // if (getIt.isRegistered<TerminalTimeoutService>()) {
  //   getIt.unregister<TerminalTimeoutService>();
  // }
}

/// Reset terminal UI module (clear và re-register)
void resetTerminalUIModule() {
  unregisterTerminalUIDependencies();
  registerTerminalUIDependencies();
}

/// Check if terminal UI dependencies are registered
bool areTerminalUIDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<TerminalThemeProvider>() &&
  //        getIt.isRegistered<TerminalFullscreenService>() &&
  //        getIt.isRegistered<TerminalLayoutService>() &&
  //        getIt.isRegistered<TerminalTimeoutService>();
  return true; // Placeholder
}

/// Get terminal UI dependencies for debugging
Map<String, bool> getTerminalUIDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'TerminalThemeProvider': getIt.isRegistered<TerminalThemeProvider>(),
    // 'TerminalFullscreenService': getIt.isRegistered<TerminalFullscreenService>(),
    // 'TerminalLayoutService': getIt.isRegistered<TerminalLayoutService>(),
    // 'TerminalTimeoutService': getIt.isRegistered<TerminalTimeoutService>(),
  };
}

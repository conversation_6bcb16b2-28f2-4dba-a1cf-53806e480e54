import 'package:equatable/equatable.dart';

/// User role domain entity
/// 
/// Represents a user's role with permissions and hierarchy
class UserRole extends Equatable {
  final String id;
  final String name;
  final String? description;
  final int level;
  final List<String> permissions;
  final bool isActive;
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;

  const UserRole({
    required this.id,
    required this.name,
    this.description,
    required this.level,
    this.permissions = const [],
    this.isActive = true,
    this.isDefault = false,
    this.createdAt,
    this.updatedAt,
    this.metadata = const {},
  });

  /// Copy with new values
  UserRole copyWith({
    String? id,
    String? name,
    String? description,
    int? level,
    List<String>? permissions,
    bool? isActive,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return UserRole(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      level: level ?? this.level,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if role has specific permission
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// Check if role has any of the specified permissions
  bool hasAnyPermission(List<String> requiredPermissions) {
    return requiredPermissions.any((permission) => permissions.contains(permission));
  }

  /// Check if role has all of the specified permissions
  bool hasAllPermissions(List<String> requiredPermissions) {
    return requiredPermissions.every((permission) => permissions.contains(permission));
  }

  /// Check if this role is admin
  bool get isAdmin => name.toLowerCase() == 'admin' || hasPermission('admin.*');

  /// Check if this role is moderator
  bool get isModerator => name.toLowerCase() == 'moderator' || hasPermission('moderate.*');

  /// Check if this role is user
  bool get isUser => name.toLowerCase() == 'user' || (!isAdmin && !isModerator);

  /// Check if this role can manage other role
  bool canManage(UserRole otherRole) {
    // Can't manage same or higher level roles
    return level > otherRole.level;
  }

  /// Get role type
  RoleType get type {
    if (isAdmin) return RoleType.admin;
    if (isModerator) return RoleType.moderator;
    return RoleType.user;
  }

  /// Get role category based on permissions
  RoleCategory get category {
    if (hasPermission('system.*')) return RoleCategory.system;
    if (hasPermission('admin.*')) return RoleCategory.administrative;
    if (hasPermission('moderate.*')) return RoleCategory.moderation;
    if (hasPermission('write.*')) return RoleCategory.operational;
    return RoleCategory.readonly;
  }

  /// Get permission groups
  List<String> get permissionGroups {
    final groups = <String>{};
    for (final permission in permissions) {
      final parts = permission.split('.');
      if (parts.isNotEmpty) {
        groups.add(parts[0]);
      }
    }
    return groups.toList()..sort();
  }

  /// Check if role has permissions in specific group
  bool hasPermissionsInGroup(String group) {
    return permissions.any((permission) => permission.startsWith('$group.'));
  }

  /// Get permissions for specific group
  List<String> getPermissionsForGroup(String group) {
    return permissions
        .where((permission) => permission.startsWith('$group.'))
        .toList();
  }

  /// Get metadata value
  T? getMetadata<T>(String key) {
    return metadata[key] as T?;
  }

  /// Validate role
  RoleValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Required fields
    if (id.isEmpty) errors.add('Role ID is required');
    if (name.isEmpty) errors.add('Role name is required');

    // Name validation
    if (name.length < 2) {
      errors.add('Role name must be at least 2 characters');
    }
    if (name.length > 50) {
      errors.add('Role name cannot exceed 50 characters');
    }

    // Level validation
    if (level < 0) {
      errors.add('Role level cannot be negative');
    }
    if (level > 100) {
      warnings.add('Role level is unusually high');
    }

    // Permission validation
    if (permissions.isEmpty) {
      warnings.add('Role has no permissions assigned');
    }

    // Check for conflicting permissions
    if (hasPermission('admin.*') && !isAdmin) {
      warnings.add('Role has admin permissions but is not marked as admin');
    }

    return RoleValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    level,
    permissions,
    isActive,
    isDefault,
    createdAt,
    updatedAt,
    metadata,
  ];

  @override
  String toString() {
    return 'UserRole(id: $id, name: $name, level: $level, permissions: ${permissions.length}, isActive: $isActive)';
  }
}

/// Role type enumeration
enum RoleType {
  admin,
  moderator,
  user,
}

/// Role category enumeration
enum RoleCategory {
  system,
  administrative,
  moderation,
  operational,
  readonly,
}

/// Role validation result
class RoleValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const RoleValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}

/// Extension for RoleType
extension RoleTypeExtension on RoleType {
  String get displayName {
    switch (this) {
      case RoleType.admin:
        return 'Administrator';
      case RoleType.moderator:
        return 'Moderator';
      case RoleType.user:
        return 'User';
    }
  }

  int get defaultLevel {
    switch (this) {
      case RoleType.admin:
        return 100;
      case RoleType.moderator:
        return 50;
      case RoleType.user:
        return 10;
    }
  }
}

/// Extension for RoleCategory
extension RoleCategoryExtension on RoleCategory {
  String get displayName {
    switch (this) {
      case RoleCategory.system:
        return 'System';
      case RoleCategory.administrative:
        return 'Administrative';
      case RoleCategory.moderation:
        return 'Moderation';
      case RoleCategory.operational:
        return 'Operational';
      case RoleCategory.readonly:
        return 'Read Only';
    }
  }

  String get description {
    switch (this) {
      case RoleCategory.system:
        return 'System-level access with full control';
      case RoleCategory.administrative:
        return 'Administrative access for management tasks';
      case RoleCategory.moderation:
        return 'Moderation access for content management';
      case RoleCategory.operational:
        return 'Operational access for daily tasks';
      case RoleCategory.readonly:
        return 'Read-only access for viewing information';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/device_command_handler.dart';

class ManualCommandWidget extends StatefulWidget {
  const ManualCommandWidget({super.key});

  @override
  State<ManualCommandWidget> createState() => _ManualCommandWidgetState();
}

class _ManualCommandWidgetState extends State<ManualCommandWidget> {
  String _selectedCommandType = 'relay_control';
  final Map<String, TextEditingController> _controllers = {};
  bool _isExecuting = false;

  final Map<String, List<String>> _commandParameters = {
    'relay_control': ['action', 'relay_id'],
    'face_auth': ['face_image', 'user_id'],
    'status_request': ['component'],
    'heartbeat': [],
    'config_update': ['config'],
  };

  final Map<String, Map<String, String>> _parameterHints = {
    'relay_control': {
      'action': 'unlock, lock, on, off, status',
      'relay_id': 'main_door, side_door, etc.',
    },
    'face_auth': {
      'face_image': 'Base64 encoded image data',
      'user_id': 'Optional user identifier',
    },
    'status_request': {
      'component': 'relay, system, all',
    },
    'config_update': {
      'config': 'JSON configuration object',
    },
  };

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    for (final commandType in _commandParameters.keys) {
      for (final param in _commandParameters[commandType]!) {
        _controllers['${commandType}_$param'] = TextEditingController();
      }
    }

    // Set default values
    _controllers['relay_control_action']?.text = 'unlock';
    _controllers['relay_control_relay_id']?.text = 'main_door';
    _controllers['status_request_component']?.text = 'system';
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildCommandTypeSelector(),
            const SizedBox(height: 16),
            _buildParameterInputs(),
            const SizedBox(height: 16),
            _buildQuickActions(),
            const SizedBox(height: 16),
            _buildExecuteButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.terminal,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 8),
        Text(
          'Manual Command Testing',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Icon(
          Icons.bug_report,
          color: Colors.orange.shade600,
          size: 20,
        ),
        const SizedBox(width: 4),
        Text(
          'Debug Mode',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.orange.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildCommandTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Command Type',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedCommandType,
              isExpanded: true,
              items: _commandParameters.keys.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_formatCommandTypeName(type)),
                );
              }).toList(),
              onChanged: _isExecuting ? null : (value) {
                if (value != null) {
                  setState(() {
                    _selectedCommandType = value;
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildParameterInputs() {
    final parameters = _commandParameters[_selectedCommandType] ?? [];
    
    if (parameters.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info,
              color: Colors.blue.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'This command type requires no additional parameters.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.blue.shade700,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Parameters',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...parameters.map((param) => _buildParameterInput(param)),
      ],
    );
  }

  Widget _buildParameterInput(String parameter) {
    final controllerKey = '${_selectedCommandType}_$parameter';
    final controller = _controllers[controllerKey];
    final hint = _parameterHints[_selectedCommandType]?[parameter];

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: _formatParameterName(parameter),
          hintText: hint,
          border: const OutlineInputBorder(),
          isDense: true,
        ),
        enabled: !_isExecuting,
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Consumer<DeviceCommandHandler>(
          builder: (context, commandHandler, child) {
            return Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickActionButton(
                  'Unlock Door',
                  Icons.lock_open,
                  Colors.green,
                  () => _executeQuickAction(commandHandler.unlockDoor),
                ),
                _buildQuickActionButton(
                  'Lock Door',
                  Icons.lock,
                  Colors.red,
                  () => _executeQuickAction(commandHandler.lockDoor),
                ),
                _buildQuickActionButton(
                  'Get Status',
                  Icons.info,
                  Colors.blue,
                  () => _executeQuickAction(commandHandler.getRelayStatus),
                ),
                _buildQuickActionButton(
                  'Heartbeat',
                  Icons.favorite,
                  Colors.pink,
                  () => _executeQuickAction(commandHandler.sendHeartbeat),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: _isExecuting ? null : onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildExecuteButton() {
    return Consumer<DeviceCommandHandler>(
      builder: (context, commandHandler, child) {
        return SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton.icon(
            onPressed: _isExecuting || !commandHandler.isInitialized
                ? null
                : () => _executeManualCommand(commandHandler),
            icon: _isExecuting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(
              _isExecuting ? 'Executing...' : 'Execute Command',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              textStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _executeManualCommand(DeviceCommandHandler commandHandler) async {
    setState(() {
      _isExecuting = true;
    });

    try {
      final payload = <String, dynamic>{};
      final parameters = _commandParameters[_selectedCommandType] ?? [];

      for (final param in parameters) {
        final controllerKey = '${_selectedCommandType}_$param';
        final controller = _controllers[controllerKey];
        final value = controller?.text.trim();
        
        if (value != null && value.isNotEmpty) {
          payload[param] = value;
        }
      }

      final result = await commandHandler.executeCommand(
        commandType: _selectedCommandType,
        payload: payload,
        source: 'manual_testing',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Command execution failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExecuting = false;
        });
      }
    }
  }

  Future<void> _executeQuickAction(Future<CommandResult> Function() action) async {
    setState(() {
      _isExecuting = true;
    });

    try {
      final result = await action();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quick action failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExecuting = false;
        });
      }
    }
  }

  String _formatCommandTypeName(String type) {
    return type.split('_').map((word) {
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  String _formatParameterName(String parameter) {
    return parameter.split('_').map((word) {
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }
}

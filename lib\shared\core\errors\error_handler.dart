import 'package:flutter/foundation.dart';
import 'failures.dart';
import 'exceptions.dart';

/// Abstract interface for global error handling
abstract class ErrorHandler {
  /// Handle and log error
  void handleError(dynamic error, [StackTrace? stackTrace]);
  
  /// Convert error to user-friendly message
  String getErrorMessage(dynamic error);
  
  /// Convert error to Failure object
  Failure convertToFailure(dynamic error);
  
  /// Check if error should be reported to crash analytics
  bool shouldReportError(dynamic error);
  
  /// Report error to analytics/crash reporting service
  Future<void> reportError(dynamic error, [StackTrace? stackTrace]);
}

/// Implementation of ErrorHandler
class ErrorHandlerImpl implements ErrorHandler {
  @override
  void handleError(dynamic error, [StackTrace? stackTrace]) {
    // Log error in debug mode
    if (kDebugMode) {
      print('Error: $error');
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }

    // Report error if needed
    if (shouldReportError(error)) {
      reportError(error, stackTrace);
    }
  }

  @override
  String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    } else if (error is Failure) {
      return error.message;
    } else if (error is Exception) {
      return _getExceptionMessage(error);
    } else {
      return 'An unexpected error occurred';
    }
  }

  @override
  Failure convertToFailure(dynamic error) {
    if (error is Failure) {
      return error;
    } else if (error is ServerException) {
      return ServerFailure(
        error.message,
        code: error.code,
        statusCode: error.statusCode,
      );
    } else if (error is NetworkException) {
      return NetworkFailure(error.message, code: error.code);
    } else if (error is AuthException) {
      return AuthFailure(error.message, code: error.code);
    } else if (error is ValidationException) {
      return ValidationFailure(
        error.message,
        code: error.code,
        fieldErrors: error.fieldErrors,
      );
    } else if (error is CacheException) {
      return CacheFailure(error.message, code: error.code);
    } else if (error is AppException) {
      return ServerFailure(error.message, code: error.code);
    } else {
      return ServerFailure('Unexpected error: ${error.toString()}');
    }
  }

  @override
  bool shouldReportError(dynamic error) {
    // Don't report validation errors or auth errors in production
    if (error is ValidationException || error is AuthException) {
      return false;
    }
    
    // Don't report network errors (they're usually temporary)
    if (error is NetworkException) {
      return false;
    }
    
    // Report server errors and unexpected errors
    return true;
  }

  @override
  Future<void> reportError(dynamic error, [StackTrace? stackTrace]) async {
    // Crash reporting service integration
    // Note: Add Firebase Crashlytics, Sentry, etc. when needed

    if (kDebugMode) {
      print('Reporting error: $error');
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// Get user-friendly message for common exceptions
  String _getExceptionMessage(Exception exception) {
    final exceptionString = exception.toString().toLowerCase();
    
    if (exceptionString.contains('socket') || 
        exceptionString.contains('network') ||
        exceptionString.contains('connection')) {
      return 'Network connection error. Please check your internet connection.';
    } else if (exceptionString.contains('timeout')) {
      return 'Request timeout. Please try again.';
    } else if (exceptionString.contains('format') ||
               exceptionString.contains('parse')) {
      return 'Data format error. Please try again.';
    } else if (exceptionString.contains('permission')) {
      return 'Permission denied. Please check your permissions.';
    } else if (exceptionString.contains('file') ||
               exceptionString.contains('storage')) {
      return 'File operation error. Please try again.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}

/// Global error handler instance
final ErrorHandler globalErrorHandler = ErrorHandlerImpl();

/// Utility functions for error handling
class ErrorUtils {
  /// Check if error is network related
  static bool isNetworkError(dynamic error) {
    return error is NetworkException ||
           (error is Exception && 
            error.toString().toLowerCase().contains('network'));
  }

  /// Check if error is authentication related
  static bool isAuthError(dynamic error) {
    return error is AuthException ||
           error is AuthFailure ||
           (error is ServerException && error.statusCode == 401) ||
           (error is ServerFailure && error.statusCode == 401);
  }

  /// Check if error is validation related
  static bool isValidationError(dynamic error) {
    return error is ValidationException ||
           error is ValidationFailure ||
           (error is ServerException && error.statusCode == 422) ||
           (error is ServerFailure && error.statusCode == 422);
  }

  /// Check if error is server related
  static bool isServerError(dynamic error) {
    return error is ServerException ||
           error is ServerFailure ||
           (error is Exception && 
            error.toString().toLowerCase().contains('server'));
  }

  /// Get error type as string
  static String getErrorType(dynamic error) {
    if (isNetworkError(error)) return 'Network';
    if (isAuthError(error)) return 'Authentication';
    if (isValidationError(error)) return 'Validation';
    if (isServerError(error)) return 'Server';
    return 'Unknown';
  }

  /// Get error severity level
  static ErrorSeverity getErrorSeverity(dynamic error) {
    if (isValidationError(error)) return ErrorSeverity.low;
    if (isNetworkError(error)) return ErrorSeverity.medium;
    if (isAuthError(error)) return ErrorSeverity.medium;
    if (isServerError(error)) return ErrorSeverity.high;
    return ErrorSeverity.high;
  }
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Mixin for classes that need error handling functionality
mixin ErrorHandlingMixin {
  ErrorHandler get errorHandler;

  /// Handle error with logging and user notification
  void handleError(dynamic error, [StackTrace? stackTrace]) {
    errorHandler.handleError(error, stackTrace);
  }

  /// Get user-friendly error message
  String getErrorMessage(dynamic error) {
    return errorHandler.getErrorMessage(error);
  }

  /// Convert error to failure
  Failure convertToFailure(dynamic error) {
    return errorHandler.convertToFailure(error);
  }

  /// Handle error and return failure
  Failure handleErrorAndReturnFailure(dynamic error, [StackTrace? stackTrace]) {
    handleError(error, stackTrace);
    return convertToFailure(error);
  }
}

console.log('🚀 Starting Relay Controller Test Server...');

const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const mqtt = require('mqtt');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');
const faker = require('faker');
const SecurityManager = require('./security');
const DeviceNamingUtils = require('./device_naming_utils');

console.log('📦 All modules loaded successfully');

// Create face images directory if it doesn't exist
const faceImagesDir = path.join(__dirname, 'public', 'face_images');
if (!fs.existsSync(faceImagesDir)) {
  fs.mkdirSync(faceImagesDir, { recursive: true });
  console.log('📁 Created face_images directory');
}

// Function to save face image to file system
function saveFaceImage(imageData, recognitionId, result) {
  try {
    // Create filename with ID and result
    const status = result.recognized ? 'recognized' : 'rejected';
    const userName = result.user ? result.user.name.replace(/[^a-zA-Z0-9]/g, '_') : 'unknown';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${recognitionId.substring(0, 8)}_${status}_${userName}_${timestamp}.jpg`;
    const filepath = path.join(faceImagesDir, filename);

    // Convert base64 to buffer and save
    const imageBuffer = Buffer.from(imageData, 'base64');
    fs.writeFileSync(filepath, imageBuffer);

    console.log(`💾 Saved face image: ${filename}`);

    // Return relative path for web access
    return `/face_images/${filename}`;
  } catch (error) {
    console.error('❌ Error saving face image:', error);
    return null;
  }
}

// Face recognition settings
const faceRecognitionSettings = {
  autoRecognize: true, // true = auto random, false = manual approval
  pendingRecognitions: new Map(), // Store pending manual recognitions
};

// Face recognition helper functions
function generateRandomUser() {
  return {
    id: faker.datatype.uuid(),
    name: faker.name.findName(),
    email: faker.internet.email(),
    phone: faker.phone.phoneNumber(),
    department: faker.commerce.department(),
    position: faker.name.jobTitle(),
    avatar: faker.image.avatar(),
    employeeId: faker.datatype.number({ min: 1000, max: 9999 }).toString(),
    accessLevel: faker.random.arrayElement(['ADMIN', 'USER', 'GUEST']),
    lastSeen: new Date().toISOString(),
  };
}

function processRecognitionRequest(imageData, deviceId, imageDataUrl = null) {
  const recognitionId = faker.datatype.uuid();

  if (faceRecognitionSettings.autoRecognize) {
    // Auto recognize with random user
    const user = generateRandomUser();
    const confidence = faker.datatype.float({ min: 0.6, max: 0.98, precision: 0.01 });

    // Determine access based on user access level and random factors
    const allowAccess = determineAccessPermission(user, confidence);
    const reason = generateAccessReason(user, allowAccess, confidence);

    return {
      success: true,
      recognized: true,
      user: user,
      confidence: confidence,
      allowAccess: allowAccess,
      reason: reason,
      recognitionId: recognitionId,
      timestamp: new Date().toISOString(),
      processingTime: faker.datatype.number({ min: 100, max: 2000 }),
      imageDataUrl: imageDataUrl // Add image data for web UI
    };
  } else {
    // Manual approval mode - store pending recognition
    const pendingData = {
      recognitionId: recognitionId,
      imageData: imageData,
      imageDataUrl: imageDataUrl,
      deviceId: deviceId,
      timestamp: new Date().toISOString(),
      status: 'pending',
    };

    faceRecognitionSettings.pendingRecognitions.set(recognitionId, pendingData);

    return {
      success: true,
      recognized: false,
      allowAccess: false,
      reason: 'Pending manual approval',
      recognitionId: recognitionId,
      status: 'pending_approval',
      message: 'Recognition request submitted for manual approval',
      timestamp: new Date().toISOString(),
    };
  }
}

// Helper function to determine access permission
function determineAccessPermission(user, confidence) {
  // High confidence users with ADMIN/USER access level get access
  if (confidence > 0.8 && (user.accessLevel === 'ADMIN' || user.accessLevel === 'USER')) {
    return true;
  }

  // Medium confidence ADMIN users still get access
  if (confidence > 0.7 && user.accessLevel === 'ADMIN') {
    return true;
  }

  // Random denial for testing (10% chance)
  if (Math.random() < 0.1) {
    return false;
  }

  // GUEST users have limited access
  if (user.accessLevel === 'GUEST') {
    return Math.random() > 0.3; // 70% chance for guests
  }

  return true; // Default allow
}

// Helper function to generate access reason
function generateAccessReason(user, allowAccess, confidence) {
  if (!allowAccess) {
    const reasons = [
      'Access denied: Insufficient confidence level',
      'Access denied: Guest access restricted',
      'Access denied: Security policy violation',
      'Access denied: Time-based restriction',
      'Access denied: User account suspended'
    ];
    return faker.random.arrayElement(reasons);
  }

  const reasons = [
    'Access granted: Valid user credentials',
    'Access granted: High confidence match',
    'Access granted: Admin privileges',
    'Access granted: User verification successful',
    'Access granted: Biometric authentication passed'
  ];
  return faker.random.arrayElement(reasons);
}

const app = express();
const server = http.createServer(app);

// Create WebSocket server instead of Socket.IO
const wss = new WebSocket.Server({
  server,
  path: '/' // Accept connections on root path
});

// Store WebSocket clients
const wsClients = new Map();

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' })); // Increase limit for face images
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));
// Serve face images
app.use('/face_images', express.static(path.join(__dirname, 'public', 'face_images')));

// In-memory storage for devices and their states
const devices = new Map();
const deviceLogs = new Map();

// Security manager
const security = new SecurityManager();

// Broadcast message to all connected WebSocket clients
function broadcastToAllClients(message) {
  const messageStr = JSON.stringify({
    timestamp: Date.now(),
    ...message
  });

  wsClients.forEach((client) => {
    if (client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(messageStr);
    }
  });
}

// MQTT Client (optional - for testing MQTT functionality)
let mqttClient = null;
try {
  mqttClient = mqtt.connect('mqtt://localhost:1883');
  mqttClient.on('connect', () => {
    console.log('Connected to MQTT broker');
    mqttClient.subscribe('relay/+/control');
  });
  
  mqttClient.on('message', (topic, message) => {
    const parts = topic.split('/');
    if (parts.length === 3 && parts[0] === 'relay' && parts[2] === 'control') {
      const deviceId = parts[1];
      const command = message.toString();
      handleRelayCommand(deviceId, command, 'MQTT');
    }
  });
} catch (error) {
  console.log('MQTT broker not available, continuing without MQTT support');
}

// Helper function to log device actions
function logDeviceAction(deviceId, action, method, details = {}) {
  if (!deviceLogs.has(deviceId)) {
    deviceLogs.set(deviceId, []);
  }
  
  const log = {
    timestamp: new Date().toISOString(),
    action,
    method,
    details
  };
  
  deviceLogs.get(deviceId).push(log);
  
  // Keep only last 100 logs per device
  if (deviceLogs.get(deviceId).length > 100) {
    deviceLogs.get(deviceId).shift();
  }
  
  // Broadcast to all connected clients
  broadcastToAllClients({
    type: 'deviceLog',
    data: { deviceId, log }
  });
}

// Helper function to handle relay commands
function handleRelayCommand(deviceId, command, method) {
  const normalizedCommand = command.toUpperCase().trim();
  let newState = null;
  
  if (normalizedCommand === 'ON' || normalizedCommand === 'TRUE' || normalizedCommand === '1') {
    newState = true;
  } else if (normalizedCommand === 'OFF' || normalizedCommand === 'FALSE' || normalizedCommand === '0') {
    newState = false;
  }
  
  if (newState !== null) {
    // Update device state with naming system support
    if (!devices.has(deviceId)) {
      // Auto-register device with enhanced metadata
      let deviceTypeCategory = 'relay';
      let linkedTerminalId = null;

      if (DeviceNamingUtils.isTerminal(deviceId)) {
        deviceTypeCategory = 'terminal';
        linkedTerminalId = deviceId;
      } else if (DeviceNamingUtils.isRelay(deviceId)) {
        deviceTypeCategory = 'relay';
        linkedTerminalId = DeviceNamingUtils.extractTerminalShortId(deviceId);
      }

      const displayName = DeviceNamingUtils.getDisplayName(deviceId);
      devices.set(deviceId, {
        id: deviceId,
        name: displayName,
        displayName: displayName,
        state: false,
        lastUpdated: new Date().toISOString(),
        type: deviceTypeCategory,
        deviceCategory: deviceTypeCategory,
        terminalId: linkedTerminalId,
        isTerminal: DeviceNamingUtils.isTerminal(deviceId),
        isRelay: DeviceNamingUtils.isRelay(deviceId),
        namingValid: DeviceNamingUtils.isTerminal(deviceId) || DeviceNamingUtils.isRelay(deviceId),
        autoRegistered: true
      });
    }
    
    const device = devices.get(deviceId);
    const oldState = device.state;
    device.state = newState;
    device.lastUpdated = new Date().toISOString();
    
    // Log the action
    logDeviceAction(deviceId, newState ? 'TURN_ON' : 'TURN_OFF', method, {
      oldState,
      newState,
      command: normalizedCommand
    });
    
    // Broadcast state change to all connected clients
    broadcastToAllClients({
      type: 'deviceStateChanged',
      data: { deviceId, device }
    });
    
    console.log(`Device ${deviceId}: ${oldState ? 'ON' : 'OFF'} -> ${newState ? 'ON' : 'OFF'} (via ${method})`);
    
    return device;
  }
  
  return null;
}

// Routes

// Serve the main dashboard
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// ============================================================================
// SECURE API ENDPOINTS
// ============================================================================

// Secure device registration with naming system support
app.post('/api/device/register', (req, res) => {
  try {
    const { device_id, device_type, device_name, hardware_hash, app_version, terminal_id } = req.body;
    console.log('Received device registration request:', req.body);

    if (!device_id || !device_type) {
      return res.status(400).json({ error: 'device_id and device_type are required' });
    }

    // Validate device naming format
    let deviceTypeCategory = device_type;
    let linkedTerminalId = terminal_id;

    if (DeviceNamingUtils.isTerminal(device_id)) {
      deviceTypeCategory = 'terminal';
      linkedTerminalId = device_id;
    } else if (DeviceNamingUtils.isRelay(device_id)) {
      deviceTypeCategory = 'relay';
      linkedTerminalId = DeviceNamingUtils.extractTerminalShortId(device_id);
      
      if (!linkedTerminalId) {
        return res.status(400).json({ 
          error: 'Invalid relay device ID format. Expected: T-XXXX-RNN or T-XXXX-NAME' 
        });
      }
    } else {
      // Handle legacy devices or custom device IDs
      if (device_type === 'terminal') {
        deviceTypeCategory = 'terminal';
        linkedTerminalId = device_id;
      } else if (device_type === 'relay') {
        deviceTypeCategory = 'relay';
        // Try to extract terminal ID from custom format
        const terminalIdMatch = device_id.match(/^(T-[A-Z0-9]+)/);
        linkedTerminalId = terminalIdMatch ? terminalIdMatch[1] : terminal_id;
      }
    }

    console.log(`Device registered: ${device_id} (${device_name}) - Type: ${deviceTypeCategory}, Terminal: ${linkedTerminalId}`);

    // Register device with security manager
    const credentials = security.registerDevice({
      device_id,
      device_type,
      device_name,
      hardware_hash,
      app_version,
    });

    // Register in device storage with enhanced metadata
    const displayName = DeviceNamingUtils.getDisplayName(device_id, device_name);
    const device = {
      id: device_id,
      name: device_name || displayName,
      displayName: displayName,
      state: false,
      lastUpdated: new Date().toISOString(),
      type: deviceTypeCategory,
      deviceCategory: deviceTypeCategory,
      terminalId: linkedTerminalId,
      isTerminal: DeviceNamingUtils.isTerminal(device_id),
      isRelay: DeviceNamingUtils.isRelay(device_id),
      namingValid: DeviceNamingUtils.isTerminal(device_id) || DeviceNamingUtils.isRelay(device_id)
    };

    devices.set(device_id, device);
    logDeviceAction(device_id, 'REGISTER', 'HTTP_SECURE', { 
      device_name, 
      device_type: deviceTypeCategory, 
      terminal_id: linkedTerminalId,
      naming_valid: device.namingValid
    });

    // Enhanced response with naming information
    res.json({
      ...credentials,
      device_info: {
        display_name: displayName,
        device_category: deviceTypeCategory,
        terminal_id: linkedTerminalId,
        naming_valid: device.namingValid
      }
    });
  } catch (error) {
    console.error('Device registration error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Refresh access token
app.post('/api/device/refresh', (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({ error: 'refresh_token is required' });
    }

    const result = security.refreshAccessToken(refresh_token);
    res.json(result);
  } catch (error) {
    res.status(401).json({ error: error.message });
  }
});

// Revoke device credentials
app.post('/api/device/revoke', security.requireAuth(), (req, res) => {
  try {
    const deviceId = req.device.device_id;
    security.revokeDevice(deviceId);

    logDeviceAction(deviceId, 'REVOKE', 'HTTP_SECURE', {});

    res.json({ success: true, message: 'Device credentials revoked' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ============================================================================
// DEVICE NAMING MANAGEMENT ENDPOINTS
// ============================================================================

// Generate new terminal shortId
app.get('/api/naming/generate-terminal-id', (req, res) => {
  try {
    const terminalId = DeviceNamingUtils.generateTerminalShortId();
    const summary = DeviceNamingUtils.generateNamingSummary(terminalId);
    
    res.json({
      success: true,
      terminal_id: terminalId,
      naming_summary: summary
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate relay device ID for terminal
app.post('/api/naming/generate-relay-id', (req, res) => {
  try {
    const { terminal_id, relay_index, relay_name } = req.body;
    
    if (!terminal_id) {
      return res.status(400).json({ error: 'terminal_id is required' });
    }
    
    if (!DeviceNamingUtils.isValidTerminalShortId(terminal_id)) {
      return res.status(400).json({ error: 'Invalid terminal shortId format' });
    }
    
    let relayId;
    if (relay_name) {
      relayId = DeviceNamingUtils.generateRelayDeviceId(terminal_id, relay_name);
    } else if (relay_index !== undefined) {
      relayId = DeviceNamingUtils.generateRelayDeviceId(terminal_id, relay_index);
    } else {
      return res.status(400).json({ error: 'Either relay_index or relay_name is required' });
    }
    
    const displayName = DeviceNamingUtils.getDisplayName(relayId);
    
    res.json({
      success: true,
      relay_device_id: relayId,
      display_name: displayName,
      terminal_id: terminal_id
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Validate device naming format
app.post('/api/naming/validate', (req, res) => {
  try {
    const { device_id } = req.body;
    
    if (!device_id) {
      return res.status(400).json({ error: 'device_id is required' });
    }
    
    const isTerminal = DeviceNamingUtils.isTerminal(device_id);
    const isRelay = DeviceNamingUtils.isRelay(device_id);
    const isValid = isTerminal || isRelay;
    
    let terminalId = null;
    let deviceType = 'unknown';
    
    if (isTerminal) {
      terminalId = device_id;
      deviceType = 'terminal';
    } else if (isRelay) {
      terminalId = DeviceNamingUtils.extractTerminalShortId(device_id);
      deviceType = 'relay';
    }
    
    const displayName = isValid ? DeviceNamingUtils.getDisplayName(device_id) : device_id;
    
    res.json({
      success: true,
      device_id: device_id,
      is_valid: isValid,
      device_type: deviceType,
      terminal_id: terminalId,
      display_name: displayName,
      validation_details: {
        is_terminal: isTerminal,
        is_relay: isRelay,
        naming_format_valid: isValid
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get naming summary for terminal
app.get('/api/naming/summary/:terminalId', (req, res) => {
  try {
    const { terminalId } = req.params;
    
    if (!DeviceNamingUtils.isValidTerminalShortId(terminalId)) {
      return res.status(400).json({ error: 'Invalid terminal shortId format' });
    }
    
    const summary = DeviceNamingUtils.generateNamingSummary(terminalId);
    
    res.json({
      success: true,
      terminal_id: terminalId,
      ...summary
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get devices grouped by terminal
app.get('/api/devices/grouped', (req, res) => {
  try {
    const grouped = DeviceNamingUtils.groupDevicesByTerminal(devices);
    
    // Convert Maps to objects for JSON serialization
    const result = {
      terminals: Object.fromEntries(grouped.terminals),
      relays: Object.fromEntries(
        Array.from(grouped.relays.entries()).map(([terminalId, relayDevices]) => [
          terminalId,
          relayDevices
        ])
      ),
      ungrouped: Object.fromEntries(grouped.ungrouped),
      summary: {
        terminal_count: grouped.terminals.size,
        relay_groups: grouped.relays.size,
        ungrouped_count: grouped.ungrouped.size,
        total_devices: devices.size
      }
    };
    
    res.json({
      success: true,
      grouped_devices: result
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Face recognition endpoint
app.post('/api/face/recognize',
  security.requireAuth(),
  security.requireHmacSignature(),
  (req, res) => {
    try {
      const deviceId = req.device.device_id;
      const { image_data, confidence_score, metadata } = req.body.payload || {};

      if (!image_data) {
        return res.status(400).json({
          success: false,
          error: 'image_data is required'
        });
      }

      console.log(`👤 Face recognition request from ${deviceId}`);
      console.log(`   Confidence: ${confidence_score}`);
      console.log(`   Image data size: ${image_data.length} characters`);
      console.log(`   Metadata:`, metadata);

      // Store image data for display in web UI
      const imageDataUrl = `data:image/jpeg;base64,${image_data}`;

      // Process recognition request
      const result = processRecognitionRequest(image_data, deviceId, imageDataUrl);

      // Save face image to file system
      const savedImagePath = saveFaceImage(image_data, result.recognitionId, result);
      if (savedImagePath) {
        result.savedImagePath = savedImagePath;
      }

      // Log the recognition attempt
      logDeviceAction(deviceId, 'FACE_RECOGNITION', 'HTTP_SECURE', {
        confidence_score,
        recognized: result.recognized,
        recognition_id: result.recognitionId,
        saved_image: savedImagePath,
        metadata
      });

      res.json({
        success: true,
        data: result,
        timestamp: Date.now(),
      });

    } catch (error) {
      console.error('Face recognition error:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// Get saved face images list
app.get('/api/face/images', (req, res) => {
  try {
    const files = fs.readdirSync(faceImagesDir)
      .filter(file => file.endsWith('.jpg'))
      .map(file => {
        const stats = fs.statSync(path.join(faceImagesDir, file));
        const parts = file.replace('.jpg', '').split('_');
        return {
          filename: file,
          path: `/face_images/${file}`,
          recognitionId: parts[0],
          status: parts[1],
          userName: parts[2],
          timestamp: parts[3],
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));

    res.json({
      success: true,
      data: files,
      total: files.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to list face images'
    });
  }
});

// Get face recognition settings
app.get('/api/face/settings', (req, res) => {
  res.json({
    success: true,
    data: {
      autoRecognize: faceRecognitionSettings.autoRecognize,
      pendingCount: faceRecognitionSettings.pendingRecognitions.size,
    }
  });
});

// Update face recognition settings
app.post('/api/face/settings', (req, res) => {
  try {
    const { autoRecognize } = req.body;

    if (typeof autoRecognize === 'boolean') {
      faceRecognitionSettings.autoRecognize = autoRecognize;
      console.log(`🔧 Face recognition auto mode: ${autoRecognize ? 'ON' : 'OFF'}`);
    }

    res.json({
      success: true,
      data: {
        autoRecognize: faceRecognitionSettings.autoRecognize,
        pendingCount: faceRecognitionSettings.pendingRecognitions.size,
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get pending recognitions (for manual approval)
app.get('/api/face/pending', (req, res) => {
  const pending = Array.from(faceRecognitionSettings.pendingRecognitions.values());
  res.json({
    success: true,
    data: pending
  });
});

// Approve/reject manual recognition
app.post('/api/face/approve/:recognitionId', (req, res) => {
  try {
    const { recognitionId } = req.params;
    const { approved, user } = req.body;

    const pendingData = faceRecognitionSettings.pendingRecognitions.get(recognitionId);
    if (!pendingData) {
      return res.status(404).json({
        success: false,
        error: 'Recognition request not found'
      });
    }

    let result;
    if (approved) {
      const userData = user || generateRandomUser();
      result = {
        success: true,
        recognized: true,
        user: userData,
        confidence: faker.datatype.float({ min: 0.7, max: 0.95, precision: 0.01 }),
        recognitionId: recognitionId,
        timestamp: new Date().toISOString(),
        processingTime: faker.datatype.number({ min: 100, max: 1000 }),
        approvedBy: 'manual',
      };
    } else {
      result = {
        success: true,
        recognized: false,
        recognitionId: recognitionId,
        timestamp: new Date().toISOString(),
        reason: 'rejected_by_admin',
      };
    }

    // Remove from pending
    faceRecognitionSettings.pendingRecognitions.delete(recognitionId);

    // Log the approval/rejection
    logDeviceAction(pendingData.deviceId, 'FACE_RECOGNITION_APPROVAL', 'HTTP_MANUAL', {
      recognition_id: recognitionId,
      approved: approved,
      recognized: result.recognized,
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Secure message endpoint (requires authentication and HMAC signature)
app.post('/api/message',
  security.requireAuth(),
  security.requireHmacSignature(),
  (req, res) => {
    try {
      const deviceId = req.device.device_id;
      const { type, payload, message_id, timestamp } = req.body;

      if (!type || !payload) {
        return res.status(400).json({
          success: false,
          error: 'type and payload are required'
        });
      }

      console.log(`🔐 Secure message from ${deviceId}: ${type}`);
      console.log(`   Payload:`, payload);

      // Log the secure message
      logDeviceAction(deviceId, 'SECURE_MESSAGE', 'HTTP_SECURE', {
        type,
        payload,
        message_id,
        timestamp
      });

      // Handle different message types
      let responseData = { message_received: true };

      switch (type) {
        case 'relay_control':
          if (payload.action) {
            const device = handleRelayCommand(deviceId, payload.action.toUpperCase(), 'HTTP_SECURE');
            responseData.relay_status = device ? payload.action : 'failed';
          }
          break;

        case 'get_relay_status': {
          const device = devices.get(deviceId);
          responseData.relay_status = device ? device.state : 'unknown';
          break;
        }

        case 'ping':
        case 'heartbeat':
          responseData.pong = true;
          responseData.server_time = Date.now();
          break;

        case 'status_request':
          const device = devices.get(deviceId);
          responseData.device_status = device ? device.status : 'unknown';
          break;

        default:
          responseData.message = `Secure message type '${type}' received`;
      }

      res.json({
        success: true,
        data: responseData,
        timestamp: Date.now(),
        message_id: message_id
      });

    } catch (error) {
      console.error('Secure message error:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

// Plain text message endpoint (no authentication required)
app.post('/api/message/plain', (req, res) => {
  try {
    const { type, payload, device_id, device_type, message_id, timestamp } = req.body;

    if (!type || !payload || !device_id) {
      return res.status(400).json({
        success: false,
        error: 'type, payload, and device_id are required'
      });
    }

    console.log(`📨 Plain text message from ${device_id}: ${type}`);
    console.log(`   Payload:`, payload);

    // Log the plain text message
    logDeviceAction(device_id, 'PLAIN_MESSAGE', 'HTTP_PLAIN', {
      type,
      payload,
      message_id,
      timestamp
    });

    // Handle different message types
    let responseData = { message_received: true };

    switch (type) {
      case 'relay_control':
        if (payload.action) {
          const device = handleRelayCommand(device_id, payload.action.toUpperCase(), 'HTTP_PLAIN');
          responseData.relay_status = device ? payload.action : 'failed';
        }
        break;

      case 'ping':
        responseData.pong = true;
        responseData.server_time = Date.now();
        break;

      case 'status_request':
        const device = devices.get(device_id);
        responseData.device_status = device ? device.status : 'unknown';
        break;

      default:
        responseData.message = `Plain text message type '${type}' received`;
    }

    res.json({
      success: true,
      data: responseData,
      timestamp: Date.now(),
      message_id: message_id
    });

  } catch (error) {
    console.error('Plain text message error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Secure relay control
app.post('/relay/control',
  security.requireAuth(),
  security.requireHmacSignature(),
  security.requireScope('relay_control'),
  (req, res) => {
    try {
      const { device_id, action } = req.body;
      const device = handleRelayCommand(device_id, action, 'HTTP_SECURE');

      if (device) {
        res.json({ success: true, device });
      } else {
        res.status(400).json({ error: 'Invalid command' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
);

// Get secure device status
app.get('/relay/status', security.requireAuth(), (req, res) => {
  try {
    const deviceId = req.query.device_id || req.device.device_id;
    const device = devices.get(deviceId);

    if (!device) {
      return res.json({ status: 'unknown' });
    }

    res.json({
      status: device.state ? 'unlocked' : 'locked',
      device
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// List registered devices (secure)
app.get('/api/devices', security.requireAuth(), (req, res) => {
  try {
    const secureDevices = security.getAllDevices();
    res.json(secureDevices);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ============================================================================
// LEGACY API ENDPOINTS (for backward compatibility and dashboard)
// ============================================================================

// Legacy device registration with naming system support
app.post('/register', (req, res) => {
  const { deviceId, deviceName, type, terminalId } = req.body;

  if (!deviceId) {
    return res.status(400).json({ error: 'deviceId is required' });
  }

  // Determine device type and terminal linkage
  let deviceTypeCategory = type || 'relay';
  let linkedTerminalId = terminalId;

  if (DeviceNamingUtils.isTerminal(deviceId)) {
    deviceTypeCategory = 'terminal';
    linkedTerminalId = deviceId;
  } else if (DeviceNamingUtils.isRelay(deviceId)) {
    deviceTypeCategory = 'relay';
    linkedTerminalId = DeviceNamingUtils.extractTerminalShortId(deviceId);
  }

  const displayName = DeviceNamingUtils.getDisplayName(deviceId, deviceName);
  const device = {
    id: deviceId,
    name: deviceName || displayName,
    displayName: displayName,
    state: false,
    lastUpdated: new Date().toISOString(),
    type: deviceTypeCategory,
    deviceCategory: deviceTypeCategory,
    terminalId: linkedTerminalId,
    isTerminal: DeviceNamingUtils.isTerminal(deviceId),
    isRelay: DeviceNamingUtils.isRelay(deviceId),
    namingValid: DeviceNamingUtils.isTerminal(deviceId) || DeviceNamingUtils.isRelay(deviceId)
  };

  devices.set(deviceId, device);

  logDeviceAction(deviceId, 'REGISTER', 'HTTP_LEGACY', { 
    deviceName, 
    type: deviceTypeCategory, 
    terminal_id: linkedTerminalId,
    naming_valid: device.namingValid
  });

  res.json({ 
    success: true, 
    device,
    device_info: {
      display_name: displayName,
      device_category: deviceTypeCategory,
      terminal_id: linkedTerminalId,
      naming_valid: device.namingValid
    }
  });
});

// Get all devices with enhanced naming information
app.get('/devices', (req, res) => {
  const deviceList = Array.from(devices.values()).map(device => ({
    ...device,
    // Ensure all devices have enhanced metadata
    displayName: device.displayName || DeviceNamingUtils.getDisplayName(device.id, device.name),
    terminalId: device.terminalId || (DeviceNamingUtils.isRelay(device.id) ? DeviceNamingUtils.extractTerminalShortId(device.id) : null),
    isTerminal: device.isTerminal !== undefined ? device.isTerminal : DeviceNamingUtils.isTerminal(device.id),
    isRelay: device.isRelay !== undefined ? device.isRelay : DeviceNamingUtils.isRelay(device.id),
    namingValid: device.namingValid !== undefined ? device.namingValid : (DeviceNamingUtils.isTerminal(device.id) || DeviceNamingUtils.isRelay(device.id))
  }));
  
  res.json(deviceList);
});

// Get specific device
app.get('/devices/:deviceId', (req, res) => {
  const { deviceId } = req.params;
  const device = devices.get(deviceId);
  
  if (!device) {
    return res.status(404).json({ error: 'Device not found' });
  }
  
  res.json(device);
});

// Get device logs
app.get('/devices/:deviceId/logs', (req, res) => {
  const { deviceId } = req.params;
  const logs = deviceLogs.get(deviceId) || [];
  res.json(logs);
});

// Generic relay control endpoints
app.get('/relay/on', (req, res) => {
  const deviceId = req.query.deviceId || 'default';
  const device = handleRelayCommand(deviceId, 'ON', 'HTTP');
  res.json({ success: true, device });
});

app.get('/relay/off', (req, res) => {
  const deviceId = req.query.deviceId || 'default';
  const device = handleRelayCommand(deviceId, 'OFF', 'HTTP');
  res.json({ success: true, device });
});

app.get('/relay/status', (req, res) => {
  const deviceId = req.query.deviceId || 'default';
  const device = devices.get(deviceId);
  
  if (!device) {
    return res.json({ status: 'unknown' });
  }
  
  res.json({ 
    status: device.state ? 'on' : 'off',
    device 
  });
});

// Device-specific endpoints
app.get('/devices/:deviceId/on', (req, res) => {
  const { deviceId } = req.params;
  const device = handleRelayCommand(deviceId, 'ON', 'HTTP');
  res.json({ success: true, device });
});

app.get('/devices/:deviceId/off', (req, res) => {
  const { deviceId } = req.params;
  const device = handleRelayCommand(deviceId, 'OFF', 'HTTP');
  res.json({ success: true, device });
});

app.get('/devices/:deviceId/status', (req, res) => {
  const { deviceId } = req.params;
  const device = devices.get(deviceId);
  
  if (!device) {
    return res.json({ status: 'unknown' });
  }
  
  res.json({ 
    status: device.state ? 'on' : 'off',
    device 
  });
});

// POST endpoints for relay control
app.post('/relay/control', (req, res) => {
  const { deviceId, command } = req.body;
  const id = deviceId || 'default';
  const device = handleRelayCommand(id, command, 'HTTP');
  
  if (device) {
    res.json({ success: true, device });
  } else {
    res.status(400).json({ error: 'Invalid command' });
  }
});

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log(`Client connected: `, req.headers['x-forwarded-for'] || req.connection.remoteAddress);
  const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  wsClients.set(clientId, {
    ws,
    deviceId: null,
    authenticated: false,
    connectedAt: new Date()
  });

  console.log(`🔌 New WebSocket connection: ${clientId}`);
  console.log(`📊 Total connections: ${wsClients.size}`);

  // Send welcome message and current device states
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to WebSocket server',
    clientId: clientId,
    deviceList: Array.from(devices.values()),
    timestamp: Date.now()
  }));

  // Handle incoming messages
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log(`📨 Message from ${clientId}:`, message);

      handleWebSocketMessage(clientId, message, ws);
    } catch (error) {
      console.error('❌ Error parsing message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid JSON format',
        timestamp: Date.now()
      }));
    }
  });

  // Handle connection close
  ws.on('close', (code, reason) => {
    console.log(`🔌 Connection closed: ${clientId}, Code: ${code}, Reason: ${reason}`);

    // Find and mark device as disconnected
    const client = wsClients.get(clientId);
    if (client && client.deviceId) {
      const device = devices.get(client.deviceId);
      if (device) {
        device.connected = false;
        device.lastUpdated = new Date().toISOString();
        devices.set(client.deviceId, device);

        logDeviceAction(client.deviceId, 'DISCONNECT', 'WebSocket', 'Device disconnected');

        // Broadcast device state change to all clients
        broadcastToAllClients({
          type: 'deviceStateChanged',
          data: { deviceId: client.deviceId, device }
        });
      }
    }

    wsClients.delete(clientId);
    console.log(`📊 Total connections: ${wsClients.size}`);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error(`❌ WebSocket error for ${clientId}:`, error);
    wsClients.delete(clientId);
  });
});

// Handle different WebSocket message types
function handleWebSocketMessage(clientId, message, ws) {
  const client = wsClients.get(clientId);
  if (!client) return;

  const { type, payload, message_id } = message;

  switch (type) {
    case 'ping':
      handlePing(clientId, message, ws);
      break;

    case 'relay_control':
      handleRelayControl(clientId, message, ws);
      break;

    case 'status_request':
      handleStatusRequest(clientId, message, ws);
      break;

    case 'get_relay_status':
      handleStatusRequest(clientId, message, ws);
      break;

    case 'device_auth':
      handleDeviceAuth(clientId, message, ws);
      break;

    case 'registerDevice':
      handleDeviceRegistration(clientId, message, ws);
      break;

    default:
      console.log(`❓ Unknown message type: ${type}`);
      sendResponse(ws, message_id, {
        success: false,
        error: `Unknown message type: ${type}`
      });
  }
}

// Handle ping messages
function handlePing(clientId, message, ws) {
  console.log(`🏓 Ping from ${clientId}`);

  sendResponse(ws, message.message_id, {
    success: true,
    data: {
      pong: true,
      server_time: Date.now(),
      client_id: clientId
    }
  });
}

// Handle relay control
function handleRelayControl(clientId, message, ws) {
  const { action, deviceId } = message.payload || {};
  const targetDeviceId = deviceId || 'default_relay';
  console.log(`🔌 Relay control from ${clientId}: ${action} for device ${targetDeviceId}`);

  const device = handleRelayCommand(targetDeviceId, action, 'WebSocket');

  sendResponse(ws, message.message_id, {
    success: !!device,
    data: device ? {
      action: action,
      status: 'completed',
      relay_id: targetDeviceId,
      device_id: targetDeviceId,
      timestamp: Date.now()
    } : null,
    error: device ? null : 'Invalid relay command'
  });

  // Broadcast status update to all clients
  if (device) {
    broadcastToAllClients({
      type: 'relay_status_update',
      data: {
        relay_id: targetDeviceId,
        device_id: targetDeviceId,
        status: action,
        device: device,
        timestamp: Date.now()
      }
    });
  }
}

// Handle status requests
function handleStatusRequest(clientId, message, ws) {
  const { component } = message.payload || {};
  console.log(`📊 Status request from ${clientId}: ${component}`);

  const statusData = {
    relay: {
      status: Math.random() > 0.5 ? 'on' : 'off',
      last_action: Date.now() - Math.random() * 60000,
      health: 'good'
    },
    server: {
      uptime: process.uptime(),
      connections: wsClients.size,
      memory: process.memoryUsage(),
      timestamp: Date.now()
    }
  };

  sendResponse(ws, message.message_id, {
    success: true,
    data: statusData[component] || statusData
  });
}

// Handle device authentication
function handleDeviceAuth(clientId, message, ws) {
  const client = wsClients.get(clientId);
  if (!client) return;

  console.log(`🔐 Device auth from ${clientId}`);

  // Mark as authenticated
  client.authenticated = true;
  client.deviceId = message.payload?.device_id || clientId;

  sendResponse(ws, message.message_id, {
    success: true,
    data: {
      authenticated: true,
      device_id: client.deviceId,
      session_id: clientId,
      timestamp: Date.now()
    }
  });
}

// Handle device registration
function handleDeviceRegistration(clientId, message, ws) {
  const { deviceId, deviceName, type } = message.payload || {};
  const client = wsClients.get(clientId);
  if (!client) return;

  if (!deviceId) {
    sendResponse(ws, message.message_id, {
      success: false,
      error: 'deviceId is required'
    });
    return;
  }

  const device = {
    id: deviceId,
    name: deviceName || `Device ${deviceId}`,
    state: false,
    lastUpdated: new Date().toISOString(),
    type: type || 'relay',
    clientId: clientId,
    connected: true
  };

  devices.set(deviceId, device);
  client.deviceId = deviceId;
  logDeviceAction(deviceId, 'REGISTER', 'WebSocket', { deviceName, type });

  sendResponse(ws, message.message_id, {
    success: true,
    data: device
  });

  // Broadcast device list update to all clients
  broadcastToAllClients({
    type: 'deviceStateChanged',
    data: { deviceId, device }
  });
}

// Send response to specific client
function sendResponse(ws, messageId, response) {
  const responseMessage = {
    message_id: messageId,
    timestamp: Date.now(),
    ...response
  };

  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(responseMessage));
  }
}



const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 WebSocket Relay Controller Test Server running on port ${PORT}`);
  console.log(`📡 WebSocket: ws://0.0.0.0:${PORT}`);
  console.log(`📊 Dashboard: http://0.0.0.0:${PORT}`);
  console.log(`🔒 Secure API: http://0.0.0.0:${PORT}/api`);
  console.log(`\n🔐 SECURE API ENDPOINTS:`);
  console.log(`  POST /api/device/register - Register device (get JWT + secret)`);
  console.log(`  POST /api/device/refresh - Refresh access token`);
  console.log(`  POST /api/device/revoke - Revoke device credentials`);
  console.log(`  POST /relay/control - Secure relay control (JWT + HMAC)`);
  console.log(`  GET  /relay/status - Get relay status (JWT required)`);
  console.log(`  GET  /api/devices - List registered devices (JWT required)`);
  console.log(`\n📡 LEGACY API ENDPOINTS:`);
  console.log(`  GET  /devices - List all devices`);
  console.log(`  POST /register - Register a device (legacy)`);
  console.log(`  GET  /relay/on?deviceId=xxx - Turn relay on`);
  console.log(`  GET  /relay/off?deviceId=xxx - Turn relay off`);
  console.log(`  GET  /relay/status?deviceId=xxx - Get relay status`);
  console.log(`  POST /relay/control - Control relay via JSON`);
  console.log(`\n🔑 Security Features:`);
  console.log(`  ✅ JWT Authentication`);
  console.log(`  ✅ HMAC Request Signing`);
  console.log(`  ✅ Replay Attack Prevention`);
  console.log(`  ✅ Device Scope Management`);
  console.log(`  ✅ Token Refresh & Revocation`);
});

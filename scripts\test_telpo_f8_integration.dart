#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'dart:async';

/// Automated testing script for Telpo F8 integration
/// Tests face detection performance, device compatibility, and system stability
void main(List<String> args) async {
  print('🧪 Starting Telpo F8 Integration Testing...');
  
  final tester = TelpoF8IntegrationTester();
  
  try {
    await tester.runFullTestSuite();
    print('✅ All tests completed successfully!');
  } catch (e) {
    print('❌ Testing failed: $e');
    exit(1);
  }
}

class TelpoF8IntegrationTester {
  static const String packageName = 'com.ccam.terminal';
  static const String apkPath = 'build/app/outputs/flutter-apk/app-terminal-debug.apk';
  static const String activityName = '$packageName/com.ccam.terminal.MainActivity';
  
  final List<TestResult> _results = [];
  
  Future<void> runFullTestSuite() async {
    print('\n📋 Telpo F8 Integration Test Suite');
    print('=' * 50);
    
    await _test1_PreTestChecks();
    await _test2_DeviceDetection();
    await _test3_AppInstallation();
    await _test4_AppLaunch();
    await _test5_FaceDetectionInit();
    await _test6_PerformanceBenchmark();
    await _test7_StabilityTest();
    await _test8_CleanupTest();
    
    _printTestSummary();
  }
  
  /// Test 1: Pre-test environment checks
  Future<void> _test1_PreTestChecks() async {
    print('\n🔍 Test 1: Pre-test Environment Checks');
    
    try {
      // Check ADB connection
      final adbResult = await Process.run('adb', ['devices']);
      if (adbResult.exitCode != 0) {
        throw Exception('ADB not available or no devices connected');
      }
      
      final devices = adbResult.stdout.toString();
      if (!devices.contains('device')) {
        throw Exception('No Android devices connected via ADB');
      }
      
      print('  ✅ ADB connection established');
      
      // Check APK exists
      final apkFile = File(apkPath);
      if (!await apkFile.exists()) {
        throw Exception('APK file not found: $apkPath');
      }
      
      final apkSize = await apkFile.length();
      print('  ✅ APK file found (${(apkSize / 1024 / 1024).toStringAsFixed(1)}MB)');
      
      // Check device model
      final modelResult = await Process.run('adb', ['shell', 'getprop', 'ro.product.model']);
      final deviceModel = modelResult.stdout.toString().trim().toLowerCase();
      
      if (deviceModel.contains('telpo') && deviceModel.contains('f8')) {
        print('  ✅ Telpo F8 device detected: $deviceModel');
      } else {
        print('  ⚠️ Warning: Not a Telpo F8 device: $deviceModel');
      }
      
      _addResult('Pre-test Checks', true, 'Environment ready');
      
    } catch (e) {
      print('  ❌ Pre-test checks failed: $e');
      _addResult('Pre-test Checks', false, e.toString());
      rethrow;
    }
  }
  
  /// Test 2: Device detection capabilities
  Future<void> _test2_DeviceDetection() async {
    print('\n📱 Test 2: Device Detection');
    
    try {
      // Get device properties
      final props = await _getDeviceProperties();
      
      print('  📊 Device Properties:');
      print('     Model: ${props['model']}');
      print('     Android: ${props['android']}');
      print('     RAM: ${props['ram']}');
      print('     Storage: ${props['storage']}');
      
      // Check if device meets requirements
      final isCompatible = _checkDeviceCompatibility(props);
      
      if (isCompatible) {
        print('  ✅ Device meets requirements');
        _addResult('Device Detection', true, 'Compatible device');
      } else {
        print('  ⚠️ Device may not meet all requirements');
        _addResult('Device Detection', true, 'Partial compatibility');
      }
      
    } catch (e) {
      print('  ❌ Device detection failed: $e');
      _addResult('Device Detection', false, e.toString());
    }
  }
  
  /// Test 3: App installation
  Future<void> _test3_AppInstallation() async {
    print('\n📦 Test 3: App Installation');
    
    try {
      // Uninstall previous version if exists
      print('  🗑️ Removing previous installation...');
      await Process.run('adb', ['uninstall', packageName]);
      
      // Install new APK
      print('  📥 Installing APK...');
      final installResult = await Process.run('adb', ['install', '-r', apkPath]);
      
      if (installResult.exitCode == 0) {
        print('  ✅ App installed successfully');
        _addResult('App Installation', true, 'Installation successful');
      } else {
        throw Exception('Installation failed: ${installResult.stderr}');
      }
      
    } catch (e) {
      print('  ❌ App installation failed: $e');
      _addResult('App Installation', false, e.toString());
      rethrow;
    }
  }
  
  /// Test 4: App launch
  Future<void> _test4_AppLaunch() async {
    print('\n🚀 Test 4: App Launch');
    
    try {
      // Clear logs
      await Process.run('adb', ['logcat', '-c']);
      
      // Launch app
      print('  🚀 Launching app...');
      final launchResult = await Process.run('adb', ['shell', 'am', 'start', '-n', activityName]);
      
      if (launchResult.exitCode != 0) {
        throw Exception('Failed to launch app: ${launchResult.stderr}');
      }
      
      // Wait for app to start
      await Future.delayed(const Duration(seconds: 5));
      
      // Check if app is running
      final psResult = await Process.run('adb', ['shell', 'ps', '|', 'grep', packageName]);
      
      if (psResult.stdout.toString().contains(packageName)) {
        print('  ✅ App launched successfully');
        _addResult('App Launch', true, 'Launch successful');
      } else {
        throw Exception('App not found in running processes');
      }
      
    } catch (e) {
      print('  ❌ App launch failed: $e');
      _addResult('App Launch', false, e.toString());
    }
  }
  
  /// Test 5: Face detection initialization
  Future<void> _test5_FaceDetectionInit() async {
    print('\n🔍 Test 5: Face Detection Initialization');
    
    try {
      print('  📋 Monitoring face detection logs...');
      
      // Monitor logs for face detection initialization
      final logProcess = await Process.start('adb', ['logcat']);
      final logStream = logProcess.stdout
          .transform(utf8.decoder)
          .transform(const LineSplitter());
      
      bool hybridSystemDetected = false;
      bool mlKitFallback = false;
      bool initSuccess = false;
      
      final timeout = Timer(const Duration(seconds: 30), () {
        logProcess.kill();
      });
      
      await for (final line in logStream) {
        if (line.contains('Telpo F8') || line.contains('Hybrid Face Detection')) {
          hybridSystemDetected = true;
          print('  🎯 Hybrid system detected');
        }
        
        if (line.contains('ML Kit Face Detection')) {
          mlKitFallback = true;
          print('  🔄 ML Kit fallback detected');
        }
        
        if (line.contains('initialized successfully') || line.contains('khởi tạo thành công')) {
          initSuccess = true;
          print('  ✅ Face detection initialized');
          break;
        }
        
        if (line.contains('Exception') || line.contains('Error')) {
          print('  ⚠️ Error detected: ${line.substring(0, 100)}...');
        }
      }
      
      timeout.cancel();
      logProcess.kill();
      
      if (initSuccess) {
        final systemType = hybridSystemDetected ? 'Hybrid' : (mlKitFallback ? 'ML Kit' : 'Unknown');
        print('  ✅ Face detection initialized with $systemType system');
        _addResult('Face Detection Init', true, 'Initialized with $systemType');
      } else {
        throw Exception('Face detection initialization timeout');
      }
      
    } catch (e) {
      print('  ❌ Face detection initialization failed: $e');
      _addResult('Face Detection Init', false, e.toString());
    }
  }
  
  /// Test 6: Performance benchmark
  Future<void> _test6_PerformanceBenchmark() async {
    print('\n⚡ Test 6: Performance Benchmark');
    
    try {
      print('  📊 Collecting performance metrics...');
      
      // Get memory usage
      final memResult = await Process.run('adb', ['shell', 'dumpsys', 'meminfo', packageName]);
      final memOutput = memResult.stdout.toString();
      final memMatch = RegExp(r'TOTAL\s+(\d+)').firstMatch(memOutput);
      final memoryMB = memMatch != null ? (int.parse(memMatch.group(1)!) / 1024).round() : 0;
      
      // Get CPU usage
      final cpuResult = await Process.run('adb', ['shell', 'top', '-n', '1']);
      final cpuOutput = cpuResult.stdout.toString();
      final cpuMatch = RegExp(packageName + r'.*?(\d+\.?\d*)%').firstMatch(cpuOutput);
      final cpuPercent = cpuMatch != null ? double.parse(cpuMatch.group(1)!) : 0.0;
      
      // Get temperature
      final tempResult = await Process.run('adb', ['shell', 'cat', '/sys/class/thermal/thermal_zone0/temp']);
      final tempOutput = tempResult.stdout.toString().trim();
      final temperature = tempOutput.isNotEmpty ? (int.parse(tempOutput) / 1000).round() : 0;
      
      print('  📊 Performance Metrics:');
      print('     Memory Usage: ${memoryMB}MB');
      print('     CPU Usage: ${cpuPercent.toStringAsFixed(1)}%');
      print('     Temperature: ${temperature}°C');
      
      // Evaluate performance
      final memoryOK = memoryMB < 150;
      final cpuOK = cpuPercent < 60;
      final tempOK = temperature < 85;
      
      if (memoryOK && cpuOK && tempOK) {
        print('  ✅ Performance within acceptable limits');
        _addResult('Performance Benchmark', true, 'Memory: ${memoryMB}MB, CPU: ${cpuPercent}%, Temp: ${temperature}°C');
      } else {
        print('  ⚠️ Performance concerns detected');
        _addResult('Performance Benchmark', false, 'Memory: ${memoryMB}MB, CPU: ${cpuPercent}%, Temp: ${temperature}°C');
      }
      
    } catch (e) {
      print('  ❌ Performance benchmark failed: $e');
      _addResult('Performance Benchmark', false, e.toString());
    }
  }
  
  /// Test 7: Stability test
  Future<void> _test7_StabilityTest() async {
    print('\n🔄 Test 7: Stability Test (5 minutes)');
    
    try {
      print('  ⏱️ Running stability test for 5 minutes...');
      
      final startTime = DateTime.now();
      final endTime = startTime.add(const Duration(minutes: 5));
      
      int crashCount = 0;
      int errorCount = 0;
      
      while (DateTime.now().isBefore(endTime)) {
        // Check if app is still running
        final psResult = await Process.run('adb', ['shell', 'ps', '|', 'grep', packageName]);
        
        if (!psResult.stdout.toString().contains(packageName)) {
          crashCount++;
          print('  ⚠️ App crash detected, restarting...');
          await Process.run('adb', ['shell', 'am', 'start', '-n', activityName]);
        }
        
        // Check for errors in logs
        final logResult = await Process.run('adb', ['logcat', '-d', '-s', 'AndroidRuntime:E']);
        if (logResult.stdout.toString().contains(packageName)) {
          errorCount++;
        }
        
        await Future.delayed(const Duration(seconds: 30));
        
        final elapsed = DateTime.now().difference(startTime);
        print('  ⏱️ Elapsed: ${elapsed.inMinutes}:${(elapsed.inSeconds % 60).toString().padLeft(2, '0')}');
      }
      
      if (crashCount == 0 && errorCount < 3) {
        print('  ✅ Stability test passed');
        _addResult('Stability Test', true, 'Crashes: $crashCount, Errors: $errorCount');
      } else {
        print('  ⚠️ Stability issues detected');
        _addResult('Stability Test', false, 'Crashes: $crashCount, Errors: $errorCount');
      }
      
    } catch (e) {
      print('  ❌ Stability test failed: $e');
      _addResult('Stability Test', false, e.toString());
    }
  }
  
  /// Test 8: Cleanup
  Future<void> _test8_CleanupTest() async {
    print('\n🧹 Test 8: Cleanup');
    
    try {
      // Stop app
      await Process.run('adb', ['shell', 'am', 'force-stop', packageName]);
      
      // Clear logs
      await Process.run('adb', ['logcat', '-c']);
      
      print('  ✅ Cleanup completed');
      _addResult('Cleanup', true, 'Environment cleaned');
      
    } catch (e) {
      print('  ❌ Cleanup failed: $e');
      _addResult('Cleanup', false, e.toString());
    }
  }
  
  // Helper methods
  
  Future<Map<String, String>> _getDeviceProperties() async {
    final props = <String, String>{};
    
    final modelResult = await Process.run('adb', ['shell', 'getprop', 'ro.product.model']);
    props['model'] = modelResult.stdout.toString().trim();
    
    final androidResult = await Process.run('adb', ['shell', 'getprop', 'ro.build.version.release']);
    props['android'] = androidResult.stdout.toString().trim();
    
    final memResult = await Process.run('adb', ['shell', 'cat', '/proc/meminfo']);
    final memMatch = RegExp(r'MemTotal:\s+(\d+)\s+kB').firstMatch(memResult.stdout.toString());
    props['ram'] = memMatch != null ? '${(int.parse(memMatch.group(1)!) / 1024 / 1024).toStringAsFixed(1)}GB' : 'Unknown';
    
    final storageResult = await Process.run('adb', ['shell', 'df', '/data']);
    props['storage'] = storageResult.stdout.toString().contains('/data') ? 'Available' : 'Unknown';
    
    return props;
  }
  
  bool _checkDeviceCompatibility(Map<String, String> props) {
    final model = props['model']?.toLowerCase() ?? '';
    final android = props['android'] ?? '';
    
    // Check if Telpo F8
    final isTelpoF8 = model.contains('telpo') && model.contains('f8');
    
    // Check Android version (should be 8.1+)
    final androidVersion = double.tryParse(android) ?? 0.0;
    final androidOK = androidVersion >= 8.1;
    
    return isTelpoF8 && androidOK;
  }
  
  void _addResult(String testName, bool passed, String details) {
    _results.add(TestResult(testName, passed, details));
  }
  
  void _printTestSummary() {
    print('\n📊 Test Summary');
    print('=' * 50);
    
    int passed = 0;
    int failed = 0;
    
    for (final result in _results) {
      final status = result.passed ? '✅' : '❌';
      print('$status ${result.testName}: ${result.details}');
      
      if (result.passed) {
        passed++;
      } else {
        failed++;
      }
    }
    
    print('\n📈 Results: $passed passed, $failed failed');
    
    if (failed == 0) {
      print('🎉 All tests passed! Telpo F8 integration is ready for production.');
    } else {
      print('⚠️ Some tests failed. Please review and fix issues before deployment.');
    }
  }
}

class TestResult {
  final String testName;
  final bool passed;
  final String details;
  
  TestResult(this.testName, this.passed, this.details);
}

import 'package:get_it/get_it.dart';
import '../../../core/network/api_client.dart';
import '../../../services/api_endpoints.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/role/member_role_model.dart';

abstract class MemberRoleRemoteDataSource {
  /// Get all member roles with pagination and optional filtering
  Future<List<MemberRoleModel>> getMemberRoles({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    bool? isActive,
  });

  /// Get member role by ID
  Future<MemberRoleModel> getMemberRoleById(String roleId);

  /// Get member roles by tenant ID
  Future<List<MemberRoleModel>> getMemberRolesByTenant(String tenantId);

  /// Create a new member role
  Future<MemberRoleModel> createMemberRole({
    required String name,
    String? description,
    String? tenantId,
    bool isActive = true,
  });

  /// Update member role
  Future<MemberRoleModel> updateMemberRole({
    required String roleId,
    String? name,
    String? description,
    bool? isActive,
  });

  /// Delete member role
  Future<void> deleteMemberRole(String roleId);
}

class MemberRoleRemoteDataSourceImpl implements MemberRoleRemoteDataSource {
  final ApiClient apiClient = GetIt.instance<ApiClient>();

  @override
  Future<List<MemberRoleModel>> getMemberRoles({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    bool? isActive,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'pageSize': limit.toString(),
        'pageIndex': page.toString(),
        if (sortBy != null) 'sortBy': sortBy,
        if (sortDirection != null) 'sortDirection': sortDirection,
        if (search != null) 'search': search,
        if (tenantId != null) 'tenant_id': tenantId,
        if (isActive != null) 'is_active': isActive.toString(),
      };

      final response = await apiClient.get(
        ApiEndpoints.roles,
        queryParameters: queryParameters,
      );

      if (response['success'] == true && response['data'] != null) {
        final rolesData = response['data']['items'] as List<dynamic>? ?? [];

        return rolesData
            .map((roleData) => MemberRoleModel.fromJson(roleData as Map<String, dynamic>))
            .toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get member roles';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to get member roles: $e');
    }
  }

  @override
  Future<MemberRoleModel> getMemberRoleById(String roleId) async {
    try {
      final response = await apiClient.get(ApiEndpoints.roleDetail(roleId));

      if (response['success'] == true && response['data'] != null) {
        return MemberRoleModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get member role';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to get member role: $e');
    }
  }

  @override
  Future<List<MemberRoleModel>> getMemberRolesByTenant(String tenantId) async {
    try {
      final response = await apiClient.get(
        ApiEndpoints.roles,
        queryParameters: {'tenant_id': tenantId, 'limit': '100'},
      );

      if (response['success'] == true && response['data'] != null) {
        final rolesData = response['data']['items'] as List<dynamic>? ?? [];

        return rolesData
            .map((roleData) => MemberRoleModel.fromJson(roleData as Map<String, dynamic>))
            .toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get member roles by tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to get member roles by tenant: $e');
    }
  }

  @override
  Future<MemberRoleModel> createMemberRole({
    required String name,
    String? description,
    String? tenantId,
    bool isActive = true,
  }) async {
    try {
      final body = <String, dynamic>{
        'name': name,
        if (description != null) 'description': description,
        if (tenantId != null) 'tenant_id': tenantId,
        'is_active': isActive,
      };

      final response = await apiClient.post(
        ApiEndpoints.createRole,
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        return MemberRoleModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to create member role';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to create member role: $e');
    }
  }

  @override
  Future<MemberRoleModel> updateMemberRole({
    required String roleId,
    String? name,
    String? description,
    bool? isActive,
  }) async {
    try {
      final body = <String, dynamic>{
        if (name != null) 'name': name,
        if (description != null) 'description': description,
        if (isActive != null) 'is_active': isActive,
      };

      final response = await apiClient.put(
        ApiEndpoints.updateRole(roleId),
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        return MemberRoleModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update member role';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to update member role: $e');
    }
  }

  @override
  Future<void> deleteMemberRole(String roleId) async {
    try {
      final response = await apiClient.delete(ApiEndpoints.deleteRole(roleId));

      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Failed to delete member role';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException('Failed to delete member role: $e');
    }
  }
}

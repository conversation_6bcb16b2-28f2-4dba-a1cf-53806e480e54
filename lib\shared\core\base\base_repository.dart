import 'package:dartz/dartz.dart';
import '../errors/failures.dart';
import '../errors/exceptions.dart';
import '../network/network_info.dart';

/// Base class cho tất cả các Repository trong ứng dụng
abstract class BaseRepository {
  final NetworkInfo networkInfo;

  BaseRepository({required this.networkInfo});

  /// Execute operation với automatic error handling
  Future<Either<Failure, T>> executeOperation<T>(
    Future<T> Function() operation, {
    bool requiresNetwork = true,
  }) async {
    try {
      // Check network connectivity if required
      if (requiresNetwork && !await networkInfo.isConnected) {
        return Left(NetworkFailure('No internet connection'));
      }

      final result = await operation();
      return Right(result);
    } catch (error) {
      return Left(_mapExceptionToFailure(error));
    }
  }

  /// Map exception to appropriate failure
  Failure _mapExceptionToFailure(dynamic error) {
    if (error is NetworkException) {
      return NetworkFailure(error.message);
    } else if (error is ServerException) {
      return ServerFailure(error.message);
    } else if (error is AuthException) {
      return AuthFailure(error.message);
    } else if (error is ValidationException) {
      return ValidationFailure(error.message, fieldErrors: error.fieldErrors);
    } else if (error is CacheException) {
      return CacheFailure(error.message);
    } else {
      return ServerFailure('Unknown error: ${error.toString()}');
    }
  }
}

/// Paginated result wrapper
class PaginatedResult<T> {
  final List<T> data;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNext;
  final bool hasPrevious;

  const PaginatedResult({
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNext,
    required this.hasPrevious,
  });
}

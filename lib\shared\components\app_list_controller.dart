import 'package:flutter/foundation.dart';

/// Controller để quản lý state cho AppListView
class AppListController<T> extends ChangeNotifier {
  List<T> _items = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize;

  AppListController({int pageSize = 20}) : _pageSize = pageSize;

  // Getters
  List<T> get items => List.unmodifiable(_items);
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;
  int get pageSize => _pageSize;
  bool get isEmpty => _items.isEmpty;
  int get length => _items.length;

  /// Load initial data
  Future<void> loadData(Future<List<T>> Function(int page, int pageSize) loadFunction) async {
    if (_isLoading) return;

    _setLoading(true);
    _setError(null);
    _currentPage = 1;

    try {
      final newItems = await loadFunction(_currentPage, _pageSize);
      _items = newItems;
      _hasMoreData = newItems.length >= _pageSize;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Load more data (infinite scroll)
  Future<void> loadMore(Future<List<T>> Function(int page, int pageSize) loadFunction) async {
    if (_isLoadingMore || !_hasMoreData || _isLoading) return;

    _setLoadingMore(true);

    try {
      final nextPage = _currentPage + 1;
      final newItems = await loadFunction(nextPage, _pageSize);
      
      if (newItems.isNotEmpty) {
        _items.addAll(newItems);
        _currentPage = nextPage;
        _hasMoreData = newItems.length >= _pageSize;
      } else {
        _hasMoreData = false;
      }
      
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoadingMore(false);
    }
  }

  /// Refresh data (pull to refresh)
  Future<void> refresh(Future<List<T>> Function(int page, int pageSize) loadFunction) async {
    _currentPage = 1;
    _hasMoreData = true;
    _setError(null);
    _setLoading(true); // Set loading state for refresh

    try {
      final newItems = await loadFunction(_currentPage, _pageSize);
      _items = newItems;
      _hasMoreData = newItems.length >= _pageSize;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false); // Always clear loading state
    }
  }

  /// Add item to list
  void addItem(T item) {
    _items.add(item);
    notifyListeners();
  }

  /// Insert item at index
  void insertItem(int index, T item) {
    _items.insert(index, item);
    notifyListeners();
  }

  /// Update item at index
  void updateItem(int index, T item) {
    if (index >= 0 && index < _items.length) {
      _items[index] = item;
      notifyListeners();
    }
  }

  /// Remove item at index
  void removeItem(int index) {
    if (index >= 0 && index < _items.length) {
      _items.removeAt(index);
      notifyListeners();
    }
  }

  /// Remove item by value
  void removeItemByValue(T item) {
    _items.remove(item);
    notifyListeners();
  }

  /// Clear all items
  void clear() {
    _items.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _setError(null);
    notifyListeners();
  }

  /// Find item by predicate
  T? findItem(bool Function(T item) predicate) {
    try {
      return _items.firstWhere(predicate);
    } catch (e) {
      return null;
    }
  }

  /// Find item index by predicate
  int findItemIndex(bool Function(T item) predicate) {
    for (int i = 0; i < _items.length; i++) {
      if (predicate(_items[i])) {
        return i;
      }
    }
    return -1;
  }

  /// Filter items
  List<T> filterItems(bool Function(T item) predicate) {
    return _items.where(predicate).toList();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set loading more state
  void _setLoadingMore(bool loadingMore) {
    _isLoadingMore = loadingMore;
    notifyListeners();
  }

  /// Set error
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Force clear loading state
  void clearLoadingState() {
    _isLoading = false;
    _isLoadingMore = false;
    notifyListeners();
  }

  /// Silent refresh without loading state (for background updates)
  Future<void> silentRefresh(Future<List<T>> Function(int page, int pageSize) loadFunction) async {
    _currentPage = 1;
    _hasMoreData = true;
    _setError(null);

    try {
      final newItems = await loadFunction(_currentPage, _pageSize);
      _items = newItems;
      _hasMoreData = newItems.length >= _pageSize;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  /// Reset controller
  void reset() {
    _items.clear();
    _isLoading = false;
    _isLoadingMore = false;
    _error = null;
    _hasMoreData = true;
    _currentPage = 1;
    notifyListeners();
  }

  @override
  void dispose() {
    _items.clear();
    super.dispose();
  }
}

import 'package:dartz/dartz.dart';
import '../entities/auth/auth_result.dart';
import '../entities/user/user.dart';
import '../../core/errors/failures.dart';

abstract class AuthRepository {
  /// Login with username and password
  Future<Either<Failure, AuthResult>> login({
    required String userName,
    required String password,
  });

  /// Logout current user
  Future<Either<Failure, void>> logout();

  /// Logout from all devices/sessions
  Future<Either<Failure, void>> logoutAll();

  /// Refresh access token
  Future<Either<Failure, AuthResult>> refreshToken(String refreshToken);

  /// Get current user profile
  Future<Either<Failure, User>> getCurrentUser();

  /// Verify if current token is valid
  Future<Either<Failure, bool>> verifyToken();

  /// Update current user profile
  Future<Either<Failure, User>> updateProfile({
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
  });

  /// Change user password
  Future<Either<Failure, bool>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  });

  /// Get all active sessions for current user
  Future<Either<Failure, List<Map<String, dynamic>>>> getSessions();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Get stored access token
  Future<String?> getAccessToken();

  /// Get stored refresh token
  Future<String?> getRefreshToken();

  /// Clear all stored authentication data
  Future<void> clearAuthData();
}

# Flutter Flavors Configuration

Dự án này được cấu hình để hỗ trợ 2 flavors (build variants):
- **Mobile**: Ứng dụng di động với đầy đủ tính năng
- **Terminal**: Ứng dụng terminal/kiosk với giao diện tối giản

## Cấu trúc thư mục

```
lib/
├── apps/
│   ├── mobile/
│   │   ├── main.dart                    # Entry point cho mobile app
│   │   └── presentation/
│   │       └── screens/                 # Mobile screens (moved từ lib/presentation/screens)
│   └── terminal/
│       ├── main.dart                    # Entry point cho terminal app
│       └── presentation/
│           └── main.dart                # Terminal main screen (đơn giản)
├── core/
│   ├── config/
│   │   ├── flavor_config.dart           # Flavor management
│   │   └── app_config.dart              # Configuration cho từng flavor
│   ├── constants/                       # Shared constants
│   ├── di/                             # Dependency injection
│   ├── error/                          # Error handling
│   ├── network/                        # Network layer
│   └── utils/                          # Utilities
├── data/                               # Shared data layer
├── domain/                             # Shared business logic
├── presentation/                       # Shared providers & widgets only
├── shared/                             # Shared components
└── routes/                             # Shared routing
```

## Cách chạy ứng dụng

### Mobile App
```bash
# Debug mode
flutter run --flavor mobile --target lib/apps/mobile/main.dart

# Release mode
flutter build apk --flavor mobile --target lib/apps/mobile/main.dart
```

### Terminal App
```bash
# Debug mode
flutter run --flavor terminal --target lib/apps/terminal/main.dart

# Release mode
flutter build apk --flavor terminal --target lib/apps/terminal/main.dart
```

### Sử dụng scripts có sẵn
```bash
# Chạy mobile app
scripts/run_mobile.bat

# Chạy terminal app
scripts/run_terminal.bat

# Build mobile app
scripts/build_mobile.bat

# Build terminal app
scripts/build_terminal.bat
```

## Khác biệt giữa Mobile và Terminal

### Mobile App
- **Navigation**: Sử dụng bottom tab bar với 4 tabs
- **Features**: Đầy đủ tính năng (biometric auth, push notifications, offline mode)
- **UI**: Responsive design cho mobile
- **Orientation**: Portrait và landscape
- **App ID**: com.ccam.mobile

### Terminal App
- **Navigation**: Không có tab bar, sử dụng mobile dashboard với header đơn giản
- **Features**: Tập trung vào face recognition và kiosk mode
- **UI**: Fullscreen mode, ẩn system UI
- **Orientation**: Chỉ landscape
- **App ID**: com.ccam.terminal
- **Screens**: Sử dụng mobile screens với layout tối giản

## Configuration

### Flavor Config
File `lib/core/config/flavor_config.dart` quản lý:
- Flavor hiện tại (mobile/terminal)
- App name và ID
- Debug mode
- Custom values cho từng flavor

### App Config
File `lib/core/config/app_config.dart` chứa:
- API endpoints khác nhau
- Feature flags
- UI configurations
- Network settings

## Shared Code

Tất cả code trong các thư mục sau được chia sẻ giữa 2 apps:
- `lib/core/` - Core utilities và configurations
- `lib/data/` - Data layer (models, repositories, services)
- `lib/domain/` - Business logic
- `lib/shared/` - Shared UI components
- `lib/presentation/` - Shared providers và widgets (screens đã move vào mobile app)

**Lưu ý**: Tất cả screens hiện tại đã được move vào `lib/apps/mobile/presentation/screens/` vì được thiết kế cho mobile. Terminal app sử dụng lại các screens này với layout đơn giản.

## Flavor-Aware Components

Sử dụng `FlavorAwareWidget` để hiển thị UI khác nhau:

```dart
FlavorAwareWidget(
  mobileWidget: MobileSpecificWidget(),
  terminalWidget: TerminalSpecificWidget(),
  defaultWidget: DefaultWidget(),
)
```

Hoặc sử dụng `FlavorAwareMixin`:

```dart
class MyWidget extends StatelessWidget with FlavorAwareMixin {
  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return MobileLayout();
    } else if (isTerminal) {
      return TerminalLayout();
    }
    return DefaultLayout();
  }
}
```

## Android Configuration

File `android/app/build.gradle.kts` đã được cấu hình với:
- Product flavors cho mobile và terminal
- Khác nhau về application ID
- Build types (debug/release)

## Lưu ý

1. **Shared Components**: Tất cả components trong `lib/shared/` có thể được sử dụng bởi cả 2 apps
2. **Feature Flags**: Sử dụng `AppConfig.isFeatureEnabled()` để kiểm tra tính năng
3. **Navigation**: Mobile app sử dụng `MainScreen`, Terminal app sử dụng `TerminalMainScreen`
4. **Testing**: Cần test cả 2 flavors để đảm bảo hoạt động đúng
5. **Assets**: Assets được chia sẻ giữa 2 apps, có thể customize nếu cần

## Thêm Flavor mới

Để thêm flavor mới:
1. Thêm enum value vào `Flavor` trong `flavor_config.dart`
2. Thêm configuration vào `app_config.dart`
3. Tạo thư mục mới trong `lib/apps/`
4. Cập nhật `android/app/build.gradle.kts`
5. Tạo scripts build/run mới

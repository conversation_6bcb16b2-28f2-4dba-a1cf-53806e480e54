import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'api_endpoints.dart';
import 'flutter_secure_storage.dart';
import 'secure_storage_keys.dart';
import 'cookie_service.dart';

/// Web-compatible HTTP client service using http package
/// Bypasses CORS issues by using browser's native XMLHttpRequest
class WebHttpClientService {
  static WebHttpClientService? _instance;
  late String _baseUrl;
  final SecureStorageService _secureStorage = SecureStorageService();
  
  WebHttpClientService._internal();
  
  factory WebHttpClientService() {
    _instance ??= WebHttpClientService._internal();
    return _instance!;
  }
  
  void initialize(String baseUrl) {
    _baseUrl = baseUrl;
    print('\n🌐 ===== WEB HTTP CLIENT INITIALIZED =====');
    print('📍 Base URL: $_baseUrl');
    print('🔧 Using http package for better web compatibility');
    print('==========================================\n');
  }
  
  /// POST request với detailed logging
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    final url = '$_baseUrl$endpoint';
    final requestHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'C-Face-Web/1.0.0 (Flutter)',
      'X-App-Version': '1.0.0',
      'X-Platform': 'flutter-web',
      ...?headers,
    };
    
    // Add auth token if available
    final token = await _secureStorage.read(key: SecureStorageKeys.accessToken);
    if (token != null) {
      requestHeaders['Authorization'] = 'Bearer $token';
    }

    // Add cookies if available
    await _addCookieHeader(requestHeaders);
    
    final requestBody = body != null ? jsonEncode(body) : null;
    
    // Log request
    print('\n🚀 ===== WEB HTTP REQUEST =====');
    print('📍 URL: POST $url');
    print('📋 Headers: $requestHeaders');
    if (requestBody != null) {
      print('📦 Body: $requestBody');
    }
    print('⏰ Time: ${DateTime.now().toIso8601String()}');
    print('==============================\n');
    
    try {
      final response = await http.post(
        Uri.parse(url),
        headers: requestHeaders,
        body: requestBody,
      ).timeout(const Duration(seconds: 60));
      
      // Log response
      print('\n✅ ===== WEB HTTP RESPONSE =====');
      print('📊 Status: ${response.statusCode} ${response.reasonPhrase}');
      print('📋 Headers: ${response.headers}');
      print('📦 Body: ${response.body}');
      print('⏰ Time: ${DateTime.now().toIso8601String()}');
      print('===============================\n');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw HttpException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      // Log error
      print('\n❌ ===== WEB HTTP ERROR =====');
      print('📍 URL: POST $url');
      print('🚨 Error: $e');
      print('⏰ Time: ${DateTime.now().toIso8601String()}');
      print('=============================\n');
      
      if (e is HttpException) {
        rethrow;
      } else {
        throw HttpException('Network error: $e', 0, e.toString());
      }
    }
  }
  
  /// GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    final url = '$_baseUrl$endpoint';
    final requestHeaders = {
      'Accept': 'application/json',
      'User-Agent': 'C-Face-Web/1.0.0 (Flutter)',
      'X-App-Version': '1.0.0',
      'X-Platform': 'flutter-web',
      ...?headers,
    };
    
    // Add auth token if available
    final token = await _secureStorage.read(key: SecureStorageKeys.accessToken);
    if (token != null) {
      requestHeaders['Authorization'] = 'Bearer $token';
    }

    // Add cookies if available
    await _addCookieHeader(requestHeaders);
    
    print('\n🚀 ===== WEB HTTP GET REQUEST =====');
    print('📍 URL: GET $url');
    print('📋 Headers: $requestHeaders');
    print('⏰ Time: ${DateTime.now().toIso8601String()}');
    print('==================================\n');
    
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: requestHeaders,
      ).timeout(const Duration(seconds: 60));
      
      print('\n✅ ===== WEB HTTP GET RESPONSE =====');
      print('📊 Status: ${response.statusCode} ${response.reasonPhrase}');
      print('📦 Body: ${response.body}');
      print('⏰ Time: ${DateTime.now().toIso8601String()}');
      print('===================================\n');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw HttpException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          response.statusCode,
          response.body,
        );
      }
    } catch (e) {
      print('\n❌ ===== WEB HTTP GET ERROR =====');
      print('📍 URL: GET $url');
      print('🚨 Error: $e');
      print('⏰ Time: ${DateTime.now().toIso8601String()}');
      print('=================================\n');
      
      if (e is HttpException) {
        rethrow;
      } else {
        throw HttpException('Network error: $e', 0, e.toString());
      }
    }
  }

  /// Add Cookie header from CookieService
  Future<void> _addCookieHeader(Map<String, String> headers) async {
    try {
      final uri = Uri.parse(_baseUrl);
      final cookies = await CookieService().getCookiesForDomain(uri.host);

      if (cookies.isNotEmpty) {
        final cookieString = cookies.map((cookie) => '${cookie.name}=${cookie.value}').join('; ');
        headers['Cookie'] = cookieString;

        if (kDebugMode) {
          print('🍪 Added Cookie header: $cookieString');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔴 Failed to add Cookie header: $e');
      }
      // Don't throw error - cookie header is not critical
    }
  }
}

/// Custom HTTP Exception
class HttpException implements Exception {
  final String message;
  final int statusCode;
  final String body;
  
  HttpException(this.message, this.statusCode, this.body);
  
  @override
  String toString() => 'HttpException: $message';
}

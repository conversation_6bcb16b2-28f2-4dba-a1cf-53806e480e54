#!/usr/bin/env python3
"""
One-click setup script for face recognition models.
This script replaces mock models with real, working models.
"""

import os
import sys
import subprocess
from pathlib import Path
import argparse

def run_command(cmd, description=""):
    """Run a command and return success status."""
    try:
        if description:
            print(f"\n{description}")
            print("-" * 50)
        
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def check_python():
    """Check Python version."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"✗ Python 3.7+ required. Current: {version.major}.{version.minor}")
        return False
    
    print(f"✓ Python {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """Install required packages."""
    print("\nInstalling required packages...")
    print("-" * 50)
    
    # Try to install requests if not available
    try:
        import requests
        print("✓ requests already installed")
        return True
    except ImportError:
        print("Installing requests...")
        return run_command("pip install requests")

def main():
    parser = argparse.ArgumentParser(description="Setup real face recognition models")
    parser.add_argument("--check-only", action="store_true", help="Only check current models")
    parser.add_argument("--force", action="store_true", help="Force download even if models exist")
    
    args = parser.parse_args()
    
    print("Face Recognition Model Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python():
        sys.exit(1)
    
    # Get script directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print(f"Project root: {project_root}")
    
    # Check current models
    check_cmd = f"python {script_dir}/check_models.py"
    print("\nChecking current models...")
    print("-" * 50)
    
    # Run check (this will show current status)
    subprocess.run(check_cmd, shell=True)
    
    if args.check_only:
        return
    
    # Install requirements
    if not install_requirements():
        print("✗ Failed to install requirements")
        sys.exit(1)
    
    # Download real models
    download_cmd = f"python {script_dir}/download_real_models.py"
    if args.force:
        download_cmd += " --force"
    
    if not run_command(download_cmd, "Downloading real models..."):
        print("✗ Failed to download models")
        sys.exit(1)
    
    # Final check
    print("\nFinal verification...")
    print("-" * 50)
    
    final_check = subprocess.run(check_cmd, shell=True)
    
    if final_check.returncode == 0:
        print("\n" + "=" * 60)
        print("✓ SUCCESS! Face recognition models are ready!")
        print("=" * 60)
        print("\nModel summary:")
        print("- ultraface_320.tflite: ~224 KB (BlazeFace face detection)")
        print("- mobilefacenet.tflite: ~3.6 MB (Face landmarker)")  
        print("- mediapipe_face.tflite: ~224 KB (BlazeFace face detection)")
        print("\nYour Flutter app should now work with real face recognition!")
    else:
        print("\n✗ Setup completed but some models may have issues")
        print("Please check the error messages above")
        sys.exit(1)

if __name__ == "__main__":
    main()

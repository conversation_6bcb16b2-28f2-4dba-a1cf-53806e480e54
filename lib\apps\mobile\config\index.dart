/// Mobile Configuration Index
/// 
/// This file exports all mobile-specific configuration components.
/// It provides easy access to mobile app configuration, settings,
/// and utilities specific to the mobile application.
/// 
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/mobile/config/index.dart';
/// 
/// // Initialize mobile configuration
/// AppConfigFactory.initialize(AppType.mobile);
/// 
/// // Get mobile configuration
/// final config = AppConfigFactory.current as MobileAppConfig;
/// 
/// // Access mobile-specific settings
/// final enableHaptic = config.enableHapticFeedback;
/// final uiConfig = config.uiConfig;
/// final features = config.featureFlags;
/// ```
library;

// ============================================================================
// MOBILE CONFIGURATION
// ============================================================================
export 'mobile_app_config.dart';

// ============================================================================
// SHARED CONFIGURATION (RE-EXPORT FOR CONVENIENCE)
// ============================================================================
export '../../../shared/core/config/base_app_config.dart';
export '../../../shared/core/config/app_config_factory.dart';

// ============================================================================
// MOBILE CONFIGURATION UTILITIES
// ============================================================================

// Import required classes for utility functions
import 'mobile_app_config.dart';
import '../../../shared/core/config/app_config_factory.dart';
import '../../../shared/core/config/base_app_config.dart';

/// Quick setup function for mobile configuration
void setupMobileConfig() {
  AppConfigFactory.initialize(AppType.mobile);
}

/// Get mobile configuration instance
MobileAppConfig getMobileConfig() {
  final config = AppConfigFactory.current;
  if (config is! MobileAppConfig) {
    throw StateError('Current configuration is not MobileAppConfig');
  }
  return config;
}

/// Validate mobile configuration
bool validateMobileConfig() {
  try {
    final config = getMobileConfig();
    return config.validateConfig();
  } catch (e) {
    return false;
  }
}

/// Get mobile configuration summary
Map<String, dynamic> getMobileConfigSummary() {
  try {
    final config = getMobileConfig();
    return {
      'appName': config.appName,
      'appVersion': config.appVersion,
      'appType': config.appType.name,
      'environment': config.environment.name,
      'isValid': config.validateConfig(),
      'features': config.featureFlags.toMap(),
      'ui': config.uiConfig.toMap(),
      'camera': config.cameraConfig.toMap(),
    };
  } catch (e) {
    return {'error': e.toString()};
  }
}

/// Check if mobile configuration is initialized
bool isMobileConfigInitialized() {
  return AppConfigFactory.isInitialized && 
         AppConfigFactory.currentAppType == AppType.mobile;
}

/// Reset mobile configuration (useful for testing)
void resetMobileConfig() {
  AppConfigFactory.reset();
}

/// Switch to mobile configuration from another app type
void switchToMobileConfig() {
  AppConfigFactory.switchToAppType(AppType.mobile);
}

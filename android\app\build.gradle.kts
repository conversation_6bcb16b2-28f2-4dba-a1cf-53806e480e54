plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.ccam.face_terminal"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // Default configuration - sẽ được override bởi flavors
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // Cấu hình flavors cho mobile và terminal
    flavorDimensions += "app"
    productFlavors {
        create("mobile") {
            dimension = "app"
            applicationId = "com.ccam.mobile.temp"
            versionNameSuffix = "-mobile"
            resValue("string", "app_name", "C-CAM Mobile Temp")
        }
        create("terminal") {
            dimension = "app"
            applicationId = "com.ccam.terminal"
            versionNameSuffix = "-terminal"
            resValue("string", "app_name", "C-CAM Terminal Debug")
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            applicationIdSuffix = ".debug"
            // Add debug suffix to app name to distinguish between flavors
            resValue("string", "app_name_suffix", " (Debug)")
        }
        release {
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
            resValue("string", "app_name_suffix", "")
        }
    }
}

flutter {
    source = "../.."

    // Cấu hình target cho từng flavor
    target = project.findProperty("target")?.toString() ?: "lib/main.dart"
}

# Relay Controller

A Flutter library for controlling relays through various communication methods including Bluetooth, HTTP, MQTT, and USB Serial.

## Features

- **Bluetooth Classic**: Control relays via Bluetooth using modern packages (`bluetooth_classic`, `flutter_blue_classic`)
- **HTTP**: Send HTTP requests to control relays over network
- **MQTT**: Publish messages to MQTT broker for relay control
- **USB Serial**: Control relays through USB OTG using `usb_serial`
- **Secure HTTP**: JWT authentication with HMAC request signing
- **Unified Interface**: All controllers implement the same `RelayController` interface
- **Device Registration**: Automatic device provisioning and management
- **Security Features**: JWT tokens, HMAC signatures, replay attack prevention
- **Error Handling**: Comprehensive exception handling for each communication method
- **Flexible Configuration**: Customizable commands, timeouts, and connection parameters

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  relay_controller: ^1.0.0
```

## Usage

### Basic Interface

All relay controllers implement the same basic interface:

```dart
import 'package:relay_controller/relay_controller.dart';

// Any controller type
RelayController controller = /* ... */;

// Turn relay ON
await controller.triggerOn();

// Turn relay OFF
await controller.triggerOff();

// Clean up resources
await controller.dispose();
```

### HTTP Relay Controller

```dart
import 'package:relay_controller/relay_controller.dart';

// Basic HTTP controller
final controller = HttpRelayController(
  deviceId: 'relay-001',
  deviceName: 'Living Room Relay',
  urlOn: 'http://*************/relay/on',
  urlOff: 'http://*************/relay/off',
);

// Register device with server (optional)
await controller.registerDevice();

// Control relay
await controller.triggerOn();
await controller.triggerOff();

// Get current status
final status = await controller.getStatus(); // true/false/null

await controller.dispose();
```

### Secure HTTP Relay Controller

For production environments requiring high security:

```dart
import 'package:relay_controller/relay_controller.dart';

// Secure controller with JWT authentication and HMAC signing
final secureController = SecureHttpRelayController(
  deviceId: 'secure-relay-001',
  deviceType: 'flutter_app',
  serverBaseUrl: 'https://api.example.com',
  deviceName: 'Production Relay',
  hardwareHash: 'device-fingerprint-hash', // optional
  appVersion: '1.0.0', // optional
);

// Register device and get credentials (JWT + secret key)
await secureController.registerDevice();

// Control relay with automatic JWT auth and HMAC signing
await secureController.triggerOn();   // Sends signed request
await secureController.triggerOff();  // Sends signed request

// Get current status
final status = await secureController.getStatus();

// Check authentication status
if (secureController.isAuthenticated) {
  print('Device is authenticated');
  print('Token expires: ${secureController.tokenExpiry}');
  print('Device scope: ${secureController.deviceScope}');
}

// Revoke credentials when done
await secureController.revokeCredentials();
await secureController.dispose();
```

#### Other HTTP Controllers

```dart
// Simplified HTTP controller
final simpleController = SimpleHttpRelayController(
  deviceId: 'relay-002',
  baseUrl: 'http://*************',
  onEndpoint: '/on',
  offEndpoint: '/off',
);

// HTTP with authentication
final authController = AuthenticatedHttpRelayController(
  deviceId: 'relay-003',
  urlOn: 'http://*************/relay/on',
  urlOff: 'http://*************/relay/off',
  username: 'admin',
  password: 'password',
);
```

### Bluetooth Relay Controller

```dart
import 'package:relay_controller/relay_controller.dart';

final controller = BluetoothRelayController(
  deviceAddress: '00:11:22:33:44:55',
);

// Connect to device
await controller.connect();

// Control relay
await controller.triggerOn();
await controller.triggerOff();

// Disconnect and cleanup
await controller.dispose();

// Check available devices
final devices = await BluetoothRelayController.getAvailableDevices();
for (var device in devices) {
  print('Device: ${device.name} - ${device.address}');
}
```

### MQTT Relay Controller

```dart
import 'package:relay_controller/relay_controller.dart';

final controller = MqttRelayController(
  brokerHost: 'mqtt.example.com',
  brokerPort: 1883,
  topic: 'home/relay/control',
  username: 'mqtt_user',  // optional
  password: 'mqtt_pass',  // optional
);

// Connect to MQTT broker
await controller.connect();

// Control relay
await controller.triggerOn();   // Publishes "ON" to topic
await controller.triggerOff();  // Publishes "OFF" to topic

// Disconnect and cleanup
await controller.dispose();
```

### USB Serial Relay Controller

```dart
import 'package:relay_controller/relay_controller.dart';

final controller = UsbRelayController();

// Get available USB devices
final devices = await UsbRelayController.getAvailableDevices();

if (devices.isNotEmpty) {
  // Connect to first device
  await controller.connect(devices.first);
  
  // Control relay
  await controller.triggerOn();   // Sends "ON\n"
  await controller.triggerOff();  // Sends "OFF\n"
  
  // Cleanup
  await controller.dispose();
}

// Auto-connect version
final autoController = AutoConnectUsbRelayController();
await autoController.initialize();  // Automatically connects to first device
await autoController.triggerOn();
await autoController.dispose();
```

## Customization

### Custom Commands

```dart
// Bluetooth with custom commands
final bluetoothController = BluetoothRelayController(
  deviceAddress: '00:11:22:33:44:55',
  onCommand: 'RELAY_ON\r\n',
  offCommand: 'RELAY_OFF\r\n',
);

// USB with custom commands
final usbController = UsbRelayController(
  onCommand: '1',
  offCommand: '0',
  baudRate: 115200,
);

// MQTT with custom messages
final mqttController = MqttRelayController(
  brokerHost: 'mqtt.example.com',
  topic: 'devices/relay1/command',
  onMessage: 'ACTIVATE',
  offMessage: 'DEACTIVATE',
);
```

### HTTP Methods and Bodies

```dart
// POST with JSON body
final httpController = HttpRelayController(
  urlOn: 'http://*************/api/relay',
  urlOff: 'http://*************/api/relay',
  method: HttpMethod.post,
  bodyOn: '{"state": "on"}',
  bodyOff: '{"state": "off"}',
  headers: {'Content-Type': 'application/json'},
);
```

## Error Handling

```dart
try {
  await controller.triggerOn();
} on BluetoothRelayException catch (e) {
  print('Bluetooth error: $e');
} on HttpRelayException catch (e) {
  print('HTTP error: $e');
} on MqttRelayException catch (e) {
  print('MQTT error: $e');
} on UsbRelayException catch (e) {
  print('USB error: $e');
} on RelayControllerException catch (e) {
  print('General relay error: $e');
}
```

## Permissions

### Android Permissions

Add these permissions to your `android/app/src/main/AndroidManifest.xml`:

```xml
<!-- Bluetooth permissions -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Internet permission for HTTP and MQTT -->
<uses-permission android:name="android.permission.INTERNET" />

<!-- USB Host permission -->
<uses-permission android:name="android.permission.USB_PERMISSION" />
<uses-feature android:name="android.hardware.usb.host" />
```

## Test Server

A comprehensive test server is included to help you test and develop with the relay controller library. The server supports HTTP, WebSocket, and MQTT protocols.

### Quick Start

```bash
cd test_server
npm install
npm start
```

Then open `http://localhost:3000` for the web dashboard.

### Features

- **Real-time Web Dashboard**: Monitor and control devices
- **HTTP API**: RESTful endpoints for all operations
- **WebSocket Support**: Real-time bidirectional communication
- **MQTT Integration**: Optional MQTT broker support
- **Device Registration**: Automatic device discovery
- **Activity Logging**: Complete audit trail

### API Endpoints

```bash
# Register device
POST /register

# Control relay
GET /relay/on?deviceId=xxx
GET /relay/off?deviceId=xxx
GET /relay/status?deviceId=xxx

# Device-specific endpoints
GET /devices/:deviceId/on
GET /devices/:deviceId/off
GET /devices/:deviceId/status
```

### Testing with Flutter

```dart
final controller = HttpRelayController(
  deviceId: 'test-relay-001',
  deviceName: 'Test Relay',
  urlOn: 'http://localhost:3000/relay/on?deviceId=test-relay-001',
  urlOff: 'http://localhost:3000/relay/off?deviceId=test-relay-001',
);

// Register and test
await controller.registerDevice();
await controller.triggerOn();
final status = await controller.getStatus();
```

See `test_server/README.md` for complete documentation.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

import 'package:flutter/foundation.dart';
import '../presentation/providers/base/base_auth_provider.dart';

/// Service to coordinate token refresh between HttpClientService and AuthProvider
class TokenRefreshService {
  static TokenRefreshService? _instance;
  static TokenRefreshService get instance => _instance ??= TokenRefreshService._();
  
  TokenRefreshService._();
  
  BaseAuthProvider? _authProvider;
  
  /// Set the auth provider for token refresh coordination
  void setAuthProvider(BaseAuthProvider authProvider) {
    _authProvider = authProvider;
  }
  
  /// Refresh token using the auth provider
  Future<bool> refreshToken() async {
    if (_authProvider == null) {
      if (kDebugMode) {
        print('TokenRefreshService: No auth provider set');
      }
      return false;
    }
    
    try {
      return await _authProvider!.refreshAuthToken();
    } catch (e) {
      if (kDebugMode) {
        print('TokenRefreshService: Token refresh failed: $e');
      }
      return false;
    }
  }
  
  /// Get current access token from auth provider
  String? get accessToken => _authProvider?.accessToken;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _authProvider?.isAuthenticated ?? false;
  
  /// Clear the auth provider reference
  void clear() {
    _authProvider = null;
  }
}

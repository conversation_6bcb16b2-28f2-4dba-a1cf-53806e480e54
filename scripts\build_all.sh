#!/bin/bash

echo "🏗️  Building All C-Face Apps..."
echo "==============================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build mobile app
echo ""
echo "📱 Building Mobile App..."
echo "========================"
flutter build apk --target lib/apps/mobile/main_mobile.dart --release

if [ $? -eq 0 ]; then
    echo "✅ Mobile app build completed!"
    # Rename mobile APK
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        cp build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/c-face-mobile-release.apk
        MOBILE_SIZE=$(du -h build/app/outputs/flutter-apk/c-face-mobile-release.apk | cut -f1)
        echo "📱 Mobile APK: build/app/outputs/flutter-apk/c-face-mobile-release.apk ($MOBILE_SIZE)"
    fi
else
    echo "❌ Mobile app build failed!"
    exit 1
fi

# Build terminal app
echo ""
echo "🖥️  Building Terminal App..."
echo "============================"
flutter build apk --target lib/apps/terminal/main_terminal.dart --release

if [ $? -eq 0 ]; then
    echo "✅ Terminal app build completed!"
    # Rename terminal APK
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        cp build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/c-face-terminal-release.apk
        TERMINAL_SIZE=$(du -h build/app/outputs/flutter-apk/c-face-terminal-release.apk | cut -f1)
        echo "🖥️  Terminal APK: build/app/outputs/flutter-apk/c-face-terminal-release.apk ($TERMINAL_SIZE)"
    fi
else
    echo "❌ Terminal app build failed!"
    exit 1
fi

# Summary
echo ""
echo "🎉 All builds completed successfully!"
echo "====================================="
echo "📱 Mobile APK:   build/app/outputs/flutter-apk/c-face-mobile-release.apk"
echo "🖥️  Terminal APK: build/app/outputs/flutter-apk/c-face-terminal-release.apk"
echo ""
echo "📊 Build Summary:"
if [ -f "build/app/outputs/flutter-apk/c-face-mobile-release.apk" ]; then
    echo "   Mobile size:   $MOBILE_SIZE"
fi
if [ -f "build/app/outputs/flutter-apk/c-face-terminal-release.apk" ]; then
    echo "   Terminal size: $TERMINAL_SIZE"
fi
echo ""
echo "🚀 Ready for deployment!"

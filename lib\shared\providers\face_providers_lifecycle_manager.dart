import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'face_capture_provider.dart';
import 'face_detection_provider.dart';
import '../models/camera_config.dart';

/// Enum để track state của lifecycle manager
enum FaceProvidersLifecycleState {
  initial,
  initializing,
  ready,
  disposing,
  disposed,
  error,
}

/// Callback types cho lifecycle events
typedef LifecycleCallback = void Function();
typedef LifecycleErrorCallback = void Function(String error);

/// Manager để quản lý lifecycle của FaceCaptureProvider và FaceDetectionProvider
/// 
/// Đảm bảo:
/// - Lazy initialization: providers chỉ được tạo khi cần
/// - Reference counting: track số lượng widgets đang sử dụng
/// - Safe disposal: cleanup đúng thứ tự và handle errors
/// - Concurrent access: prevent race conditions
class FaceProvidersLifecycleManager extends ChangeNotifier {
  static FaceProvidersLifecycleManager? _instance;
  
  // Singleton pattern để đảm bảo chỉ có 1 instance
  static FaceProvidersLifecycleManager get instance {
    _instance ??= FaceProvidersLifecycleManager._internal();
    return _instance!;
  }
  
  FaceProvidersLifecycleManager._internal();
  
  // State management
  FaceProvidersLifecycleState _state = FaceProvidersLifecycleState.initial;
  String? _errorMessage;
  
  // Providers
  FaceCaptureProvider? _faceCaptureProvider;
  FaceDetectionProvider? _faceDetectionProvider;
  
  // Reference counting
  int _referenceCount = 0;
  final Set<String> _activeWidgets = <String>{};
  
  // Concurrent access protection
  Completer<void>? _initializationCompleter;
  Completer<void>? _disposalCompleter;
  bool _isInitializing = false;
  bool _isDisposing = false;
  
  // Callbacks
  final List<LifecycleCallback> _onReadyCallbacks = [];
  final List<LifecycleCallback> _onDisposedCallbacks = [];
  final List<LifecycleErrorCallback> _onErrorCallbacks = [];
  
  // Getters
  FaceProvidersLifecycleState get state => _state;
  String? get errorMessage => _errorMessage;
  bool get isReady => _state == FaceProvidersLifecycleState.ready;
  bool get isInitializing => _isInitializing;
  bool get isDisposing => _isDisposing;
  int get referenceCount => _referenceCount;
  
  FaceCaptureProvider? get faceCaptureProvider => _faceCaptureProvider;
  FaceDetectionProvider? get faceDetectionProvider => _faceDetectionProvider;
  
  /// Đăng ký widget sử dụng face providers
  /// [widgetId] - unique identifier cho widget
  /// [autoInitialize] - có tự động initialize providers không
  Future<void> registerWidget(String widgetId, {bool autoInitialize = true}) async {
    if (_activeWidgets.contains(widgetId)) {
      if (kDebugMode) {
        print('! Widget $widgetId already registered');
      }
      // If already registered, just return without error
      return;
    }

    _activeWidgets.add(widgetId);
    _referenceCount++;

    if (kDebugMode) {
      print('📝 Widget $widgetId registered. Reference count: $_referenceCount');
    }

    if (autoInitialize && _state == FaceProvidersLifecycleState.initial) {
      await initializeProviders();
    }

    notifyListeners();
  }
  
  /// Hủy đăng ký widget
  /// [widgetId] - unique identifier cho widget
  /// [autoDispose] - có tự động dispose providers khi không còn widget nào sử dụng
  Future<void> unregisterWidget(String widgetId, {bool autoDispose = true}) async {
    if (!_activeWidgets.contains(widgetId)) {
      if (kDebugMode) {
        print('⚠️ Widget $widgetId not registered');
      }
      return;
    }
    
    _activeWidgets.remove(widgetId);
    _referenceCount = (_referenceCount - 1).clamp(0, double.infinity).toInt();
    
    if (kDebugMode) {
      print('📝 Widget $widgetId unregistered. Reference count: $_referenceCount');
    }
    
    if (autoDispose && _referenceCount == 0 && _state == FaceProvidersLifecycleState.ready) {
      await disposeProviders();
    }
    
    notifyListeners();
  }
  
  /// Initialize face providers
  Future<void> initializeProviders({
    FaceDetectorMode performanceMode = FaceDetectorMode.fast,
    bool enableTracking = true,
    bool enableClassification = true,
    CameraConfig? cameraConfig,
  }) async {
    // Prevent concurrent initialization
    if (_isInitializing) {
      if (kDebugMode) {
        print('⏳ Providers already initializing, waiting...');
      }
      // Wait for current initialization to complete
      if (_initializationCompleter != null) {
        await _initializationCompleter!.future;
      }
      return;
    }

    if (_state == FaceProvidersLifecycleState.ready) {
      if (kDebugMode) {
        print('✅ Providers already initialized');
      }
      return;
    }

    if (_isDisposing) {
      throw Exception('Cannot initialize while disposing');
    }

    _isInitializing = true;
    _initializationCompleter = Completer<void>();
    _setState(FaceProvidersLifecycleState.initializing);

    try {
      if (kDebugMode) {
        print('🚀 Initializing face providers...');
      }

      // Create providers if not exist
      _faceCaptureProvider ??= FaceCaptureProvider();
      _faceDetectionProvider ??= FaceDetectionProvider();

      // Initialize face detection first
      await _faceDetectionProvider!.initializeFaceDetector(
        performanceMode: performanceMode,
        enableTracking: enableTracking,
        enableClassification: enableClassification,
      );

      // Then initialize camera with face capture config
      final config = cameraConfig ?? CameraConfig.faceCapture;
      if (_faceCaptureProvider!.status == CameraStatus.initial) {
        await _faceCaptureProvider!.initializeCamera(config);
      }

      _setState(FaceProvidersLifecycleState.ready);
      _clearError();

      // Notify callbacks
      for (final callback in _onReadyCallbacks) {
        try {
          callback();
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error in ready callback: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Face providers initialized successfully');
      }

      _initializationCompleter!.complete();

    } catch (e) {
      final errorMsg = 'Failed to initialize face providers: $e';
      _setError(errorMsg);
      _setState(FaceProvidersLifecycleState.error);

      // Notify error callbacks
      for (final callback in _onErrorCallbacks) {
        try {
          callback(errorMsg);
        } catch (callbackError) {
          if (kDebugMode) {
            print('❌ Error in error callback: $callbackError');
          }
        }
      }

      if (kDebugMode) {
        print('❌ $errorMsg');
      }

      _initializationCompleter!.completeError(e);
      rethrow;
    } finally {
      _isInitializing = false;
      _initializationCompleter = null;
    }
  }
  
  /// Dispose face providers
  Future<void> disposeProviders() async {
    // Prevent concurrent disposal
    if (_isDisposing) {
      if (kDebugMode) {
        print('⏳ Providers already disposing, waiting...');
      }
      // Wait for current disposal to complete
      if (_disposalCompleter != null) {
        await _disposalCompleter!.future;
      }
      return;
    }

    if (_state == FaceProvidersLifecycleState.initial) {
      if (kDebugMode) {
        print('✅ Providers already disposed');
      }
      return;
    }

    if (_isInitializing) {
      throw Exception('Cannot dispose while initializing');
    }

    _isDisposing = true;
    _disposalCompleter = Completer<void>();
    _setState(FaceProvidersLifecycleState.disposing);

    try {
      if (kDebugMode) {
        print('🧹 Disposing face providers...');
      }

      // Clear all widget registrations first
      _activeWidgets.clear();
      _referenceCount = 0;

      // Stop any ongoing operations first
      if (_faceDetectionProvider != null && !_faceDetectionProvider!.isDisposed) {
        _faceDetectionProvider!.clearFaces();
      }

      if (_faceCaptureProvider != null &&
          !_faceCaptureProvider!.isDisposed &&
          _faceCaptureProvider!.isStreamingEnabled) {
        await _faceCaptureProvider!.toggleImageStream(false);
      }

      // Extended delay to ensure operations complete
      await Future.delayed(const Duration(milliseconds: 500));

      // Quick dispose for smooth modal closing
      debugPrint('🧹 Starting quick dispose for smooth modal closing...');

      // Stop operations first without waiting
      if (_faceDetectionProvider != null && !_faceDetectionProvider!.isDisposed) {
        _faceDetectionProvider!.clearFaces();
      }

      if (_faceCaptureProvider != null && !_faceCaptureProvider!.isDisposed) {
        if (_faceCaptureProvider!.isStreamingEnabled) {
          // Stop stream without waiting for completion
          _faceCaptureProvider!.toggleImageStream(false);
        }
      }

      // Quick dispose without long delays
      if (_faceCaptureProvider != null && !_faceCaptureProvider!.isDisposed) {
        debugPrint('🧹 Quick disposing face capture provider...');
        _faceCaptureProvider!.dispose(); // Synchronous dispose
        _faceCaptureProvider = null;
        // Minimal delay for essential cleanup
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (_faceDetectionProvider != null && !_faceDetectionProvider!.isDisposed) {
        debugPrint('🧹 Quick disposing face detection provider...');
        _faceDetectionProvider!.dispose();
        _faceDetectionProvider = null;
        // Minimal delay for essential cleanup
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Minimal final delay for UI smoothness
      await Future.delayed(const Duration(milliseconds: 100));

      _setState(FaceProvidersLifecycleState.initial);
      _clearError();

      // Notify callbacks
      for (final callback in _onDisposedCallbacks) {
        try {
          callback();
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error in disposed callback: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Face providers disposed successfully');
      }

      _disposalCompleter!.complete();

    } catch (e) {
      final errorMsg = 'Failed to dispose face providers: $e';
      _setError(errorMsg);
      _setState(FaceProvidersLifecycleState.error);

      // Notify error callbacks
      for (final callback in _onErrorCallbacks) {
        try {
          callback(errorMsg);
        } catch (callbackError) {
          if (kDebugMode) {
            print('❌ Error in error callback: $callbackError');
          }
        }
      }

      if (kDebugMode) {
        print('❌ $errorMsg');
      }

      _disposalCompleter!.completeError(e);
      rethrow;
    } finally {
      _isDisposing = false;
      _disposalCompleter = null;
    }
  }
  
  /// Force dispose providers (ignore reference count)
  Future<void> forceDisposeProviders() async {
    _activeWidgets.clear();
    _referenceCount = 0;
    await disposeProviders();
  }
  
  /// Reset lifecycle manager to initial state
  Future<void> reset() async {
    await forceDisposeProviders();
    _clearError();
    _clearCallbacks();
    if (kDebugMode) {
      print('🔄 Lifecycle manager reset to initial state');
    }
  }
  
  // Callback management
  void addOnReadyCallback(LifecycleCallback callback) {
    _onReadyCallbacks.add(callback);
  }
  
  void removeOnReadyCallback(LifecycleCallback callback) {
    _onReadyCallbacks.remove(callback);
  }
  
  void addOnDisposedCallback(LifecycleCallback callback) {
    _onDisposedCallbacks.add(callback);
  }
  
  void removeOnDisposedCallback(LifecycleCallback callback) {
    _onDisposedCallbacks.remove(callback);
  }
  
  void addOnErrorCallback(LifecycleErrorCallback callback) {
    _onErrorCallbacks.add(callback);
  }
  
  void removeOnErrorCallback(LifecycleErrorCallback callback) {
    _onErrorCallbacks.remove(callback);
  }
  
  void _clearCallbacks() {
    _onReadyCallbacks.clear();
    _onDisposedCallbacks.clear();
    _onErrorCallbacks.clear();
  }
  
  // Private methods
  void _setState(FaceProvidersLifecycleState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
  }
  
  @override
  void dispose() {
    forceDisposeProviders();
    _clearCallbacks();
    super.dispose();
  }
}

import 'package:flutter/foundation.dart';

/// Face detection engine types available
enum FaceDetectionEngineType {
  /// Google ML Kit face detection (default for mobile)
  mlKit,
  
  /// MediaPipe BlazeFace engine (recommended for terminal)
  mediaPipe,
  
  /// UltraFace TensorFlow Lite engine (high performance)
  ultraFace,
  
  /// Hybrid detector (combines multiple engines)
  hybrid,
}

/// Configuration for face detection system
class FaceDetectionConfig {
  /// Primary detection engine to use
  final FaceDetectionEngineType primaryEngine;
  
  /// Fallback engine (used in hybrid mode)
  final FaceDetectionEngineType? fallbackEngine;
  
  /// Confidence threshold for face detection (0.0 - 1.0)
  final double confidenceThreshold;
  
  /// Maximum number of faces to detect
  final int maxFaces;
  
  /// Enable landmark detection
  final bool enableLandmarks;
  
  /// Enable face tracking
  final bool enableTracking;
  
  /// Performance mode
  final PerformanceMode performanceMode;
  
  /// Platform-specific optimizations
  final bool enableGPUAcceleration;
  
  /// Enable fallback to secondary engine on failure
  final bool enableFallback;
  
  /// Timeout for detection operations (milliseconds)
  final int detectionTimeoutMs;
  
  const FaceDetectionConfig({
    this.primaryEngine = FaceDetectionEngineType.mlKit,
    this.fallbackEngine,
    this.confidenceThreshold = 0.5,
    this.maxFaces = 5,
    this.enableLandmarks = false,
    this.enableTracking = false,
    this.performanceMode = PerformanceMode.balanced,
    this.enableGPUAcceleration = true,
    this.enableFallback = false,
    this.detectionTimeoutMs = 5000,
  });
  
  /// Create configuration optimized for terminal devices (Telpo F8)
  factory FaceDetectionConfig.forTerminal({
    bool useMediaPipe = true,
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    return FaceDetectionConfig(
      primaryEngine: useMediaPipe 
          ? FaceDetectionEngineType.mediaPipe 
          : FaceDetectionEngineType.ultraFace,
      fallbackEngine: useMediaPipe 
          ? FaceDetectionEngineType.ultraFace 
          : FaceDetectionEngineType.mediaPipe,
      confidenceThreshold: confidenceThreshold ?? 0.6,
      maxFaces: maxFaces ?? 3,
      enableLandmarks: enableLandmarks ?? true,
      enableTracking: true,
      performanceMode: PerformanceMode.maxPerformance,
      enableGPUAcceleration: true,
      enableFallback: true,
      detectionTimeoutMs: 3000,
    );
  }
  
  /// Create configuration optimized for mobile devices
  factory FaceDetectionConfig.forMobile({
    bool useMediaPipe = false,
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    return FaceDetectionConfig(
      primaryEngine: useMediaPipe 
          ? FaceDetectionEngineType.mediaPipe 
          : FaceDetectionEngineType.mlKit,
      fallbackEngine: useMediaPipe 
          ? FaceDetectionEngineType.mlKit 
          : FaceDetectionEngineType.mediaPipe,
      confidenceThreshold: confidenceThreshold ?? 0.5,
      maxFaces: maxFaces ?? 5,
      enableLandmarks: enableLandmarks ?? false,
      enableTracking: false,
      performanceMode: PerformanceMode.balanced,
      enableGPUAcceleration: true,
      enableFallback: true,
      detectionTimeoutMs: 5000,
    );
  }
  
  /// Create hybrid configuration with MediaPipe as primary
  factory FaceDetectionConfig.hybridMediaPipe({
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    return FaceDetectionConfig(
      primaryEngine: FaceDetectionEngineType.hybrid,
      fallbackEngine: FaceDetectionEngineType.mlKit,
      confidenceThreshold: confidenceThreshold ?? 0.5,
      maxFaces: maxFaces ?? 5,
      enableLandmarks: enableLandmarks ?? true,
      enableTracking: true,
      performanceMode: PerformanceMode.balanced,
      enableGPUAcceleration: true,
      enableFallback: true,
      detectionTimeoutMs: 4000,
    );
  }
  
  /// Copy configuration with modifications
  FaceDetectionConfig copyWith({
    FaceDetectionEngineType? primaryEngine,
    FaceDetectionEngineType? fallbackEngine,
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
    bool? enableTracking,
    PerformanceMode? performanceMode,
    bool? enableGPUAcceleration,
    bool? enableFallback,
    int? detectionTimeoutMs,
  }) {
    return FaceDetectionConfig(
      primaryEngine: primaryEngine ?? this.primaryEngine,
      fallbackEngine: fallbackEngine ?? this.fallbackEngine,
      confidenceThreshold: confidenceThreshold ?? this.confidenceThreshold,
      maxFaces: maxFaces ?? this.maxFaces,
      enableLandmarks: enableLandmarks ?? this.enableLandmarks,
      enableTracking: enableTracking ?? this.enableTracking,
      performanceMode: performanceMode ?? this.performanceMode,
      enableGPUAcceleration: enableGPUAcceleration ?? this.enableGPUAcceleration,
      enableFallback: enableFallback ?? this.enableFallback,
      detectionTimeoutMs: detectionTimeoutMs ?? this.detectionTimeoutMs,
    );
  }
  
  /// Get recommended configuration for current platform
  static FaceDetectionConfig getRecommended({bool preferMediaPipe = false}) {
    if (kIsWeb) {
      // Web platform - use ML Kit
      return FaceDetectionConfig.forMobile(useMediaPipe: false);
    }
    
    if (defaultTargetPlatform == TargetPlatform.android ||
        defaultTargetPlatform == TargetPlatform.iOS) {
      // Mobile platforms
      return FaceDetectionConfig.forMobile(useMediaPipe: preferMediaPipe);
    }
    
    // Desktop/Terminal platforms
    return FaceDetectionConfig.forTerminal(useMediaPipe: preferMediaPipe);
  }
  
  @override
  String toString() {
    return 'FaceDetectionConfig('
        'primary: $primaryEngine, '
        'fallback: $fallbackEngine, '
        'confidence: $confidenceThreshold, '
        'maxFaces: $maxFaces, '
        'landmarks: $enableLandmarks, '
        'tracking: $enableTracking, '
        'performance: $performanceMode'
        ')';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FaceDetectionConfig &&
        other.primaryEngine == primaryEngine &&
        other.fallbackEngine == fallbackEngine &&
        other.confidenceThreshold == confidenceThreshold &&
        other.maxFaces == maxFaces &&
        other.enableLandmarks == enableLandmarks &&
        other.enableTracking == enableTracking &&
        other.performanceMode == performanceMode &&
        other.enableGPUAcceleration == enableGPUAcceleration &&
        other.enableFallback == enableFallback &&
        other.detectionTimeoutMs == detectionTimeoutMs;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      primaryEngine,
      fallbackEngine,
      confidenceThreshold,
      maxFaces,
      enableLandmarks,
      enableTracking,
      performanceMode,
      enableGPUAcceleration,
      enableFallback,
      detectionTimeoutMs,
    );
  }
}

/// Performance modes for face detection
enum PerformanceMode {
  /// Maximum accuracy, slower performance
  maxAccuracy,
  
  /// Balanced accuracy and performance
  balanced,
  
  /// Maximum performance, lower accuracy
  maxPerformance,
  
  /// Power saving mode
  powerSaver,
}

/// Extension for performance mode descriptions
extension PerformanceModeExtension on PerformanceMode {
  String get description {
    switch (this) {
      case PerformanceMode.maxAccuracy:
        return 'Maximum accuracy, slower performance';
      case PerformanceMode.balanced:
        return 'Balanced accuracy and performance';
      case PerformanceMode.maxPerformance:
        return 'Maximum performance, lower accuracy';
      case PerformanceMode.powerSaver:
        return 'Power saving mode';
    }
  }
  
  double get confidenceAdjustment {
    switch (this) {
      case PerformanceMode.maxAccuracy:
        return 0.1; // Higher threshold for accuracy
      case PerformanceMode.balanced:
        return 0.0; // No adjustment
      case PerformanceMode.maxPerformance:
        return -0.1; // Lower threshold for speed
      case PerformanceMode.powerSaver:
        return -0.05; // Slightly lower threshold
    }
  }
}

/// Extension for engine type descriptions
extension FaceDetectionEngineTypeExtension on FaceDetectionEngineType {
  String get displayName {
    switch (this) {
      case FaceDetectionEngineType.mlKit:
        return 'Google ML Kit';
      case FaceDetectionEngineType.mediaPipe:
        return 'MediaPipe BlazeFace';
      case FaceDetectionEngineType.ultraFace:
        return 'UltraFace TensorFlow';
      case FaceDetectionEngineType.hybrid:
        return 'Hybrid Detector';
    }
  }
  
  String get description {
    switch (this) {
      case FaceDetectionEngineType.mlKit:
        return 'Google ML Kit face detection - Good for mobile apps';
      case FaceDetectionEngineType.mediaPipe:
        return 'MediaPipe BlazeFace - Optimized for real-time detection';
      case FaceDetectionEngineType.ultraFace:
        return 'UltraFace TensorFlow Lite - High performance for terminals';
      case FaceDetectionEngineType.hybrid:
        return 'Hybrid detector - Combines multiple engines for reliability';
    }
  }
  
  bool get supportsLandmarks {
    switch (this) {
      case FaceDetectionEngineType.mlKit:
        return true;
      case FaceDetectionEngineType.mediaPipe:
        return true;
      case FaceDetectionEngineType.ultraFace:
        return false; // Current implementation doesn't support landmarks
      case FaceDetectionEngineType.hybrid:
        return true;
    }
  }
  
  bool get supportsTracking {
    switch (this) {
      case FaceDetectionEngineType.mlKit:
        return true;
      case FaceDetectionEngineType.mediaPipe:
        return false; // Current implementation doesn't support tracking
      case FaceDetectionEngineType.ultraFace:
        return false;
      case FaceDetectionEngineType.hybrid:
        return true;
    }
  }
}

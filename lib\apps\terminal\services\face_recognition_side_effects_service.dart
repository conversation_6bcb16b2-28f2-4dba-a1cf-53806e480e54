import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../shared/services/relay_management_service.dart';
import '../../../packages/relay_controller/lib/relay_controller.dart';
import '../providers/face_recognition_side_effects_provider.dart';
import '../providers/device_auto_registration_provider.dart';

/// Service for executing side effects after face recognition
/// 
/// This service handles the execution of configured side effects
/// when face recognition results are received from the server
class FaceRecognitionSideEffectsService {
  static FaceRecognitionSideEffectsService? _instance;
  static FaceRecognitionSideEffectsService get instance => 
      _instance ??= FaceRecognitionSideEffectsService._();

  FaceRecognitionSideEffectsService._();

  final RelayManagementService _relayService = RelayManagementService.instance;
  
  bool _isInitialized = false;
  bool _isExecuting = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize relay management service if needed
      if (!_relayService.isInitialized) {
        // Will be initialized when needed with actual device config
      }
      
      _isInitialized = true;
      debugPrint('FaceRecognitionSideEffectsService initialized');
      
    } catch (e) {
      debugPrint('Error initializing FaceRecognitionSideEffectsService: $e');
      rethrow;
    }
  }

  /// Execute side effects based on face recognition result
  Future<void> executeSideEffects({
    required Map<String, dynamic> recognitionResult,
    required BuildContext? context,
  }) async {
    if (!_isInitialized || _isExecuting) return;

    try {
      _isExecuting = true;
      
      final provider = FaceRecognitionSideEffectsProvider.instance;
      
      // Check if side effects are enabled
      if (!provider.isEnabled) {
        debugPrint('Side effects disabled, skipping execution');
        return;
      }

      // Extract recognition data
      final bool serverAllowAccess = recognitionResult['allowAccess'] ?? false;
      final double confidence = (recognitionResult['confidence'] ?? 0.0).toDouble();
      final Map<String, dynamic>? user = recognitionResult['user'];
      final String? accessLevel = user?['accessLevel'];
      final String recognitionId = recognitionResult['recognitionId'] ?? 'unknown';
      
      debugPrint('Executing side effects for recognition: $recognitionId');
      debugPrint('  Server Allow Access: $serverAllowAccess');
      debugPrint('  Confidence: $confidence');
      debugPrint('  Access Level: $accessLevel');

      // Check if access should be granted based on configuration
      final shouldGrantAccess = provider.shouldGrantAccess(
        serverAllowAccess: serverAllowAccess,
        confidence: confidence,
        accessLevel: accessLevel,
      );

      debugPrint('  Final Access Decision: $shouldGrantAccess');

      // Execute side effects based on access decision
      if (shouldGrantAccess) {
        await _executeAccessGrantedSideEffects(
          provider: provider,
          recognitionResult: recognitionResult,
          context: context,
        );
      } else {
        await _executeAccessDeniedSideEffects(
          provider: provider,
          recognitionResult: recognitionResult,
          context: context,
        );
      }

      // Always log if enabled
      if (provider.logAllAttempts) {
        await _logRecognitionAttempt(
          recognitionResult: recognitionResult,
          finalDecision: shouldGrantAccess,
        );
      }

      // Save image if enabled
      if (provider.saveRecognitionImages) {
        await _saveRecognitionImage(recognitionResult);
      }

    } catch (e) {
      debugPrint('Error executing side effects: $e');
    } finally {
      _isExecuting = false;
    }
  }

  /// Execute side effects when access is granted
  Future<void> _executeAccessGrantedSideEffects({
    required FaceRecognitionSideEffectsProvider provider,
    required Map<String, dynamic> recognitionResult,
    required BuildContext? context,
  }) async {
    debugPrint('Executing access granted side effects');

    // Trigger relay if enabled
    if (provider.triggerRelayOnAccess) {
      await _triggerRelay(provider);
    }

    // Show success notification if enabled
    if (provider.showSuccessNotification && context != null) {
      await _showSuccessNotification(
        context: context,
        user: recognitionResult['user'],
        duration: provider.notificationDuration,
      );
    }
  }

  /// Execute side effects when access is denied
  Future<void> _executeAccessDeniedSideEffects({
    required FaceRecognitionSideEffectsProvider provider,
    required Map<String, dynamic> recognitionResult,
    required BuildContext? context,
  }) async {
    debugPrint('Executing access denied side effects');

    // Show denied notification if enabled
    if (provider.showDeniedNotification && context != null) {
      await _showDeniedNotification(
        context: context,
        reason: recognitionResult['reason'] ?? 'Access denied',
        duration: provider.notificationDuration,
      );
    }
  }

  /// Trigger relay for access granted
  Future<void> _triggerRelay(FaceRecognitionSideEffectsProvider provider) async {
    try {
      debugPrint('Triggering relay: ${provider.defaultRelayDeviceId}');
      
      // Initialize relay service with device config if needed
      if (!_relayService.isInitialized) {
        final deviceConfig = RelayDeviceConfig(
          deviceId: provider.defaultRelayDeviceId,
          deviceName: 'Face Recognition Relay',
          relayCount: 4,
          baudRate: 115200,
        );
        
        await _relayService.initialize(
          config: deviceConfig,
          autoConnect: true,
        );
      }

      // Turn relay ON
      await _relayService.controlRelay(0, RelayAction.on);
      debugPrint('✅ Relay turned ON');

      // Wait for configured duration
      await Future.delayed(Duration(seconds: provider.relayTriggerDuration));

      // Turn relay OFF
      await _relayService.controlRelay(0, RelayAction.off);
      debugPrint('✅ Relay turned OFF after ${provider.relayTriggerDuration}s');

    } catch (e) {
      debugPrint('❌ Failed to trigger relay: $e');
    }
  }

  /// Show success notification
  Future<void> _showSuccessNotification({
    required BuildContext context,
    required Map<String, dynamic>? user,
    required int duration,
  }) async {
    try {
      final userName = user?['name'] ?? 'User';
      final accessLevel = user?['accessLevel'] ?? 'USER';
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Access Granted',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('Welcome, $userName ($accessLevel)'),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: duration),
        ),
      );
      
      debugPrint('✅ Success notification shown for: $userName');
      
    } catch (e) {
      debugPrint('❌ Failed to show success notification: $e');
    }
  }

  /// Show denied notification
  Future<void> _showDeniedNotification({
    required BuildContext context,
    required String reason,
    required int duration,
  }) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.block, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Access Denied',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(reason),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: duration),
        ),
      );
      
      debugPrint('✅ Denied notification shown: $reason');
      
    } catch (e) {
      debugPrint('❌ Failed to show denied notification: $e');
    }
  }

  /// Log recognition attempt
  Future<void> _logRecognitionAttempt({
    required Map<String, dynamic> recognitionResult,
    required bool finalDecision,
  }) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'recognition_id': recognitionResult['recognitionId'],
        'server_decision': recognitionResult['allowAccess'],
        'final_decision': finalDecision,
        'confidence': recognitionResult['confidence'],
        'user': recognitionResult['user'],
        'reason': recognitionResult['reason'],
      };
      
      // TODO: Implement actual logging to file/database
      debugPrint('📝 Recognition attempt logged: ${logEntry['recognition_id']}');
      
    } catch (e) {
      debugPrint('❌ Failed to log recognition attempt: $e');
    }
  }

  /// Save recognition image
  Future<void> _saveRecognitionImage(Map<String, dynamic> recognitionResult) async {
    try {
      // TODO: Implement image saving functionality
      debugPrint('💾 Recognition image saved: ${recognitionResult['recognitionId']}');
      
    } catch (e) {
      debugPrint('❌ Failed to save recognition image: $e');
    }
  }

  /// Auto-register relay devices when terminal connects
  Future<void> autoRegisterRelayDevices(String terminalId) async {
    try {
      final autoRegProvider = DeviceAutoRegistrationProvider.instance;
      
      if (!autoRegProvider.isInitialized) {
        await autoRegProvider.initialize();
      }

      if (autoRegProvider.autoRegisterEnabled) {
        debugPrint('Auto-registering relay devices for terminal: $terminalId');
        
        final success = await autoRegProvider.autoRegisterRelayDevices(terminalId);
        
        if (success) {
          debugPrint('✅ Auto-registration completed successfully');
          
          // Update side effects provider with registered devices
          final sideEffectsProvider = FaceRecognitionSideEffectsProvider.instance;
          for (final relayId in autoRegProvider.registeredRelayDevices) {
            await sideEffectsProvider.addRelayDevice(relayId);
          }
          
        } else {
          debugPrint('❌ Auto-registration failed: ${autoRegProvider.lastError}');
        }
      } else {
        debugPrint('Auto-registration disabled');
      }
      
    } catch (e) {
      debugPrint('❌ Auto-registration error: $e');
    }
  }

  /// Get service status
  Map<String, dynamic> getServiceStatus() {
    return {
      'initialized': _isInitialized,
      'executing': _isExecuting,
      'relay_service_initialized': _relayService.isInitialized,
      'relay_service_connected': _relayService.isConnected,
    };
  }

  /// Dispose service
  void dispose() {
    _isInitialized = false;
    _isExecuting = false;
  }
}

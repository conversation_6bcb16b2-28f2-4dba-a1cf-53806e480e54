/// Core Layer Index - Export all core components
///
/// This file exports all core-related components for easy importing.
/// The core layer provides shared utilities, base classes, and infrastructure
/// that can be used across both mobile and terminal applications.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/index.dart';
///
/// // Use base classes
/// class MyRepository extends BaseRepository { ... }
/// class MyProvider extends BaseProvider { ... }
///
/// // Use utilities
/// final isValid = ValidationUtils.validateEmail(email);
/// final formatted = AppDateUtils.formatDisplayDate(date);
///
/// // Use error handling
/// try {
///   // some operation
/// } catch (e) {
///   final failure = globalErrorHandler.convertToFailure(e);
/// }
/// ```

// ============================================================================
// IMPORTS FOR UTILITY FUNCTIONS
// ============================================================================
import 'package:dartz/dartz.dart';
import 'errors/failures.dart';
import 'errors/error_handler.dart';
import 'di/modules/core_module.dart' as core_module;
import 'di/modules/network_module.dart' as network_module;
import 'di/modules/auth_module.dart' as auth_module;
import 'di/modules/user_module.dart' as user_module;
import 'di/modules/face_module.dart' as face_module;
import 'utils/logger.dart';

// ============================================================================
// BASE CLASSES
// ============================================================================
export 'base/base_data_source.dart' hide ValidationMixin, CachingMixin;
export 'base/base_provider.dart';
export 'base/base_repository.dart';
export 'base/base_use_case.dart' hide ValidationMixin, CachingMixin;

// ============================================================================
// CONFIGURATION
// ============================================================================
export 'config/app_config.dart';
export 'config/theme.dart';

// ============================================================================
// CONSTANTS
// ============================================================================
export 'constants/index.dart'; // This exports all constants

// ============================================================================
// DEPENDENCY INJECTION
// ============================================================================
export 'di/shared_service_locator.dart'; // Multi-app shared DI
export 'di/service_locator.dart'; // Legacy single-app DI
export 'di/modules/core_module.dart' hide getIt;
export 'di/modules/network_module.dart' hide getIt;
export 'di/modules/auth_module.dart' hide getIt;
export 'di/modules/user_module.dart' hide getIt;
export 'di/modules/face_module.dart' hide getIt;

// ============================================================================
// ERROR HANDLING
// ============================================================================
export 'errors/exceptions.dart';
export 'errors/failures.dart';
export 'errors/error_handler.dart';

// ============================================================================
// MIXINS
// ============================================================================
export 'mixins/loading_mixin.dart';

// ============================================================================
// NETWORK
// ============================================================================
export 'network/network_info.dart';

// ============================================================================
// STORAGE
// ============================================================================
export 'storage/secure_storage_service.dart';

// ============================================================================
// UTILITIES
// ============================================================================
export 'utils/date_utils.dart';
export 'utils/logger.dart';
export 'utils/string_utils.dart';
export 'utils/validation_utils.dart';

// ============================================================================
// COMMONLY USED EXTERNAL PACKAGES
// ============================================================================
// Re-export commonly used external packages for convenience
export 'package:dartz/dartz.dart' show Either, Left, Right;
export 'package:equatable/equatable.dart' show Equatable;
export 'package:get_it/get_it.dart' show GetIt;

// ============================================================================
// TYPE ALIASES FOR CONVENIENCE
// ============================================================================

/// Type alias for Either with Failure as left type
typedef FailureOr<T> = Either<Failure, T>;

/// Type alias for async operations that can fail
typedef AsyncFailureOr<T> = Future<Either<Failure, T>>;

/// Type alias for validation functions
typedef Validator<T> = String? Function(T? value);

/// Type alias for async operations
typedef AsyncOperation<T> = Future<T> Function();

/// Type alias for sync operations
typedef SyncOperation<T> = T Function();

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/// Create a successful Either result
FailureOr<T> success<T>(T value) => Right(value);

/// Create a failed Either result
FailureOr<T> failure<T>(Failure failure) => Left(failure);

/// Create a successful async Either result
AsyncFailureOr<T> asyncSuccess<T>(T value) async => Right(value);

/// Create a failed async Either result
AsyncFailureOr<T> asyncFailure<T>(Failure failure) async => Left(failure);

/// Execute an operation and wrap result in Either
AsyncFailureOr<T> executeOperation<T>(AsyncOperation<T> operation) async {
  try {
    final result = await operation();
    return Right(result);
  } catch (error) {
    final failure = globalErrorHandler.convertToFailure(error);
    return Left(failure);
  }
}

/// Execute a sync operation and wrap result in Either
FailureOr<T> executeSyncOperation<T>(SyncOperation<T> operation) {
  try {
    final result = operation();
    return Right(result);
  } catch (error) {
    final failure = globalErrorHandler.convertToFailure(error);
    return Left(failure);
  }
}

// ============================================================================
// CORE CONSTANTS
// ============================================================================

/// Core layer version for debugging
const String coreLayerVersion = '1.0.0';

/// Core layer build date
const String coreLayerBuildDate = '2025-01-27';

/// Core layer description
const String coreLayerDescription = 'Shared core layer for multi-app architecture';

// ============================================================================
// DEBUGGING UTILITIES
// ============================================================================

/// Get core layer information for debugging
Map<String, dynamic> getCoreLayerInfo() {
  return {
    'version': coreLayerVersion,
    'buildDate': coreLayerBuildDate,
    'description': coreLayerDescription,
    'dependencies': {
      'core': core_module.areCoreDependenciesRegistered(),
      'network': network_module.areNetworkDependenciesRegistered(),
      'auth': auth_module.areAuthDependenciesRegistered(),
      'user': user_module.areUserDependenciesRegistered(),
      'face': face_module.areFaceDependenciesRegistered(),
    },
  };
}

/// Print core layer status for debugging
void printCoreLayerStatus() {
  final info = getCoreLayerInfo();
  Logger.i('CoreLayer', 'Core Layer Status: $info');
}

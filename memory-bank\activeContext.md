# Active Context

## Current Work Focus
Đã refactor hệ thống relay từ ESP32-specific sang flexible USB-TTL controller với configurable device profiles để hỗ trợ nhiều loại thiết bị relay khác nhau.

## Recent Changes - Flexible USB-TTL Relay Controller Refactoring
- **USB-TTL Relay Controller**: Refactor từ ESP32-specific sang generic controller với device profile system:
  - **DeviceProfile.esp32()**: `R0:1`, `R0:0`, `R1:TOGGLE`, `R2:500`, `ALL:1`, `ALL:0`
  - **DeviceProfile.arduino()**: `REL_0_ON`, `REL_0_OFF`, `REL_ALL_ON`, etc.
  - **DeviceProfile.simple()**: `01`, `00`, `1T`, `1P500`, `A1`, `A0`
  - **DeviceProfile.custom()**: User-defined command templates
  - Configurable command terminators và capability flags

- **Build Issues Fixed**: Resolved import paths, export conflicts, và method call issues:
  - Fixed import paths: thêm `/lib/` vào package imports
  - Removed duplicate exports: `RelayAction` và `RelayConfiguration` từ ESP32 controller
  - Fixed static method calls: `getAvailableDevices()` trong AutoConnect classes
  - Updated constructor calls: thêm `deviceProfile` parameter


- **Relay Management Service**: Service quản lý toàn diện cho relay operations
  - Configuration management cho 4 relay (có thể mở rộng)
  - Status monitoring và event streaming
  - Server registration capabilities
  - Error handling và reconnection logic

- **Configuration System Integration**: Tích hợp đầy đủ với hệ thống config linh hoạt
  - 35+ configuration parameters bao gồm device profile configs
  - Device profile selection: esp32, arduino, simple, custom
  - Custom command template configuration cho flexible device support
  - Support environment variables và runtime changes
  - Vietnamese localization cho tất cả parameters
  - Validation và type safety

- **Relay Control Provider**: Provider chuyên biệt cho terminal app
  - Real-time status monitoring
  - Event-driven UI updates
  - Convenience methods cho face recognition integration
  - Timer management cho timed operations

- **Server API Integration**: API service cho server communication
  - Device registration với comprehensive metadata
  - Relay command execution qua server
  - Device history và monitoring
  - Ping/health check functionality

- **Relay Control UI**: Interface điều khiển relay cho terminal app
  - Individual relay controls với real-time status
  - Quick actions (mở cửa, thông báo thành công)
  - Raw command interface cho ESP32
  - Configuration display và monitoring

## USB-TTL Relay Configuration Parameters
Hệ thống hỗ trợ cấu hình toàn diện qua 4 categories:
- **Device Settings**: Device ID, name, relay count, baud rate, connection type, device profile
- **Device Profile Config**: Profile type (esp32/arduino/simple/custom), custom command templates
- **Individual Relay Config**: Tên, enable status, default timeout cho từng relay
- **Network/Server Config**: Server URL, auth token, registration settings
- **Timing Config**: Connection timeout, retry attempts, status update intervals

## Technical Architecture
1. **Hardware Layer**: Các thiết bị relay khác nhau với USB-TTL communication
2. **Controller Layer**: UsbTtlRelayController với DeviceProfile abstraction
3. **Service Layer**: RelayManagementService với flexible device profile configuration
4. **Provider Layer**: RelayControlProvider cho terminal app state management
5. **API Layer**: RelayApiService cho server communication
6. **UI Layer**: Relay control screens với real-time monitoring

## Files Created/Modified
- `lib/packages/relay_controller/lib/src/usb_ttl_relay_controller.dart` (NEW: flexible USB-TTL controller)
- `lib/packages/relay_controller/lib/src/esp32_relay_controller.dart` (@Deprecated: legacy ESP32 controller)
- Updated `lib/shared/services/relay_management_service.dart` (supports device profiles)
- Updated `lib/shared/core/config/relay_config_parameters.dart` (added device profile configs)
- `lib/shared/core/errors/relay_exceptions.dart` (error handling)
- `lib/shared/services/relay_api_service.dart` (API integration)
- Updated `lib/apps/terminal/providers/relay_control_provider.dart` (generic relay device)
- Updated `lib/apps/terminal/presentation/screens/relay_control_screen.dart` (generic UI)
- Updated `lib/shared/core/config/config_parameters_registry.dart` (config integration)
- Updated `lib/packages/relay_controller/lib/relay_controller.dart` (new exports, deprecated old)

## Integration Points
- **Face Recognition**: Trigger door unlock và access granted indicators
- **Configuration System**: Full integration với flexible config parameters
- **Server Communication**: Device registration và remote control capabilities  
- **Terminal App**: Complete UI cho relay monitoring và control
- **Kiosk Mode**: Ready for integration với kiosk functionality

## Default Relay Configuration
- **Relay 0**: "Cửa chính" - 5 giây timeout, enabled
- **Relay 1**: "Cửa phụ" - 3 giây timeout, enabled  
- **Relay 2**: "Đèn báo" - 1 giây timeout, disabled by default
- **Relay 3**: "Còi báo" - 500ms timeout, disabled by default

## Build Status
✅ USB-TTL Relay Controller: IMPLEMENTED (with DeviceProfile support)
✅ Device Profile System: IMPLEMENTED (ESP32, Arduino, Simple, Custom)
✅ Configuration Integration: COMPLETED (added device profile configs)
✅ Relay Management Service: REFACTORED (supports flexible device profiles)
✅ Terminal Provider: UPDATED (generic relay device support)
✅ Server API Integration: IMPLEMENTED
✅ Relay Control UI: UPDATED (generic relay interface)
✅ Build Issues: RESOLVED (import paths, export conflicts, method calls)
⏳ Hardware Testing: PENDING (test with different device types)

## Next Steps
- Test với các loại hardware khác nhau (ESP32, Arduino, custom devices)
- Verify device profile command templates hoạt động chính xác
- Create documentation cho custom device profile configuration
- Integrate với face recognition flow
- Add relay control triggers cho successful/failed authentication
- Performance testing với multiple relay operations và device types 
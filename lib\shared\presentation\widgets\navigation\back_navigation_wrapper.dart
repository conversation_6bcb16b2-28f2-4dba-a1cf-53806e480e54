import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// Widget wrapper để xử lý edge swipe navigation và hardware back button
/// cho toàn bộ ứng dụng Flutter mobile, tích hợp với GoRouter
class BackNavigationWrapper extends StatefulWidget {
  final Widget child;
  final bool enableEdgeSwipe;
  final bool enableHardwareBack;
  final VoidCallback? onExitConfirmation;
  final bool isRootScreen;
  final String? customBackRoute;

  const BackNavigationWrapper({
    super.key,
    required this.child,
    this.enableEdgeSwipe = true,
    this.enableHardwareBack = true,
    this.onExitConfirmation,
    this.isRootScreen = false,
    this.customBackRoute,
  });

  @override
  State<BackNavigationWrapper> createState() => _BackNavigationWrapperState();
}

class _BackNavigationWrapperState extends State<BackNavigationWrapper> {
  static const double _edgeSwipeThreshold = 50.0;
  static const double _swipeVelocityThreshold = 300.0;
  
  double _startX = 0.0;
  bool _isSwipeInProgress = false;

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = widget.child;

    // Wrap với PopScope để xử lý hardware back button
    if (widget.enableHardwareBack) {
      wrappedChild = PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          await _handleBackNavigation(context);
        },
        child: wrappedChild,
      );
    }

    // Wrap với GestureDetector để xử lý edge swipe
    if (widget.enableEdgeSwipe) {
      wrappedChild = GestureDetector(
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: wrappedChild,
      );
    }

    return wrappedChild;
  }

  void _onPanStart(DragStartDetails details) {
    _startX = details.globalPosition.dx;
    _isSwipeInProgress = details.globalPosition.dx <= _edgeSwipeThreshold;
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isSwipeInProgress) return;

    final currentX = details.globalPosition.dx;
    final deltaX = currentX - _startX;

    // Chỉ xử lý swipe từ trái sang phải
    if (deltaX < 0) {
      _isSwipeInProgress = false;
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isSwipeInProgress) return;

    final velocity = details.velocity.pixelsPerSecond.dx;
    
    // Kiểm tra velocity và distance để xác định swipe hợp lệ
    if (velocity > _swipeVelocityThreshold) {
      _handleBackNavigation(context);
    }

    _isSwipeInProgress = false;
  }

  Future<void> _handleBackNavigation(BuildContext context) async {
    // Nếu có custom back route, sử dụng nó
    if (widget.customBackRoute != null) {
      context.go(widget.customBackRoute!);
      return;
    }

    // Nếu là root screen, hiển thị exit confirmation
    if (widget.isRootScreen) {
      if (widget.onExitConfirmation != null) {
        widget.onExitConfirmation!();
      } else {
        await _showExitConfirmation(context);
      }
      return;
    }

    // Kiểm tra xem có thể go back trong nested navigator không
    final navigator = Navigator.maybeOf(context);
    if (navigator != null && navigator.canPop()) {
      navigator.pop();
    } else {
      // Nếu không thể pop trong nested navigator, kiểm tra GoRouter
      if (context.canPop()) {
        context.pop();
      } else {
        // Nếu không thể pop, navigate về dashboard
        context.go('/dashboard');
      }
    }
  }

  Future<void> _showExitConfirmation(BuildContext context) async {
    final shouldExit = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thoát ứng dụng'),
        content: const Text('Bạn có chắc chắn muốn thoát ứng dụng không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Thoát'),
          ),
        ],
      ),
    );

    if (shouldExit == true) {
      SystemNavigator.pop();
    }
  }
}

/// Extension để dễ dàng sử dụng BackNavigationWrapper
extension BackNavigationWrapperExtension on Widget {
  /// Wrap widget với BackNavigationWrapper với cấu hình mặc định
  Widget withBackNavigation({
    bool enableEdgeSwipe = true,
    bool enableHardwareBack = true,
    VoidCallback? onExitConfirmation,
    bool isRootScreen = false,
    String? customBackRoute,
  }) {
    return BackNavigationWrapper(
      enableEdgeSwipe: enableEdgeSwipe,
      enableHardwareBack: enableHardwareBack,
      onExitConfirmation: onExitConfirmation,
      isRootScreen: isRootScreen,
      customBackRoute: customBackRoute,
      child: this,
    );
  }

  /// Wrap widget cho root screen với exit confirmation
  Widget withRootBackNavigation({
    VoidCallback? onExitConfirmation,
  }) {
    return BackNavigationWrapper(
      enableEdgeSwipe: true,
      enableHardwareBack: true,
      onExitConfirmation: onExitConfirmation,
      isRootScreen: true,
      child: this,
    );
  }

  /// Wrap widget với custom back route
  Widget withCustomBackNavigation(String backRoute) {
    return BackNavigationWrapper(
      enableEdgeSwipe: true,
      enableHardwareBack: true,
      customBackRoute: backRoute,
      child: this,
    );
  }
}

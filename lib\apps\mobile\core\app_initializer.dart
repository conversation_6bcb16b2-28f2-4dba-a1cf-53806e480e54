import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../../shared/core/di/shared_service_locator.dart';
import '../../../shared/services/service_locator.dart';
import '../../../shared/services/cookie_service.dart';
import '../../../shared/services/auth_state_service.dart';
import '../../../shared/core/storage/secure_storage_service.dart';

/// Handles app initialization in background to improve startup performance
class AppInitializer {
  static final AppInitializer _instance = AppInitializer._internal();
  factory AppInitializer() => _instance;
  AppInitializer._internal();

  bool _isInitialized = false;
  bool _isInitializing = false;

  /// Check if app is fully initialized
  bool get isInitialized => _isInitialized;

  /// Check if initialization is in progress
  bool get isInitializing => _isInitializing;

  /// Initialize app services in background
  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) return;

    _isInitializing = true;

    try {
      if (kDebugMode) {
        print('🚀 Starting app initialization...');
      }

      // Run initializations in parallel where possible
      await Future.wait([
        _initializeServiceLocator(),
        _initializeCoreServices(),
      ]);

      _isInitialized = true;
      if (kDebugMode) {
        print('🎉 App initialization complete!');
      }

    } catch (e) {
      if (kDebugMode) {
        print('🔴 App initialization failed: $e');
      }
      // Don't throw - app should still work with limited functionality
    } finally {
      _isInitializing = false;
    }
  }

  Future<void> _initializeServiceLocator() async {
    // Setup service locator với shared dependencies
    await SharedServiceLocator.setupSharedDependencies();
    if (kDebugMode) {
      print('✅ Shared dependencies setup complete');
    }

    // Initialize global service locator
    await serviceLocator.initialize();
    if (kDebugMode) {
      print('✅ Service locator initialized');
    }
  }

  Future<void> _initializeCoreServices() async {
    // Small delay to let service locator setup first
    await Future.delayed(const Duration(milliseconds: 100));

    // Initialize services in parallel
    await Future.wait([
      GetIt.instance<CookieService>().initialize(),
      GetIt.instance<AuthStateService>().initialize(
        GetIt.instance<SecureStorageService>(),
      ),
    ]);

    if (kDebugMode) {
      print('✅ Core services initialized');
    }
  }

  /// Wait for initialization to complete
  Future<void> waitForInitialization() async {
    while (_isInitializing) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// Reset initialization state (for testing)
  void reset() {
    _isInitialized = false;
    _isInitializing = false;
  }
}

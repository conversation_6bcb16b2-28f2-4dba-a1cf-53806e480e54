import 'package:flutter/foundation.dart';
import '../../services/api_endpoints.dart';

/// Application configuration class
/// Manages environment-specific settings and app-wide configurations
class AppConfig {
  // Singleton pattern
  static AppConfig? _instance;
  AppConfig._internal();
  
  factory AppConfig() {
    _instance ??= AppConfig._internal();
    return _instance!;
  }

  // ============================================================================
  // ENVIRONMENT CONFIGURATION
  // ============================================================================
  
  /// Current app environment
  AppEnvironment get environment {
    if (kDebugMode) {
      return AppEnvironment.development;
    } else if (kProfileMode) {
      return AppEnvironment.staging;
    } else {
      return AppEnvironment.production;
    }
  }

  /// Check if app is in development mode
  bool get isDevelopment => environment == AppEnvironment.development;
  
  /// Check if app is in staging mode
  bool get isStaging => environment == AppEnvironment.staging;
  
  /// Check if app is in production mode
  bool get isProduction => environment == AppEnvironment.production;

  // ============================================================================
  // API CONFIGURATION
  // ============================================================================
  
  /// Base API URL based on environment
  String get baseApiUrl {
    switch (environment) {
      case AppEnvironment.development:
        return '${ApiEndpoints.devBaseUrl}${ApiEndpoints.apiVersion}';
      case AppEnvironment.staging:
        return '${ApiEndpoints.stagingBaseUrl}${ApiEndpoints.apiVersion}';
      case AppEnvironment.production:
        return '${ApiEndpoints.prodBaseUrl}${ApiEndpoints.apiVersion}';
    }
  }

  /// API timeout duration
  Duration get apiTimeout => const Duration(seconds: 30);
  
  /// API retry attempts
  int get apiRetryAttempts => 3;
  
  /// API retry delay
  Duration get apiRetryDelay => const Duration(seconds: 2);

  // ============================================================================
  // FACE DETECTION CONFIGURATION
  // ============================================================================
  
  /// Face detection performance mode
  FaceDetectionMode get faceDetectionMode {
    return isDevelopment 
        ? FaceDetectionMode.accurate 
        : FaceDetectionMode.fast;
  }
  
  /// Face detection timeout
  Duration get faceDetectionTimeout => const Duration(milliseconds: 100);
  
  /// Frame skip count for face detection
  int get faceDetectionFrameSkip => 3;
  
  /// Minimum face size for detection
  double get minFaceSize => 0.1;
  
  /// Face quality threshold
  double get faceQualityThreshold => 0.7;

  // ============================================================================
  // CACHE CONFIGURATION
  // ============================================================================
  
  /// Cache duration for API responses
  Duration get cacheApiDuration => const Duration(minutes: 5);
  
  /// Cache duration for user data
  Duration get cacheUserDuration => const Duration(hours: 1);
  
  /// Cache duration for face detection results
  Duration get cacheFaceDuration => const Duration(minutes: 1);
  
  /// Maximum cache size in MB
  int get maxCacheSizeMB => 50;

  // ============================================================================
  // UI CONFIGURATION
  // ============================================================================
  
  /// Default animation duration
  Duration get defaultAnimationDuration => const Duration(milliseconds: 300);
  
  /// Default page transition duration
  Duration get pageTransitionDuration => const Duration(milliseconds: 250);
  
  /// Default snackbar duration
  Duration get snackbarDuration => const Duration(seconds: 3);
  
  /// Default loading indicator delay
  Duration get loadingIndicatorDelay => const Duration(milliseconds: 500);

  // ============================================================================
  // SECURITY CONFIGURATION
  // ============================================================================
  
  /// Token refresh threshold (refresh when token expires in this duration)
  Duration get tokenRefreshThreshold => const Duration(minutes: 5);
  
  /// Session timeout duration
  Duration get sessionTimeout => const Duration(hours: 8);
  
  /// Maximum login attempts
  int get maxLoginAttempts => 5;
  
  /// Login attempt lockout duration
  Duration get loginLockoutDuration => const Duration(minutes: 15);

  // ============================================================================
  // LOGGING CONFIGURATION
  // ============================================================================
  
  /// Enable debug logging
  bool get enableDebugLogging => isDevelopment;
  
  /// Enable API request/response logging
  bool get enableApiLogging => isDevelopment;
  
  /// Enable error reporting
  bool get enableErrorReporting => isProduction;
  
  /// Enable analytics
  bool get enableAnalytics => isProduction;

  // ============================================================================
  // FEATURE FLAGS
  // ============================================================================
  
  /// Enable face capture feature
  bool get enableFaceCapture => true;
  
  /// Enable user management feature
  bool get enableUserManagement => true;
  
  /// Enable offline mode
  bool get enableOfflineMode => false;
  
  /// Enable biometric authentication
  bool get enableBiometricAuth => true;
  
  /// Enable push notifications
  bool get enablePushNotifications => isProduction;

  // ============================================================================
  // CAMERA CONFIGURATION
  // ============================================================================
  
  /// Default camera resolution
  CameraResolution get defaultCameraResolution => CameraResolution.medium;
  
  /// Camera preview aspect ratio
  double get cameraAspectRatio => 16 / 9;
  
  /// Camera capture timeout
  Duration get cameraCaptureTimeout => const Duration(seconds: 10);
  
  /// Maximum number of face captures
  int get maxFaceCaptures => 5;

  // ============================================================================
  // VALIDATION CONFIGURATION
  // ============================================================================
  
  /// Minimum password length
  int get minPasswordLength => 6;
  
  /// Maximum password length
  int get maxPasswordLength => 128;
  
  /// Username minimum length
  int get minUsernameLength => 3;
  
  /// Username maximum length
  int get maxUsernameLength => 50;
  
  /// Maximum file upload size in MB
  int get maxFileUploadSizeMB => 10;

  // ============================================================================
  // METHODS
  // ============================================================================
  
  /// Get configuration as map for debugging
  Map<String, dynamic> toMap() {
    return {
      'environment': environment.name,
      'baseApiUrl': baseApiUrl,
      'apiTimeout': apiTimeout.inSeconds,
      'faceDetectionMode': faceDetectionMode.name,
      'enableDebugLogging': enableDebugLogging,
      'enableFaceCapture': enableFaceCapture,
      'enableUserManagement': enableUserManagement,
      'enableOfflineMode': enableOfflineMode,
      'minPasswordLength': minPasswordLength,
      'maxFileUploadSizeMB': maxFileUploadSizeMB,
    };
  }

  /// Update configuration (for testing purposes)
  void updateConfig({
    String? baseApiUrl,
    Duration? apiTimeout,
    bool? enableDebugLogging,
  }) {
    // This method can be used for testing or dynamic configuration updates
    // Implementation depends on specific requirements
  }
}

/// App environment enumeration
enum AppEnvironment {
  development,
  staging,
  production,
}

/// Face detection mode enumeration
enum FaceDetectionMode {
  fast,
  accurate,
}

/// Camera resolution enumeration
enum CameraResolution {
  low,
  medium,
  high,
  veryHigh,
}

/// App configuration utilities
class AppConfigUtils {
  /// Get environment name as string
  static String getEnvironmentName(AppEnvironment environment) {
    switch (environment) {
      case AppEnvironment.development:
        return 'Development';
      case AppEnvironment.staging:
        return 'Staging';
      case AppEnvironment.production:
        return 'Production';
    }
  }

  /// Get face detection mode description
  static String getFaceDetectionModeDescription(FaceDetectionMode mode) {
    switch (mode) {
      case FaceDetectionMode.fast:
        return 'Fast detection with lower accuracy';
      case FaceDetectionMode.accurate:
        return 'Accurate detection with slower performance';
    }
  }

  /// Get camera resolution description
  static String getCameraResolutionDescription(CameraResolution resolution) {
    switch (resolution) {
      case CameraResolution.low:
        return '480p - Low quality, fast processing';
      case CameraResolution.medium:
        return '720p - Medium quality, balanced performance';
      case CameraResolution.high:
        return '1080p - High quality, slower processing';
      case CameraResolution.veryHigh:
        return '4K - Very high quality, slowest processing';
    }
  }
}

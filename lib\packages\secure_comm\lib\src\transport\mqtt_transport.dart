import 'dart:convert';
import 'dart:async';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'transport_interface.dart';
import 'http_transport.dart';
import '../models/secure_message.dart';
import '../models/device_registration.dart';
import '../exceptions.dart';

/// MQTT transport implementation for secure communication
class MqttTransport implements TransportInterface {
  /// MQTT broker host
  final String brokerHost;

  /// MQTT broker port
  final int brokerPort;

  /// MQTT client ID
  final String clientId;

  /// Username for MQTT authentication
  final String? username;

  /// Password for MQTT authentication
  final String? password;

  /// Whether to use secure connection (TLS)
  final bool useSecureConnection;

  /// Base topic for communication
  final String baseTopic;

  /// HTTP fallback for registration (MQTT doesn't support request-response well)
  final String? httpFallbackUrl;

  /// MQTT client instance
  MqttServerClient? _client;

  /// Response completers for request-response pattern
  final Map<String, Completer<SecureResponse>> _responseCompleters = {};

  /// Connection status
  bool _isConnected = false;

  /// Creates a new MQTT transport
  MqttTransport({
    required this.brokerHost,
    this.brokerPort = 1883,
    required this.clientId,
    this.username,
    this.password,
    this.useSecureConnection = false,
    this.baseTopic = 'secure_comm',
    this.httpFallbackUrl,
  });

  @override
  String get transportType => 'mqtt';

  @override
  bool get isConnected => _isConnected;

  /// Connect to MQTT broker
  Future<void> connect() async {
    if (_isConnected) return;

    try {
      _client = MqttServerClient.withPort(brokerHost, clientId, brokerPort);
      _client!.useWebSocket = false;
      _client!.secure = useSecureConnection;
      _client!.keepAlivePeriod = 60;
      _client!.autoReconnect = true;

      if (username != null && password != null) {
        _client!.connectionMessage = MqttConnectMessage()
            .withClientIdentifier(clientId)
            .authenticateAs(username!, password!)
            .startClean()
            .withWillQos(MqttQos.atLeastOnce);
      }

      final connMessage = MqttConnectMessage()
          .withClientIdentifier(clientId)
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);
      
      _client!.connectionMessage = connMessage;

      await _client!.connect();
      
      if (_client!.connectionStatus!.state == MqttConnectionState.connected) {
        _isConnected = true;
        _setupSubscriptions();
      } else {
        throw TransportException('MQTT connection failed: ${_client!.connectionStatus}');
      }

    } catch (e) {
      throw TransportException('MQTT connection error', e);
    }
  }

  /// Setup MQTT subscriptions for responses
  void _setupSubscriptions() {
    if (_client == null) return;

    // Subscribe to response topic
    final responseTopic = '$baseTopic/response/$clientId';
    _client!.subscribe(responseTopic, MqttQos.atLeastOnce);

    // Listen for responses
    _client!.updates!.listen((List<MqttReceivedMessage<MqttMessage>> messages) {
      for (final message in messages) {
        _handleIncomingMessage(message);
      }
    });
  }

  /// Handle incoming MQTT messages
  void _handleIncomingMessage(MqttReceivedMessage<MqttMessage> receivedMessage) {
    final topic = receivedMessage.topic;
    final payload = MqttPublishPayload.bytesToStringAsString(
      (receivedMessage.payload as MqttPublishMessage).payload.message,
    );

    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final messageId = data['message_id'] as String?;
      
      if (messageId != null && _responseCompleters.containsKey(messageId)) {
        final response = SecureResponse.fromJson(data);
        _responseCompleters[messageId]!.complete(response);
        _responseCompleters.remove(messageId);
      }
    } catch (e) {
      print('Failed to parse MQTT response: $e');
    }
  }

  @override
  Future<DeviceRegistrationResponse> registerDevice(
    DeviceRegistrationRequest request,
  ) async {
    // MQTT doesn't handle request-response well, use HTTP fallback if available
    if (httpFallbackUrl != null) {
      // Use HTTP transport for registration
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        return await httpTransport.registerDevice(request);
      } finally {
        await httpTransport.dispose();
      }
    }

    throw TransportException('MQTT registration requires HTTP fallback URL');
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    // Use HTTP fallback for token refresh
    if (httpFallbackUrl != null) {
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        return await httpTransport.refreshToken(refreshToken);
      } finally {
        await httpTransport.dispose();
      }
    }

    throw TransportException('MQTT token refresh requires HTTP fallback URL');
  }

  @override
  Future<SecureResponse> sendMessage({
    required SecureMessage message,
    required String accessToken,
  }) async {
    if (!_isConnected) {
      await connect();
    }

    if (_client == null) {
      throw TransportException('MQTT client not initialized');
    }

    try {
      // Create message with response topic
      final messageData = message.toJson();
      messageData['response_topic'] = '$baseTopic/response/$clientId';
      messageData['access_token'] = accessToken;

      // Publish message
      final topic = '$baseTopic/message/${message.type}';
      final payload = jsonEncode(messageData);
      
      _client!.publishMessage(
        topic,
        MqttQos.atLeastOnce,
        MqttClientPayloadBuilder().addString(payload).payload!,
      );

      // Wait for response if message has ID
      if (message.messageId != null) {
        final completer = Completer<SecureResponse>();
        _responseCompleters[message.messageId!] = completer;

        // Set timeout
        Timer(Duration(seconds: 30), () {
          if (!completer.isCompleted) {
            _responseCompleters.remove(message.messageId);
            completer.completeError(TimeoutException('MQTT response timeout'));
          }
        });

        return await completer.future;
      } else {
        // Return success response for fire-and-forget messages
        return SecureResponse(
          success: true,
          timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
      }

    } catch (e) {
      throw TransportException('MQTT message send failed', e);
    }
  }

  @override
  Future<SecureResponse> sendPlainTextMessage(Map<String, dynamic> message) async {
    if (!_isConnected) {
      await connect();
    }

    if (_client == null) {
      throw TransportException('MQTT client not initialized');
    }

    try {
      // Add response topic for plain text messages
      final messageData = Map<String, dynamic>.from(message);
      messageData['response_topic'] = '$baseTopic/response/$clientId';

      final payload = jsonEncode(messageData);
      final topic = '$baseTopic/plain';

      _client!.publishMessage(
        topic,
        MqttQos.atLeastOnce,
        MqttClientPayloadBuilder().addString(payload).payload!,
      );

      // Return success response for plain text messages
      return SecureResponse(
        success: true,
        timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

    } catch (e) {
      throw TransportException('MQTT plain text message send failed', e);
    }
  }

  @override
  Future<void> revokeCredentials(String accessToken) async {
    // Use HTTP fallback for credential revocation
    if (httpFallbackUrl != null) {
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        await httpTransport.revokeCredentials(accessToken);
      } finally {
        await httpTransport.dispose();
      }
    }

    // Also disconnect from MQTT
    await disconnect();
  }

  /// Disconnect from MQTT broker
  Future<void> disconnect() async {
    if (_client != null && _isConnected) {
      _client!.disconnect();
      _isConnected = false;
    }
  }

  @override
  Future<void> dispose() async {
    // Complete any pending responses with error
    for (final completer in _responseCompleters.values) {
      if (!completer.isCompleted) {
        completer.completeError(TransportException('Transport disposed'));
      }
    }
    _responseCompleters.clear();

    await disconnect();
    _client = null;
  }
}

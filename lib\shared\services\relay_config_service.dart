import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
/// Import RelayDeviceConfig from relay management service
import 'relay_management_service.dart';

/// Service for managing relay configuration settings
class RelayConfigService {
  static const String _keyBaudRate = 'relay_baud_rate';
  static const String _keyDeviceName = 'relay_device_name';
  static const String _keyRelayCount = 'relay_count';
  static const String _keyAutoConnect = 'relay_auto_connect';
  
  // Default values for USB-TTL relay modules
  static const int _defaultBaudRate = 9600;
  static const String _defaultDeviceName = 'USB-TTL Relay';
  static const int _defaultRelayCount = 4;
  static const bool _defaultAutoConnect = true;

  static RelayConfigService? _instance;
  static RelayConfigService get instance {
    _instance ??= RelayConfigService._();
    return _instance!;
  }
  
  RelayConfigService._();

  SharedPreferences? _prefs;

  /// Initialize the service
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Get baud rate setting
  int get baudRate {
    _ensureInitialized();
    return _prefs!.getInt(_keyBaudRate) ?? _defaultBaudRate;
  }

  /// Set baud rate setting
  Future<void> setBaudRate(int baudRate) async {
    _ensureInitialized();
    await _prefs!.setInt(_keyBaudRate, baudRate);
    
    if (kDebugMode) {
      print('🔧 Relay baud rate updated: $baudRate');
    }
  }

  /// Get device name setting
  String get deviceName {
    _ensureInitialized();
    return _prefs!.getString(_keyDeviceName) ?? _defaultDeviceName;
  }

  /// Set device name setting
  Future<void> setDeviceName(String name) async {
    _ensureInitialized();
    await _prefs!.setString(_keyDeviceName, name);
    
    if (kDebugMode) {
      print('🔧 Relay device name updated: $name');
    }
  }

  /// Get relay count setting
  int get relayCount {
    _ensureInitialized();
    return _prefs!.getInt(_keyRelayCount) ?? _defaultRelayCount;
  }

  /// Set relay count setting
  Future<void> setRelayCount(int count) async {
    _ensureInitialized();
    await _prefs!.setInt(_keyRelayCount, count);
    
    if (kDebugMode) {
      print('🔧 Relay count updated: $count');
    }
  }

  /// Get auto connect setting
  bool get autoConnect {
    _ensureInitialized();
    return _prefs!.getBool(_keyAutoConnect) ?? _defaultAutoConnect;
  }

  /// Set auto connect setting
  Future<void> setAutoConnect(bool enabled) async {
    _ensureInitialized();
    await _prefs!.setBool(_keyAutoConnect, enabled);
    
    if (kDebugMode) {
      print('🔧 Relay auto connect updated: $enabled');
    }
  }

  /// Get all relay configuration as a map
  Map<String, dynamic> getAllSettings() {
    _ensureInitialized();
    return {
      'baudRate': baudRate,
      'deviceName': deviceName,
      'relayCount': relayCount,
      'autoConnect': autoConnect,
    };
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _ensureInitialized();
    
    await _prefs!.remove(_keyBaudRate);
    await _prefs!.remove(_keyDeviceName);
    await _prefs!.remove(_keyRelayCount);
    await _prefs!.remove(_keyAutoConnect);
    
    if (kDebugMode) {
      print('🔧 Relay settings reset to defaults');
      print('   Baud rate: $_defaultBaudRate');
      print('   Device name: $_defaultDeviceName');
      print('   Relay count: $_defaultRelayCount');
      print('   Auto connect: $_defaultAutoConnect');
    }
  }

  /// Get common baud rate options for USB-TTL modules
  static List<int> get commonBaudRates => [
    1200,
    2400,
    4800,
    9600,   // Most common for USB-TTL relay modules
    19200,
    38400,
    57600,
    115200, // Common for ESP32/Arduino
  ];

  /// Get baud rate description
  static String getBaudRateDescription(int baudRate) {
    switch (baudRate) {
      case 9600:
        return '9600 (USB-TTL Relay Default)';
      case 115200:
        return '115200 (ESP32/Arduino)';
      case 57600:
        return '57600 (High Speed)';
      case 38400:
        return '38400 (Medium Speed)';
      case 19200:
        return '19200 (Medium Speed)';
      case 4800:
        return '4800 (Low Speed)';
      case 2400:
        return '2400 (Very Low Speed)';
      case 1200:
        return '1200 (Ultra Low Speed)';
      default:
        return '$baudRate bps';
    }
  }

  /// Validate baud rate
  static bool isValidBaudRate(int baudRate) {
    return commonBaudRates.contains(baudRate);
  }

  /// Ensure service is initialized
  void _ensureInitialized() {
    if (_prefs == null) {
      throw StateError('RelayConfigService not initialized. Call initialize() first.');
    }
  }

  /// Create RelayDeviceConfig from current settings
  RelayDeviceConfig createDeviceConfig({String? customDeviceId}) {
    return RelayDeviceConfig(
      deviceId: customDeviceId ?? 'usb-ttl-relay-${DateTime.now().millisecondsSinceEpoch}',
      deviceName: deviceName,
      relayCount: relayCount,
      baudRate: baudRate,
    );
  }
}

/// Configuration for relay throttling and timing control
class RelayThrottleConfig {
  // LED Control (R0) - Face Detection Indicator
  static const String _keyLedOnDuration = 'relay_led_on_duration';
  static const String _keyLedCooldown = 'relay_led_cooldown';
  static const String _keyLedMaxDuration = 'relay_led_max_duration';
  static const String _keyLedAutoControl = 'relay_led_auto_control';

  // Door Control (R1) - Access Control
  static const String _keyDoorUnlockDuration = 'relay_door_unlock_duration';
  static const String _keyDoorCooldown = 'relay_door_cooldown';
  static const String _keyDoorMaxDuration = 'relay_door_max_duration';
  static const String _keyDoorAutoControl = 'relay_door_auto_control';

  // Default values based on real-world usage
  static const int _defaultLedOnDuration = 3000;      // 3 seconds
  static const int _defaultLedCooldown = 1000;        // 1 second
  static const int _defaultLedMaxDuration = 30000;    // 30 seconds max
  static const bool _defaultLedAutoControl = true;

  static const int _defaultDoorUnlockDuration = 7000;  // 7 seconds
  static const int _defaultDoorCooldown = 3000;       // 3 seconds
  static const int _defaultDoorMaxDuration = 15000;   // 15 seconds max
  static const bool _defaultDoorAutoControl = true;

  final SharedPreferences _prefs;

  RelayThrottleConfig(this._prefs);

  // LED Control Settings
  int get ledOnDuration => _prefs.getInt(_keyLedOnDuration) ?? _defaultLedOnDuration;
  Future<void> setLedOnDuration(int duration) async {
    await _prefs.setInt(_keyLedOnDuration, duration.clamp(1000, _defaultLedMaxDuration));
  }

  int get ledCooldown => _prefs.getInt(_keyLedCooldown) ?? _defaultLedCooldown;
  Future<void> setLedCooldown(int cooldown) async {
    await _prefs.setInt(_keyLedCooldown, cooldown.clamp(500, 10000));
  }

  int get ledMaxDuration => _prefs.getInt(_keyLedMaxDuration) ?? _defaultLedMaxDuration;
  Future<void> setLedMaxDuration(int duration) async {
    await _prefs.setInt(_keyLedMaxDuration, duration.clamp(5000, 60000));
  }

  bool get ledAutoControl => _prefs.getBool(_keyLedAutoControl) ?? _defaultLedAutoControl;
  Future<void> setLedAutoControl(bool enabled) async {
    await _prefs.setBool(_keyLedAutoControl, enabled);
  }

  // Door Control Settings
  int get doorUnlockDuration => _prefs.getInt(_keyDoorUnlockDuration) ?? _defaultDoorUnlockDuration;
  Future<void> setDoorUnlockDuration(int duration) async {
    await _prefs.setInt(_keyDoorUnlockDuration, duration.clamp(3000, _defaultDoorMaxDuration));
  }

  int get doorCooldown => _prefs.getInt(_keyDoorCooldown) ?? _defaultDoorCooldown;
  Future<void> setDoorCooldown(int cooldown) async {
    await _prefs.setInt(_keyDoorCooldown, cooldown.clamp(1000, 10000));
  }

  int get doorMaxDuration => _prefs.getInt(_keyDoorMaxDuration) ?? _defaultDoorMaxDuration;
  Future<void> setDoorMaxDuration(int duration) async {
    await _prefs.setInt(_keyDoorMaxDuration, duration.clamp(5000, 30000));
  }

  bool get doorAutoControl => _prefs.getBool(_keyDoorAutoControl) ?? _defaultDoorAutoControl;
  Future<void> setDoorAutoControl(bool enabled) async {
    await _prefs.setBool(_keyDoorAutoControl, enabled);
  }

  /// Get all throttle settings
  Map<String, dynamic> getAllSettings() {
    return {
      'ledOnDuration': ledOnDuration,
      'ledCooldown': ledCooldown,
      'ledMaxDuration': ledMaxDuration,
      'ledAutoControl': ledAutoControl,
      'doorUnlockDuration': doorUnlockDuration,
      'doorCooldown': doorCooldown,
      'doorMaxDuration': doorMaxDuration,
      'doorAutoControl': doorAutoControl,
    };
  }

  /// Reset all throttle settings to defaults
  Future<void> resetToDefaults() async {
    await _prefs.remove(_keyLedOnDuration);
    await _prefs.remove(_keyLedCooldown);
    await _prefs.remove(_keyLedMaxDuration);
    await _prefs.remove(_keyLedAutoControl);
    await _prefs.remove(_keyDoorUnlockDuration);
    await _prefs.remove(_keyDoorCooldown);
    await _prefs.remove(_keyDoorMaxDuration);
    await _prefs.remove(_keyDoorAutoControl);
  }
}



import 'dart:async';
import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';

import '../core/interfaces/face_detection_engine.dart';
import '../core/models/face_recognition_config.dart';
import '../shared/engines/ultraface_detection_engine.dart';
import 'recognition/terminal_recognition_service.dart';
import 'triggers/side_effect_controller.dart';
import 'sync/terminal_sync_service.dart';
import 'optimization/device_optimizer.dart';

/// Terminal-specific face recognition system optimized for edge devices
class TerminalFaceSystem extends ChangeNotifier {
  // Core components
  FaceDetectionEngine? _detectionEngine;
  TerminalRecognitionService? _recognitionService;
  SideEffectController? _sideEffectController;
  TerminalSyncService? _syncService;
  DeviceOptimizer? _deviceOptimizer;
  
  // Configuration
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  bool _isProcessing = false;
  bool _isOnline = true;
  
  // State
  List<FaceDetection> _detectedFaces = [];
  TerminalRecognitionResult? _lastRecognition;
  int _consecutiveFailures = 0;
  
  // Performance tracking
  int _totalProcessed = 0;
  int _totalRecognized = 0;
  double _averageFPS = 0.0;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isProcessing => _isProcessing;
  bool get isOnline => _isOnline;
  List<FaceDetection> get detectedFaces => List.unmodifiable(_detectedFaces);
  TerminalRecognitionResult? get lastRecognition => _lastRecognition;
  double get averageFPS => _averageFPS;
  int get totalProcessed => _totalProcessed;
  int get totalRecognized => _totalRecognized;
  double get recognitionRate => _totalProcessed > 0 ? _totalRecognized / _totalProcessed : 0.0;
  
  /// Initialize terminal face system
  Future<void> initialize({
    required FaceRecognitionConfig config,
    required TerminalDeviceType deviceType,
  }) async {
    if (_isInitialized) {
      throw StateError('TerminalFaceSystem is already initialized');
    }
    
    try {
      _config = config;
      
      if (kDebugMode) {
        print('🖥️ Initializing TerminalFaceSystem');
        print('   Device Type: ${deviceType.name}');
        print('   Detection Engine: ${config.detectionEngine.name}');
        print('   Target FPS: ${config.targetFPS}');
      }
      
      // Initialize device optimizer first
      _deviceOptimizer = DeviceOptimizer.create(deviceType);
      await _deviceOptimizer!.initialize();
      
      // Apply device-specific optimizations to config
      final optimizedConfig = _deviceOptimizer!.optimizeConfig(config);
      _config = optimizedConfig;
      
      // Initialize detection engine (UltraFace for terminals)
      _detectionEngine = UltraFaceDetectionEngine(config: optimizedConfig);
      await _detectionEngine!.initialize();
      
      // Initialize recognition service
      _recognitionService = TerminalRecognitionService(config: optimizedConfig);
      await _recognitionService!.initialize();
      
      // Initialize side effect controller
      _sideEffectController = SideEffectController.create(deviceType);
      await _sideEffectController!.initialize();
      
      // Initialize sync service
      _syncService = TerminalSyncService(config: optimizedConfig);
      await _syncService!.initialize();
      
      // Listen to network changes
      _syncService!.networkStatusStream.listen(_onNetworkStatusChanged);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ TerminalFaceSystem initialized successfully');
        print('   Optimized FPS: ${optimizedConfig.targetFPS}');
        print('   Memory Limit: ${optimizedConfig.memoryLimit}MB');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize TerminalFaceSystem: $e');
      }
      await dispose();
      rethrow;
    }
  }
  
  /// Process camera image for face detection and recognition
  Future<void> processCameraImage(CameraImage image) async {
    if (!_isInitialized || _isProcessing) return;
    
    _isProcessing = true;
    final stopwatch = Stopwatch()..start();
    
    try {
      // Apply device-specific preprocessing
      final optimizedImage = await _deviceOptimizer!.preprocessImage(image);
      
      // Detect faces
      final faces = await _detectionEngine!.processCameraImage(optimizedImage);
      _detectedFaces = faces;
      
      // Update FPS tracking
      stopwatch.stop();
      _updateFPSTracking(stopwatch.elapsedMilliseconds);
      
      // Process recognition for best face
      if (faces.isNotEmpty) {
        final bestFace = _selectBestFace(faces);
        if (bestFace != null && _shouldTriggerRecognition(bestFace)) {
          await _processRecognition(bestFace);
        }
      }
      
      _totalProcessed++;
      _consecutiveFailures = 0;
      
      notifyListeners();
      
    } catch (e) {
      _consecutiveFailures++;
      
      if (kDebugMode) {
        print('❌ Error processing camera image: $e');
        print('   Consecutive failures: $_consecutiveFailures');
      }
      
      // Reset system if too many failures
      if (_consecutiveFailures > 10) {
        await _resetSystem();
      }
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Process face recognition and trigger side effects
  Future<void> _processRecognition(FaceDetection face) async {
    if (face.croppedFace == null) return;
    
    try {
      // Recognize face (online/offline hybrid)
      final result = await _recognitionService!.recognizeFace(
        face.croppedFace!,
        isOnline: _isOnline,
      );
      
      if (result != null) {
        _lastRecognition = result;
        _totalRecognized++;
        
        if (kDebugMode) {
          print('✅ Terminal Recognition: ${result.userName}');
          print('   Confidence: ${(result.confidence * 100).toStringAsFixed(1)}%');
          print('   Source: ${result.source}');
          print('   Access Level: ${result.accessLevel}');
        }
        
        // Trigger side effects based on recognition result
        await _triggerSideEffects(result);
        
        // Sync recognition event to server (if online)
        if (_isOnline) {
          _syncService!.syncRecognitionEvent(result);
        }
        
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Recognition failed: $e');
      }
    }
  }
  
  /// Trigger side effects based on recognition result
  Future<void> _triggerSideEffects(TerminalRecognitionResult result) async {
    try {
      // Check access permissions
      if (!result.hasAccess) {
        await _sideEffectController!.triggerAccessDenied(result);
        return;
      }
      
      // Trigger access granted effects
      await _sideEffectController!.triggerAccessGranted(result);
      
      if (kDebugMode) {
        print('🔌 Side effects triggered for: ${result.userName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to trigger side effects: $e');
      }
    }
  }
  
  /// Select best face for recognition
  FaceDetection? _selectBestFace(List<FaceDetection> faces) {
    if (faces.isEmpty) return null;
    
    // Sort by quality and confidence
    faces.sort((a, b) {
      final scoreA = a.quality * 0.6 + a.confidence * 0.4;
      final scoreB = b.quality * 0.6 + b.confidence * 0.4;
      return scoreB.compareTo(scoreA);
    });
    
    final bestFace = faces.first;
    
    // Check minimum thresholds
    if (bestFace.quality < _config!.minFaceQuality ||
        bestFace.confidence < _config!.minConfidence) {
      return null;
    }
    
    return bestFace;
  }
  
  /// Check if recognition should be triggered
  bool _shouldTriggerRecognition(FaceDetection face) {
    // Check if recognition service is ready
    if (_recognitionService?.isThrottled == true) return false;
    
    // Check face quality
    if (face.quality < _config!.minFaceQuality) return false;
    
    // Check if face is frontal (important for terminals)
    if (_config!.requireFrontalFace && !face.pose.isFrontal) return false;
    
    return true;
  }
  
  /// Handle network status changes
  void _onNetworkStatusChanged(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      
      if (kDebugMode) {
        print('🌐 Terminal network status: ${isOnline ? "Online" : "Offline"}');
      }
      
      // Switch recognition mode
      _recognitionService?.setOnlineMode(isOnline);
      
      notifyListeners();
    }
  }
  
  /// Update FPS tracking
  void _updateFPSTracking(int processingTimeMs) {
    final currentFPS = processingTimeMs > 0 ? 1000.0 / processingTimeMs : 0.0;
    _averageFPS = _averageFPS == 0.0 ? currentFPS : (_averageFPS * 0.9 + currentFPS * 0.1);
  }
  
  /// Reset system after failures
  Future<void> _resetSystem() async {
    if (kDebugMode) {
      print('🔄 Resetting terminal system after failures...');
    }
    
    try {
      await _detectionEngine?.dispose();
      await _recognitionService?.dispose();
      
      // Reinitialize core components
      _detectionEngine = UltraFaceDetectionEngine(config: _config!);
      await _detectionEngine!.initialize();
      
      _recognitionService = TerminalRecognitionService(config: _config!);
      await _recognitionService!.initialize();
      
      _consecutiveFailures = 0;
      
      if (kDebugMode) {
        print('✅ Terminal system reset complete');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to reset system: $e');
      }
    }
  }
  
  /// Get terminal statistics
  TerminalStats getStats() {
    return TerminalStats(
      totalProcessed: _totalProcessed,
      totalRecognized: _totalRecognized,
      recognitionRate: recognitionRate,
      averageFPS: _averageFPS,
      isOnline: _isOnline,
      consecutiveFailures: _consecutiveFailures,
      memoryUsage: _deviceOptimizer?.getCurrentMemoryUsage() ?? 0,
    );
  }
  
  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isInitialized = false;
    
    await _detectionEngine?.dispose();
    await _recognitionService?.dispose();
    await _sideEffectController?.dispose();
    await _syncService?.dispose();
    await _deviceOptimizer?.dispose();
    
    if (kDebugMode) {
      print('🗑️ TerminalFaceSystem disposed');
    }
    
    super.dispose();
  }
}

/// Terminal recognition result with access control
class TerminalRecognitionResult {
  final String userId;
  final String userName;
  final double confidence;
  final String source; // 'online' or 'offline'
  final AccessLevel accessLevel;
  final bool hasAccess;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;
  
  const TerminalRecognitionResult({
    required this.userId,
    required this.userName,
    required this.confidence,
    required this.source,
    required this.accessLevel,
    required this.hasAccess,
    required this.timestamp,
    this.metadata = const {},
  });
}

/// Access levels for terminal control
enum AccessLevel {
  admin,
  user,
  guest,
  denied,
}

/// Terminal statistics
class TerminalStats {
  final int totalProcessed;
  final int totalRecognized;
  final double recognitionRate;
  final double averageFPS;
  final bool isOnline;
  final int consecutiveFailures;
  final int memoryUsage;
  
  const TerminalStats({
    required this.totalProcessed,
    required this.totalRecognized,
    required this.recognitionRate,
    required this.averageFPS,
    required this.isOnline,
    required this.consecutiveFailures,
    required this.memoryUsage,
  });
  
  @override
  String toString() {
    return 'TerminalStats('
        'processed: $totalProcessed, '
        'recognized: $totalRecognized, '
        'rate: ${(recognitionRate * 100).toStringAsFixed(1)}%, '
        'fps: ${averageFPS.toStringAsFixed(1)}, '
        'online: $isOnline'
        ')';
  }
}

/**
 * Device Naming Utilities for Test Server
 * Supports terminal shortId format and relay device linking
 */

class DeviceNamingUtils {
  // Characters to exclude from shortId generation (confusing characters)
  static EXCLUDED_CHARS = ['0', 'O', '1', 'I', 'L'];
  
  // Valid characters for shortId generation
  static VALID_CHARS = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'.split('').filter(
    char => !DeviceNamingUtils.EXCLUDED_CHARS.includes(char)
  );

  /**
   * Generate terminal shortId with format T-XXXX
   * @returns {string} Terminal shortId (e.g., T-A3B4)
   */
  static generateTerminalShortId() {
    const chars = [];
    for (let i = 0; i < 4; i++) {
      const randomIndex = Math.floor(Math.random() * DeviceNamingUtils.VALID_CHARS.length);
      chars.push(DeviceNamingUtils.VALID_CHARS[randomIndex]);
    }
    return `T-${chars.join('')}`;
  }

  /**
   * Generate relay device ID linked to terminal
   * @param {string} terminalShortId - Terminal shortId (e.g., T-A3B4)
   * @param {number|string} relayIndex - Relay index (number) or name (string)
   * @returns {string} Relay device ID
   */
  static generateRelayDeviceId(terminalShortId, relayIndex) {
    if (!DeviceNamingUtils.isValidTerminalShortId(terminalShortId)) {
      throw new Error(`Invalid terminal shortId format: ${terminalShortId}`);
    }

    if (typeof relayIndex === 'number') {
      // Numbered relay: T-A3B4-R01, T-A3B4-R02
      const paddedIndex = relayIndex.toString().padStart(2, '0');
      return `${terminalShortId}-R${paddedIndex}`;
    } else {
      // Named relay: T-A3B4-DOOR, T-A3B4-ALARM
      return `${terminalShortId}-${relayIndex.toUpperCase()}`;
    }
  }

  /**
   * Extract terminal shortId from relay device ID
   * @param {string} relayDeviceId - Relay device ID
   * @returns {string|null} Terminal shortId or null if not valid relay device
   */
  static extractTerminalShortId(relayDeviceId) {
    if (!relayDeviceId || typeof relayDeviceId !== 'string') {
      return null;
    }

    const parts = relayDeviceId.split('-');
    if (parts.length < 3) {
      return null;
    }

    const potentialTerminalId = `${parts[0]}-${parts[1]}`;
    return DeviceNamingUtils.isValidTerminalShortId(potentialTerminalId) 
      ? potentialTerminalId 
      : null;
  }

  /**
   * Validate terminal shortId format
   * @param {string} terminalShortId - Terminal shortId to validate
   * @returns {boolean} True if valid format
   */
  static isValidTerminalShortId(terminalShortId) {
    if (!terminalShortId || typeof terminalShortId !== 'string') {
      return false;
    }

    const pattern = /^T-[A-Z2-9]{4}$/;
    if (!pattern.test(terminalShortId)) {
      return false;
    }

    // Check no excluded characters
    const shortIdPart = terminalShortId.substring(2);
    return !DeviceNamingUtils.EXCLUDED_CHARS.some(char => shortIdPart.includes(char));
  }

  /**
   * Validate relay device ID format
   * @param {string} relayDeviceId - Relay device ID to validate
   * @returns {boolean} True if valid format
   */
  static isValidRelayDeviceId(relayDeviceId) {
    if (!relayDeviceId || typeof relayDeviceId !== 'string') {
      return false;
    }

    const parts = relayDeviceId.split('-');
    if (parts.length < 3) {
      return false;
    }

    // Check terminal part
    const terminalPart = `${parts[0]}-${parts[1]}`;
    if (!DeviceNamingUtils.isValidTerminalShortId(terminalPart)) {
      return false;
    }

    // Check relay part (either Rnn or custom name)
    const relayPart = parts.slice(2).join('-');
    const numberedPattern = /^R\d{2}$/;
    const namedPattern = /^[A-Z0-9_]+$/;
    
    return numberedPattern.test(relayPart) || namedPattern.test(relayPart);
  }

  /**
   * Get display name for device
   * @param {string} deviceId - Device ID
   * @param {string} deviceName - Optional custom device name
   * @returns {string} Human-readable display name
   */
  static getDisplayName(deviceId, deviceName = null) {
    if (deviceName && deviceName.trim()) {
      return deviceName.trim();
    }

    if (DeviceNamingUtils.isValidTerminalShortId(deviceId)) {
      return `Terminal ${deviceId}`;
    }

    if (DeviceNamingUtils.isValidRelayDeviceId(deviceId)) {
      const terminalId = DeviceNamingUtils.extractTerminalShortId(deviceId);
      const parts = deviceId.split('-');
      const relayPart = parts.slice(2).join('-');
      
      if (relayPart.startsWith('R') && /^R\d{2}$/.test(relayPart)) {
        const relayNumber = parseInt(relayPart.substring(1));
        return `${terminalId} Relay ${relayNumber}`;
      } else {
        return `${terminalId} ${relayPart}`;
      }
    }

    return deviceId; // Fallback to device ID
  }

  /**
   * Check if device is a terminal
   * @param {string} deviceId - Device ID to check
   * @returns {boolean} True if device is a terminal
   */
  static isTerminal(deviceId) {
    return DeviceNamingUtils.isValidTerminalShortId(deviceId);
  }

  /**
   * Check if device is a relay
   * @param {string} deviceId - Device ID to check
   * @returns {boolean} True if device is a relay
   */
  static isRelay(deviceId) {
    return DeviceNamingUtils.isValidRelayDeviceId(deviceId);
  }

  /**
   * Group devices by terminal
   * @param {Map} devices - Map of device ID to device objects
   * @returns {Object} Grouped devices by terminal
   */
  static groupDevicesByTerminal(devices) {
    const grouped = {
      terminals: new Map(),
      relays: new Map(),
      ungrouped: new Map()
    };

    for (const [deviceId, device] of devices) {
      if (DeviceNamingUtils.isTerminal(deviceId)) {
        grouped.terminals.set(deviceId, device);
      } else if (DeviceNamingUtils.isRelay(deviceId)) {
        const terminalId = DeviceNamingUtils.extractTerminalShortId(deviceId);
        if (!grouped.relays.has(terminalId)) {
          grouped.relays.set(terminalId, []);
        }
        grouped.relays.get(terminalId).push({ deviceId, device });
      } else {
        grouped.ungrouped.set(deviceId, device);
      }
    }

    return grouped;
  }

  /**
   * Generate device naming summary
   * @param {string} terminalShortId - Terminal shortId
   * @returns {Object} Naming examples and patterns
   */
  static generateNamingSummary(terminalShortId) {
    if (!DeviceNamingUtils.isValidTerminalShortId(terminalShortId)) {
      throw new Error(`Invalid terminal shortId: ${terminalShortId}`);
    }

    return {
      terminal: {
        id: terminalShortId,
        displayName: DeviceNamingUtils.getDisplayName(terminalShortId)
      },
      relays: {
        numbered: [
          { id: `${terminalShortId}-R01`, displayName: `${terminalShortId} Relay 1` },
          { id: `${terminalShortId}-R02`, displayName: `${terminalShortId} Relay 2` },
          { id: `${terminalShortId}-R03`, displayName: `${terminalShortId} Relay 3` }
        ],
        named: [
          { id: `${terminalShortId}-DOOR`, displayName: `${terminalShortId} DOOR` },
          { id: `${terminalShortId}-ALARM`, displayName: `${terminalShortId} ALARM` },
          { id: `${terminalShortId}-LED`, displayName: `${terminalShortId} LED` }
        ]
      }
    };
  }
}

module.exports = DeviceNamingUtils; 
import 'package:equatable/equatable.dart';
import '../user/user.dart';

/// Domain entity for authentication result
/// 
/// Represents the result of authentication operations in the domain layer
class AuthResult extends Equatable {
  final String accessToken;
  final String? refreshToken;
  final User? user;
  final DateTime? expiresAt;
  final String tokenType;
  final List<String> scopes;
  final Map<String, dynamic> metadata;

  const AuthResult({
    required this.accessToken,
    this.refreshToken,
    this.user,
    this.expiresAt,
    this.tokenType = 'Bearer',
    this.scopes = const [],
    this.metadata = const {},
  });

  /// Copy with new values
  AuthResult copyWith({
    String? accessToken,
    String? refreshToken,
    User? user,
    DateTime? expiresAt,
    String? tokenType,
    List<String>? scopes,
    Map<String, dynamic>? metadata,
  }) {
    return AuthResult(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      user: user ?? this.user,
      expiresAt: expiresAt ?? this.expiresAt,
      tokenType: tokenType ?? this.tokenType,
      scopes: scopes ?? this.scopes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if token is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if token will expire soon (within threshold)
  bool willExpireSoon([Duration threshold = const Duration(minutes: 5)]) {
    if (expiresAt == null) return false;
    final thresholdTime = DateTime.now().add(threshold);
    return thresholdTime.isAfter(expiresAt!);
  }

  /// Get time until expiration
  Duration? get timeUntilExpiration {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// Check if has specific scope
  bool hasScope(String scope) {
    return scopes.contains(scope);
  }

  /// Check if has any of the specified scopes
  bool hasAnyScope(List<String> requiredScopes) {
    return requiredScopes.any((scope) => scopes.contains(scope));
  }

  /// Check if has all of the specified scopes
  bool hasAllScopes(List<String> requiredScopes) {
    return requiredScopes.every((scope) => scopes.contains(scope));
  }

  /// Get metadata value
  T? getMetadata<T>(String key) {
    return metadata[key] as T?;
  }

  /// Check if auth result is valid for use
  bool get isValid {
    return accessToken.isNotEmpty && !isExpired;
  }

  /// Check if refresh is needed
  bool get needsRefresh {
    return isExpired || willExpireSoon();
  }

  /// Get authorization header value
  String get authorizationHeader {
    return '$tokenType $accessToken';
  }

  /// Get user ID if available
  String? get userId => user?.id;

  /// Get user name if available
  String? get userName => user?.name;

  /// Get user email if available
  String? get userEmail => user?.email;

  /// Check if user has specific role
  bool hasRole(String role) {
    return user?.hasRole(role) ?? false;
  }

  /// Check if user has any of the specified roles
  bool hasAnyRole(List<String> roles) {
    return user?.hasAnyRole(roles) ?? false;
  }

  /// Check if user has permission
  bool hasPermission(String permission) {
    return user?.hasPermission(permission) ?? false;
  }

  /// Get session duration
  Duration? get sessionDuration {
    if (expiresAt == null) return null;
    // Assuming session started when token was issued
    // In real implementation, you might have an issuedAt field
    final now = DateTime.now();
    final estimatedIssuedAt = expiresAt!.subtract(const Duration(hours: 8)); // Default session length
    return now.difference(estimatedIssuedAt);
  }

  /// Check if this is a fresh login (token issued recently)
  bool get isFreshLogin {
    final sessionDur = sessionDuration;
    if (sessionDur == null) return false;
    return sessionDur < const Duration(minutes: 5);
  }

  /// Get security level based on token properties
  SecurityLevel get securityLevel {
    if (hasScope('admin') || hasRole('admin')) {
      return SecurityLevel.high;
    } else if (hasScope('write') || hasRole('moderator')) {
      return SecurityLevel.medium;
    } else {
      return SecurityLevel.low;
    }
  }

  /// Validate auth result
  AuthValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Required field validation
    if (accessToken.isEmpty) {
      errors.add('Access token is required');
    }

    // Token expiration validation
    if (isExpired) {
      errors.add('Access token is expired');
    } else if (willExpireSoon()) {
      warnings.add('Access token will expire soon');
    }

    // Token type validation
    if (!['Bearer', 'Basic'].contains(tokenType)) {
      warnings.add('Unusual token type: $tokenType');
    }

    // User validation
    if (user == null) {
      warnings.add('No user information available');
    } else {
      final userValidation = user!.validate();
      if (!userValidation.isValid) {
        warnings.add('User information has issues');
      }
    }

    // Scope validation
    if (scopes.isEmpty) {
      warnings.add('No scopes defined for this token');
    }

    return AuthValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  @override
  List<Object?> get props => [
    accessToken,
    refreshToken,
    user,
    expiresAt,
    tokenType,
    scopes,
    metadata,
  ];

  @override
  String toString() {
    return 'AuthResult(accessToken: [HIDDEN], refreshToken: ${refreshToken != null ? '[HIDDEN]' : 'null'}, user: $user, expiresAt: $expiresAt, tokenType: $tokenType, scopes: $scopes)';
  }
}

/// Security levels for authentication
enum SecurityLevel {
  low,
  medium,
  high,
}

/// Result of auth validation
class AuthValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const AuthValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;

  @override
  String toString() {
    return 'AuthValidationResult(isValid: $isValid, errors: $errors, warnings: $warnings)';
  }
}

/// Extension for SecurityLevel
extension SecurityLevelExtension on SecurityLevel {
  String get displayName {
    switch (this) {
      case SecurityLevel.low:
        return 'Low';
      case SecurityLevel.medium:
        return 'Medium';
      case SecurityLevel.high:
        return 'High';
    }
  }

  int get level {
    switch (this) {
      case SecurityLevel.low:
        return 1;
      case SecurityLevel.medium:
        return 2;
      case SecurityLevel.high:
        return 3;
    }
  }

  bool operator >=(SecurityLevel other) => level >= other.level;
  bool operator <=(SecurityLevel other) => level <= other.level;
  bool operator >(SecurityLevel other) => level > other.level;
  bool operator <(SecurityLevel other) => level < other.level;
}

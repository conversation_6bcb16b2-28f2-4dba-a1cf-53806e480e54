import 'package:get_it/get_it.dart';

final GetIt getIt = GetIt.instance;

/// Mobile Camera Module
/// 
/// This module registers mobile-specific camera dependencies
/// including camera controllers, face detection services,
/// and mobile-specific camera configurations.
/// 
/// Note: This module will be fully implemented when mobile
/// camera and face detection features are migrated.
void registerMobileCameraDependencies() {
  // TODO: Implement when mobile camera features are migrated
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<MobileCameraController>(
  //   () => MobileCameraControllerImpl(),
  // );
  // 
  // getIt.registerLazySingleton<MobileFaceDetectionService>(
  //   () => MobileFaceDetectionServiceImpl(),
  // );
  // 
  // getIt.registerLazySingleton<MobileCameraPermissionService>(
  //   () => MobileCameraPermissionServiceImpl(),
  // );
}

/// Unregister mobile camera dependencies (for testing)
void unregisterMobileCameraDependencies() {
  // TODO: Implement when camera dependencies are added
  // if (getIt.isRegistered<MobileCameraController>()) {
  //   getIt.unregister<MobileCameraController>();
  // }
  // if (getIt.isRegistered<MobileFaceDetectionService>()) {
  //   getIt.unregister<MobileFaceDetectionService>();
  // }
  // if (getIt.isRegistered<MobileCameraPermissionService>()) {
  //   getIt.unregister<MobileCameraPermissionService>();
  // }
}

/// Reset mobile camera module (clear và re-register)
void resetMobileCameraModule() {
  unregisterMobileCameraDependencies();
  registerMobileCameraDependencies();
}

/// Check if mobile camera dependencies are registered
bool areMobileCameraDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<MobileCameraController>() &&
  //        getIt.isRegistered<MobileFaceDetectionService>() &&
  //        getIt.isRegistered<MobileCameraPermissionService>();
  return true; // Placeholder
}

/// Get mobile camera dependencies for debugging
Map<String, bool> getMobileCameraDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'MobileCameraController': getIt.isRegistered<MobileCameraController>(),
    // 'MobileFaceDetectionService': getIt.isRegistered<MobileFaceDetectionService>(),
    // 'MobileCameraPermissionService': getIt.isRegistered<MobileCameraPermissionService>(),
  };
}

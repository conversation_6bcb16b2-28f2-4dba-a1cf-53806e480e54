<div
  class="flex flex-col justify-between items-center w-[375px] h-[815px] overflow-hidden bg-[#f4fbff]"
>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3 py-4"
    >
      <div
        class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
      >
        <div
          class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 h-6 gap-6"
        >
          <div
            class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-3"
          >
            <svg
              width="7"
              height="12"
              viewBox="0 0 7 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-[5px] h-2.5"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M6 1L1.70711 5.29289C1.37377 5.62623 1.20711 5.79289 1.20711 6C1.20711 6.20711 1.37377 6.37377 1.70711 6.70711L6 11"
                stroke="#85888C"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <div
              class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative"
            >
              <p
                class="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#15171a]"
              >
                Thêm mới thành viên
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow gap-4"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4"
    >
      <div
        class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
      >
        <div
          class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative"
        >
          <div
            class="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]"
          ></div>
          <div
            class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]"
            >
              Thông tin thành viên
            </p>
          </div>
        </div>
        <div
          class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4"
        >
          <div
            class="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2"
          >
            <div
              class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative"
            >
              <p
                class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#8f959e]"
              >
                Thông tin chung
              </p>
            </div>
            <div
              class="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 rounded-xl bg-white border border-[#e5e6e7]"
            >
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3 rounded-tl-lg rounded-tr-lg"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Họ và tên
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]"
                  >
                    *
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Nhập họ và tên
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-4"
              >
                <svg
                  width="311"
                  height="2"
                  viewBox="0 0 311 2"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="self-stretch flex-grow-0 flex-shrink-0"
                  preserveAspectRatio="none"
                >
                  <line
                    y1="1"
                    x2="311"
                    y2="1"
                    stroke="#E5E6E7"
                    stroke-width="0.5"
                  ></line>
                </svg>
              </div>
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Email
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]"
                  >
                    *
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Nhập thông tin email
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-4"
              >
                <svg
                  width="311"
                  height="1"
                  viewBox="0 0 311 1"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="self-stretch flex-grow-0 flex-shrink-0"
                  preserveAspectRatio="none"
                >
                  <line
                    y1="0.25"
                    x2="311"
                    y2="0.25"
                    stroke="#E5E6E7"
                    stroke-width="0.5"
                  ></line>
                </svg>
              </div>
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Số điện thoại
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]"
                  >
                    *
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Nhập số điện thoại
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-4"
              >
                <svg
                  width="311"
                  height="1"
                  viewBox="0 0 311 1"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="self-stretch flex-grow-0 flex-shrink-0"
                  preserveAspectRatio="none"
                >
                  <line
                    y1="0.5"
                    x2="311"
                    y2="0.5"
                    stroke="#E5E6E7"
                    stroke-width="0.5"
                  ></line>
                </svg>
              </div>
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Ngày sinh
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Chọn ngày sinh
                    </p>
                  </div>
                  <svg
                    width="14"
                    height="15"
                    viewBox="0 0 14 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                    preserveAspectRatio="none"
                  >
                    <g clip-path="url(#clip0_198_2200)">
                      <path
                        d="M10.5 2.04199V3.20866M3.5 2.04199V3.20866"
                        stroke="#8F959E"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                      <path
                        d="M1.45801 8.01689C1.45801 5.47513 1.45801 4.20425 2.18841 3.41462C2.91881 2.625 4.09438 2.625 6.44551 2.625H7.55384C9.90497 2.625 11.0805 2.625 11.8109 3.41462C12.5413 4.20425 12.5413 5.47513 12.5413 8.01689V8.31644C12.5413 10.8582 12.5413 12.1291 11.8109 12.9187C11.0805 13.7083 9.90497 13.7083 7.55384 13.7083H6.44551C4.09438 13.7083 2.91881 13.7083 2.18841 12.9187C1.45801 12.1291 1.45801 10.8582 1.45801 8.31644V8.01689Z"
                        stroke="#8F959E"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                      <path
                        d="M1.75 5.54199H12.25"
                        stroke="#8F959E"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                    </g>
                    <defs>
                      <clipPath id="clip0_198_2200">
                        <rect
                          width="14"
                          height="14"
                          fill="white"
                          transform="translate(0 0.875)"
                        ></rect>
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
              <div
                class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-4"
              >
                <svg
                  width="311"
                  height="1"
                  viewBox="0 0 311 1"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="self-stretch flex-grow-0 flex-shrink-0"
                  preserveAspectRatio="none"
                >
                  <line
                    y1="0.75"
                    x2="311"
                    y2="0.75"
                    stroke="#E5E6E7"
                    stroke-width="0.5"
                  ></line>
                </svg>
              </div>
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3 rounded-bl-lg rounded-br-lg"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Giới tính
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Chọn giới tính
                    </p>
                  </div>
                  <svg
                    width="14"
                    height="15"
                    viewBox="0 0 14 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M3.5 5.375L6.29289 8.16789C6.62623 8.50123 6.79289 8.66789 7 8.66789C7.20711 8.66789 7.37377 8.50123 7.70711 8.16789L10.5 5.375"
                      stroke="#8F959E"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div
            class="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2"
          >
            <div
              class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative"
            >
              <p
                class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#8f959e]"
              >
                Thông tin tổ chức
              </p>
            </div>
            <div
              class="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 rounded-xl bg-white border border-[#e5e6e7]"
            >
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Đơn vị
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]"
                  >
                    *
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Chọn đơn vị
                    </p>
                  </div>
                  <svg
                    width="14"
                    height="15"
                    viewBox="0 0 14 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M3.5 5.625L6.29289 8.41789C6.62623 8.75123 6.79289 8.91789 7 8.91789C7.20711 8.91789 7.37377 8.75123 7.70711 8.41789L10.5 5.625"
                      stroke="#8F959E"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </svg>
                </div>
              </div>
              <div
                class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-2.5 px-4"
              >
                <svg
                  width="311"
                  height="1"
                  viewBox="0 0 311 1"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="self-stretch flex-grow-0 flex-shrink-0"
                  preserveAspectRatio="none"
                >
                  <line
                    y1="0.25"
                    x2="311"
                    y2="0.25"
                    stroke="#E5E6E7"
                    stroke-width="0.5"
                  ></line>
                </svg>
              </div>
              <div
                class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3 rounded-bl-lg rounded-br-lg"
              >
                <div
                  class="flex justify-start items-center flex-grow relative gap-0.5 pb-[1.25px]"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]"
                  >
                    Vai trò
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]"
                  >
                    *
                  </p>
                </div>
                <div
                  class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2"
                >
                  <div
                    class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden"
                  >
                    <p
                      class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]"
                    >
                      Chọn vai trò
                    </p>
                  </div>
                  <svg
                    width="14"
                    height="15"
                    viewBox="0 0 14 15"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M3.5 5.875L6.29289 8.66789C6.62623 9.00123 6.79289 9.16789 7 9.16789C7.20711 9.16789 7.37377 9.00123 7.70711 8.66789L10.5 5.875"
                      stroke="#8F959E"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
      >
        <div
          class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative"
        >
          <div
            class="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]"
          ></div>
          <div
            class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]"
            >
              Đăng ký dữ liệu khuôn mặt
            </p>
          </div>
        </div>
        <div
          class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-1"
        >
          <div
            class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-1"
          >
            <div
              class="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 h-[102px] relative overflow-hidden gap-2 px-5 py-[13px] rounded-lg bg-white border border-[#008fd3] border-dashed"
            >
              <svg
                width="21"
                height="21"
                viewBox="0 0 21 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="flex-grow-0 flex-shrink-0 w-5 h-5 relative"
                preserveAspectRatio="none"
              >
                <path
                  d="M8.11259 2.83301C5.89069 2.88363 4.5929 3.09514 3.70619 3.97878C2.93012 4.75215 2.6707 5.83931 2.58398 7.58301M12.8887 2.83301C15.1106 2.88363 16.4084 3.09514 17.2951 3.97878C18.0712 4.75215 18.3306 5.83931 18.4173 7.58301M12.8887 18.6663C15.1106 18.6157 16.4084 18.4042 17.2951 17.5206C18.0712 16.7472 18.3306 15.66 18.4173 13.9163M8.11259 18.6663C5.89069 18.6157 4.5929 18.4042 3.70619 17.5206C2.93012 16.7472 2.6707 15.66 2.58398 13.9163"
                  stroke="#008FD3"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></path>
                <path
                  d="M7.16699 14.9163C8.69904 12.7535 12.266 12.6355 13.8337 14.9163M12.5837 8.66634C12.5837 9.81693 11.6509 10.7497 10.5003 10.7497C9.34973 10.7497 8.41699 9.81693 8.41699 8.66634C8.41699 7.51575 9.34973 6.58301 10.5003 6.58301C11.6509 6.58301 12.5837 7.51575 12.5837 8.66634Z"
                  stroke="#008FD3"
                  stroke-width="1.5"
                  stroke-linecap="round"
                ></path>
              </svg>
              <p
                class="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]"
              >
                Đăng ký dữ liệu khuôn mặt
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
      >
        <div
          class="flex justify-center items-center self-stretch flex-grow relative gap-2.5 px-6 py-2.5 rounded-lg bg-[#7ac5e9]"
        >
          <p
            class="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-white"
          >
            Thêm mới
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../../../../shared/providers/face_detection_provider.dart';
import '../../../../core/constants/app_colors.dart';

/// Face statistics overlay for debugging and monitoring
class FaceStatsOverlay extends StatelessWidget {
  final Face? detectedFace;
  final FaceDetectionProvider? faceDetectionProvider;
  final bool isVisible;
  final int totalFacesDetected;
  final int capturedImagesCount;

  const FaceStatsOverlay({
    super.key,
    this.detectedFace,
    this.faceDetectionProvider,
    this.isVisible = false,
    this.totalFacesDetected = 0,
    this.capturedImagesCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      top: 80,
      left: 20,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatRow('Faces:', totalFacesDetected.toString()),
            _buildStatRow('Captured:', capturedImagesCount.toString()),
            if (detectedFace != null) ...[
              const SizedBox(height: 4),
              _buildStatRow('Size:', _getFaceSize()),
              _buildStatRow('Quality:', _getFaceQuality()),
              _buildStatRow('Angle X:', _getAngleX()),
              _buildStatRow('Angle Y:', _getAngleY()),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _getFaceSize() {
    if (detectedFace == null) return 'N/A';
    final box = detectedFace!.boundingBox;
    return '${box.width.round()}x${box.height.round()}';
  }

  String _getFaceQuality() {
    if (detectedFace == null || faceDetectionProvider == null) return 'N/A';
    final quality = faceDetectionProvider!.getFaceQuality(detectedFace!);
    return '${(quality * 100).round()}%';
  }

  String _getAngleX() {
    if (detectedFace == null) return 'N/A';
    final angle = detectedFace!.headEulerAngleX ?? 0.0;
    return '${angle.round()}°';
  }

  String _getAngleY() {
    if (detectedFace == null) return 'N/A';
    final angle = detectedFace!.headEulerAngleY ?? 0.0;
    return '${angle.round()}°';
  }


}

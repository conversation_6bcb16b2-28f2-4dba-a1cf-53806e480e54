import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Enhanced navigation service for handling post-login and other navigation scenarios
class NavigationService {
  static const NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  const NavigationService._internal();

  /// Navigate to dashboard after successful login
  /// This is the primary post-login navigation method
  static Future<void> navigateToPostLogin(BuildContext context) async {
    try {
      if (kDebugMode) {
        debugPrint('🚀 NavigationService: Navigating to post-login dashboard');
      }

      // Clear any existing navigation stack and go to dashboard
      context.go('/dashboard');

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully navigated to dashboard');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error navigating to dashboard: $e');
      }

      // Fallback navigation
      try {
        context.go('/');
      } catch (fallbackError) {
        if (kDebugMode) {
          debugPrint('❌ NavigationService: Fallback navigation also failed: $fallbackError');
        }
      }
    }
  }

  /// Navigate based on tenant status after login
  /// If currentTenantId is null/empty, go to tenants screen
  /// Otherwise, go to dashboard
  static Future<void> navigateToPostLoginWithTenant(
    BuildContext context, {
    String? currentTenantId,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🚀 NavigationService: Navigating post-login with tenant check');
        debugPrint('Current Tenant ID: $currentTenantId');
      }

      // Check if tenant ID is null, empty, or undefined
      final hasValidTenant = currentTenantId != null &&
                            currentTenantId.isNotEmpty &&
                            currentTenantId != 'undefined';

      if (hasValidTenant) {
        // User has a valid tenant, go to dashboard
        if (kDebugMode) {
          debugPrint('✅ NavigationService: Valid tenant found, navigating to dashboard');
        }
        context.go('/dashboard');
      } else {
        // No valid tenant, go to tenants screen for selection
        if (kDebugMode) {
          debugPrint('⚠️ NavigationService: No valid tenant, navigating to tenants screen');
        }
        context.go('/tenants');
      }

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Post-login tenant navigation completed');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error in tenant-based navigation: $e');
      }

      // Fallback to tenants screen on error
      try {
        context.go('/tenants');
      } catch (fallbackError) {
        if (kDebugMode) {
          debugPrint('❌ NavigationService: Fallback tenant navigation failed: $fallbackError');
        }
        // Last resort - go to dashboard
        context.go('/dashboard');
      }
    }
  }

  /// Navigate to login screen (for logout or authentication required)
  static Future<void> navigateToLogin(BuildContext context, {String? reason}) async {
    try {
      if (kDebugMode) {
        debugPrint('🔐 NavigationService: Navigating to login${reason != null ? ' - Reason: $reason' : ''}');
      }

      // Clear navigation stack and go to login
      context.go('/login');

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully navigated to login');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error navigating to login: $e');
      }
    }
  }

  /// Navigate to splash screen (for app initialization)
  static Future<void> navigateToSplash(BuildContext context) async {
    try {
      if (kDebugMode) {
        debugPrint('💫 NavigationService: Navigating to splash');
      }

      context.go('/splash');

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully navigated to splash');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error navigating to splash: $e');
      }
    }
  }

  /// Handle authentication state changes
  static Future<void> handleAuthStateChange(
    BuildContext context, {
    required bool isAuthenticated,
    String? currentRoute,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 NavigationService: Handling auth state change - Authenticated: $isAuthenticated, Current: $currentRoute');
      }

      if (isAuthenticated) {
        // User is authenticated
        if (currentRoute != null && _isAuthRoute(currentRoute)) {
          // User is on auth route but authenticated, redirect to dashboard
          await navigateToPostLogin(context);
        }
        // If already on protected route, stay there
      } else {
        // User is not authenticated
        if (currentRoute != null && _isProtectedRoute(currentRoute)) {
          // User is on protected route but not authenticated, redirect to login
          await navigateToLogin(context, reason: 'Authentication required');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error handling auth state change: $e');
      }
    }
  }

  /// Navigate with error handling and logging
  static Future<void> navigateWithErrorHandling(
    BuildContext context,
    String route, {
    String? description,
    Map<String, String>? queryParameters,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🧭 NavigationService: Navigating to $route${description != null ? ' - $description' : ''}');
      }

      if (queryParameters != null && queryParameters.isNotEmpty) {
        final uri = Uri(path: route, queryParameters: queryParameters);
        context.go(uri.toString());
      } else {
        context.go(route);
      }

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully navigated to $route');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error navigating to $route: $e');
      }
      
      // Show error to user if needed
      if (context.mounted) {
        _showNavigationError(context, route, e.toString());
      }
    }
  }

  /// Push route with error handling
  static Future<void> pushWithErrorHandling(
    BuildContext context,
    String route, {
    String? description,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('📤 NavigationService: Pushing $route${description != null ? ' - $description' : ''}');
      }

      context.push(route);

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully pushed $route');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error pushing $route: $e');
      }
      
      _showNavigationError(context, route, e.toString());
    }
  }

  /// Replace current route with error handling
  static Future<void> replaceWithErrorHandling(
    BuildContext context,
    String route, {
    String? description,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 NavigationService: Replacing with $route${description != null ? ' - $description' : ''}');
      }

      context.pushReplacement(route);

      if (kDebugMode) {
        debugPrint('✅ NavigationService: Successfully replaced with $route');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error replacing with $route: $e');
      }
      
      _showNavigationError(context, route, e.toString());
    }
  }

  /// Go back with error handling
  static Future<void> goBackWithErrorHandling(BuildContext context) async {
    try {
      if (context.canPop()) {
        if (kDebugMode) {
          debugPrint('⬅️ NavigationService: Going back');
        }
        context.pop();
      } else {
        if (kDebugMode) {
          debugPrint('🏠 NavigationService: Cannot go back, navigating to dashboard');
        }
        await navigateToPostLogin(context);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ NavigationService: Error going back: $e');
      }
      
      // Fallback to dashboard
      if (context.mounted) {
        await navigateToPostLogin(context);
      }
    }
  }

  /// Check if current route is valid
  static bool isValidRoute(String route) {
    final validRoutes = [
      '/',
      '/splash',
      '/login',
      '/dashboard',
      '/main',
      '/tenants',
      '/users',
      '/profile',
      '/settings',
      '/notifications',
      '/tools',
    ];
    
    return validRoutes.contains(route) || 
           route.startsWith('/user-detail/') ||
           route.startsWith('/tenant-detail/') ||
           route.startsWith('/face-result/');
  }

  /// Private helper methods
  static bool _isAuthRoute(String route) {
    final authRoutes = ['/login', '/register', '/forgot-password'];
    return authRoutes.contains(route) || route.startsWith('/forgot-password');
  }

  static bool _isProtectedRoute(String route) {
    final publicRoutes = ['/splash', '/login', '/register', '/forgot-password'];
    return !publicRoutes.contains(route) && !route.startsWith('/forgot-password');
  }

  static void _showNavigationError(BuildContext context, String route, String error) {
    // Check if context is still mounted before showing snackbar
    if (!context.mounted) return;

    if (kDebugMode) {
      // In debug mode, show detailed error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Navigation Error: Failed to navigate to $route\nError: $error'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    } else {
      // In production, show generic error
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã xảy ra lỗi khi điều hướng. Vui lòng thử lại.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
}

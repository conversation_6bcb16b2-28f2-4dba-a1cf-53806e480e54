import '../providers/face_capture_provider.dart';
import '../providers/face_detection_provider.dart';
import '../models/camera_config.dart';
import 'face_detection_overlay.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';

class CameraPreviewWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final VoidCallback? onReload;
  final bool enableFaceDetection;
  final bool showBoundingBoxes;
  final bool showQualityInfo;
  final Function(Face?)? onBestFaceChanged;
  final Widget? overlayWidget;

  const CameraPreviewWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.onReload,
    this.enableFaceDetection = true,
    this.showBoundingBoxes = true,
    this.showQualityInfo = true,
    this.onBestFaceChanged,
    this.overlayWidget,
  });

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget>
    with WidgetsBindingObserver {
  late FaceDetectionProvider _faceDetectionProvider;
  Face? _lastBestFace;
  bool _isDisposed = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Initialize providers with lifecycle awareness
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProviders();
    });
  }

  @override
  void dispose() {
    _isDisposed = true;

    // Clear camera switch callback to prevent memory leaks
    try {
      final cameraProvider = context.read<FaceCaptureProvider>();
      cameraProvider.setOnCameraSwitchedCallback(null);
    } catch (e) {
      debugPrint('⚠️ Error clearing camera switch callback: $e');
    }

    // Cleanup lifecycle awareness
    _cleanupWidget();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _cleanupWidget() async {
    if (_isDisposed) return;

    try {
      // Stop image stream if enabled
      final cameraProvider = context.read<FaceCaptureProvider>();
      if (cameraProvider.isStreamingEnabled) {
        await cameraProvider.toggleImageStream(false);
      }

      // Clear face detection data
      _faceDetectionProvider.clearFaces();

      // Reset state
      _isInitialized = false;
      _lastBestFace = null;

    } catch (e) {
      debugPrint('Error during camera preview cleanup: $e');
    }
  }

  Future<void> _initializeProviders() async {
    if (_isDisposed) return;

    try {
      final cameraProvider = context.read<FaceCaptureProvider>();
      _faceDetectionProvider = context.read<FaceDetectionProvider>();

      // Check if providers are ready (managed by FaceProvidersWrapper)
      if (cameraProvider.isDisposed || _faceDetectionProvider.isDisposed) {
        debugPrint('Providers are disposed, cannot initialize camera preview');
        return;
      }

      // Set up image stream callback after camera is ready
      if (widget.enableFaceDetection && cameraProvider.isCameraReady && !_isDisposed) {
        cameraProvider.setImageStreamCallback(_onImageAvailable);
        // Force start image stream if not already started
        if (!cameraProvider.isStreamingEnabled) {
          await cameraProvider.toggleImageStream(true);
        }

        // Set up camera switch callback to restart face detection pipeline
        cameraProvider.setOnCameraSwitchedCallback(_onCameraSwitched);
      }

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing camera preview providers: $e');
    }
  }

  void _onImageAvailable(CameraImage cameraImage, CameraDescription camera) {
    if (_isDisposed) return; // Prevent operations after disposal

    if (widget.enableFaceDetection && !_faceDetectionProvider.isDisposed) {
      _faceDetectionProvider.detectFacesFromImage(cameraImage, camera);
    }
  }

  // Handle camera switch completion - restart face detection pipeline
  void _onCameraSwitched() async {
    if (_isDisposed || !widget.enableFaceDetection || !mounted) return;

    debugPrint('📷 Camera switched, restarting face detection pipeline...');

    try {
      // Restart the face detection pipeline
      await _faceDetectionProvider.restartPipeline();

      // Check mounted again after async operation
      if (!mounted || _isDisposed) return;

      // Ensure image stream callback is properly set
      final cameraProvider = context.read<FaceCaptureProvider>();
      if (cameraProvider.isCameraReady && !_isDisposed) {
        cameraProvider.setImageStreamCallback(_onImageAvailable);

        // Ensure image stream is active
        if (!cameraProvider.isStreamingEnabled) {
          await cameraProvider.toggleImageStream(true);
        }
      }

      debugPrint('✅ Face detection pipeline restarted after camera switch');
    } catch (e) {
      debugPrint('❌ Error restarting face detection pipeline after camera switch: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_isDisposed) return; // Prevent operations after disposal

    try {
      final cameraProvider = context.read<FaceCaptureProvider>();

      // Check if provider is still valid
      if (cameraProvider.isDisposed) return;

      switch (state) {
        case AppLifecycleState.paused:
          // Camera provider handles lifecycle automatically
          break;
        case AppLifecycleState.resumed:
          // Restart detection if needed
          if (widget.enableFaceDetection &&
              cameraProvider.isCameraReady &&
              !_isDisposed) {
            cameraProvider.setImageStreamCallback(_onImageAvailable);
          }
          break;
        default:
          break;
      }
    } catch (e) {
      debugPrint('Error handling app lifecycle state change: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<FaceCaptureProvider, FaceDetectionProvider>(
      builder: (context, cameraProvider, faceProvider, child) {
        // Monitor best face changes
        if (widget.onBestFaceChanged != null) {
          final currentBestFace = faceProvider.getBestFace();
          if (currentBestFace != _lastBestFace) {
            _lastBestFace = currentBestFace;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              widget.onBestFaceChanged!(currentBestFace);
            });
          }
        }

        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius,
            color: Colors.black,
          ),
          child: widget.borderRadius != null && widget.borderRadius != BorderRadius.zero
            ? ClipRRect(
                borderRadius: widget.borderRadius!,
                child: _buildCameraContent(cameraProvider, faceProvider),
              )
            : _buildCameraContent(cameraProvider, faceProvider),
        );
      },
    );
  }

  Widget _buildCameraContent(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider,
  ) {
    switch (cameraProvider.status) {
      case CameraStatus.initial:
      case CameraStatus.loading:
        return _buildLoadingState();
      case CameraStatus.ready:
        if (cameraProvider.cameraController != null) {
          return _buildCameraPreview(cameraProvider, faceProvider);
        }
        return _buildErrorState('Camera not available');
      case CameraStatus.error:
        return _buildErrorState(cameraProvider.errorMessage ?? 'Camera error');
      case CameraStatus.permissionDenied:
        return _buildPermissionDeniedState();
    }
  }

  Widget _buildLoadingState() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(Color(0xFF008FD3)),
            ),
            SizedBox(height: 16),
            Text(
              'Đang khởi tạo camera...',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraPreview(
    FaceCaptureProvider cameraProvider,
    FaceDetectionProvider faceProvider,
  ) {
    final controller = cameraProvider.cameraController!;

    Widget preview = Stack(
      fit: StackFit.expand,
      children: [
        // Camera preview with proper aspect ratio
        _buildAspectRatioCameraPreview(controller),

        // Face detection overlay
        if (widget.enableFaceDetection && widget.showBoundingBoxes) ...[
          FaceDetectionOverlay(
            faces: faceProvider.faces,
            bestFace: faceProvider.bestFace,
            imageSize: Size(
              controller.value.previewSize!.height, // Note: swapped for camera
              controller.value.previewSize!.width,
            ),
            isFrontCamera:
                controller.description.lensDirection ==
                CameraLensDirection.front,
            getFaceQuality: faceProvider.getFaceQuality,
            child: const SizedBox.expand(),
          ),
        ],

        // Custom overlay widget
        if (widget.overlayWidget != null) widget.overlayWidget!,

        // Face detection info overlay
        if (widget.enableFaceDetection && widget.showQualityInfo)
          _buildFaceInfoOverlay(faceProvider),
      ],
    );

    return preview;
  }

  /// Build camera preview with proper aspect ratio (full width priority)
  Widget _buildAspectRatioCameraPreview(CameraController controller) {
    final screenSize = MediaQuery.of(context).size;
    final previewSize = controller.value.previewSize!;

    // Calculate camera aspect ratio (accounting for rotation)
    final isPortrait = screenSize.height > screenSize.width;
    final cameraWidth = isPortrait ? previewSize.height : previewSize.width;
    final cameraHeight = isPortrait ? previewSize.width : previewSize.height;
    final cameraAspectRatio = cameraWidth / cameraHeight;

    // Calculate preview dimensions with full width priority
    final previewWidth = screenSize.width;
    final previewHeight = previewWidth / cameraAspectRatio;

    return Center(
      child: SizedBox(
        width: previewWidth,
        height: previewHeight,
        child: CameraPreview(controller),
      ),
    );
  }

  Widget _buildFaceInfoOverlay(FaceDetectionProvider faceProvider) {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(color: Colors.white, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final cameraProvider = context.read<FaceCaptureProvider>();
                final config = CameraConfig.faceCapture.copyWith(
                  enableImageStream: widget.enableFaceDetection,
                );
                await cameraProvider.initializeCamera(config);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF008FD3),
                foregroundColor: Colors.white,
              ),
              child: const Text('Thử lại'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionDeniedState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.camera_alt_outlined,
              color: Colors.orange,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'Cần quyền truy cập camera',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Vui lòng cấp quyền camera để sử dụng tính năng này',
              style: TextStyle(color: Colors.white70, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final cameraProvider = context.read<FaceCaptureProvider>();
                final config = CameraConfig.faceCapture.copyWith(
                  enableImageStream: widget.enableFaceDetection,
                );
                await cameraProvider.initializeCamera(config);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF008FD3),
                foregroundColor: Colors.white,
              ),
              child: const Text('Cấp quyền'),
            ),
          ],
        ),
      ),
    );
  }
}

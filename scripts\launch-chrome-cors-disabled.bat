@echo off
echo ========================================
echo   C-Face Terminal - Chrome CORS Bypass
echo ========================================
echo.
echo Starting Chrome with CORS disabled...
echo This allows cross-origin requests for development.
echo.
echo WARNING: Only use this for development!
echo Do NOT browse other websites with this instance.
echo.

REM Create temp directory if it doesn't exist
if not exist "C:\temp\chrome_dev_session" mkdir "C:\temp\chrome_dev_session"

REM Kill existing Chrome processes
taskkill /f /im chrome.exe >nul 2>&1

REM Wait a moment for processes to close
timeout /t 2 /nobreak >nul

REM Launch Chrome with CORS disabled
echo Launching Chrome with flags:
echo --disable-web-security
echo --user-data-dir="C:\temp\chrome_dev_session"
echo --allow-running-insecure-content
echo --disable-features=VizDisplayCompositor
echo.

start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" ^
  --disable-web-security ^
  --user-data-dir="C:\temp\chrome_dev_session" ^
  --allow-running-insecure-content ^
  --disable-features=VizDisplayCompositor ^
  --disable-extensions ^
  --no-first-run ^
  --no-default-browser-check ^
  "http://localhost:8080"

echo.
echo Chrome launched successfully!
echo You should see a warning banner about unsupported command-line flags.
echo This confirms CORS is disabled.
echo.
echo Navigate to: http://localhost:8080
echo.
pause

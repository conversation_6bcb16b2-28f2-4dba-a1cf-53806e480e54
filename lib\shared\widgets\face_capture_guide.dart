import 'dart:io';
import 'package:flutter/material.dart';
import '../core/constants/face_capture_constants.dart';
import '../providers/face_detection_provider.dart';
import 'captured_image_preview_modal.dart';

class FaceCaptureGuide extends StatelessWidget {
  final Map<FaceDirection, String?> capturedImages;
  final FaceDirection? currentDirection;
  final Function(FaceDirection)? onImageTap;
  final Function(FaceDirection)? onRetakeImage;

  const FaceCaptureGuide({
    super.key,
    this.capturedImages = const {},
    this.currentDirection,
    this.onImageTap,
    this.onRetakeImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: FaceCaptureConstants.guidePadding,
      decoration: BoxDecoration(
        color: FaceCaptureConstants.guideBackgroundColor.withValues(
          alpha: FaceCaptureConstants.guideBackgroundOpacity,
        ),
        borderRadius: BorderRadius.circular(FaceCaptureConstants.guideBorderRadius),
      ),
      child: IntrinsicWidth(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top icon - Top direction
            _buildGuideIcon(
              direction: FaceDirection.top,
              arrowDirection: ArrowDirection.up,
              faceType: FaceType.happy,
            ),
            SizedBox(height: FaceCaptureConstants.guideRowSpacing),

            // Middle row with 3 icons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Left direction
                _buildGuideIcon(
                  direction: FaceDirection.left,
                  faceType: FaceType.sad,
                  arrowDirection: ArrowDirection.left,
                ),
                SizedBox(width: FaceCaptureConstants.guideIconSpacing),
                // Front direction
                _buildGuideIcon(
                  direction: FaceDirection.front,
                  faceType: FaceType.happy,
                ),
                SizedBox(width: FaceCaptureConstants.guideIconSpacing),
                // Right direction
                _buildGuideIcon(
                  direction: FaceDirection.right,
                  faceType: FaceType.wink,
                  arrowDirection: ArrowDirection.right,
                ),
              ],
            ),
            SizedBox(height: FaceCaptureConstants.guideRowSpacing),

            // Bottom icon - Bottom direction
            _buildGuideIcon(
              direction: FaceDirection.bottom,
              arrowDirection: ArrowDirection.down,
              faceType: FaceType.happy,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideIcon({
    required FaceDirection direction,
    ArrowDirection? arrowDirection,
    FaceType? faceType,
  }) {
    final imagePath = capturedImages[direction];
    final isCurrentDirection = currentDirection == direction;
    final hasImage = imagePath != null;

    Widget iconContent = Container(
      width: FaceCaptureConstants.guideIconSize,
      height: FaceCaptureConstants.guideIconSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isCurrentDirection
              ? FaceCaptureConstants.guideActiveBorderColor
              : hasImage
                  ? FaceCaptureConstants.guideCapturedBorderColor
                  : FaceCaptureConstants.guideInactiveBorderColor,
          width: FaceCaptureConstants.guideIconBorderWidth,
        ),
      ),
      child: ClipOval(
        child: hasImage
            ? Stack(
                children: [
                  Image.file(
                    File(imagePath),
                    fit: BoxFit.cover,
                    width: FaceCaptureConstants.guideIconSize,
                    height: FaceCaptureConstants.guideIconSize,
                  ),
                  // Overlay để hiển thị icon khi hover/tap
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.visibility,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              )
            : Center(
                child: _buildIconContent(arrowDirection, faceType),
              ),
      ),
    );

    // Nếu có ảnh và có callback, wrap với GestureDetector
    if (hasImage && onImageTap != null) {
      return GestureDetector(
        onTap: () => _handleImageTap(direction, imagePath),
        child: iconContent,
      );
    }

    return iconContent;
  }

  void _handleImageTap(FaceDirection direction, String imagePath) {
    if (onImageTap != null) {
      onImageTap!(direction);
    }
  }

  Widget _buildIconContent(ArrowDirection? arrowDirection, FaceType? faceType) {
    // For left and right arrows, arrange horizontally
    if (arrowDirection == ArrowDirection.left || arrowDirection == ArrowDirection.right) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (arrowDirection == ArrowDirection.left) ...[
            _buildArrowIcon(arrowDirection!),
            const SizedBox(width: 2),
          ],
          if (faceType != null) _buildFaceIcon(faceType),
          if (arrowDirection == ArrowDirection.right) ...[
            const SizedBox(width: 2),
            _buildArrowIcon(arrowDirection!),
          ],
        ],
      );
    }

    // For up and down arrows, arrange vertically
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (arrowDirection == ArrowDirection.up) ...[
          _buildArrowIcon(arrowDirection!),
          SizedBox(height: FaceCaptureConstants.iconContentSpacing),
        ],
        if (faceType != null) _buildFaceIcon(faceType),
        if (arrowDirection == ArrowDirection.down) ...[
          SizedBox(height: FaceCaptureConstants.iconContentSpacing),
          _buildArrowIcon(arrowDirection!),
        ],
      ],
    );
  }

  Widget _buildArrowIcon(ArrowDirection direction) {
    return SizedBox(
      width: FaceCaptureConstants.arrowIconSize,
      height: FaceCaptureConstants.arrowIconSize,
      child: CustomPaint(
        painter: _ArrowPainter(direction: direction),
      ),
    );
  }

  Widget _buildFaceIcon(FaceType type) {
    return SizedBox(
      width: FaceCaptureConstants.faceIconSize,
      height: FaceCaptureConstants.faceIconSize,
      child: CustomPaint(
        painter: _FacePainter(type: type),
      ),
    );
  }
}

enum ArrowDirection { up, down, left, right }
enum FaceType { happy, sad, wink }

class _ArrowPainter extends CustomPainter {
  final ArrowDirection direction;

  _ArrowPainter({required this.direction});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final path = Path();

    switch (direction) {
      case ArrowDirection.up:
        // Vertical line
        path.moveTo(size.width / 2, size.height * 0.8);
        path.lineTo(size.width / 2, size.height * 0.2);
        // Arrow head
        path.moveTo(size.width * 0.3, size.height * 0.4);
        path.lineTo(size.width / 2, size.height * 0.2);
        path.lineTo(size.width * 0.7, size.height * 0.4);
        break;
      case ArrowDirection.down:
        // Vertical line
        path.moveTo(size.width / 2, size.height * 0.2);
        path.lineTo(size.width / 2, size.height * 0.8);
        // Arrow head
        path.moveTo(size.width * 0.3, size.height * 0.6);
        path.lineTo(size.width / 2, size.height * 0.8);
        path.lineTo(size.width * 0.7, size.height * 0.6);
        break;
      case ArrowDirection.left:
        // Horizontal line
        path.moveTo(size.width * 0.8, size.height / 2);
        path.lineTo(size.width * 0.2, size.height / 2);
        // Arrow head
        path.moveTo(size.width * 0.4, size.height * 0.3);
        path.lineTo(size.width * 0.2, size.height / 2);
        path.lineTo(size.width * 0.4, size.height * 0.7);
        break;
      case ArrowDirection.right:
        // Horizontal line
        path.moveTo(size.width * 0.2, size.height / 2);
        path.lineTo(size.width * 0.8, size.height / 2);
        // Arrow head
        path.moveTo(size.width * 0.6, size.height * 0.3);
        path.lineTo(size.width * 0.8, size.height / 2);
        path.lineTo(size.width * 0.6, size.height * 0.7);
        break;
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _FacePainter extends CustomPainter {
  final FaceType type;

  _FacePainter({required this.type});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Draw circle
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      size.width / 2 - 1,
      paint,
    );

    final eyePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;

    switch (type) {
      case FaceType.happy:
        // Draw both eyes
        canvas.drawCircle(
          Offset(size.width * 0.35, size.height * 0.35),
          1,
          eyePaint,
        );
        canvas.drawCircle(
          Offset(size.width * 0.65, size.height * 0.35),
          1,
          eyePaint,
        );
        // Draw smile
        final smilePath = Path();
        smilePath.addArc(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height * 0.6),
            width: size.width * 0.4,
            height: size.height * 0.2,
          ),
          0,
          3.14159,
        );
        canvas.drawPath(smilePath, paint);
        break;
      case FaceType.sad:
        // Draw one eye
        canvas.drawCircle(
          Offset(size.width * 0.35, size.height * 0.35),
          1,
          eyePaint,
        );
        // Draw sad mouth (curved line)
        final mouthPath = Path();
        mouthPath.moveTo(size.width * 0.3, size.height * 0.7);
        mouthPath.quadraticBezierTo(
          size.width * 0.2,
          size.height * 0.6,
          size.width * 0.1,
          size.height * 0.65,
        );
        canvas.drawPath(mouthPath, paint);
        break;
      case FaceType.wink:
        // Draw one eye
        canvas.drawCircle(
          Offset(size.width * 0.65, size.height * 0.35),
          1,
          eyePaint,
        );
        // Draw wink mouth
        final mouthPath = Path();
        mouthPath.moveTo(size.width * 0.6, size.height * 0.7);
        mouthPath.quadraticBezierTo(
          size.width * 0.75,
          size.height * 0.6,
          size.width * 0.9,
          size.height * 0.65,
        );
        canvas.drawPath(mouthPath, paint);
        break;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

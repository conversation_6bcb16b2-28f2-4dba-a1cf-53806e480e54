import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../apps/mobile/routes/mobile_route_names.dart';
import '../../icons/tab_icons.dart';

// Define colors from Tailwind
const Color colorPrimary = Color(0xFF008FD3);
const Color colorInactive = Color(0xFF9CA5B3);
const Color colorBackground = Colors.white;

class TabsBar extends StatefulWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;

  const TabsBar({super.key, required this.currentIndex, required this.onTap});

  @override
  State<TabsBar> createState() => _TabsBarState();
}

class _TabsBarState extends State<TabsBar> {
  @override
  Widget build(BuildContext context) {
    // p-3 (padding 12px)
    // gap-3 between items (12px)
    // box-shadow: 0px -4px 24px 0 rgba(16,55,99,0.16);

    return Container(
      padding: const EdgeInsets.all(12.0), // p-3
      decoration: BoxDecoration(
        color: colorBackground,
        boxShadow: [
          BoxShadow(
            color: const Color(
              0xFF103763,
            ).withValues(alpha: 0.16), // rgba(16,55,99,0.16)
            offset: const Offset(0, -4), // 0px -4px
            blurRadius: 24.0, // 24px
          ),
        ],
      ),
      child: Row(
        // flex justify-start items-start (items-start is default for Row crossAxisAlignment)
        // self-stretch is handled by Row's default behavior within constraints
        // gap-3
        mainAxisAlignment: MainAxisAlignment
            .spaceAround, // Use spaceAround or spaceBetween for typical nav bars
        crossAxisAlignment: CrossAxisAlignment.start, // Matches items-start
        children: <Widget>[
          _buildNavItem(
            icon: HomeIcon(isActive: widget.currentIndex == 0),
            label: 'Trang chủ',
            index: 0,
            isActive: widget.currentIndex == 0,
          ),
          _buildNavItem(
            icon: ToolsIcon(isActive: widget.currentIndex == 1),
            label: 'Công cụ',
            index: 1,
            isActive: widget.currentIndex == 1,
          ),
          _buildNavItem(
            icon: NotificationIcon(isActive: widget.currentIndex == 2),
            label: 'Thông báo',
            index: 2,
            isActive: widget.currentIndex == 2,
          ),
          _buildNavItem(
            icon: ProfileIcon(isActive: widget.currentIndex == 3),
            label: 'Tài khoản',
            index: 3,
            isActive: widget.currentIndex == 3,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required Widget icon,
    required String label,
    required int index,
    required bool isActive,
  }) {
    final Color itemColor = isActive ? colorPrimary : colorInactive;

    // flex-grow for each item, making them share space equally
    return Expanded(
      child: InkWell(
        onTap: () {
          // Call the parent onTap callback
          widget.onTap(index);

          // Navigate to appropriate screen based on index
          switch (index) {
            case 0: // Trang chủ
              context.go(MobileRouteNames.dashboard);
              break;
            case 1: // Công cụ
              context.go(MobileRouteNames.tools);
              break;
            case 2: // Thông báo
              context.go(MobileRouteNames.notifications);
              break;
            case 3: // Tài khoản
              context.go(MobileRouteNames.profile);
              break;
          }
        },
        borderRadius: BorderRadius.circular(
          8,
        ), // Optional: for ink splash shape
        child: Column(
          // flex flex-col justify-center items-center gap-0.5
          mainAxisSize: MainAxisSize.min, // Important for Column in Row
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            icon,
            const SizedBox(height: 2.0), // gap-0.5 (0.5 * 4px = 2px)
            Text(
              label,
              style: TextStyle(
                fontSize: 10.0, // text-[10px]
                fontWeight: FontWeight.w500, // font-medium
                color: itemColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/device_management_service.dart';

/// Widget for controlling device management settings including screen brightness
class DeviceManagementWidget extends StatefulWidget {
  const DeviceManagementWidget({super.key});

  @override
  State<DeviceManagementWidget> createState() => _DeviceManagementWidgetState();
}

class _DeviceManagementWidgetState extends State<DeviceManagementWidget> {
  final DeviceManagementService _deviceService = DeviceManagementService.instance;
  
  double _brightness = 0.5;
  bool _isWakeLockEnabled = false;
  Map<String, dynamic> _deviceInfo = {};
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _loadDeviceStatus();
  }
  
  Future<void> _loadDeviceStatus() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      _brightness = await _deviceService.getBrightness();
      _isWakeLockEnabled = _deviceService.isWakeLockEnabled;
      _deviceInfo = await _deviceService.getDeviceInfo();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load device status: $e');
      }
    }
    
    setState(() {
      _isLoading = false;
    });
  }
  
  Future<void> _setBrightness(double value) async {
    try {
      await _deviceService.setBrightness(value);
      setState(() {
        _brightness = value;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set brightness: $e');
      }
    }
  }
  
  Future<void> _toggleWakeLock() async {
    try {
      if (_isWakeLockEnabled) {
        await _deviceService.disableWakeLock();
      } else {
        await _deviceService.enableWakeLock();
      }
      
      setState(() {
        _isWakeLockEnabled = _deviceService.isWakeLockEnabled;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to toggle wake lock: $e');
      }
    }
  }
  
  Future<void> _resetBrightness() async {
    try {
      await _deviceService.resetBrightness();
      _brightness = await _deviceService.getBrightness();
      setState(() {});
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to reset brightness: $e');
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.settings, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Device Management',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Screen Brightness Control
            _buildBrightnessControl(),
            
            const SizedBox(height: 16),
            
            // Wake Lock Control
            _buildWakeLockControl(),
            
            const SizedBox(height: 16),
            
            // Device Information
            _buildDeviceInfo(),
            
            const SizedBox(height: 16),
            
            // Control Buttons
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBrightnessControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.brightness_6, size: 20),
            const SizedBox(width: 8),
            Text(
              'Screen Brightness: ${(_brightness * 100).toInt()}%',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(Icons.brightness_low, size: 16),
            Expanded(
              child: Slider(
                value: _brightness,
                min: 0.1,
                max: 1.0,
                divisions: 9,
                onChanged: _setBrightness,
                activeColor: Colors.blue,
              ),
            ),
            const Icon(Icons.brightness_high, size: 16),
          ],
        ),
      ],
    );
  }
  
  Widget _buildWakeLockControl() {
    return Row(
      children: [
        Icon(
          _isWakeLockEnabled ? Icons.screen_lock_portrait : Icons.screen_lock_portrait_outlined,
          size: 20,
          color: _isWakeLockEnabled ? Colors.green : Colors.grey,
        ),
        const SizedBox(width: 8),
        const Text(
          'Keep Screen On',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        const Spacer(),
        Switch(
          value: _isWakeLockEnabled,
          onChanged: (_) => _toggleWakeLock(),
          activeColor: Colors.green,
        ),
      ],
    );
  }
  
  Widget _buildDeviceInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.info, size: 20),
            SizedBox(width: 8),
            Text(
              'Device Information',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            children: [
              _buildInfoRow('Platform', _deviceInfo['platform'] ?? 'Unknown'),
              _buildInfoRow('Model', _deviceInfo['model'] ?? 'Unknown'),
              _buildInfoRow('Manufacturer', _deviceInfo['manufacturer'] ?? 'Unknown'),
              _buildInfoRow('Android Version', _deviceInfo['version'] ?? 'Unknown'),
              _buildInfoRow('SDK Level', '${_deviceInfo['sdk'] ?? 'Unknown'}'),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildControlButtons() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ElevatedButton.icon(
          onPressed: _resetBrightness,
          icon: const Icon(Icons.refresh, size: 16),
          label: const Text('Reset Brightness'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
        
        ElevatedButton.icon(
          onPressed: _loadDeviceStatus,
          icon: const Icon(Icons.sync, size: 16),
          label: const Text('Refresh Status'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/foundation.dart';
import '../../../shared/services/relay_api_service.dart' as api;
import '../../../shared/services/http_client_service.dart';
import '../../../shared/core/config/configuration_manager.dart';
import '../../../shared/core/config/relay_config_parameters.dart';
import 'face_recognition_side_effects_provider.dart';

/// Provider for auto-registering relay devices with server
/// 
/// This provider handles automatic registration of relay devices
/// when terminal device connects to server, following the naming convention
/// from server.js (T-xxx for terminal, T-xxx-Rxx for relays)
class DeviceAutoRegistrationProvider extends ChangeNotifier {
  static DeviceAutoRegistrationProvider? _instance;
  static DeviceAutoRegistrationProvider get instance => 
      _instance ??= DeviceAutoRegistrationProvider._();

  DeviceAutoRegistrationProvider._();

  final api.RelayApiService _apiService = api.RelayApiService.instance;
  
  // Registration state
  bool _isInitialized = false;
  bool _isRegistering = false;
  String? _terminalDeviceId;
  List<String> _registeredRelayDevices = [];
  List<String> _pendingRegistrations = [];
  String? _lastError;
  
  // Configuration
  bool _autoRegisterEnabled = true;
  int _defaultRelayCount = 4;
  List<String> _relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];
  String _deviceNamePrefix = 'Terminal Relay';

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isRegistering => _isRegistering;
  String? get terminalDeviceId => _terminalDeviceId;
  List<String> get registeredRelayDevices => List.unmodifiable(_registeredRelayDevices);
  List<String> get pendingRegistrations => List.unmodifiable(_pendingRegistrations);
  String? get lastError => _lastError;
  bool get autoRegisterEnabled => _autoRegisterEnabled;
  int get defaultRelayCount => _defaultRelayCount;
  List<String> get relayProfiles => List.unmodifiable(_relayProfiles);

  /// Initialize provider
  Future<void> initialize() async {
    try {
      await _loadConfiguration();
      _isInitialized = true;
      _lastError = null;
      notifyListeners();
    } catch (e) {
      _lastError = e.toString();
      debugPrint('Error initializing DeviceAutoRegistrationProvider: $e');
      notifyListeners();
    }
  }

  /// Load configuration
  Future<void> _loadConfiguration() async {
    final config = ConfigurationManager.instance;
    
    _terminalDeviceId = config.getValue<String>(RelayConfigKeys.relayDeviceId, 'T-A3B4');
    _autoRegisterEnabled = config.getValue<bool>('device_auto_registration.enabled', _autoRegisterEnabled);
    _defaultRelayCount = config.getValue<int>('device_auto_registration.relay_count', _defaultRelayCount);
    _relayProfiles = config.getValue<List<String>>('device_auto_registration.profiles', _relayProfiles);
    _deviceNamePrefix = config.getValue<String>('device_auto_registration.name_prefix', _deviceNamePrefix);
    _registeredRelayDevices = config.getValue<List<String>>('device_auto_registration.registered_devices', _registeredRelayDevices);
  }

  /// Save configuration
  Future<void> _saveConfiguration() async {
    final config = ConfigurationManager.instance;
    
    await config.setValue('device_auto_registration.enabled', _autoRegisterEnabled);
    await config.setValue('device_auto_registration.relay_count', _defaultRelayCount);
    await config.setValue('device_auto_registration.profiles', _relayProfiles);
    await config.setValue('device_auto_registration.name_prefix', _deviceNamePrefix);
    await config.setValue('device_auto_registration.registered_devices', _registeredRelayDevices);
  }

  /// Auto-register relay devices for terminal
  Future<bool> autoRegisterRelayDevices(String terminalId) async {
    if (!_autoRegisterEnabled || _isRegistering) {
      return false;
    }

    try {
      _isRegistering = true;
      _terminalDeviceId = terminalId;
      _lastError = null;
      notifyListeners();

      // Generate relay device IDs
      final sideEffectsProvider = FaceRecognitionSideEffectsProvider.instance;
      final relayDeviceIds = sideEffectsProvider.generateRelayDeviceIds(terminalId);
      
      debugPrint('Auto-registering ${relayDeviceIds.length} relay devices for terminal: $terminalId');
      
      bool allSuccessful = true;
      _pendingRegistrations = List.from(relayDeviceIds);
      notifyListeners();

      // Register each relay device
      for (int i = 0; i < relayDeviceIds.length; i++) {
        final relayId = relayDeviceIds[i];
        final profile = i < _relayProfiles.length ? _relayProfiles[i] : 'relay_${i + 1}';
        
        try {
          final success = await _registerSingleRelayDevice(
            relayId: relayId,
            terminalId: terminalId,
            profile: profile,
            index: i + 1,
          );
          
          if (success) {
            _registeredRelayDevices.add(relayId);
            await sideEffectsProvider.addRelayDevice(relayId);
          } else {
            allSuccessful = false;
          }
          
          _pendingRegistrations.remove(relayId);
          notifyListeners();
          
          // Small delay between registrations
          await Future.delayed(const Duration(milliseconds: 500));
          
        } catch (e) {
          debugPrint('Failed to register relay device $relayId: $e');
          _pendingRegistrations.remove(relayId);
          allSuccessful = false;
        }
      }

      await _saveConfiguration();
      
      debugPrint('Auto-registration completed. Success: $allSuccessful, Registered: ${_registeredRelayDevices.length}');
      
      return allSuccessful;
      
    } catch (e) {
      _lastError = e.toString();
      debugPrint('Auto-registration failed: $e');
      return false;
    } finally {
      _isRegistering = false;
      _pendingRegistrations.clear();
      notifyListeners();
    }
  }

  /// Register single relay device
  Future<bool> _registerSingleRelayDevice({
    required String relayId,
    required String terminalId,
    required String profile,
    required int index,
  }) async {
    try {
      final deviceName = '$_deviceNamePrefix $index ($profile)';
      
      debugPrint('Registering relay device: $relayId ($deviceName)');
      
      final response = await _apiService.registerDevice(
        deviceConfig: RelayDeviceConfig(
          deviceId: relayId,
          deviceName: deviceName,
          relayCount: 1, // Each relay device controls 1 relay
          baudRate: 115200,
        ),
        additionalInfo: {
          'terminal_id': terminalId,
          'device_type': 'relay',
          'profile': profile,
          'relay_index': index,
          'auto_registered': true,
          'hardware_hash': 'auto_generated_${DateTime.now().millisecondsSinceEpoch}',
          'app_version': '1.0.0',
        },
        useSecureApi: true,
      );
      
      debugPrint('✅ Successfully registered relay device: $relayId');
      debugPrint('   Device Name: $deviceName');
      debugPrint('   Registration ID: ${response.registrationId}');
      debugPrint('   Registered At: ${response.registeredAt}');
      
      return true;
      
    } catch (e) {
      debugPrint('❌ Failed to register relay device $relayId: $e');
      return false;
    }
  }

  /// Manually register additional relay device
  Future<bool> registerAdditionalRelay({
    required String relayId,
    required String deviceName,
    String? profile,
  }) async {
    if (_terminalDeviceId == null) {
      _lastError = 'Terminal device not registered';
      notifyListeners();
      return false;
    }

    try {
      _isRegistering = true;
      _lastError = null;
      notifyListeners();

      final success = await _registerSingleRelayDevice(
        relayId: relayId,
        terminalId: _terminalDeviceId!,
        profile: profile ?? 'custom',
        index: _registeredRelayDevices.length + 1,
      );

      if (success) {
        _registeredRelayDevices.add(relayId);
        await FaceRecognitionSideEffectsProvider.instance.addRelayDevice(relayId);
        await _saveConfiguration();
      }

      return success;
      
    } catch (e) {
      _lastError = e.toString();
      return false;
    } finally {
      _isRegistering = false;
      notifyListeners();
    }
  }

  /// Unregister relay device
  Future<bool> unregisterRelayDevice(String relayId) async {
    try {
      // TODO: Add API call to unregister device from server
      // For now, just remove from local list
      
      if (_registeredRelayDevices.remove(relayId)) {
        await FaceRecognitionSideEffectsProvider.instance.removeRelayDevice(relayId);
        await _saveConfiguration();
        notifyListeners();
        return true;
      }
      
      return false;
      
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Update auto-registration settings
  Future<void> updateAutoRegistrationSettings({
    bool? enabled,
    int? relayCount,
    List<String>? profiles,
    String? namePrefix,
  }) async {
    if (enabled != null) _autoRegisterEnabled = enabled;
    if (relayCount != null) _defaultRelayCount = relayCount;
    if (profiles != null) _relayProfiles = profiles;
    if (namePrefix != null) _deviceNamePrefix = namePrefix;
    
    await _saveConfiguration();
    notifyListeners();
  }

  /// Get registration status summary
  Map<String, dynamic> getRegistrationSummary() {
    return {
      'terminal_device_id': _terminalDeviceId,
      'auto_register_enabled': _autoRegisterEnabled,
      'registered_relay_count': _registeredRelayDevices.length,
      'pending_registrations': _pendingRegistrations.length,
      'is_registering': _isRegistering,
      'last_error': _lastError,
      'default_relay_count': _defaultRelayCount,
      'relay_profiles': _relayProfiles,
    };
  }

  /// Check if relay device is registered
  bool isRelayRegistered(String relayId) {
    return _registeredRelayDevices.contains(relayId);
  }

  /// Get relay device info
  Map<String, dynamic>? getRelayDeviceInfo(String relayId) {
    if (!isRelayRegistered(relayId)) return null;
    
    final index = _registeredRelayDevices.indexOf(relayId);
    final profile = index < _relayProfiles.length ? _relayProfiles[index] : 'custom';
    
    return {
      'device_id': relayId,
      'terminal_id': _terminalDeviceId,
      'profile': profile,
      'index': index + 1,
      'device_name': '$_deviceNamePrefix ${index + 1} ($profile)',
    };
  }

  /// Clear all registrations (for testing/reset)
  Future<void> clearAllRegistrations() async {
    _registeredRelayDevices.clear();
    _pendingRegistrations.clear();
    _terminalDeviceId = null;
    _lastError = null;
    
    await _saveConfiguration();
    notifyListeners();
  }
}

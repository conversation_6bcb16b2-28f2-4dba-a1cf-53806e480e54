# Migration Overview: C-Faces → Multi-App Structure

## 🎯 Mục Tiêu Migration

Chuyển đổi từ **single-app Clean Architecture** sang **multi-app structure** với:
- **Mobile App**: Ứng dụng di động với UX chuẩn
- **Terminal App**: Ứng dụng kiosk với giao diện đơn giản
- **Shared Components**: Tối đa hóa code reuse (>70%)

## 📊 Tóm Tắt Kế Hoạch

| Thông Tin               | Chi Tiết                    |
|:------------------------|:----------------------------|
| **Tổng Tasks**          | 35 tasks                    |
| **Tổng Thời Gian**      | 143 giờ (~18 ngày)         |
| **Timeline**            | 6 tuần                      |
| **Team Size**           | 2-3 developers              |
| **Code Reuse Target**   | >70%                        |
| **Test Coverage**       | >80%                        |

## 🏗️ Cấu Trúc <PERSON>i

### Trước Migration
```
lib/
├── core/           # Infrastructure
├── data/           # Data layer
├── domain/         # Business logic
├── presentation/   # UI layer
└── routes/         # Navigation
```

### Sau Migration
```
lib/
├── apps/
│   ├── mobile/     # Mobile-specific app
│   └── terminal/   # Terminal-specific app
└── shared/         # Shared business logic (70%+)
    ├── core/       # Infrastructure
    ├── data/       # Data layer
    ├── domain/     # Business logic
    └── presentation/ # Shared UI components
```

## 📋 Phases Overview

| Phase | Nội Dung | Tasks | Thời Gian | Tỷ Lệ |
|:------|:---------|:------|:----------|:------|
| **1** | Setup & Core Migration | 7 | 22h | 15.4% |
| **2** | Shared Components | 11 | 24h | 16.8% |
| **3** | Mobile App | 6 | 23h | 16.1% |
| **4** | Terminal App | 7 | 37h | 25.9% |
| **5** | Testing & QA | 4 | 28h | 19.6% |
| **6** | Documentation | 3 | 9h | 6.3% |

## 🔄 Migration Strategy

### Shared Components (Di chuyển vào `lib/shared/`)
- ✅ **Core Layer**: 100% infrastructure, network, DI, storage
- ✅ **Domain Layer**: 100% entities, use cases, repository interfaces
- ✅ **Data Layer**: 100% models, repositories, data sources
- ✅ **Base Presentation**: Shared widgets, themes, base providers

### App-Specific Components
- 📱 **Mobile App**: Responsive UI, bottom nav, standard mobile UX
- 🖥️ **Terminal App**: Kiosk mode, large buttons, auto-timeout, admin access

## 🛠️ Key Technical Decisions

### Dependency Injection Strategy
```dart
// Hierarchical DI
SharedServiceLocator.setup()     // Shared services
├── MobileServiceLocator.setup() // Mobile-specific
└── TerminalServiceLocator.setup() // Terminal-specific
```

### State Management Strategy
```dart
// Base provider pattern
BaseAuthProvider                  // Shared logic
├── MobileAuthProvider           // Mobile navigation
└── TerminalAuthProvider         // Kiosk behavior
```

### Build Configuration
```bash
# Separate entry points
flutter build apk --target=lib/apps/mobile/main_mobile.dart
flutter build apk --target=lib/apps/terminal/main_terminal.dart
```

## 📅 Timeline Chi Tiết

| Tuần | Focus | Deliverables |
|:-----|:------|:-------------|
| **1-2** | Foundation | Working shared core, basic app structure |
| **3** | Shared Components | Complete shared business logic |
| **4-5** | App Development | Working mobile and terminal apps |
| **6** | QA & Docs | Production-ready multi-app system |

## 🎯 Success Criteria

### Technical Metrics
| Metric | Target | Measurement |
|:-------|:-------|:------------|
| **Build Success** | 100% | Both apps build without errors |
| **Code Reuse** | >70% | Shared code / Total code |
| **Test Coverage** | >80% | Unit + Integration tests |
| **Performance** | No degradation | App startup time, memory usage |

### Business Metrics
| Metric | Target | Measurement |
|:-------|:-------|:------------|
| **Feature Parity** | 100% | All current features work |
| **UX Quality** | Maintained | User experience preserved |
| **Dev Velocity** | Improved | Faster feature development |
| **Maintenance** | Reduced | Lower maintenance overhead |

## 🚨 Risk Assessment

| Risk Level | Item | Mitigation |
|:-----------|:-----|:-----------|
| **🔴 High** | Import path updates | IDE refactoring tools, migration scripts |
| **🔴 High** | DI configuration | Thorough testing, gradual migration |
| **🟡 Medium** | Build configuration | Proper CI/CD setup, build scripts |
| **🟡 Medium** | Testing strategy | Clear guidelines, mock strategies |
| **🟢 Low** | Domain logic | Business logic unchanged |
| **🟢 Low** | API integration | No backend changes |

## 📁 Files Tạo Ra

| File | Mục Đích |
|:-----|:---------|
| **MIGRATION_PLAN.md** | Kế hoạch chi tiết với 35 tasks |
| **MIGRATION_SCRIPTS.md** | Scripts và templates hỗ trợ |
| **MIGRATION_CHECKLIST.md** | Checklist thực hiện từng bước |
| **MIGRATION_OVERVIEW.md** | Tóm tắt tổng quan (file này) |

## 🚀 Getting Started

### Bước 1: Preparation
1. Review tài liệu migration
2. Setup team và assign responsibilities
3. Backup codebase hiện tại
4. Prepare development environment

### Bước 2: Execute
1. Bắt đầu với **SETUP-001**: Tạo cấu trúc thư mục
2. Follow checklist trong **MIGRATION_CHECKLIST.md**
3. Use scripts trong **MIGRATION_SCRIPTS.md**
4. Track progress theo **MIGRATION_PLAN.md**

### Bước 3: Verify
1. Test both mobile và terminal apps
2. Verify code reuse metrics
3. Check test coverage
4. Performance testing

## 📞 Support

Nếu gặp vấn đề trong quá trình migration:
1. Check **MIGRATION_CHECKLIST.md** cho troubleshooting
2. Review **MIGRATION_SCRIPTS.md** cho automation tools
3. Refer to **MIGRATION_PLAN.md** cho detailed guidance
4. Update progress tracking tables

---

**Status**: 🔴 Ready to Start  
**Next Action**: Execute SETUP-001 (Tạo cấu trúc thư mục multi-app mới)

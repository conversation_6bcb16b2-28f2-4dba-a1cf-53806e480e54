// Enums for User entity
enum UserStatus {
  active('ACTIVE'),
  inactive('INACTIVE'),
  suspended('SUSPENDED'),
  pending('PENDING');

  const UserStatus(this.value);
  final String value;

  static UserStatus fromString(String value) {
    return UserStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => UserStatus.active,
    );
  }
}

enum UserGender {
  male('MALE'),
  female('FEMALE'),
  other('OTHER');

  const UserGender(this.value);
  final String value;

  static UserGender fromString(String value) {
    return UserGender.values.firstWhere(
      (gender) => gender.value == value,
      orElse: () => UserGender.other,
    );
  }
}

class User {
  final String id;
  final String unitId;
  final String? faceId;
  final String? avatarId;
  final String? memberRoleId;
  final String code;
  final String name;
  final String? email;
  final String? phone;
  final DateTime? dob;
  final UserGender? gender;
  final String username;
  final String password;
  final UserStatus status;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  User({
    required this.id,
    required this.unitId,
    this.faceId,
    this.avatarId,
    this.memberRoleId,
    required this.code,
    required this.name,
    this.email,
    this.phone,
    this.dob,
    this.gender,
    required this.username,
    required this.password,
    required this.status,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  // Computed properties for backward compatibility
  String get fullName => name;
  String? get phoneNumber => phone;
  String? get dateOfBirth => dob?.toIso8601String().split('T')[0];
  String get userName => username.isNotEmpty ? username : (name.isNotEmpty ? name : 'User');
  String? get organizationId => null; // Will be derived from unit relationship
  String? get avatar => avatarId;
}

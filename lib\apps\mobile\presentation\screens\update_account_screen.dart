import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/domain/use_cases/user/update_profile_use_case.dart';
import '../../../../shared/core/errors/failures.dart';
import '../providers/auth_provider.dart';

/// Update Account screen - màn hình cập nhật thông tin tài khoản
class UpdateAccountScreen extends StatefulWidget {
  const UpdateAccountScreen({super.key});

  @override
  State<UpdateAccountScreen> createState() => _UpdateAccountScreenState();
}

class _UpdateAccountScreenState extends State<UpdateAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _dobController = TextEditingController();
  
  bool _isLoading = false;
  String? _selectedGender;
  DateTime? _selectedDate;
  
  String? _errorMessage;
  Map<String, List<String>>? _fieldErrors;

  late final UpdateProfileUseCase _updateProfileUseCase;

  @override
  void initState() {
    super.initState();
    _updateProfileUseCase = GetIt.instance<UpdateProfileUseCase>();
    _loadCurrentUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _dobController.dispose();
    super.dispose();
  }

  void _loadCurrentUserData() {
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.currentUser;
    
    if (user != null) {
      _nameController.text = user.name ?? '';
      _emailController.text = user.email ?? '';
      _phoneController.text = user.phone ?? '';
      _selectedGender = user.gender;
      
      if (user.dob != null) {
        _selectedDate = user.dob;
        _dobController.text = _formatDate(_selectedDate!);
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4FBFF),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF4FBFF),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF15171A)),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Cập nhật tài khoản',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF15171A),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(height: AppDimensions.spacing16),
                        
                        // Error message
                        if (_errorMessage != null) ...[
                          Container(
                            padding: EdgeInsets.all(AppDimensions.paddingS),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFEBEE),
                              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                              border: Border.all(color: const Color(0xFFE03E59)),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: AppTextStyles.caption.copyWith(
                                color: const Color(0xFFE03E59),
                              ),
                            ),
                          ),
                          SizedBox(height: AppDimensions.spacing16),
                        ],

                        // Name Field
                        _buildTextField(
                          controller: _nameController,
                          label: 'Họ và tên',
                          hint: 'Nhập họ và tên',
                          fieldKey: 'name',
                          isRequired: true,
                        ),
                        SizedBox(height: AppDimensions.spacing16),

                        // Email Field
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email',
                          hint: 'Nhập địa chỉ email',
                          fieldKey: 'email',
                          keyboardType: TextInputType.emailAddress,
                        ),
                        SizedBox(height: AppDimensions.spacing16),

                        // Phone Field
                        _buildTextField(
                          controller: _phoneController,
                          label: 'Số điện thoại',
                          hint: 'Nhập số điện thoại',
                          fieldKey: 'phone',
                          keyboardType: TextInputType.phone,
                        ),
                        SizedBox(height: AppDimensions.spacing16),

                        // Date of Birth Field
                        _buildDateField(),
                        SizedBox(height: AppDimensions.spacing16),

                        // Gender Field
                        _buildGenderField(),
                      ],
                    ),
                  ),
                ),

                // Update Button
                SizedBox(height: AppDimensions.spacing16),
                _buildUpdateButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required String fieldKey,
    TextInputType? keyboardType,
    bool isRequired = false,
  }) {
    final fieldError = _fieldErrors?[fieldKey];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isRequired ? ' *' : ''),
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2329),
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTextStyles.caption.copyWith(
              color: const Color(0xFF8F959E),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE03E59)),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: isRequired ? (value) {
            if (value == null || value.trim().isEmpty) {
              return '$label không được để trống';
            }
            return null;
          } : null,
        ),
        if (fieldError != null && fieldError.isNotEmpty) ...[
          SizedBox(height: AppDimensions.spacing4),
          ...fieldError.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Text(
              error,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFFE03E59),
              ),
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildDateField() {
    final fieldError = _fieldErrors?['dob'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày sinh',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2329),
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        TextFormField(
          controller: _dobController,
          readOnly: true,
          onTap: _selectDate,
          decoration: InputDecoration(
            hintText: 'Chọn ngày sinh',
            hintStyle: AppTextStyles.caption.copyWith(
              color: const Color(0xFF8F959E),
            ),
            suffixIcon: const Icon(
              Icons.calendar_today,
              color: Color(0xFF8F959E),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
        ),
        if (fieldError != null && fieldError.isNotEmpty) ...[
          SizedBox(height: AppDimensions.spacing4),
          ...fieldError.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Text(
              error,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFFE03E59),
              ),
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildGenderField() {
    final fieldError = _fieldErrors?['gender'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Giới tính',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2329),
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        DropdownButtonFormField<String>(
          value: _selectedGender,
          decoration: InputDecoration(
            hintText: 'Chọn giới tính',
            hintStyle: AppTextStyles.caption.copyWith(
              color: const Color(0xFF8F959E),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          items: const [
            DropdownMenuItem(value: 'male', child: Text('Nam')),
            DropdownMenuItem(value: 'female', child: Text('Nữ')),
            DropdownMenuItem(value: 'other', child: Text('Khác')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedGender = value;
            });
          },
        ),
        if (fieldError != null && fieldError.isNotEmpty) ...[
          SizedBox(height: AppDimensions.spacing4),
          ...fieldError.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Text(
              error,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFFE03E59),
              ),
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildUpdateButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleUpdateAccount,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingM),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 0,
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              'Cập nhật',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dobController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _handleUpdateAccount() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
      _fieldErrors = null;
    });

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final params = UpdateProfileParams(
        name: _nameController.text.trim().isEmpty ? null : _nameController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        dob: _selectedDate,
        gender: _selectedGender,
      );

      final result = await _updateProfileUseCase(params);

      result.fold(
        (failure) {
          setState(() {
            if (failure is ValidationFailure && failure.fieldErrors != null) {
              _fieldErrors = failure.fieldErrors;
              _errorMessage = failure.message;
            } else {
              _errorMessage = failure.message;
            }
          });
        },
        (user) {
          // Update the auth provider with new user data
          final authProvider = context.read<AuthProvider>();
          authProvider.updateCurrentUser(user);
          
          // Show success message and go back
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Cập nhật thông tin thành công'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          }
        },
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không mong muốn: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

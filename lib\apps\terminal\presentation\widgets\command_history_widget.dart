import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/device_command_handler.dart';

class CommandHistoryWidget extends StatelessWidget {
  final int maxEntries;
  final bool showFilters;

  const CommandHistoryWidget({
    super.key,
    this.maxEntries = 50,
    this.showFilters = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceCommandHandler>(
      builder: (context, commandHandler, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context, commandHandler),
                const SizedBox(height: 16),
                _buildHistoryList(context, commandHandler),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, DeviceCommandHandler commandHandler) {
    return Row(
      children: [
        Icon(
          Icons.history,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 8),
        Text(
          'Command History',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (commandHandler.commandHistory.isNotEmpty)
          TextButton.icon(
            onPressed: () => _showClearConfirmation(context, commandHandler),
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear'),
          ),
      ],
    );
  }

  Widget _buildHistoryList(BuildContext context, DeviceCommandHandler commandHandler) {
    final history = commandHandler.commandHistory.take(maxEntries).toList();

    if (history.isEmpty) {
      return _buildEmptyState(context);
    }

    return SizedBox(
      height: 400,
      child: ListView.separated(
        itemCount: history.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final entry = history[index];
          return _buildHistoryEntry(context, entry);
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No commands executed yet',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Command history will appear here after execution',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryEntry(BuildContext context, CommandHistoryEntry entry) {
    return ExpansionTile(
      leading: _buildCommandIcon(entry.commandType, entry.result.success),
      title: Row(
        children: [
          Expanded(
            child: Text(
              _formatCommandTitle(entry.commandType),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildStatusChip(entry.result.success),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Text(
            DateFormat('MMM dd, HH:mm:ss').format(entry.timestamp),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          if (entry.source != 'manual') ...[
            const SizedBox(height: 2),
            Text(
              'Source: ${entry.source}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade500,
                fontSize: 11,
              ),
            ),
          ],
        ],
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: _buildEntryDetails(context, entry),
        ),
      ],
    );
  }

  Widget _buildCommandIcon(String commandType, bool success) {
    IconData icon;
    Color color;

    switch (commandType) {
      case 'relay_control':
        icon = Icons.electrical_services;
        break;
      case 'face_auth':
        icon = Icons.face;
        break;
      case 'status_request':
        icon = Icons.info;
        break;
      case 'heartbeat':
        icon = Icons.favorite;
        break;
      case 'config_update':
        icon = Icons.settings;
        break;
      default:
        icon = Icons.code;
    }

    color = success ? Colors.green.shade600 : Colors.red.shade600;

    return CircleAvatar(
      radius: 16,
      backgroundColor: color.withValues(alpha: 0.1),
      child: Icon(
        icon,
        size: 16,
        color: color,
      ),
    );
  }

  Widget _buildStatusChip(bool success) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: success ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: success ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Text(
        success ? 'SUCCESS' : 'FAILED',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: success ? Colors.green.shade700 : Colors.red.shade700,
        ),
      ),
    );
  }

  Widget _buildEntryDetails(BuildContext context, CommandHistoryEntry entry) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Result Message
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: entry.result.success 
                ? Colors.green.shade50 
                : Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: entry.result.success 
                  ? Colors.green.shade200 
                  : Colors.red.shade200,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Result',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: entry.result.success 
                      ? Colors.green.shade700 
                      : Colors.red.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                entry.result.message,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),

        // Payload
        if (entry.payload.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Payload',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Text(
              _formatPayload(entry.payload),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],

        // Result Data
        if (entry.result.data != null && entry.result.data!.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Response Data',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Text(
              _formatPayload(entry.result.data!),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _formatCommandTitle(String commandType) {
    switch (commandType) {
      case 'relay_control':
        return 'Relay Control';
      case 'face_auth':
        return 'Face Authentication';
      case 'status_request':
        return 'Status Request';
      case 'heartbeat':
        return 'Heartbeat';
      case 'config_update':
        return 'Configuration Update';
      default:
        return commandType.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatPayload(Map<String, dynamic> payload) {
    final buffer = StringBuffer();
    payload.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString().trim();
  }

  void _showClearConfirmation(BuildContext context, DeviceCommandHandler commandHandler) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Command History'),
        content: const Text(
          'Are you sure you want to clear all command history? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              commandHandler.clearHistory();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Command history cleared'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}

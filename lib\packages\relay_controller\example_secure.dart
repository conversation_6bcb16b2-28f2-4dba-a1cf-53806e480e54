import 'package:relay_controller/relay_controller.dart';

/// Example demonstrating secure relay controller usage
void main() async {
  print('🔐 Secure Relay Controller Example');
  print('=====================================\n');

  // Create secure controller
  final controller = SecureHttpRelayController(
    deviceId: 'example-secure-relay-001',
    deviceType: 'flutter_app',
    serverBaseUrl: 'http://localhost:3000',
    deviceName: 'Example Secure Relay',
    appVersion: '1.0.0',
  );

  try {
    print('📝 Step 1: Registering device...');
    await controller.registerDevice();
    print('✅ Device registered successfully!');
    print('   - Device ID: ${controller.deviceId}');
    print('   - Authenticated: ${controller.isAuthenticated}');
    print('   - Token expires: ${controller.tokenExpiry}');
    print('   - Device scope: ${controller.deviceScope}');
    print('');

    print('🔓 Step 2: Turning relay ON...');
    await controller.triggerOn();
    print('✅ Relay turned ON successfully!');
    print('');

    print('📊 Step 3: Getting relay status...');
    final status = await controller.getStatus();
    print('✅ Relay status: ${status == true ? "ON" : status == false ? "OFF" : "UNKNOWN"}');
    print('');

    print('🔒 Step 4: Turning relay OFF...');
    await controller.triggerOff();
    print('✅ Relay turned OFF successfully!');
    print('');

    print('📊 Step 5: Getting relay status again...');
    final statusAfter = await controller.getStatus();
    print('✅ Relay status: ${statusAfter == true ? "ON" : statusAfter == false ? "OFF" : "UNKNOWN"}');
    print('');

    print('🚫 Step 6: Revoking credentials...');
    await controller.revokeCredentials();
    print('✅ Credentials revoked successfully!');
    print('   - Authenticated: ${controller.isAuthenticated}');
    print('');

  } catch (e) {
    print('❌ Error: $e');
    print('');
    print('💡 Make sure the test server is running:');
    print('   cd test_server');
    print('   npm install');
    print('   npm start');
    print('');
  } finally {
    await controller.dispose();
    print('🧹 Controller disposed.');
  }

  print('\n🎉 Example completed!');
  print('\n📚 Security Features Demonstrated:');
  print('   ✅ Device registration with JWT');
  print('   ✅ HMAC request signing');
  print('   ✅ Automatic token management');
  print('   ✅ Secure relay control');
  print('   ✅ Credential revocation');
}

/// Example demonstrating device authentication utilities
void demonstrateDeviceAuth() {
  print('\n🔑 Device Authentication Utilities');
  print('===================================\n');

  // Generate device ID
  final deviceId = DeviceAuth.generateDeviceId(prefix: 'demo');
  print('📱 Generated Device ID: $deviceId');

  // Generate hardware hash
  final hardwareHash = DeviceAuth.generateHardwareHash();
  print('🔧 Hardware Hash: ${hardwareHash.substring(0, 16)}...');

  // Create HMAC signature
  const secretKey = 'demo-secret-key';
  const action = 'unlock';
  final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;

  final signature = DeviceAuth.createHmacSignature(
    secretKey: secretKey,
    deviceId: deviceId,
    action: action,
    timestamp: timestamp,
  );

  print('🔐 HMAC Signature: ${signature.substring(0, 16)}...');

  // Verify signature
  final isValid = DeviceAuth.verifyHmacSignature(
    secretKey: secretKey,
    signature: signature,
    deviceId: deviceId,
    action: action,
    timestamp: timestamp,
  );

  print('✅ Signature Valid: $isValid');

  // Test timestamp validation
  final isTimestampValid = DeviceAuth.isTimestampValid(timestamp);
  print('⏰ Timestamp Valid: $isTimestampValid');

  // Create device fingerprint
  final fingerprint = DeviceAuth.createDeviceFingerprint(
    deviceId: deviceId,
    deviceType: 'demo_app',
    hardwareHash: hardwareHash,
    appVersion: '1.0.0',
  );

  print('👆 Device Fingerprint: ${fingerprint.substring(0, 16)}...');

  print('\n🎯 All authentication utilities working correctly!');
}

/// Example demonstrating secure relay command creation
void demonstrateSecureCommand() {
  print('\n📡 Secure Relay Command Creation');
  print('=================================\n');

  const secretKey = 'demo-secret-key-for-signing';
  final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;

  // Create unsigned command
  final command = SecureRelayCommand(
    deviceId: 'demo-device-001',
    action: 'unlock',
    timestamp: timestamp,
    additionalData: {
      'priority': 'high',
      'source': 'mobile_app',
    },
  );

  print('📝 Unsigned Command:');
  print('   Device ID: ${command.deviceId}');
  print('   Action: ${command.action}');
  print('   Timestamp: ${command.timestamp}');
  print('   Additional Data: ${command.additionalData}');

  // Sign the command
  final signedCommand = command.withSignature(secretKey);

  print('\n🔐 Signed Command:');
  print('   Signature: ${signedCommand.signature?.substring(0, 16)}...');

  // Convert to JSON
  final json = signedCommand.toJson();
  print('\n📄 JSON Payload:');
  json.forEach((key, value) {
    if (key == 'signature' && value is String && value.length > 20) {
      print('   $key: ${value.substring(0, 16)}...');
    } else {
      print('   $key: $value');
    }
  });

  print('\n✅ Secure command created and signed successfully!');
}

/// Run all examples
void runAllExamples() async {
  await main();
  demonstrateDeviceAuth();
  demonstrateSecureCommand();
}

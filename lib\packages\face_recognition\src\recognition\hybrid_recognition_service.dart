import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';

import '../core/interfaces/face_recognition_service.dart';
import '../core/models/face_recognition_config.dart';
import '../shared/services/network_detection_service.dart';
import '../shared/services/online_recognition_service.dart';
import '../shared/services/offline_recognition_service.dart';

/// Hybrid recognition service that switches between online and offline
class HybridRecognitionService implements FaceRecognitionService {
  final FaceRecognitionConfig _config;
  final NetworkDetectionService _networkService;
  
  OnlineRecognitionService? _onlineService;
  OfflineRecognitionService? _offlineService;
  
  bool _isInitialized = false;
  bool _isOnlineMode = true;
  DateTime _lastRecognitionTime = DateTime.now();
  
  HybridRecognitionService({
    required FaceRecognitionConfig config,
    required NetworkDetectionService networkService,
  }) : _config = config,
       _networkService = networkService;
  
  @override
  String get serviceName => 'Hybrid Recognition';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  bool get isThrottled {
    final now = DateTime.now();
    final timeSinceLastRecognition = now.difference(_lastRecognitionTime);
    return timeSinceLastRecognition < _config.recognitionThrottle;
  }
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🔄 Initializing HybridRecognitionService');
      }
      
      // Initialize online service
      _onlineService = OnlineRecognitionService(config: _config);
      await _onlineService!.initialize();
      
      // Initialize offline service
      _offlineService = OfflineRecognitionService(config: _config);
      await _offlineService!.initialize();
      
      // Set initial mode based on network status
      _isOnlineMode = _networkService.isOnline;
      
      // Listen to network changes
      _networkService.addListener(_onNetworkChanged);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ HybridRecognitionService initialized');
        print('   Initial mode: ${_isOnlineMode ? "Online" : "Offline"}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize HybridRecognitionService: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes) async {
    if (!_isInitialized) {
      throw StateError('HybridRecognitionService not initialized');
    }
    
    if (isThrottled) {
      if (kDebugMode) {
        print('⏸️ Recognition throttled');
      }
      return null;
    }
    
    _lastRecognitionTime = DateTime.now();
    
    try {
      RecognitionResult? result;
      
      if (_isOnlineMode && _onlineService != null) {
        // Try online recognition first
        result = await _tryOnlineRecognition(faceImageBytes);
        
        // Fallback to offline if online fails
        if (result == null && _offlineService != null) {
          if (kDebugMode) {
            print('🔄 Online recognition failed, falling back to offline');
          }
          result = await _tryOfflineRecognition(faceImageBytes);
        }
      } else if (_offlineService != null) {
        // Use offline recognition
        result = await _tryOfflineRecognition(faceImageBytes);
      }
      
      return result;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Hybrid recognition failed: $e');
      }
      return null;
    }
  }
  
  /// Try online recognition
  Future<RecognitionResult?> _tryOnlineRecognition(Uint8List faceImageBytes) async {
    try {
      final result = await _onlineService!.recognizeFace(faceImageBytes);
      if (result != null) {
        return RecognitionResult(
          userId: result.userId,
          userName: result.userName,
          confidence: result.confidence,
          source: 'online',
          timestamp: result.timestamp,
          metadata: result.metadata,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Online recognition error: $e');
      }
    }
    return null;
  }
  
  /// Try offline recognition
  Future<RecognitionResult?> _tryOfflineRecognition(Uint8List faceImageBytes) async {
    try {
      final result = await _offlineService!.recognizeFace(faceImageBytes);
      if (result != null) {
        return RecognitionResult(
          userId: result.userId,
          userName: result.userName,
          confidence: result.confidence,
          source: 'offline',
          timestamp: result.timestamp,
          metadata: result.metadata,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Offline recognition error: $e');
      }
    }
    return null;
  }
  
  @override
  void setOnlineMode(bool isOnline) {
    if (_isOnlineMode != isOnline) {
      _isOnlineMode = isOnline;
      
      if (kDebugMode) {
        print('🔄 Hybrid recognition mode changed to: ${isOnline ? "Online" : "Offline"}');
      }
    }
  }
  
  /// Handle network status changes
  void _onNetworkChanged() {
    final wasOnline = _isOnlineMode;
    _isOnlineMode = _networkService.isOnline;
    
    if (wasOnline != _isOnlineMode) {
      if (kDebugMode) {
        print('🌐 Network status changed: ${_isOnlineMode ? "Online" : "Offline"}');
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    _networkService.removeListener(_onNetworkChanged);
    
    await _onlineService?.dispose();
    await _offlineService?.dispose();
    
    _onlineService = null;
    _offlineService = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ HybridRecognitionService disposed');
    }
  }
}

import 'dart:typed_data';
import 'dart:ui';
import 'dart:math' as math;

import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;

/// Utility class for image processing operations
class ImageUtils {
  /// Convert CameraImage to RGB bytes
  static Uint8List convertCameraImageToRGB(CameraImage image) {
    switch (image.format.group) {
      case ImageFormatGroup.yuv420:
        return _convertYUV420ToRGB(image);
      case ImageFormatGroup.bgra8888:
        return _convertBGRA8888ToRGB(image);
      case ImageFormatGroup.nv21:
        return _convertNV21ToRGB(image);
      default:
        throw UnsupportedError('Unsupported image format: ${image.format.group}');
    }
  }
  
  /// Convert YUV420 to RGB
  static Uint8List _convertYUV420ToRGB(CameraImage image) {
    final int width = image.width;
    final int height = image.height;
    final int uvRowStride = image.planes[1].bytesPerRow;
    final int uvPixelStride = image.planes[1].bytesPerPixel!;
    
    final Uint8List yPlane = image.planes[0].bytes;
    final Uint8List uPlane = image.planes[1].bytes;
    final Uint8List vPlane = image.planes[2].bytes;
    
    final Uint8List rgbBytes = Uint8List(width * height * 3);
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int yIndex = y * width + x;
        final int uvIndex = (y ~/ 2) * uvRowStride + (x ~/ 2) * uvPixelStride;
        
        final int yValue = yPlane[yIndex];
        final int uValue = uPlane[uvIndex];
        final int vValue = vPlane[uvIndex];
        
        // YUV to RGB conversion
        final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
        final int g = (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128)).round().clamp(0, 255);
        final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
        
        final int rgbIndex = yIndex * 3;
        rgbBytes[rgbIndex] = r;
        rgbBytes[rgbIndex + 1] = g;
        rgbBytes[rgbIndex + 2] = b;
      }
    }
    
    return rgbBytes;
  }
  
  /// Convert BGRA8888 to RGB
  static Uint8List _convertBGRA8888ToRGB(CameraImage image) {
    final Uint8List bgraBytes = image.planes[0].bytes;
    final Uint8List rgbBytes = Uint8List(image.width * image.height * 3);
    
    for (int i = 0; i < bgraBytes.length; i += 4) {
      final int rgbIndex = (i ~/ 4) * 3;
      rgbBytes[rgbIndex] = bgraBytes[i + 2]; // R
      rgbBytes[rgbIndex + 1] = bgraBytes[i + 1]; // G
      rgbBytes[rgbIndex + 2] = bgraBytes[i]; // B
      // Skip alpha channel
    }
    
    return rgbBytes;
  }
  
  /// Convert NV21 to RGB
  static Uint8List _convertNV21ToRGB(CameraImage image) {
    final int width = image.width;
    final int height = image.height;
    final Uint8List yuvBytes = image.planes[0].bytes;
    final Uint8List rgbBytes = Uint8List(width * height * 3);
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int yIndex = y * width + x;
        final int uvIndex = width * height + (y ~/ 2) * width + (x & ~1);
        
        final int yValue = yuvBytes[yIndex] & 0xFF;
        final int vValue = yuvBytes[uvIndex] & 0xFF;
        final int uValue = yuvBytes[uvIndex + 1] & 0xFF;
        
        // YUV to RGB conversion
        final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
        final int g = (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128)).round().clamp(0, 255);
        final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
        
        final int rgbIndex = yIndex * 3;
        rgbBytes[rgbIndex] = r;
        rgbBytes[rgbIndex + 1] = g;
        rgbBytes[rgbIndex + 2] = b;
      }
    }
    
    return rgbBytes;
  }
  
  /// Crop and align face from image
  static Uint8List cropAndAlignFace(
    Uint8List imageBytes,
    Rect boundingBox,
    List<Point> landmarks, {
    int targetSize = 112,
  }) {
    final image = img.decodeImage(imageBytes);
    if (image == null) {
      throw ArgumentError('Failed to decode image');
    }
    
    // If landmarks are available, use them for alignment
    if (landmarks.length >= 2) {
      return _alignFaceWithLandmarks(image, landmarks, targetSize);
    } else {
      // Simple crop without alignment
      return _cropFaceSimple(image, boundingBox, targetSize);
    }
  }
  
  /// Align face using eye landmarks
  static Uint8List _alignFaceWithLandmarks(
    img.Image image,
    List<Point> landmarks,
    int targetSize,
  ) {
    // Assume first two landmarks are left and right eyes
    final Point leftEye = landmarks[0];
    final Point rightEye = landmarks[1];
    
    // Calculate angle between eyes
    final double deltaX = rightEye.x - leftEye.x;
    final double deltaY = rightEye.y - leftEye.y;
    final double angle = math.atan2(deltaY, deltaX);
    
    // Calculate center point between eyes
    final double centerX = (leftEye.x + rightEye.x) / 2;
    final double centerY = (leftEye.y + rightEye.y) / 2;
    
    // Calculate eye distance for scaling
    final double eyeDistance = math.sqrt(deltaX * deltaX + deltaY * deltaY);
    final double desiredEyeDistance = targetSize * 0.35; // 35% of target size
    final double scale = desiredEyeDistance / eyeDistance;
    
    // Create transformation matrix
    final double cosAngle = math.cos(-angle);
    final double sinAngle = math.sin(-angle);
    
    // Create aligned image
    final alignedImage = img.Image(width: targetSize, height: targetSize);
    img.fill(alignedImage, color: img.ColorRgb8(128, 128, 128));
    
    final int halfSize = targetSize ~/ 2;
    
    for (int y = 0; y < targetSize; y++) {
      for (int x = 0; x < targetSize; x++) {
        // Transform coordinates
        final double dx = (x - halfSize) / scale;
        final double dy = (y - halfSize) / scale;
        
        final double rotatedX = dx * cosAngle - dy * sinAngle;
        final double rotatedY = dx * sinAngle + dy * cosAngle;
        
        final int sourceX = (centerX + rotatedX).round();
        final int sourceY = (centerY + rotatedY).round();
        
        // Check bounds and copy pixel
        if (sourceX >= 0 && sourceX < image.width &&
            sourceY >= 0 && sourceY < image.height) {
          final pixel = image.getPixel(sourceX, sourceY);
          alignedImage.setPixel(x, y, pixel);
        }
      }
    }
    
    return Uint8List.fromList(img.encodeJpg(alignedImage, quality: 95));
  }
  
  /// Simple face crop without alignment
  static Uint8List _cropFaceSimple(
    img.Image image,
    Rect boundingBox,
    int targetSize,
  ) {
    // Expand bounding box slightly
    final double expansion = 0.2;
    final double expandedWidth = boundingBox.width * (1 + expansion);
    final double expandedHeight = boundingBox.height * (1 + expansion);
    
    final double left = (boundingBox.left - expandedWidth * expansion / 2).clamp(0, image.width.toDouble());
    final double top = (boundingBox.top - expandedHeight * expansion / 2).clamp(0, image.height.toDouble());
    final double right = (left + expandedWidth).clamp(0, image.width.toDouble());
    final double bottom = (top + expandedHeight).clamp(0, image.height.toDouble());
    
    // Crop face region
    final croppedImage = img.copyCrop(
      image,
      x: left.toInt(),
      y: top.toInt(),
      width: (right - left).toInt(),
      height: (bottom - top).toInt(),
    );
    
    // Resize to target size
    final resizedImage = img.copyResize(
      croppedImage,
      width: targetSize,
      height: targetSize,
      interpolation: img.Interpolation.cubic,
    );
    
    return Uint8List.fromList(img.encodeJpg(resizedImage, quality: 95));
  }
  
  /// Resize image while maintaining aspect ratio
  static img.Image resizeWithAspectRatio(
    img.Image image,
    int maxWidth,
    int maxHeight,
  ) {
    final double aspectRatio = image.width / image.height;
    int newWidth, newHeight;
    
    if (aspectRatio > 1) {
      // Landscape
      newWidth = maxWidth;
      newHeight = (maxWidth / aspectRatio).round();
    } else {
      // Portrait
      newHeight = maxHeight;
      newWidth = (maxHeight * aspectRatio).round();
    }
    
    return img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
      interpolation: img.Interpolation.cubic,
    );
  }
  
  /// Calculate image quality score
  static double calculateImageQuality(Uint8List imageBytes) {
    final image = img.decodeImage(imageBytes);
    if (image == null) return 0.0;
    
    // Calculate sharpness using Laplacian variance
    final sharpness = _calculateSharpness(image);
    
    // Calculate brightness
    final brightness = _calculateBrightness(image);
    
    // Calculate contrast
    final contrast = _calculateContrast(image);
    
    // Combine metrics
    final quality = (sharpness * 0.5 + brightness * 0.3 + contrast * 0.2).clamp(0.0, 1.0);
    
    return quality;
  }
  
  /// Calculate image sharpness using Laplacian variance
  static double _calculateSharpness(img.Image image) {
    // Convert to grayscale
    final grayscale = img.grayscale(image);
    
    double variance = 0.0;
    int count = 0;
    
    // Apply Laplacian kernel
    for (int y = 1; y < grayscale.height - 1; y++) {
      for (int x = 1; x < grayscale.width - 1; x++) {
        final center = grayscale.getPixel(x, y).r;
        final top = grayscale.getPixel(x, y - 1).r;
        final bottom = grayscale.getPixel(x, y + 1).r;
        final left = grayscale.getPixel(x - 1, y).r;
        final right = grayscale.getPixel(x + 1, y).r;
        
        final laplacian = (4 * center - top - bottom - left - right).abs();
        variance += laplacian * laplacian;
        count++;
      }
    }
    
    variance /= count;
    return math.min(1.0, variance / 10000); // Normalize
  }
  
  /// Calculate image brightness
  static double _calculateBrightness(img.Image image) {
    double totalBrightness = 0.0;
    int pixelCount = 0;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final brightness = (pixel.r + pixel.g + pixel.b) / 3;
        totalBrightness += brightness;
        pixelCount++;
      }
    }
    
    final averageBrightness = totalBrightness / pixelCount;
    
    // Optimal brightness is around 128, penalize too dark or too bright
    final normalizedBrightness = 1.0 - (averageBrightness - 128).abs() / 128;
    return normalizedBrightness.clamp(0.0, 1.0);
  }
  
  /// Calculate image contrast
  static double _calculateContrast(img.Image image) {
    double mean = 0.0;
    int pixelCount = 0;
    
    // Calculate mean brightness
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final brightness = (pixel.r + pixel.g + pixel.b) / 3;
        mean += brightness;
        pixelCount++;
      }
    }
    mean /= pixelCount;
    
    // Calculate standard deviation
    double variance = 0.0;
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final brightness = (pixel.r + pixel.g + pixel.b) / 3;
        variance += (brightness - mean) * (brightness - mean);
      }
    }
    variance /= pixelCount;
    
    final standardDeviation = math.sqrt(variance);
    return math.min(1.0, standardDeviation / 64); // Normalize
  }
}

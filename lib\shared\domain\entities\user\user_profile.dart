import 'package:equatable/equatable.dart';

/// User profile domain entity
class UserProfile extends Equatable {
  final String? avatarId;
  final String? bio;
  final String? website;
  final String? location;
  final String? company;
  final String? jobTitle;
  final Map<String, String> socialLinks;
  final List<String> skills;
  final List<String> interests;
  final Map<String, dynamic> preferences;
  final DateTime? lastLoginAt;
  final DateTime? profileUpdatedAt;

  const UserProfile({
    this.avatarId,
    this.bio,
    this.website,
    this.location,
    this.company,
    this.jobTitle,
    this.socialLinks = const {},
    this.skills = const [],
    this.interests = const [],
    this.preferences = const {},
    this.lastLoginAt,
    this.profileUpdatedAt,
  });

  /// Copy with new values
  UserProfile copyWith({
    String? avatarId,
    String? bio,
    String? website,
    String? location,
    String? company,
    String? jobTitle,
    Map<String, String>? socialLinks,
    List<String>? skills,
    List<String>? interests,
    Map<String, dynamic>? preferences,
    DateTime? lastLoginAt,
    DateTime? profileUpdatedAt,
  }) {
    return UserProfile(
      avatarId: avatarId ?? this.avatarId,
      bio: bio ?? this.bio,
      website: website ?? this.website,
      location: location ?? this.location,
      company: company ?? this.company,
      jobTitle: jobTitle ?? this.jobTitle,
      socialLinks: socialLinks ?? this.socialLinks,
      skills: skills ?? this.skills,
      interests: interests ?? this.interests,
      preferences: preferences ?? this.preferences,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      profileUpdatedAt: profileUpdatedAt ?? this.profileUpdatedAt,
    );
  }

  /// Check if profile is complete
  bool get isComplete {
    return avatarId != null &&
           bio != null &&
           location != null &&
           company != null &&
           jobTitle != null;
  }

  /// Get completion percentage
  double get completionPercentage {
    int completedFields = 0;
    int totalFields = 6; // avatarId, bio, website, location, company, jobTitle

    if (avatarId != null && avatarId!.isNotEmpty) completedFields++;
    if (bio != null && bio!.isNotEmpty) completedFields++;
    if (website != null && website!.isNotEmpty) completedFields++;
    if (location != null && location!.isNotEmpty) completedFields++;
    if (company != null && company!.isNotEmpty) completedFields++;
    if (jobTitle != null && jobTitle!.isNotEmpty) completedFields++;

    return completedFields / totalFields;
  }

  /// Check if user has avatar
  bool get hasAvatar => avatarId != null && avatarId!.isNotEmpty;

  @override
  List<Object?> get props => [
    avatarId,
    bio,
    website,
    location,
    company,
    jobTitle,
    socialLinks,
    skills,
    interests,
    preferences,
    lastLoginAt,
    profileUpdatedAt,
  ];

  @override
  String toString() {
    return 'UserProfile(avatarId: $avatarId, bio: $bio, location: $location, company: $company, jobTitle: $jobTitle)';
  }
}

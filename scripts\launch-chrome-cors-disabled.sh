#!/bin/bash

echo "========================================"
echo "  C-Face Terminal - Chrome CORS Bypass"
echo "========================================"
echo ""
echo "Starting Chrome with CORS disabled..."
echo "This allows cross-origin requests for development."
echo ""
echo "WARNING: Only use this for development!"
echo "Do NOT browse other websites with this instance."
echo ""

# Create temp directory if it doesn't exist
mkdir -p "/tmp/chrome_dev_session"

# Kill existing Chrome processes
pkill -f chrome > /dev/null 2>&1

# Wait a moment for processes to close
sleep 2

# Detect OS and set Chrome path
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CHROME_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    CHROME_PATH=$(which google-chrome || which chromium-browser || which chrome)
else
    echo "Unsupported OS: $OSTYPE"
    exit 1
fi

# Check if Chrome exists
if [ ! -f "$CHROME_PATH" ] && [ ! -x "$CHROME_PATH" ]; then
    echo "Chrome not found at: $CHROME_PATH"
    echo "Please install Chrome or update the path in this script."
    exit 1
fi

# Launch Chrome with CORS disabled
echo "Launching Chrome with flags:"
echo "--disable-web-security"
echo "--user-data-dir=\"/tmp/chrome_dev_session\""
echo "--allow-running-insecure-content"
echo "--disable-features=VizDisplayCompositor"
echo ""

"$CHROME_PATH" \
  --disable-web-security \
  --user-data-dir="/tmp/chrome_dev_session" \
  --allow-running-insecure-content \
  --disable-features=VizDisplayCompositor \
  --disable-extensions \
  --no-first-run \
  --no-default-browser-check \
  "http://localhost:8080" &

echo ""
echo "Chrome launched successfully!"
echo "You should see a warning banner about unsupported command-line flags."
echo "This confirms CORS is disabled."
echo ""
echo "Navigate to: http://localhost:8080"
echo ""

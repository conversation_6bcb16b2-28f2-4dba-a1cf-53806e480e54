name: c_face_terminal
description: "C-Face Terminal - Multi-App Flutter project with Clean Architecture supporting both mobile and terminal/kiosk applications."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.1.0
  google_mlkit_face_detection: ^0.13.1
  camera: ^0.10.6

  permission_handler: ^12.0.0+1
  provider: ^6.1.5
  intl: ^0.19.0
  sqflite: ^2.4.2
  path: ^1.9.1
  path_provider: ^2.1.5
  flutter_secure_storage: ^9.2.2
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2
  connectivity_plus: ^6.0.5
  shared_preferences: ^2.2.2
  battery_plus: ^6.0.2
  network_info_plus: ^5.0.3
  platform: ^3.1.5
  http: ^1.1.0
  convert: ^3.1.2

  # Face recognition dependencies
  tflite_flutter: ^0.11.0

  # Multi-app support dependencies
  get_it: ^8.0.0              # Dependency injection
  dartz: ^0.10.1              # Functional programming (Either, Option)
  equatable: ^2.0.7           # Value equality
  go_router: ^14.6.1          # Declarative routing

  # Additional utilities for multi-app
  freezed_annotation: ^2.4.4  # Code generation annotations
  json_annotation: ^4.9.0     # JSON serialization annotations
  cookie_jar: ^4.0.8
  mime: ^1.0.6                 # MIME type detection for file uploads
  http_parser: ^4.1.0          # HTTP parser for MediaType
  image: ^4.5.4

  # Logging
  logger: ^2.0.2

  # Terminal app dependencies
  uuid: ^4.5.1

  # Device management and always-on functionality
  wakelock_plus: ^1.2.8          # Keep screen awake
  screen_brightness: ^2.1.5       # Control screen brightness

  # Local packages
  relay_controller:
    path: lib/packages/relay_controller

  # Optional transport dependencies (commented out to avoid build issues)
  # mqtt_client: ^10.4.0
  # flutter_bluetooth_serial: ^0.4.0
  usb_serial: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Code generation dependencies
  build_runner: ^2.4.13       # Code generation runner
  freezed: ^2.5.7             # Code generation for data classes
  json_serializable: ^6.8.0   # JSON serialization code generation

  # Testing dependencies for multi-app
  mockito: ^5.4.4             # Mocking framework
  bloc_test: ^9.1.7           # Testing utilities (if using bloc)
  integration_test:           # Integration testing
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets configuration for multi-app structure
  assets:
    # Configuration files (only existing assets)
    - assets/config/

    # Model files
    - assets/models/

    # Face recognition models
    - lib/packages/face_recognition/assets/models/
    - lib/packages/face_recognition/assets/configs/

    # Shared assets (commented out until files are added)
    # - assets/shared/images/
    # - assets/shared/icons/
    # - assets/shared/fonts/

    # Mobile app specific assets (commented out until files are added)
    # - assets/mobile/images/
    # - assets/mobile/icons/

    # Terminal app specific assets (commented out until files are added)
    # - assets/terminal/images/
    # - assets/terminal/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts for multi-app (commented out until font files are added)
  # fonts:
  #   # Shared fonts (available to both apps)
  #   - family: AppFont
  #     fonts:
  #       - asset: assets/shared/fonts/AppFont-Regular.ttf
  #       - asset: assets/shared/fonts/AppFont-Bold.ttf
  #         weight: 700
  #       - asset: assets/shared/fonts/AppFont-Light.ttf
  #         weight: 300
  #
  #   # Terminal-specific fonts (larger, more readable for kiosk)
  #   - family: TerminalFont
  #     fonts:
  #       - asset: assets/terminal/fonts/TerminalFont-Regular.ttf
  #       - asset: assets/terminal/fonts/TerminalFont-Bold.ttf
  #         weight: 700

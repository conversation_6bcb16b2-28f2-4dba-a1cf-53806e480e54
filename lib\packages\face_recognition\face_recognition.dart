/// Face Recognition Package
/// 
/// Modular face recognition system designed for dual-app architecture:
/// - Mobile App: Management, enrollment, and administration
/// - Terminal App: Real-time detection and edge device control
library face_recognition;

import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

// Import statements for the exports (these would be the actual implementations)
import 'src/core/face_recognition_system.dart';
import 'src/core/models/face_recognition_config.dart';

// Core exports
export 'src/core/face_recognition_system.dart';
export 'src/core/models/face_recognition_config.dart';
export 'src/core/interfaces/face_detection_engine.dart';

// Mobile app exports (commented out until files are created)
// export 'src/mobile/mobile_face_system.dart';
// export 'src/mobile/enrollment/face_enrollment_service.dart';
// export 'src/mobile/management/user_management_service.dart';
// export 'src/mobile/analytics/attendance_analytics_service.dart';
// export 'src/mobile/configuration/terminal_config_service.dart';

// Terminal app exports (commented out until files are created)
// export 'src/terminal/terminal_face_system.dart';
// export 'src/terminal/triggers/side_effect_controller.dart';
// export 'src/terminal/recognition/terminal_recognition_service.dart';
// export 'src/terminal/sync/terminal_sync_service.dart';
// export 'src/terminal/optimization/device_optimizer.dart';

// Shared exports
export 'src/shared/engines/ultraface_detection_engine.dart';
export 'src/shared/engines/mediapipe_detection_engine.dart';
export 'src/shared/engines/ml_kit_detection_engine.dart';
export 'src/shared/network/network_detection_service.dart';
export 'src/shared/storage/face_database.dart';
export 'src/shared/utils/image_utils.dart';

/// Face Recognition Package Version
const String packageVersion = '1.0.0';

/// Package initialization for mobile apps
class FaceRecognitionMobile {
  static FaceRecognitionSystem? _instance;

  /// Get or create mobile face system instance
  static FaceRecognitionSystem getInstance() {
    _instance ??= FaceRecognitionSystem();
    return _instance!;
  }

  /// Initialize for mobile app
  static Future<FaceRecognitionSystem> initialize({
    MobileDeviceType deviceType = MobileDeviceType.android,
    PerformanceProfile performanceProfile = PerformanceProfile.balanced,
    String? serverEndpoint,
    String? apiKey,
  }) async {
    final config = FaceRecognitionConfig.forMobile(
      deviceType: deviceType,
      performanceProfile: performanceProfile,
      onlineEndpoint: serverEndpoint,
      apiKey: apiKey,
    );

    final system = getInstance();
    await system.initialize(config: config);

    return system;
  }

  /// Dispose mobile system
  static Future<void> dispose() async {
    await _instance?.dispose();
    _instance = null;
  }
}

/// Package initialization for terminal apps
class FaceRecognitionTerminal {
  static FaceRecognitionSystem? _instance;

  /// Get or create terminal face system instance
  static FaceRecognitionSystem getInstance() {
    _instance ??= FaceRecognitionSystem();
    return _instance!;
  }

  /// Initialize for terminal app
  static Future<FaceRecognitionSystem> initialize({
    TerminalDeviceType deviceType = TerminalDeviceType.generic,
    PerformanceProfile performanceProfile = PerformanceProfile.maxPerformance,
    String? serverEndpoint,
    String? apiKey,
  }) async {
    final config = FaceRecognitionConfig.forTerminal(
      deviceType: deviceType,
      performanceProfile: performanceProfile,
      onlineEndpoint: serverEndpoint,
      apiKey: apiKey,
    );

    final system = getInstance();
    await system.initialize(config: config);

    return system;
  }

  /// Dispose terminal system
  static Future<void> dispose() async {
    await _instance?.dispose();
    _instance = null;
  }
}

/// Utility class for package information
class FaceRecognitionInfo {
  /// Get package version
  static String get version => packageVersion;
  
  /// Get supported platforms for mobile
  static List<MobileDeviceType> get supportedMobilePlatforms => [
    MobileDeviceType.android,
    MobileDeviceType.ios,
  ];
  
  /// Get supported platforms for terminal
  static List<TerminalDeviceType> get supportedTerminalPlatforms => [
    TerminalDeviceType.telpoF8,
    TerminalDeviceType.androidTerminal,
    TerminalDeviceType.generic,
  ];
  
  /// Get available detection engines
  static List<DetectionEngine> get availableEngines => [
    DetectionEngine.ultraface,
    DetectionEngine.mediapipe,
    DetectionEngine.mlKit,
  ];
  
  /// Get recommended engine for platform
  static DetectionEngine getRecommendedEngine(PlatformType platform) {
    switch (platform) {
      case PlatformType.terminal:
        return DetectionEngine.ultraface;
      case PlatformType.mobile:
        return DetectionEngine.mediapipe;
    }
  }
  
  /// Check if engine is compatible with platform
  static bool isEngineCompatible(DetectionEngine engine, PlatformType platform) {
    // All engines are compatible with both platforms for now
    return true;
  }
}

/// Package configuration presets
class FaceRecognitionPresets {
  /// High performance preset for terminals
  static FaceRecognitionConfig get terminalHighPerformance => FaceRecognitionConfig.forTerminal(
    deviceType: TerminalDeviceType.telpoF8,
    performanceProfile: PerformanceProfile.maxPerformance,
  );
  
  /// Balanced preset for terminals
  static FaceRecognitionConfig get terminalBalanced => FaceRecognitionConfig.forTerminal(
    deviceType: TerminalDeviceType.generic,
    performanceProfile: PerformanceProfile.balanced,
  );
  
  /// High accuracy preset for mobile
  static FaceRecognitionConfig get mobileHighAccuracy => FaceRecognitionConfig.forMobile(
    deviceType: MobileDeviceType.android,
    performanceProfile: PerformanceProfile.balanced,
  );
  
  /// Power saver preset for mobile
  static FaceRecognitionConfig get mobilePowerSaver => FaceRecognitionConfig.forMobile(
    deviceType: MobileDeviceType.android,
    performanceProfile: PerformanceProfile.powerSaver,
  );
}

// Type aliases for backward compatibility
typedef MobileFaceSystem = FaceRecognitionSystem;
typedef TerminalFaceSystem = FaceRecognitionSystem;

// Placeholder classes for missing types
class EnrollmentSession {
  final String userId;
  final String userName;
  final String projectId;

  EnrollmentSession({
    required this.userId,
    required this.userName,
    required this.projectId,
  });
}

class EnrollmentProgress {
  final double progress;
  final String status;
  final String feedback;
  final bool isComplete;

  EnrollmentProgress({
    required this.progress,
    required this.status,
    this.feedback = '',
    this.isComplete = false,
  });
}

class TerminalRecognitionResult {
  final String? userId;
  final String? userName;
  final double confidence;
  final bool isRecognized;
  final bool hasAccess;

  TerminalRecognitionResult({
    this.userId,
    this.userName,
    required this.confidence,
    required this.isRecognized,
    this.hasAccess = false,
  });
}

class TerminalStats {
  final double averageFPS;
  final int totalProcessed;
  final int totalRecognized;
  final double recognitionRate;
  final bool isOnline;
  final int memoryUsage;
  final int consecutiveFailures;

  TerminalStats({
    required this.averageFPS,
    required this.totalProcessed,
    required this.totalRecognized,
    required this.recognitionRate,
    required this.isOnline,
    this.memoryUsage = 0,
    this.consecutiveFailures = 0,
  });
}

enum AccessLevel {
  admin,
  user,
  guest,
}

class EnrollmentResult {
  final bool success;
  final String message;
  final String? enrollmentId;
  final String? errorMessage;

  EnrollmentResult({
    required this.success,
    required this.message,
    this.enrollmentId,
    this.errorMessage,
  });
}

// Additional imports would go here when files are created

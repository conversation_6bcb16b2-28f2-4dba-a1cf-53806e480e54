# Placeholder file for TensorFlow Lite models
# 
# This directory should contain the following model files:
# - ultraface_320.tflite (UltraFace detection model)
# - mobilefacenet.tflite (MobileFaceNet recognition model)
# - mediapipe_face_detection.tflite (MediaPipe face detection model)
#
# Download these models from their respective sources:
# - UltraFace: https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB
# - MobileFaceNet: https://github.com/sirius-ai/MobileFaceNet_TF
# - MediaPipe: https://github.com/google/mediapipe
#
# Note: This placeholder file ensures the assets directory is included in the build
# even when model files are not yet downloaded.

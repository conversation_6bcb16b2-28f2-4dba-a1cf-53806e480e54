# C-Face Terminal Multi-App Build System

## 🏗️ Overview

This project uses a multi-app architecture with separate entry points for mobile and terminal/kiosk applications. The build system provides automated scripts, CI/CD integration, and development tools.

## 📱 Applications

### Mobile App
- **Entry Point**: `lib/apps/mobile/main_mobile.dart`
- **Target**: Mobile devices (phones, tablets)
- **Features**: Responsive UI, system navigation, Material 3
- **Orientation**: Portrait + Landscape support

### Terminal App
- **Entry Point**: `lib/apps/terminal/main_terminal.dart`
- **Target**: Kiosk/Terminal devices
- **Features**: Immersive UI, large buttons, admin access
- **Orientation**: Landscape only (kiosk optimized)

## 🔧 Build Commands

### Development (Debug Mode)
```bash
# Run mobile app with hot reload
./scripts/run_mobile.sh

# Run terminal app with hot reload
./scripts/run_terminal.sh

# Test build configurations
./scripts/test_builds.sh
```

### Production (Release Mode)
```bash
# Build mobile APK
./scripts/build_mobile.sh

# Build terminal APK
./scripts/build_terminal.sh

# Build both APKs
./scripts/build_all.sh
```

### Using Makefile (if available)
```bash
# Show all available commands
make help

# Development shortcuts
make run-mobile          # Run mobile app
make run-terminal        # Run terminal app
make test               # Test configurations

# Production shortcuts
make build-mobile       # Build mobile APK
make build-terminal     # Build terminal APK
make build-all         # Build both APKs

# Complete workflows
make dev-mobile        # deps + run mobile
make prod-all          # clean + deps + build all
```

## 📊 Build Outputs

### Mobile App
- **APK**: `build/app/outputs/flutter-apk/app-release.apk`
- **Named**: `build/app/outputs/flutter-apk/c-face-mobile-release.apk`

### Terminal App
- **APK**: `build/app/outputs/flutter-apk/app-release.apk`
- **Named**: `build/app/outputs/flutter-apk/c-face-terminal-release.apk`

## 🧪 Testing & Validation

### Build Configuration Test
```bash
./scripts/test_builds.sh
```

This script:
- Validates entry point files exist
- Runs Flutter analyze on both apps
- Checks for build configuration issues
- Reports test results

### Code Analysis
```bash
# Analyze mobile app
flutter analyze lib/apps/mobile/main_mobile.dart

# Analyze terminal app
flutter analyze lib/apps/terminal/main_terminal.dart

# Analyze entire project
flutter analyze
```

## 🚀 CI/CD Pipeline

### GitHub Actions
- **File**: `.github/workflows/multi_app_build.yml`
- **Triggers**: Push to main/develop, Pull requests
- **Jobs**:
  - `test`: Validate build configurations
  - `build-mobile`: Build mobile APK
  - `build-terminal`: Build terminal APK
  - `build-all`: Build both apps (main branch only)

### Artifacts
- Mobile APK (30 days retention)
- Terminal APK (30 days retention)
- Combined APKs (90 days retention for main branch)

## 📁 Project Structure

```
├── lib/
│   └── apps/
│       ├── mobile/
│       │   └── main_mobile.dart      # Mobile entry point
│       └── terminal/
│           └── main_terminal.dart    # Terminal entry point
├── scripts/
│   ├── build_mobile.sh              # Build mobile APK
│   ├── build_terminal.sh            # Build terminal APK
│   ├── build_all.sh                 # Build both APKs
│   ├── run_mobile.sh                # Run mobile (debug)
│   ├── run_terminal.sh              # Run terminal (debug)
│   └── test_builds.sh               # Test configurations
├── .github/
│   └── workflows/
│       └── multi_app_build.yml      # CI/CD pipeline
├── Makefile                         # Build automation
└── BUILD_SYSTEM.md                  # This documentation
```

## 🎯 App-Specific Features

### Mobile App Features
- **UI**: Standard mobile interface with system navigation
- **Theme**: Material 3 with blue color scheme
- **Navigation**: Bottom navigation ready
- **Orientation**: Portrait and landscape support
- **Features**: Face detection, user management, offline support

### Terminal App Features
- **UI**: Kiosk mode with immersive fullscreen
- **Theme**: Material 3 with orange color scheme
- **Buttons**: Large touch-friendly (200x200px)
- **Orientation**: Landscape only
- **Features**: Kiosk mode, auto-timeout, admin access

## 🔍 Troubleshooting

### Common Issues

#### Build Fails
```bash
# Clean and retry
flutter clean
flutter pub get
./scripts/build_mobile.sh
```

#### Permission Denied
```bash
# Fix script permissions
chmod +x scripts/*.sh
```

#### Flutter Not Found
```bash
# Check Flutter installation
flutter --version
which flutter
```

### Build Script Features

#### Error Handling
- Flutter availability check
- Build result validation
- APK size reporting
- Comprehensive error messages

#### Build Process
1. Clean previous builds
2. Get dependencies
3. Build release APK
4. Verify build success
5. Report APK location and size

## 📝 Development Workflow

### Starting Development
```bash
# 1. Get dependencies
flutter pub get

# 2. Test configurations
./scripts/test_builds.sh

# 3. Run desired app
./scripts/run_mobile.sh     # For mobile development
./scripts/run_terminal.sh   # For terminal development
```

### Before Deployment
```bash
# 1. Test both configurations
./scripts/test_builds.sh

# 2. Build both apps
./scripts/build_all.sh

# 3. Verify APKs created
ls -la build/app/outputs/flutter-apk/
```

## 🎉 Success Indicators

### Build System Ready When:
- ✅ All scripts executable (`ls -la scripts/`)
- ✅ Build tests pass (`./scripts/test_builds.sh`)
- ✅ Both entry points analyze clean
- ✅ CI/CD pipeline configured
- ✅ APKs build successfully

---

**Build System Status**: ✅ **READY**  
**Last Updated**: 2025-06-26  
**Version**: 1.0.0

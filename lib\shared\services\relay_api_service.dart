import 'package:flutter/foundation.dart';
import 'http_client_service.dart';
import 'api_endpoints.dart';
import '../core/errors/relay_exceptions.dart';
import '../core/config/config_helper.dart';
import 'relay_management_service.dart';

/// API Service for relay device registration and management with server
class RelayApiService {
  static RelayApiService? _instance;
  static RelayApiService get instance => _instance ??= RelayApiService._();

  RelayApiService._();

  late HttpClientService _httpClient;
  bool _isInitialized = false;

  /// Initialize the relay API service
  Future<void> initialize(HttpClientService httpClient) async {
    _httpClient = httpClient;
    _isInitialized = true;
    debugPrint('✅ RelayApiService initialized');
  }

  /// Ensure service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw RelayRegistrationException('RelayApiService not initialized');
    }
  }

  /// Register relay device with server using secure API
  ///
  /// [deviceConfig] contains relay device configuration
  /// [additionalInfo] contains additional device information
  /// [useSecureApi] determines whether to use secure API or legacy API
  Future<RelayRegistrationResponse> registerDevice({
    required RelayDeviceConfig deviceConfig,
    Map<String, dynamic>? additionalInfo,
    bool useSecureApi = true,
  }) async {
    _ensureInitialized();

    try {
      final registrationData = _buildRegistrationData(deviceConfig, additionalInfo, useSecureApi);

      // Choose endpoint based on API type
      final endpoint = useSecureApi ? ApiEndpoints.registerDevice : ApiEndpoints.registerDeviceLegacy;

      final response = await _httpClient.post<Map<String, dynamic>>(
        endpoint,
        data: registrationData,
      );

      if (response.success && response.data != null) {
        return RelayRegistrationResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Device registration failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to register relay device: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Update relay device information
  /// 
  /// [deviceId] is the relay device ID
  /// [updates] contains the fields to update
  Future<RelayUpdateResponse> updateDevice({
    required String deviceId,
    required Map<String, dynamic> updates,
  }) async {
    _ensureInitialized();

    try {
      final response = await _httpClient.put<Map<String, dynamic>>(
        ApiEndpoints.updateDevice(deviceId),
        data: updates,
      );

      if (response.success && response.data != null) {
        return RelayUpdateResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Device update failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to update relay device: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Get relay device information from server
  /// 
  /// [deviceId] is the relay device ID
  Future<RelayDeviceInfo> getDeviceInfo(String deviceId) async {
    _ensureInitialized();

    try {
      final response = await _httpClient.get<Map<String, dynamic>>(
        ApiEndpoints.deviceDetail(deviceId),
      );

      if (response.success && response.data != null) {
        return RelayDeviceInfo.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Failed to get device info: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to get relay device info: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Send relay control command to server
  /// 
  /// [deviceId] is the relay device ID
  /// [command] is the relay command to execute
  Future<RelayCommandResponse> sendRelayCommand({
    required String deviceId,
    required RelayCommand command,
  }) async {
    _ensureInitialized();

    try {
      final commandData = {
        'deviceId': deviceId,
        'command': command.command,
        'relayIndex': command.relayIndex,
        'action': command.action?.name,
        'duration': command.duration?.inMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiEndpoints.relayControl,
        data: commandData,
      );

      if (response.success && response.data != null) {
        return RelayCommandResponse.fromJson(response.data!);
      } else {
        throw RelayCommandException(
          'Relay command failed: ${response.message}',
          command: command.command,
          relayIndex: command.relayIndex,
        );
      }
    } catch (e) {
      throw RelayCommandException(
        'Failed to send relay command: $e',
        command: command.command,
        relayIndex: command.relayIndex,
        cause: e,
      );
    }
  }

  /// Get relay device history from server
  /// 
  /// [deviceId] is the relay device ID
  /// [limit] is the maximum number of history entries to return
  /// [offset] is the offset for pagination
  Future<RelayHistoryResponse> getDeviceHistory({
    required String deviceId,
    int limit = 50,
    int offset = 0,
  }) async {
    _ensureInitialized();

    try {
      final queryParams = {
        'limit': limit,
        'offset': offset,
      };

      final response = await _httpClient.get<Map<String, dynamic>>(
        ApiEndpoints.deviceHistory(deviceId),
        queryParameters: queryParams,
      );

      if (response.success && response.data != null) {
        return RelayHistoryResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Failed to get device history: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to get relay device history: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Delete relay device from server
  /// 
  /// [deviceId] is the relay device ID
  Future<void> deleteDevice(String deviceId) async {
    _ensureInitialized();

    try {
      final response = await _httpClient.delete<Map<String, dynamic>>(
        ApiEndpoints.deleteDevice(deviceId),
      );

      if (!response.success) {
        throw RelayRegistrationException(
          'Device deletion failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to delete relay device: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Get all relay devices from server
  /// 
  /// [limit] is the maximum number of devices to return
  /// [offset] is the offset for pagination
  /// [filter] is optional filter criteria
  Future<RelayDeviceListResponse> getDevices({
    int limit = 50,
    int offset = 0,
    Map<String, dynamic>? filter,
  }) async {
    _ensureInitialized();

    try {
      final queryParams = {
        'limit': limit,
        'offset': offset,
        if (filter != null) ...filter,
      };

      final response = await _httpClient.get<Map<String, dynamic>>(
        ApiEndpoints.devices,
        queryParameters: queryParams,
      );

      if (response.success && response.data != null) {
        return RelayDeviceListResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Failed to get devices: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to get relay devices: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Ping relay device to check connectivity
  /// 
  /// [deviceId] is the relay device ID
  Future<RelayPingResponse> pingDevice(String deviceId) async {
    _ensureInitialized();

    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiEndpoints.pingDevice(deviceId),
        data: {
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success && response.data != null) {
        return RelayPingResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Device ping failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to ping relay device: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Build registration data for relay device
  Map<String, dynamic> _buildRegistrationData(
    RelayDeviceConfig deviceConfig,
    Map<String, dynamic>? additionalInfo,
    bool useSecureApi,
  ) {
    if (useSecureApi) {
      // Secure API format (matches test server /api/device/register)
      return {
        'device_id': deviceConfig.deviceId,
        'device_type': 'relay',
        'device_name': deviceConfig.deviceName,
        'hardware_hash': _generateHardwareHash(deviceConfig),
        'app_version': '1.0.0',
        'terminal_id': _extractTerminalId(deviceConfig.deviceId),
        'configuration': {
          'relayCount': deviceConfig.relayCount,
          'baudRate': deviceConfig.baudRate,
          'connectionType': 'usb_serial',
          'deviceProfile': _getDeviceProfileName(),
          'relayConfigs': deviceConfig.relayConfigs.map((config) => {
            'index': config.index,
            'name': config.name,
            'description': config.description,
            'defaultTimeoutMs': config.defaultTimeoutMs,
            'isEnabled': config.isEnabled,
          }).toList(),
        },
        'capabilities': [
          'relay_control',
          'individual_control',
          'timed_control',
          'all_control',
          'toggle_support',
          'status_monitoring',
        ],
        'metadata': {
          'platform': 'flutter',
          'sdkVersion': '3.0.0',
          ...?additionalInfo,
        },
      };
    } else {
      // Legacy API format (matches test server /register)
      return {
        'deviceId': deviceConfig.deviceId,
        'deviceName': deviceConfig.deviceName,
        'type': 'relay',
        'terminalId': _extractTerminalId(deviceConfig.deviceId),
        'configuration': {
          'relayCount': deviceConfig.relayCount,
          'baudRate': deviceConfig.baudRate,
          'connectionType': 'usb_serial',
          'deviceProfile': _getDeviceProfileName(),
        },
        'metadata': {
          'appVersion': '1.0.0',
          'platform': 'flutter',
          ...?additionalInfo,
        },
      };
    }
  }

  /// Generate hardware hash for device identification
  String _generateHardwareHash(RelayDeviceConfig deviceConfig) {
    final data = '${deviceConfig.deviceId}_${deviceConfig.baudRate}_${deviceConfig.relayCount}';
    return data.hashCode.toString();
  }

  /// Extract terminal ID from device ID if it follows naming convention
  String? _extractTerminalId(String deviceId) {
    // Check if device ID follows T-XXXX-RNN or T-XXXX-NAME format
    final relayPattern = RegExp(r'^(T-[A-Z0-9]{4})-');
    final match = relayPattern.firstMatch(deviceId);
    return match?.group(1);
  }

  /// Get device profile name from configuration
  String _getDeviceProfileName() {
    try {
      // Import ConfigHelper to get configuration values
      return ConfigHelper.getValue('relay.device_profile', 'esp32');
    } catch (e) {
      // Fallback to default if configuration is not available
      return 'esp32';
    }
  }

  /// Get server URL for error reporting
  String _getServerUrl() {
    try {
      return _httpClient.toString(); // Fallback representation
    } catch (e) {
      return 'unknown';
    }
  }

  /// Send face recognition request to server
  ///
  /// [deviceId] is the relay device ID
  /// [imageData] is the base64 encoded face image
  /// [confidenceScore] is the face detection confidence
  /// [metadata] contains additional recognition metadata
  Future<FaceRecognitionResponse> recognizeFace({
    required String deviceId,
    required String imageData,
    double? confidenceScore,
    Map<String, dynamic>? metadata,
  }) async {
    _ensureInitialized();

    try {
      final requestData = {
        'image_data': imageData,
        'confidence_score': confidenceScore,
        'metadata': metadata,
      };

      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiEndpoints.faceRecognize,
        data: requestData,
      );

      if (response.success && response.data != null) {
        return FaceRecognitionResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Face recognition failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to recognize face: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Send secure message to server
  ///
  /// [deviceId] is the relay device ID
  /// [messageType] is the type of message
  /// [payload] contains the message data
  /// [messageId] is optional message identifier
  Future<SecureMessageResponse> sendSecureMessage({
    required String deviceId,
    required String messageType,
    required Map<String, dynamic> payload,
    String? messageId,
  }) async {
    _ensureInitialized();

    try {
      final requestData = {
        'type': messageType,
        'payload': payload,
        'message_id': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiEndpoints.secureMessage,
        data: requestData,
      );

      if (response.success && response.data != null) {
        return SecureMessageResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Secure message failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to send secure message: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }

  /// Send plain text message to server (no authentication required)
  ///
  /// [deviceId] is the relay device ID
  /// [messageType] is the type of message
  /// [payload] contains the message data
  /// [deviceType] is the type of device
  /// [messageId] is optional message identifier
  Future<PlainMessageResponse> sendPlainMessage({
    required String deviceId,
    required String messageType,
    required Map<String, dynamic> payload,
    String? deviceType,
    String? messageId,
  }) async {
    _ensureInitialized();

    try {
      final requestData = {
        'type': messageType,
        'payload': payload,
        'device_id': deviceId,
        'device_type': deviceType ?? 'relay',
        'message_id': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiEndpoints.plainMessage,
        data: requestData,
      );

      if (response.success && response.data != null) {
        return PlainMessageResponse.fromJson(response.data!);
      } else {
        throw RelayRegistrationException(
          'Plain message failed: ${response.message}',
          serverUrl: _getServerUrl(),
        );
      }
    } catch (e) {
      throw RelayRegistrationException(
        'Failed to send plain message: $e',
        serverUrl: _getServerUrl(),
        cause: e,
      );
    }
  }
}

/// Response model for relay device registration
class RelayRegistrationResponse {
  final String deviceId;
  final String status;
  final String message;
  final Map<String, dynamic>? serverConfig;
  final DateTime registeredAt;

  const RelayRegistrationResponse({
    required this.deviceId,
    required this.status,
    required this.message,
    this.serverConfig,
    required this.registeredAt,
  });

  factory RelayRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return RelayRegistrationResponse(
      deviceId: json['deviceId'] as String,
      status: json['status'] as String,
      message: json['message'] as String,
      serverConfig: json['serverConfig'] as Map<String, dynamic>?,
      registeredAt: DateTime.parse(json['registeredAt'] as String),
    );
  }

  Map<String, dynamic> toJson() => {
    'deviceId': deviceId,
    'status': status,
    'message': message,
    'serverConfig': serverConfig,
    'registeredAt': registeredAt.toIso8601String(),
  };
}

/// Response model for relay device update
class RelayUpdateResponse {
  final String deviceId;
  final String status;
  final String message;
  final Map<String, dynamic>? updatedFields;
  final DateTime updatedAt;

  const RelayUpdateResponse({
    required this.deviceId,
    required this.status,
    required this.message,
    this.updatedFields,
    required this.updatedAt,
  });

  factory RelayUpdateResponse.fromJson(Map<String, dynamic> json) {
    return RelayUpdateResponse(
      deviceId: json['deviceId'] as String,
      status: json['status'] as String,
      message: json['message'] as String,
      updatedFields: json['updatedFields'] as Map<String, dynamic>?,
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
}

/// Response model for relay device information
class RelayDeviceInfo {
  final String deviceId;
  final String deviceName;
  final String deviceType;
  final String status;
  final Map<String, dynamic> configuration;
  final List<String> capabilities;
  final DateTime lastSeen;
  final DateTime registeredAt;

  const RelayDeviceInfo({
    required this.deviceId,
    required this.deviceName,
    required this.deviceType,
    required this.status,
    required this.configuration,
    required this.capabilities,
    required this.lastSeen,
    required this.registeredAt,
  });

  factory RelayDeviceInfo.fromJson(Map<String, dynamic> json) {
    return RelayDeviceInfo(
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      deviceType: json['deviceType'] as String,
      status: json['status'] as String,
      configuration: json['configuration'] as Map<String, dynamic>,
      capabilities: List<String>.from(json['capabilities'] as List),
      lastSeen: DateTime.parse(json['lastSeen'] as String),
      registeredAt: DateTime.parse(json['registeredAt'] as String),
    );
  }
}

/// Model for relay command
class RelayCommand {
  final String command;
  final int? relayIndex;
  final RelayAction? action;
  final Duration? duration;

  const RelayCommand({
    required this.command,
    this.relayIndex,
    this.action,
    this.duration,
  });

  factory RelayCommand.controlRelay(int relayIndex, RelayAction action) {
    return RelayCommand(
      command: 'R$relayIndex:${action.name.toUpperCase()}',
      relayIndex: relayIndex,
      action: action,
    );
  }

  factory RelayCommand.timedRelay(int relayIndex, Duration duration) {
    return RelayCommand(
      command: 'R$relayIndex:${duration.inMilliseconds}',
      relayIndex: relayIndex,
      duration: duration,
    );
  }

  factory RelayCommand.allRelays(RelayAction action) {
    return RelayCommand(
      command: 'ALL:${action == RelayAction.on ? '1' : '0'}',
      action: action,
    );
  }

  factory RelayCommand.raw(String rawCommand) {
    return RelayCommand(command: rawCommand);
  }
}

/// Relay actions enum
enum RelayAction {
  on,
  off,
  toggle,
}

/// Response model for relay command execution
class RelayCommandResponse {
  final String commandId;
  final String status;
  final String message;
  final Map<String, dynamic>? result;
  final DateTime executedAt;

  const RelayCommandResponse({
    required this.commandId,
    required this.status,
    required this.message,
    this.result,
    required this.executedAt,
  });

  factory RelayCommandResponse.fromJson(Map<String, dynamic> json) {
    return RelayCommandResponse(
      commandId: json['commandId'] as String,
      status: json['status'] as String,
      message: json['message'] as String,
      result: json['result'] as Map<String, dynamic>?,
      executedAt: DateTime.parse(json['executedAt'] as String),
    );
  }
}

/// Response model for relay device history
class RelayHistoryResponse {
  final List<RelayHistoryEntry> entries;
  final int total;
  final int limit;
  final int offset;

  const RelayHistoryResponse({
    required this.entries,
    required this.total,
    required this.limit,
    required this.offset,
  });

  factory RelayHistoryResponse.fromJson(Map<String, dynamic> json) {
    return RelayHistoryResponse(
      entries: (json['entries'] as List)
          .map((e) => RelayHistoryEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      limit: json['limit'] as int,
      offset: json['offset'] as int,
    );
  }
}

/// Model for relay history entry
class RelayHistoryEntry {
  final String id;
  final String deviceId;
  final String action;
  final Map<String, dynamic>? details;
  final DateTime timestamp;

  const RelayHistoryEntry({
    required this.id,
    required this.deviceId,
    required this.action,
    this.details,
    required this.timestamp,
  });

  factory RelayHistoryEntry.fromJson(Map<String, dynamic> json) {
    return RelayHistoryEntry(
      id: json['id'] as String,
      deviceId: json['deviceId'] as String,
      action: json['action'] as String,
      details: json['details'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Response model for device list
class RelayDeviceListResponse {
  final List<RelayDeviceInfo> devices;
  final int total;
  final int limit;
  final int offset;

  const RelayDeviceListResponse({
    required this.devices,
    required this.total,
    required this.limit,
    required this.offset,
  });

  factory RelayDeviceListResponse.fromJson(Map<String, dynamic> json) {
    return RelayDeviceListResponse(
      devices: (json['devices'] as List)
          .map((e) => RelayDeviceInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
      limit: json['limit'] as int,
      offset: json['offset'] as int,
    );
  }
}

/// Response model for device ping
class RelayPingResponse {
  final String deviceId;
  final String status;
  final int latencyMs;
  final DateTime timestamp;

  const RelayPingResponse({
    required this.deviceId,
    required this.status,
    required this.latencyMs,
    required this.timestamp,
  });

  factory RelayPingResponse.fromJson(Map<String, dynamic> json) {
    return RelayPingResponse(
      deviceId: json['deviceId'] as String,
      status: json['status'] as String,
      latencyMs: json['latencyMs'] as int,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Response model for face recognition requests
class FaceRecognitionResponse {
  final bool success;
  final bool recognized;
  final Map<String, dynamic>? user;
  final double? confidence;
  final bool? allowAccess;
  final String? reason;
  final String recognitionId;
  final DateTime timestamp;
  final int? processingTime;
  final String? savedImagePath;

  const FaceRecognitionResponse({
    required this.success,
    required this.recognized,
    this.user,
    this.confidence,
    this.allowAccess,
    this.reason,
    required this.recognitionId,
    required this.timestamp,
    this.processingTime,
    this.savedImagePath,
  });

  factory FaceRecognitionResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? json;
    return FaceRecognitionResponse(
      success: json['success'] as bool? ?? true,
      recognized: data['recognized'] as bool? ?? false,
      user: data['user'] as Map<String, dynamic>?,
      confidence: (data['confidence'] as num?)?.toDouble(),
      allowAccess: data['allowAccess'] as bool?,
      reason: data['reason'] as String?,
      recognitionId: data['recognitionId'] as String? ?? '',
      timestamp: DateTime.parse(data['timestamp'] as String? ?? DateTime.now().toIso8601String()),
      processingTime: data['processingTime'] as int?,
      savedImagePath: data['savedImagePath'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
    'success': success,
    'recognized': recognized,
    'user': user,
    'confidence': confidence,
    'allowAccess': allowAccess,
    'reason': reason,
    'recognitionId': recognitionId,
    'timestamp': timestamp.toIso8601String(),
    'processingTime': processingTime,
    'savedImagePath': savedImagePath,
  };
}

/// Response model for secure message requests
class SecureMessageResponse {
  final bool success;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? messageId;

  const SecureMessageResponse({
    required this.success,
    required this.data,
    required this.timestamp,
    this.messageId,
  });

  factory SecureMessageResponse.fromJson(Map<String, dynamic> json) {
    return SecureMessageResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>? ?? {},
      timestamp: DateTime.parse(json['timestamp']?.toString() ?? DateTime.now().toIso8601String()),
      messageId: json['message_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
    'success': success,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'message_id': messageId,
  };
}

/// Response model for plain text message requests
class PlainMessageResponse {
  final bool success;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? messageId;

  const PlainMessageResponse({
    required this.success,
    required this.data,
    required this.timestamp,
    this.messageId,
  });

  factory PlainMessageResponse.fromJson(Map<String, dynamic> json) {
    return PlainMessageResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>? ?? {},
      timestamp: DateTime.parse(json['timestamp']?.toString() ?? DateTime.now().toIso8601String()),
      messageId: json['message_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
    'success': success,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
    'message_id': messageId,
  };
}
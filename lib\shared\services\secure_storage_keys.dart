/// Constants cho các key được sử dụng trong SecureStorageService
/// Tập trung quản lý tất cả các key để tránh trùng lặp và dễ bảo trì
class SecureStorageKeys {
  // Private constructor để ngăn việc khởi tạo instance
  SecureStorageKeys._();

  // ========== USER AUTHENTICATION ==========
  /// Key để lưu trữ access token
  static const String accessToken = 'access_token';
  
  /// Key để lưu trữ refresh token
  static const String refreshToken = 'refresh_token';
  
  /// Key để lưu trữ user ID
  static const String userId = 'user_id';
  
  /// Key để lưu trữ username
  static const String username = 'username';
  
  /// Key để lưu trữ email
  static const String userEmail = 'user_email';

  // ========== FACE DETECTION SETTINGS ==========
  /// Key để lưu trữ cài đặt độ nhạy của face detection
  static const String faceDetectionSensitivity = 'face_detection_sensitivity';
  
  /// Key để lưu trữ cài đặt có bật face detection hay không
  static const String faceDetectionEnabled = 'face_detection_enabled';
  
  /// Key để lưu trữ danh sách các khuôn mặt đã được đăng ký
  static const String registeredFaces = 'registered_faces';
  
  /// Key để lưu trữ ngưỡng confidence cho face recognition
  static const String faceRecognitionThreshold = 'face_recognition_threshold';

  // ========== APP SETTINGS ==========
  /// Key để lưu trữ ngôn ngữ ứng dụng
  static const String appLanguage = 'app_language';
  
  /// Key để lưu trữ theme mode (light/dark)
  static const String themeMode = 'theme_mode';
  
  /// Key để lưu trữ cài đặt có hiển thị onboarding hay không
  static const String showOnboarding = 'show_onboarding';
  
  /// Key để lưu trữ phiên bản ứng dụng cuối cùng được sử dụng
  static const String lastAppVersion = 'last_app_version';

  // ========== SECURITY SETTINGS ==========
  /// Key để lưu trữ cài đặt có yêu cầu biometric authentication hay không
  static const String biometricAuthEnabled = 'biometric_auth_enabled';
  
  /// Key để lưu trữ cài đặt có yêu cầu PIN hay không
  static const String pinAuthEnabled = 'pin_auth_enabled';
  
  /// Key để lưu trữ PIN (đã được hash)
  static const String userPin = 'user_pin';
  
  /// Key để lưu trữ số lần thử đăng nhập sai
  static const String failedLoginAttempts = 'failed_login_attempts';
  
  /// Key để lưu trữ thời gian bị khóa tài khoản
  static const String accountLockTime = 'account_lock_time';

  // ========== TERMINAL SETTINGS ==========
  /// Key để lưu trữ cài đặt terminal
  static const String terminalSettings = 'terminal_settings';
  
  /// Key để lưu trữ lịch sử lệnh terminal
  static const String terminalHistory = 'terminal_history';
  
  /// Key để lưu trữ cài đặt font size cho terminal
  static const String terminalFontSize = 'terminal_font_size';
  
  /// Key để lưu trữ cài đặt màu sắc cho terminal
  static const String terminalColorScheme = 'terminal_color_scheme';

  // ========== DEVICE INFO ==========
  /// Key để lưu trữ device ID
  static const String deviceId = 'device_id';
  
  /// Key để lưu trữ device name
  static const String deviceName = 'device_name';
  
  /// Key để lưu trữ thông tin device fingerprint
  static const String deviceFingerprint = 'device_fingerprint';

  // ========== BACKUP & SYNC ==========
  /// Key để lưu trữ cài đặt auto backup
  static const String autoBackupEnabled = 'auto_backup_enabled';
  
  /// Key để lưu trữ thời gian backup cuối cùng
  static const String lastBackupTime = 'last_backup_time';
  
  /// Key để lưu trữ cài đặt sync với cloud
  static const String cloudSyncEnabled = 'cloud_sync_enabled';

  // ========== HELPER METHODS ==========
  
  /// Lấy tất cả các key được định nghĩa trong class này
  static List<String> getAllKeys() {
    return [
      // User Authentication
      accessToken,
      refreshToken,
      userId,
      username,
      userEmail,
      
      // Face Detection Settings
      faceDetectionSensitivity,
      faceDetectionEnabled,
      registeredFaces,
      faceRecognitionThreshold,
      
      // App Settings
      appLanguage,
      themeMode,
      showOnboarding,
      lastAppVersion,
      
      // Security Settings
      biometricAuthEnabled,
      pinAuthEnabled,
      userPin,
      failedLoginAttempts,
      accountLockTime,
      
      // Terminal Settings
      terminalSettings,
      terminalHistory,
      terminalFontSize,
      terminalColorScheme,
      
      // Device Info
      deviceId,
      deviceName,
      deviceFingerprint,
      
      // Backup & Sync
      autoBackupEnabled,
      lastBackupTime,
      cloudSyncEnabled,
    ];
  }
  
  /// Kiểm tra xem một key có hợp lệ hay không
  static bool isValidKey(String key) {
    return getAllKeys().contains(key);
  }
  
  /// Lấy danh sách key theo category
  static List<String> getKeysByCategory(String category) {
    switch (category.toLowerCase()) {
      case 'auth':
      case 'authentication':
        return [accessToken, refreshToken, userId, username, userEmail];
      
      case 'face':
      case 'face_detection':
        return [
          faceDetectionSensitivity,
          faceDetectionEnabled,
          registeredFaces,
          faceRecognitionThreshold,
        ];
      
      case 'app':
      case 'app_settings':
        return [appLanguage, themeMode, showOnboarding, lastAppVersion];
      
      case 'security':
        return [
          biometricAuthEnabled,
          pinAuthEnabled,
          userPin,
          failedLoginAttempts,
          accountLockTime,
        ];
      
      case 'terminal':
        return [
          terminalSettings,
          terminalHistory,
          terminalFontSize,
          terminalColorScheme,
        ];
      
      case 'device':
        return [deviceId, deviceName, deviceFingerprint];
      
      case 'backup':
      case 'sync':
        return [autoBackupEnabled, lastBackupTime, cloudSyncEnabled];
      
      default:
        return [];
    }
  }
}

import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:get_it/get_it.dart';
import '../core/network/api_client.dart';
import '../core/constants/face_cropping_constants.dart';
import 'face_cropping_service.dart';
import 'face_cropping_side_effects_handler.dart';

/// Service for handling face cropping API operations with three different approaches
class FaceCroppingApiService {
  static const String _logTag = '🌐 FaceCroppingAPI';
  
  final ApiClient _apiClient = GetIt.instance<ApiClient>();
  
  // ============================================================================
  // METHOD 1: SYNCHRONOUS API APPROACH
  // ============================================================================
  
  /// Method 1: Synchronous face cropping API call with direct response handling
  /// 
  /// [imagePath] - Path to the source image
  /// [face] - Detected face object from ML Kit
  /// [context] - Additional context data for processing
  /// [enabledSideEffects] - List of side effects to execute
  /// [cropPadding] - Padding around face region (default 0.2)
  /// [outputQuality] - JPEG quality for cropped image (default 85)
  /// 
  /// Returns: Complete API response with side effects results
  Future<FaceCroppingSyncResult> processFaceCroppingSynchronous({
    required String imagePath,
    required Face face,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
    double cropPadding = FaceCroppingConstants.defaultPadding,
    int outputQuality = FaceCroppingConstants.defaultOutputQuality,
  }) async {
    final startTime = DateTime.now();
    
    try {
      debugPrint('$_logTag Starting synchronous face cropping process');
      
      // Step 1: Crop the face from the image
      final croppedImagePath = await FaceCroppingService.cropFaceFromImage(
        imagePath: imagePath,
        face: face,
        padding: cropPadding,
        outputQuality: outputQuality,
      );
      
      if (croppedImagePath == null) {
        throw Exception('Failed to crop face from image');
      }
      
      debugPrint('$_logTag Face cropped successfully: $croppedImagePath');
      
      // Step 2: Prepare API request data
      final requestData = await _prepareApiRequestData(
        croppedImagePath: croppedImagePath,
        originalImagePath: imagePath,
        face: face,
        context: context ?? {},
      );
      
      // Step 3: Make synchronous API call
      final apiResponse = await _makeSynchronousApiCall(requestData);
      
      debugPrint('$_logTag API call completed successfully');
      
      // Step 4: Execute side effects
      final sideEffectsResults = await FaceCroppingSideEffectsHandler.executeSideEffects(
        response: apiResponse,
        enabledSideEffects: enabledSideEffects,
        context: context,
      );
      
      // Step 5: Clean up temporary files
      await _cleanupTempFiles([croppedImagePath]);
      
      final totalDuration = DateTime.now().difference(startTime);
      
      debugPrint('$_logTag ✅ Synchronous processing completed in ${totalDuration.inMilliseconds}ms');
      
      return FaceCroppingSyncResult(
        success: true,
        apiResponse: apiResponse,
        sideEffectsResults: sideEffectsResults,
        processingTime: totalDuration,
        croppedImagePath: croppedImagePath,
        originalImagePath: imagePath,
      );
      
    } catch (e, stackTrace) {
      final totalDuration = DateTime.now().difference(startTime);
      
      debugPrint('$_logTag ❌ Synchronous processing failed: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      
      return FaceCroppingSyncResult(
        success: false,
        error: e.toString(),
        processingTime: totalDuration,
        originalImagePath: imagePath,
      );
    }
  }
  
  /// Make synchronous API call to face cropping endpoint
  Future<Map<String, dynamic>> _makeSynchronousApiCall(
    Map<String, dynamic> requestData,
  ) async {
    try {
      debugPrint('$_logTag Making synchronous API call');
      
      final response = await _apiClient.post(
        FaceCroppingConstants.faceCropSyncEndpoint,
        body: requestData,
      ).timeout(FaceCroppingConstants.synchronousTimeout);
      
      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('API call failed: ${response['error'] ?? 'Unknown error'}');
      }
      
    } catch (e) {
      debugPrint('$_logTag ❌ Synchronous API call failed: $e');
      rethrow;
    }
  }
  
  // ============================================================================
  // METHOD 2: ASYNCHRONOUS API APPROACH
  // ============================================================================
  
  /// Method 2: Asynchronous face cropping API call with callback mechanism
  /// 
  /// [imagePath] - Path to the source image
  /// [face] - Detected face object from ML Kit
  /// [onProgress] - Progress callback function
  /// [onComplete] - Completion callback function
  /// [onError] - Error callback function
  /// [context] - Additional context data for processing
  /// [enabledSideEffects] - List of side effects to execute
  /// [cropPadding] - Padding around face region (default 0.2)
  /// [outputQuality] - JPEG quality for cropped image (default 85)
  /// 
  /// Returns: Async operation ID for tracking
  Future<String> processFaceCroppingAsynchronous({
    required String imagePath,
    required Face face,
    required Function(double progress, String status) onProgress,
    required Function(FaceCroppingAsyncResult result) onComplete,
    required Function(String error) onError,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
    double cropPadding = FaceCroppingConstants.defaultPadding,
    int outputQuality = FaceCroppingConstants.defaultOutputQuality,
  }) async {
    final operationId = _generateOperationId();
    
    debugPrint('$_logTag Starting asynchronous face cropping process: $operationId');
    
    // Start async processing without waiting
    _processAsynchronously(
      operationId: operationId,
      imagePath: imagePath,
      face: face,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
      context: context ?? {},
      enabledSideEffects: enabledSideEffects,
      cropPadding: cropPadding,
      outputQuality: outputQuality,
    );
    
    return operationId;
  }
  
  /// Internal async processing method
  Future<void> _processAsynchronously({
    required String operationId,
    required String imagePath,
    required Face face,
    required Function(double progress, String status) onProgress,
    required Function(FaceCroppingAsyncResult result) onComplete,
    required Function(String error) onError,
    required Map<String, dynamic> context,
    List<SideEffectType>? enabledSideEffects,
    required double cropPadding,
    required int outputQuality,
  }) async {
    final startTime = DateTime.now();
    
    try {
      // Step 1: Crop face (20% progress)
      onProgress(0.2, 'Cropping face from image...');
      
      final croppedImagePath = await FaceCroppingService.cropFaceFromImage(
        imagePath: imagePath,
        face: face,
        padding: cropPadding,
        outputQuality: outputQuality,
      );
      
      if (croppedImagePath == null) {
        throw Exception('Failed to crop face from image');
      }
      
      // Step 2: Prepare API request (40% progress)
      onProgress(0.4, 'Preparing API request...');
      
      final requestData = await _prepareApiRequestData(
        croppedImagePath: croppedImagePath,
        originalImagePath: imagePath,
        face: face,
        context: context,
      );
      
      // Step 3: Make API call (70% progress)
      onProgress(0.7, 'Sending data to server...');
      
      final apiResponse = await _makeAsynchronousApiCall(requestData);
      
      // Step 4: Execute side effects (90% progress)
      onProgress(0.9, 'Executing side effects...');
      
      final sideEffectsResults = await FaceCroppingSideEffectsHandler.executeSideEffects(
        response: apiResponse,
        enabledSideEffects: enabledSideEffects,
        context: context,
      );
      
      // Step 5: Complete (100% progress)
      onProgress(1.0, 'Processing completed');
      
      await _cleanupTempFiles([croppedImagePath]);
      
      final totalDuration = DateTime.now().difference(startTime);
      
      debugPrint('$_logTag ✅ Asynchronous processing completed: $operationId');
      
      onComplete(FaceCroppingAsyncResult(
        success: true,
        operationId: operationId,
        apiResponse: apiResponse,
        sideEffectsResults: sideEffectsResults,
        processingTime: totalDuration,
        croppedImagePath: croppedImagePath,
        originalImagePath: imagePath,
      ));
      
    } catch (e, stackTrace) {
      final totalDuration = DateTime.now().difference(startTime);
      
      debugPrint('$_logTag ❌ Asynchronous processing failed: $operationId - $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      
      onError('Processing failed: $e');
      
      onComplete(FaceCroppingAsyncResult(
        success: false,
        operationId: operationId,
        error: e.toString(),
        processingTime: totalDuration,
        originalImagePath: imagePath,
      ));
    }
  }
  
  /// Make asynchronous API call to face cropping endpoint
  Future<Map<String, dynamic>> _makeAsynchronousApiCall(
    Map<String, dynamic> requestData,
  ) async {
    try {
      debugPrint('$_logTag Making asynchronous API call');

      final response = await _apiClient.post(
        FaceCroppingConstants.faceCropAsyncEndpoint,
        body: requestData,
      ).timeout(FaceCroppingConstants.asynchronousTimeout);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('API call failed: ${response['error'] ?? 'Unknown error'}');
      }

    } catch (e) {
      debugPrint('$_logTag ❌ Asynchronous API call failed: $e');
      rethrow;
    }
  }

  // ============================================================================
  // METHOD 3: QUEUE-BASED API APPROACH
  // ============================================================================

  static final List<FaceCroppingQueueItem> _processingQueue = [];
  static bool _isQueueProcessing = false;

  /// Method 3: Queue-based face cropping for batch processing
  ///
  /// [imagePath] - Path to the source image
  /// [face] - Detected face object from ML Kit
  /// [priority] - Processing priority (higher = processed first)
  /// [context] - Additional context data for processing
  /// [enabledSideEffects] - List of side effects to execute
  /// [cropPadding] - Padding around face region (default 0.2)
  /// [outputQuality] - JPEG quality for cropped image (default 85)
  ///
  /// Returns: Queue item ID for tracking
  Future<String> addToFaceCroppingQueue({
    required String imagePath,
    required Face face,
    int priority = 0,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
    double cropPadding = FaceCroppingConstants.defaultPadding,
    int outputQuality = FaceCroppingConstants.defaultOutputQuality,
  }) async {
    if (_processingQueue.length >= FaceCroppingConstants.maxQueueSize) {
      throw Exception(FaceCroppingConstants.errorQueueFull);
    }

    final queueItem = FaceCroppingQueueItem(
      id: _generateOperationId(),
      imagePath: imagePath,
      face: face,
      priority: priority,
      context: context ?? {},
      enabledSideEffects: enabledSideEffects,
      cropPadding: cropPadding,
      outputQuality: outputQuality,
      addedAt: DateTime.now(),
      status: QueueItemStatus.pending,
    );

    _processingQueue.add(queueItem);

    // Sort queue by priority (higher priority first)
    _processingQueue.sort((a, b) => b.priority.compareTo(a.priority));

    debugPrint('$_logTag Added item to queue: ${queueItem.id} (Priority: $priority)');
    debugPrint('$_logTag Queue size: ${_processingQueue.length}');

    // Start queue processing if not already running
    if (!_isQueueProcessing) {
      _startQueueProcessing();
    }

    return queueItem.id;
  }

  /// Get queue status and item information
  Map<String, dynamic> getQueueStatus() {
    final pendingItems = _processingQueue.where((item) => item.status == QueueItemStatus.pending).length;
    final processingItems = _processingQueue.where((item) => item.status == QueueItemStatus.processing).length;
    final completedItems = _processingQueue.where((item) => item.status == QueueItemStatus.completed).length;
    final failedItems = _processingQueue.where((item) => item.status == QueueItemStatus.failed).length;

    return {
      'total_items': _processingQueue.length,
      'pending': pendingItems,
      'processing': processingItems,
      'completed': completedItems,
      'failed': failedItems,
      'is_processing': _isQueueProcessing,
      'queue_items': _processingQueue.map((item) => item.toJson()).toList(),
    };
  }

  /// Start queue processing
  Future<void> _startQueueProcessing() async {
    if (_isQueueProcessing) return;

    _isQueueProcessing = true;
    debugPrint('$_logTag Starting queue processing...');

    while (_processingQueue.isNotEmpty && _isQueueProcessing) {
      // Get batch of items to process
      final batch = _processingQueue
          .where((item) => item.status == QueueItemStatus.pending)
          .take(FaceCroppingConstants.queueBatchSize)
          .toList();

      if (batch.isEmpty) {
        // No pending items, wait and check again
        await Future.delayed(FaceCroppingConstants.queueProcessingInterval);
        continue;
      }

      debugPrint('$_logTag Processing batch of ${batch.length} items');

      // Process batch concurrently
      final futures = batch.map((item) => _processQueueItem(item)).toList();
      await Future.wait(futures);

      // Clean up completed/failed items older than max wait time
      _cleanupOldQueueItems();

      // Wait before processing next batch
      await Future.delayed(FaceCroppingConstants.queueProcessingInterval);
    }

    _isQueueProcessing = false;
    debugPrint('$_logTag Queue processing stopped');
  }

  /// Process a single queue item
  Future<void> _processQueueItem(FaceCroppingQueueItem item) async {
    try {
      item.status = QueueItemStatus.processing;
      item.startedAt = DateTime.now();

      debugPrint('$_logTag Processing queue item: ${item.id}');

      // Crop face
      final croppedImagePath = await FaceCroppingService.cropFaceFromImage(
        imagePath: item.imagePath,
        face: item.face,
        padding: item.cropPadding,
        outputQuality: item.outputQuality,
      );

      if (croppedImagePath == null) {
        throw Exception('Failed to crop face from image');
      }

      // Prepare API request
      final requestData = await _prepareApiRequestData(
        croppedImagePath: croppedImagePath,
        originalImagePath: item.imagePath,
        face: item.face,
        context: item.context,
      );

      // Make API call
      final apiResponse = await _makeQueueBasedApiCall(requestData);

      // Execute side effects
      final sideEffectsResults = await FaceCroppingSideEffectsHandler.executeSideEffects(
        response: apiResponse,
        enabledSideEffects: item.enabledSideEffects,
        context: item.context,
      );

      // Update item with results
      item.status = QueueItemStatus.completed;
      item.completedAt = DateTime.now();
      item.result = FaceCroppingQueueResult(
        success: true,
        apiResponse: apiResponse,
        sideEffectsResults: sideEffectsResults,
        croppedImagePath: croppedImagePath,
      );

      // Clean up temp files
      await _cleanupTempFiles([croppedImagePath]);

      debugPrint('$_logTag ✅ Queue item completed: ${item.id}');

    } catch (e, stackTrace) {
      item.status = QueueItemStatus.failed;
      item.completedAt = DateTime.now();
      item.error = e.toString();

      debugPrint('$_logTag ❌ Queue item failed: ${item.id} - $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
    }
  }

  /// Make queue-based API call
  Future<Map<String, dynamic>> _makeQueueBasedApiCall(
    Map<String, dynamic> requestData,
  ) async {
    try {
      debugPrint('$_logTag Making queue-based API call');

      final response = await _apiClient.post(
        FaceCroppingConstants.faceCropQueueEndpoint,
        body: requestData,
      ).timeout(FaceCroppingConstants.queueBasedTimeout);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('API call failed: ${response['error'] ?? 'Unknown error'}');
      }

    } catch (e) {
      debugPrint('$_logTag ❌ Queue-based API call failed: $e');
      rethrow;
    }
  }

  /// Clean up old queue items
  void _cleanupOldQueueItems() {
    final cutoffTime = DateTime.now().subtract(FaceCroppingConstants.maxQueueWaitTime);

    _processingQueue.removeWhere((item) {
      final shouldRemove = (item.status == QueueItemStatus.completed ||
                           item.status == QueueItemStatus.failed) &&
                          item.addedAt.isBefore(cutoffTime);

      if (shouldRemove) {
        debugPrint('$_logTag 🧹 Cleaned up old queue item: ${item.id}');
      }

      return shouldRemove;
    });
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Prepare API request data with cropped image and metadata
  Future<Map<String, dynamic>> _prepareApiRequestData({
    required String croppedImagePath,
    required String originalImagePath,
    required Face face,
    required Map<String, dynamic> context,
  }) async {
    try {
      // Read cropped image as base64
      final croppedImageBytes = await File(croppedImagePath).readAsBytes();
      final croppedImageBase64 = base64Encode(croppedImageBytes);

      // Prepare face metadata
      final faceMetadata = {
        'bounding_box': {
          'left': face.boundingBox.left,
          'top': face.boundingBox.top,
          'right': face.boundingBox.right,
          'bottom': face.boundingBox.bottom,
          'width': face.boundingBox.width,
          'height': face.boundingBox.height,
        },
        'tracking_id': face.trackingId,
        'head_euler_angle_y': face.headEulerAngleY,
        'head_euler_angle_z': face.headEulerAngleZ,
        'left_eye_open_probability': face.leftEyeOpenProbability,
        'right_eye_open_probability': face.rightEyeOpenProbability,
        'smiling_probability': face.smilingProbability,
      };

      return {
        'cropped_image': croppedImageBase64,
        'original_image_path': originalImagePath,
        'cropped_image_path': croppedImagePath,
        'face_metadata': faceMetadata,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      debugPrint('$_logTag ❌ Error preparing API request data: $e');
      rethrow;
    }
  }

  /// Generate unique operation ID
  String _generateOperationId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'face_crop_${timestamp}_$random';
  }

  /// Clean up temporary files
  Future<void> _cleanupTempFiles(List<String> filePaths) async {
    for (final filePath in filePaths) {
      try {
        final file = File(filePath);
        if (file.existsSync()) {
          await file.delete();
          debugPrint('$_logTag 🧹 Cleaned up temp file: $filePath');
        }
      } catch (e) {
        debugPrint('$_logTag ⚠️ Failed to cleanup temp file: $filePath - $e');
      }
    }
  }
}

// ============================================================================
// RESULT CLASSES
// ============================================================================

/// Result class for synchronous face cropping operations
class FaceCroppingSyncResult {
  final bool success;
  final Map<String, dynamic>? apiResponse;
  final Map<SideEffectType, SideEffectResult>? sideEffectsResults;
  final String? error;
  final Duration processingTime;
  final String? croppedImagePath;
  final String originalImagePath;

  FaceCroppingSyncResult({
    required this.success,
    this.apiResponse,
    this.sideEffectsResults,
    this.error,
    required this.processingTime,
    this.croppedImagePath,
    required this.originalImagePath,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'api_response': apiResponse,
      'side_effects_results': sideEffectsResults?.map((key, value) => MapEntry(key.name, value.toJson())),
      'error': error,
      'processing_time_ms': processingTime.inMilliseconds,
      'cropped_image_path': croppedImagePath,
      'original_image_path': originalImagePath,
    };
  }
}

/// Result class for asynchronous face cropping operations
class FaceCroppingAsyncResult {
  final bool success;
  final String operationId;
  final Map<String, dynamic>? apiResponse;
  final Map<SideEffectType, SideEffectResult>? sideEffectsResults;
  final String? error;
  final Duration processingTime;
  final String? croppedImagePath;
  final String originalImagePath;

  FaceCroppingAsyncResult({
    required this.success,
    required this.operationId,
    this.apiResponse,
    this.sideEffectsResults,
    this.error,
    required this.processingTime,
    this.croppedImagePath,
    required this.originalImagePath,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'operation_id': operationId,
      'api_response': apiResponse,
      'side_effects_results': sideEffectsResults?.map((key, value) => MapEntry(key.name, value.toJson())),
      'error': error,
      'processing_time_ms': processingTime.inMilliseconds,
      'cropped_image_path': croppedImagePath,
      'original_image_path': originalImagePath,
    };
  }
}

/// Queue item status enumeration
enum QueueItemStatus {
  pending,
  processing,
  completed,
  failed,
}

/// Queue item for batch processing
class FaceCroppingQueueItem {
  final String id;
  final String imagePath;
  final Face face;
  final int priority;
  final Map<String, dynamic> context;
  final List<SideEffectType>? enabledSideEffects;
  final double cropPadding;
  final int outputQuality;
  final DateTime addedAt;

  QueueItemStatus status;
  DateTime? startedAt;
  DateTime? completedAt;
  String? error;
  FaceCroppingQueueResult? result;

  FaceCroppingQueueItem({
    required this.id,
    required this.imagePath,
    required this.face,
    required this.priority,
    required this.context,
    this.enabledSideEffects,
    required this.cropPadding,
    required this.outputQuality,
    required this.addedAt,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image_path': imagePath,
      'priority': priority,
      'context': context,
      'enabled_side_effects': enabledSideEffects?.map((e) => e.name).toList(),
      'crop_padding': cropPadding,
      'output_quality': outputQuality,
      'added_at': addedAt.toIso8601String(),
      'status': status.name,
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'error': error,
      'result': result?.toJson(),
    };
  }
}

/// Result for queue-based processing
class FaceCroppingQueueResult {
  final bool success;
  final Map<String, dynamic>? apiResponse;
  final Map<SideEffectType, SideEffectResult>? sideEffectsResults;
  final String? croppedImagePath;
  final String? error;

  FaceCroppingQueueResult({
    required this.success,
    this.apiResponse,
    this.sideEffectsResults,
    this.croppedImagePath,
    this.error,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'api_response': apiResponse,
      'side_effects_results': sideEffectsResults?.map((key, value) => MapEntry(key.name, value.toJson())),
      'cropped_image_path': croppedImagePath,
      'error': error,
    };
  }
}

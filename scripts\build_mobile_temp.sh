#!/bin/bash

echo "🔨 Building C-Face Mobile App (TEMP VERSION)..."
echo "==============================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean
flutter pub get

# Build APK
echo "🔨 Building APK (TEMP VERSION)..."
echo "📱 Target: lib/apps/mobile/main_mobile.dart"
echo "📦 Package: com.ccam.mobile.temp"
echo "🏗️ Mode: Debug"
echo ""

flutter build apk --target lib/apps/mobile/main_mobile.dart --flavor mobile --debug

# Check build result
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build successful!"
    echo "📱 APK location: build/app/outputs/flutter-apk/app-mobile-debug.apk"
    echo "📦 Package name: com.ccam.mobile.temp.debug"
    echo ""
    echo "💡 To install on device:"
    echo "   adb install build/app/outputs/flutter-apk/app-mobile-debug.apk"
else
    echo ""
    echo "❌ Build failed!"
    echo "💡 Try these troubleshooting steps:"
    echo "   1. Check Flutter setup: flutter doctor"
    echo "   2. Clean and rebuild: flutter clean && flutter pub get"
    echo "   3. Check Android SDK setup"
fi

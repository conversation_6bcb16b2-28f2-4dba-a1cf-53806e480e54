import 'package:flutter/material.dart';
import '../../../core/errors/failures.dart';

enum ErrorSeverity { info, warning, error, critical }

class EnhancedErrorMessage extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final ErrorSeverity severity;
  final Failure? failure;
  final bool showRetryButton;
  final VoidCallback? onRetry;
  final bool showDetails;
  final Duration? autoHideDuration;

  const EnhancedErrorMessage({
    super.key,
    required this.message,
    this.onDismiss,
    this.padding,
    this.margin,
    this.severity = ErrorSeverity.error,
    this.failure,
    this.showRetryButton = false,
    this.onRetry,
    this.showDetails = false,
    this.autoHideDuration,
  });

  /// Factory constructor for authentication errors
  factory EnhancedErrorMessage.auth({
    required String message,
    VoidCallback? onDismiss,
    VoidCallback? onRetry,
    Failure? failure,
  }) {
    return EnhancedErrorMessage(
      message: message,
      onDismiss: onDismiss,
      onRetry: onRetry,
      failure: failure,
      severity: ErrorSeverity.error,
      showRetryButton: true,
      showDetails: true,
    );
  }

  /// Factory constructor for network errors
  factory EnhancedErrorMessage.network({
    required String message,
    VoidCallback? onDismiss,
    VoidCallback? onRetry,
    Failure? failure,
  }) {
    return EnhancedErrorMessage(
      message: message,
      onDismiss: onDismiss,
      onRetry: onRetry,
      failure: failure,
      severity: ErrorSeverity.warning,
      showRetryButton: true,
      showDetails: false,
    );
  }

  /// Factory constructor for validation errors
  factory EnhancedErrorMessage.validation({
    required String message,
    VoidCallback? onDismiss,
    Failure? failure,
  }) {
    return EnhancedErrorMessage(
      message: message,
      onDismiss: onDismiss,
      failure: failure,
      severity: ErrorSeverity.info,
      showRetryButton: false,
      showDetails: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colors = _getColorsForSeverity();
    
    return Container(
      padding: padding ?? const EdgeInsets.all(12),
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: colors.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colors.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getIconForSeverity(),
                color: colors.iconColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: colors.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onDismiss,
                  iconSize: 18,
                  color: colors.iconColor,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
            ],
          ),
          if (showDetails && failure != null) ...[
            const SizedBox(height: 8),
            _buildErrorDetails(),
          ],
          if (showRetryButton && onRetry != null) ...[
            const SizedBox(height: 12),
            _buildRetryButton(colors),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorDetails() {
    if (failure == null) return const SizedBox.shrink();

    final details = <String>[];
    
    if (failure!.code != null) {
      details.add('Mã lỗi: ${failure!.code}');
    }
    
    if (failure is ValidationFailure) {
      final validationFailure = failure as ValidationFailure;
      if (validationFailure.fieldErrors != null) {
        for (final entry in validationFailure.fieldErrors!.entries) {
          details.add('${entry.key}: ${entry.value.join(', ')}');
        }
      }
    }
    
    if (failure is ServerFailure) {
      final serverFailure = failure as ServerFailure;
      if (serverFailure.statusCode != null) {
        details.add('HTTP Status: ${serverFailure.statusCode}');
      }
    }

    if (details.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: details.map((detail) => Text(
          detail,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildRetryButton(_ErrorColors colors) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onRetry,
        icon: const Icon(Icons.refresh, size: 16),
        label: const Text('Thử lại'),
        style: OutlinedButton.styleFrom(
          foregroundColor: colors.iconColor,
          side: BorderSide(color: colors.borderColor),
          padding: const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
    );
  }

  IconData _getIconForSeverity() {
    switch (severity) {
      case ErrorSeverity.info:
        return Icons.info_outline;
      case ErrorSeverity.warning:
        return Icons.warning_amber_outlined;
      case ErrorSeverity.error:
        return Icons.error_outline;
      case ErrorSeverity.critical:
        return Icons.dangerous_outlined;
    }
  }

  _ErrorColors _getColorsForSeverity() {
    switch (severity) {
      case ErrorSeverity.info:
        return _ErrorColors(
          backgroundColor: Colors.blue.shade50,
          borderColor: Colors.blue.shade200,
          textColor: Colors.blue.shade700,
          iconColor: Colors.blue.shade600,
        );
      case ErrorSeverity.warning:
        return _ErrorColors(
          backgroundColor: Colors.orange.shade50,
          borderColor: Colors.orange.shade200,
          textColor: Colors.orange.shade700,
          iconColor: Colors.orange.shade600,
        );
      case ErrorSeverity.error:
        return _ErrorColors(
          backgroundColor: Colors.red.shade50,
          borderColor: Colors.red.shade200,
          textColor: Colors.red.shade700,
          iconColor: Colors.red.shade600,
        );
      case ErrorSeverity.critical:
        return _ErrorColors(
          backgroundColor: Colors.red.shade100,
          borderColor: Colors.red.shade400,
          textColor: Colors.red.shade800,
          iconColor: Colors.red.shade700,
        );
    }
  }
}

class _ErrorColors {
  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;
  final Color iconColor;

  const _ErrorColors({
    required this.backgroundColor,
    required this.borderColor,
    required this.textColor,
    required this.iconColor,
  });
}

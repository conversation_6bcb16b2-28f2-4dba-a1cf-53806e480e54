library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../configuration_manager.dart';
import '../flexible_config_system.dart';
import '../../../providers/face_detection_provider.dart';
import '../../../models/camera_config.dart';

/// Service to sync configuration changes with app components
class ConfigComponentSyncService {
  static ConfigComponentSyncService? _instance;
  static ConfigComponentSyncService get instance => _instance ??= ConfigComponentSyncService._();
  
  ConfigComponentSyncService._();
  
  StreamSubscription<ConfigChangeEvent>? _configSubscription;
  FaceDetectionProvider? _faceDetectionProvider;
  bool _isInitialized = false;
  
  /// Initialize the sync service
  Future<void> initialize({
    FaceDetectionProvider? faceDetectionProvider,
  }) async {
    if (_isInitialized) return;
    
    _faceDetectionProvider = faceDetectionProvider;
    
    // Listen to configuration changes
    final manager = ConfigurationManager.instance;
    _configSubscription = manager.changeStream.listen(_handleConfigChange);
    
    // Apply initial configuration
    await _applyInitialConfiguration();
    
    _isInitialized = true;
    debugPrint('ConfigComponentSyncService initialized');
  }
  
  /// Register face detection provider
  void registerFaceDetectionProvider(FaceDetectionProvider provider) {
    _faceDetectionProvider = provider;
    
    // Apply current configuration to the provider
    if (_isInitialized) {
      _syncFaceDetectionConfig();
    }
  }
  
  /// Handle configuration changes
  void _handleConfigChange(ConfigChangeEvent event) {
    final friendlyMessage = _getFriendlyConfigMessage(event.key, event.newValue);
    debugPrint('⚙️ Cấu hình đã thay đổi: $friendlyMessage');

    // Sync face detection related changes
    if (_isFaceDetectionConfig(event.key)) {
      _syncFaceDetectionConfig();
    }

    // Add other component syncing here as needed
  }
  
  /// Check if config key is related to face detection
  bool _isFaceDetectionConfig(String key) {
    return key.startsWith('face_detection.') ||
           key == ConfigKeys.minFaceQualityForDetection ||
           key == ConfigKeys.minFaceQualityForRecognition ||
           key == ConfigKeys.minFaceQualityForAvatarCapture ||
           key == ConfigKeys.frameSkipCount ||
           key == ConfigKeys.detectionTimeout;
  }
  
  /// Apply initial configuration to all components
  Future<void> _applyInitialConfiguration() async {
    _syncFaceDetectionConfig();
  }
  
  /// Sync face detection configuration
  void _syncFaceDetectionConfig() {
    if (_faceDetectionProvider == null) return;

    try {
      final manager = ConfigurationManager.instance;

      // Get current config as base
      final currentConfig = _faceDetectionProvider!.config;

      // Build FaceDetectionConfig with updated values from configuration
      final config = FaceDetectionConfig(
        frameSkip: manager.getValue(
          ConfigKeys.frameSkipCount,
          defaultValue: currentConfig.frameSkip,
        ),
        minDetectionInterval: manager.getValue(
          ConfigKeys.detectionTimeout,
          defaultValue: currentConfig.minDetectionInterval,
        ),
        minFaceSize: currentConfig.minFaceSize, // Use current value
        minFaceArea: currentConfig.minFaceArea, // Use current value
        idealFaceArea: currentConfig.idealFaceArea, // Use current value
        qualityThreshold: manager.getValue(
          ConfigKeys.minFaceQualityForDetection,
          defaultValue: currentConfig.qualityThreshold,
        ),
        maxYawAngle: currentConfig.maxYawAngle, // Use current value
        maxRollAngle: currentConfig.maxRollAngle, // Use current value
        centerAngleTolerance: currentConfig.centerAngleTolerance, // Use current value
        directionAngleTolerance: currentConfig.directionAngleTolerance, // Use current value
      );

      // Update face detection provider
      _faceDetectionProvider!.updateConfiguration(config);

      debugPrint('🎯 Cấu hình nhận diện khuôn mặt đã được cập nhật: Ngưỡng chất lượng = ${(config.qualityThreshold * 100).toStringAsFixed(1)}%');
    } catch (e) {
      debugPrint('❌ Lỗi khi đồng bộ cấu hình nhận diện khuôn mặt: $e');
    }
  }

  /// Get friendly message for configuration changes
  String _getFriendlyConfigMessage(String key, dynamic value) {
    switch (key) {
      case 'face_detection.min_quality_detection':
        return 'Ngưỡng chất lượng tối thiểu cho phát hiện khuôn mặt = ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.min_quality_recognition':
        return 'Ngưỡng chất lượng tối thiểu cho nhận diện khuôn mặt = ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.min_quality_avatar_capture':
        return 'Ngưỡng chất lượng tối thiểu cho chụp avatar = ${(value * 100).toStringAsFixed(1)}%';
      case 'face_detection.frame_skip_count':
        return 'Số khung hình bỏ qua = $value';
      case 'face_detection.detection_timeout':
        if (value is Duration) {
          return 'Thời gian chờ phát hiện = ${value.inMilliseconds}ms';
        }
        return 'Thời gian chờ phát hiện = ${value}ms';
      case 'face_detection.recognition_throttle_duration':
        if (value is Duration) {
          return 'Thời gian giãn cách nhận diện = ${value.inSeconds}s';
        }
        return 'Thời gian giãn cách nhận diện = ${value}s';
      case 'face_detection.significant_quality_change':
        return 'Ngưỡng thay đổi chất lượng đáng kể = ${(value * 100).toStringAsFixed(1)}%';
      default:
        // For other keys, try to make them more readable
        final friendlyKey = key.replaceAll('_', ' ').replaceAll('.', ' → ');
        return '$friendlyKey = $value';
    }
  }
  
  /// Dispose the service
  Future<void> dispose() async {
    await _configSubscription?.cancel();
    _configSubscription = null;
    _faceDetectionProvider = null;
    _isInitialized = false;
    debugPrint('ConfigComponentSyncService disposed');
  }
}

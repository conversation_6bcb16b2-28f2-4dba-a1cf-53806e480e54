# Create User API Format

## Correct Request Body Format

```json
{
    "current_unit_id": "6853aea44df145f54f08ee08",
    "name": "Quy<PERSON>t Đại ca 3",
    "email": "<EMAIL>",
    "phone": "0123456783",
    "dob": "2025-06-09",
    "gender": "male",
    "username": "<EMAIL>",
    "password": "<EMAIL>",
    "roleId": "684fbc17dd841b7aeca925fa"
}
```

## Key Changes Made

1. **Removed fields:**
   - `current_tenant_id` - Not needed in request body
   - `code` - Auto-generated by backend

2. **Field name corrections:**
   - `unit_id` → `current_unit_id`
   - `member_role_id` → `roleId`

3. **Required fields:**
   - `current_unit_id` - Unit where user is assigned
   - `name` - User's full name
   - `email` - User's email address
   - `phone` - User's phone number
   - `username` - Login username
   - `password` - Login password
   - `roleId` - Role ID for the user

4. **Optional fields:**
   - `dob` - Date of birth (YYYY-MM-DD format)
   - `gender` - Must be one of: "male", "female", "other"

## Update User API Format

For update operations, the same field names apply but without `username` and `password`:

```json
{
    "current_unit_id": "68662aba45564c8dd61148e3",
    "name": "traicv2",
    "email": "<EMAIL>",
    "phone": "03232323232",
    "dob": "2025-07-05",
    "gender": "male",
    "roleId": "68662aba45564c8dd61148ed"
}
```

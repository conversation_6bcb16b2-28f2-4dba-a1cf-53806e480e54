import 'package:dartz/dartz.dart';
import '../../domain/entities/user/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../data_sources/remote/user_remote_data_source.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource remoteDataSource;

  UserRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<User>>> getUsers({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? unitId,
    String? memberRoleId,
  }) async {
    try {
      final userModels = await remoteDataSource.getUsers(
        page: page,
        limit: limit,
        sortBy: sortBy,
        sortDirection: sortDirection,
        search: search,
        unitId: unitId,
        memberRoleId: memberRoleId,
      );

      final users = userModels.map((model) => model.toEntity()).toList();
      return Right(users);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get users: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> getUserById(String userId) async {
    try {
      final userModel = await remoteDataSource.getUserById(userId);
      return Right(userModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get user: $e'));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getUsersByUnit(String unitId) async {
    try {
      final userModels = await remoteDataSource.getUsersByUnit(unitId);
      final users = userModels.map((model) => model.toEntity()).toList();
      return Right(users);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get users by unit: $e'));
    }
  }

  @override
  Future<Either<Failure, List<User>>> getUsersByRole(String memberRoleId) async {
    try {
      final userModels = await remoteDataSource.getUsersByRole(memberRoleId);
      final users = userModels.map((model) => model.toEntity()).toList();
      return Right(users);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to get users by role: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> createUser({
    required String username,
    required String password,
    required String name,
    required String unitId,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? memberRoleId,
  }) async {
    try {
      final userModel = await remoteDataSource.createUser(
        username: username,
        password: password,
        name: name,
        unitId: unitId,
        email: email,
        phone: phone,
        dob: dob,
        gender: gender,
        memberRoleId: memberRoleId,
      );

      return Right(userModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to create user: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> updateUser({
    required String userId,
    String? name,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? unitId,
    String? memberRoleId,
  }) async {
    try {
      final userModel = await remoteDataSource.updateUser(
        userId: userId,
        name: name,
        email: email,
        phone: phone,
        dob: dob,
        gender: gender,
        unitId: unitId,
        memberRoleId: memberRoleId,
      );

      return Right(userModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to update user: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUser(String userId) async {
    try {
      await remoteDataSource.deleteUser(userId);
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to delete user: $e'));
    }
  }

  @override
  Future<Either<Failure, User>> uploadAvatar({
    required String userId,
    required String fileName,
    required String fileData,
  }) async {
    try {
      final userModel = await remoteDataSource.uploadAvatar(
        userId: userId,
        fileName: fileName,
        fileData: fileData,
      );

      return Right(userModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Failed to upload avatar: $e'));
    }
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'mobile_route_names.dart';

/// Extension methods for easier navigation in mobile app
/// 
/// Migrated from c-faces project and adapted for mobile app architecture
/// Provides convenient navigation methods with type safety
extension MobileNavigationExtension on BuildContext {
  // ============================================================================
  // BASIC NAVIGATION METHODS
  // ============================================================================
  
  /// Navigate to login screen
  void goToLogin() => go(MobileRouteNames.login);
  
  /// Navigate to home/dashboard
  void goToHome() => go(MobileRouteNames.dashboard);
  void goToDashboard() => go(MobileRouteNames.dashboard);
  void goToMain() => go(MobileRouteNames.main);
  
  /// Navigate to splash screen
  void goToSplash() => go(MobileRouteNames.splash);
  
  // ============================================================================
  // AUTHENTICATION NAVIGATION
  // ============================================================================
  
  /// Navigate to forgot password flow
  void goToForgotPassword() => go(MobileRouteNames.forgotPassword);
  void goToEnterOtp() => go(MobileRouteNames.enterOtp);
  void goToConfirmPassword() => go(MobileRouteNames.confirmPassword);
  void goToSuccessfully() => go(MobileRouteNames.successfully);
  
  // ============================================================================
  // TENANT MANAGEMENT NAVIGATION
  // ============================================================================
  
  /// Navigate to tenants screen
  void goToTenants() => go(MobileRouteNames.tenants);
  
  /// Navigate to tenant creation screen
  void goToTenantCreate() => go(MobileRouteNames.tenantCreate);
  
  // ============================================================================
  // USER MANAGEMENT NAVIGATION
  // ============================================================================
  
  /// Navigate to users screen
  void goToUsers() => go(MobileRouteNames.users);
  
  /// Navigate to user detail screen
  void goToUserDetail() => go(MobileRouteNames.userDetail);
  
  /// Navigate to user face registration screen
  void goToUserFaceRegister() => go(MobileRouteNames.userFaceRegister);
  
  /// Navigate to user item detail screen
  void goToUserItemDetail() => go(MobileRouteNames.userItemDetail);
  
  // ============================================================================
  // TOOLS NAVIGATION
  // ============================================================================
  
  /// Navigate to tools screen
  void goToTools() => go(MobileRouteNames.tools);
  
  /// Navigate to access control screen
  void goToAccessControl() => go(MobileRouteNames.accessControl);
  
  /// Navigate to attendance screen
  void goToAttendance() => go(MobileRouteNames.attendance);
  
  /// Navigate to security monitoring screen
  void goToSecurityMonitoring() => go(MobileRouteNames.securityMonitoring);
  
  /// Navigate to system management screen
  void goToSystemManagement() => go(MobileRouteNames.systemManagement);
  
  // ============================================================================
  // PROFILE & SETTINGS NAVIGATION
  // ============================================================================
  
  /// Navigate to profile screen
  void goToProfile() => go(MobileRouteNames.profile);
  
  /// Navigate to profile edit screen
  void goToProfileEdit() => go(MobileRouteNames.profileEdit);
  
  /// Navigate to settings screen
  void goToSettings() => go(MobileRouteNames.settings);
  
  /// Navigate to notifications screen
  void goToNotifications() => go(MobileRouteNames.notifications);
  
  // ============================================================================
  // FACE CAPTURE NAVIGATION
  // ============================================================================
  
  /// Navigate to face capture screen
  void goToFaceCapture() => go(MobileRouteNames.faceCapture);
  
  /// Navigate to face guide screen
  void goToFaceGuide() => go(MobileRouteNames.faceGuide);
  
  /// Navigate to face result screen
  void goToFaceResult() => go(MobileRouteNames.faceResult);
  
  /// Navigate to face validation screen
  void goToFaceValidation() => go(MobileRouteNames.faceValidation);
  
  // ============================================================================
  // ERROR NAVIGATION
  // ============================================================================
  
  /// Navigate to error screen with message
  void goToError(String message) => go(MobileRouteNames.errorWithMessage(message));
  
  /// Navigate to not found screen
  void goToNotFound() => go(MobileRouteNames.notFound);
  
  // ============================================================================
  // NAVIGATION WITH PARAMETERS
  // ============================================================================
  
  /// Navigate to profile with user ID
  void goToProfileWithId(String userId) => go(MobileRouteNames.profileWithId(userId));
  
  /// Navigate to user detail with user ID
  void goToUserDetailWithId(String userId) => go(MobileRouteNames.userDetailWithId(userId));
  
  /// Navigate to tenant detail with tenant ID
  void goToTenantDetailWithId(String tenantId) => go(MobileRouteNames.tenantDetailWithId(tenantId));
  
  /// Navigate to face result with face ID
  void goToFaceResultWithId(String faceId) => go(MobileRouteNames.faceResultWithId(faceId));
  
  // ============================================================================
  // PUSH METHODS (for modal navigation)
  // ============================================================================
  
  /// Push login screen
  void pushLogin() => push(MobileRouteNames.login);
  
  /// Push profile screen
  void pushProfile() => push(MobileRouteNames.profile);
  
  /// Push settings screen
  void pushSettings() => push(MobileRouteNames.settings);
  
  /// Push user detail screen
  void pushUserDetail() => push(MobileRouteNames.userDetail);
  
  /// Push face capture screen
  void pushFaceCapture() => push(MobileRouteNames.faceCapture);

  // ============================================================================
  // REPLACE METHODS
  // ============================================================================
  
  /// Replace current route with login
  void replaceWithLogin() => pushReplacement(MobileRouteNames.login);
  
  /// Replace current route with home
  void replaceWithHome() => pushReplacement(MobileRouteNames.dashboard);
  
  /// Replace current route with main
  void replaceWithMain() => pushReplacement(MobileRouteNames.main);
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Check if can go back
  bool get canGoBack => canPop();
  
  /// Go back or navigate to home if can't go back
  void goBackOrHome() {
    if (canPop()) {
      pop();
    } else {
      go(MobileRouteNames.root);
    }
  }
  
  /// Go back or navigate to dashboard if can't go back
  void goBackOrDashboard() {
    if (canPop()) {
      pop();
    } else {
      go(MobileRouteNames.dashboard);
    }
  }
  
  // ============================================================================
  // CURRENT ROUTE HELPERS
  // ============================================================================
  
  /// Get current route path
  String get currentRoute => GoRouterState.of(this).uri.path;
  
  /// Check if currently on login page
  bool get isOnLoginPage => currentRoute == MobileRouteNames.login;
  
  /// Check if currently on home/dashboard page
  bool get isOnHomePage => currentRoute == MobileRouteNames.dashboard;
  bool get isOnDashboardPage => currentRoute == MobileRouteNames.dashboard;
  bool get isOnMainPage => currentRoute == MobileRouteNames.main;
  
  /// Check if currently on auth page
  bool get isOnAuthPage => MobileRouteNames.isAuthRoute(currentRoute);
  
  /// Check if currently on protected page
  bool get isOnProtectedPage => MobileRouteNames.isProtectedRoute(currentRoute);
  
  /// Check if currently on face-related page
  bool get isOnFacePage => MobileRouteNames.isFaceRoute(currentRoute);
  
  /// Check if currently on user management page
  bool get isOnUserPage => MobileRouteNames.isUserRoute(currentRoute);
  
  /// Check if currently on tenant management page
  bool get isOnTenantPage => MobileRouteNames.isTenantRoute(currentRoute);
  
  /// Check if currently on tools page
  bool get isOnToolsPage => MobileRouteNames.isToolsRoute(currentRoute);
  
  // ============================================================================
  // ROUTE INFORMATION
  // ============================================================================
  
  /// Get current route display name
  String get currentRouteDisplayName => MobileRouteNames.getDisplayName(currentRoute);
  
  /// Get route parameters
  Map<String, String> get routeParameters => GoRouterState.of(this).pathParameters;
  
  /// Get query parameters
  Map<String, String> get queryParameters => GoRouterState.of(this).uri.queryParameters;
  
  /// Get specific route parameter
  String? getRouteParameter(String key) => routeParameters[key];
  
  /// Get specific query parameter
  String? getQueryParameter(String key) => queryParameters[key];
}

import 'package:flutter/material.dart';
import '../configuration_manager.dart';
import '../config_parameters_registry.dart';
import '../flexible_config_system.dart';

/// Widget để toggle on/off toàn bộ category configuration
class ConfigCategoryToggle extends StatefulWidget {
  final String category;
  final VoidCallback? onToggle;

  const ConfigCategoryToggle({
    Key? key,
    required this.category,
    this.onToggle,
  }) : super(key: key);

  @override
  State<ConfigCategoryToggle> createState() => _ConfigCategoryToggleState();
}

class _ConfigCategoryToggleState extends State<ConfigCategoryToggle> {
  bool _isEnabled = true;
  bool _isLoading = false;
  Map<String, dynamic> _originalValues = {};

  @override
  void initState() {
    super.initState();
    _loadCategoryState();
  }

  void _loadCategoryState() async {
    setState(() => _isLoading = true);
    
    try {
      final manager = ConfigurationManager.instance;
      final toggleKey = '${widget.category}_enabled';
      
      // Check if category is enabled (default to true)
      _isEnabled = manager.getValue<bool>(toggleKey, defaultValue: true);
      
      // Load original values for restore
      final parameters = ConfigParametersRegistry.getParametersByCategory(widget.category);
      for (final parameter in parameters.values) {
        _originalValues[parameter.key] = manager.getValue(parameter.key, defaultValue: parameter.defaultValue);
      }
    } catch (e) {
      debugPrint('Error loading category state: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _toggleCategory() async {
    if (_isLoading) return;
    
    setState(() => _isLoading = true);
    
    try {
      final manager = ConfigurationManager.instance;
      final toggleKey = '${widget.category}_enabled';
      final newState = !_isEnabled;
      
      // Save toggle state
      await manager.setValue(toggleKey, newState);
      
      final parameters = ConfigParametersRegistry.getParametersByCategory(widget.category);
      
      if (newState) {
        // Enabling category - restore original values
        for (final parameter in parameters.values) {
          final originalValue = _originalValues[parameter.key] ?? parameter.defaultValue;
          await manager.setValue(parameter.key, originalValue);
        }
        
        _showSnackBar('✅ Đã bật cấu hình ${_getCategoryDisplayName(widget.category)}', Colors.green);
      } else {
        // Disabling category - set to default/disabled values
        for (final parameter in parameters.values) {
          // Store current value before disabling
          _originalValues[parameter.key] = manager.getValue(parameter.key, defaultValue: parameter.defaultValue);
          
          // Set to disabled value based on parameter type
          final disabledValue = _getDisabledValue(parameter);
          await manager.setValue(parameter.key, disabledValue);
        }
        
        _showSnackBar('⚠️ Đã tắt cấu hình ${_getCategoryDisplayName(widget.category)}', Colors.orange);
      }
      
      setState(() => _isEnabled = newState);
      widget.onToggle?.call();
      
    } catch (e) {
      _showSnackBar('❌ Lỗi khi toggle category: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  dynamic _getDisabledValue(ConfigParameter parameter) {
    switch (parameter.type) {
      case ConfigValueType.boolean:
        return false;
      case ConfigValueType.integer:
        return 0;
      case ConfigValueType.double:
        return 0.0;
      case ConfigValueType.duration:
        return Duration.zero;
      case ConfigValueType.string:
        return '';
      case ConfigValueType.color:
        return 0xFF000000; // Black
      default:
        return parameter.defaultValue;
    }
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'face_detection':
        return 'Nhận diện khuôn mặt';
      case 'network':
        return 'Mạng';
      case 'ui':
        return 'Giao diện';
      case 'performance':
        return 'Hiệu suất';
      case 'camera':
        return 'Camera';
      case 'security':
        return 'Bảo mật';
      case 'cache':
        return 'Cache';
      case 'kiosk':
        return 'Kiosk';
      case 'debug':
        return 'Debug';
      default:
        return category.toUpperCase();
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: _isEnabled ? Colors.green.shade50 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isEnabled ? Colors.green.shade300 : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isEnabled ? Icons.toggle_on : Icons.toggle_off,
            color: _isEnabled ? Colors.green : Colors.grey,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getCategoryDisplayName(widget.category),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _isEnabled ? Colors.green.shade700 : Colors.grey.shade600,
                  ),
                ),
                Text(
                  _isEnabled ? 'Đang hoạt động' : 'Đã tắt',
                  style: TextStyle(
                    fontSize: 12,
                    color: _isEnabled ? Colors.green.shade600 : Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
          if (_isLoading)
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            Switch(
              value: _isEnabled,
              onChanged: (_) => _toggleCategory(),
              activeColor: Colors.green,
            ),
        ],
      ),
    );
  }
}

/// Widget hiển thị tất cả category toggles
class ConfigCategoryToggles extends StatelessWidget {
  final VoidCallback? onToggle;

  const ConfigCategoryToggles({
    Key? key,
    this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categories = [
      'face_detection',
      'network',
      'ui',
      'performance',
      'camera',
      'security',
      'cache',
      'kiosk',
      'debug',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Bật/Tắt cấu hình theo danh mục',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...categories.map((category) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ConfigCategoryToggle(
            category: category,
            onToggle: onToggle,
          ),
        )).toList(),
        const SizedBox(height: 16),
      ],
    );
  }
} 
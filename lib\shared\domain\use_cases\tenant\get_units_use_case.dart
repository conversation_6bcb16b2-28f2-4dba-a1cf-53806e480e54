import 'package:dartz/dartz.dart';
import '../../repositories/tenant_repository.dart';
import '../../../core/errors/failures.dart';

/// Use case for getting units with pagination and filtering
class GetUnitsUseCase {
  final TenantRepository repository;

  GetUnitsUseCase(this.repository);

  /// Execute the use case to get units
  Future<Either<Failure, List<Map<String, dynamic>>>> call({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    return await repository.getUnits(
      page: page,
      limit: limit,
      sortBy: sortBy,
      sortDirection: sortDirection,
      search: search,
      tenantId: tenantId,
      parentUnitId: parentUnitId,
    );
  }

  /// Execute the use case to get units with pagination info
  Future<Either<Failure, Map<String, dynamic>>> callWithPagination({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    return await repository.getUnitsWithPagination(
      page: page,
      limit: limit,
      sortBy: sortBy,
      sortDirection: sortDirection,
      search: search,
      tenantId: tenantId,
      parentUnitId: parentUnitId,
    );
  }
}

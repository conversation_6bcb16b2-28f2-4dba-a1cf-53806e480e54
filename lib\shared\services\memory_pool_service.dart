import 'dart:typed_data';
import 'package:flutter/foundation.dart';

/// Memory pool service to reduce garbage collection and improve performance
class MemoryPoolService {
  static MemoryPoolService? _instance;
  static MemoryPoolService get instance {
    _instance ??= MemoryPoolService._();
    return _instance!;
  }
  
  MemoryPoolService._();

  // Image buffer pools for different sizes
  final Map<String, List<Uint8List>> _imageBufferPools = {};
  final Map<String, int> _poolUsageCount = {};
  
  // Pool configuration
  static const int maxPoolSize = 10;
  static const int minPoolSize = 3;
  
  // Common image sizes for camera processing
  static const Map<String, List<int>> commonImageSizes = {
    'hd': [1280, 720],      // HD
    'fhd': [1920, 1080],    // Full HD
    'vga': [640, 480],      // VGA
    'qvga': [320, 240],     // QVGA
  };

  /// Initialize memory pools
  void initialize() {
    _initializeImageBufferPools();
    
    if (kDebugMode) {
      print('🧠 MemoryPoolService initialized with ${_imageBufferPools.length} pools');
    }
  }

  /// Initialize image buffer pools for common sizes
  void _initializeImageBufferPools() {
    for (final entry in commonImageSizes.entries) {
      final key = entry.key;
      final size = entry.value;
      final bufferSize = size[0] * size[1] * 4; // RGBA
      
      _imageBufferPools[key] = [];
      _poolUsageCount[key] = 0;
      
      // Pre-allocate minimum buffers
      for (int i = 0; i < minPoolSize; i++) {
        _imageBufferPools[key]!.add(Uint8List(bufferSize));
      }
      
      if (kDebugMode) {
        print('📦 Created image buffer pool: $key (${size[0]}x${size[1]}, ${bufferSize} bytes)');
      }
    }
  }

  /// Get image buffer from pool
  Uint8List getImageBuffer(String sizeKey) {
    final pool = _imageBufferPools[sizeKey];
    if (pool == null) {
      throw ArgumentError('Unknown image size key: $sizeKey');
    }
    
    _poolUsageCount[sizeKey] = (_poolUsageCount[sizeKey] ?? 0) + 1;
    
    if (pool.isNotEmpty) {
      final buffer = pool.removeLast();
      if (kDebugMode && _poolUsageCount[sizeKey]! % 100 == 0) {
        print('📦 Pool $sizeKey usage: ${_poolUsageCount[sizeKey]}, available: ${pool.length}');
      }
      return buffer;
    } else {
      // Pool is empty, create new buffer
      final size = commonImageSizes[sizeKey]!;
      final bufferSize = size[0] * size[1] * 4;
      
      if (kDebugMode) {
        print('⚠️ Pool $sizeKey empty, creating new buffer');
      }
      
      return Uint8List(bufferSize);
    }
  }

  /// Return image buffer to pool
  void returnImageBuffer(String sizeKey, Uint8List buffer) {
    final pool = _imageBufferPools[sizeKey];
    if (pool == null) return;
    
    // Only return to pool if not at max capacity
    if (pool.length < maxPoolSize) {
      // Clear buffer data for security
      buffer.fillRange(0, buffer.length, 0);
      pool.add(buffer);
    }
    // If pool is full, let buffer be garbage collected
  }

  /// Get custom size buffer (not pooled)
  Uint8List getCustomBuffer(int width, int height, {int channels = 4}) {
    final bufferSize = width * height * channels;
    return Uint8List(bufferSize);
  }

  /// Get pool statistics
  Map<String, dynamic> getPoolStatistics() {
    final stats = <String, dynamic>{};
    
    for (final entry in _imageBufferPools.entries) {
      final key = entry.key;
      final pool = entry.value;
      final usage = _poolUsageCount[key] ?? 0;
      
      stats[key] = {
        'available': pool.length,
        'totalUsage': usage,
        'size': commonImageSizes[key],
      };
    }
    
    return stats;
  }

  /// Optimize pools by adjusting sizes based on usage
  void optimizePools() {
    for (final entry in _imageBufferPools.entries) {
      final key = entry.key;
      final pool = entry.value;
      final usage = _poolUsageCount[key] ?? 0;
      
      // If pool is rarely used, reduce size
      if (usage < 10 && pool.length > minPoolSize) {
        final removeCount = (pool.length - minPoolSize).clamp(0, 2);
        for (int i = 0; i < removeCount; i++) {
          if (pool.isNotEmpty) pool.removeLast();
        }
        
        if (kDebugMode) {
          print('🔧 Optimized pool $key: removed $removeCount buffers');
        }
      }
      
      // Reset usage counter
      _poolUsageCount[key] = 0;
    }
  }

  /// Clear all pools
  void clearPools() {
    for (final pool in _imageBufferPools.values) {
      pool.clear();
    }
    _poolUsageCount.clear();
    
    if (kDebugMode) {
      print('🧹 All memory pools cleared');
    }
  }

  /// Dispose service
  void dispose() {
    clearPools();
    _imageBufferPools.clear();
    
    if (kDebugMode) {
      print('🔄 MemoryPoolService disposed');
    }
  }
}

/// Helper class for managing image processing memory
class ImageProcessingMemoryManager {
  static final MemoryPoolService _memoryPool = MemoryPoolService.instance;
  
  /// Process image with memory pool management
  static T processImageWithPool<T>(
    String sizeKey,
    T Function(Uint8List buffer) processor,
  ) {
    final buffer = _memoryPool.getImageBuffer(sizeKey);
    try {
      return processor(buffer);
    } finally {
      _memoryPool.returnImageBuffer(sizeKey, buffer);
    }
  }
  
  /// Get appropriate size key for image dimensions
  static String getSizeKey(int width, int height) {
    // Find closest matching size
    String bestKey = 'vga';
    int bestDiff = double.maxFinite.toInt();
    
    for (final entry in MemoryPoolService.commonImageSizes.entries) {
      final size = entry.value;
      final diff = (width - size[0]).abs() + (height - size[1]).abs();
      
      if (diff < bestDiff) {
        bestDiff = diff;
        bestKey = entry.key;
      }
    }
    
    return bestKey;
  }
}

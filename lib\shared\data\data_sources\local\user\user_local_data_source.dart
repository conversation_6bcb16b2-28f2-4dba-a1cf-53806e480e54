import '../../../models/user/user_model.dart';

/// Abstract interface for user local data source
abstract class UserLocalDataSource {
  /// Cache users list
  Future<void> cacheUsers(List<UserModel> users, {String? cacheKey});
  
  /// Get cached users list
  Future<List<UserModel>?> getCachedUsers({String? cacheKey});
  
  /// Cache single user
  Future<void> cacheUser(UserModel user);
  
  /// Get cached user by ID
  Future<UserModel?> getCachedUser(String userId);
  
  /// Save user search results
  Future<void> cacheSearchResults(String query, List<UserModel> results);
  
  /// Get cached search results
  Future<List<UserModel>?> getCachedSearchResults(String query);
  
  /// Save user filters
  Future<void> saveUserFilters(Map<String, dynamic> filters);
  
  /// Get saved user filters
  Future<Map<String, dynamic>?> getUserFilters();
  
  /// Save user sort preferences
  Future<void> saveUserSortPreferences(String sortBy, String sortDirection);
  
  /// Get user sort preferences
  Future<Map<String, String>?> getUserSortPreferences();
  
  /// Clear all user cache
  Future<void> clearUserCache();
  
  /// Clear expired cache entries
  Future<void> clearExpiredCache();
  
  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats();
}

/// Re-export secure communication models for terminal app
///
/// This file provides access to secure communication classes and models
/// from the secure_comm package for use in the terminal application.

// Export main SecureComm class
export '../../../packages/secure_comm/lib/src/secure_comm_base.dart';

// Export message models
export '../../../packages/secure_comm/lib/src/models/secure_message.dart';
export '../../../packages/secure_comm/lib/src/models/device_registration.dart';

// Export transport interfaces
export '../../../packages/secure_comm/lib/src/transport/transport_interface.dart';
export '../../../packages/secure_comm/lib/src/transport/http_transport.dart';
export '../../../packages/secure_comm/lib/src/transport/mqtt_transport.dart';
export '../../../packages/secure_comm/lib/src/transport/websocket_transport.dart';

// Export utilities
export '../../../packages/secure_comm/lib/src/message_builder.dart';
export '../../../packages/secure_comm/lib/src/crypto_utils.dart';

// Export exceptions
export '../../../packages/secure_comm/lib/src/exceptions.dart';

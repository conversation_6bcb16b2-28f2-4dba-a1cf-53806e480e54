import 'package:equatable/equatable.dart';
import 'user_role.dart';
import 'user_unit.dart';
import 'user_profile.dart';

/// Enhanced User domain entity
/// 
/// Represents a user in the domain layer with comprehensive information
class User extends Equatable {
  final String id;
  final String username;
  final String name;
  final String? email;
  final String? phone;
  final DateTime? dob;
  final String? gender;
  final String? avatar;
  final bool? isActive;
  final String? currentTenantId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserRole? role;
  final UserUnit? unit;
  final UserProfile? profile;
  final List<String> permissions;
  final Map<String, dynamic> metadata;

  const User({
    required this.id,
    required this.username,
    required this.name,
    this.email,
    this.phone,
    this.dob,
    this.gender,
    this.avatar,
    this.isActive,
    this.currentTenantId,
    this.createdAt,
    this.updatedAt,
    this.role,
    this.unit,
    this.profile,
    this.permissions = const [],
    this.metadata = const {},
  });

  /// Copy with new values
  User copyWith({
    String? id,
    String? username,
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
    String? avatar,
    bool? isActive,
    String? currentTenantId,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserRole? role,
    UserUnit? unit,
    UserProfile? profile,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dob: dob ?? this.dob,
      gender: gender ?? this.gender,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      currentTenantId: currentTenantId ?? this.currentTenantId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      role: role ?? this.role,
      unit: unit ?? this.unit,
      profile: profile ?? this.profile,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get display name (name or username)
  String get displayName => name.isNotEmpty ? name : username;

  /// Get initials for avatar
  String get initials {
    final words = displayName.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0].substring(0, 1).toUpperCase();
    }
    return 'U';
  }

  /// Get age from date of birth
  int? get age {
    if (dob == null) return null;
    final now = DateTime.now();
    int age = now.year - dob!.year;
    if (now.month < dob!.month || (now.month == dob!.month && now.day < dob!.day)) {
      age--;
    }
    return age;
  }

  /// Check if user is active
  bool get isActiveUser => isActive ?? false;

  /// Check if user has specific role
  bool hasRole(String roleName) {
    return role?.name.toLowerCase() == roleName.toLowerCase();
  }

  /// Check if user has any of the specified roles
  bool hasAnyRole(List<String> roleNames) {
    if (role == null) return false;
    return roleNames.any((roleName) => hasRole(roleName));
  }

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    return permissions.contains(permission) || 
           role?.hasPermission(permission) == true;
  }

  /// Check if user has any of the specified permissions
  bool hasAnyPermission(List<String> requiredPermissions) {
    return requiredPermissions.any((permission) => hasPermission(permission));
  }

  /// Check if user has all of the specified permissions
  bool hasAllPermissions(List<String> requiredPermissions) {
    return requiredPermissions.every((permission) => hasPermission(permission));
  }

  /// Check if user belongs to specific unit
  bool belongsToUnit(String unitId) {
    return unit?.id == unitId;
  }

  /// Check if user belongs to any of the specified units
  bool belongsToAnyUnit(List<String> unitIds) {
    if (unit == null) return false;
    return unitIds.contains(unit!.id);
  }

  /// Get user's role level
  int get roleLevel => role?.level ?? 0;

  /// Check if user has higher role level than specified
  bool hasRoleLevelAbove(int level) {
    return roleLevel > level;
  }

  /// Check if user has role level at or above specified
  bool hasRoleLevelAtLeast(int level) {
    return roleLevel >= level;
  }

  /// Get metadata value
  T? getMetadata<T>(String key) {
    return metadata[key] as T?;
  }

  /// Check if user can manage other user
  bool canManage(User otherUser) {
    // Can't manage yourself
    if (id == otherUser.id) return false;
    
    // Must have higher role level
    if (roleLevel <= otherUser.roleLevel) return false;
    
    // Must have user management permission
    if (!hasPermission('user.manage')) return false;
    
    // If both users are in units, manager must be in same or parent unit
    if (unit != null && otherUser.unit != null) {
      return unit!.canManage(otherUser.unit!);
    }
    
    return true;
  }

  /// Check if user can view other user's details
  bool canView(User otherUser) {
    // Can always view yourself
    if (id == otherUser.id) return true;
    
    // Must have user view permission
    if (!hasPermission('user.view')) return false;
    
    // If both users are in units, viewer must be in same or parent unit
    if (unit != null && otherUser.unit != null) {
      return unit!.canView(otherUser.unit!);
    }
    
    return true;
  }

  /// Get user status
  UserStatus get status {
    if (!isActiveUser) return UserStatus.inactive;
    
    if (role?.isAdmin == true) return UserStatus.admin;
    if (role?.isModerator == true) return UserStatus.moderator;
    
    return UserStatus.active;
  }

  /// Get contact information
  UserContact get contact {
    return UserContact(
      email: email,
      phone: phone,
      hasEmail: email != null && email!.isNotEmpty,
      hasPhone: phone != null && phone!.isNotEmpty,
    );
  }

  /// Validate user data
  UserValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Required fields
    if (id.isEmpty) errors.add('User ID is required');
    if (username.isEmpty) errors.add('Username is required');
    if (name.isEmpty) errors.add('Name is required');

    // Username validation
    if (username.length < 3) {
      errors.add('Username must be at least 3 characters');
    }
    if (username.length > 50) {
      errors.add('Username cannot exceed 50 characters');
    }

    // Email validation
    if (email != null && email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
      if (!emailRegex.hasMatch(email!)) {
        errors.add('Invalid email format');
      }
    }

    // Phone validation
    if (phone != null && phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
      if (!phoneRegex.hasMatch(phone!)) {
        warnings.add('Phone number format may be invalid');
      }
    }

    // Age validation
    if (dob != null) {
      final userAge = age;
      if (userAge != null) {
        if (userAge < 0) {
          errors.add('Date of birth cannot be in the future');
        } else if (userAge > 150) {
          warnings.add('Age seems unusually high');
        }
      }
    }

    // Role validation
    if (role != null) {
      final roleValidation = role!.validate();
      if (!roleValidation.isValid) {
        warnings.add('User role has validation issues');
      }
    }

    // Unit validation
    if (unit != null) {
      final unitValidation = unit!.validate();
      if (!unitValidation.isValid) {
        warnings.add('User unit has validation issues');
      }
    }

    return UserValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  @override
  List<Object?> get props => [
    id,
    username,
    name,
    email,
    phone,
    dob,
    gender,
    avatar,
    isActive,
    createdAt,
    updatedAt,
    role,
    unit,
    profile,
    permissions,
    metadata,
  ];

  @override
  String toString() {
    return 'User(id: $id, username: $username, name: $name, email: $email, role: ${role?.name}, unit: ${unit?.name}, isActive: $isActive)';
  }
}

/// User status enumeration
enum UserStatus {
  active,
  inactive,
  admin,
  moderator,
}

/// User contact information
class UserContact {
  final String? email;
  final String? phone;
  final bool hasEmail;
  final bool hasPhone;

  const UserContact({
    this.email,
    this.phone,
    required this.hasEmail,
    required this.hasPhone,
  });

  bool get hasAnyContact => hasEmail || hasPhone;
  String get primaryContact => hasEmail ? email! : (hasPhone ? phone! : '');
}

/// User validation result
class UserValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const UserValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}

/// Extension for UserStatus
extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.inactive:
        return 'Inactive';
      case UserStatus.admin:
        return 'Administrator';
      case UserStatus.moderator:
        return 'Moderator';
    }
  }

  bool get isPrivileged => this == UserStatus.admin || this == UserStatus.moderator;
}

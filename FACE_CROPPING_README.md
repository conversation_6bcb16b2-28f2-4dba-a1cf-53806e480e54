# Face Cropping Functionality

This document describes the comprehensive face cropping functionality implemented in the mobile app, which includes face detection, cropping, and three different API processing methods with configurable side effects.

## Overview

The face cropping system provides:
- **Face Detection & Cropping**: Extract face regions from captured images using Google ML Kit
- **Priority Queue Processing**: Sequential processing of best face only to reduce device load
- **Three API Methods**: Synchronous, Asynchronous, and Queue-based processing
- **Side Effects System**: Configurable actions triggered by API responses (relay triggers, database updates, notifications, etc.)
- **Enhanced Integration**: Seamless integration with existing face capture workflow

## 🎯 Priority Queue System

### Key Features
- **Single Face Processing**: Only processes the best (largest/closest) face at a time
- **Quality Prioritization**: Automatically selects highest quality faces based on size, pose, and eye detection
- **Sequential Pipeline**: Detect → Crop → API Processing → Side Effects → Complete before next face
- **Device Load Reduction**: Prevents simultaneous processing to avoid overwhelming mobile devices
- **Intelligent Cooldown**: 1.5-2s delay between processing attempts to prevent duplicate processing

### Processing Flow
```
Face Detection → Quality Assessment → Best Face Selection → Queue Addition
                                                                ↓
Side Effects ← API Response ← Face Cropping ← Queue Processing
```

## Architecture

### Core Components

1. **FaceCroppingService** - Core face cropping functionality
2. **FaceCroppingApiService** - Three API processing methods
3. **FaceCroppingSideEffectsHandler** - Configurable side effects system
4. **EnhancedFaceCaptureService** - Integration with existing face capture
5. **EnhancedFaceCaptureProvider** - State management and UI integration

### Processing Methods

#### Priority Queue Processing (Recommended)
```dart
// Initialize priority processing
final priorityProvider = PriorityFaceProcessingProvider();
priorityProvider.setMinFaceQuality(0.7); // 70% quality threshold
priorityProvider.setFaceDetectionCooldown(Duration(milliseconds: 1500));

// Auto-process detected faces (only best face)
final taskId = await priorityProvider.processDetectedFaces(
  imagePath: imagePath,
  detectedFaces: allDetectedFaces, // System selects best face automatically
  direction: FaceDirection.front,
  context: {'user_id': 'user123'},
  enabledSideEffects: [SideEffectType.auditLog, SideEffectType.databaseUpdate],
);
```

#### Method 1: Synchronous Processing
```dart
final result = await apiService.processFaceCroppingSynchronous(
  imagePath: imagePath,
  face: detectedFace,
  context: {'user_id': 'user123'},
  enabledSideEffects: [SideEffectType.auditLog, SideEffectType.databaseUpdate],
);
```

#### Method 2: Asynchronous Processing
```dart
final operationId = await apiService.processFaceCroppingAsynchronous(
  imagePath: imagePath,
  face: detectedFace,
  onProgress: (progress, status) => print('Progress: $progress'),
  onComplete: (result) => print('Completed: ${result.success}'),
  onError: (error) => print('Error: $error'),
);
```

#### Method 3: Queue-based Processing
```dart
final queueId = await apiService.addToFaceCroppingQueue(
  imagePath: imagePath,
  face: detectedFace,
  priority: 5,
  context: {'batch_id': 'batch123'},
);

// Check queue status
final status = apiService.getQueueStatus();
```

## Side Effects System

The system supports configurable side effects that are triggered based on API responses:

### Available Side Effects
- **RelayTrigger**: Hardware relay control
- **DatabaseUpdate**: Database record updates
- **PushNotification**: Push notifications
- **EmailNotification**: Email notifications
- **AuditLog**: Audit trail logging
- **WebhookCall**: Custom webhook calls
- **FileSystemOperation**: File system operations
- **CacheUpdate**: Cache updates

### Configuration
```dart
provider.setEnabledSideEffects([
  SideEffectType.auditLog,
  SideEffectType.databaseUpdate,
  SideEffectType.pushNotification,
]);
```

## Integration Examples

### Priority Queue Integration (Recommended)
```dart
// Initialize integrated service
final integratedService = IntegratedFaceProcessingService();
final priorityProvider = PriorityFaceProcessingProvider();

await integratedService.initialize(
  priorityProvider: priorityProvider,
  faceDetectionProvider: faceDetectionProvider,
  autoProcessingEnabled: true,
  autoProcessingInterval: Duration(milliseconds: 2000), // 2s interval
  minFaceQualityThreshold: 0.7, // 70% quality
);

// Auto-processing will handle face detection and processing
// Only the best face will be processed at a time
```

### Widget Integration with Priority Processing
```dart
// Use priority face processing widget
PriorityFaceProcessingWidget(
  autoProcess: true,
  defaultDirection: FaceDirection.front,
  context: {'user_id': 'user123'},
  enabledSideEffects: [
    SideEffectType.auditLog,
    SideEffectType.databaseUpdate,
  ],
  onTaskAdded: (taskId) => print('Task added: $taskId'),
  onTaskCompleted: (taskId, success) => print('Task completed: $success'),
  child: YourCameraWidget(),
)
```

### Basic Integration (Legacy)
```dart
// Create enhanced provider
final provider = EnhancedFaceCaptureProvider();

// Configure processing
provider.setProcessingMode(ProcessingMode.synchronous);
provider.setCropPadding(0.2); // 20% padding
provider.setOutputQuality(85); // 85% JPEG quality

// Process captured faces
final result = await provider.processCapture(
  capturedImages: capturedImages,
  detectedFaces: detectedFaces,
);

if (result.success) {
  print('Processed ${result.croppedImageCount} cropped faces');
  print('API Results: ${result.getApiResultsSummary()}');
}
```

## Configuration

### Constants (FaceCroppingConstants)
```dart
// Cropping parameters
static const double defaultPadding = 0.2;
static const int defaultOutputQuality = 85;

// API timeouts
static const Duration synchronousTimeout = Duration(seconds: 30);
static const Duration asynchronousTimeout = Duration(seconds: 60);
static const Duration queueBasedTimeout = Duration(seconds: 120);

// Queue settings
static const int maxQueueSize = 50;
static const int queueBatchSize = 5;
```

### Processing Modes
- **Synchronous**: Immediate processing with direct response
- **Asynchronous**: Non-blocking processing with callbacks
- **QueueBased**: Batch processing with priority queuing

## API Endpoints

The system expects the following API endpoints to be available:

- `/face/crop/sync` - Synchronous processing
- `/face/crop/async` - Asynchronous processing  
- `/face/crop/queue` - Queue-based processing
- `/face/side-effects` - Side effects execution
- `/face/side-effects/status` - Side effects status

## Testing

### Running Tests
```dart
// Run comprehensive tests
final testResults = await FaceCroppingTestService.runAllTests();

print('Overall Success: ${testResults.overallSuccess}');
print('Basic Cropping: ${testResults.basicCroppingTest.success}');
print('Sync API: ${testResults.syncApiTest.success}');
print('Async API: ${testResults.asyncApiTest.success}');
print('Queue API: ${testResults.queueApiTest.success}');
```

### Test Coverage
- Basic face cropping functionality
- All three API processing methods
- Side effects handler
- Enhanced face capture service
- Configuration validation

## Error Handling

The system provides comprehensive error handling:

```dart
try {
  final result = await provider.processCapture(
    capturedImages: capturedImages,
    detectedFaces: detectedFaces,
  );
  
  if (!result.success) {
    // Handle processing failure
    print('Processing failed: ${result.error}');
  }
} catch (e) {
  // Handle exceptions
  print('Exception: $e');
}
```

## Performance Considerations

### Optimization Features
- **Buffer Pooling**: Reuses memory buffers for image processing
- **Concurrent Processing**: Parallel processing with semaphore limits
- **Queue Management**: Batch processing with configurable intervals
- **Cleanup**: Automatic temporary file cleanup

### Performance Monitoring
```dart
provider.addListener(() {
  if (provider.isProcessing) {
    print('Progress: ${(provider.processingProgress * 100).toInt()}%');
    print('Status: ${provider.processingStatus}');
  }
});
```

## File Structure

```
lib/shared/
├── services/
│   ├── face_cropping_service.dart              # Core cropping functionality
│   ├── face_cropping_api_service.dart          # Three API methods
│   ├── face_cropping_side_effects_handler.dart # Side effects system
│   ├── enhanced_face_capture_service.dart      # Integration service
│   └── face_cropping_test_service.dart         # Testing utilities
├── providers/
│   └── enhanced_face_capture_provider.dart     # State management
├── models/
│   └── enhanced_face_capture_result.dart       # Result models
├── core/constants/
│   └── face_cropping_constants.dart            # Configuration constants
├── widgets/
│   └── face_cropping_demo_widget.dart          # Demo UI widget
└── examples/
    └── face_cropping_integration_example.dart  # Integration examples
```

## Dependencies

The face cropping functionality requires:
- `google_mlkit_face_detection` - Face detection
- `image` - Image processing
- `http` - API communication
- `path_provider` - File system access

## Future Enhancements

Potential future improvements:
- Real-time face cropping during capture
- Advanced image quality assessment
- Machine learning-based crop optimization
- Cloud-based processing options
- Advanced side effects (SMS, Slack, etc.)

## Support

For issues or questions about the face cropping functionality:
1. Check the test results using `FaceCroppingTestService`
2. Review the integration examples
3. Verify configuration constants
4. Check API endpoint availability

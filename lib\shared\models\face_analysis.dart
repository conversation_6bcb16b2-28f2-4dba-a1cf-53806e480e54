import 'dart:math';
import 'dart:ui';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// Enhanced face analysis model with improved comparison and quality assessment
class FaceAnalysis {
  final Face face;
  final double qualityScore;
  final FaceQualityMetrics qualityMetrics;
  final FacePoseInfo poseInfo;
  final DateTime timestamp;
  final String id;

  FaceAnalysis({
    required this.face,
    required this.qualityScore,
    required this.qualityMetrics,
    required this.poseInfo,
    required this.timestamp,
    required this.id,
  });

  /// Create face analysis from ML Kit Face
  factory FaceAnalysis.fromFace(Face face, {String? customId}) {
    final id = customId ?? _generateFaceId(face);
    final qualityMetrics = FaceQualityMetrics.fromFace(face);
    final poseInfo = FacePoseInfo.fromFace(face);
    final qualityScore = _calculateQualityScore(face, qualityMetrics, poseInfo);

    return FaceAnalysis(
      face: face,
      qualityScore: qualityScore,
      qualityMetrics: qualityMetrics,
      poseInfo: poseInfo,
      timestamp: DateTime.now(),
      id: id,
    );
  }

  /// Generate unique ID for face based on its characteristics
  static String _generateFaceId(Face face) {
    final box = face.boundingBox;
    final centerX = (box.left + box.right) / 2;
    final centerY = (box.top + box.bottom) / 2;
    final area = box.width * box.height;
    
    // Create a simple hash based on position and size
    final hash = (centerX * 1000 + centerY * 1000 + area).round();
    return 'face_${hash}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Calculate comprehensive quality score
  static double _calculateQualityScore(
    Face face,
    FaceQualityMetrics metrics,
    FacePoseInfo poseInfo,
  ) {
    double score = 0.0;

    // Size quality (40% weight)
    score += 0.4 * metrics.sizeQuality;

    // Pose quality (30% weight)
    score += 0.3 * poseInfo.poseQuality;

    // Sharpness/clarity (20% weight) - estimated from bounding box stability
    score += 0.2 * metrics.clarityEstimate;

    // Lighting/contrast (10% weight) - basic estimation
    score += 0.1 * metrics.lightingEstimate;

    return score.clamp(0.0, 1.0);
  }

  /// Compare with another face analysis
  bool isSimilarTo(FaceAnalysis other, {double threshold = 0.15}) {
    return FaceComparator.compareFaces(this, other) < threshold;
  }

  /// Check if this face is better quality than another
  bool isBetterThan(FaceAnalysis other) {
    return qualityScore > other.qualityScore;
  }

  /// Get quality level string
  String get qualityLevel {
    if (qualityScore >= 0.9) return 'Excellent';
    if (qualityScore >= 0.75) return 'Good';
    if (qualityScore >= 0.6) return 'Fair';
    if (qualityScore >= 0.4) return 'Acceptable';
    return 'Poor';
  }

  /// Check if face meets minimum quality requirements
  bool meetsQualityThreshold(double threshold) {
    return qualityScore >= threshold;
  }

  @override
  String toString() {
    return 'FaceAnalysis(id: $id, quality: ${qualityScore.toStringAsFixed(2)}, '
           'level: $qualityLevel, pose: ${poseInfo.direction})';
  }
}

/// Detailed face quality metrics
class FaceQualityMetrics {
  final double sizeQuality;
  final double aspectRatioQuality;
  final double clarityEstimate;
  final double lightingEstimate;
  final double faceArea;
  final double faceWidth;
  final double faceHeight;

  FaceQualityMetrics({
    required this.sizeQuality,
    required this.aspectRatioQuality,
    required this.clarityEstimate,
    required this.lightingEstimate,
    required this.faceArea,
    required this.faceWidth,
    required this.faceHeight,
  });

  factory FaceQualityMetrics.fromFace(Face face) {
    final box = face.boundingBox;
    final area = box.width * box.height;
    final width = box.width;
    final height = box.height;

    // Size quality based on area
    const minArea = 10000.0;
    const idealArea = 50000.0;
    final sizeQuality = area < minArea 
        ? 0.0 
        : (area / idealArea).clamp(0.0, 1.0);

    // Aspect ratio quality (faces should be roughly rectangular)
    final aspectRatio = width / height;
    const idealRatio = 0.75; // Typical face aspect ratio
    final ratioDeviation = (aspectRatio - idealRatio).abs() / idealRatio;
    final aspectRatioQuality = (1.0 - ratioDeviation).clamp(0.0, 1.0);

    // Clarity estimate (larger faces tend to be clearer)
    final clarityEstimate = (area / 100000).clamp(0.0, 1.0);

    // Lighting estimate (basic - could be improved with actual image analysis)
    final lightingEstimate = 0.7; // Default moderate lighting

    return FaceQualityMetrics(
      sizeQuality: sizeQuality,
      aspectRatioQuality: aspectRatioQuality,
      clarityEstimate: clarityEstimate,
      lightingEstimate: lightingEstimate,
      faceArea: area,
      faceWidth: width,
      faceHeight: height,
    );
  }
}

/// Face pose information
class FacePoseInfo {
  final double? eulerX;
  final double? eulerY;
  final double? eulerZ;
  final FaceDirection direction;
  final double poseQuality;
  final bool isFrontal;

  FacePoseInfo({
    required this.eulerX,
    required this.eulerY,
    required this.eulerZ,
    required this.direction,
    required this.poseQuality,
    required this.isFrontal,
  });

  factory FacePoseInfo.fromFace(Face face) {
    final eulerX = face.headEulerAngleX;
    final eulerY = face.headEulerAngleY;
    final eulerZ = face.headEulerAngleZ;

    final direction = _detectDirection(eulerX, eulerY);
    final poseQuality = _calculatePoseQuality(eulerX, eulerY, eulerZ);
    final isFrontal = direction == FaceDirection.front;

    return FacePoseInfo(
      eulerX: eulerX,
      eulerY: eulerY,
      eulerZ: eulerZ,
      direction: direction,
      poseQuality: poseQuality,
      isFrontal: isFrontal,
    );
  }

  static FaceDirection _detectDirection(double? eulerX, double? eulerY) {
    if (eulerX == null || eulerY == null) return FaceDirection.unknown;
    
    const centerTolerance = 10.0;
    const directionTolerance = 15.0;
    
    if (eulerX.abs() <= centerTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.front;
    }
    if (eulerX > directionTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.top;
    }
    if (eulerX < -directionTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.bottom;
    }
    if (eulerX.abs() <= centerTolerance && eulerY < -directionTolerance) {
      return FaceDirection.left;
    }
    if (eulerX.abs() <= centerTolerance && eulerY > directionTolerance) {
      return FaceDirection.right;
    }
    return FaceDirection.unknown;
  }

  static double _calculatePoseQuality(double? eulerX, double? eulerY, double? eulerZ) {
    if (eulerX == null || eulerY == null) return 0.5;

    // Penalize extreme poses
    const maxYaw = 30.0;
    const maxPitch = 25.0;
    const maxRoll = 20.0;

    final yawPenalty = (eulerY.abs()) / maxYaw;
    final pitchPenalty = (eulerX.abs()) / maxPitch;
    final rollPenalty = (eulerZ?.abs() ?? 0) / maxRoll;

    final totalPenalty = (yawPenalty + pitchPenalty + rollPenalty) / 3;
    return (1.0 - totalPenalty).clamp(0.0, 1.0);
  }
}

/// Face direction enum
enum FaceDirection { front, top, bottom, left, right, unknown }

/// Face comparator for improved face matching
class FaceComparator {
  /// Compare two face analyses and return similarity score (0.0 = identical, 1.0 = completely different)
  static double compareFaces(FaceAnalysis face1, FaceAnalysis face2) {
    double totalDistance = 0.0;
    int factors = 0;

    // Position similarity (30% weight)
    final positionDistance = _comparePositions(face1.face.boundingBox, face2.face.boundingBox);
    totalDistance += 0.3 * positionDistance;
    factors++;

    // Size similarity (25% weight)
    final sizeDistance = _compareSizes(face1.qualityMetrics, face2.qualityMetrics);
    totalDistance += 0.25 * sizeDistance;
    factors++;

    // Pose similarity (25% weight)
    final poseDistance = _comparePoses(face1.poseInfo, face2.poseInfo);
    totalDistance += 0.25 * poseDistance;
    factors++;

    // Time proximity (20% weight)
    final timeDistance = _compareTimestamps(face1.timestamp, face2.timestamp);
    totalDistance += 0.2 * timeDistance;
    factors++;

    return factors > 0 ? totalDistance / factors : 1.0;
  }

  static double _comparePositions(Rect box1, Rect box2) {
    final center1 = Offset(box1.center.dx, box1.center.dy);
    final center2 = Offset(box2.center.dx, box2.center.dy);
    
    final distance = (center1 - center2).distance;
    final maxDistance = max(box1.width + box1.height, box2.width + box2.height);
    
    return (distance / maxDistance).clamp(0.0, 1.0);
  }

  static double _compareSizes(FaceQualityMetrics metrics1, FaceQualityMetrics metrics2) {
    final areaRatio = metrics1.faceArea / metrics2.faceArea;
    final normalizedRatio = areaRatio > 1.0 ? 1.0 / areaRatio : areaRatio;
    return 1.0 - normalizedRatio;
  }

  static double _comparePoses(FacePoseInfo pose1, FacePoseInfo pose2) {
    if (pose1.eulerX == null || pose1.eulerY == null || 
        pose2.eulerX == null || pose2.eulerY == null) {
      return 0.5; // Default moderate difference
    }

    final xDiff = (pose1.eulerX! - pose2.eulerX!).abs();
    final yDiff = (pose1.eulerY! - pose2.eulerY!).abs();
    
    // Normalize to 0-1 range (assuming max difference of 60 degrees)
    return ((xDiff + yDiff) / 120.0).clamp(0.0, 1.0);
  }

  static double _compareTimestamps(DateTime time1, DateTime time2) {
    final diffMs = (time1.difference(time2)).inMilliseconds.abs();
    // Normalize to 0-1 range (1 second = 0.5 difference)
    return (diffMs / 2000.0).clamp(0.0, 1.0);
  }
}

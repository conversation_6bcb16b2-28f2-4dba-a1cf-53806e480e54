/// Comprehensive validation utilities for the application
class ValidationUtils {
  // Private constructor to prevent instantiation
  ValidationUtils._();

  /// Validate email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    if (value.length > 254) {
      return 'Email address is too long';
    }
    
    return null;
  }

  /// Validate password
  static String? validatePassword(String? value, {
    int minLength = 6,
    int maxLength = 128,
    bool requireUppercase = false,
    bool requireLowercase = false,
    bool requireNumbers = false,
    bool requireSpecialChars = false,
  }) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < minLength) {
      return 'Password must be at least $minLength characters';
    }
    
    if (value.length > maxLength) {
      return 'Password cannot exceed $maxLength characters';
    }
    
    if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }
    
    if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }
    
    if (requireNumbers && !RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }
    
    if (requireSpecialChars && !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }
    
    return null;
  }

  /// Validate password confirmation
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Password confirmation is required';
    }
    
    if (value != password) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  /// Validate phone number
  static String? validatePhone(String? value, {bool required = true}) {
    if (value == null || value.isEmpty) {
      return required ? 'Phone number is required' : null;
    }
    
    // Remove all non-digit characters
    final digits = value.replaceAll(RegExp(r'[^0-9]'), '');
    
    if (digits.length < 10) {
      return 'Phone number must be at least 10 digits';
    }
    
    if (digits.length > 15) {
      return 'Phone number cannot exceed 15 digits';
    }
    
    return null;
  }

  /// Validate name
  static String? validateName(String? value, {
    bool required = true,
    int minLength = 2,
    int maxLength = 50,
    String fieldName = 'Name',
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? '$fieldName is required' : null;
    }
    
    final trimmed = value.trim();
    
    if (trimmed.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    
    if (trimmed.length > maxLength) {
      return '$fieldName cannot exceed $maxLength characters';
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!RegExp(r"^[a-zA-ZÀ-ÿ\s\-']+$").hasMatch(trimmed)) {
      return '$fieldName contains invalid characters';
    }
    
    return null;
  }

  /// Validate username
  static String? validateUsername(String? value, {
    int minLength = 3,
    int maxLength = 30,
  }) {
    if (value == null || value.isEmpty) {
      return 'Username is required';
    }
    
    if (value.length < minLength) {
      return 'Username must be at least $minLength characters';
    }
    
    if (value.length > maxLength) {
      return 'Username cannot exceed $maxLength characters';
    }
    
    // Username can contain letters, numbers, underscores, and hyphens
    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(value)) {
      return 'Username can only contain letters, numbers, underscores, and hyphens';
    }
    
    // Must start with a letter
    if (!RegExp(r'^[a-zA-Z]').hasMatch(value)) {
      return 'Username must start with a letter';
    }
    
    return null;
  }

  /// Validate date of birth
  static String? validateDateOfBirth(DateTime? value, {
    int minAge = 0,
    int maxAge = 150,
  }) {
    if (value == null) {
      return 'Date of birth is required';
    }
    
    final now = DateTime.now();
    final age = now.year - value.year;
    
    // Check if birthday hasn't occurred this year
    final adjustedAge = (now.month < value.month || 
        (now.month == value.month && now.day < value.day)) ? age - 1 : age;
    
    if (adjustedAge < minAge) {
      return 'Must be at least $minAge years old';
    }
    
    if (adjustedAge > maxAge) {
      return 'Age cannot exceed $maxAge years';
    }
    
    // Check if date is in the future
    if (value.isAfter(now)) {
      return 'Date of birth cannot be in the future';
    }
    
    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value, {bool required = true}) {
    if (value == null || value.isEmpty) {
      return required ? 'URL is required' : null;
    }
    
    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || !uri.hasAuthority) {
        return 'Please enter a valid URL';
      }
      
      // Check for valid schemes
      if (!['http', 'https'].contains(uri.scheme.toLowerCase())) {
        return 'URL must start with http:// or https://';
      }
      
      return null;
    } catch (e) {
      return 'Please enter a valid URL';
    }
  }

  /// Validate required field
  static String? validateRequired(dynamic value, String fieldName) {
    if (value == null) {
      return '$fieldName is required';
    }
    
    if (value is String && value.trim().isEmpty) {
      return '$fieldName is required';
    }
    
    if (value is List && value.isEmpty) {
      return '$fieldName is required';
    }
    
    return null;
  }

  /// Validate string length
  static String? validateLength(String? value, {
    int? minLength,
    int? maxLength,
    String fieldName = 'Field',
  }) {
    if (value == null) return null;
    
    if (minLength != null && value.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    
    if (maxLength != null && value.length > maxLength) {
      return '$fieldName cannot exceed $maxLength characters';
    }
    
    return null;
  }

  /// Validate number range
  static String? validateNumberRange(num? value, {
    num? min,
    num? max,
    String fieldName = 'Value',
  }) {
    if (value == null) return null;
    
    if (min != null && value < min) {
      return '$fieldName must be at least $min';
    }
    
    if (max != null && value > max) {
      return '$fieldName cannot exceed $max';
    }
    
    return null;
  }

  /// Combine multiple validators
  static String? combineValidators(dynamic value, List<String? Function(dynamic)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  /// Create a required validator
  static String? Function(dynamic) required(String fieldName) {
    return (value) => validateRequired(value, fieldName);
  }

  /// Create an email validator
  static String? Function(String?) get email => validateEmail;

  /// Create a password validator with specific requirements
  static String? Function(String?) password({
    int minLength = 6,
    int maxLength = 128,
    bool requireUppercase = false,
    bool requireLowercase = false,
    bool requireNumbers = false,
    bool requireSpecialChars = false,
  }) {
    return (value) => validatePassword(
      value,
      minLength: minLength,
      maxLength: maxLength,
      requireUppercase: requireUppercase,
      requireLowercase: requireLowercase,
      requireNumbers: requireNumbers,
      requireSpecialChars: requireSpecialChars,
    );
  }

  /// Create a length validator
  static String? Function(String?) length({
    int? minLength,
    int? maxLength,
    String fieldName = 'Field',
  }) {
    return (value) => validateLength(
      value,
      minLength: minLength,
      maxLength: maxLength,
      fieldName: fieldName,
    );
  }
}

# CORS Setup for Web Development

## 🚨 Problem
When running the Flutter web app, API calls to external servers are blocked by CORS (Cross-Origin Resource Sharing) policy.

## ✅ Solution: Disable Chrome Web Security

### **Option 1: Use Provided Scripts (Recommended)**

#### Windows:
```bash
# Run the batch script
scripts/launch-chrome-cors-disabled.bat
```

#### Linux/Mac:
```bash
# Make script executable
chmod +x scripts/launch-chrome-cors-disabled.sh

# Run the script
./scripts/launch-chrome-cors-disabled.sh
```

### **Option 2: Manual Chrome Launch**

#### Windows:
```cmd
# Close all Chrome instances first
taskkill /f /im chrome.exe

# Launch Chrome with CORS disabled
"C:\Program Files\Google\Chrome\Application\chrome.exe" ^
  --disable-web-security ^
  --user-data-dir="C:\temp\chrome_dev_session" ^
  --allow-running-insecure-content ^
  --disable-features=VizDisplayCompositor ^
  "http://localhost:8080"
```

#### Linux:
```bash
# Close all Chrome instances first
pkill chrome

# Launch Chrome with CORS disabled
google-chrome \
  --disable-web-security \
  --user-data-dir="/tmp/chrome_dev_session" \
  --allow-running-insecure-content \
  --disable-features=VizDisplayCompositor \
  "http://localhost:8080"
```

#### macOS:
```bash
# Close all Chrome instances first
pkill -f "Google Chrome"

# Launch Chrome with CORS disabled
"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" \
  --disable-web-security \
  --user-data-dir="/tmp/chrome_dev_session" \
  --allow-running-insecure-content \
  --disable-features=VizDisplayCompositor \
  "http://localhost:8080"
```

## 🔍 Verification

After launching Chrome with the flags above, you should see:

1. **Warning Banner**: A yellow warning banner at the top saying:
   ```
   "You are using an unsupported command-line flag: --disable-web-security"
   ```

2. **Console Message**: In the Flutter app console, you'll see:
   ```
   🌐 ===== WEB PLATFORM DETECTED =====
   🚨 CORS may block cross-origin requests
   💡 To bypass CORS, start Chrome with: [instructions]
   ```

## ⚠️ Important Security Notes

1. **Development Only**: Only use this for development purposes
2. **Don't Browse Other Sites**: Don't visit other websites with this Chrome instance
3. **Separate User Data**: Uses a separate user data directory to isolate from your main Chrome profile
4. **Close After Development**: Close this Chrome instance when done developing

## 🔧 Alternative Solutions

### Production CORS Proxy
For production, consider using a CORS proxy server instead of disabling browser security.

### Server-Side CORS Headers
The ideal solution is to configure the API server to send proper CORS headers:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-App-Version, X-Platform
```

## 🧪 Testing

1. Start the Flutter web app: `flutter run -d chrome --web-port=8080`
2. Launch Chrome with CORS disabled using one of the methods above
3. Navigate to `http://localhost:8080`
4. Try logging in - API calls should now work without CORS errors

## 📊 Logging

The app will automatically detect web platform and show appropriate CORS instructions in the console.

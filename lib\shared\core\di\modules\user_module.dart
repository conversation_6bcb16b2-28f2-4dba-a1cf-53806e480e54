import 'package:get_it/get_it.dart';
import '../../../data/data_sources/remote/member_role_remote_data_source.dart';
import '../../../data/services/roles_api_service.dart';
import '../../../data/services/users_api_service.dart';

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến User Management
void registerUserDependencies() {
  // Register member role data source
  if (!getIt.isRegistered<MemberRoleRemoteDataSource>()) {
    getIt.registerLazySingleton<MemberRoleRemoteDataSource>(
      () => MemberRoleRemoteDataSourceImpl(),
    );
  }

  // Register roles API service
  if (!getIt.isRegistered<RolesApiService>()) {
    getIt.registerLazySingleton<RolesApiService>(
      () => RolesApiService(),
    );
  }

  // Register users API service
  if (!getIt.isRegistered<UsersApiService>()) {
    getIt.registerLazySingleton<UsersApiService>(
      () => UsersApiService(),
    );
  }
}

/// Unregister tất cả user dependencies (for testing)
void unregisterUserDependencies() {
  if (getIt.isRegistered<MemberRoleRemoteDataSource>()) {
    getIt.unregister<MemberRoleRemoteDataSource>();
  }
  if (getIt.isRegistered<RolesApiService>()) {
    getIt.unregister<RolesApiService>();
  }
  if (getIt.isRegistered<UsersApiService>()) {
    getIt.unregister<UsersApiService>();
  }
}

/// Reset user module (clear và re-register)
void resetUserModule() {
  unregisterUserDependencies();
  registerUserDependencies();
}

/// Check if user dependencies are registered
bool areUserDependenciesRegistered() {
  return getIt.isRegistered<MemberRoleRemoteDataSource>() &&
         getIt.isRegistered<RolesApiService>() &&
         getIt.isRegistered<UsersApiService>();
}

/// Get user-related dependencies for debugging
Map<String, bool> getUserDependenciesStatus() {
  return {
    'MemberRoleRemoteDataSource': getIt.isRegistered<MemberRoleRemoteDataSource>(),
    'RolesApiService': getIt.isRegistered<RolesApiService>(),
    'UsersApiService': getIt.isRegistered<UsersApiService>(),
  };
}

<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="dataLengthNotice"
            type="String" />
    </data>
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.activity.DataLengthCalculatorActivity">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/bt_calculate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:text="@string/calculate" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@+id/bt_calculate"
                android:orientation="vertical">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_image_width"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/image_width"
                    app:counterEnabled="true"
                    app:counterOverflowTextAppearance="@style/HintNormalTextStyle"
                    app:counterMaxLength="4">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_image_width"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_image_height"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/image_height"
                    app:counterEnabled="true"
                    app:counterOverflowTextAppearance="@style/HintNormalTextStyle"
                    app:counterMaxLength="4">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_image_height"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </RelativeLayout>

        <TextView
            android:textSize="@dimen/text_size_calculate_result"
            android:textColor="@android:color/black"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@{dataLengthNotice}" />
    </LinearLayout>
</layout>
import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Service to monitor network connectivity and notify listeners of changes
class NetworkDetectionService extends ChangeNotifier {
  static final NetworkDetectionService _instance = NetworkDetectionService._internal();
  factory NetworkDetectionService() => _instance;
  NetworkDetectionService._internal();

  bool _isOnline = true;
  bool _isInitialized = false;
  Timer? _networkCheckTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  /// Current online status
  bool get isOnline => _isOnline;
  
  /// Whether the service has been initialized
  bool get isInitialized => _isInitialized;
  
  /// Initialize the network detection service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initial connectivity check
      await _checkConnectivity();
      
      // Listen to connectivity changes
      _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          _onConnectivityChanged(results.isNotEmpty ? results.first : ConnectivityResult.none);
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ Connectivity stream error: $error');
          }
        },
      );
      
      // Periodic connectivity verification (every 10 seconds)
      _networkCheckTimer = Timer.periodic(
        const Duration(seconds: 10),
        (_) => _checkConnectivity(),
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('🌐 NetworkDetectionService initialized');
        print('   Initial status: ${_isOnline ? "Online" : "Offline"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize NetworkDetectionService: $e');
      }
      rethrow;
    }
  }
  
  /// Handle connectivity changes from the stream
  void _onConnectivityChanged(ConnectivityResult result) {
    if (kDebugMode) {
      print('🔄 Connectivity changed: $result');
    }
    
    // Verify actual internet connectivity, not just network connection
    _checkConnectivity();
  }
  
  /// Check actual internet connectivity by attempting to reach a reliable host
  Future<void> _checkConnectivity() async {
    try {
      // Try to reach multiple reliable hosts
      final hosts = ['*******', '*******', 'google.com'];
      bool hasConnection = false;
      
      for (final host in hosts) {
        try {
          final result = await InternetAddress.lookup(host)
              .timeout(const Duration(seconds: 3));
          
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            hasConnection = true;
            break;
          }
        } catch (e) {
          // Continue to next host
          continue;
        }
      }
      
      _updateOnlineStatus(hasConnection);
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Connectivity check failed: $e');
      }
      _updateOnlineStatus(false);
    }
  }
  
  /// Update online status and notify listeners if changed
  void _updateOnlineStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      final previousStatus = _isOnline;
      _isOnline = isOnline;
      
      if (kDebugMode) {
        print('🔄 Network status changed: ${previousStatus ? "Online" : "Offline"} → ${_isOnline ? "Online" : "Offline"}');
      }
      
      notifyListeners();
    }
  }
  
  /// Force a connectivity check
  Future<void> forceCheck() async {
    if (kDebugMode) {
      print('🔍 Forcing connectivity check...');
    }
    await _checkConnectivity();
  }
  
  /// Dispose of resources
  @override
  void dispose() {
    _networkCheckTimer?.cancel();
    _connectivitySubscription?.cancel();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ NetworkDetectionService disposed');
    }
    
    super.dispose();
  }
}

/// Extension to get network status as string
extension NetworkStatusExtension on NetworkDetectionService {
  String get statusString => isOnline ? 'Online' : 'Offline';
  
  String get statusEmoji => isOnline ? '🟢' : '🔴';
}

/// Configuration System Initializer
/// 
/// Provides easy initialization and setup for the flexible configuration system
/// with sensible defaults and environment-specific configurations.

import 'package:flutter/foundation.dart';
import 'configuration_manager.dart';
import 'config_parameters_registry.dart';

/// Configuration initializer with preset configurations
class ConfigInitializer {
  static bool _isInitialized = false;
  
  /// Initialize configuration system with default settings
  static Future<void> initialize({
    bool enableFileConfig = true,
    bool enableEnvironmentConfig = true,
    bool enableRemoteConfig = false,
    String? remoteServerUrl,
    String? deviceId,
    String? authToken,
    bool enableDebugLogging = kDebugMode,
  }) async {
    if (_isInitialized) {
      debugPrint('Configuration system already initialized');
      return;
    }

    try {
      if (enableDebugLogging) {
        debugPrint('Initializing flexible configuration system...');
      }

      // Initialize the configuration manager
      await ConfigurationManager.instance.initialize(
        enableFileConfig: enableFileConfig,
        enableEnvironmentConfig: enableEnvironmentConfig,
        enableRemoteConfig: enableRemoteConfig,
        remoteServerUrl: remoteServerUrl,
        deviceId: deviceId ?? _getDefaultDeviceId(),
        authToken: authToken,
      );

      _isInitialized = true;
      
      if (enableDebugLogging) {
        debugPrint('Configuration system initialized successfully');
        _logConfigurationSummary();
      }

    } catch (e) {
      debugPrint('Failed to initialize configuration system: $e');
      rethrow;
    }
  }

  /// Initialize for mobile app
  static Future<void> initializeForMobile({
    String? deviceId,
    bool enableRemoteConfig = false,
    String? remoteServerUrl,
    String? authToken,
  }) async {
    await initialize(
      enableFileConfig: true,
      enableEnvironmentConfig: false, // Usually not needed for mobile
      enableRemoteConfig: enableRemoteConfig,
      remoteServerUrl: remoteServerUrl,
      deviceId: deviceId ?? 'mobile_${DateTime.now().millisecondsSinceEpoch}',
      authToken: authToken,
      enableDebugLogging: kDebugMode,
    );
  }

  /// Initialize for terminal app
  static Future<void> initializeForTerminal({
    String? deviceId,
    bool enableRemoteConfig = true,
    String? remoteServerUrl,
    String? authToken,
  }) async {
    await initialize(
      enableFileConfig: true,
      enableEnvironmentConfig: true, // Important for terminal deployments
      enableRemoteConfig: enableRemoteConfig,
      remoteServerUrl: remoteServerUrl ?? _getDefaultRemoteUrl(),
      deviceId: deviceId ?? 'terminal_001',
      authToken: authToken,
      enableDebugLogging: kDebugMode,
    );
  }

  /// Initialize for development
  static Future<void> initializeForDevelopment({
    String? deviceId,
  }) async {
    await initialize(
      enableFileConfig: true,
      enableEnvironmentConfig: true,
      enableRemoteConfig: false, // Usually not needed for development
      deviceId: deviceId ?? 'dev_${DateTime.now().millisecondsSinceEpoch}',
      enableDebugLogging: true,
    );
  }

  /// Initialize for testing
  static Future<void> initializeForTesting({
    Map<String, dynamic>? testConfig,
  }) async {
    await initialize(
      enableFileConfig: false,
      enableEnvironmentConfig: false,
      enableRemoteConfig: false,
      deviceId: 'test_device',
      enableDebugLogging: false,
    );

    // Apply test configuration if provided
    if (testConfig != null) {
      await ConfigurationManager.instance.importConfiguration(testConfig);
    }
  }

  /// Check if configuration system is initialized
  static bool get isInitialized => _isInitialized;

  /// Reload configuration from all sources
  static Future<void> reload() async {
    if (!_isInitialized) {
      throw StateError('Configuration system not initialized');
    }
    
    await ConfigurationManager.instance.reload();
    debugPrint('Configuration reloaded from all sources');
  }

  /// Validate current configuration
  static List<String> validate() {
    if (!_isInitialized) {
      return ['Configuration system not initialized'];
    }
    
    return ConfigurationManager.instance.validateConfiguration();
  }

  /// Export current configuration
  static Map<String, dynamic> exportConfiguration({
    bool includeSecure = false,
  }) {
    if (!_isInitialized) {
      throw StateError('Configuration system not initialized');
    }
    
    return ConfigurationManager.instance.exportConfiguration(
      includeSecure: includeSecure,
    );
  }

  /// Import configuration
  static Future<void> importConfiguration(Map<String, dynamic> config) async {
    if (!_isInitialized) {
      throw StateError('Configuration system not initialized');
    }
    
    await ConfigurationManager.instance.importConfiguration(config);
  }

  /// Reset to default configuration
  static Future<void> resetToDefaults() async {
    if (!_isInitialized) {
      throw StateError('Configuration system not initialized');
    }

    // Get all parameters and their default values
    final parameters = ConfigParametersRegistry.getAllParameters();
    final defaultConfig = <String, dynamic>{};
    
    for (final parameter in parameters.values) {
      defaultConfig[parameter.key] = parameter.defaultValue;
    }
    
    await ConfigurationManager.instance.importConfiguration(defaultConfig);
    debugPrint('Configuration reset to defaults');
  }

  /// Get configuration statistics
  static Map<String, dynamic> getStatistics() {
    if (!_isInitialized) {
      return {'initialized': false};
    }

    final manager = ConfigurationManager.instance;
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    // Count parameters by category
    final categoryCounts = <String, int>{};
    for (final parameter in parameters.values) {
      categoryCounts[parameter.category] = 
          (categoryCounts[parameter.category] ?? 0) + 1;
    }

    return {
      'initialized': true,
      'total_parameters': parameters.length,
      'active_providers': manager.activeProviders.length,
      'provider_types': manager.activeProviders.map((p) => p.name).toList(),
      'categories': categoryCounts,
      'validation_errors': manager.validateConfiguration().length,
    };
  }

  /// Dispose configuration system
  static Future<void> dispose() async {
    if (!_isInitialized) return;
    
    await ConfigurationManager.instance.dispose();
    _isInitialized = false;
    debugPrint('Configuration system disposed');
  }

  /// Get default device ID based on platform
  static String _getDefaultDeviceId() {
    if (kIsWeb) {
      return 'web_${DateTime.now().millisecondsSinceEpoch}';
    } else {
      return 'device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Get default remote server URL
  static String _getDefaultRemoteUrl() {
    if (kDebugMode) {
      return 'http://localhost:3000';
    } else {
      return 'https://api.ccam.com';
    }
  }

  /// Log configuration summary
  static void _logConfigurationSummary() {
    try {
      final stats = getStatistics();
      debugPrint('Configuration Summary:');
      debugPrint('  - Total parameters: ${stats['total_parameters']}');
      debugPrint('  - Active providers: ${stats['active_providers']}');
      debugPrint('  - Provider types: ${stats['provider_types']}');
      debugPrint('  - Categories: ${stats['categories']}');
      
      final validationErrors = stats['validation_errors'] as int;
      if (validationErrors > 0) {
        debugPrint('  - Validation errors: $validationErrors');
      }
    } catch (e) {
      debugPrint('Error logging configuration summary: $e');
    }
  }

  /// Create sample configuration for testing
  static Map<String, dynamic> createSampleConfiguration() {
    return {
      // Face Detection
      'face_detection.min_quality_detection': 0.5,
      'face_detection.min_quality_recognition': 0.6,
      'face_detection.recognition_throttle_duration': 5000,
      'face_detection.frame_skip_count': 2,
      
      // Network
      'network.base_api_url': 'https://test-api.ccam.com/api/v3.1',
      'network.request_timeout': 15000,
      'network.max_retry_attempts': 2,
      
      // UI
      'ui.primary_color': 0xFF1976D2,
      'ui.success_color': 0xFF388E3C,
      'ui.avatar_size': 100.0,
      'ui.animation_duration': 250,
      
      // Performance
      'performance.normal_frame_rate': 25,
      'performance.optimized_frame_rate': 12,
      'performance.power_saving_delay': 20000,
      
      // Camera
      'camera.default_resolution': 'high',
      'camera.max_face_captures': 3,
      
      // Debug
      'debug.enable_face_detection_logs': true,
      'debug.enable_performance_monitoring': true,
    };
  }

  /// Validate configuration against schema
  static List<String> validateConfigurationSchema(Map<String, dynamic> config) {
    final errors = <String>[];
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final entry in config.entries) {
      final parameter = parameters[entry.key];
      
      if (parameter == null) {
        errors.add('Unknown configuration key: ${entry.key}');
        continue;
      }
      
      try {
        final parsedValue = parameter.parseValue(entry.value);
        if (!parameter.isValid(parsedValue)) {
          errors.add('Invalid value for ${entry.key}: ${entry.value}');
        }
      } catch (e) {
        errors.add('Error parsing ${entry.key}: $e');
      }
    }
    
    return errors;
  }
}

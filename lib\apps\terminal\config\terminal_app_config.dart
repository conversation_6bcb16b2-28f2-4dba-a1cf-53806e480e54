import '../../../shared/core/config/base_app_config.dart';

/// Terminal Application Configuration
/// 
/// This class provides terminal-specific configuration settings
/// while inheriting shared configuration from BaseAppConfig.
/// It includes kiosk mode, hardware integration, and terminal-specific behaviors.
class TerminalAppConfig extends BaseAppConfig {
  // Singleton pattern
  static TerminalAppConfig? _instance;
  TerminalAppConfig._internal();
  
  factory TerminalAppConfig() {
    _instance ??= TerminalAppConfig._internal();
    return _instance!;
  }

  // ============================================================================
  // APP IDENTIFICATION
  // ============================================================================
  
  @override
  AppType get appType => AppType.terminal;
  
  @override
  String get appName => 'C-Face Terminal';
  
  @override
  String get appVersion => '1.0.0';
  
  @override
  String get buildNumber => '1';

  // ============================================================================
  // TERMINAL-SPECIFIC UI CONFIGURATION
  // ============================================================================
  
  @override
  TerminalUIConfig get uiConfig => TerminalUIConfig();

  // ============================================================================
  // TERMINAL-SPECIFIC FEATURE FLAGS
  // ============================================================================
  
  @override
  TerminalFeatureFlags get featureFlags => TerminalFeatureFlags();

  // ============================================================================
  // TERMINAL-SPECIFIC CAMERA CONFIGURATION
  // ============================================================================
  
  @override
  TerminalCameraConfig get cameraConfig => TerminalCameraConfig();

  // ============================================================================
  // KIOSK MODE CONFIGURATION
  // ============================================================================
  
  /// Enable kiosk mode
  bool get enableKioskMode => !isDevelopment;
  
  /// Kiosk mode timeout (return to home screen)
  Duration get kioskTimeout => const Duration(minutes: 2);
  
  /// Enable auto-restart
  bool get enableAutoRestart => isProduction;
  
  /// Auto-restart interval
  Duration get autoRestartInterval => const Duration(hours: 24);
  
  /// Enable screensaver
  bool get enableScreensaver => true;
  
  /// Screensaver timeout
  Duration get screensaverTimeout => const Duration(minutes: 5);
  
  /// Disable system UI (status bar, navigation bar)
  bool get disableSystemUI => enableKioskMode;
  
  /// Prevent app switching
  bool get preventAppSwitching => enableKioskMode;
  
  /// Enable admin mode access
  bool get enableAdminMode => true;
  
  /// Admin mode access code
  String get adminAccessCode => 'ADMIN2024';

  // ============================================================================
  // HARDWARE CONFIGURATION
  // ============================================================================
  
  /// Enable hardware monitoring
  bool get enableHardwareMonitoring => true;
  
  /// Hardware check interval
  Duration get hardwareCheckInterval => const Duration(minutes: 5);
  
  /// Enable temperature monitoring
  bool get enableTemperatureMonitoring => true;
  
  /// Temperature warning threshold (Celsius)
  double get temperatureWarningThreshold => 70.0;
  
  /// Temperature critical threshold (Celsius)
  double get temperatureCriticalThreshold => 85.0;
  
  /// Enable memory monitoring
  bool get enableMemoryMonitoring => true;
  
  /// Memory warning threshold (percentage)
  double get memoryWarningThreshold => 80.0;
  
  /// Memory critical threshold (percentage)
  double get memoryCriticalThreshold => 95.0;
  
  /// Enable storage monitoring
  bool get enableStorageMonitoring => true;
  
  /// Storage warning threshold (percentage)
  double get storageWarningThreshold => 85.0;
  
  /// Storage critical threshold (percentage)
  double get storageCriticalThreshold => 95.0;

  // ============================================================================
  // DISPLAY CONFIGURATION
  // ============================================================================
  
  /// Force fullscreen mode
  bool get forceFullscreen => enableKioskMode;
  
  /// Display brightness (0.0 to 1.0)
  double get displayBrightness => 0.8;
  
  /// Enable auto-brightness
  bool get enableAutoBrightness => false;
  
  /// Display orientation lock
  TerminalOrientation get orientationLock => TerminalOrientation.landscape;
  
  /// Enable display sleep prevention
  bool get preventDisplaySleep => true;
  
  /// Display resolution preference
  DisplayResolution get preferredResolution => DisplayResolution.fullHD;

  // ============================================================================
  // NETWORK CONFIGURATION
  // ============================================================================
  
  /// Enable ethernet preference over WiFi
  bool get preferEthernet => true;
  
  /// Network connectivity check interval
  Duration get networkCheckInterval => const Duration(minutes: 1);
  
  /// Enable network failover
  bool get enableNetworkFailover => true;
  
  /// Maximum network retry attempts
  int get maxNetworkRetries => 5;
  
  /// Network timeout for terminal operations
  Duration get terminalNetworkTimeout => const Duration(seconds: 10);

  // ============================================================================
  // SECURITY CONFIGURATION
  // ============================================================================
  
  /// Enable physical security features
  bool get enablePhysicalSecurity => isProduction;
  
  /// Enable tamper detection
  bool get enableTamperDetection => isProduction;
  
  /// Enable secure boot verification
  bool get enableSecureBoot => isProduction;
  
  /// Enable encrypted storage
  bool get enableEncryptedStorage => isProduction;
  
  /// Security audit log retention (days)
  int get securityLogRetentionDays => 30;

  // ============================================================================
  // MAINTENANCE CONFIGURATION
  // ============================================================================
  
  /// Enable automatic updates
  bool get enableAutoUpdates => isProduction;
  
  /// Update check interval
  Duration get updateCheckInterval => const Duration(hours: 6);
  
  /// Enable remote diagnostics
  bool get enableRemoteDiagnostics => true;
  
  /// Enable remote configuration
  bool get enableRemoteConfiguration => true;
  
  /// Log retention period (days)
  int get logRetentionDays => 7;
  
  /// Enable log compression
  bool get enableLogCompression => true;

  // ============================================================================
  // VALIDATION
  // ============================================================================
  
  @override
  bool validateAppSpecificConfig() {
    try {
      // Validate terminal-specific settings
      if (temperatureWarningThreshold >= temperatureCriticalThreshold) {
        return false;
      }
      if (memoryWarningThreshold >= memoryCriticalThreshold) {
        return false;
      }
      if (storageWarningThreshold >= storageCriticalThreshold) {
        return false;
      }
      if (displayBrightness < 0.0 || displayBrightness > 1.0) {
        return false;
      }
      if (maxNetworkRetries <= 0) {
        return false;
      }
      if (logRetentionDays <= 0) {
        return false;
      }
      
      // Validate sub-configurations
      return uiConfig.validate() && 
             featureFlags.validate() && 
             cameraConfig.validate();
    } catch (e) {
      return false;
    }
  }
}

/// Terminal UI Configuration
class TerminalUIConfig implements UIConfig {
  @override
  Duration get defaultAnimationDuration => const Duration(milliseconds: 200); // Faster for terminal
  
  @override
  Duration get pageTransitionDuration => const Duration(milliseconds: 150); // Faster for terminal
  
  @override
  Duration get snackbarDuration => const Duration(seconds: 5); // Longer for terminal
  
  @override
  Duration get loadingIndicatorDelay => const Duration(milliseconds: 200); // Faster for terminal
  
  /// Terminal-specific UI settings
  Duration get idleAnimationDuration => const Duration(seconds: 30);
  Duration get attractModeTimeout => const Duration(minutes: 1);
  
  /// Terminal layout settings
  double get minTouchTargetSize => 60.0; // Larger for terminal
  double get defaultPadding => 24.0; // Larger for terminal
  double get largePadding => 48.0;
  
  /// Terminal text settings
  double get defaultFontSize => 18.0; // Larger for terminal
  double get largeFontSize => 24.0;
  double get extraLargeFontSize => 32.0;
  
  /// Terminal interaction settings
  Duration get buttonPressTimeout => const Duration(seconds: 10);
  Duration get inactivityTimeout => const Duration(minutes: 2);
  
  bool validate() {
    return minTouchTargetSize > 0 && 
           defaultPadding >= 0 && 
           defaultFontSize > 0;
  }
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'defaultAnimationDuration': defaultAnimationDuration.inMilliseconds,
      'pageTransitionDuration': pageTransitionDuration.inMilliseconds,
      'snackbarDuration': snackbarDuration.inSeconds,
      'loadingIndicatorDelay': loadingIndicatorDelay.inMilliseconds,
      'minTouchTargetSize': minTouchTargetSize,
      'defaultPadding': defaultPadding,
      'defaultFontSize': defaultFontSize,
      'inactivityTimeout': inactivityTimeout.inMinutes,
    };
  }
}

/// Terminal Feature Flags
class TerminalFeatureFlags implements FeatureFlags {
  @override
  bool get enableFaceCapture => true;
  
  @override
  bool get enableUserManagement => true;
  
  @override
  bool get enableOfflineMode => true; // Important for terminals
  
  @override
  bool get enableBiometricAuth => false; // Not typically available on terminals
  
  @override
  bool get enablePushNotifications => false; // Not needed for terminals
  
  /// Terminal-specific features
  bool get enableKioskMode => true;
  bool get enableHardwareMonitoring => true;
  bool get enableRemoteManagement => true;
  bool get enableMaintenanceMode => true;
  bool get enableDiagnostics => true;
  bool get enableSecurityAudit => true;
  bool get enablePerformanceMonitoring => true;
  bool get enableAutoRecovery => true;
  bool get enableScheduledTasks => true;
  bool get enableDataSync => true;
  
  bool validate() => true; // All boolean flags are valid by default
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'enableFaceCapture': enableFaceCapture,
      'enableUserManagement': enableUserManagement,
      'enableOfflineMode': enableOfflineMode,
      'enableBiometricAuth': enableBiometricAuth,
      'enablePushNotifications': enablePushNotifications,
      'enableKioskMode': enableKioskMode,
      'enableHardwareMonitoring': enableHardwareMonitoring,
      'enableRemoteManagement': enableRemoteManagement,
      'enableMaintenanceMode': enableMaintenanceMode,
    };
  }
}

/// Terminal Camera Configuration
class TerminalCameraConfig implements CameraConfig {
  @override
  CameraResolution get defaultCameraResolution => CameraResolution.veryHigh; // High quality for terminals
  
  @override
  double get cameraAspectRatio => 16 / 9; // Widescreen for terminals
  
  @override
  Duration get cameraCaptureTimeout => const Duration(seconds: 15); // Longer for terminals
  
  @override
  int get maxFaceCaptures => 3; // Fewer captures needed for terminals
  
  @override
  Duration get cacheFaceDuration => const Duration(minutes: 5); // Longer cache for terminals
  
  /// Terminal-specific camera settings
  bool get enableAutoFocus => true;
  bool get enableImageStabilization => true;
  bool get enableLowLightMode => true;
  bool get enableHDR => true;
  double get exposureCompensation => 0.0;
  int get imageQuality => 95; // High quality for terminals
  bool get enableContinuousCapture => false;
  bool get enableBurstMode => false;
  
  bool validate() {
    return maxFaceCaptures > 0 && 
           imageQuality > 0 && imageQuality <= 100 &&
           cameraAspectRatio > 0;
  }
  
  @override
  Map<String, dynamic> toMap() {
    return {
      'defaultCameraResolution': defaultCameraResolution.name,
      'cameraAspectRatio': cameraAspectRatio,
      'cameraCaptureTimeout': cameraCaptureTimeout.inSeconds,
      'maxFaceCaptures': maxFaceCaptures,
      'enableAutoFocus': enableAutoFocus,
      'enableImageStabilization': enableImageStabilization,
      'enableLowLightMode': enableLowLightMode,
      'imageQuality': imageQuality,
    };
  }
}

/// Terminal-specific enums
enum TerminalOrientation {
  portrait,
  landscape,
  auto,
}

enum DisplayResolution {
  hd,
  fullHD,
  quadHD,
  ultraHD,
}

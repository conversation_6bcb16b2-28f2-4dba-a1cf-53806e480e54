# 🎯 Face Recognition Package

## Overview
Modular face recognition system designed for **dual-app architecture**:
- **Mobile App**: Management, enrollment, and administration
- **Terminal App**: Real-time detection and edge device control

## Architecture Roles

### 📱 **Mobile App Features**
- 👥 **Member Management**: Register new users
- 🎭 **Face Enrollment**: Capture and train face models
- 📊 **Project Management**: Assign users to projects
- ⏰ **Attendance Tracking**: View reports and analytics
- 🔧 **Terminal Configuration**: Setup and manage edge devices

### 🖥️ **Terminal App Features**
- 🚀 **Real-time Detection**: Ultra-fast face recognition
- 🔌 **Side Effect Triggers**: Control lights, doors, relays
- 📡 **Offline Operation**: Work without internet
- 🔄 **Data Sync**: Sync with mobile app/server
- ⚡ **Edge Optimization**: Optimized for Telpo F8, etc.

## Architecture

```
lib/packages/face_recognition/
├── src/
│   ├── core/                    # Shared core components
│   │   ├── interfaces/
│   │   ├── models/
│   │   └── exceptions/
│   ├── mobile/                  # Mobile app specific
│   │   ├── enrollment/          # Face registration & training
│   │   ├── management/          # User & project management
│   │   ├── analytics/           # Reports & attendance
│   │   └── configuration/       # Terminal setup
│   ├── terminal/                # Terminal app specific
│   │   ├── detection/           # Real-time face detection
│   │   ├── recognition/         # Edge recognition
│   │   ├── triggers/            # Side effect controllers
│   │   ├── sync/                # Data synchronization
│   │   └── optimization/        # Device-specific optimizations
│   ├── shared/                  # Shared between apps
│   │   ├── engines/             # Detection engines (UltraFace, etc.)
│   │   ├── network/             # Network services
│   │   ├── storage/             # Local database
│   │   └── utils/               # Common utilities
├── assets/                      # Models and resources
│   ├── models/
│   │   ├── detection/           # Detection models
│   │   └── recognition/         # Recognition models
│   └── configs/
│       ├── mobile/              # Mobile configurations
│       └── terminal/            # Terminal configurations
└── examples/                    # Usage examples
    ├── mobile_example/
    └── terminal_example/
```

## Usage

### Basic Setup
```dart
import 'package:face_recognition/face_recognition.dart';

// Initialize the face recognition system
final faceRecognition = FaceRecognitionSystem();
await faceRecognition.initialize(
  config: FaceRecognitionConfig(
    detectionEngine: DetectionEngine.ultraface,
    recognitionMode: RecognitionMode.hybrid,
    performanceProfile: PerformanceProfile.balanced,
  ),
);
```

### Terminal App Integration
```dart
// Terminal-specific configuration
final terminalConfig = FaceRecognitionConfig.forTerminal(
  deviceType: TerminalDeviceType.telpoF8,
  performanceProfile: PerformanceProfile.maxPerformance,
);

final faceRecognition = FaceRecognitionSystem();
await faceRecognition.initialize(config: terminalConfig);
```

### Mobile App Integration
```dart
// Mobile-specific configuration
final mobileConfig = FaceRecognitionConfig.forMobile(
  deviceType: MobileDeviceType.android,
  performanceProfile: PerformanceProfile.balanced,
);

final faceRecognition = FaceRecognitionSystem();
await faceRecognition.initialize(config: mobileConfig);
```

## Performance Profiles

| Profile | Target FPS | Memory | CPU | Use Case |
|---------|------------|--------|-----|----------|
| **MaxPerformance** | 60+ | High | High | Terminal devices |
| **Balanced** | 30+ | Medium | Medium | Mobile apps |
| **PowerSaver** | 15+ | Low | Low | Battery-constrained |

## Detection Engines

### UltraFace
- **Size**: 1.1MB
- **FPS**: 40-60
- **Accuracy**: 95%+
- **Best for**: Embedded devices

### MediaPipe
- **Size**: 2.5MB
- **FPS**: 30-45
- **Accuracy**: 97%+
- **Best for**: High accuracy needs

### Google ML Kit
- **Size**: Variable
- **FPS**: 15-25
- **Accuracy**: 90%+
- **Best for**: Quick prototyping

## Configuration Examples

### Terminal Configuration
```dart
final config = FaceRecognitionConfig(
  // Detection settings
  detectionEngine: DetectionEngine.ultraface,
  minFaceSize: 40,
  maxFaces: 5,
  
  // Recognition settings
  recognitionMode: RecognitionMode.hybrid,
  onlineEndpoint: 'https://api.terminal.com/face/recognize',
  
  // Performance settings
  targetFPS: 45,
  processingThreads: 2,
  memoryLimit: 150, // MB
  
  // Terminal-specific
  deviceOptimizations: {
    'telpo_f8': TelpoF8Optimizations(),
    'android_terminal': AndroidTerminalOptimizations(),
  },
);
```

### Mobile Configuration
```dart
final config = FaceRecognitionConfig(
  // Detection settings
  detectionEngine: DetectionEngine.mediapipe,
  minFaceSize: 30,
  maxFaces: 3,
  
  // Recognition settings
  recognitionMode: RecognitionMode.online,
  onlineEndpoint: 'https://api.mobile.com/face/recognize',
  
  // Performance settings
  targetFPS: 30,
  processingThreads: 1,
  memoryLimit: 100, // MB
  
  // Mobile-specific
  batteryOptimization: true,
  backgroundProcessing: false,
);
```

## Dependencies

```yaml
dependencies:
  camera: ^0.10.5
  tflite_flutter: ^0.10.4
  connectivity_plus: ^4.0.2
  sqflite: ^2.3.0
  http: ^1.1.0
  image: ^4.0.17
```

## Platform Support

| Platform | Terminal App | Mobile App | Status |
|----------|--------------|------------|--------|
| **Android** | ✅ | ✅ | Supported |
| **iOS** | ❌ | ✅ | Mobile only |
| **Windows** | ⚠️ | ❌ | Limited |
| **Linux** | ⚠️ | ❌ | Limited |

## Performance Benchmarks

### Telpo F8 (Android 11)
- **UltraFace**: 45 FPS, 60MB RAM
- **MediaPipe**: 35 FPS, 80MB RAM
- **ML Kit**: 20 FPS, 120MB RAM

### Mobile Devices
- **High-end**: 60+ FPS
- **Mid-range**: 30+ FPS
- **Low-end**: 15+ FPS

## License
MIT License - See LICENSE file for details

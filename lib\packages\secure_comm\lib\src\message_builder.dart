import 'models/secure_message.dart';
import 'crypto_utils.dart';

/// Message builder for creating and signing secure messages
class MessageBuilder {
  /// Device ID for this builder
  final String deviceId;

  /// Secret key for HMAC signing
  final String secretKey;

  /// Default message priority
  final int defaultPriority;

  /// Whether to encrypt messages by default
  final bool defaultEncryption;

  const MessageBuilder({
    required this.deviceId,
    required this.secretKey,
    this.defaultPriority = 0,
    this.defaultEncryption = false,
  });

  /// Build and sign a secure message
  SecureMessage buildMessage({
    required String type,
    required Map<String, dynamic> payload,
    String? messageId,
    int? priority,
    bool? encrypted,
  }) {
    // Validate inputs
    if (!CryptoUtils.isValidDeviceId(deviceId)) {
      throw ArgumentError('Invalid device ID format: $deviceId');
    }

    if (!CryptoUtils.isValidMessageType(type)) {
      throw ArgumentError('Invalid message type format: $type');
    }

    // Generate message ID if not provided
    final finalMessageId = messageId ?? CryptoUtils.generateMessageId();

    // Create unsigned message
    final message = SecureMessage.create(
      deviceId: deviceId,
      type: type,
      payload: payload,
      messageId: finalMessageId,
      priority: priority ?? defaultPriority,
      encrypted: encrypted ?? defaultEncryption,
    );

    // Sign the message
    final signature = _signMessage(message);

    // Return signed message
    return message.withSignature(signature);
  }

  /// Build a face authentication message
  SecureMessage buildFaceAuthMessage({
    required String faceImageBase64,
    String? userId,
    Map<String, dynamic>? metadata,
  }) {
    final payload = {
      'face_image': faceImageBase64,
      if (userId != null) 'user_id': userId,
      if (metadata != null) ...metadata,
    };

    return buildMessage(
      type: 'face_auth',
      payload: payload,
      priority: 1, // High priority for authentication
    );
  }

  /// Build a relay control message
  SecureMessage buildRelayControlMessage({
    required String action, // 'unlock', 'lock', 'on', 'off'
    String? relayId,
    Map<String, dynamic>? metadata,
  }) {
    final payload = {
      'action': action,
      if (relayId != null) 'relay_id': relayId,
      if (metadata != null) ...metadata,
    };

    return buildMessage(
      type: 'relay_control',
      payload: payload,
    );
  }

  /// Build a status request message
  SecureMessage buildStatusMessage({
    String? component,
    Map<String, dynamic>? filters,
  }) {
    final payload = {
      if (component != null) 'component': component,
      if (filters != null) 'filters': filters,
    };

    return buildMessage(
      type: 'status',
      payload: payload,
    );
  }

  /// Build a log message
  SecureMessage buildLogMessage({
    required String level, // 'info', 'warning', 'error', 'debug'
    required String message,
    String? category,
    Map<String, dynamic>? context,
  }) {
    final payload = {
      'level': level,
      'message': message,
      if (category != null) 'category': category,
      if (context != null) 'context': context,
    };

    return buildMessage(
      type: 'log',
      payload: payload,
      priority: level == 'error' ? 2 : 0,
    );
  }

  /// Build an image upload message
  SecureMessage buildImageUploadMessage({
    required String imageBase64,
    required String imageType, // 'face', 'document', 'scene'
    String? filename,
    Map<String, dynamic>? metadata,
  }) {
    final payload = {
      'image_data': imageBase64,
      'image_type': imageType,
      if (filename != null) 'filename': filename,
      if (metadata != null) ...metadata,
    };

    return buildMessage(
      type: 'image_upload',
      payload: payload,
      priority: 1,
    );
  }

  /// Build a configuration update message
  SecureMessage buildConfigUpdateMessage({
    required Map<String, dynamic> config,
    String? configVersion,
  }) {
    final payload = {
      'config': config,
      if (configVersion != null) 'config_version': configVersion,
    };

    return buildMessage(
      type: 'config_update',
      payload: payload,
    );
  }

  /// Build a heartbeat/ping message
  SecureMessage buildHeartbeatMessage({
    Map<String, dynamic>? systemInfo,
  }) {
    final payload = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      if (systemInfo != null) 'system_info': systemInfo,
    };

    return buildMessage(
      type: 'heartbeat',
      payload: payload,
    );
  }

  /// Build a custom message with arbitrary payload
  SecureMessage buildCustomMessage({
    required String type,
    required Map<String, dynamic> payload,
    int? priority,
    bool? encrypted,
  }) {
    return buildMessage(
      type: type,
      payload: payload,
      priority: priority,
      encrypted: encrypted,
    );
  }

  /// Verify a received message signature
  bool verifyMessage(SecureMessage message) {
    if (message.signature == null) return false;

    return CryptoUtils.verifyHmacSignature(
      secretKey: secretKey,
      signature: message.signature!,
      message: message.getCanonicalString(),
    );
  }

  /// Sign a message with HMAC
  String _signMessage(SecureMessage message) {
    final canonicalString = message.getCanonicalString();
    return CryptoUtils.createHmacSignature(
      secretKey: secretKey,
      message: canonicalString,
    );
  }

  /// Create a new builder with different device ID
  MessageBuilder withDeviceId(String newDeviceId) {
    return MessageBuilder(
      deviceId: newDeviceId,
      secretKey: secretKey,
      defaultPriority: defaultPriority,
      defaultEncryption: defaultEncryption,
    );
  }

  /// Create a new builder with different secret key
  MessageBuilder withSecretKey(String newSecretKey) {
    return MessageBuilder(
      deviceId: deviceId,
      secretKey: newSecretKey,
      defaultPriority: defaultPriority,
      defaultEncryption: defaultEncryption,
    );
  }

  /// Create a new builder with different defaults
  MessageBuilder withDefaults({
    int? priority,
    bool? encryption,
  }) {
    return MessageBuilder(
      deviceId: deviceId,
      secretKey: secretKey,
      defaultPriority: priority ?? defaultPriority,
      defaultEncryption: encryption ?? defaultEncryption,
    );
  }
}

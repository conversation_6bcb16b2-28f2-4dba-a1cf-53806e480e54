#!/bin/bash

echo "🌐 Running C-Face Terminal App on Web..."
echo "========================================"

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Run terminal app on web
echo "🏃 Starting terminal app on web..."
echo "🖥️  Target: lib/apps/terminal/main_terminal.dart"
echo "🌐 Platform: Chrome Web Browser"
echo "🔧 Mode: Debug (hot reload enabled)"
echo "🎯 Optimized for: Kiosk/Terminal devices"
echo ""
echo "Press 'r' to hot reload, 'R' to hot restart, 'q' to quit"
echo ""

flutter run --target lib/apps/terminal/main_terminal.dart -d chrome --web-port 8081 --flavor terminal

echo ""
echo "🌐 Terminal app web session ended."

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../shared/services/relay_management_service.dart';
import '../../../shared/core/config/configuration_manager.dart';
import '../../../shared/core/config/relay_config_parameters.dart';
import '../../../packages/relay_controller/lib/relay_controller.dart';

/// Relay Control Provider for Terminal App
/// 
/// This provider manages relay operations specifically for the terminal application,
/// integrating with the face recognition system and kiosk mode functionality.
class RelayControlProvider extends ChangeNotifier {
  static RelayControlProvider? _instance;
  static RelayControlProvider get instance => _instance ??= RelayControlProvider._();

  RelayControlProvider._();

  final RelayManagementService _relayService = RelayManagementService.instance;
  StreamSubscription<RelayStatusUpdate>? _statusSubscription;
  
  bool _isInitialized = false;
  bool _isEnabled = false;
  String? _lastError;
  DateTime? _lastActivity;
  List<RelayStatus> _relayStatuses = [];
  Map<int, Timer> _relayTimers = {};

  /// Stream for relay events (for UI updates)
  final StreamController<RelayEvent> _eventController = StreamController.broadcast();
  Stream<RelayEvent> get eventStream => _eventController.stream;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled;
  bool get isConnected => _relayService.isConnected;
  String? get lastError => _lastError;
  DateTime? get lastActivity => _lastActivity;
  List<RelayStatus> get relayStatuses => List.unmodifiable(_relayStatuses);
  RelayDeviceConfig? get deviceConfig => _relayService.deviceConfig;

  /// Initialize relay control provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check if relay is enabled in configuration
      _isEnabled = ConfigurationManager.instance.getValue<bool>(
        RelayConfigKeys.relayEnabled,
        defaultValue: false,
      );

      if (!_isEnabled) {
        debugPrint('Relay control is disabled in configuration');
        _isInitialized = true;
        return;
      }

      // Load relay configuration from config system
      final config = _loadRelayConfigFromSettings();
      
      // Initialize relay service
      await _relayService.initialize(
        config: config,
        autoConnect: config.relayCount > 0,
      );

      // Initialize relay statuses
      _initializeRelayStatuses(config.relayCount);

      // Listen to relay status updates
      _statusSubscription = _relayService.statusUpdates.listen(_handleStatusUpdate);

      _isInitialized = true;
      _lastActivity = DateTime.now();
      
      _eventController.add(RelayEvent.initialized());
      
      debugPrint('✅ RelayControlProvider initialized successfully');
      notifyListeners();

    } catch (e) {
      _lastError = e.toString();
      debugPrint('❌ Failed to initialize RelayControlProvider: $e');
      _eventController.add(RelayEvent.error(e.toString()));
      notifyListeners();
      rethrow;
    }
  }

  /// Load relay configuration from app settings
  RelayDeviceConfig _loadRelayConfigFromSettings() {
    final configManager = ConfigurationManager.instance;
    
    return RelayDeviceConfig(
      deviceId: configManager.getValue<String>(RelayConfigKeys.relayDeviceId),
      deviceName: configManager.getValue<String>(RelayConfigKeys.relayDeviceName),
      relayCount: configManager.getValue<int>(RelayConfigKeys.relayCount),
      baudRate: configManager.getValue<int>(RelayConfigKeys.relayBaudRate),
      relayConfigs: _loadIndividualRelayConfigs(),
    );
  }

  /// Load individual relay configurations
  List<RelayConfig> _loadIndividualRelayConfigs() {
    final configManager = ConfigurationManager.instance;
    final relayCount = configManager.getValue<int>(RelayConfigKeys.relayCount);
    
    final configs = <RelayConfig>[];
    for (int i = 0; i < relayCount; i++) {
      configs.add(RelayConfig(
        index: i,
        name: _getRelayName(i),
        isEnabled: _getRelayEnabled(i),
        defaultTimeoutMs: _getRelayDefaultTimeout(i)?.inMilliseconds,
      ));
    }
    
    return configs;
  }

  /// Get relay name from configuration
  String _getRelayName(int index) {
    final configManager = ConfigurationManager.instance;
    switch (index) {
      case 0: return configManager.getValue<String>(RelayConfigKeys.relay0Name);
      case 1: return configManager.getValue<String>(RelayConfigKeys.relay1Name);
      case 2: return configManager.getValue<String>(RelayConfigKeys.relay2Name);
      case 3: return configManager.getValue<String>(RelayConfigKeys.relay3Name);
      default: return 'Relay $index';
    }
  }

  /// Get relay enabled status from configuration
  bool _getRelayEnabled(int index) {
    final configManager = ConfigurationManager.instance;
    switch (index) {
      case 0: return configManager.getValue<bool>(RelayConfigKeys.relay0Enabled);
      case 1: return configManager.getValue<bool>(RelayConfigKeys.relay1Enabled);
      case 2: return configManager.getValue<bool>(RelayConfigKeys.relay2Enabled);
      case 3: return configManager.getValue<bool>(RelayConfigKeys.relay3Enabled);
      default: return false;
    }
  }

  /// Get relay default timeout from configuration
  Duration? _getRelayDefaultTimeout(int index) {
    final configManager = ConfigurationManager.instance;
    switch (index) {
      case 0: return configManager.getValue<Duration>(RelayConfigKeys.relay0DefaultTimeout);
      case 1: return configManager.getValue<Duration>(RelayConfigKeys.relay1DefaultTimeout);
      case 2: return configManager.getValue<Duration>(RelayConfigKeys.relay2DefaultTimeout);
      case 3: return configManager.getValue<Duration>(RelayConfigKeys.relay3DefaultTimeout);
      default: return null;
    }
  }

  /// Initialize relay statuses
  void _initializeRelayStatuses(int relayCount) {
    _relayStatuses = List.generate(relayCount, (index) => RelayStatus(
      index: index,
      name: _getRelayName(index),
      isEnabled: _getRelayEnabled(index),
      state: RelayState.off,
      lastChanged: DateTime.now(),
    ));
  }

  /// Handle relay status updates from service
  void _handleStatusUpdate(RelayStatusUpdate update) {
    _lastActivity = DateTime.now();
    
    switch (update.type) {
      case RelayStatusType.connected:
        _eventController.add(RelayEvent.connected());
        break;
      case RelayStatusType.disconnected:
        _eventController.add(RelayEvent.disconnected());
        break;
      case RelayStatusType.relayAction:
        _updateRelayStatus(update.relayIndex!, update.action!);
        break;
      case RelayStatusType.relayTimedAction:
        _updateRelayStatus(update.relayIndex!, RelayAction.on);
        _scheduleRelayOff(update.relayIndex!, update.durationMs!);
        break;
      case RelayStatusType.allRelaysAction:
        _updateAllRelayStatuses(update.action!);
        break;
      default:
        break;
    }
    
    notifyListeners();
  }

  /// Update individual relay status
  void _updateRelayStatus(int relayIndex, RelayAction action) {
    if (relayIndex >= 0 && relayIndex < _relayStatuses.length) {
      final status = _relayStatuses[relayIndex];
      RelayState newState;
      
      switch (action) {
        case RelayAction.on:
          newState = RelayState.on;
          break;
        case RelayAction.off:
          newState = RelayState.off;
          break;
        case RelayAction.toggle:
          newState = status.state == RelayState.on ? RelayState.off : RelayState.on;
          break;
      }
      
      _relayStatuses[relayIndex] = status.copyWith(
        state: newState,
        lastChanged: DateTime.now(),
      );
      
      _eventController.add(RelayEvent.relayStateChanged(relayIndex, newState));
    }
  }

  /// Update all relay statuses
  void _updateAllRelayStatuses(RelayAction action) {
    final newState = action == RelayAction.on ? RelayState.on : RelayState.off;
    
    for (int i = 0; i < _relayStatuses.length; i++) {
      _relayStatuses[i] = _relayStatuses[i].copyWith(
        state: newState,
        lastChanged: DateTime.now(),
      );
    }
    
    _eventController.add(RelayEvent.allRelaysStateChanged(newState));
  }

  /// Schedule automatic relay turn off
  void _scheduleRelayOff(int relayIndex, int durationMs) {
    // Cancel existing timer for this relay
    _relayTimers[relayIndex]?.cancel();
    
    // Schedule new timer
    _relayTimers[relayIndex] = Timer(Duration(milliseconds: durationMs), () {
      _updateRelayStatus(relayIndex, RelayAction.off);
      _relayTimers.remove(relayIndex);
      notifyListeners();
    });
  }

  /// Control a specific relay
  Future<void> controlRelay(int relayIndex, RelayAction action) async {
    if (!_isInitialized || !_isEnabled) {
      throw StateError('Relay control not initialized or disabled');
    }

    if (relayIndex < 0 || relayIndex >= _relayStatuses.length) {
      throw ArgumentError('Invalid relay index: $relayIndex');
    }

    final relayStatus = _relayStatuses[relayIndex];
    if (!relayStatus.isEnabled) {
      throw StateError('Relay $relayIndex is disabled');
    }

    try {
      await _relayService.controlRelay(relayIndex, action);
      _lastError = null;
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Control relay with timed operation
  Future<void> controlRelayTimed(int relayIndex, {Duration? duration}) async {
    if (!_isInitialized || !_isEnabled) {
      throw StateError('Relay control not initialized or disabled');
    }

    if (relayIndex < 0 || relayIndex >= _relayStatuses.length) {
      throw ArgumentError('Invalid relay index: $relayIndex');
    }

    final relayStatus = _relayStatuses[relayIndex];
    if (!relayStatus.isEnabled) {
      throw StateError('Relay $relayIndex is disabled');
    }

    // Use default timeout if not specified
    duration ??= _getRelayDefaultTimeout(relayIndex) ?? Duration(seconds: 5);
    
    if (duration == Duration.zero) {
      // Permanent on - use regular control
      await controlRelay(relayIndex, RelayAction.on);
      return;
    }

    try {
      await _relayService.controlRelayTimed(relayIndex, duration.inMilliseconds);
      _lastError = null;
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Control all relays
  Future<void> controlAllRelays(RelayAction action) async {
    if (!_isInitialized || !_isEnabled) {
      throw StateError('Relay control not initialized or disabled');
    }

    if (action == RelayAction.toggle) {
      throw ArgumentError('Toggle action not supported for all relays');
    }

    try {
      await _relayService.controlAllRelays(action);
      _lastError = null;
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Trigger door unlock (convenience method for face recognition)
  Future<void> triggerDoorUnlock() async {
    // Use relay 0 for main door by default
    await controlRelayTimed(0);
  }

  /// Trigger access granted indicators (convenience method)
  Future<void> triggerAccessGranted() async {
    // Use relay 2 for indicator light if enabled
    if (_relayStatuses.length > 2 && _relayStatuses[2].isEnabled) {
      await controlRelayTimed(2, duration: Duration(seconds: 2));
    }
  }

  /// Send raw command to relay device
  Future<void> sendRawCommand(String command) async {
    if (!_isInitialized || !_isEnabled) {
      throw StateError('Relay control not initialized or disabled');
    }

    try {
      await _relayService.sendRawCommand(command);
      _lastError = null;
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Get relay status by index
  RelayStatus? getRelayStatus(int index) {
    if (index >= 0 && index < _relayStatuses.length) {
      return _relayStatuses[index];
    }
    return null;
  }

  /// Register device with server
  Future<void> registerWithServer() async {
    if (!_isInitialized || !_isEnabled) {
      throw StateError('Relay control not initialized or disabled');
    }

    final configManager = ConfigurationManager.instance;
    final registrationEnabled = configManager.getValue<bool>(
      RelayConfigKeys.relayRegistrationEnabled,
      defaultValue: false,
    );

    if (!registrationEnabled) {
      debugPrint('Relay server registration is disabled');
      return;
    }

    final serverUrl = configManager.getValue<String>(RelayConfigKeys.relayServerUrl);
    final authToken = configManager.getValue<String>(RelayConfigKeys.relayServerAuthToken);

    if (serverUrl.isEmpty) {
      throw StateError('Server URL not configured');
    }

    try {
      await _relayService.registerWithServer(
        serverUrl: serverUrl,
        authToken: authToken.isNotEmpty ? authToken : null,
      );
      _lastError = null;
      _eventController.add(RelayEvent.serverRegistered());
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Reconnect to relay device
  Future<void> reconnect() async {
    if (!_isEnabled) {
      throw StateError('Relay control is disabled');
    }

    try {
      await _relayService.disconnect();
      
      final config = _loadRelayConfigFromSettings();
      await _relayService.initialize(
        config: config,
        autoConnect: true,
      );
      
      _lastError = null;
      _eventController.add(RelayEvent.reconnected());
    } catch (e) {
      _lastError = e.toString();
      _eventController.add(RelayEvent.error(e.toString()));
      rethrow;
    }
  }

  /// Dispose provider
  @override
  void dispose() {
    _statusSubscription?.cancel();
    _eventController.close();
    
    // Cancel all relay timers
    for (final timer in _relayTimers.values) {
      timer.cancel();
    }
    _relayTimers.clear();
    
    super.dispose();
  }
}

/// Relay status model
class RelayStatus {
  final int index;
  final String name;
  final bool isEnabled;
  final RelayState state;
  final DateTime lastChanged;

  const RelayStatus({
    required this.index,
    required this.name,
    required this.isEnabled,
    required this.state,
    required this.lastChanged,
  });

  RelayStatus copyWith({
    int? index,
    String? name,
    bool? isEnabled,
    RelayState? state,
    DateTime? lastChanged,
  }) {
    return RelayStatus(
      index: index ?? this.index,
      name: name ?? this.name,
      isEnabled: isEnabled ?? this.isEnabled,
      state: state ?? this.state,
      lastChanged: lastChanged ?? this.lastChanged,
    );
  }

  @override
  String toString() => 'RelayStatus(index: $index, name: $name, state: $state)';
}

/// Relay state enumeration
enum RelayState {
  off,
  on,
  unknown,
}

/// Relay event for UI updates
class RelayEvent {
  final RelayEventType type;
  final String? message;
  final int? relayIndex;
  final RelayState? relayState;

  const RelayEvent({
    required this.type,
    this.message,
    this.relayIndex,
    this.relayState,
  });

  factory RelayEvent.initialized() => const RelayEvent(type: RelayEventType.initialized);
  factory RelayEvent.connected() => const RelayEvent(type: RelayEventType.connected);
  factory RelayEvent.disconnected() => const RelayEvent(type: RelayEventType.disconnected);
  factory RelayEvent.reconnected() => const RelayEvent(type: RelayEventType.reconnected);
  factory RelayEvent.serverRegistered() => const RelayEvent(type: RelayEventType.serverRegistered);
  factory RelayEvent.error(String message) => RelayEvent(type: RelayEventType.error, message: message);
  factory RelayEvent.relayStateChanged(int index, RelayState state) => RelayEvent(
    type: RelayEventType.relayStateChanged,
    relayIndex: index,
    relayState: state,
  );
  factory RelayEvent.allRelaysStateChanged(RelayState state) => RelayEvent(
    type: RelayEventType.allRelaysStateChanged,
    relayState: state,
  );
}

/// Relay event types
enum RelayEventType {
  initialized,
  connected,
  disconnected,
  reconnected,
  serverRegistered,
  error,
  relayStateChanged,
  allRelaysStateChanged,
} 
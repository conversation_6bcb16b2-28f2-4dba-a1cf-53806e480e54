import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';

import '../../../packages/face_recognition/src/config/face_detection_config.dart';
import '../../../packages/face_recognition/src/providers/unified_face_detection_provider.dart';
import '../../../packages/face_recognition/src/detection/detection_engine.dart';
import '../config/face_detection_terminal_config.dart';

/// Terminal-specific face detection provider
/// Manages face detection for terminal applications with MediaPipe as default
class TerminalFaceDetectionProvider extends ChangeNotifier {
  UnifiedFaceDetectionProvider? _detectionProvider;
  FaceDetectionConfig _config;
  
  bool _isInitialized = false;
  bool _isDetecting = false;
  String? _lastError;
  
  // Detection results
  List<DetectedFace> _lastDetectedFaces = [];
  DateTime _lastDetectionTime = DateTime.now();
  
  // Performance metrics
  double _averageFPS = 0.0;
  int _totalDetections = 0;
  int _successfulDetections = 0;
  
  // Configuration options
  bool _useMediaPipe = true;
  bool _enableLandmarks = true;
  bool _enableFallback = true;
  double _confidenceThreshold = 0.6;
  int _maxFaces = 3;
  
  TerminalFaceDetectionProvider({
    FaceDetectionConfig? config,
    bool? useMediaPipe,
    bool? enableLandmarks,
    bool? enableFallback,
    double? confidenceThreshold,
    int? maxFaces,
  }) : _config = config ?? FaceDetectionTerminalConfig.getTerminalConfig(
         useMediaPipe: useMediaPipe ?? true, // Default to MediaPipe (REAL ML KIT IMPLEMENTATION)
         enableLandmarks: enableLandmarks ?? true,
         enableFallback: enableFallback ?? true,
         confidenceThreshold: confidenceThreshold ?? 0.6,
         maxFaces: maxFaces ?? 3,
       ) {
    _useMediaPipe = useMediaPipe ?? true; // Default to MediaPipe (REAL ML KIT IMPLEMENTATION)
    _enableLandmarks = enableLandmarks ?? true;
    _enableFallback = enableFallback ?? true;
    _confidenceThreshold = confidenceThreshold ?? 0.6;
    _maxFaces = maxFaces ?? 3;
  }
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  String? get lastError => _lastError;
  List<DetectedFace> get lastDetectedFaces => _lastDetectedFaces;
  DateTime get lastDetectionTime => _lastDetectionTime;
  double get averageFPS => _averageFPS;
  int get totalDetections => _totalDetections;
  int get successfulDetections => _successfulDetections;
  double get successRate => _totalDetections > 0 ? _successfulDetections / _totalDetections : 0.0;
  
  // Configuration getters
  bool get useMediaPipe => _useMediaPipe;
  bool get enableLandmarks => _enableLandmarks;
  bool get enableFallback => _enableFallback;
  double get confidenceThreshold => _confidenceThreshold;
  int get maxFaces => _maxFaces;
  FaceDetectionConfig get config => _config;
  FaceDetectionEngineType get currentEngine => _config.primaryEngine;
  
  /// Initialize the face detection provider
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing TerminalFaceDetectionProvider...');
        print('📋 Config: ${FaceDetectionTerminalConfig.getConfigDebugInfo(_config)}');
      }
      
      _detectionProvider = UnifiedFaceDetectionProvider(config: _config);
      await _detectionProvider!.initialize();
      
      _isInitialized = true;
      _lastError = null;
      
      if (kDebugMode) {
        print('✅ TerminalFaceDetectionProvider initialized successfully');
        print('🔧 Engine: ${_config.primaryEngine.displayName}');
        print('🔄 Fallback: ${_config.fallbackEngine?.displayName ?? 'None'}');
      }
      
      notifyListeners();
      
    } catch (e) {
      _lastError = 'Failed to initialize face detection: $e';
      
      if (kDebugMode) {
        print('❌ TerminalFaceDetectionProvider initialization failed: $e');
      }
      
      notifyListeners();
      rethrow;
    }
  }
  
  /// Detect faces from camera image
  Future<List<DetectedFace>> detectFaces(CameraImage image) async {
    if (!_isInitialized || _detectionProvider == null) {
      throw Exception('Face detection provider not initialized');
    }
    
    if (_isDetecting) {
      // Return last results if already detecting to avoid blocking
      return _lastDetectedFaces;
    }
    
    _isDetecting = true;
    _totalDetections++;
    
    try {
      final faces = await _detectionProvider!.detectFaces(image);
      
      _lastDetectedFaces = faces;
      _lastDetectionTime = DateTime.now();
      _successfulDetections++;
      _lastError = null;
      
      // Update performance metrics
      _updatePerformanceMetrics();
      
      if (kDebugMode && faces.isNotEmpty) {
        print('👥 Detected ${faces.length} faces with ${_config.primaryEngine.displayName}');
      }
      
      notifyListeners();
      return faces;
      
    } catch (e) {
      _lastError = 'Face detection failed: $e';
      
      if (kDebugMode) {
        print('⚠️ Face detection failed: $e');
      }
      
      notifyListeners();
      return [];
      
    } finally {
      _isDetecting = false;
    }
  }
  
  /// Detect faces from image bytes
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (!_isInitialized || _detectionProvider == null) {
      throw Exception('Face detection provider not initialized');
    }
    
    if (_isDetecting) {
      return _lastDetectedFaces;
    }
    
    _isDetecting = true;
    _totalDetections++;
    
    try {
      final faces = await _detectionProvider!.detectFacesFromBytes(bytes, width, height);
      
      _lastDetectedFaces = faces;
      _lastDetectionTime = DateTime.now();
      _successfulDetections++;
      _lastError = null;
      
      _updatePerformanceMetrics();
      
      notifyListeners();
      return faces;
      
    } catch (e) {
      _lastError = 'Face detection from bytes failed: $e';
      
      if (kDebugMode) {
        print('⚠️ Face detection from bytes failed: $e');
      }
      
      notifyListeners();
      return [];
      
    } finally {
      _isDetecting = false;
    }
  }
  
  /// Switch to MediaPipe engine
  Future<void> switchToMediaPipe() async {
    if (_config.primaryEngine == FaceDetectionEngineType.mediaPipe) return;
    
    await _switchEngine(FaceDetectionEngineType.mediaPipe);
    _useMediaPipe = true;
    
    if (kDebugMode) {
      print('🔄 Switched to MediaPipe engine');
    }
  }
  
  /// Switch to ML Kit engine
  Future<void> switchToMLKit() async {
    if (_config.primaryEngine == FaceDetectionEngineType.mlKit) return;
    
    await _switchEngine(FaceDetectionEngineType.mlKit);
    _useMediaPipe = false;
    
    if (kDebugMode) {
      print('🔄 Switched to ML Kit engine');
    }
  }
  
  /// Switch to hybrid engine
  Future<void> switchToHybrid() async {
    if (_config.primaryEngine == FaceDetectionEngineType.hybrid) return;
    
    await _switchEngine(FaceDetectionEngineType.hybrid);
    
    if (kDebugMode) {
      print('🔄 Switched to Hybrid engine');
    }
  }
  
  /// Update configuration
  Future<void> updateConfiguration({
    bool? useMediaPipe,
    bool? enableLandmarks,
    bool? enableFallback,
    double? confidenceThreshold,
    int? maxFaces,
    PerformanceMode? performanceMode,
  }) async {
    bool needsReinitialization = false;
    
    // Update local settings
    if (useMediaPipe != null && useMediaPipe != _useMediaPipe) {
      _useMediaPipe = useMediaPipe;
      needsReinitialization = true;
    }
    
    if (enableLandmarks != null) _enableLandmarks = enableLandmarks;
    if (enableFallback != null) _enableFallback = enableFallback;
    if (confidenceThreshold != null) _confidenceThreshold = confidenceThreshold;
    if (maxFaces != null) _maxFaces = maxFaces;
    
    // Create new config
    final newConfig = FaceDetectionTerminalConfig.getTerminalConfig(
      useMediaPipe: _useMediaPipe,
      enableLandmarks: _enableLandmarks,
      enableFallback: _enableFallback,
      confidenceThreshold: _confidenceThreshold,
      maxFaces: _maxFaces,
    );
    
    // Update performance mode if specified
    final finalConfig = performanceMode != null 
        ? newConfig.copyWith(performanceMode: performanceMode)
        : newConfig;
    
    _config = finalConfig;
    
    if (_isInitialized && _detectionProvider != null) {
      if (needsReinitialization) {
        // Reinitialize with new engine
        await dispose();
        await initialize();
      } else {
        // Just update configuration
        await _detectionProvider!.updateConfig(_config);
      }
    }
    
    if (kDebugMode) {
      print('⚙️ Configuration updated: ${FaceDetectionTerminalConfig.getConfigDebugInfo(_config)}');
    }
    
    notifyListeners();
  }
  
  /// Get current engine information
  Map<String, dynamic> getEngineInfo() {
    if (!_isInitialized || _detectionProvider == null) {
      return {'status': 'not_initialized'};
    }
    
    final info = _detectionProvider!.getEngineInfo();
    info.addAll({
      'terminalConfig': FaceDetectionTerminalConfig.getConfigDebugInfo(_config),
      'performance': {
        'averageFPS': _averageFPS,
        'totalDetections': _totalDetections,
        'successfulDetections': _successfulDetections,
        'successRate': successRate,
        'lastDetectionTime': _lastDetectionTime.toIso8601String(),
      },
    });
    
    return info;
  }
  
  /// Reset performance metrics
  void resetMetrics() {
    _totalDetections = 0;
    _successfulDetections = 0;
    _averageFPS = 0.0;
    _lastDetectedFaces.clear();
    _lastError = null;
    
    notifyListeners();
    
    if (kDebugMode) {
      print('📊 Performance metrics reset');
    }
  }
  
  /// Dispose the provider
  @override
  Future<void> dispose() async {
    if (_detectionProvider != null) {
      await _detectionProvider!.dispose();
      _detectionProvider = null;
    }
    
    _isInitialized = false;
    _isDetecting = false;
    
    if (kDebugMode) {
      print('🗑️ TerminalFaceDetectionProvider disposed');
    }
    
    super.dispose();
  }
  
  // Private helper methods
  
  Future<void> _switchEngine(FaceDetectionEngineType newEngine) async {
    if (!_isInitialized || _detectionProvider == null) return;
    
    await _detectionProvider!.switchEngine(newEngine);
    _config = _config.copyWith(primaryEngine: newEngine);
    
    notifyListeners();
  }
  
  void _updatePerformanceMetrics() {
    if (_detectionProvider != null) {
      _averageFPS = _detectionProvider!.currentFPS;
    }
  }
}

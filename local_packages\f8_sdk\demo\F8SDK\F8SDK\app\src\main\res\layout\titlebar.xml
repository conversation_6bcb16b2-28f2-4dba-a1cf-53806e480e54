<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:layout_gravity="center"
    android:padding="5dp"
    android:background="#6871ac" >

    <ImageView
        android:id="@+id/logo"
        android:layout_width="50dp"
        android:layout_height="fill_parent"
        android:layout_alignParentLeft="true"
        android:layout_gravity="center_vertical"
        android:src="@mipmap/sdk" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center|center_vertical|center_horizontal"
        android:gravity="center"
        android:text="DemoSDK"
        android:textColor="#FFFFFF"
        android:textSize="20sp" />

    <TextView
        android:id="@+id/title_tv_version"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_gravity="right|center_vertical"
        android:gravity="center"
        android:text=""
        android:textColor="#005533"
        android:textSize="18sp" />

</RelativeLayout>
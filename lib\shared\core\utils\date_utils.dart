import 'package:intl/intl.dart';

/// Comprehensive date utilities for the application
///
/// Note: This class uses the name DateUtils which may conflict with <PERSON><PERSON><PERSON>'s DateUtils.
/// In a real project, consider using AppDateUtils or similar to avoid conflicts.
class AppDateUtils {
  // Private constructor to prevent instantiation
  AppDateUtils._();

  // Common date formats
  static const String iso8601Format = 'yyyy-MM-ddTHH:mm:ss.SSSZ';
  static const String dateOnlyFormat = 'yyyy-MM-dd';
  static const String timeOnlyFormat = 'HH:mm:ss';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String displayTimeFormat = 'HH:mm';
  static const String monthYearFormat = 'MM/yyyy';
  static const String dayMonthFormat = 'dd/MM';
  static const String fullDateFormat = 'EEEE, dd MMMM yyyy';
  static const String shortDateFormat = 'dd MMM yyyy';

  // Formatters
  static final DateFormat _iso8601Formatter = DateFormat(iso8601Format);
  static final DateFormat _dateOnlyFormatter = DateFormat(dateOnlyFormat);
  static final DateFormat _timeOnlyFormatter = DateFormat(timeOnlyFormat);
  static final DateFormat _displayDateFormatter = DateFormat(displayDateFormat);
  static final DateFormat _displayDateTimeFormatter = DateFormat(displayDateTimeFormat);
  static final DateFormat _displayTimeFormatter = DateFormat(displayTimeFormat);
  static final DateFormat _monthYearFormatter = DateFormat(monthYearFormat);
  static final DateFormat _dayMonthFormatter = DateFormat(dayMonthFormat);
  static final DateFormat _fullDateFormatter = DateFormat(fullDateFormat);
  static final DateFormat _shortDateFormatter = DateFormat(shortDateFormat);

  /// Format date to ISO 8601 string
  static String toIso8601String(DateTime date) {
    return _iso8601Formatter.format(date.toUtc());
  }

  /// Parse ISO 8601 string to DateTime
  static DateTime? fromIso8601String(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Format date for display (dd/MM/yyyy)
  static String formatDisplayDate(DateTime date) {
    return _displayDateFormatter.format(date);
  }

  /// Format date and time for display (dd/MM/yyyy HH:mm)
  static String formatDisplayDateTime(DateTime date) {
    return _displayDateTimeFormatter.format(date);
  }

  /// Format time for display (HH:mm)
  static String formatDisplayTime(DateTime date) {
    return _displayTimeFormatter.format(date);
  }

  /// Format date only (yyyy-MM-dd)
  static String formatDateOnly(DateTime date) {
    return _dateOnlyFormatter.format(date);
  }

  /// Format time only (HH:mm:ss)
  static String formatTimeOnly(DateTime date) {
    return _timeOnlyFormatter.format(date);
  }

  /// Format month and year (MM/yyyy)
  static String formatMonthYear(DateTime date) {
    return _monthYearFormatter.format(date);
  }

  /// Format day and month (dd/MM)
  static String formatDayMonth(DateTime date) {
    return _dayMonthFormatter.format(date);
  }

  /// Format full date (Wednesday, 15 March 2023)
  static String formatFullDate(DateTime date) {
    return _fullDateFormatter.format(date);
  }

  /// Format short date (15 Mar 2023)
  static String formatShortDate(DateTime date) {
    return _shortDateFormatter.format(date);
  }

  /// Parse display date string (dd/MM/yyyy)
  static DateTime? parseDisplayDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return _displayDateFormatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date only string (yyyy-MM-dd)
  static DateTime? parseDateOnly(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return _dateOnlyFormatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Get relative time string (e.g., "2 hours ago", "in 3 days")
  static String getRelativeTime(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.isNegative) {
      // Future date
      final futureDiff = date.difference(now);
      if (futureDiff.inDays > 0) {
        return 'in ${futureDiff.inDays} day${futureDiff.inDays == 1 ? '' : 's'}';
      } else if (futureDiff.inHours > 0) {
        return 'in ${futureDiff.inHours} hour${futureDiff.inHours == 1 ? '' : 's'}';
      } else if (futureDiff.inMinutes > 0) {
        return 'in ${futureDiff.inMinutes} minute${futureDiff.inMinutes == 1 ? '' : 's'}';
      } else {
        return 'in a few seconds';
      }
    } else {
      // Past date
      if (difference.inDays > 0) {
        return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
      } else {
        return 'just now';
      }
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && 
           date.month == tomorrow.month && 
           date.day == tomorrow.day;
  }

  /// Check if date is this week
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Check if date is this month
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// Check if date is this year
  static bool isThisYear(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year;
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of week (Monday)
  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// Get end of week (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime endOfMonth(DateTime date) {
    final nextMonth = date.month == 12 
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return nextMonth.subtract(const Duration(days: 1));
  }

  /// Calculate age from date of birth
  static int calculateAge(DateTime dateOfBirth) {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    
    return age;
  }

  /// Get days in month
  static int getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  /// Check if year is leap year
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  /// Get week number of year
  static int getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final dayOfYear = date.difference(startOfYear).inDays + 1;
    return ((dayOfYear - date.weekday + 10) / 7).floor();
  }

  /// Get quarter of year (1-4)
  static int getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  /// Format duration to human readable string
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h ${duration.inMinutes % 60}m';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  /// Get smart date format based on how recent the date is
  static String getSmartDateFormat(DateTime date) {
    if (isToday(date)) {
      return 'Today ${formatDisplayTime(date)}';
    } else if (isYesterday(date)) {
      return 'Yesterday ${formatDisplayTime(date)}';
    } else if (isTomorrow(date)) {
      return 'Tomorrow ${formatDisplayTime(date)}';
    } else if (isThisWeek(date)) {
      return DateFormat('EEEE HH:mm').format(date);
    } else if (isThisYear(date)) {
      return formatShortDate(date);
    } else {
      return formatDisplayDate(date);
    }
  }
}

import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// Service to convert CameraImage to various formats for face recognition
class CameraImageConverter {
  
  /// Convert CameraImage to JPEG bytes for face recognition
  /// This is optimized for face recognition accuracy and server compatibility
  static Future<Uint8List?> convertToJpegBytes(
    CameraImage cameraImage,
    CameraDescription cameraDescription, {
    int quality = 85,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 Converting CameraImage to JPEG bytes...');
        print('   Format: ${cameraImage.format.group}');
        print('   Size: ${cameraImage.width}x${cameraImage.height}');
      }

      // Convert based on image format
      img.Image? image;
      
      switch (cameraImage.format.group) {
        case ImageFormatGroup.yuv420:
          image = _convertYUV420ToImage(cameraImage);
          break;
        case ImageFormatGroup.nv21:
          image = _convertNV21ToImage(cameraImage);
          break;
        case ImageFormatGroup.bgra8888:
          image = _convertBGRA8888ToImage(cameraImage);
          break;
        default:
          if (kDebugMode) {
            print('❌ Unsupported image format: ${cameraImage.format.group}');
          }
          return null;
      }

      if (image == null) {
        if (kDebugMode) {
          print('❌ Failed to convert CameraImage to Image');
        }
        return null;
      }

      // Apply rotation based on camera orientation
      image = _applyRotation(image, cameraDescription);

      // Encode to JPEG
      final jpegBytes = img.encodeJpg(image, quality: quality);
      
      if (kDebugMode) {
        print('✅ CameraImage converted to JPEG');
        print('   Output size: ${jpegBytes.length} bytes');
        print('   Compression ratio: ${((cameraImage.planes.fold<int>(0, (sum, plane) => sum + plane.bytes.length) - jpegBytes.length) / cameraImage.planes.fold<int>(0, (sum, plane) => sum + plane.bytes.length) * 100).toStringAsFixed(1)}%');
      }

      return Uint8List.fromList(jpegBytes);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting CameraImage to JPEG: $e');
      }
      return null;
    }
  }

  /// Convert YUV420 format to Image
  static img.Image? _convertYUV420ToImage(CameraImage cameraImage) {
    try {
      final int width = cameraImage.width;
      final int height = cameraImage.height;
      
      final yPlane = cameraImage.planes[0];
      final uPlane = cameraImage.planes[1];
      final vPlane = cameraImage.planes[2];
      
      final image = img.Image(width: width, height: height);
      
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int yIndex = y * yPlane.bytesPerRow + x;
          final int uvIndex = (y ~/ 2) * uPlane.bytesPerRow + (x ~/ 2);
          
          if (yIndex < yPlane.bytes.length && 
              uvIndex < uPlane.bytes.length && 
              uvIndex < vPlane.bytes.length) {
            
            final int yValue = yPlane.bytes[yIndex];
            final int uValue = uPlane.bytes[uvIndex];
            final int vValue = vPlane.bytes[uvIndex];
            
            // YUV to RGB conversion
            final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
            final int g = (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128)).round().clamp(0, 255);
            final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
            
            image.setPixelRgb(x, y, r, g, b);
          }
        }
      }
      
      return image;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting YUV420: $e');
      }
      return null;
    }
  }

  /// Convert NV21 format to Image
  static img.Image? _convertNV21ToImage(CameraImage cameraImage) {
    try {
      final int width = cameraImage.width;
      final int height = cameraImage.height;
      
      final Uint8List bytes = cameraImage.planes[0].bytes;
      final image = img.Image(width: width, height: height);
      
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int yIndex = y * width + x;
          final int uvIndex = width * height + (y ~/ 2) * width + (x & ~1);
          
          if (yIndex < bytes.length && uvIndex + 1 < bytes.length) {
            final int yValue = bytes[yIndex];
            final int vValue = bytes[uvIndex];
            final int uValue = bytes[uvIndex + 1];
            
            // YUV to RGB conversion
            final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
            final int g = (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128)).round().clamp(0, 255);
            final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
            
            image.setPixelRgb(x, y, r, g, b);
          }
        }
      }
      
      return image;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting NV21: $e');
      }
      return null;
    }
  }

  /// Convert BGRA8888 format to Image
  static img.Image? _convertBGRA8888ToImage(CameraImage cameraImage) {
    try {
      final int width = cameraImage.width;
      final int height = cameraImage.height;
      final Uint8List bytes = cameraImage.planes[0].bytes;
      
      final image = img.Image(width: width, height: height);
      
      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int index = (y * width + x) * 4;
          
          if (index + 3 < bytes.length) {
            final int b = bytes[index];
            final int g = bytes[index + 1];
            final int r = bytes[index + 2];
            // Alpha channel at index + 3 is ignored for JPEG
            
            image.setPixelRgb(x, y, r, g, b);
          }
        }
      }
      
      return image;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting BGRA8888: $e');
      }
      return null;
    }
  }

  /// Apply rotation based on camera description
  static img.Image _applyRotation(img.Image image, CameraDescription camera) {
    // Apply rotation based on sensor orientation first
    switch (camera.sensorOrientation) {
      case 90:
        image = img.copyRotate(image, angle: 90);
        break;
      case 180:
        image = img.copyRotate(image, angle: 180);
        break;
      case 270:
        image = img.copyRotate(image, angle: 270);
        break;
    }

    // Front camera needs horizontal flip to match preview
    if (camera.lensDirection == CameraLensDirection.front) {
      image = img.flipHorizontal(image);
    }

    return image;
  }

  /// Get estimated file size for quality planning
  static int estimateJpegSize(CameraImage cameraImage, int quality) {
    final int pixelCount = cameraImage.width * cameraImage.height;
    final double compressionRatio = quality / 100.0;
    
    // Rough estimation: 3 bytes per pixel * compression ratio
    return (pixelCount * 3 * compressionRatio).round();
  }
}

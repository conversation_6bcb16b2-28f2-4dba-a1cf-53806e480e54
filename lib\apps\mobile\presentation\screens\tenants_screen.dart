import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../shared/components/app_search_field.dart';
import '../../../../shared/components/tenant_list_item.dart';
import '../../../../shared/components/tenants_empty_state.dart';
import '../../../../shared/icons/app_icons.dart';
import '../../../../shared/domain/use_cases/tenant/get_user_tenants_use_case.dart';
import '../../../../shared/domain/use_cases/tenant/switch_tenant_context_use_case.dart';
import '../../../../shared/domain/entities/tenant/tenant.dart';
import '../../../../shared/core/errors/failures.dart';
import '../../../../shared/services/selected_tenant_service.dart';
import '../../routes/mobile_route_names.dart';

/// Màn hình danh sách tổ chức
class TenantsScreen extends StatefulWidget {
  const TenantsScreen({super.key});

  @override
  State<TenantsScreen> createState() => _TenantsScreenState();
}

class _TenantsScreenState extends State<TenantsScreen> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();

  // API state
  bool _isLoading = false;
  List<Tenant> _tenants = [];
  List<Tenant> _filteredTenants = [];
  String? _errorMessage;

  // Pagination
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadTenants();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore && _hasMoreData) {
      _loadMoreTenants();
    }
  }

  // API Methods
  Future<void> _loadTenants() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final useCase = GetIt.instance<GetUserTenantsUseCase>();
      final result = await useCase(const GetUserTenantsParams(page: 1, size: 10));

      result.fold(
        (failure) {
          setState(() {
            _errorMessage = _getErrorMessage(failure);
            _isLoading = false;
          });
        },
        (tenants) {
          setState(() {
            _tenants = tenants;
            _filteredTenants = tenants;
            _currentPage = 1;
            _hasMoreData = tenants.length == 10; // Has more if we got full page
            _isLoading = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi tải dữ liệu: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreTenants() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final useCase = GetIt.instance<GetUserTenantsUseCase>();
      final result = await useCase(GetUserTenantsParams(
        page: _currentPage + 1,
        size: 10,
      ));

      result.fold(
        (failure) {
          setState(() {
            _isLoadingMore = false;
          });
          // Show snackbar for load more errors
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Lỗi tải thêm dữ liệu: ${_getErrorMessage(failure)}')),
            );
          }
        },
        (newTenants) {
          setState(() {
            _tenants.addAll(newTenants);
            if (_searchController.text.isEmpty) {
              _filteredTenants = _tenants;
            }
            _currentPage++;
            _hasMoreData = newTenants.length == 10;
            _isLoadingMore = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      // Show snackbar for load more errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi tải thêm dữ liệu: $e')),
        );
      }
    }
  }

  void _handleAddNew() {
    context.go(MobileRouteNames.tenantCreate);
  }

  void _handleTenantTap(Tenant tenant) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Call switch context API
      final switchUseCase = GetIt.instance<SwitchTenantContextUseCase>();
      final result = await switchUseCase(SwitchTenantContextParams(tenantId: tenant.id));

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      result.fold(
        (failure) {
          // Show error message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Lỗi chuyển đổi tenant: ${_getErrorMessage(failure)}'),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        (success) {
          if (success) {
            // Save selected tenant
            SelectedTenantService().setSelectedTenant(tenant);

            // Navigate to dashboard if switch successful
            if (mounted) {
              context.go('/dashboard');
            }
          } else {
            // Show error if switch failed
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Không thể chuyển đổi tenant. Vui lòng thử lại.'),
                  backgroundColor: AppColors.error,
                ),
              );
            }
          }
        },
      );
    } catch (e) {
      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi không mong muốn: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _handleSearch(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredTenants = _tenants;
      } else {
        _filteredTenants = _tenants
            .where(
              (tenant) =>
                  tenant.name.toLowerCase().contains(query.toLowerCase()) ||
                  (tenant.address?.toLowerCase().contains(query.toLowerCase()) ?? false),
            )
            .toList();
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
                ),
                child: Column(
                  children: [
                    _buildSearchField(),
                    SizedBox(height: AppDimensions.spacing12),
                    _buildSectionHeader(),
                    SizedBox(height: AppDimensions.spacing8),
                    Flexible(
                      child: _buildTenantsList(),
                    ),
                    SizedBox(
                      height: AppDimensions.spacing16,
                    ), // Bottom padding
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Danh sách tổ chức', style: AppTextStyles.heading4),
          GestureDetector(
            onTap: _handleAddNew,
            child: Container(
              padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppDimensions.radiusRound),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppIcons.add(size: 14),
                  SizedBox(width: AppDimensions.spacing6),
                  Text('Thêm mới', style: AppTextStyles.linkSmall),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return AppSearchField(
      placeholder: 'Theo tên tổ chức',
      controller: _searchController,
      onChanged: _handleSearch,
    );
  }

  Widget _buildSectionHeader() {
    return Padding(
      padding: EdgeInsets.only(left: AppDimensions.paddingXS),
      child: Row(
        children: [
          Text(
            'Tổ chức',
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantsList() {
    // Show loading indicator
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Show error state
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Lỗi tải dữ liệu',
              style: AppTextStyles.heading4,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTenants,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    // Show empty state
    if (_filteredTenants.isEmpty) {
      return Center(
        child: TenantsEmptyState(
          onAddTenant: _handleAddNew,
        ),
      );
    }

    // Show tenants list with lazy loading
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height - 30,
      ),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.border,
          width: AppDimensions.borderNormal,
        ),
      ),
      child: ListView.builder(
        controller: _scrollController,
        shrinkWrap: true,
        padding: EdgeInsets.only(top: AppDimensions.paddingS,
          bottom: AppDimensions.paddingS,
        ),
        itemCount: _filteredTenants.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at bottom
          if (index == _filteredTenants.length) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final tenant = _filteredTenants[index];
          return TenantListItem(
            name: tenant.name,
            address: tenant.address ?? 'Không có địa chỉ',
            avatarText: tenant.name.isNotEmpty ? tenant.name[0].toUpperCase() : 'T',
            showDivider: index < _filteredTenants.length - 1,
            onTap: () => _handleTenantTap(tenant),
          );
        },
      ),
    );
  }

  /// Get error message from failure
  String _getErrorMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.';
    } else if (failure is ServerFailure) {
      return failure.message.isNotEmpty
          ? failure.message
          : 'Lỗi server. Vui lòng thử lại sau.';
    } else if (failure is AuthFailure) {
      return 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
    } else if (failure is ValidationFailure) {
      return failure.message.isNotEmpty
          ? failure.message
          : 'Dữ liệu không hợp lệ.';
    } else {
      return 'Đã xảy ra lỗi. Vui lòng thử lại.';
    }
  }
}

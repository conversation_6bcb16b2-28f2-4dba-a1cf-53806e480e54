/// Error Handling Index - Export all error handling components
///
/// This file exports all error handling related components including
/// exceptions, failures, and error handlers.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/errors/index.dart';
/// 
/// // Throw exceptions
/// throw ServerException('API error');
/// throw ValidationException('Invalid input', fieldErrors: {'email': ['Invalid format']});
/// 
/// // Handle failures
/// final failure = ServerFailure('Server error');
/// final errorMessage = globalErrorHandler.getErrorMessage(failure);
/// 
/// // Use error utilities
/// if (ErrorUtils.isNetworkError(error)) {
///   // Handle network error
/// }
/// ```

// ============================================================================
// ERROR HANDLING EXPORTS
// ============================================================================

/// Exception classes for different error types
export 'exceptions.dart';

/// Failure classes for error representation
export 'failures.dart';

/// Global error handler and utilities
export 'error_handler.dart';

# 🚀 **C-Face Terminal - Installation & Setup Guide**

## **Prerequisites**

### 1. **Install Flutter**
```bash
# Download Flutter SDK from https://flutter.dev/docs/get-started/install
# Add Flutter to your PATH
flutter --version  # Should show Flutter 3.8.1+
```

### 2. **Install Development Tools**
```bash
# For Android development
# Install Android Studio with Android SDK
# Install Android SDK Command-line Tools

# For iOS development (macOS only)
# Install Xcode from App Store

# Verify setup
flutter doctor
```

### 3. **Connect Device or Setup Emulator**
```bash
# Check available devices
flutter devices

# For Android: Connect device via USB or start Android emulator
# For iOS: Connect device or start iOS simulator
```

## **📦 Installation Steps**

### 1. **Clone & Navigate to Project**
```bash
cd c:\Users\<USER>\workspace\ccam-mobile
```

### 2. **Install Dependencies**
```bash
# Get Flutter dependencies
flutter pub get

# Or use Makefile
make deps
```

### 3. **Verify Installation**
```bash
# Test build configurations
make test

# Or manually
./scripts/test_builds.sh
```

## **🎯 Running the Applications**

### **Option A: Using Makefile (Recommended)**

#### **Mobile App**
```bash
# Run mobile app in debug mode
make run-mobile

# Or with dependencies update
make dev-mobile

# Run on web browser
make run-mobile-web
```

#### **Terminal App**
```bash
# Run terminal app in debug mode
make run-terminal

# Or with dependencies update
make dev-terminal

# Run on web browser
make run-terminal-web
```

### **Option B: Using Scripts Directly**

#### **Mobile App**
```bash
# Make scripts executable (if needed)
chmod +x scripts/*.sh

# Run mobile app
./scripts/run_mobile.sh

# Run mobile app on web
./scripts/run_mobile_web.sh
```

#### **Terminal App**
```bash
# Run terminal app
./scripts/run_terminal.sh

# Run terminal app on web
./scripts/run_terminal_web.sh
```

### **Option C: Manual Flutter Commands**

#### **Mobile App**
```bash
# Debug mode
flutter run --target lib/apps/mobile/main_mobile.dart --debug --flavor mobile

# Web browser
flutter run --target lib/apps/mobile/main_mobile.dart -d chrome --flavor mobile
```

#### **Terminal App**
```bash
# Debug mode
flutter run --target lib/apps/terminal/main_terminal.dart --debug --flavor terminal

# Web browser
flutter run --target lib/apps/terminal/main_terminal.dart -d chrome --flavor terminal
```

## **🏗️ Building for Production**

### **Build APKs**
```bash
# Build mobile APK
make build-mobile

# Build terminal APK
make build-terminal

# Build both APKs
make build-all

# Complete production build (clean + deps + build)
make prod-all
```

### **APK Locations**
- **Mobile APK**: `build/app/outputs/flutter-apk/c-face-mobile-release.apk`
- **Terminal APK**: `build/app/outputs/flutter-apk/c-face-terminal-release.apk`

## **⚙️ Configuration**

### **App Configuration Files**
- **Mobile Config**: `lib/apps/mobile/config/mobile_app_config.dart`
- **Terminal Config**: `lib/apps/terminal/config/terminal_app_config.dart`
- **Shared Config**: `lib/core/config/`

### **Environment Setup**
```bash
# Check Flutter environment
flutter doctor

# Check connected devices
flutter devices

# Clean if needed
flutter clean && flutter pub get
```

## **🎮 Development Controls**

### **Hot Reload Commands (when app is running)**
- **`r`** - Hot reload (fast refresh)
- **`R`** - Hot restart (full restart)
- **`q`** - Quit application
- **`h`** - Show help

## **🔧 Troubleshooting**

### **Common Issues & Solutions**

#### **1. Flutter Not Found**
```bash
# Check Flutter installation
flutter --version
which flutter

# Add Flutter to PATH if needed
export PATH="$PATH:/path/to/flutter/bin"
```

#### **2. Build Fails**
```bash
# Clean and retry
make clean
make deps
make run-mobile  # or run-terminal
```

#### **3. Permission Denied (Linux/macOS)**
```bash
# Fix script permissions
chmod +x scripts/*.sh
```

#### **4. No Connected Devices**
```bash
# Check devices
flutter devices

# For Android: Enable USB debugging
# For iOS: Trust computer in device settings
```

#### **5. Dependencies Issues**
```bash
# Update dependencies
flutter pub upgrade
flutter pub get

# Clear pub cache if needed
flutter pub cache clean
```

## **📱 App Differences**

### **Mobile App Features**
- **UI**: Standard mobile interface with Material 3
- **Navigation**: Bottom navigation ready
- **Orientation**: Portrait + Landscape
- **Features**: Full feature set, biometric auth, offline mode

### **Terminal App Features**
- **UI**: Kiosk mode with fullscreen interface
- **Navigation**: Linear flow, large touch buttons
- **Orientation**: Landscape only
- **Features**: Simplified UI, auto-timeout, admin access

## **🎉 Quick Start Commands**

```bash
# 1. Install dependencies
make deps

# 2. Test configuration
make test

# 3. Run mobile app
make run-mobile

# 4. Or run terminal app
make run-terminal
```

## **📋 Available Make Commands**

```bash
make help           # Show all available commands
make clean          # Clean build artifacts
make deps           # Get dependencies
make test           # Test configurations
make run-mobile     # Run mobile app
make run-terminal   # Run terminal app
make build-mobile   # Build mobile APK
make build-terminal # Build terminal APK
make build-all      # Build both APKs
```

## **📚 Additional Resources**

### **Project Documentation**
- **README.md** - Project overview and migration status
- **FLAVORS_README.md** - Detailed flavor configuration guide
- **BUILD_SYSTEM.md** - Complete build system documentation
- **docs/migration/** - Migration documentation and progress

### **Key Project Files**
- **pubspec.yaml** - Dependencies and project configuration
- **Makefile** - Build automation commands
- **scripts/** - Build and run scripts for both apps
- **lib/apps/mobile/** - Mobile application code
- **lib/apps/terminal/** - Terminal application code
- **lib/shared/** - Shared components and business logic

### **Development Workflow**
1. **Setup**: Install Flutter, get dependencies, verify configuration
2. **Development**: Use hot reload for rapid development
3. **Testing**: Test both mobile and terminal configurations
4. **Building**: Create production APKs for deployment
5. **Deployment**: Install APKs on target devices

The project is well-structured with comprehensive build scripts and documentation. You can start with `make help` to see all available options, then use `make dev-mobile` or `make dev-terminal` to quickly get started with development!

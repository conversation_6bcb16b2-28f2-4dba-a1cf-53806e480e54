import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// Test HTTP connection to verify cleartext traffic is allowed
class HttpConnectionTest {
  static const String _testEndpoint = 'http://10.161.80.12';
  
  /// Test basic HTTP connection to the API server
  static Future<bool> testHttpConnection() async {
    if (kDebugMode) {
      print('🔗 Testing HTTP connection to $_testEndpoint');
    }
    
    try {
      // Test basic connectivity
      final response = await http.get(
        Uri.parse('$_testEndpoint/health'),
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'C-CAM-Terminal/1.0',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (kDebugMode) {
        print('✅ HTTP connection test successful');
        print('   Status: ${response.statusCode}');
        print('   Headers: ${response.headers}');
        print('   Body length: ${response.body.length} chars');
      }
      
      return response.statusCode == 200;
      
    } on SocketException catch (e) {
      if (kDebugMode) {
        print('❌ HTTP connection failed - Socket error: $e');
        print('   This might indicate network connectivity issues');
      }
      return false;
      
    } on HttpException catch (e) {
      if (kDebugMode) {
        print('❌ HTTP connection failed - HTTP error: $e');
        print('   This might indicate server issues');
      }
      return false;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ HTTP connection failed - Unknown error: $e');
        print('   This might indicate cleartext traffic blocking');
      }
      return false;
    }
  }
  
  /// Test face recognition endpoint specifically
  static Future<bool> testFaceRecognitionEndpoint() async {
    if (kDebugMode) {
      print('🔍 Testing face recognition endpoint');
    }
    
    try {
      final response = await http.get(
        Uri.parse('$_testEndpoint/api/v3.1/face/recognize'),
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'C-CAM-Terminal/1.0',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (kDebugMode) {
        print('✅ Face recognition endpoint test completed');
        print('   Status: ${response.statusCode}');
        print('   Expected: 405 (Method Not Allowed) for GET request');
      }
      
      // 405 is expected for GET request to POST endpoint
      return response.statusCode == 405 || response.statusCode == 200;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition endpoint test failed: $e');
      }
      return false;
    }
  }
  
  /// Test network security configuration
  static Future<Map<String, dynamic>> testNetworkSecurity() async {
    if (kDebugMode) {
      print('🛡️ Testing network security configuration');
    }
    
    final results = <String, dynamic>{};
    
    // Test HTTP connection
    results['http_connection'] = await testHttpConnection();
    
    // Test face recognition endpoint
    results['face_recognition_endpoint'] = await testFaceRecognitionEndpoint();
    
    // Test HTTPS fallback (should work for external sites)
    try {
      final httpsResponse = await http.get(
        Uri.parse('https://httpbin.org/get'),
      ).timeout(const Duration(seconds: 10));
      results['https_fallback'] = httpsResponse.statusCode == 200;
    } catch (e) {
      results['https_fallback'] = false;
    }
    
    if (kDebugMode) {
      print('📊 Network security test results:');
      results.forEach((key, value) {
        final status = value ? '✅' : '❌';
        print('   $key: $status');
      });
    }
    
    return results;
  }
  
  /// Run comprehensive connection tests
  static Future<bool> runAllTests() async {
    if (kDebugMode) {
      print('🚀 Running comprehensive HTTP connection tests...');
    }
    
    final results = await testNetworkSecurity();
    
    // Check if critical tests passed
    final httpWorks = results['http_connection'] == true;
    final endpointWorks = results['face_recognition_endpoint'] == true;
    
    final allPassed = httpWorks && endpointWorks;
    
    if (kDebugMode) {
      if (allPassed) {
        print('✅ All HTTP connection tests PASSED');
        print('   Terminal app can communicate with API server');
      } else {
        print('❌ Some HTTP connection tests FAILED');
        print('   Check network security configuration');
        if (!httpWorks) {
          print('   - Basic HTTP connection failed');
        }
        if (!endpointWorks) {
          print('   - Face recognition endpoint failed');
        }
      }
    }
    
    return allPassed;
  }
}

/// Extension for easy testing
extension HttpConnectionTestExtension on String {
  /// Test if this URL is accessible via HTTP
  Future<bool> testHttpAccess() async {
    try {
      final response = await http.get(Uri.parse(this))
          .timeout(const Duration(seconds: 5));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

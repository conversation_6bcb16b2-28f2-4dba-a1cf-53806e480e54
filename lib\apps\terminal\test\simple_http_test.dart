import 'dart:io';
import 'dart:convert';

/// Simple HTTP test without Flutter dependencies
void main() async {
  print('🔐 Simple HTTP Test for Terminal Server');
  print('========================================\n');

  await testServerConnectivity();
  await testDeviceRegistration();
  await testSecureEndpoints();
  
  print('\n✅ All HTTP tests completed!');
}

/// Test basic server connectivity
Future<void> testServerConnectivity() async {
  print('📡 Test 1: Server Connectivity');
  print('------------------------------');
  
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://localhost:3000'));
    final response = await request.close();
    
    if (response.statusCode == 200) {
      print('✅ Server is reachable at http://localhost:3000');
      print('   Status Code: ${response.statusCode}');
    } else {
      print('❌ Server returned status code: ${response.statusCode}');
    }
    
    client.close();
  } catch (e) {
    print('❌ Failed to connect to server: $e');
    print('   Make sure the test server is running on port 3000');
  }
  
  print('');
}

/// Test device registration endpoint
Future<void> testDeviceRegistration() async {
  print('📝 Test 2: Device Registration');
  print('------------------------------');
  
  try {
    final client = HttpClient();
    
    // Test registration request
    final registrationData = {
      'device_id': 'test-terminal-001',
      'device_type': 'face_terminal',
      'device_name': 'Test Terminal Device',
      'hardware_hash': 'test-hardware-hash-123',
      'app_version': '1.0.0',
      'capabilities': ['face_auth', 'relay_control', 'heartbeat'],
      'metadata': {
        'location': 'Test Lab',
        'version': '1.0.0',
        'test_mode': true,
      },
    };
    
    final request = await client.postUrl(Uri.parse('http://localhost:3000/api/device/register'));
    request.headers.contentType = ContentType.json;
    request.write(jsonEncode(registrationData));
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      print('✅ Device registration successful!');
      print('   Status Code: ${response.statusCode}');
      
      final responseData = jsonDecode(responseBody);
      print('   Access Token: ${responseData['access_token']?.toString().substring(0, 20)}...');
      print('   Secret Key: ${responseData['secret_key']?.toString().substring(0, 20)}...');
      print('   Scopes: ${responseData['scopes']}');
      print('   Expires In: ${responseData['expires_in']} seconds');
    } else {
      print('❌ Device registration failed!');
      print('   Status Code: ${response.statusCode}');
      print('   Response: $responseBody');
    }
    
    client.close();
  } catch (e) {
    print('❌ Device registration error: $e');
  }
  
  print('');
}

/// Test secure endpoints
Future<void> testSecureEndpoints() async {
  print('🔒 Test 3: Secure Endpoints');
  print('----------------------------');
  
  try {
    final client = HttpClient();
    
    // First register a device to get credentials
    final registrationData = {
      'device_id': 'test-secure-001',
      'device_type': 'face_terminal',
      'device_name': 'Test Secure Device',
      'hardware_hash': 'test-secure-hash-456',
      'app_version': '1.0.0',
      'capabilities': ['relay_control'],
    };
    
    print('🔄 Registering device for secure test...');
    final regRequest = await client.postUrl(Uri.parse('http://localhost:3000/api/device/register'));
    regRequest.headers.contentType = ContentType.json;
    regRequest.write(jsonEncode(registrationData));
    
    final regResponse = await regRequest.close();
    final regResponseBody = await regResponse.transform(utf8.decoder).join();
    
    if (regResponse.statusCode != 200) {
      print('❌ Failed to register device for secure test');
      client.close();
      return;
    }
    
    final credentials = jsonDecode(regResponseBody);
    final accessToken = credentials['access_token'];
    final secretKey = credentials['secret_key'];
    
    print('✅ Device registered for secure test');
    
    // Test secure relay control endpoint
    print('🔄 Testing secure relay control...');
    
    final relayData = {
      'device_id': 'test-relay-001',
      'action': 'unlock',
      'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };
    
    // Add HMAC signature (simplified - in real implementation would use proper HMAC)
    relayData['signature'] = 'test-signature-${DateTime.now().millisecondsSinceEpoch}';
    
    final relayRequest = await client.postUrl(Uri.parse('http://localhost:3000/relay/control'));
    relayRequest.headers.contentType = ContentType.json;
    relayRequest.headers.add('Authorization', 'Bearer $accessToken');
    relayRequest.write(jsonEncode(relayData));
    
    final relayResponse = await relayRequest.close();
    final relayResponseBody = await relayResponse.transform(utf8.decoder).join();
    
    if (relayResponse.statusCode == 200) {
      print('✅ Secure relay control successful!');
      print('   Response: $relayResponseBody');
    } else {
      print('❌ Secure relay control failed!');
      print('   Status Code: ${relayResponse.statusCode}');
      print('   Response: $relayResponseBody');
    }
    
    // Test device list endpoint
    print('🔄 Testing device list endpoint...');
    
    final listRequest = await client.getUrl(Uri.parse('http://localhost:3000/api/devices'));
    listRequest.headers.add('Authorization', 'Bearer $accessToken');
    
    final listResponse = await listRequest.close();
    final listResponseBody = await listResponse.transform(utf8.decoder).join();
    
    if (listResponse.statusCode == 200) {
      print('✅ Device list retrieval successful!');
      final devices = jsonDecode(listResponseBody);
      print('   Number of devices: ${devices.length}');
    } else {
      print('❌ Device list retrieval failed!');
      print('   Status Code: ${listResponse.statusCode}');
      print('   Response: $listResponseBody');
    }
    
    client.close();
  } catch (e) {
    print('❌ Secure endpoints test error: $e');
  }
  
  print('');
}

import 'package:dartz/dartz.dart';
import '../../entities/user/user.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';

class GetUserByIdUseCase {
  final UserRepository repository;

  GetUserByIdUseCase(this.repository);

  Future<Either<Failure, User>> call(String userId) async {
    // Validate input parameters
    if (userId.trim().isEmpty) {
      return Left(ValidationFailure(
        'User ID is required',
        fieldErrors: {'userId': ['User ID cannot be empty']},
      ));
    }

    // Call repository to get user by ID
    return await repository.getUserById(userId);
  }
}

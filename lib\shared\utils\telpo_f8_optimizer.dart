import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../models/lightweight_ml_config.dart';
import '../models/camera_config.dart';

/// Telpo F8 Terminal specific optimizations
class TelpoF8Optimizer {
  static bool _isPowerSavingMode = false;
  static bool _touchDetected = false;
  static Timer? _powerSavingTimer;
  static DateTime _lastTouchTime = DateTime.now();
  
  // FPS Monitoring
  static int _frameCount = 0;
  static int _processedFrames = 0;
  static DateTime _fpsStartTime = DateTime.now();
  static double _currentFPS = 0.0;
  static double _processingFPS = 0.0;
  
  /// Initialize Telpo F8 optimizations
  static void initialize() {
    debugPrint('🔧 Initializing Telpo F8 Terminal optimizations...');
    
    // Start FPS monitoring
    _startFPSMonitoring();
    
    // Setup power saving mode timer
    _setupPowerSavingMode();
    
    debugPrint('✅ Telpo F8 optimizations initialized');
  }

  /// Record frame for FPS calculation
  static void recordFrame() {
    _frameCount++;
  }

  /// Record processed frame for processing FPS
  static void recordProcessedFrame() {
    _processedFrames++;
  }

  /// Get current FPS metrics
  static Map<String, dynamic> getFPSMetrics() {
    return {
      'camera_fps': _currentFPS,
      'processing_fps': _processingFPS,
      'frame_count': _frameCount,
      'processed_frames': _processedFrames,
      'skip_ratio': _frameCount > 0 ? ((_frameCount - _processedFrames) / _frameCount * 100) : 0,
      'power_saving_mode': _isPowerSavingMode,
    };
  }

  /// Start FPS monitoring
  static void _startFPSMonitoring() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      final now = DateTime.now();
      final elapsed = now.difference(_fpsStartTime).inMilliseconds;
      
      if (elapsed >= 1000) {
        _currentFPS = _frameCount * 1000 / elapsed;
        _processingFPS = _processedFrames * 1000 / elapsed;
        
        if (kDebugMode) {
          debugPrint('📊 TELPO F8 FPS METRICS:');
          debugPrint('   Camera FPS: ${_currentFPS.toStringAsFixed(1)}');
          debugPrint('   Processing FPS: ${_processingFPS.toStringAsFixed(1)}');
          debugPrint('   Skip Ratio: ${((_frameCount - _processedFrames) / _frameCount * 100).toStringAsFixed(1)}%');
          debugPrint('   Power Saving: $_isPowerSavingMode');
        }
        
        // Reset counters
        _frameCount = 0;
        _processedFrames = 0;
        _fpsStartTime = now;
      }
    });
  }

  /// Setup power saving mode with touch detection
  static void _setupPowerSavingMode() {
    // Enter power saving mode after 30 seconds of no touch
    Timer.periodic(const Duration(seconds: 5), (timer) {
      final timeSinceLastTouch = DateTime.now().difference(_lastTouchTime);
      
      if (timeSinceLastTouch.inSeconds > 30 && !_isPowerSavingMode) {
        _enterPowerSavingMode();
      }
    });
  }

  /// Handle touch event (call this from UI)
  static void onTouchDetected() {
    _lastTouchTime = DateTime.now();
    _touchDetected = true;
    
    if (_isPowerSavingMode) {
      _exitPowerSavingMode();
    }
  }

  /// Enter power saving mode
  static void _enterPowerSavingMode() {
    _isPowerSavingMode = true;
    debugPrint('🔋 Entering power saving mode (Telpo F8)');
    debugPrint('   Touch screen to exit power saving mode');
  }

  /// Exit power saving mode
  static void _exitPowerSavingMode() {
    _isPowerSavingMode = false;
    debugPrint('⚡ Exiting power saving mode (Telpo F8)');
    debugPrint('   Resuming normal performance');
  }

  /// Get recommended camera config for current mode
  static CameraConfig getRecommendedCameraConfig() {
    if (_isPowerSavingMode) {
      return CameraConfig.powerSavingMode;
    } else {
      return CameraConfig.telpoF8Optimized;
    }
  }

  /// Get recommended ML config for current mode
  static FaceDetectorOptions getRecommendedMLConfig() {
    return LightweightMLConfig.getRecommendedConfig(
      isTelpoF8: true,
      isPowerSaving: _isPowerSavingMode,
    );
  }

  /// Get frame skip interval based on current mode
  static int getFrameSkipInterval() {
    if (_isPowerSavingMode) {
      return 5; // Process every 5th frame in power saving
    } else {
      return 2; // Process every 2nd frame in normal mode
    }
  }

  /// Check if should process current frame
  static bool shouldProcessFrame(int frameCount) {
    final interval = getFrameSkipInterval();
    return frameCount % interval == 0;
  }

  /// Get optimization summary
  static String getOptimizationSummary() {
    final metrics = getFPSMetrics();
    return '''
🔧 TELPO F8 TERMINAL OPTIMIZATION STATUS:
   
📊 Performance Metrics:
   Camera FPS: ${metrics['camera_fps'].toStringAsFixed(1)}
   Processing FPS: ${metrics['processing_fps'].toStringAsFixed(1)}
   Frame Skip Ratio: ${metrics['skip_ratio'].toStringAsFixed(1)}%
   
🔋 Power Management:
   Power Saving Mode: ${metrics['power_saving_mode'] ? 'ACTIVE' : 'INACTIVE'}
   Touch Detection: ${_touchDetected ? 'ACTIVE' : 'INACTIVE'}
   
⚙️ Current Configuration:
   Camera: ${_isPowerSavingMode ? 'Power Saving (480p)' : 'Optimized (720p)'}
   ML Kit: ${_isPowerSavingMode ? 'Power Saving (35% min face)' : 'Telpo F8 Optimized (20% min face)'}
   Frame Skip: Every ${getFrameSkipInterval()} frames
   
💡 Recommendations:
   - Touch screen to exit power saving mode
   - Optimal face distance: 50-80cm from camera
   - Ensure good lighting for best detection
''';
  }

  /// Dispose resources
  static void dispose() {
    _powerSavingTimer?.cancel();
    debugPrint('🧹 Telpo F8 optimizer disposed');
  }
}

/// Temporary FlavorConfig placeholder for legacy service locator
/// 
/// This is a temporary implementation to support the legacy service locator
/// until the proper flavor configuration is migrated.
/// 
/// Note: This will be replaced when CORE-004 (app-specific configuration) is implemented.
class FlavorConfig {
  static FlavorConfig? _instance;
  
  final Flavor flavor;
  final String appVersion;
  
  FlavorConfig._internal({
    required this.flavor,
    required this.appVersion,
  });
  
  static FlavorConfig get instance {
    _instance ??= FlavorConfig._internal(
      flavor: Flavor.development, // Default to development
      appVersion: '1.0.0', // Default version
    );
    return _instance!;
  }
  
  /// Initialize with specific configuration
  static void initialize({
    required Flavor flavor,
    required String appVersion,
  }) {
    _instance = FlavorConfig._internal(
      flavor: flavor,
      appVersion: appVersion,
    );
  }
}

/// Flavor enumeration
enum Flavor {
  development,
  staging,
  production;
  
  String get name {
    switch (this) {
      case Flavor.development:
        return 'development';
      case Flavor.staging:
        return 'staging';
      case Flavor.production:
        return 'production';
    }
  }
}

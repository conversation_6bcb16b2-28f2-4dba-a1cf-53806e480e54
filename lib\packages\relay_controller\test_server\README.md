# Relay Controller Test Server

A comprehensive test server for the relay_controller Flutter library with support for HTTP, WebSocket, and MQTT protocols.

## Features

- **HTTP API**: RESTful endpoints for relay control
- **WebSocket**: Real-time bidirectional communication
- **MQTT Support**: Optional MQTT broker integration
- **Web Dashboard**: Real-time monitoring and control interface with terminal-relay hierarchy
- **Device Registration**: Automatic device discovery and registration
- **Activity Logging**: Complete audit trail of all device actions
- **Multi-Protocol**: Test all relay controller communication methods
- **Device Naming System**: Structured terminal shortId and relay device linking
- **Enhanced Security**: JWT-based authentication and device management

## Device Naming System

The server supports a structured device naming convention:

### Terminal Format
- **Format**: `T-XXXX` (e.g., `T-A3B4`, `T-X9Z6`)
- **Characters**: Alphanumeric excluding confusing characters (0, O, 1, I, L)
- **Purpose**: Unique identifier for terminal devices

### Relay Format
- **Numbered**: `T-XXXX-RNN` (e.g., `T-A3B4-R01`, `T-A3B4-R02`)
- **Named**: `T-XXXX-NAME` (e.g., `T-A3B4-DOOR`, `T-A3B4-ALARM`)
- **Purpose**: Link relay devices to their controlling terminal

### Benefits
- **Easy Identification**: Clear device hierarchy and relationships
- **Scalable**: Support for multiple relays per terminal
- **Professional**: Structured naming for enterprise environments
- **Backward Compatible**: Legacy device IDs still supported

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Start the Server

```bash
npm start
```

Or for development with auto-reload:

```bash
npm run dev
```

### 3. Open Dashboard

Navigate to `http://localhost:3000` in your browser to access the web dashboard.

## API Endpoints

### Device Management

- `POST /api/device/register` - Secure device registration with naming system support
- `POST /register` - Legacy device registration (for backward compatibility)
- `GET /devices` - List all registered devices with enhanced metadata
- `GET /devices/:deviceId` - Get specific device info
- `GET /devices/:deviceId/logs` - Get device activity logs
- `GET /api/devices/grouped` - Get devices grouped by terminal hierarchy

### Device Naming System

- `GET /api/naming/generate-terminal-id` - Generate new terminal shortId (T-XXXX format)
- `POST /api/naming/generate-relay-id` - Generate relay device ID for terminal
- `POST /api/naming/validate` - Validate device naming format
- `GET /api/naming/summary/:terminalId` - Get naming summary for terminal

### Relay Control

#### Generic Endpoints (with query parameter)
- `GET /relay/on?deviceId=xxx` - Turn relay on
- `GET /relay/off?deviceId=xxx` - Turn relay off
- `GET /relay/status?deviceId=xxx` - Get relay status

#### Device-Specific Endpoints
- `GET /devices/:deviceId/on` - Turn specific device on
- `GET /devices/:deviceId/off` - Turn specific device off
- `GET /devices/:deviceId/status` - Get specific device status

#### JSON Control
- `POST /relay/control` - Control relay via JSON body

### Example Requests

#### Register Device (Legacy)
```bash
curl -X POST http://localhost:3000/register \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "relay-001", "deviceName": "Living Room Relay"}'
```

#### Secure Device Registration with Naming System
```bash
curl -X POST http://localhost:3000/api/device/register \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "T-A3B4",
    "device_type": "terminal",
    "device_name": "Main Entrance Terminal"
  }'

curl -X POST http://localhost:3000/api/device/register \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "T-A3B4-R01",
    "device_type": "relay",
    "device_name": "Door Lock Relay"
  }'
```

#### Device Naming Examples
```bash
# Generate new terminal ID
curl http://localhost:3000/api/naming/generate-terminal-id

# Validate device naming
curl -X POST http://localhost:3000/api/naming/validate \
  -H "Content-Type: application/json" \
  -d '{"device_id": "T-A3B4-R01"}'

# Get grouped devices
curl http://localhost:3000/api/devices/grouped
```

#### Control Relay
```bash
# Turn on
curl "http://localhost:3000/relay/on?deviceId=relay-001"

# Turn off
curl "http://localhost:3000/relay/off?deviceId=relay-001"

# Get status
curl "http://localhost:3000/relay/status?deviceId=relay-001"
```

#### JSON Control
```bash
curl -X POST http://localhost:3000/relay/control \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "relay-001", "command": "ON"}'
```

## WebSocket Events

### Client to Server
- `registerDevice` - Register a new device
- `relayControl` - Control relay state

### Server to Client
- `deviceList` - Initial device list
- `deviceStateChanged` - Device state update
- `deviceLog` - New activity log entry
- `registerResult` - Device registration result
- `relayControlResult` - Relay control result

### Example WebSocket Usage

```javascript
const socket = io('http://localhost:3000');

// Register device
socket.emit('registerDevice', {
  deviceId: 'relay-001',
  deviceName: 'Test Relay',
  type: 'relay'
});

// Control relay
socket.emit('relayControl', {
  deviceId: 'relay-001',
  command: 'ON'
});

// Listen for updates
socket.on('deviceStateChanged', (data) => {
  console.log('Device state changed:', data);
});
```

## MQTT Integration

The server optionally connects to an MQTT broker on `localhost:1883` and subscribes to the topic pattern `relay/+/control`.

### MQTT Topic Structure
- `relay/{deviceId}/control` - Send commands to specific device

### Example MQTT Commands
```bash
# Using mosquitto_pub
mosquitto_pub -h localhost -t "relay/relay-001/control" -m "ON"
mosquitto_pub -h localhost -t "relay/relay-001/control" -m "OFF"
```

## Testing with Flutter App

### 1. Update Flutter Example

```dart
final controller = HttpRelayController(
  deviceId: 'flutter-relay-001',
  deviceName: 'Flutter Test Relay',
  urlOn: 'http://localhost:3000/relay/on?deviceId=flutter-relay-001',
  urlOff: 'http://localhost:3000/relay/off?deviceId=flutter-relay-001',
);

// Register device
await controller.registerDevice();

// Control relay
await controller.triggerOn();
await controller.triggerOff();

// Get status
final status = await controller.getStatus();
```

### 2. Test Different Protocols

#### HTTP Controller
```dart
final httpController = HttpRelayController(
  deviceId: 'http-relay-001',
  urlOn: 'http://localhost:3000/devices/http-relay-001/on',
  urlOff: 'http://localhost:3000/devices/http-relay-001/off',
);
```

#### MQTT Controller (requires MQTT broker)
```dart
final mqttController = MqttRelayController(
  deviceId: 'mqtt-relay-001',
  brokerHost: 'localhost',
  topic: 'relay/mqtt-relay-001/control',
);
```

## Development

### Project Structure
```
test_server/
├── server.js          # Main server file
├── package.json       # Dependencies
├── public/
│   └── index.html     # Web dashboard
└── README.md          # This file
```

### Adding New Features

1. **New API Endpoints**: Add routes in `server.js`
2. **WebSocket Events**: Add event handlers in the `io.on('connection')` block
3. **Dashboard Updates**: Modify `public/index.html`
4. **MQTT Topics**: Update MQTT subscription patterns

### Environment Variables

- `PORT` - Server port (default: 3000)
- `MQTT_BROKER` - MQTT broker URL (default: mqtt://localhost:1883)

## Troubleshooting

### MQTT Connection Issues
If MQTT broker is not available, the server will continue without MQTT support. Install and start a local MQTT broker like Mosquitto:

```bash
# Ubuntu/Debian
sudo apt-get install mosquitto mosquitto-clients

# macOS
brew install mosquitto

# Start broker
mosquitto
```

### Port Already in Use
Change the port by setting the PORT environment variable:

```bash
PORT=3001 npm start
```

### CORS Issues
The server is configured to allow all origins. For production, update the CORS configuration in `server.js`.

## License

MIT License - see the main relay_controller package for details.

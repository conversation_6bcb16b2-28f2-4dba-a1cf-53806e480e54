import '../models/secure_message.dart';
import '../models/device_registration.dart';

/// Abstract interface for transport layer implementations
/// 
/// This interface allows SecureComm to work with different transport
/// mechanisms (HTTP, MQTT, WebSocket) without being tied to any specific one.
abstract class TransportInterface {
  /// Register device with server
  Future<DeviceRegistrationResponse> registerDevice(
    DeviceRegistrationRequest request,
  );

  /// Refresh access token
  Future<Map<String, dynamic>> refreshToken(String refreshToken);

  /// Send secure message to server
  Future<SecureResponse> sendMessage({
    required SecureMessage message,
    required String accessToken,
  });

  /// Send plain text message without encryption/signing
  Future<SecureResponse> sendPlainTextMessage(Map<String, dynamic> message);

  /// Revoke device credentials
  Future<void> revokeCredentials(String accessToken);

  /// Dispose transport resources
  Future<void> dispose();

  /// Check if transport is connected/available
  bool get isConnected;

  /// Get transport type identifier
  String get transportType;
}

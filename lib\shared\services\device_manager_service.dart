import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:network_info_plus/network_info_plus.dart' as network_info;

/// Model class để lưu trữ thông tin thiết bị
class DeviceInformation {
  final String deviceId;
  final String deviceName;
  final String deviceModel;
  final String deviceBrand;
  final String operatingSystem;
  final String osVersion;
  final String appName;
  final String appVersion;
  final String buildNumber;
  final String packageName;
  final Map<String, dynamic> platformSpecificInfo;
  final NetworkInfo networkInfo;
  final BatteryInfo batteryInfo;
  final SystemInfo systemInfo;
  final DateTime collectedAt;

  const DeviceInformation({
    required this.deviceId,
    required this.deviceName,
    required this.deviceModel,
    required this.deviceBrand,
    required this.operatingSystem,
    required this.osVersion,
    required this.appName,
    required this.appVersion,
    required this.buildNumber,
    required this.packageName,
    required this.platformSpecificInfo,
    required this.networkInfo,
    required this.batteryInfo,
    required this.systemInfo,
    required this.collectedAt,
  });

  /// Chuyển đổi thành Map để lưu trữ hoặc gửi qua API
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'deviceModel': deviceModel,
      'deviceBrand': deviceBrand,
      'operatingSystem': operatingSystem,
      'osVersion': osVersion,
      'appName': appName,
      'appVersion': appVersion,
      'buildNumber': buildNumber,
      'packageName': packageName,
      'platformSpecificInfo': platformSpecificInfo,
      'networkInfo': networkInfo.toMap(),
      'batteryInfo': batteryInfo.toMap(),
      'systemInfo': systemInfo.toMap(),
      'collectedAt': collectedAt.toIso8601String(),
    };
  }

  /// Chuyển đổi thành JSON string
  String toJson() => jsonEncode(toMap());

  /// Tạo instance từ Map
  factory DeviceInformation.fromMap(Map<String, dynamic> map) {
    return DeviceInformation(
      deviceId: map['deviceId'] ?? '',
      deviceName: map['deviceName'] ?? '',
      deviceModel: map['deviceModel'] ?? '',
      deviceBrand: map['deviceBrand'] ?? '',
      operatingSystem: map['operatingSystem'] ?? '',
      osVersion: map['osVersion'] ?? '',
      appName: map['appName'] ?? '',
      appVersion: map['appVersion'] ?? '',
      buildNumber: map['buildNumber'] ?? '',
      packageName: map['packageName'] ?? '',
      platformSpecificInfo: Map<String, dynamic>.from(
        map['platformSpecificInfo'] ?? {},
      ),
      networkInfo: NetworkInfo.fromMap(map['networkInfo'] ?? {}),
      batteryInfo: BatteryInfo.fromMap(map['batteryInfo'] ?? {}),
      systemInfo: SystemInfo.fromMap(map['systemInfo'] ?? {}),
      collectedAt: DateTime.parse(
        map['collectedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// Tạo instance từ JSON string
  factory DeviceInformation.fromJson(String source) =>
      DeviceInformation.fromMap(jsonDecode(source));

  @override
  String toString() {
    return 'DeviceInformation(deviceId: $deviceId, deviceName: $deviceName, '
        'deviceModel: $deviceModel, operatingSystem: $operatingSystem)';
  }
}

/// Model class cho thông tin mạng
class NetworkInfo {
  final String connectionType;
  final String? wifiName;
  final String? wifiBSSID;
  final String? wifiIP;
  final String? wifiIPv6;
  final String? wifiGatewayIP;
  final String? wifiBroadcast;
  final String? wifiSubmask;
  final bool isConnected;

  const NetworkInfo({
    required this.connectionType,
    this.wifiName,
    this.wifiBSSID,
    this.wifiIP,
    this.wifiIPv6,
    this.wifiGatewayIP,
    this.wifiBroadcast,
    this.wifiSubmask,
    required this.isConnected,
  });

  Map<String, dynamic> toMap() {
    return {
      'connectionType': connectionType,
      'wifiName': wifiName,
      'wifiBSSID': wifiBSSID,
      'wifiIP': wifiIP,
      'wifiIPv6': wifiIPv6,
      'wifiGatewayIP': wifiGatewayIP,
      'wifiBroadcast': wifiBroadcast,
      'wifiSubmask': wifiSubmask,
      'isConnected': isConnected,
    };
  }

  factory NetworkInfo.fromMap(Map<String, dynamic> map) {
    return NetworkInfo(
      connectionType: map['connectionType'] ?? 'unknown',
      wifiName: map['wifiName'],
      wifiBSSID: map['wifiBSSID'],
      wifiIP: map['wifiIP'],
      wifiIPv6: map['wifiIPv6'],
      wifiGatewayIP: map['wifiGatewayIP'],
      wifiBroadcast: map['wifiBroadcast'],
      wifiSubmask: map['wifiSubmask'],
      isConnected: map['isConnected'] ?? false,
    );
  }
}

/// Model class cho thông tin pin
class BatteryInfo {
  final int batteryLevel;
  final String batteryState;
  final bool isInBatterySaveMode;

  const BatteryInfo({
    required this.batteryLevel,
    required this.batteryState,
    required this.isInBatterySaveMode,
  });

  Map<String, dynamic> toMap() {
    return {
      'batteryLevel': batteryLevel,
      'batteryState': batteryState,
      'isInBatterySaveMode': isInBatterySaveMode,
    };
  }

  factory BatteryInfo.fromMap(Map<String, dynamic> map) {
    return BatteryInfo(
      batteryLevel: map['batteryLevel'] ?? 0,
      batteryState: map['batteryState'] ?? 'unknown',
      isInBatterySaveMode: map['isInBatterySaveMode'] ?? false,
    );
  }
}

/// Model class cho thông tin hệ thống
class SystemInfo {
  final String platform;
  final bool isPhysicalDevice;
  final String locale;
  final List<String> supportedLocales;
  final double screenWidth;
  final double screenHeight;
  final double pixelRatio;
  final String orientation;

  const SystemInfo({
    required this.platform,
    required this.isPhysicalDevice,
    required this.locale,
    required this.supportedLocales,
    required this.screenWidth,
    required this.screenHeight,
    required this.pixelRatio,
    required this.orientation,
  });

  Map<String, dynamic> toMap() {
    return {
      'platform': platform,
      'isPhysicalDevice': isPhysicalDevice,
      'locale': locale,
      'supportedLocales': supportedLocales,
      'screenWidth': screenWidth,
      'screenHeight': screenHeight,
      'pixelRatio': pixelRatio,
      'orientation': orientation,
    };
  }

  factory SystemInfo.fromMap(Map<String, dynamic> map) {
    return SystemInfo(
      platform: map['platform'] ?? '',
      isPhysicalDevice: map['isPhysicalDevice'] ?? true,
      locale: map['locale'] ?? '',
      supportedLocales: List<String>.from(map['supportedLocales'] ?? []),
      screenWidth: map['screenWidth']?.toDouble() ?? 0.0,
      screenHeight: map['screenHeight']?.toDouble() ?? 0.0,
      pixelRatio: map['pixelRatio']?.toDouble() ?? 1.0,
      orientation: map['orientation'] ?? '',
    );
  }
}

/// Service class để thu thập toàn bộ thông tin thiết bị
class DeviceManagerService {
  static final DeviceManagerService _instance =
      DeviceManagerService._internal();

  factory DeviceManagerService() => _instance;

  DeviceManagerService._internal();

  // Instances của các plugin
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final Connectivity _connectivity = Connectivity();
  final Battery _battery = Battery();
  final network_info.NetworkInfo _networkInfoPlugin =
      network_info.NetworkInfo();

  /// Thu thập toàn bộ thông tin thiết bị
  ///
  /// Returns [DeviceInformation] chứa tất cả thông tin thiết bị
  /// Throws [Exception] nếu có lỗi xảy ra trong quá trình thu thập
  Future<DeviceInformation> collectDeviceInformation() async {
    try {
      // Thu thập thông tin song song để tối ưu hiệu suất
      final results = await Future.wait([
        _getDeviceInfo(),
        _getPackageInfo(),
        _getNetworkInfo(),
        _getBatteryInfo(),
        _getSystemInfo(),
      ]);

      final deviceInfo = results[0] as Map<String, dynamic>;
      final packageInfo = results[1] as PackageInfo;
      final networkInfo = results[2] as NetworkInfo;
      final batteryInfo = results[3] as BatteryInfo;
      final systemInfo = results[4] as SystemInfo;

      return DeviceInformation(
        deviceId: deviceInfo['deviceId'] ?? '',
        deviceName: deviceInfo['deviceName'] ?? '',
        deviceModel: deviceInfo['deviceModel'] ?? '',
        deviceBrand: deviceInfo['deviceBrand'] ?? '',
        operatingSystem: deviceInfo['operatingSystem'] ?? '',
        osVersion: deviceInfo['osVersion'] ?? '',
        appName: packageInfo.appName,
        appVersion: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
        packageName: packageInfo.packageName,
        platformSpecificInfo: deviceInfo['platformSpecificInfo'] ?? {},
        networkInfo: networkInfo,
        batteryInfo: batteryInfo,
        systemInfo: systemInfo,
        collectedAt: DateTime.now(),
      );
    } catch (e) {
      throw Exception('Lỗi khi thu thập thông tin thiết bị: $e');
    }
  }

  /// Thu thập thông tin cơ bản của thiết bị
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidDeviceInfo();
      } else if (Platform.isIOS) {
        return await _getIOSDeviceInfo();
      } else if (Platform.isWindows) {
        return await _getWindowsDeviceInfo();
      } else if (Platform.isLinux) {
        return await _getLinuxDeviceInfo();
      } else if (Platform.isMacOS) {
        return await _getMacOSDeviceInfo();
      } else if (kIsWeb) {
        return await _getWebDeviceInfo();
      } else {
        return _getUnknownDeviceInfo();
      }
    } catch (e) {
      throw Exception('Lỗi khi thu thập device info: $e');
    }
  }

  /// Thu thập thông tin Android device
  Future<Map<String, dynamic>> _getAndroidDeviceInfo() async {
    final androidInfo = await _deviceInfo.androidInfo;
    return {
      'deviceId': androidInfo.id,
      'deviceName': androidInfo.device,
      'deviceModel': androidInfo.model,
      'deviceBrand': androidInfo.brand,
      'operatingSystem': 'Android',
      'osVersion': androidInfo.version.release,
      'platformSpecificInfo': {
        'manufacturer': androidInfo.manufacturer,
        'product': androidInfo.product,
        'hardware': androidInfo.hardware,
        'bootloader': androidInfo.bootloader,
        'board': androidInfo.board,
        'host': androidInfo.host,
        'fingerprint': androidInfo.fingerprint,
        'tags': androidInfo.tags,
        'type': androidInfo.type,
        'display': androidInfo.display,
        'sdkInt': androidInfo.version.sdkInt,
        'securityPatch': androidInfo.version.securityPatch,
        'isPhysicalDevice': androidInfo.isPhysicalDevice,
        'systemFeatures': androidInfo.systemFeatures,
        'supportedAbis': androidInfo.supportedAbis,
        'supported32BitAbis': androidInfo.supported32BitAbis,
        'supported64BitAbis': androidInfo.supported64BitAbis,
      },
    };
  }

  /// Thu thập thông tin iOS device
  Future<Map<String, dynamic>> _getIOSDeviceInfo() async {
    final iosInfo = await _deviceInfo.iosInfo;
    return {
      'deviceId': iosInfo.identifierForVendor ?? '',
      'deviceName': iosInfo.name,
      'deviceModel': iosInfo.model,
      'deviceBrand': 'Apple',
      'operatingSystem': 'iOS',
      'osVersion': iosInfo.systemVersion,
      'platformSpecificInfo': {
        'localizedModel': iosInfo.localizedModel,
        'systemName': iosInfo.systemName,
        'utsname': {
          'sysname': iosInfo.utsname.sysname,
          'nodename': iosInfo.utsname.nodename,
          'release': iosInfo.utsname.release,
          'version': iosInfo.utsname.version,
          'machine': iosInfo.utsname.machine,
        },
        'isPhysicalDevice': iosInfo.isPhysicalDevice,
      },
    };
  }

  /// Thu thập thông tin Windows device
  Future<Map<String, dynamic>> _getWindowsDeviceInfo() async {
    final windowsInfo = await _deviceInfo.windowsInfo;
    return {
      'deviceId': windowsInfo.deviceId,
      'deviceName': windowsInfo.computerName,
      'deviceModel': windowsInfo.productName,
      'deviceBrand': 'Microsoft',
      'operatingSystem': 'Windows',
      'osVersion': '${windowsInfo.majorVersion}.${windowsInfo.minorVersion}',
      'platformSpecificInfo': {
        'userName': windowsInfo.userName,
        'buildNumber': windowsInfo.buildNumber,
        'platformId': windowsInfo.platformId,
        'csdVersion': windowsInfo.csdVersion,
        'servicePackMajor': windowsInfo.servicePackMajor,
        'servicePackMinor': windowsInfo.servicePackMinor,
        'suitMask': windowsInfo.suitMask,
        'productType': windowsInfo.productType,
        'reserved': windowsInfo.reserved,
        'buildLab': windowsInfo.buildLab,
        'buildLabEx': windowsInfo.buildLabEx,
        'digitalProductId': windowsInfo.digitalProductId,
        'displayVersion': windowsInfo.displayVersion,
        'editionId': windowsInfo.editionId,
        'installDate': windowsInfo.installDate.toIso8601String(),
        'productId': windowsInfo.productId,
        'registeredOwner': windowsInfo.registeredOwner,
        'releaseId': windowsInfo.releaseId,
      },
    };
  }

  /// Thu thập thông tin Linux device
  Future<Map<String, dynamic>> _getLinuxDeviceInfo() async {
    final linuxInfo = await _deviceInfo.linuxInfo;
    return {
      'deviceId': linuxInfo.machineId ?? '',
      'deviceName': linuxInfo.name,
      'deviceModel': linuxInfo.prettyName,
      'deviceBrand': 'Linux',
      'operatingSystem': 'Linux',
      'osVersion': linuxInfo.version ?? '',
      'platformSpecificInfo': {
        'id': linuxInfo.id,
        'idLike': linuxInfo.idLike,
        'versionCodename': linuxInfo.versionCodename,
        'versionId': linuxInfo.versionId,
        'buildId': linuxInfo.buildId,
        'variant': linuxInfo.variant,
        'variantId': linuxInfo.variantId,
      },
    };
  }

  /// Thu thập thông tin macOS device
  Future<Map<String, dynamic>> _getMacOSDeviceInfo() async {
    final macInfo = await _deviceInfo.macOsInfo;
    return {
      'deviceId': macInfo.systemGUID ?? '',
      'deviceName': macInfo.computerName,
      'deviceModel': macInfo.model,
      'deviceBrand': 'Apple',
      'operatingSystem': 'macOS',
      'osVersion': macInfo.osRelease,
      'platformSpecificInfo': {
        'kernelVersion': macInfo.kernelVersion,
        'majorVersion': macInfo.majorVersion,
        'minorVersion': macInfo.minorVersion,
        'patchVersion': macInfo.patchVersion,
        'arch': macInfo.arch,
        'memorySize': macInfo.memorySize,
        'cpuFrequency': macInfo.cpuFrequency,
        'hostName': macInfo.hostName,
        // 'userName': macInfo.userName, // Property không tồn tại
      },
    };
  }

  /// Thu thập thông tin Web device
  Future<Map<String, dynamic>> _getWebDeviceInfo() async {
    final webInfo = await _deviceInfo.webBrowserInfo;
    return {
      'deviceId': 'web_${webInfo.userAgent?.hashCode ?? 0}',
      'deviceName': webInfo.browserName.name,
      'deviceModel': 'Web Browser',
      'deviceBrand': webInfo.vendor ?? 'Unknown',
      'operatingSystem': 'Web',
      'osVersion': webInfo.platform ?? '',
      'platformSpecificInfo': {
        'userAgent': webInfo.userAgent,
        'language': webInfo.language,
        'languages': webInfo.languages,
        'platform': webInfo.platform,
        'product': webInfo.product,
        'productSub': webInfo.productSub,
        'vendor': webInfo.vendor,
        'vendorSub': webInfo.vendorSub,
        'hardwareConcurrency': webInfo.hardwareConcurrency,
        'maxTouchPoints': webInfo.maxTouchPoints,
      },
    };
  }

  /// Thu thập thông tin cho platform không xác định
  Map<String, dynamic> _getUnknownDeviceInfo() {
    return {
      'deviceId': 'unknown_${DateTime.now().millisecondsSinceEpoch}',
      'deviceName': 'Unknown Device',
      'deviceModel': 'Unknown Model',
      'deviceBrand': 'Unknown Brand',
      'operatingSystem': 'Unknown OS',
      'osVersion': 'Unknown Version',
      'platformSpecificInfo': {},
    };
  }

  /// Thu thập thông tin package/app
  Future<PackageInfo> _getPackageInfo() async {
    try {
      return await PackageInfo.fromPlatform();
    } catch (e) {
      throw Exception('Lỗi khi thu thập package info: $e');
    }
  }

  /// Thu thập thông tin mạng
  Future<NetworkInfo> _getNetworkInfo() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      final isConnected =
          connectivityResult.isNotEmpty &&
          !connectivityResult.contains(ConnectivityResult.none);

      String connectionType = 'none';
      if (connectivityResult.contains(ConnectivityResult.wifi)) {
        connectionType = 'wifi';
      } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
        connectionType = 'mobile';
      } else if (connectivityResult.contains(ConnectivityResult.ethernet)) {
        connectionType = 'ethernet';
      } else if (connectivityResult.contains(ConnectivityResult.bluetooth)) {
        connectionType = 'bluetooth';
      } else if (connectivityResult.contains(ConnectivityResult.vpn)) {
        connectionType = 'vpn';
      } else if (connectivityResult.contains(ConnectivityResult.other)) {
        connectionType = 'other';
      }

      String? wifiName;
      String? wifiBSSID;
      String? wifiIP;
      String? wifiIPv6;
      String? wifiGatewayIP;
      String? wifiBroadcast;
      String? wifiSubmask;

      // Thu thập thông tin WiFi chi tiết nếu đang kết nối WiFi
      if (connectionType == 'wifi') {
        try {
          wifiName = await _networkInfoPlugin.getWifiName();
          wifiBSSID = await _networkInfoPlugin.getWifiBSSID();
          wifiIP = await _networkInfoPlugin.getWifiIP();
          wifiIPv6 = await _networkInfoPlugin.getWifiIPv6();
          wifiGatewayIP = await _networkInfoPlugin.getWifiGatewayIP();
          wifiBroadcast = await _networkInfoPlugin.getWifiBroadcast();
          wifiSubmask = await _networkInfoPlugin.getWifiSubmask();
        } catch (e) {
          // Một số thông tin có thể không khả dụng trên một số platform
          debugPrint('Không thể thu thập một số thông tin WiFi: $e');
        }
      }

      return NetworkInfo(
        connectionType: connectionType,
        wifiName: wifiName,
        wifiBSSID: wifiBSSID,
        wifiIP: wifiIP,
        wifiIPv6: wifiIPv6,
        wifiGatewayIP: wifiGatewayIP,
        wifiBroadcast: wifiBroadcast,
        wifiSubmask: wifiSubmask,
        isConnected: isConnected,
      );
    } catch (e) {
      throw Exception('Lỗi khi thu thập network info: $e');
    }
  }

  /// Thu thập thông tin pin
  Future<BatteryInfo> _getBatteryInfo() async {
    try {
      final batteryLevel = await _battery.batteryLevel;
      final batteryState = await _battery.batteryState;
      final isInBatterySaveMode = await _battery.isInBatterySaveMode;

      String batteryStateString;
      switch (batteryState) {
        case BatteryState.charging:
          batteryStateString = 'charging';
          break;
        case BatteryState.discharging:
          batteryStateString = 'discharging';
          break;
        case BatteryState.full:
          batteryStateString = 'full';
          break;
        case BatteryState.connectedNotCharging:
          batteryStateString = 'connected_not_charging';
          break;
        default:
          batteryStateString = 'unknown';
      }

      return BatteryInfo(
        batteryLevel: batteryLevel,
        batteryState: batteryStateString,
        isInBatterySaveMode: isInBatterySaveMode,
      );
    } catch (e) {
      throw Exception('Lỗi khi thu thập battery info: $e');
    }
  }

  /// Thu thập thông tin hệ thống
  Future<SystemInfo> _getSystemInfo() async {
    try {
      // Thu thập thông tin từ Flutter framework
      final view = WidgetsBinding.instance.platformDispatcher.views.first;
      final screenSize = view.physicalSize;
      final pixelRatio = view.devicePixelRatio;
      final platformDispatcher = WidgetsBinding.instance.platformDispatcher;
      final locale = platformDispatcher.locale;
      final locales = platformDispatcher.locales;

      return SystemInfo(
        platform: Platform.operatingSystem,
        isPhysicalDevice: kIsWeb ? false : !kDebugMode, // Approximation
        locale: locale.toString(),
        supportedLocales: locales.map((l) => l.toString()).toList(),
        screenWidth: screenSize.width / pixelRatio,
        screenHeight: screenSize.height / pixelRatio,
        pixelRatio: pixelRatio,
        orientation: screenSize.width > screenSize.height
            ? 'landscape'
            : 'portrait',
      );
    } catch (e) {
      throw Exception('Lỗi khi thu thập system info: $e');
    }
  }

  // ========== UTILITY METHODS ==========

  /// Tạo device fingerprint duy nhất
  Future<String> generateDeviceFingerprint() async {
    try {
      final deviceInfo = await collectDeviceInformation();
      final fingerprintData = {
        'deviceId': deviceInfo.deviceId,
        'deviceModel': deviceInfo.deviceModel,
        'deviceBrand': deviceInfo.deviceBrand,
        'osVersion': deviceInfo.osVersion,
        'screenWidth': deviceInfo.systemInfo.screenWidth,
        'screenHeight': deviceInfo.systemInfo.screenHeight,
        'pixelRatio': deviceInfo.systemInfo.pixelRatio,
      };

      final fingerprintString = jsonEncode(fingerprintData);
      return fingerprintString.hashCode.toString();
    } catch (e) {
      throw Exception('Lỗi khi tạo device fingerprint: $e');
    }
  }

  /// Kiểm tra xem thiết bị có phải là thiết bị thật hay không
  Future<bool> isPhysicalDevice() async {
    try {
      if (kIsWeb) return false;

      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return androidInfo.isPhysicalDevice;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return iosInfo.isPhysicalDevice;
      }

      return true; // Assume physical device for other platforms
    } catch (e) {
      return true; // Default to physical device if unable to determine
    }
  }

  /// Lấy thông tin cơ bản của thiết bị (lightweight version)
  Future<Map<String, String>> getBasicDeviceInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final connectivityResult = await _connectivity.checkConnectivity();

      String deviceId = '';
      String deviceName = '';
      String osVersion = '';

      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceId = androidInfo.id;
        deviceName = androidInfo.model;
        osVersion = androidInfo.version.release;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
        deviceName = iosInfo.name;
        osVersion = iosInfo.systemVersion;
      }

      return {
        'deviceId': deviceId,
        'deviceName': deviceName,
        'osVersion': osVersion,
        'appVersion': packageInfo.version,
        'connectionType': connectivityResult.isNotEmpty
            ? connectivityResult.first.name
            : 'none',
      };
    } catch (e) {
      throw Exception('Lỗi khi lấy basic device info: $e');
    }
  }

  /// Stream để theo dõi thay đổi kết nối mạng
  Stream<List<ConnectivityResult>> get connectivityStream =>
      _connectivity.onConnectivityChanged;

  /// Stream để theo dõi thay đổi trạng thái pin
  Stream<BatteryState> get batteryStateStream => _battery.onBatteryStateChanged;
}

import 'dart:io';
import 'dart:convert';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

/// Service for collecting system information
class SystemInfoService {
  static final SystemInfoService _instance = SystemInfoService._internal();
  factory SystemInfoService() => _instance;
  SystemInfoService._internal();

  final Logger _logger = Logger();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final Battery _battery = Battery();
  final Connectivity _connectivity = Connectivity();

  /// Get comprehensive system information
  Future<Map<String, dynamic>> getSystemInfo() async {
    try {
      final info = <String, dynamic>{};
      
      // Basic system info
      info['platform'] = Platform.operatingSystem;
      info['timestamp'] = DateTime.now().toIso8601String();
      
      // Memory info
      final memoryInfo = await _getMemoryInfo();
      info.addAll(memoryInfo);
      
      // CPU info
      final cpuInfo = await _getCpuInfo();
      info.addAll(cpuInfo);
      
      // Device info
      final deviceInfo = await _getDeviceInfo();
      info.addAll(deviceInfo);
      
      // Battery info
      final batteryInfo = await _getBatteryInfo();
      info.addAll(batteryInfo);
      
      // Network info
      final networkInfo = await _getNetworkInfo();
      info.addAll(networkInfo);
      
      return info;
    } catch (e) {
      _logger.e('Failed to get system info', error: e);
      return _getFallbackSystemInfo();
    }
  }

  /// Get memory information
  Future<Map<String, dynamic>> _getMemoryInfo() async {
    try {
      if (Platform.isWindows) {
        return await _getWindowsMemoryInfo();
      } else if (Platform.isLinux) {
        return await _getLinuxMemoryInfo();
      } else if (Platform.isAndroid) {
        return await _getAndroidMemoryInfo();
      } else {
        return {
          'memory_total': 'Unknown',
          'memory_used': 'Unknown',
          'memory_free': 'Unknown',
          'memory_usage_percent': 0.0,
        };
      }
    } catch (e) {
      _logger.w('Failed to get memory info', error: e);
      return {
        'memory_total': 'Error',
        'memory_used': 'Error',
        'memory_free': 'Error',
        'memory_usage_percent': 0.0,
      };
    }
  }

  /// Get Windows memory information
  Future<Map<String, dynamic>> _getWindowsMemoryInfo() async {
    try {
      // Use PowerShell to get memory info
      final result = await Process.run('powershell', [
        '-Command',
        'Get-WmiObject -Class Win32_OperatingSystem | Select-Object TotalVisibleMemorySize,FreePhysicalMemory | ConvertTo-Json'
      ]);
      
      if (result.exitCode == 0) {
        final data = jsonDecode(result.stdout);
        final totalKB = int.parse(data['TotalVisibleMemorySize'].toString());
        final freeKB = int.parse(data['FreePhysicalMemory'].toString());
        final usedKB = totalKB - freeKB;
        final usagePercent = (usedKB / totalKB * 100);
        
        return {
          'memory_total': '${(totalKB / 1024 / 1024).toStringAsFixed(2)} GB',
          'memory_used': '${(usedKB / 1024 / 1024).toStringAsFixed(2)} GB',
          'memory_free': '${(freeKB / 1024 / 1024).toStringAsFixed(2)} GB',
          'memory_usage_percent': usagePercent.toStringAsFixed(1),
        };
      }
    } catch (e) {
      _logger.w('Failed to get Windows memory info', error: e);
    }
    
    return {
      'memory_total': 'Unknown',
      'memory_used': 'Unknown',
      'memory_free': 'Unknown',
      'memory_usage_percent': 0.0,
    };
  }

  /// Get Linux memory information
  Future<Map<String, dynamic>> _getLinuxMemoryInfo() async {
    try {
      final file = File('/proc/meminfo');
      if (await file.exists()) {
        final content = await file.readAsString();
        final lines = content.split('\n');
        
        int? totalKB, freeKB, availableKB;
        
        for (final line in lines) {
          if (line.startsWith('MemTotal:')) {
            totalKB = int.tryParse(line.split(RegExp(r'\s+'))[1]);
          } else if (line.startsWith('MemFree:')) {
            freeKB = int.tryParse(line.split(RegExp(r'\s+'))[1]);
          } else if (line.startsWith('MemAvailable:')) {
            availableKB = int.tryParse(line.split(RegExp(r'\s+'))[1]);
          }
        }
        
        if (totalKB != null && freeKB != null) {
          final usedKB = totalKB - (availableKB ?? freeKB);
          final usagePercent = (usedKB / totalKB * 100);
          
          return {
            'memory_total': '${(totalKB / 1024 / 1024).toStringAsFixed(2)} GB',
            'memory_used': '${(usedKB / 1024 / 1024).toStringAsFixed(2)} GB',
            'memory_free': '${(freeKB / 1024 / 1024).toStringAsFixed(2)} GB',
            'memory_usage_percent': usagePercent.toStringAsFixed(1),
          };
        }
      }
    } catch (e) {
      _logger.w('Failed to get Linux memory info', error: e);
    }
    
    return {
      'memory_total': 'Unknown',
      'memory_used': 'Unknown',
      'memory_free': 'Unknown',
      'memory_usage_percent': 0.0,
    };
  }

  /// Get Android memory information (simplified)
  Future<Map<String, dynamic>> _getAndroidMemoryInfo() async {
    return {
      'memory_total': 'Android',
      'memory_used': 'Android',
      'memory_free': 'Android',
      'memory_usage_percent': 0.0,
    };
  }

  /// Get CPU information
  Future<Map<String, dynamic>> _getCpuInfo() async {
    try {
      if (Platform.isWindows) {
        return await _getWindowsCpuInfo();
      } else if (Platform.isLinux) {
        return await _getLinuxCpuInfo();
      } else {
        return {
          'cpu_usage_percent': 0.0,
          'cpu_cores': 'Unknown',
          'cpu_model': 'Unknown',
        };
      }
    } catch (e) {
      _logger.w('Failed to get CPU info', error: e);
      return {
        'cpu_usage_percent': 0.0,
        'cpu_cores': 'Error',
        'cpu_model': 'Error',
      };
    }
  }

  /// Get Windows CPU information
  Future<Map<String, dynamic>> _getWindowsCpuInfo() async {
    try {
      // Get CPU usage
      final usageResult = await Process.run('powershell', [
        '-Command',
        'Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average | Select-Object Average | ConvertTo-Json'
      ]);
      
      // Get CPU info
      final infoResult = await Process.run('powershell', [
        '-Command',
        'Get-WmiObject -Class Win32_Processor | Select-Object Name,NumberOfCores | ConvertTo-Json'
      ]);
      
      double cpuUsage = 0.0;
      String cpuModel = 'Unknown';
      int cpuCores = 0;
      
      if (usageResult.exitCode == 0) {
        final usageData = jsonDecode(usageResult.stdout);
        cpuUsage = double.tryParse(usageData['Average'].toString()) ?? 0.0;
      }
      
      if (infoResult.exitCode == 0) {
        final infoData = jsonDecode(infoResult.stdout);
        if (infoData is List && infoData.isNotEmpty) {
          cpuModel = infoData[0]['Name']?.toString() ?? 'Unknown';
          cpuCores = int.tryParse(infoData[0]['NumberOfCores'].toString()) ?? 0;
        } else if (infoData is Map) {
          cpuModel = infoData['Name']?.toString() ?? 'Unknown';
          cpuCores = int.tryParse(infoData['NumberOfCores'].toString()) ?? 0;
        }
      }
      
      return {
        'cpu_usage_percent': cpuUsage.toStringAsFixed(1),
        'cpu_cores': cpuCores.toString(),
        'cpu_model': cpuModel,
      };
    } catch (e) {
      _logger.w('Failed to get Windows CPU info', error: e);
      return {
        'cpu_usage_percent': 0.0,
        'cpu_cores': 'Error',
        'cpu_model': 'Error',
      };
    }
  }

  /// Get Linux CPU information
  Future<Map<String, dynamic>> _getLinuxCpuInfo() async {
    try {
      // Get CPU usage from /proc/stat
      final statFile = File('/proc/stat');
      if (await statFile.exists()) {
        final content = await statFile.readAsString();
        final lines = content.split('\n');
        final cpuLine = lines.firstWhere((line) => line.startsWith('cpu '), orElse: () => '');
        
        if (cpuLine.isNotEmpty) {
          final values = cpuLine.split(RegExp(r'\s+'));
          if (values.length >= 8) {
            final idle = int.tryParse(values[4]) ?? 0;
            final total = values.skip(1).take(7).map((v) => int.tryParse(v) ?? 0).reduce((a, b) => a + b);
            final usage = total > 0 ? ((total - idle) / total * 100) : 0.0;
            
            return {
              'cpu_usage_percent': usage.toStringAsFixed(1),
              'cpu_cores': Platform.numberOfProcessors.toString(),
              'cpu_model': 'Linux CPU',
            };
          }
        }
      }
    } catch (e) {
      _logger.w('Failed to get Linux CPU info', error: e);
    }
    
    return {
      'cpu_usage_percent': 0.0,
      'cpu_cores': Platform.numberOfProcessors.toString(),
      'cpu_model': 'Linux CPU',
    };
  }

  /// Get device information
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isWindows) {
        final windowsInfo = await _deviceInfo.windowsInfo;
        return {
          'device_name': windowsInfo.computerName,
          'device_model': 'Windows PC',
          'os_version': '${windowsInfo.productName} ${windowsInfo.displayVersion}',
        };
      } else if (Platform.isLinux) {
        return {
          'device_name': Platform.localHostname,
          'device_model': 'Linux PC',
          'os_version': Platform.operatingSystemVersion,
        };
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'device_name': androidInfo.model,
          'device_model': '${androidInfo.brand} ${androidInfo.model}',
          'os_version': 'Android ${androidInfo.version.release}',
        };
      }
    } catch (e) {
      _logger.w('Failed to get device info', error: e);
    }
    
    return {
      'device_name': 'Unknown',
      'device_model': 'Unknown',
      'os_version': Platform.operatingSystemVersion,
    };
  }

  /// Get battery information
  Future<Map<String, dynamic>> _getBatteryInfo() async {
    try {
      final batteryLevel = await _battery.batteryLevel;
      final batteryState = await _battery.batteryState;
      
      return {
        'battery_level': batteryLevel,
        'battery_state': batteryState.toString().split('.').last,
      };
    } catch (e) {
      _logger.w('Failed to get battery info', error: e);
      return {
        'battery_level': -1,
        'battery_state': 'unknown',
      };
    }
  }

  /// Get network information
  Future<Map<String, dynamic>> _getNetworkInfo() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      
      return {
        'connectivity': connectivityResult.map((e) => e.toString().split('.').last).toList(),
        'network_available': connectivityResult.isNotEmpty && !connectivityResult.contains(ConnectivityResult.none),
      };
    } catch (e) {
      _logger.w('Failed to get network info', error: e);
      return {
        'connectivity': ['unknown'],
        'network_available': false,
      };
    }
  }

  /// Get fallback system information when detailed info fails
  Map<String, dynamic> _getFallbackSystemInfo() {
    return {
      'platform': Platform.operatingSystem,
      'timestamp': DateTime.now().toIso8601String(),
      'memory_total': 'Unknown',
      'memory_used': 'Unknown',
      'memory_free': 'Unknown',
      'memory_usage_percent': 0.0,
      'cpu_usage_percent': 0.0,
      'cpu_cores': Platform.numberOfProcessors.toString(),
      'cpu_model': 'Unknown',
      'device_name': Platform.localHostname,
      'device_model': 'Unknown',
      'os_version': Platform.operatingSystemVersion,
      'battery_level': -1,
      'battery_state': 'unknown',
      'connectivity': ['unknown'],
      'network_available': false,
    };
  }
}

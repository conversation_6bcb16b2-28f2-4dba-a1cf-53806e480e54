# Kế Hoạch Triển Khai Chức Năng Đăng Nhập

## 📋 Tổng Quan

Kế hoạch triển khai chức năng đăng nhập cho c-face-terminal mobile app dựa trên pattern từ c-faces implementation.

## 🎯 Mục Tiêu Chính

1. **Dynamic Base URL Configuration**: Hỗ trợ chuyển đổi giữa On Cloud và On Premise mode
2. **Repository Pattern**: Sử dụng clean architecture với repository pattern
3. **Authentication State Management**: Quản lý trạng thái đăng nhập toàn ứng dụng
4. **API Integration**: Tích hợp với API endpoint `/api/v3.1/identity/login`
5. **Error Handling**: Xử lý lỗi và validation đầy đủ
6. **Post-Login Navigation**: Redirect đến dashboard sau khi login thành công sử dụng `goToDashboard() => context?.go(MobileRouteNames.dashboard)`

## 📊 Phân Tích Hiện Trạng

### ✅ Đã Có
- Login screen UI với toggle On Cloud/On Premise
- Base auth repository interface và implementation
- Login use case structure
- API client với token management
- Base auth provider structure

### ❌ Cần Triển Khai
- Dynamic base URL configuration logic
- Auth provider integration với login screen
- API endpoint update cho đúng specification
- Error handling và validation
- Token refresh mechanism

## 📋 Task Tracking Table

| Mã Task | Tiêu Đề | Priority | Status |
|:--------|:--------|:---------|:-------|
| LOGIN-001 | Cập nhật API Endpoints | High | completed |
| LOGIN-002 | Dynamic Base URL Configuration | High | completed |
| LOGIN-003 | Cập nhật Mobile Auth Provider | High | completed |
| LOGIN-004 | Integration với Login Use Case | Medium | completed |
| LOGIN-005 | Implement handleLogin Method | High | completed |
| LOGIN-006 | UI State Management | Medium | completed |
| LOGIN-007 | Auth Repository Implementation Update | High | completed |
| LOGIN-008 | Remote Data Source Update | High | pending |
| LOGIN-009 | Comprehensive Error Handling | Medium | pending |
| LOGIN-010 | Input Validation | Medium | pending |
| LOGIN-011 | Post-Login Navigation Setup | High | pending |
| LOGIN-012 | Unit Tests | Low | pending |
| LOGIN-013 | Integration Tests | Medium | pending |
| LOGIN-014 | Documentation Update | Low | pending |

> **Lưu ý**: Sau khi hoàn thành mỗi task chạy lệnh 'flutter analyze' và fix lỗi nếu có, nếu không còn lỗi thì cập nhật status thành `completed` và tạo summary trong folder `summary/`

## 🚀 Kế Hoạch Triển Khai

### Phase 1: Cập Nhật API Configuration (2-3 giờ)

#### Task LOGIN-001: Cập nhật API Endpoints
**File**: `lib/shared/services/api_endpoints.dart`
- Thay đổi từ `/auth/login` thành `/api/v3.1/identity/login`
- Đảm bảo tương thích với base URL configuration

#### Task LOGIN-002: Dynamic Base URL Configuration
**Files**: 
- `lib/shared/services/service_locator.dart`
- `lib/shared/core/config/app_config.dart`

**Triển khai**:
```dart
// Thêm method để lấy base URL theo environment
static String getBaseUrlByEnvironment(AppEnvironment environment) {
  switch (environment) {
    case AppEnvironment.development:
      return 'http://192.168.137.1:5000';
    case AppEnvironment.staging:
      return 'https://staging-api.c-faces.com';
    case AppEnvironment.production:
      return 'https://api.c-faces.com';
  }
}
```

### Phase 2: Auth Provider Enhancement (3-4 giờ)

#### Task LOGIN-003: Cập nhật Mobile Auth Provider
**File**: `lib/apps/mobile/presentation/providers/auth_provider.dart`

**Triển khai**:
- Kế thừa từ base auth provider
- Thêm dynamic base URL support
- Implement proper error handling
- Add loading states

#### Task LOGIN-004: Integration với Login Use Case
**File**: `lib/shared/domain/use_cases/auth/login_use_case.dart`
- Đảm bảo validation đầy đủ
- Error handling cho network issues
- Support cho dynamic base URL

### Phase 3: Login Screen Integration (2-3 giờ)

#### Task LOGIN-005: Implement handleLogin Method
**File**: `lib/apps/mobile/presentation/screens/login_screen.dart`

**Logic Flow**:
```dart
void _handleLogin() async {
  if (!_formKey.currentState!.validate()) return;

  // Determine base URL based on toggle
  String baseUrl;
  if (_selectedToggle == 'On Cloud') {
    baseUrl = ApiEndpoints.getBaseUrlByEnvironment(environment);
  } else {
    baseUrl = _serverAddressController.text;
  }

  // Update API client base URL
  await _updateApiClientBaseUrl(baseUrl);

  // Perform login
  final success = await context.read<AuthProvider>().login(
    userName: _usernameController.text,
    password: _passwordController.text,
  );

  if (success) {
    // Redirect to dashboard after successful login
    goToDashboard();
  }
}

// Navigation helper method
static void goToDashboard() => context?.go(MobileRouteNames.dashboard);
```

#### Task LOGIN-006: UI State Management
- Loading states during login
- Error message display
- Form validation
- Toggle state management

### Phase 4: Repository Layer Enhancement (2-3 giờ)

#### Task LOGIN-007: Auth Repository Implementation Update
**File**: `lib/shared/data/repositories/auth_repository_impl.dart`

**Enhancements**:
- Dynamic base URL support
- Proper error mapping
- Token storage và retrieval
- Network error handling

#### Task LOGIN-008: Remote Data Source Update
**File**: `lib/shared/data/data_sources/auth_remote_data_source.dart`

**API Integration**:
```dart
Future<AuthResultModel> login({
  required String userName,
  required String password,
}) async {
  final response = await apiClient.post(
    ApiEndpoints.login,
    body: {
      'username': userName,
      'password': password,
    },
  );
  
  return AuthResultModel.fromJson(response['data']);
}
```

### Phase 5: Error Handling & Validation (1-2 giờ)

#### Task LOGIN-009: Comprehensive Error Handling
- Network connectivity errors
- Server errors (4xx, 5xx)
- Validation errors
- Authentication failures

#### Task LOGIN-010: Input Validation
- Username/email format validation
- Password strength requirements
- Server address validation for On Premise mode

#### Task LOGIN-011: Post-Login Navigation Setup
- Implement `goToDashboard()` method
- Setup navigation to `MobileRouteNames.dashboard`
- Handle navigation context properly
- Test navigation flow

### Phase 6: Testing & Documentation (2-3 giờ)

#### Task LOGIN-012: Unit Tests
- Login use case tests
- Repository tests
- Provider tests

#### Task LOGIN-013: Integration Tests
- End-to-end login flow
- Error scenarios
- Toggle functionality
- Navigation testing

#### Task LOGIN-014: Documentation Update
- API documentation
- Code comments
- Usage examples
- Task summaries

## 📅 Timeline Estimate

| Phase | Thời Gian | Mô Tả |
|:------|:----------|:------|
| Phase 1 | 2-3 giờ | API Configuration |
| Phase 2 | 3-4 giờ | Auth Provider Enhancement |
| Phase 3 | 2-3 giờ | Login Screen Integration |
| Phase 4 | 2-3 giờ | Repository Layer |
| Phase 5 | 1-2 giờ | Error Handling |
| Phase 6 | 2-3 giờ | Testing & Documentation |
| **Tổng** | **12-18 giờ** | **Hoàn thành toàn bộ** |

## 🔧 Dependencies

### Required Packages
- `dartz`: For Either type (đã có)
- `provider`: For state management (đã có)
- `go_router`: For navigation (đã có)
- `http`: For API calls (đã có)

### External Dependencies
- Backend API endpoint `/api/v3.1/identity/login`
- Network connectivity
- Secure storage for tokens

## ⚠️ Risks & Mitigation

| Risk | Impact | Mitigation |
|:-----|:-------|:-----------|
| API endpoint changes | High | Comprehensive testing với mock data |
| Network connectivity issues | Medium | Proper error handling và retry logic |
| Token expiration | Medium | Implement refresh token mechanism |
| UI state management complexity | Low | Use proven provider pattern |

## 📋 Acceptance Criteria

### Functional Requirements
- [ ] User có thể toggle giữa On Cloud và On Premise mode
- [ ] Login thành công với credentials hợp lệ
- [ ] Error messages hiển thị rõ ràng cho các trường hợp lỗi
- [ ] Navigation đến dashboard screen sau login thành công sử dụng goToDashboard()
- [ ] Token được lưu trữ an toàn và sử dụng cho subsequent API calls

### Non-Functional Requirements
- [ ] Response time < 3 seconds cho login request
- [ ] UI responsive và không bị freeze during login
- [ ] Error handling graceful cho tất cả edge cases
- [ ] Code coverage >= 80% cho login functionality

## 🔄 Next Steps

1. Review và approve implementation plan
2. Setup development environment
3. Begin Phase 1 implementation
4. Regular progress reviews sau mỗi phase
5. Integration testing trước khi deploy

## 📝 Notes

- Tất cả API calls phải sử dụng base URL được chọn từ toggle
- Error messages phải được localize (Vietnamese)
- Cần maintain backward compatibility với existing code
- Follow existing code style và architecture patterns

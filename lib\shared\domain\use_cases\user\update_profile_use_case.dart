import 'package:dartz/dartz.dart';
import '../../entities/user/user.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/errors/failures.dart';

class UpdateProfileUseCase {
  final AuthRepository repository;

  UpdateProfileUseCase(this.repository);

  Future<Either<Failure, User>> call(UpdateProfileParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to update profile
    return await repository.updateProfile(
      name: params.name,
      email: params.email,
      phone: params.phone,
      dob: params.dob,
      gender: params.gender,
    );
  }

  ValidationFailure? _validateParams(UpdateProfileParams params) {
    final errors = <String, List<String>>{};

    // Validate email if provided
    if (params.email != null && params.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(params.email!)) {
        errors['email'] = ['Invalid email format'];
      }
    }

    // Validate phone if provided
    if (params.phone != null && params.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
      if (!phoneRegex.hasMatch(params.phone!)) {
        errors['phone'] = ['Invalid phone number format'];
      }
    }

    // Validate name if provided
    if (params.name != null && params.name!.isNotEmpty) {
      if (params.name!.length < 2) {
        errors['name'] = ['Name must be at least 2 characters'];
      }
    }

    // Validate gender if provided
    if (params.gender != null && params.gender!.isNotEmpty) {
      final validGenders = ['male', 'female', 'other'];
      if (!validGenders.contains(params.gender!.toLowerCase())) {
        errors['gender'] = ['Gender must be one of: male, female, other'];
      }
    }

    // Validate date of birth if provided
    if (params.dob != null) {
      final now = DateTime.now();
      final age = now.difference(params.dob!).inDays / 365;
      if (age < 0) {
        errors['dob'] = ['Date of birth cannot be in the future'];
      } else if (age > 150) {
        errors['dob'] = ['Invalid date of birth'];
      }
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Invalid profile parameters',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class UpdateProfileParams {
  final String? name;
  final String? email;
  final String? phone;
  final DateTime? dob;
  final String? gender;

  const UpdateProfileParams({
    this.name,
    this.email,
    this.phone,
    this.dob,
    this.gender,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpdateProfileParams &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.dob == dob &&
        other.gender == gender;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        dob.hashCode ^
        gender.hashCode;
  }

  @override
  String toString() {
    return 'UpdateProfileParams(name: $name, email: $email, phone: $phone, dob: $dob, gender: $gender)';
  }
}

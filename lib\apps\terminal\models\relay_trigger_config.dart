import 'package:flutter/foundation.dart';

/// Relay trigger scenarios
enum RelayTriggerScenario {
  faceDetected,           // <PERSON>hi phát hiện khuôn mặt
  faceNotDetected,        // <PERSON>hi không còn phát hiện khuôn mặt
  faceRecognitionStart,   // Khi bắt đầu nhận diện
  accessGranted,          // Khi được phép truy cập
  accessDenied,           // Khi bị từ chối truy cập
  noFaceTimeout,          // Khi không có khuôn mặt quá lâu
  systemStartup,          // Khi khởi động hệ thống
  emergencyOverride,      // Trường hợp khẩn cấp
}

/// Relay configuration for each scenario
class RelayTriggerAction {
  final int relayIndex;           // R0, R1, R2, R3
  final String relayName;         // Terminal, LED, Camera, Backup
  final int durationSeconds;      // Thời gian trigger (giây)
  final bool isEnabled;           // <PERSON><PERSON> kích hoạt không
  final int priority;             // <PERSON><PERSON> ưu tiên (0 = cao nhất)
  final int delayMs;              // Delay trước khi trigger (ms)
  final String description;       // <PERSON><PERSON> tả chức năng

  const RelayTriggerAction({
    required this.relayIndex,
    required this.relayName,
    required this.durationSeconds,
    this.isEnabled = true,
    this.priority = 0,
    this.delayMs = 0,
    this.description = '',
  });

  Map<String, dynamic> toJson() => {
    'relayIndex': relayIndex,
    'relayName': relayName,
    'durationSeconds': durationSeconds,
    'isEnabled': isEnabled,
    'priority': priority,
    'delayMs': delayMs,
    'description': description,
  };

  factory RelayTriggerAction.fromJson(Map<String, dynamic> json) => RelayTriggerAction(
    relayIndex: json['relayIndex'] ?? 0,
    relayName: json['relayName'] ?? '',
    durationSeconds: json['durationSeconds'] ?? 3,
    isEnabled: json['isEnabled'] ?? true,
    priority: json['priority'] ?? 0,
    delayMs: json['delayMs'] ?? 0,
    description: json['description'] ?? '',
  );

  RelayTriggerAction copyWith({
    int? relayIndex,
    String? relayName,
    int? durationSeconds,
    bool? isEnabled,
    int? priority,
    int? delayMs,
    String? description,
  }) => RelayTriggerAction(
    relayIndex: relayIndex ?? this.relayIndex,
    relayName: relayName ?? this.relayName,
    durationSeconds: durationSeconds ?? this.durationSeconds,
    isEnabled: isEnabled ?? this.isEnabled,
    priority: priority ?? this.priority,
    delayMs: delayMs ?? this.delayMs,
    description: description ?? this.description,
  );
}

/// Complete relay trigger configuration
class RelayTriggerConfig {
  final Map<RelayTriggerScenario, List<RelayTriggerAction>> scenarios;
  final bool isEnabled;
  final String deviceId;
  final int maxConcurrentTriggers;

  const RelayTriggerConfig({
    required this.scenarios,
    this.isEnabled = true,
    this.deviceId = 'terminal_001',
    this.maxConcurrentTriggers = 4,
  });

  /// Default configuration with common scenarios
  /// R0: LED Lighting, R1: Terminal Door, R2-R3: Backup/Side Effects
  static RelayTriggerConfig get defaultConfig => RelayTriggerConfig(
    scenarios: {
      // Khi phát hiện khuôn mặt -> Bật R0 (LED) để tăng độ sáng
      RelayTriggerScenario.faceDetected: [
        RelayTriggerAction(
          relayIndex: 0,
          relayName: 'LED Lighting',
          durationSeconds: 0, // Sẽ tắt khi không còn face
          priority: 1,
          delayMs: 0,
          description: 'Bật LED khi phát hiện khuôn mặt (R0)',
        ),
      ],

      // Khi không còn phát hiện khuôn mặt -> Tắt R0 (LED)
      RelayTriggerScenario.faceNotDetected: [
        RelayTriggerAction(
          relayIndex: 0,
          relayName: 'LED Off',
          durationSeconds: 1,
          priority: 1,
          delayMs: 0,
          description: 'Tắt LED khi không còn phát hiện khuôn mặt (R0)',
        ),
      ],

      // Khi bắt đầu nhận diện -> Kích hoạt backup systems (R2, R3)
      RelayTriggerScenario.faceRecognitionStart: [
        RelayTriggerAction(
          relayIndex: 2,
          relayName: 'Backup Sensor 1',
          durationSeconds: 15,
          priority: 2,
          delayMs: 0,
          description: 'Kích hoạt cảm biến phụ trợ 1 (R2)',
        ),
        RelayTriggerAction(
          relayIndex: 3,
          relayName: 'Backup Sensor 2',
          durationSeconds: 15,
          priority: 3,
          delayMs: 500,
          description: 'Kích hoạt cảm biến phụ trợ 2 (R3)',
        ),
      ],

      // Khi được phép truy cập -> Mở R1 (Terminal) trong 5-10 giây
      RelayTriggerScenario.accessGranted: [
        RelayTriggerAction(
          relayIndex: 1,
          relayName: 'Terminal Door',
          durationSeconds: 7, // 5-10 giây, chọn 7 giây làm mặc định
          priority: 0, // Cao nhất
          delayMs: 0,
          description: 'Mở cửa terminal (R1) - quan trọng nhất',
        ),
      ],

      // Khi bị từ chối -> LED cảnh báo (R0)
      RelayTriggerScenario.accessDenied: [
        RelayTriggerAction(
          relayIndex: 0,
          relayName: 'LED Warning',
          durationSeconds: 2,
          priority: 1,
          delayMs: 0,
          description: 'LED cảnh báo từ chối truy cập (R0)',
        ),
      ],

      // Khi không có khuôn mặt quá lâu -> Tắt R0 (LED)
      RelayTriggerScenario.noFaceTimeout: [
        RelayTriggerAction(
          relayIndex: 0,
          relayName: 'LED Off',
          durationSeconds: 1,
          priority: 1,
          delayMs: 0,
          description: 'Tắt LED khi không có khuôn mặt (R0)',
        ),
      ],
    },
  );

  Map<String, dynamic> toJson() => {
    'scenarios': scenarios.map((key, value) => MapEntry(
      key.name,
      value.map((action) => action.toJson()).toList(),
    )),
    'isEnabled': isEnabled,
    'deviceId': deviceId,
    'maxConcurrentTriggers': maxConcurrentTriggers,
  };

  factory RelayTriggerConfig.fromJson(Map<String, dynamic> json) {
    final scenariosJson = json['scenarios'] as Map<String, dynamic>? ?? {};
    final scenarios = <RelayTriggerScenario, List<RelayTriggerAction>>{};
    
    for (final entry in scenariosJson.entries) {
      final scenario = RelayTriggerScenario.values.firstWhere(
        (s) => s.name == entry.key,
        orElse: () => RelayTriggerScenario.faceDetected,
      );
      final actions = (entry.value as List)
          .map((actionJson) => RelayTriggerAction.fromJson(actionJson))
          .toList();
      scenarios[scenario] = actions;
    }

    return RelayTriggerConfig(
      scenarios: scenarios,
      isEnabled: json['isEnabled'] ?? true,
      deviceId: json['deviceId'] ?? 'terminal_001',
      maxConcurrentTriggers: json['maxConcurrentTriggers'] ?? 4,
    );
  }

  RelayTriggerConfig copyWith({
    Map<RelayTriggerScenario, List<RelayTriggerAction>>? scenarios,
    bool? isEnabled,
    String? deviceId,
    int? maxConcurrentTriggers,
  }) => RelayTriggerConfig(
    scenarios: scenarios ?? this.scenarios,
    isEnabled: isEnabled ?? this.isEnabled,
    deviceId: deviceId ?? this.deviceId,
    maxConcurrentTriggers: maxConcurrentTriggers ?? this.maxConcurrentTriggers,
  );

  /// Get actions for a specific scenario, sorted by priority
  List<RelayTriggerAction> getActionsForScenario(RelayTriggerScenario scenario) {
    final actions = scenarios[scenario] ?? [];
    return actions.where((action) => action.isEnabled).toList()
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  /// Check if any relay is configured for a scenario
  bool hasActionsForScenario(RelayTriggerScenario scenario) {
    return getActionsForScenario(scenario).isNotEmpty;
  }
}

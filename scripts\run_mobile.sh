#!/bin/bash

echo "🚀 Running C-Face Mobile App..."
echo "==============================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Check for connected devices
echo "📱 Checking for connected devices..."
flutter devices

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Run mobile app in debug mode
echo "🏃 Starting mobile app in debug mode..."
echo "📱 Target: lib/apps/mobile/main_mobile.dart"
echo "🔧 Mode: Debug (hot reload enabled)"
echo ""

# Try Android first (default)
echo "🤖 Attempting to run on Android device..."
echo "Press 'r' to hot reload, 'R' to hot restart, 'q' to quit"
echo ""

flutter run --target lib/apps/mobile/main_mobile.dart --debug --flavor mobile

# If Android fails, suggest web alternative
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Android run failed. You can try running on web instead:"
    echo "   flutter run --target lib/apps/mobile/main_mobile.dart -d chrome --flavor mobile"
    echo ""
    echo "💡 To fix Android issues:"
    echo "   1. Check connected devices: flutter devices"
    echo "   2. Check Android setup: flutter doctor"
    echo "   3. Try cleaning: flutter clean && flutter pub get"
fi

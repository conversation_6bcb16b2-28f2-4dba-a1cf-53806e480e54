import 'package:flutter/foundation.dart';
import '../../../core/network/api_client.dart';
import '../../../services/api_endpoints.dart';
import '../../../core/errors/exceptions.dart';
import '../../models/user/user_model.dart';

abstract class UserRemoteDataSource {
  /// Get all users with pagination and optional filtering
  Future<List<UserModel>> getUsers({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? unitId,
    String? memberRoleId,
  });

  /// Get user by ID
  Future<UserModel> getUserById(String userId);

  /// Get users by unit ID
  Future<List<UserModel>> getUsersByUnit(String unitId);

  /// Get users by member role ID
  Future<List<UserModel>> getUsersByRole(String memberRoleId);

  /// Create a new user
  Future<UserModel> createUser({
    required String username,
    required String password,
    required String name,
    required String unitId,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? memberRoleId,
  });

  /// Update user information
  Future<UserModel> updateUser({
    required String userId,
    String? name,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? unitId,
    String? memberRoleId,
  });

  /// Delete user
  Future<void> deleteUser(String userId);

  /// Upload user avatar
  Future<UserModel> uploadAvatar({
    required String userId,
    required String fileName,
    required String fileData,
  });
}

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final ApiClient apiClient;

  UserRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<UserModel>> getUsers({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? unitId,
    String? memberRoleId,
  }) async {
    try {
      // API uses pageIndex and pageSize instead of skip/limit
      final queryParameters = <String, dynamic>{
        'pageSize': limit.toString(),
        'pageIndex': page.toString(),
        if (sortBy != null) 'sortBy': sortBy,
        if (sortDirection != null) 'sortDirection': sortDirection,
        if (search != null) 'search': search,
        if (unitId != null) 'unit_id': unitId, // Keep as unit_id for query params
        if (memberRoleId != null) 'member_role_id': memberRoleId, // Keep as member_role_id for query params
      };

      final response = await apiClient.get(
        ApiEndpoints.users,
        queryParameters: queryParameters,
      );

      if (response['success'] == true && response['data'] != null) {
        // API returns data.items array with users
        final usersData = response['data']['items'] as List<dynamic>? ?? [];

        return usersData
            .map((userData) => UserModel.fromJson(userData as Map<String, dynamic>))
            .toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get users';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to get users: $e');
    }
  }

  @override
  Future<UserModel> getUserById(String userId) async {
    try {
      final response = await apiClient.get(ApiEndpoints.userDetail(userId));

      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get user';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to get user: $e');
    }
  }

  @override
  Future<List<UserModel>> getUsersByUnit(String unitId) async {
    try {
      final response = await apiClient.get('${ApiEndpoints.users}/unit/$unitId');

      if (response['success'] == true && response['data'] != null) {
        final usersData = response['data']['users'] as List<dynamic>? ??
                         response['data'] as List<dynamic>? ?? [];

        return usersData
            .map((userData) => UserModel.fromJson(userData as Map<String, dynamic>))
            .toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get users by unit';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to get users by unit: $e');
    }
  }

  @override
  Future<List<UserModel>> getUsersByRole(String memberRoleId) async {
    try {
      final response = await apiClient.get('${ApiEndpoints.users}/role/$memberRoleId');

      if (response['success'] == true && response['data'] != null) {
        final usersData = response['data']['users'] as List<dynamic>? ??
                         response['data'] as List<dynamic>? ?? [];

        return usersData
            .map((userData) => UserModel.fromJson(userData as Map<String, dynamic>))
            .toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get users by role';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to get users by role: $e');
    }
  }

  @override
  Future<UserModel> createUser({
    required String username,
    required String password,
    required String name,
    required String unitId,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? memberRoleId,
  }) async {
    try {
      final body = <String, dynamic>{
        'username': username,
        'password': password,
        'name': name,
        'current_unit_id': unitId,
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
        if (dob != null) 'dob': dob,
        if (gender != null) 'gender': gender,
        if (memberRoleId != null) 'roleId': memberRoleId,
      };

      final response = await apiClient.post(
        ApiEndpoints.createUser,
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to create user';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to create user: $e');
    }
  }

  @override
  Future<UserModel> updateUser({
    required String userId,
    String? name,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? unitId,
    String? memberRoleId,
  }) async {
    try {
      final body = <String, dynamic>{
        if (name != null) 'name': name,
        if (email != null) 'email': email,
        if (phone != null) 'phone': phone,
        if (dob != null) 'dob': dob,
        if (gender != null) 'gender': gender,
        if (unitId != null) 'current_unit_id': unitId,
        if (memberRoleId != null) 'roleId': memberRoleId,
      };

      final response = await apiClient.put(
        ApiEndpoints.updateUser(userId),
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update user';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to update user: $e');
    }
  }

  @override
  Future<void> deleteUser(String userId) async {
    try {
      final response = await apiClient.delete(ApiEndpoints.deleteUser(userId));

      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Failed to delete user';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to delete user: $e');
    }
  }

  @override
  Future<UserModel> uploadAvatar({
    required String userId,
    required String fileName,
    required String fileData,
  }) async {
    try {
      final body = {
        'fileName': fileName,
        'fileData': fileData,
      };

      final response = await apiClient.post(
        '${ApiEndpoints.users}/$userId/avatar',
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        final userData = response['data']['user'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to upload avatar';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is ServerException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to upload avatar: $e');
    }
  }
}

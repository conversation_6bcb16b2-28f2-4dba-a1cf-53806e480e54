<?xml version="1.0" encoding="utf-8"?>
<layout>

    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:id="@+id/rg_ft_orient"
        tools:context=".ui.ChooseFaceTrackOrientActivity">
        <RadioButton
            android:id="@+id/rb_ft_op_0"
            android:text="@string/ft_op_0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <RadioButton
            android:id="@+id/rb_ft_op_90"
            android:text="@string/ft_op_90"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <RadioButton
            android:id="@+id/rb_ft_op_180"
            android:text="@string/ft_op_180"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <RadioButton
            android:id="@+id/rb_ft_op_270"
            android:text="@string/ft_op_270"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <RadioButton
            android:id="@+id/rb_ft_op_all"
            android:text="@string/ft_op_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </RadioGroup>
</layout>
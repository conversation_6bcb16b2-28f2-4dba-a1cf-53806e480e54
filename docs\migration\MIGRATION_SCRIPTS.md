# Migration Scripts & Templates

## 1. Directory Setup Script

### setup_structure.sh
```bash
#!/bin/bash

# Create multi-app directory structure
echo "Creating multi-app directory structure..."

# Apps structure
mkdir -p lib/apps/mobile/presentation/{screens,widgets,providers}
mkdir -p lib/apps/mobile/routes
mkdir -p lib/apps/mobile/config
mkdir -p lib/apps/terminal/presentation/{screens,widgets,providers}
mkdir -p lib/apps/terminal/routes
mkdir -p lib/apps/terminal/config

# Shared structure
mkdir -p lib/shared/core/{base,config,constants,di,errors,network,storage,utils}
mkdir -p lib/shared/data/{data_sources,models,repositories}
mkdir -p lib/shared/domain/{entities,repositories,use_cases}
mkdir -p lib/shared/presentation/{widgets,themes,providers}

# Test structure
mkdir -p test/shared/{core,data,domain}
mkdir -p test/apps/mobile
mkdir -p test/apps/terminal

echo "Directory structure created successfully!"
```

## 2. Import Path Migration Script

### migrate_imports.py
```python
import os
import re

def migrate_imports(file_path):
    """Migrate import paths from single app to multi-app structure"""
    
    with open(file_path, 'r') as file:
        content = file.read()
    
    # Migration patterns
    patterns = [
        # Core imports
        (r"import '../../core/", "import '../../../shared/core/"),
        (r"import '../core/", "import '../../shared/core/"),
        (r"import 'core/", "import 'shared/core/"),
        
        # Domain imports
        (r"import '../../domain/", "import '../../../shared/domain/"),
        (r"import '../domain/", "import '../../shared/domain/"),
        (r"import 'domain/", "import 'shared/domain/"),
        
        # Data imports
        (r"import '../../data/", "import '../../../shared/data/"),
        (r"import '../data/", "import '../../shared/data/"),
        (r"import 'data/", "import 'shared/data/"),
    ]
    
    # Apply migrations
    for old_pattern, new_pattern in patterns:
        content = re.sub(old_pattern, new_pattern, content)
    
    # Write back
    with open(file_path, 'w') as file:
        file.write(content)

def migrate_directory(directory):
    """Migrate all Dart files in directory"""
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                print(f"Migrating: {file_path}")
                migrate_imports(file_path)

# Usage
if __name__ == "__main__":
    migrate_directory("lib/shared")
    migrate_directory("lib/apps")
    print("Import migration completed!")
```

## 3. App Entry Point Templates

### lib/apps/mobile/main_mobile.dart
```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../shared/core/di/shared_service_locator.dart';
import 'config/mobile_config.dart';
import 'di/mobile_service_locator.dart';
import 'presentation/providers/mobile_auth_provider.dart';
import 'presentation/providers/mobile_navigation_provider.dart';
import 'routes/mobile_app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Setup dependencies
  await MobileServiceLocator.setup();
  
  runApp(const MobileApp());
}

class MobileApp extends StatelessWidget {
  const MobileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => MobileAuthProvider()),
        ChangeNotifierProvider(create: (_) => MobileNavigationProvider()),
      ],
      child: MaterialApp.router(
        title: 'C-Faces Mobile',
        theme: MobileConfig.theme,
        routerConfig: MobileAppRouter.router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
```

### lib/apps/terminal/main_terminal.dart
```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../shared/core/di/shared_service_locator.dart';
import 'config/terminal_config.dart';
import 'di/terminal_service_locator.dart';
import 'presentation/providers/terminal_auth_provider.dart';
import 'presentation/providers/kiosk_mode_provider.dart';
import 'routes/terminal_app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Setup dependencies
  await TerminalServiceLocator.setup();
  
  // Enable kiosk mode
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  
  runApp(const TerminalApp());
}

class TerminalApp extends StatelessWidget {
  const TerminalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => TerminalAuthProvider()),
        ChangeNotifierProvider(create: (_) => KioskModeProvider()),
      ],
      child: MaterialApp.router(
        title: 'C-Faces Terminal',
        theme: TerminalConfig.theme,
        routerConfig: TerminalAppRouter.router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
```

## 4. Shared Service Locator Template

### lib/shared/core/di/shared_service_locator.dart
```dart
import 'package:get_it/get_it.dart';

import '../network/api_client.dart';
import '../storage/secure_storage_service.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../data/data_sources/remote/auth_remote_data_source.dart';
import '../../data/data_sources/remote/user_remote_data_source.dart';
import '../../data/data_sources/local/auth_local_data_source.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/use_cases/auth/login_use_case.dart';
import '../../domain/use_cases/auth/logout_use_case.dart';
import '../../domain/use_cases/user/get_users_use_case.dart';

class SharedServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  
  static Future<void> setup() async {
    // Core services
    _getIt.registerLazySingleton<ApiClient>(() => ApiClient());
    _getIt.registerLazySingleton<SecureStorageService>(() => SecureStorageService());
    
    // Data sources
    _getIt.registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(apiClient: _getIt()),
    );
    _getIt.registerLazySingleton<UserRemoteDataSource>(
      () => UserRemoteDataSourceImpl(apiClient: _getIt()),
    );
    _getIt.registerLazySingleton<AuthLocalDataSource>(
      () => AuthLocalDataSourceImpl(storageService: _getIt()),
    );
    
    // Repositories
    _getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: _getIt(),
        localDataSource: _getIt(),
        apiClient: _getIt(),
      ),
    );
    _getIt.registerLazySingleton<UserRepository>(
      () => UserRepositoryImpl(remoteDataSource: _getIt()),
    );
    
    // Use cases
    _getIt.registerLazySingleton<LoginUseCase>(
      () => LoginUseCase(_getIt<AuthRepository>()),
    );
    _getIt.registerLazySingleton<LogoutUseCase>(
      () => LogoutUseCase(_getIt<AuthRepository>()),
    );
    _getIt.registerLazySingleton<GetUsersUseCase>(
      () => GetUsersUseCase(_getIt<UserRepository>()),
    );
  }
  
  static T get<T extends Object>() => _getIt.get<T>();
}
```

## 5. Base Provider Template

### lib/shared/presentation/providers/base_auth_provider.dart
```dart
import 'package:flutter/foundation.dart';
import 'package:dartz/dartz.dart';

import '../../core/errors/failures.dart';
import '../../domain/entities/auth/auth_result.dart';
import '../../domain/entities/user/user.dart';
import '../../domain/use_cases/auth/login_use_case.dart';
import '../../domain/use_cases/auth/logout_use_case.dart';
import '../../domain/repositories/auth_repository.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

abstract class BaseAuthProvider extends ChangeNotifier {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final AuthRepository authRepository;

  BaseAuthProvider({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.authRepository,
  }) {
    _checkAuthStatus();
  }

  // State
  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  // Abstract methods for app-specific behavior
  void onLoginSuccess(AuthResult authResult);
  void onLogoutSuccess();

  // Shared login logic
  Future<bool> login({
    required String userName,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await loginUseCase(
        LoginParams(userName: userName, password: password),
      );

      return result.fold(
        (failure) {
          _handleFailure(failure);
          return false;
        },
        (authResult) {
          _handleLoginSuccess(authResult);
          onLoginSuccess(authResult); // App-specific behavior
          return true;
        },
      );
    } catch (e) {
      _setError('Unexpected error occurred: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Shared logout logic
  Future<void> logout() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await logoutUseCase();
      result.fold(
        (failure) => _handleLogoutSuccess(),
        (_) => _handleLogoutSuccess(),
      );
      onLogoutSuccess(); // App-specific behavior
    } catch (e) {
      _handleLogoutSuccess();
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _handleLoginSuccess(AuthResult authResult) {
    _user = authResult.user;
    _setStatus(AuthStatus.authenticated);
    _clearError();
  }

  void _handleLogoutSuccess() {
    _user = null;
    _setStatus(AuthStatus.unauthenticated);
    _clearError();
  }

  void _handleFailure(Failure failure) {
    _setStatus(AuthStatus.error);
    if (failure is ValidationFailure && failure.fieldErrors != null) {
      final errorMessages = failure.fieldErrors!.entries
          .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
          .join('\n');
      _setError(errorMessages);
    } else {
      _setError(failure.message);
    }
  }

  Future<void> _checkAuthStatus() async {
    _setLoading(true);
    try {
      final isAuthenticated = await authRepository.isAuthenticated();
      if (isAuthenticated) {
        final localUserResult = await authRepository.getCurrentUser();
        await localUserResult.fold(
          (failure) async {
            await authRepository.clearAuthData();
            _setStatus(AuthStatus.unauthenticated);
          },
          (user) async {
            _user = user;
            _setStatus(AuthStatus.authenticated);
            _setLoading(false);
            _verifyTokenInBackground();
          },
        );
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }
    } catch (e) {
      _setStatus(AuthStatus.unauthenticated);
    } finally {
      if (_status != AuthStatus.authenticated) {
        _setLoading(false);
      }
    }
  }

  void _verifyTokenInBackground() {
    Future.microtask(() async {
      try {
        final verifyResult = await authRepository.verifyToken();
        verifyResult.fold(
          (failure) {
            if (kDebugMode) {
              debugPrint('Token verification failed: ${failure.message}');
            }
          },
          (isValid) {
            if (!isValid) {
              logout();
            }
          },
        );
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Network error during token verification: $e');
        }
      }
    });
  }

  void _setStatus(AuthStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _setStatus(AuthStatus.error);
  }

  void _clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _setStatus(
        _user != null ? AuthStatus.authenticated : AuthStatus.unauthenticated,
      );
    }
  }

  void clearError() {
    _clearError();
  }
}
```

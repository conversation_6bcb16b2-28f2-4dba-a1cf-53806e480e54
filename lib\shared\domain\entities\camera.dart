class Camera {
  final String id;
  final String name;
  final String ipAddress;
  final String rtspUrl;
  final String previewImageUrl;
  final String createdBy;
  final DateTime createdAt;

  Camera({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.rtspUrl,
    required this.previewImageUrl,
    required this.createdBy,
    required this.createdAt,
  });
}

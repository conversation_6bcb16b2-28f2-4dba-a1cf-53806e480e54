import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// Device authentication and security utilities
class <PERSON>ceAuth {
  /// Generate a secure device ID
  static String generateDeviceId({String? prefix}) {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    final deviceId = base64Url.encode(bytes).replaceAll('=', '');
    return prefix != null ? '$prefix-$deviceId' : deviceId;
  }

  /// Generate hardware hash (simulated for Flutter apps)
  static String generateHardwareHash() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return sha256.convert(bytes).toString();
  }

  /// Create HMAC signature for request
  static String createHmacSignature({
    required String secretKey,
    required String deviceId,
    required String action,
    required int timestamp,
    Map<String, dynamic>? additionalData,
  }) {
    final data = {
      'device_id': deviceId,
      'action': action,
      'timestamp': timestamp,
      if (additionalData != null) ...additionalData,
    };
    
    final message = _canonicalizeData(data);
    final key = utf8.encode(secretKey);
    final messageBytes = utf8.encode(message);
    
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(messageBytes);
    
    return digest.toString();
  }

  /// Verify HMAC signature
  static bool verifyHmacSignature({
    required String secretKey,
    required String signature,
    required String deviceId,
    required String action,
    required int timestamp,
    Map<String, dynamic>? additionalData,
  }) {
    final expectedSignature = createHmacSignature(
      secretKey: secretKey,
      deviceId: deviceId,
      action: action,
      timestamp: timestamp,
      additionalData: additionalData,
    );
    
    return signature == expectedSignature;
  }

  /// Check if timestamp is within acceptable range (prevent replay attacks)
  static bool isTimestampValid(int timestamp, {int toleranceSeconds = 300}) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final diff = (now - timestamp).abs();
    return diff <= toleranceSeconds;
  }

  /// Canonicalize data for consistent signing
  static String _canonicalizeData(Map<String, dynamic> data) {
    final sortedKeys = data.keys.toList()..sort();
    final parts = <String>[];
    
    for (final key in sortedKeys) {
      final value = data[key];
      if (value != null) {
        parts.add('$key=${value.toString()}');
      }
    }
    
    return parts.join('&');
  }

  /// Generate a secure random string
  static String generateSecureToken({int length = 32}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Parse JWT payload (basic parsing, not verification)
  static Map<String, dynamic>? parseJwtPayload(String jwt) {
    try {
      final parts = jwt.split('.');
      if (parts.length != 3) return null;
      
      final payload = parts[1];
      // Add padding if needed
      final paddedPayload = payload + '=' * (4 - payload.length % 4);
      final decoded = base64Url.decode(paddedPayload);
      final jsonString = utf8.decode(decoded);
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// Check if JWT is expired
  static bool isJwtExpired(String jwt) {
    final payload = parseJwtPayload(jwt);
    if (payload == null) return true;
    
    final exp = payload['exp'] as int?;
    if (exp == null) return false;
    
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return now >= exp;
  }

  /// Extract device ID from JWT
  static String? getDeviceIdFromJwt(String jwt) {
    final payload = parseJwtPayload(jwt);
    return payload?['device_id'] as String?;
  }

  /// Create device fingerprint
  static String createDeviceFingerprint({
    required String deviceId,
    required String deviceType,
    String? hardwareHash,
    String? appVersion,
  }) {
    final data = {
      'device_id': deviceId,
      'device_type': deviceType,
      if (hardwareHash != null) 'hardware_hash': hardwareHash,
      if (appVersion != null) 'app_version': appVersion,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    final message = _canonicalizeData(data);
    return sha256.convert(utf8.encode(message)).toString();
  }
}

/// Device registration request model
class DeviceRegistrationRequest {
  final String deviceId;
  final String deviceType;
  final String? publicKey;
  final String? hardwareHash;
  final String? deviceName;
  final String? appVersion;

  const DeviceRegistrationRequest({
    required this.deviceId,
    required this.deviceType,
    this.publicKey,
    this.hardwareHash,
    this.deviceName,
    this.appVersion,
  });

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'device_type': deviceType,
      if (publicKey != null) 'public_key': publicKey,
      if (hardwareHash != null) 'hardware_hash': hardwareHash,
      if (deviceName != null) 'device_name': deviceName,
      if (appVersion != null) 'app_version': appVersion,
    };
  }
}

/// Device registration response model
class DeviceRegistrationResponse {
  final String accessToken;
  final int expiresIn;
  final String? refreshToken;
  final String relayEndpoint;
  final List<String> deviceScope;
  final String? secretKey; // For HMAC signing

  const DeviceRegistrationResponse({
    required this.accessToken,
    required this.expiresIn,
    this.refreshToken,
    required this.relayEndpoint,
    required this.deviceScope,
    this.secretKey,
  });

  factory DeviceRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return DeviceRegistrationResponse(
      accessToken: json['access_token'] as String,
      expiresIn: json['expires_in'] as int,
      refreshToken: json['refresh_token'] as String?,
      relayEndpoint: json['relay_endpoint'] as String,
      deviceScope: List<String>.from(json['device_scope'] as List),
      secretKey: json['secret_key'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'expires_in': expiresIn,
      if (refreshToken != null) 'refresh_token': refreshToken,
      'relay_endpoint': relayEndpoint,
      'device_scope': deviceScope,
      if (secretKey != null) 'secret_key': secretKey,
    };
  }
}

/// Secure relay command model
class SecureRelayCommand {
  final String deviceId;
  final String action;
  final int timestamp;
  final String? signature;
  final Map<String, dynamic>? additionalData;

  const SecureRelayCommand({
    required this.deviceId,
    required this.action,
    required this.timestamp,
    this.signature,
    this.additionalData,
  });

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'action': action,
      'timestamp': timestamp,
      if (signature != null) 'signature': signature,
      if (additionalData != null) ...additionalData!,
    };
  }

  /// Create signed command
  SecureRelayCommand withSignature(String secretKey) {
    final signature = DeviceAuth.createHmacSignature(
      secretKey: secretKey,
      deviceId: deviceId,
      action: action,
      timestamp: timestamp,
      additionalData: additionalData,
    );

    return SecureRelayCommand(
      deviceId: deviceId,
      action: action,
      timestamp: timestamp,
      signature: signature,
      additionalData: additionalData,
    );
  }
}

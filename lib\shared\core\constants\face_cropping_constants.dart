import 'package:flutter/material.dart';

/// Available side effect types
enum SideEffectType {
  /// Trigger hardware relay
  relayTrigger,

  /// Update database records
  databaseUpdate,

  /// Send push notification
  pushNotification,

  /// Send email notification
  emailNotification,

  /// Log audit trail
  auditLog,

  /// Custom webhook call
  webhookCall,

  /// File system operation
  fileSystemOperation,

  /// Cache update
  cacheUpdate,
}

/// Processing mode for face cropping API calls
enum ProcessingMode {
  /// Synchronous processing - wait for immediate response
  synchronous,

  /// Asynchronous processing - use callback mechanism
  asynchronous,

  /// Queue-based processing - batch processing
  queueBased,
}

/// Constants for face cropping functionality
class FaceCroppingConstants {
  // ============================================================================
  // CROPPING PARAMETERS
  // ============================================================================
  
  /// Default padding around face region (20% of face size)
  static const double defaultPadding = 0.2;
  
  /// Minimum padding allowed (5% of face size)
  static const double minPadding = 0.05;
  
  /// Maximum padding allowed (50% of face size)
  static const double maxPadding = 0.5;
  
  /// Default JPEG quality for cropped images (85%)
  static const int defaultOutputQuality = 85;
  
  /// High quality JPEG setting (95%)
  static const int highQuality = 95;
  
  /// Low quality JPEG setting for faster processing (70%)
  static const int lowQuality = 70;
  
  /// Minimum image dimensions for face cropping (pixels)
  static const int minImageWidth = 100;
  static const int minImageHeight = 100;
  
  /// Maximum image dimensions to prevent memory issues (pixels)
  static const int maxImageWidth = 4096;
  static const int maxImageHeight = 4096;
  
  // ============================================================================
  // API PROCESSING MODES
  // ============================================================================
  
  // ============================================================================
  // API TIMEOUTS AND RETRY SETTINGS
  // ============================================================================
  
  /// Timeout for synchronous API calls (30 seconds)
  static const Duration synchronousTimeout = Duration(seconds: 30);
  
  /// Timeout for asynchronous API calls (60 seconds)
  static const Duration asynchronousTimeout = Duration(seconds: 60);
  
  /// Timeout for queue-based API calls (120 seconds)
  static const Duration queueBasedTimeout = Duration(seconds: 120);
  
  /// Maximum number of retry attempts for failed API calls
  static const int maxRetryAttempts = 3;
  
  /// Delay between retry attempts
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// Exponential backoff multiplier for retries
  static const double retryBackoffMultiplier = 2.0;
  
  // ============================================================================
  // QUEUE PROCESSING SETTINGS
  // ============================================================================
  
  /// Maximum number of items in processing queue
  static const int maxQueueSize = 50;
  
  /// Batch size for queue processing
  static const int queueBatchSize = 5;
  
  /// Interval between queue processing batches
  static const Duration queueProcessingInterval = Duration(seconds: 5);
  
  /// Maximum wait time for queue processing
  static const Duration maxQueueWaitTime = Duration(minutes: 10);
  
  // ============================================================================
  // SIDE EFFECTS CONFIGURATION
  // ============================================================================

  /// Default side effects to enable
  static const List<SideEffectType> defaultEnabledSideEffects = [
    SideEffectType.auditLog,
    SideEffectType.databaseUpdate,
  ];
  
  /// Side effect execution timeout
  static const Duration sideEffectTimeout = Duration(seconds: 15);
  
  /// Maximum concurrent side effect executions
  static const int maxConcurrentSideEffects = 3;
  
  // ============================================================================
  // API ENDPOINTS
  // ============================================================================
  
  /// Face cropping API endpoints
  static const String faceCropSyncEndpoint = '/face/crop/sync';
  static const String faceCropAsyncEndpoint = '/face/crop/async';
  static const String faceCropQueueEndpoint = '/face/crop/queue';
  
  /// Side effects API endpoints
  static const String sideEffectsEndpoint = '/face/side-effects';
  static const String sideEffectsStatusEndpoint = '/face/side-effects/status';
  
  // ============================================================================
  // FILE NAMING AND STORAGE
  // ============================================================================
  
  /// File name suffix for cropped images
  static const String croppedImageSuffix = 'cropped';
  
  /// File name suffix for processed images
  static const String processedImageSuffix = 'processed';
  
  /// Directory name for temporary cropped files
  static const String tempCroppedDir = 'temp_cropped';
  
  /// Directory name for processed face images
  static const String processedFacesDir = 'processed_faces';
  
  /// Maximum age for temporary files (24 hours)
  static const Duration tempFileMaxAge = Duration(hours: 24);
  
  // ============================================================================
  // LOGGING AND DEBUGGING
  // ============================================================================
  
  /// Enable detailed logging for face cropping operations
  static const bool enableDetailedLogging = true;
  
  /// Enable performance monitoring for cropping operations
  static const bool enablePerformanceMonitoring = true;
  
  /// Log tag for face cropping operations
  static const String logTag = '🔪 FaceCropping';
  
  // ============================================================================
  // ERROR MESSAGES
  // ============================================================================
  
  /// Error messages for face cropping operations
  static const String errorInvalidImage = 'Invalid or corrupted image file';
  static const String errorNoFaceDetected = 'No face detected in image';
  static const String errorCroppingFailed = 'Face cropping operation failed';
  static const String errorApiCallFailed = 'API call failed';
  static const String errorSideEffectFailed = 'Side effect execution failed';
  static const String errorQueueFull = 'Processing queue is full';
  static const String errorTimeout = 'Operation timed out';
  static const String errorInsufficientStorage = 'Insufficient storage space';
  
  // ============================================================================
  // SUCCESS MESSAGES
  // ============================================================================
  
  /// Success messages for face cropping operations
  static const String successFaceCropped = 'Face cropped successfully';
  static const String successApiCallCompleted = 'API call completed successfully';
  static const String successSideEffectsExecuted = 'Side effects executed successfully';
  static const String successQueueProcessed = 'Queue processed successfully';
  
  // ============================================================================
  // UI COLORS AND STYLES
  // ============================================================================
  
  /// Colors for face cropping UI elements
  static const Color successColor = Colors.green;
  static const Color errorColor = Colors.red;
  static const Color warningColor = Colors.orange;
  static const Color processingColor = Colors.blue;
  
  /// Notification duration for face cropping operations
  static const Duration notificationDuration = Duration(seconds: 3);
  
  // ============================================================================
  // PERFORMANCE THRESHOLDS
  // ============================================================================
  
  /// Maximum processing time for single face crop (milliseconds)
  static const int maxCropProcessingTime = 5000;
  
  /// Warning threshold for processing time (milliseconds)
  static const int processingTimeWarningThreshold = 2000;
  
  /// Maximum memory usage for image processing (MB)
  static const int maxMemoryUsageMB = 100;
  
  // ============================================================================
  // VALIDATION RULES
  // ============================================================================
  
  /// Minimum face size for cropping (percentage of image)
  static const double minFaceSizePercentage = 0.05;
  
  /// Maximum face size for cropping (percentage of image)
  static const double maxFaceSizePercentage = 0.8;
  
  /// Minimum face quality score for processing
  static const double minFaceQualityScore = 0.3;
  
  /// Preferred face quality score for optimal results
  static const double preferredFaceQualityScore = 0.7;
}

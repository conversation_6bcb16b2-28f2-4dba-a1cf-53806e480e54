import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../errors/exceptions.dart';

/// Abstract interface for secure storage operations
abstract class SecureStorageService {
  /// Write string value to secure storage
  Future<void> write(String key, String value);
  
  /// Read string value from secure storage
  Future<String?> read(String key);
  
  /// Write object to secure storage (JSON serialized)
  Future<void> writeObject(String key, Map<String, dynamic> object);
  
  /// Read object from secure storage (JSON deserialized)
  Future<Map<String, dynamic>?> readObject(String key);
  
  /// Delete value from secure storage
  Future<void> delete(String key);
  
  /// Check if key exists in secure storage
  Future<bool> containsKey(String key);
  
  /// Clear all data from secure storage
  Future<void> clear();
  
  /// Get all keys from secure storage
  Future<Set<String>> getAllKeys();
}

/// Implementation of SecureStorageService using flutter_secure_storage
class SecureStorageServiceImpl implements SecureStorageService {
  final FlutterSecureStorage _storage;

  SecureStorageServiceImpl(this._storage);

  @override
  Future<void> write(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
    } catch (e) {
      throw CacheException('Failed to write to secure storage: ${e.toString()}');
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      return await _storage.read(key: key);
    } catch (e) {
      throw CacheException('Failed to read from secure storage: ${e.toString()}');
    }
  }

  @override
  Future<void> writeObject(String key, Map<String, dynamic> object) async {
    try {
      final jsonString = jsonEncode(object);
      await write(key, jsonString);
    } catch (e) {
      throw CacheException('Failed to write object to secure storage: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>?> readObject(String key) async {
    try {
      final jsonString = await read(key);
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException('Failed to read object from secure storage: ${e.toString()}');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      await _storage.delete(key: key);
    } catch (e) {
      throw CacheException('Failed to delete from secure storage: ${e.toString()}');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      throw CacheException('Failed to check key in secure storage: ${e.toString()}');
    }
  }

  @override
  Future<void> clear() async {
    try {
      await _storage.deleteAll();
    } catch (e) {
      throw CacheException('Failed to clear secure storage: ${e.toString()}');
    }
  }

  @override
  Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _storage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      throw CacheException('Failed to get all keys from secure storage: ${e.toString()}');
    }
  }
}

/// Storage keys constants
class StorageKeys {
  // Authentication
  static const String accessToken = 'access_token';
  static const String refreshToken = 'refresh_token';
  static const String userProfile = 'user_profile';
  static const String loginCredentials = 'login_credentials';
  
  // App Settings
  static const String appSettings = 'app_settings';
  static const String themeMode = 'theme_mode';
  static const String language = 'language';
  
  // Cache
  static const String userCache = 'user_cache';
  static const String apiCache = 'api_cache';
  
  // Face Detection
  static const String faceSettings = 'face_settings';
  static const String faceCache = 'face_cache';
  
  // Session
  static const String sessionData = 'session_data';
  static const String lastLoginTime = 'last_login_time';
}

/// Storage utilities
class StorageUtils {
  /// Generate cache key with timestamp
  static String generateCacheKey(String prefix, String identifier) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_${identifier}_$timestamp';
  }
  
  /// Check if cache is expired
  static bool isCacheExpired(String cacheKey, Duration maxAge) {
    try {
      final parts = cacheKey.split('_');
      if (parts.length < 3) return true;
      
      final timestamp = int.tryParse(parts.last);
      if (timestamp == null) return true;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return DateTime.now().difference(cacheTime) > maxAge;
    } catch (e) {
      return true; // Consider expired if parsing fails
    }
  }
  
  /// Extract identifier from cache key
  static String? extractIdentifier(String cacheKey) {
    try {
      final parts = cacheKey.split('_');
      if (parts.length < 3) return null;
      
      // Remove prefix and timestamp, join the rest
      return parts.sublist(1, parts.length - 1).join('_');
    } catch (e) {
      return null;
    }
  }
}

/// Mixin for classes that need secure storage functionality
mixin SecureStorageMixin {
  SecureStorageService get storageService;
  
  /// Save data to secure storage
  Future<void> saveToStorage(String key, String value) async {
    await storageService.write(key, value);
  }
  
  /// Load data from secure storage
  Future<String?> loadFromStorage(String key) async {
    return await storageService.read(key);
  }
  
  /// Save object to secure storage
  Future<void> saveObjectToStorage(String key, Map<String, dynamic> object) async {
    await storageService.writeObject(key, object);
  }
  
  /// Load object from secure storage
  Future<Map<String, dynamic>?> loadObjectFromStorage(String key) async {
    return await storageService.readObject(key);
  }
  
  /// Remove data from secure storage
  Future<void> removeFromStorage(String key) async {
    await storageService.delete(key);
  }
  
  /// Check if key exists in storage
  Future<bool> existsInStorage(String key) async {
    return await storageService.containsKey(key);
  }
}

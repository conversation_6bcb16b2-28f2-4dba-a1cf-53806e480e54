import 'package:flutter/foundation.dart';

/// Authentication status enumeration
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Simple mobile auth provider for routing
/// 
/// This is a temporary implementation for the mobile router
/// Will be replaced with proper shared provider implementation later
class MobileAuthProvider extends ChangeNotifier {
  AuthStatus _authStatus = AuthStatus.initial;
  String? _errorMessage;
  bool _isAuthenticated = false;

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Current authentication status
  AuthStatus get authStatus => _authStatus;

  /// Whether user is authenticated
  bool get isAuthenticated => _isAuthenticated;

  /// Current error message
  String? get errorMessage => _errorMessage;

  /// Whether authentication is in progress
  bool get isLoading => _authStatus == AuthStatus.loading;

  /// Whether there's an authentication error
  bool get hasError => _authStatus == AuthStatus.error;

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  /// Initialize authentication state
  Future<void> initialize() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate checking stored authentication
      await Future.delayed(const Duration(milliseconds: 500));
      
      // For now, assume user is not authenticated
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAuthenticated = false;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate login API call
      await Future.delayed(const Duration(seconds: 1));
      
      // For demo purposes, accept any non-empty credentials
      if (email.isNotEmpty && password.isNotEmpty) {
        _setAuthStatus(AuthStatus.authenticated);
        _isAuthenticated = true;
        _errorMessage = null;
        notifyListeners();
        return true;
      } else {
        _setError('Invalid credentials');
        return false;
      }
    } catch (error) {
      _setError(error.toString());
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate logout API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAuthenticated = false;
      _errorMessage = null;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  /// Check authentication status
  Future<void> checkAuthStatus() async {
    _setAuthStatus(AuthStatus.loading);
    
    try {
      // Simulate checking authentication status
      await Future.delayed(const Duration(milliseconds: 300));
      
      // For now, assume user is not authenticated
      _setAuthStatus(AuthStatus.unauthenticated);
      _isAuthenticated = false;
    } catch (error) {
      _setError(error.toString());
    }
    
    notifyListeners();
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /// Set authentication status
  void _setAuthStatus(AuthStatus status) {
    _authStatus = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
  }

  /// Set error state
  void _setError(String message) {
    _authStatus = AuthStatus.error;
    _errorMessage = message;
    _isAuthenticated = false;
  }

  /// Clear error
  void clearError() {
    if (_authStatus == AuthStatus.error) {
      _authStatus = AuthStatus.unauthenticated;
      _errorMessage = null;
      notifyListeners();
    }
  }
}

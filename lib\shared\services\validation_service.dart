import '../core/errors/failures.dart';

/// Comprehensive validation service for the application
class ValidationService {
  static const ValidationService _instance = ValidationService._internal();
  factory ValidationService() => _instance;
  const ValidationService._internal();

  /// Validate login form data
  ValidationFailure? validateLoginForm({
    required String userName,
    required String password,
    String? serverAddress,
    bool isOnPremise = false,
  }) {
    final errors = <String, List<String>>{};

    // Validate server address for on-premise mode
    if (isOnPremise) {
      final serverError = validateServerAddress(serverAddress);
      if (serverError != null) {
        errors['serverAddress'] = [serverError];
      }
    }

    // Validate username
    final userNameError = validateUserName(userName);
    if (userNameError != null) {
      errors['userName'] = [userNameError];
    }

    // Validate password
    final passwordError = validatePassword(password);
    if (passwordError != null) {
      errors['password'] = [passwordError];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Thông tin đăng nhập không hợp lệ',
        code: 'VALIDATION_ERROR',
        fieldErrors: errors,
      );
    }

    return null;
  }

  /// Validate server address
  String? validateServerAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập địa chỉ máy chủ';
    }

    final trimmed = value.trim();

    // Check minimum length
    if (trimmed.length < 3) {
      return 'Địa chỉ máy chủ quá ngắn';
    }

    // Check maximum length
    if (trimmed.length > 255) {
      return 'Địa chỉ máy chủ quá dài';
    }

    // Basic URL format validation
    final urlPattern = RegExp(
      r'^https?://[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*(:([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?(/.*)?$',
      caseSensitive: false,
    );

    if (!urlPattern.hasMatch(trimmed)) {
      return 'Địa chỉ máy chủ không hợp lệ. Ví dụ: http://*************:3000';
    }

    return null;
  }

  /// Validate username
  String? validateUserName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập tên đăng nhập';
    }

    final trimmed = value.trim();

    // Check minimum length
    if (trimmed.length < 3) {
      return 'Tên đăng nhập phải có ít nhất 3 ký tự';
    }

    // Check maximum length
    if (trimmed.length > 50) {
      return 'Tên đăng nhập không được vượt quá 50 ký tự';
    }

    // Check for valid characters (alphanumeric, underscore, dot, hyphen)
    // final validPattern = RegExp(r'^[a-zA-Z0-9._-]+$');
    // if (!validPattern.hasMatch(trimmed)) {
    //   return 'Tên đăng nhập chỉ được chứa chữ cái, số, dấu chấm, gạch dưới và gạch ngang';
    // }

    // Check that it doesn't start or end with special characters
    if (trimmed.startsWith('.') || trimmed.startsWith('_') || trimmed.startsWith('-') ||
        trimmed.endsWith('.') || trimmed.endsWith('_') || trimmed.endsWith('-')) {
      return 'Tên đăng nhập không được bắt đầu hoặc kết thúc bằng ký tự đặc biệt';
    }

    // Check for consecutive special characters
    if (RegExp(r'[._-]{2,}').hasMatch(trimmed)) {
      return 'Tên đăng nhập không được chứa các ký tự đặc biệt liên tiếp';
    }

    return null;
  }

  /// Validate password
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Vui lòng nhập mật khẩu';
    }

    // Check minimum length
    if (value.length < 6) {
      return 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    // Check maximum length
    if (value.length > 128) {
      return 'Mật khẩu không được vượt quá 128 ký tự';
    }

    // Check for whitespace at beginning or end
    if (value != value.trim()) {
      return 'Mật khẩu không được có khoảng trắng ở đầu hoặc cuối';
    }

    return null;
  }

  /// Validate password with strength requirements
  String? validatePasswordStrength(String? value, {
    bool requireUppercase = false,
    bool requireLowercase = false,
    bool requireNumbers = false,
    bool requireSpecialChars = false,
    int minLength = 8,
  }) {
    // First check basic validation
    final basicError = validatePassword(value);
    if (basicError != null) return basicError;

    if (value!.length < minLength) {
      return 'Mật khẩu phải có ít nhất $minLength ký tự';
    }

    final errors = <String>[];

    if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
      errors.add('chữ hoa');
    }

    if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
      errors.add('chữ thường');
    }

    if (requireNumbers && !RegExp(r'[0-9]').hasMatch(value)) {
      errors.add('số');
    }

    if (requireSpecialChars && !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      errors.add('ký tự đặc biệt');
    }

    if (errors.isNotEmpty) {
      return 'Mật khẩu phải chứa: ${errors.join(', ')}';
    }

    return null;
  }

  /// Validate email format
  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập địa chỉ email';
    }

    final trimmed = value.trim();

    // Check maximum length
    if (trimmed.length > 254) {
      return 'Địa chỉ email quá dài';
    }

    // Email regex pattern
    final emailPattern = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailPattern.hasMatch(trimmed)) {
      return 'Địa chỉ email không hợp lệ';
    }

    return null;
  }

  /// Validate phone number (Vietnamese format)
  String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập số điện thoại';
    }

    final trimmed = value.trim().replaceAll(RegExp(r'[\s-()]'), '');

    // Vietnamese phone number patterns
    final phonePattern = RegExp(r'^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$');

    if (!phonePattern.hasMatch(trimmed)) {
      return 'Số điện thoại không hợp lệ. Ví dụ: 0901234567';
    }

    return null;
  }

  /// Validate required field
  String? validateRequired(dynamic value, String fieldName) {
    if (value == null) {
      return '$fieldName không được để trống';
    }

    if (value is String && value.trim().isEmpty) {
      return '$fieldName không được để trống';
    }

    if (value is List && value.isEmpty) {
      return '$fieldName không được để trống';
    }

    return null;
  }

  /// Validate field length
  String? validateLength(String? value, {
    required String fieldName,
    int? minLength,
    int? maxLength,
  }) {
    if (value == null) return null;

    if (minLength != null && value.length < minLength) {
      return '$fieldName phải có ít nhất $minLength ký tự';
    }

    if (maxLength != null && value.length > maxLength) {
      return '$fieldName không được vượt quá $maxLength ký tự';
    }

    return null;
  }

  /// Validate numeric value
  String? validateNumeric(String? value, {
    required String fieldName,
    num? min,
    num? max,
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName không được để trống';
    }

    final numValue = num.tryParse(value.trim());
    if (numValue == null) {
      return '$fieldName phải là một số hợp lệ';
    }

    if (min != null && numValue < min) {
      return '$fieldName phải lớn hơn hoặc bằng $min';
    }

    if (max != null && numValue > max) {
      return '$fieldName phải nhỏ hơn hoặc bằng $max';
    }

    return null;
  }

  /// Combine multiple validators
  String? combineValidators(dynamic value, List<String? Function(dynamic)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  /// Get password strength score (0-4)
  int getPasswordStrength(String password) {
    int score = 0;

    // Length check
    if (password.length >= 8) score++;

    // Uppercase check
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;

    // Lowercase check
    if (RegExp(r'[a-z]').hasMatch(password)) score++;

    // Number check
    if (RegExp(r'[0-9]').hasMatch(password)) score++;

    // Special character check
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;

    return score > 4 ? 4 : score;
  }

  /// Get password strength description
  String getPasswordStrengthDescription(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'Rất yếu';
      case 2:
        return 'Yếu';
      case 3:
        return 'Trung bình';
      case 4:
        return 'Mạnh';
      default:
        return 'Rất mạnh';
    }
  }
}

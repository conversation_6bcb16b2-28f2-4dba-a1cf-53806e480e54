import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'relay_config_service.dart';
import 'relay_management_service.dart';
import 'package:relay_controller/relay_controller.dart' as relay;

/// Service for throttling relay operations to prevent excessive switching
/// and implement real-world timing constraints for LED and door control
class RelayThrottleService {
  static RelayThrottleService? _instance;
  static RelayThrottleService get instance {
    _instance ??= RelayThrottleService._();
    return _instance!;
  }
  
  RelayThrottleService._();

  RelayThrottleConfig? _throttleConfig;
  final Map<int, Timer?> _activeTimers = {};
  final Map<int, DateTime> _lastActionTime = {};
  final Map<int, RelayState> _currentState = {};
  
  /// Initialize the throttle service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _throttleConfig = RelayThrottleConfig(prefs);
    
    if (kDebugMode) {
      print('🕐 RelayThrottleService initialized');
      print('   LED settings: ${_throttleConfig!.ledOnDuration}ms ON, ${_throttleConfig!.ledCooldown}ms cooldown');
      print('   Door settings: ${_throttleConfig!.doorUnlockDuration}ms unlock, ${_throttleConfig!.doorCooldown}ms cooldown');
    }
  }

  /// Smart LED (R0) control with intelligent face detection logic
  Future<void> controlLed({required bool faceDetected, bool force = false}) async {
    await _smartRelayControl(
      relayIndex: 0,
      triggerOn: faceDetected,
      onDuration: _throttleConfig!.ledOnDuration,
      cooldownDuration: _throttleConfig!.ledCooldown,
      autoControl: _throttleConfig!.ledAutoControl,
      force: force,
      description: 'LED (Face Detection)',
    );
  }

  /// Smart Door (R1) control with access control logic
  Future<void> controlDoor({required bool accessGranted, bool force = false}) async {
    await _smartRelayControl(
      relayIndex: 1,
      triggerOn: accessGranted,
      onDuration: _throttleConfig!.doorUnlockDuration,
      cooldownDuration: _throttleConfig!.doorCooldown,
      autoControl: _throttleConfig!.doorAutoControl,
      force: force,
      description: 'Door (Access Control)',
    );
  }

  /// Smart relay control with intelligent ON/OFF logic
  /// - ON events: Immediate response + start timer
  /// - OFF events: Let timer handle (prevent rapid switching)
  Future<void> _smartRelayControl({
    required int relayIndex,
    required bool triggerOn,
    required int onDuration,
    required int cooldownDuration,
    required bool autoControl,
    required bool force,
    required String description,
  }) async {
    _ensureInitialized();

    if (triggerOn) {
      // ON event: Always respond immediately (real-time response)
      await _controlThrottledRelay(
        relayIndex: relayIndex,
        targetState: RelayState.on,
        onDuration: onDuration,
        cooldownDuration: cooldownDuration,
        autoControl: autoControl,
        force: force,
        description: description,
      );
    } else {
      // OFF event: Only turn OFF if no active timer (let auto-timeout handle it)
      final activeTimer = _activeTimers[relayIndex];
      if (activeTimer == null || !activeTimer.isActive) {
        // No active timer, safe to turn OFF
        await _controlThrottledRelay(
          relayIndex: relayIndex,
          targetState: RelayState.off,
          onDuration: onDuration,
          cooldownDuration: cooldownDuration,
          autoControl: false, // Don't auto-control for OFF commands
          force: force,
          description: description,
        );
      } else {
        // Active timer exists, let it handle the timeout naturally
        if (kDebugMode) {
          final remaining = getTimeRemaining(relayIndex) ?? 0;
          print('🕐 $description has active timer, letting it timeout naturally (${remaining}ms remaining)');
        }
      }
    }
  }

  /// Generic throttled relay control (internal method)
  Future<void> _controlThrottledRelay({
    required int relayIndex,
    required RelayState targetState,
    required int onDuration,
    required int cooldownDuration,
    required bool autoControl,
    required bool force,
    required String description,
  }) async {
    _ensureInitialized();
    
    final now = DateTime.now();
    final lastAction = _lastActionTime[relayIndex];
    final currentState = _currentState[relayIndex] ?? RelayState.off;
    
    // Check cooldown period (unless forced)
    if (!force && lastAction != null) {
      final timeSinceLastAction = now.difference(lastAction).inMilliseconds;
      if (timeSinceLastAction < cooldownDuration) {
        if (kDebugMode) {
          print('🕐 $description throttled: ${cooldownDuration - timeSinceLastAction}ms remaining');
        }
        return;
      }
    }

    // Skip if already in target state and not forced
    if (!force && currentState == targetState) {
      if (kDebugMode) {
        print('🔄 $description already in ${targetState.name} state');
      }
      return;
    }

    try {
      final relayService = RelayManagementService.instance;
      
      if (targetState == RelayState.on) {
        // Turn ON relay
        await relayService.controlRelay(relayIndex, relay.RelayAction.on);
        _currentState[relayIndex] = RelayState.on;
        _lastActionTime[relayIndex] = now;
        
        if (kDebugMode) {
          print('🔛 $description turned ON for ${onDuration}ms');
        }
        
        // Cancel existing timer
        _activeTimers[relayIndex]?.cancel();
        
        // Set timer to turn OFF after duration (if auto control enabled)
        if (autoControl) {
          _activeTimers[relayIndex] = Timer(Duration(milliseconds: onDuration), () async {
            await _turnOffRelay(relayIndex, description);
          });
        }
        
      } else {
        // Turn OFF relay
        await _turnOffRelay(relayIndex, description);
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to control $description: $e');
      }
      rethrow;
    }
  }

  /// Turn OFF relay and update state
  Future<void> _turnOffRelay(int relayIndex, String description) async {
    try {
      final relayService = RelayManagementService.instance;
      await relayService.controlRelay(relayIndex, relay.RelayAction.off);
      
      _currentState[relayIndex] = RelayState.off;
      _lastActionTime[relayIndex] = DateTime.now();
      _activeTimers[relayIndex]?.cancel();
      _activeTimers[relayIndex] = null;
      
      if (kDebugMode) {
        print('🔴 $description turned OFF');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to turn OFF $description: $e');
      }
      rethrow;
    }
  }

  /// Get current relay state
  RelayState getRelayState(int relayIndex) {
    return _currentState[relayIndex] ?? RelayState.off;
  }

  /// Get time remaining for active timer
  int? getTimeRemaining(int relayIndex) {
    final timer = _activeTimers[relayIndex];
    if (timer == null || !timer.isActive) return null;
    
    final lastAction = _lastActionTime[relayIndex];
    if (lastAction == null) return null;
    
    final elapsed = DateTime.now().difference(lastAction).inMilliseconds;
    final duration = relayIndex == 0 
        ? _throttleConfig!.ledOnDuration 
        : _throttleConfig!.doorUnlockDuration;
    
    return (duration - elapsed).clamp(0, duration);
  }

  /// Get cooldown remaining time
  int getCooldownRemaining(int relayIndex) {
    final lastAction = _lastActionTime[relayIndex];
    if (lastAction == null) return 0;
    
    final elapsed = DateTime.now().difference(lastAction).inMilliseconds;
    final cooldown = relayIndex == 0 
        ? _throttleConfig!.ledCooldown 
        : _throttleConfig!.doorCooldown;
    
    return (cooldown - elapsed).clamp(0, cooldown);
  }

  /// Force turn OFF all relays and clear timers
  Future<void> emergencyStop() async {
    if (kDebugMode) {
      print('🚨 Emergency stop: Turning OFF all relays');
    }
    
    // Cancel all timers
    for (final timer in _activeTimers.values) {
      timer?.cancel();
    }
    _activeTimers.clear();
    
    // Turn OFF all relays
    try {
      final relayService = RelayManagementService.instance;
      for (int i = 0; i < 4; i++) {
        await relayService.controlRelay(i, relay.RelayAction.off);
        _currentState[i] = RelayState.off;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Emergency stop failed: $e');
      }
    }
  }

  /// Get throttle configuration
  RelayThrottleConfig get config {
    _ensureInitialized();
    return _throttleConfig!;
  }

  /// Get status summary
  Map<String, dynamic> getStatusSummary() {
    return {
      'relayStates': _currentState.map((key, value) => MapEntry(key.toString(), value.name)),
      'activeTimers': _activeTimers.keys.where((key) => _activeTimers[key]?.isActive == true).toList(),
      'timeRemaining': {
        for (int i = 0; i < 4; i++)
          i.toString(): getTimeRemaining(i),
      },
      'cooldownRemaining': {
        for (int i = 0; i < 4; i++)
          i.toString(): getCooldownRemaining(i),
      },
    };
  }

  /// Dispose resources
  void dispose() {
    for (final timer in _activeTimers.values) {
      timer?.cancel();
    }
    _activeTimers.clear();
    _currentState.clear();
    _lastActionTime.clear();
  }

  void _ensureInitialized() {
    if (_throttleConfig == null) {
      throw StateError('RelayThrottleService not initialized. Call initialize() first.');
    }
  }
}

/// Relay state enumeration
enum RelayState {
  on,
  off,
}

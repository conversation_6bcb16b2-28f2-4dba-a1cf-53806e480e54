import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// Provider to manage captured face images for display in avatar
class CapturedFaceProvider extends ChangeNotifier {
  Uint8List? _capturedFaceImage;
  Face? _capturedFace;
  double _capturedFaceQuality = 0.0;
  DateTime? _captureTime;

  // Getters
  Uint8List? get capturedFaceImage => _capturedFaceImage;
  Face? get capturedFace => _capturedFace;
  double get capturedFaceQuality => _capturedFaceQuality;
  DateTime? get captureTime => _captureTime;
  bool get hasCapturedFace => _capturedFaceImage != null;

  /// Update captured face image
  void updateCapturedFace({
    required Uint8List imageBytes,
    required Face face,
    required double quality,
  }) {
    _capturedFaceImage = imageBytes;
    _capturedFace = face;
    _capturedFaceQuality = quality;
    _captureTime = DateTime.now();
    
    if (kDebugMode) {
      print('📸 Face captured updated - Quality: ${(quality * 100).toStringAsFixed(1)}%');
    }
    
    notifyListeners();
  }

  /// Clear captured face
  void clearCapturedFace() {
    _capturedFaceImage = null;
    _capturedFace = null;
    _capturedFaceQuality = 0.0;
    _captureTime = null;
    
    if (kDebugMode) {
      print('🗑️ Captured face cleared');
    }
    
    notifyListeners();
  }

  /// Check if captured face is recent (within last 30 seconds)
  bool get isRecentCapture {
    if (_captureTime == null) return false;
    final now = DateTime.now();
    final difference = now.difference(_captureTime!);
    return difference.inSeconds <= 30;
  }

  @override
  void dispose() {
    _capturedFaceImage = null;
    _capturedFace = null;
    super.dispose();
  }
}

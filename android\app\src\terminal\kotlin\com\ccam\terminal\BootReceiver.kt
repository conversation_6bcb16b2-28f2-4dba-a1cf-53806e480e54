package com.ccam.terminal

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * Boot receiver to automatically start the terminal app on device boot
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Boot receiver triggered: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                Log.i(TAG, "Device boot completed, starting terminal app")
                startTerminalApp(context)
            }
        }
    }
    
    private fun startTerminalApp(context: Context) {
        try {
            val launchIntent = Intent(context, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                putExtra("auto_started", true)
            }
            
            context.startActivity(launchIntent)
            Log.i(TAG, "Terminal app started successfully")
            
            // Also start the keep-alive service
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(serviceIntent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start terminal app", e)
        }
    }
}

import 'package:flutter/material.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/components/app_button.dart';
import '../../../../../shared/components/app_input_field.dart';
import '../../../../../shared/components/app_logo.dart';

/// M<PERSON>n hình nhập email để reset password
class EnterEmailScreen extends StatefulWidget {
  const EnterEmailScreen({super.key});

  @override
  State<EnterEmailScreen> createState() => _EnterEmailScreenState();
}

class _EnterEmailScreenState extends State<EnterEmailScreen> {
  final _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Add listener to update button state
    _emailController.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  bool get _isFormValid {
    return _emailController.text.trim().isNotEmpty &&
        _emailController.text.contains('@');
  }

  void _handleSendOtp() {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          // Navigate to OTP screen
          Navigator.of(context).pushNamed('/enter-otp');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLogoSection(),
              SizedBox(height: AppDimensions.spacing24),
              _buildTitleSection(),
              SizedBox(height: AppDimensions.spacing24),
              _buildForm(),
              SizedBox(height: AppDimensions.spacing20),
              _buildSendButton(),
              SizedBox(height: AppDimensions.spacing16),
              _buildBackLink(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Row(children: [const AppLogo()]);
  }

  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Đặt lại mật khẩu',
          style: AppTextStyles.heading3.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        SizedBox(height: AppDimensions.spacing4),
        Text(
          'Nhập email để tiếp tục quá trình lấy lại mật khẩu',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppInputField(
            controller: _emailController,
            label: 'Email',
            placeholder: 'Nhập thông tin email của bạn',
            isRequired: true,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value?.trim().isEmpty ?? true) {
                return 'Vui lòng nhập email';
              }
              if (!value!.contains('@')) {
                return 'Email không hợp lệ';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton() {
    return AppButton(
      text: 'Yêu cầu đổi mật khẩu',
      onPressed: (_isLoading || !_isFormValid) ? null : _handleSendOtp,
      isLoading: _isLoading,
    );
  }

  Widget _buildBackLink() {
    return Center(
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Text(
          '< Trở lại đăng nhập',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

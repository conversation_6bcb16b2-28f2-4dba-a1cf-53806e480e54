import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_dimensions.dart';

/// Generic list view component với virtual scroll và infinite scroll
class AppListView<T> extends StatefulWidget {
  /// List items hiện tại
  final List<T> items;
  
  /// Builder function để render từng item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  
  /// Callback khi cần load thêm data (infinite scroll)
  final Future<List<T>> Function()? onLoadMore;
  
  /// Callback khi pull to refresh
  final Future<List<T>> Function()? onRefresh;
  
  /// Separator giữa các items
  final Widget? separator;
  
  /// Padding cho list
  final EdgeInsetsGeometry? padding;
  
  /// Loading indicator widget
  final Widget? loadingWidget;
  
  /// Empty state widget
  final Widget? emptyWidget;
  
  /// Error widget
  final Widget Function(String error)? errorWidget;
  
  /// Threshold để trigger load more (số items từ cuối)
  final int loadMoreThreshold;
  
  /// Có enable infinite scroll không
  final bool enableInfiniteScroll;
  
  /// Có enable pull to refresh không
  final bool enablePullToRefresh;
  
  /// Có đang loading không
  final bool isLoading;
  
  /// Error message nếu có
  final String? error;
  
  /// Có còn data để load không
  final bool hasMoreData;

  const AppListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.onLoadMore,
    this.onRefresh,
    this.separator,
    this.padding,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.loadMoreThreshold = 3,
    this.enableInfiniteScroll = true,
    this.enablePullToRefresh = true,
    this.isLoading = false,
    this.error,
    this.hasMoreData = true,
  });

  @override
  State<AppListView<T>> createState() => _AppListViewState<T>();
}

class _AppListViewState<T> extends State<AppListView<T>> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    if (widget.enableInfiniteScroll) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!widget.enableInfiniteScroll || 
        widget.onLoadMore == null || 
        _isLoadingMore || 
        !widget.hasMoreData) {
      return;
    }

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    final threshold = maxScroll - (widget.loadMoreThreshold * 100); // Estimate item height

    if (currentScroll >= threshold) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore || !widget.hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      await widget.onLoadMore!();
    } catch (e) {
      // Error handling sẽ được handle bởi parent
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<void> _onRefresh() async {
    if (widget.onRefresh != null) {
      await widget.onRefresh!();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Error state
    if (widget.error != null && widget.items.isEmpty) {
      return _buildErrorWidget();
    }

    // Loading state (initial load)
    if (widget.isLoading && widget.items.isEmpty) {
      return _buildLoadingWidget();
    }

    // Empty state
    if (widget.items.isEmpty) {
      return _buildEmptyWidget();
    }

    // Main list
    Widget listView = ListView.separated(
      controller: _scrollController,
      padding: widget.padding ?? EdgeInsets.all(AppDimensions.paddingM),
      itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
      separatorBuilder: (context, index) {
        if (index >= widget.items.length) return const SizedBox.shrink();
        return widget.separator ?? SizedBox(height: AppDimensions.spacing12);
      },
      itemBuilder: (context, index) {
        // Loading more indicator
        if (index >= widget.items.length) {
          return _buildLoadMoreIndicator();
        }

        // Regular item
        final item = widget.items[index];
        return widget.itemBuilder(context, item, index);
      },
    );

    // Wrap với RefreshIndicator nếu enable
    if (widget.enablePullToRefresh && widget.onRefresh != null) {
      listView = RefreshIndicator(
        onRefresh: _onRefresh,
        child: listView,
      );
    }

    return listView;
  }

  Widget _buildLoadingWidget() {
    return widget.loadingWidget ?? 
        const Center(
          child: CircularProgressIndicator(),
        );
  }

  Widget _buildEmptyWidget() {
    return widget.emptyWidget ?? 
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inbox_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              SizedBox(height: AppDimensions.spacing16),
              Text(
                'Không có dữ liệu',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget?.call(widget.error!) ?? 
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error,
              ),
              SizedBox(height: AppDimensions.spacing16),
              Text(
                widget.error!,
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

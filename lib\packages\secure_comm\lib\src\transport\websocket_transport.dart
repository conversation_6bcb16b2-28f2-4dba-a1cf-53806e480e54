import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'transport_interface.dart';
import 'http_transport.dart';
import '../models/secure_message.dart';
import '../models/device_registration.dart';
import '../exceptions.dart';

/// WebSocket transport implementation for secure communication
class WebSocketTransport implements TransportInterface {
  /// WebSocket server URL
  final String serverUrl;

  /// Connection timeout
  final Duration timeout;

  /// Additional headers for WebSocket connection
  final Map<String, String> headers;

  /// HTTP fallback URL for registration
  final String? httpFallbackUrl;

  /// WebSocket connection
  WebSocket? _socket;

  /// Connection status
  bool _isConnected = false;

  /// Response completers for request-response pattern
  final Map<String, Completer<SecureResponse>> _responseCompleters = {};

  /// Message stream controller
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();

  /// Creates a new WebSocket transport
  WebSocketTransport(
    this.serverUrl, {
    this.timeout = const Duration(seconds: 30),
    this.headers = const {},
    this.httpFallbackUrl,
  });

  @override
  String get transportType => 'websocket';

  @override
  bool get isConnected => _isConnected;

  /// Connect to WebSocket server
  Future<void> connect() async {
    if (_isConnected) return;

    try {
      _socket = await WebSocket.connect(
        serverUrl,
        headers: headers,
      ).timeout(timeout);

      _isConnected = true;
      _setupMessageHandling();

    } catch (e) {
      throw TransportException('WebSocket connection failed', e);
    }
  }

  /// Setup WebSocket message handling
  void _setupMessageHandling() {
    if (_socket == null) return;

    _socket!.listen(
      (data) {
        try {
          final message = jsonDecode(data as String) as Map<String, dynamic>;
          _handleIncomingMessage(message);
        } catch (e) {
          print('Failed to parse WebSocket message: $e');
        }
      },
      onError: (error) {
        print('WebSocket error: $error');
        _isConnected = false;
      },
      onDone: () {
        _isConnected = false;
        _completeAllPendingRequests(
          TransportException('WebSocket connection closed'),
        );
      },
    );
  }

  /// Handle incoming WebSocket messages
  void _handleIncomingMessage(Map<String, dynamic> message) {
    final messageId = message['message_id'] as String?;
    final type = message['type'] as String?;

    // Handle response messages
    if (messageId != null && _responseCompleters.containsKey(messageId)) {
      final response = SecureResponse.fromJson(message);
      _responseCompleters[messageId]!.complete(response);
      _responseCompleters.remove(messageId);
      return;
    }

    // Handle other message types
    _messageController.add(message);
  }

  @override
  Future<DeviceRegistrationResponse> registerDevice(
    DeviceRegistrationRequest request,
  ) async {
    // Use HTTP fallback for registration if available
    if (httpFallbackUrl != null) {
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        return await httpTransport.registerDevice(request);
      } finally {
        await httpTransport.dispose();
      }
    }

    // Use WebSocket for registration
    if (!_isConnected) {
      await connect();
    }

    final messageId = _generateMessageId();
    final message = {
      'type': 'device_registration',
      'message_id': messageId,
      'payload': request.toJson(),
      'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };

    final completer = Completer<SecureResponse>();
    _responseCompleters[messageId] = completer;

    _socket!.add(jsonEncode(message));

    // Set timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        _responseCompleters.remove(messageId);
        completer.completeError(TimeoutException('WebSocket registration timeout'));
      }
    });

    try {
      final response = await completer.future;
      if (response.success && response.data != null) {
        return DeviceRegistrationResponse.fromJson(response.data!);
      } else {
        throw DeviceRegistrationException(response.error ?? 'Registration failed');
      }
    } catch (e) {
      throw DeviceRegistrationException('WebSocket registration failed', e);
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    // Use HTTP fallback for token refresh if available
    if (httpFallbackUrl != null) {
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        return await httpTransport.refreshToken(refreshToken);
      } finally {
        await httpTransport.dispose();
      }
    }

    // Use WebSocket for token refresh
    if (!_isConnected) {
      await connect();
    }

    final messageId = _generateMessageId();
    final message = {
      'type': 'token_refresh',
      'message_id': messageId,
      'payload': {'refresh_token': refreshToken},
      'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };

    final completer = Completer<SecureResponse>();
    _responseCompleters[messageId] = completer;

    _socket!.add(jsonEncode(message));

    // Set timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        _responseCompleters.remove(messageId);
        completer.completeError(TimeoutException('WebSocket token refresh timeout'));
      }
    });

    try {
      final response = await completer.future;
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw AuthenticationException(response.error ?? 'Token refresh failed');
      }
    } catch (e) {
      throw AuthenticationException('WebSocket token refresh failed', e);
    }
  }

  @override
  Future<SecureResponse> sendMessage({
    required SecureMessage message,
    required String accessToken,
  }) async {
    if (!_isConnected) {
      await connect();
    }

    if (_socket == null) {
      throw TransportException('WebSocket not connected');
    }

    try {
      final messageData = message.toJson();
      messageData['access_token'] = accessToken;

      _socket!.add(jsonEncode(messageData));

      // Wait for response if message has ID
      if (message.messageId != null) {
        final completer = Completer<SecureResponse>();
        _responseCompleters[message.messageId!] = completer;

        // Set timeout
        Timer(timeout, () {
          if (!completer.isCompleted) {
            _responseCompleters.remove(message.messageId);
            completer.completeError(TimeoutException('WebSocket response timeout'));
          }
        });

        return await completer.future;
      } else {
        // Return success response for fire-and-forget messages
        return SecureResponse(
          success: true,
          timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
      }

    } catch (e) {
      throw TransportException('WebSocket message send failed', e);
    }
  }

  @override
  Future<SecureResponse> sendPlainTextMessage(Map<String, dynamic> message) async {
    if (!_isConnected) {
      await connect();
    }

    if (_socket == null) {
      throw TransportException('WebSocket not connected');
    }

    try {
      final messageId = message['message_id'] ?? _generateMessageId();
      final messageData = Map<String, dynamic>.from(message);
      messageData['message_id'] = messageId;

      _socket!.add(jsonEncode(messageData));

      // Wait for response if message has ID
      if (messageId != null) {
        final completer = Completer<SecureResponse>();
        _responseCompleters[messageId] = completer;

        // Set timeout
        Timer(Duration(seconds: 30), () {
          if (!completer.isCompleted) {
            _responseCompleters.remove(messageId);
            completer.completeError(TimeoutException('WebSocket response timeout'));
          }
        });

        return await completer.future;
      } else {
        // Return success response for fire-and-forget messages
        return SecureResponse(
          success: true,
          timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        );
      }

    } catch (e) {
      throw TransportException('WebSocket plain text message send failed', e);
    }
  }

  @override
  Future<void> revokeCredentials(String accessToken) async {
    // Use HTTP fallback for credential revocation if available
    if (httpFallbackUrl != null) {
      final httpTransport = HttpTransport(httpFallbackUrl!);
      try {
        await httpTransport.revokeCredentials(accessToken);
      } finally {
        await httpTransport.dispose();
      }
      return;
    }

    // Use WebSocket for credential revocation
    if (!_isConnected) return;

    final messageId = _generateMessageId();
    final message = {
      'type': 'credential_revocation',
      'message_id': messageId,
      'payload': {'access_token': accessToken},
      'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };

    _socket!.add(jsonEncode(message));
    await disconnect();
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    if (_socket != null && _isConnected) {
      await _socket!.close();
      _isConnected = false;
    }
  }

  /// Generate unique message ID
  String _generateMessageId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 0xFFFFFF).toRadixString(36);
    return '${timestamp.toRadixString(36)}-$random';
  }

  /// Complete all pending requests with error
  void _completeAllPendingRequests(Exception error) {
    for (final completer in _responseCompleters.values) {
      if (!completer.isCompleted) {
        completer.completeError(error);
      }
    }
    _responseCompleters.clear();
  }

  /// Get message stream for listening to server messages
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;

  @override
  Future<void> dispose() async {
    _completeAllPendingRequests(
      TransportException('Transport disposed'),
    );

    await disconnect();
    await _messageController.close();
    _socket = null;
  }
}

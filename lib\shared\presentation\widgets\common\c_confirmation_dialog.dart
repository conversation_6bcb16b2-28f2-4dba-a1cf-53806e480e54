import 'package:flutter/material.dart';

/// Custom confirmation dialog for user actions
/// 
/// Provides consistent confirmation dialogs across mobile and terminal apps
class CConfirmationDialog extends StatelessWidget {
  final String title;
  final String? content;
  final Widget? contentWidget;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmColor;
  final Color? cancelColor;
  final IconData? icon;
  final Color? iconColor;
  final bool isDangerous;
  final bool barrierDismissible;

  const CConfirmationDialog({
    super.key,
    required this.title,
    this.content,
    this.contentWidget,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.cancelColor,
    this.icon,
    this.iconColor,
    this.isDangerous = false,
    this.barrierDismissible = true,
  }) : assert(content != null || contentWidget != null, 'Either content or contentWidget must be provided');

  /// Create a delete confirmation dialog
  const CConfirmationDialog.delete({
    super.key,
    required this.title,
    this.content = 'This action cannot be undone.',
    this.contentWidget,
    this.confirmText = 'Delete',
    this.cancelText = 'Cancel',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.cancelColor,
    this.iconColor,
    this.barrierDismissible = true,
  }) : icon = Icons.delete_outline,
       isDangerous = true;

  /// Create a logout confirmation dialog
  const CConfirmationDialog.logout({
    super.key,
    this.title = 'Logout',
    this.content = 'Are you sure you want to logout?',
    this.contentWidget,
    this.confirmText = 'Logout',
    this.cancelText = 'Cancel',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.cancelColor,
    this.iconColor,
    this.barrierDismissible = true,
  }) : icon = Icons.logout,
       isDangerous = false;

  /// Create a save confirmation dialog
  const CConfirmationDialog.save({
    super.key,
    this.title = 'Save Changes',
    this.content = 'Do you want to save your changes?',
    this.contentWidget,
    this.confirmText = 'Save',
    this.cancelText = 'Discard',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.cancelColor,
    this.iconColor,
    this.barrierDismissible = true,
  }) : icon = Icons.save_outlined,
       isDangerous = false;

  /// Create a discard changes confirmation dialog
  const CConfirmationDialog.discardChanges({
    super.key,
    this.title = 'Discard Changes',
    this.content = 'You have unsaved changes. Are you sure you want to discard them?',
    this.contentWidget,
    this.confirmText = 'Discard',
    this.cancelText = 'Keep Editing',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.cancelColor,
    this.iconColor,
    this.barrierDismissible = true,
  }) : icon = Icons.warning_outlined,
       isDangerous = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      icon: icon != null ? Icon(
        icon!,
        color: iconColor ?? (isDangerous ? theme.colorScheme.error : theme.colorScheme.primary),
        size: 32,
      ) : null,
      title: Text(
        title,
        style: theme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      content: contentWidget ?? (content != null ? Text(
        content!,
        style: theme.textTheme.bodyLarge,
      ) : null),
      actions: [
        TextButton(
          onPressed: onCancel ?? () => Navigator.of(context).pop(false),
          child: Text(
            cancelText,
            style: TextStyle(
              color: cancelColor ?? theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: onConfirm ?? () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmColor ?? (isDangerous ? theme.colorScheme.error : null),
            foregroundColor: isDangerous ? theme.colorScheme.onError : null,
          ),
          child: Text(confirmText),
        ),
      ],
    );
  }

  /// Show the confirmation dialog
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    String? content,
    Widget? contentWidget,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmColor,
    Color? cancelColor,
    IconData? icon,
    Color? iconColor,
    bool isDangerous = false,
    bool barrierDismissible = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => CConfirmationDialog(
        title: title,
        content: content,
        contentWidget: contentWidget,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        confirmColor: confirmColor,
        cancelColor: cancelColor,
        icon: icon,
        iconColor: iconColor,
        isDangerous: isDangerous,
        barrierDismissible: barrierDismissible,
      ),
    );
  }

  /// Show a delete confirmation dialog
  static Future<bool?> showDelete({
    required BuildContext context,
    required String title,
    String content = 'This action cannot be undone.',
    Widget? contentWidget,
    String confirmText = 'Delete',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
  }) {
    return show(
      context: context,
      title: title,
      content: content,
      contentWidget: contentWidget,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
      icon: Icons.delete_outline,
      isDangerous: true,
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show a logout confirmation dialog
  static Future<bool?> showLogout({
    required BuildContext context,
    String title = 'Logout',
    String content = 'Are you sure you want to logout?',
    Widget? contentWidget,
    String confirmText = 'Logout',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
  }) {
    return show(
      context: context,
      title: title,
      content: content,
      contentWidget: contentWidget,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
      icon: Icons.logout,
      isDangerous: false,
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show a discard changes confirmation dialog
  static Future<bool?> showDiscardChanges({
    required BuildContext context,
    String title = 'Discard Changes',
    String content = 'You have unsaved changes. Are you sure you want to discard them?',
    Widget? contentWidget,
    String confirmText = 'Discard',
    String cancelText = 'Keep Editing',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
  }) {
    return show(
      context: context,
      title: title,
      content: content,
      contentWidget: contentWidget,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
      icon: Icons.warning_outlined,
      isDangerous: true,
      barrierDismissible: barrierDismissible,
    );
  }
}

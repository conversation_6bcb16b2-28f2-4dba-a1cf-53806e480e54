import 'package:sqflite/sqflite.dart';
import 'database_config.dart';

/// Base repository class cung cấp các phương thức CRUD cơ bản
abstract class BaseRepository<T> {
  /// Tên bảng trong database
  String get tableName;
  
  /// <PERSON>yển đổi từ Map sang Object
  T fromMap(Map<String, dynamic> map);
  
  /// Chuyển đổi từ Object sang Map
  Map<String, dynamic> toMap(T item);
  
  /// Lấy database instance
  Future<Database> get database async => await DatabaseConfig.database;
  
  /// Thêm mới một record
  Future<int> insert(T item) async {
    final db = await database;
    final map = toMap(item);
    
    // Thêm timestamp
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    
    return await db.insert(tableName, map);
  }
  
  /// Thêm nhiều records cùng lúc
  Future<List<int>> insertBatch(List<T> items) async {
    final db = await database;
    final batch = db.batch();
    final now = DateTime.now().toIso8601String();
    
    for (final item in items) {
      final map = toMap(item);
      map['created_at'] = now;
      map['updated_at'] = now;
      batch.insert(tableName, map);
    }
    
    final results = await batch.commit();
    return results.cast<int>();
  }
  
  /// Cập nhật một record theo ID
  Future<int> update(T item, int id) async {
    final db = await database;
    final map = toMap(item);
    
    // Cập nhật timestamp
    map['updated_at'] = DateTime.now().toIso8601String();
    
    return await db.update(
      tableName,
      map,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  /// Cập nhật với điều kiện tùy chỉnh
  Future<int> updateWhere(T item, String where, List<dynamic> whereArgs) async {
    final db = await database;
    final map = toMap(item);
    
    // Cập nhật timestamp
    map['updated_at'] = DateTime.now().toIso8601String();
    
    return await db.update(
      tableName,
      map,
      where: where,
      whereArgs: whereArgs,
    );
  }
  
  /// Xóa một record theo ID
  Future<int> delete(int id) async {
    final db = await database;
    return await db.delete(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  /// Xóa với điều kiện tùy chỉnh
  Future<int> deleteWhere(String where, List<dynamic> whereArgs) async {
    final db = await database;
    return await db.delete(
      tableName,
      where: where,
      whereArgs: whereArgs,
    );
  }
  
  /// Lấy một record theo ID
  Future<T?> findById(int id) async {
    final db = await database;
    final maps = await db.query(
      tableName,
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    
    if (maps.isNotEmpty) {
      return fromMap(maps.first);
    }
    return null;
  }
  
  /// Lấy tất cả records
  Future<List<T>> findAll({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    final maps = await db.query(
      tableName,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
  
  /// Tìm kiếm với điều kiện
  Future<List<T>> findWhere(
    String where,
    List<dynamic> whereArgs, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    final maps = await db.query(
      tableName,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
  
  /// Đếm tổng số records
  Future<int> count() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
    return Sqflite.firstIntValue(result) ?? 0;
  }
  
  /// Đếm với điều kiện
  Future<int> countWhere(String where, List<dynamic> whereArgs) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $tableName WHERE $where',
      whereArgs,
    );
    return Sqflite.firstIntValue(result) ?? 0;
  }
  
  /// Kiểm tra record có tồn tại không
  Future<bool> exists(int id) async {
    final count = await countWhere('id = ?', [id]);
    return count > 0;
  }
  
  /// Thực hiện raw query
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }
  
  /// Thực hiện raw insert/update/delete
  Future<int> rawExecute(
    String sql, [
    List<dynamic>? arguments,
  ]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }
  
  /// Tìm kiếm với phân trang
  Future<Map<String, dynamic>> findWithPagination({
    int page = 1,
    int pageSize = 20,
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
  }) async {
    final offset = (page - 1) * pageSize;
    
    // Lấy dữ liệu
    final items = await findWhere(
      where ?? '1=1',
      whereArgs ?? [],
      orderBy: orderBy,
      limit: pageSize,
      offset: offset,
    );
    
    // Đếm tổng số
    final totalCount = where != null 
        ? await countWhere(where, whereArgs ?? [])
        : await count();
    
    final totalPages = (totalCount / pageSize).ceil();
    
    return {
      'items': items,
      'pagination': {
        'current_page': page,
        'page_size': pageSize,
        'total_count': totalCount,
        'total_pages': totalPages,
        'has_next': page < totalPages,
        'has_previous': page > 1,
      },
    };
  }
  
  /// Xóa tất cả records (cẩn thận khi sử dụng)
  Future<int> deleteAll() async {
    final db = await database;
    return await db.delete(tableName);
  }
}

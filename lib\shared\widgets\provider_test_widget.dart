import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/face_capture_provider.dart';
import '../providers/face_detection_provider.dart';

/// Test widget to verify that providers are available in the widget tree
/// This can be used for debugging provider issues
class ProviderTestWidget extends StatelessWidget {
  const ProviderTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Provider Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Provider Availability Test',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Test FaceCaptureProvider
            _buildProviderTest<FaceCaptureProvider>(
              'FaceCaptureProvider',
              (provider) => [
                'Status: ${provider.status}',
                'Camera Ready: ${provider.isCameraReady}',
                'Streaming: ${provider.isStreamingEnabled}',
                'Available Cameras: ${provider.cameras.length}',
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Test FaceDetectionProvider
            _buildProviderTest<FaceDetectionProvider>(
              'FaceDetectionProvider',
              (provider) => [
                'Faces Detected: ${provider.faces.length}',
                'Has Best Face: ${provider.hasBestFace}',
                'Face Quality: ${provider.faceQuality.toStringAsFixed(2)}',
                'Is Detecting: ${provider.isDetecting}',
              ],
            ),
            
            const SizedBox(height: 30),
            
            // Test buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: () => _testCameraInitialization(context),
                  child: const Text('Test Camera'),
                ),
                const SizedBox(width: 10),
                ElevatedButton(
                  onPressed: () => _testFaceDetection(context),
                  child: const Text('Test Face Detection'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderTest<T extends ChangeNotifier>(
    String providerName,
    List<String> Function(T provider) getInfo,
  ) {
    return Consumer<T>(
      builder: (context, provider, child) {
        final info = getInfo(provider);
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  providerName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 8),
                ...info.map((item) => Text('• $item')),
              ],
            ),
          ),
        );
      },
    );
  }

  void _testCameraInitialization(BuildContext context) async {
    try {
      final cameraProvider = context.read<FaceCaptureProvider>();
      await cameraProvider.initializeCamera();
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Camera initialized successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Camera initialization failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _testFaceDetection(BuildContext context) async {
    try {
      final faceDetectionProvider = context.read<FaceDetectionProvider>();
      await faceDetectionProvider.initializeFaceDetector();
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Face detection initialized successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Face detection initialization failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'color_schemes.dart';

/// App text themes for consistent typography
/// 
/// Provides text styles for both light and dark themes
/// Based on Material Design 3 typography system
class AppTextThemes {
  
  // ============================================================================
  // FONT FAMILIES
  // ============================================================================
  
  static const String primaryFontFamily = 'Inter';
  static const String secondaryFontFamily = 'Roboto';
  static const String monospaceFontFamily = 'JetBrains Mono';
  
  // ============================================================================
  // LIGHT TEXT THEME
  // ============================================================================
  
  static TextTheme get lightTextTheme {
    return TextTheme(
      // Display styles
      displayLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.22,
      ),
      
      // Headline styles
      headlineLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.33,
      ),
      
      // Title styles
      titleLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.15,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.43,
      ),
      
      // Label styles
      labelLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.45,
      ),
      
      // Body styles
      bodyLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.15,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: AppColorSchemes.lightColorScheme.onBackground,
        height: 1.33,
      ),
    );
  }
  
  // ============================================================================
  // DARK TEXT THEME
  // ============================================================================
  
  static TextTheme get darkTextTheme {
    return TextTheme(
      // Display styles
      displayLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.22,
      ),
      
      // Headline styles
      headlineLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.33,
      ),
      
      // Title styles
      titleLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 22,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.15,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.43,
      ),
      
      // Label styles
      labelLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.45,
      ),
      
      // Body styles
      bodyLarge: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.15,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontFamily: primaryFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: AppColorSchemes.darkColorScheme.onBackground,
        height: 1.33,
      ),
    );
  }
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Get text theme based on brightness
  static TextTheme getTextTheme(Brightness brightness) {
    return brightness == Brightness.light ? lightTextTheme : darkTextTheme;
  }
  
  /// Get monospace text style for code/terminal display
  static TextStyle getMonospaceStyle({
    required Brightness brightness,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.w400,
  }) {
    final colorScheme = AppColorSchemes.getColorScheme(brightness);
    return TextStyle(
      fontFamily: monospaceFontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: colorScheme.onBackground,
      height: 1.4,
    );
  }
  
  /// Get caption text style for secondary information
  static TextStyle getCaptionStyle({
    required Brightness brightness,
    double fontSize = 12,
  }) {
    final colorScheme = AppColorSchemes.getColorScheme(brightness);
    return TextStyle(
      fontFamily: primaryFontFamily,
      fontSize: fontSize,
      fontWeight: FontWeight.w400,
      color: colorScheme.onSurfaceVariant,
      height: 1.33,
    );
  }
}

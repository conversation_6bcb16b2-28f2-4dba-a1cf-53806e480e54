# Server Communication Setup Guide

## 🎯 Overview

Hệ thống đăng ký device và giao tiếp server-device đã được hoàn thiện với các tính năng:

- **Device Registration**: Device tự động đăng ký với server
- **Command Center**: UI để gửi command từ server đến device
- **Real-time Communication**: WebSocket/MQTT cho giao tiếp 2 chiều
- **Security**: Mã hóa và xác thực an toàn
- **Terminal Widget**: UI trên mobile app để test commands

## 🚀 Quick Start

### 1. Start Test Server

**Windows:**
```cmd
start_test_server.bat
```

**Linux/Mac:**
```bash
./start_test_server.sh
```

**Manual:**
```bash
# Use simple test server (recommended)
node test_server_simple.js

# Or use enhanced test server
cd lib/packages/relay_controller/test_server
npm install
node server.js
```

Server sẽ chạy tại:
- **Dashboard**: http://localhost:3000
- **WebSocket**: ws://localhost:3000
- **API**: http://localhost:3000/api

### 2. Build & Run Terminal App

```bash
# Build terminal app
flutter build apk --debug -t lib/main_terminal.dart

# Or run in debug mode
flutter run -t lib/main_terminal.dart
```

### 3. Test Server Communication Widget

1. Mở terminal app trên mobile
2. Vào **Stream Screen** (màn hình chính)
3. Tìm **Server Communication Widget** ở góc dưới phải
4. Cấu hình server connection:
   - **Server Address**: localhost (hoặc IP của máy chạy server)
   - **Port**: 3000
   - **Device ID**: terminal_001 (hoặc ID tùy chọn)
   - **Device Name**: Terminal Device (hoặc tên tùy chọn)
5. Tap **Connect** để kết nối
6. Device sẽ xuất hiện trong web dashboard

## 🎮 Command Center Features

### Dashboard UI (Web)

Truy cập http://localhost:3000 để thấy:

1. **Device List**: Hiển thị tất cả devices đã đăng ký
2. **Command Center**: 
   - Select target device
   - Choose command type (relay_control, ping, status_request, etc.)
   - Configure payload parameters
   - Send command to device

3. **Quick Actions**:
   - Ping All Devices
   - Get All Status
   - Emergency Stop

### Terminal App UI

#### Stream Screen Widget (Floating)

Widget **Server Communication** xuất hiện ở góc dưới phải của Stream Screen:

1. **Server Configuration** (khi chưa kết nối):
   - Input fields cho Server Address, Port
   - Input fields cho Device ID, Device Name
   - Connect button với loading indicator

2. **Connection Status**:
   - Real-time connection indicator (green/red)
   - Last ping time
   - Relay status

3. **Quick Commands** (khi đã kết nối):
   - Ping server
   - Relay ON/OFF/Toggle
   - Get Status
   - Custom commands với JSON payload

4. **Command History**:
   - Scrollable log của tất cả commands
   - Timestamp và status cho mỗi command
   - Auto-scroll to latest

#### Terminal Screen Tabs

Trong terminal app, tab **Server** có đầy đủ tính năng tương tự nhưng trong layout riêng biệt.

## 📡 Command Types

### 1. Relay Control
```json
{
  "type": "relay_control",
  "payload": {
    "action": "on|off|toggle",
    "relay_id": "main_relay"
  }
}
```

### 2. Ping/Pong
```json
{
  "type": "ping",
  "payload": {
    "message": "ping"
  }
}
```

### 3. Status Request
```json
{
  "type": "status_request",
  "payload": {
    "component": "relay|all"
  }
}
```

### 4. Config Update
```json
{
  "type": "config_update",
  "payload": {
    "setting": "value"
  }
}
```

## 🔒 Security Features

- **Device Authentication**: Mỗi device có unique ID và token
- **Message Encryption**: Tất cả messages được mã hóa
- **Access Control**: Server kiểm tra quyền trước khi thực hiện command
- **Secure Transport**: WebSocket với TLS hoặc MQTT với authentication

## 🧪 Testing Workflow

### 1. Basic Connection Test
1. Start server
2. Run terminal app
3. Register device
4. Check device appears in dashboard
5. Send ping from dashboard
6. Verify pong response

### 2. Relay Control Test
1. Send "Relay ON" command from dashboard
2. Check terminal app receives command
3. Verify relay status updates
4. Send "Relay OFF" command
5. Confirm state changes

### 3. Bidirectional Communication
1. Send command from dashboard to device
2. Send command from device to server
3. Verify both directions work
4. Check command history in both UIs

## 🛠️ Configuration

### Server Settings
Edit `lib/packages/relay_controller/test_server/server.js`:

```javascript
const PORT = process.env.PORT || 3000;
const DEVICE_TIMEOUT = 30000; // 30 seconds
const MAX_DEVICES = 100;
```

### Device Settings
Edit device registration in terminal app:

```dart
final deviceRegistration = DeviceRegistration(
  deviceId: 'terminal_001',
  deviceName: 'Terminal Device',
  deviceType: 'relay_controller',
  capabilities: ['relay_control', 'face_auth'],
);
```

## 📊 Monitoring

### Dashboard Logs
- Device connections/disconnections
- Command send/receive
- Error messages
- Performance metrics

### Device Logs
- Command execution results
- Connection status
- Error handling
- System status

## 🔧 Troubleshooting

### Common Issues

1. **Device not appearing in dashboard**
   - Check network connection
   - Verify server is running
   - Check device registration logs

2. **Commands not working**
   - Verify device is connected
   - Check command format
   - Review error logs

3. **WebSocket connection fails**
   - Check firewall settings
   - Verify server URL
   - Try HTTP fallback

### Debug Commands

```bash
# Check server logs
tail -f server.log

# Test API endpoints
curl http://localhost:3000/api/devices

# Test WebSocket connection
wscat -c ws://localhost:3000
```

## 🚀 Next Steps

1. **Production Deployment**: Deploy server to cloud
2. **SSL/TLS**: Add HTTPS/WSS support
3. **Database**: Store device data persistently
4. **Monitoring**: Add metrics and alerting
5. **Mobile UI**: Enhance terminal app interface
6. **Authentication**: Add user login system

## 📝 API Reference

### REST Endpoints

- `POST /api/register` - Register new device
- `GET /api/devices` - Get device list
- `POST /api/command` - Send command to device
- `GET /api/status/:deviceId` - Get device status

### WebSocket Events

- `registerDevice` - Device registration
- `deviceCommand` - Send command to device
- `deviceCommandResponse` - Command response
- `deviceStateChanged` - Device state update
- `serverCommand` - Command from server to device

---

✅ **System Ready**: Server và terminal app đã sẵn sàng để test giao tiếp 2 chiều an toàn!

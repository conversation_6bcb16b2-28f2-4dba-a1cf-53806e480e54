import 'dart:typed_data';
import 'dart:math' as math;
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

import '../detection_engine.dart';

/// MediaPipe Face Detection Engine
/// Uses Google ML Kit Face Detection (MediaPipe-based) for real-time face detection
/// Optimized for Telpo F8 RK3399 hardware with detailed timing logs
class MediaPipeEngine implements DetectionEngine {
  // Google ML Kit Face Detector (MediaPipe-based)
  late FaceDetector _faceDetector;
  bool _isInitialized = false;

  // Configuration
  double _confidenceThreshold = 0.5;
  int _maxFaces = 5;
  bool _enableLandmarks = true;
  
  // Performance tracking
  int _totalFramesProcessed = 0;
  double _totalProcessingTime = 0.0;
  DateTime _lastFrameTime = DateTime.now();
  double _currentFPS = 0.0;

  @override
  String get name => 'MediaPipe-MLKit';

  @override
  String get version => '1.0.0';

  @override
  bool get isInitialized => _isInitialized;

  /// Configure the MediaPipe engine
  void configure({
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    _confidenceThreshold = confidenceThreshold ?? _confidenceThreshold;
    _maxFaces = maxFaces ?? _maxFaces;
    _enableLandmarks = enableLandmarks ?? _enableLandmarks;
  }

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing MediaPipe ML Kit Engine...');
        print('   Confidence Threshold: $_confidenceThreshold');
        print('   Max Faces: $_maxFaces');
        print('   Enable Landmarks: $_enableLandmarks');
      }

      // Initialize Google ML Kit Face Detector (MediaPipe-based)
      final options = FaceDetectorOptions(
        enableContours: _enableLandmarks,
        enableLandmarks: _enableLandmarks,
        enableClassification: false,
        enableTracking: false,
        minFaceSize: 0.1, // Minimum face size (10% of image)
        performanceMode: FaceDetectorMode.accurate, // Use accurate mode for better results
      );

      _faceDetector = FaceDetector(options: options);

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ MediaPipe ML Kit Engine initialized successfully');
        print('   Using Google ML Kit Face Detection (MediaPipe-based)');
        print('   Performance Mode: Accurate');
        print('   Landmarks: $_enableLandmarks');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize MediaPipe ML Kit Engine: $e');
      }
      rethrow;
    }
  }

  /// Convert CameraImage to InputImage for ML Kit
  InputImage _convertCameraImageToInputImage(CameraImage image) {
    final WriteBuffer allBytes = WriteBuffer();
    for (final Plane plane in image.planes) {
      allBytes.putUint8List(plane.bytes);
    }
    final bytes = allBytes.done().buffer.asUint8List();

    return InputImage.fromBytes(
      bytes: bytes,
      metadata: InputImageMetadata(
        size: Size(image.width.toDouble(), image.height.toDouble()),
        rotation: InputImageRotation.rotation0deg,
        format: InputImageFormat.nv21,
        bytesPerRow: image.planes[0].bytesPerRow,
      ),
    );
  }

  /// Convert ML Kit faces to DetectedFace objects
  List<DetectedFace> _convertMLKitFacesToDetectedFaces(List<Face> faces, int imageWidth, int imageHeight) {
    final List<DetectedFace> detectedFaces = [];

    for (final face in faces) {
      // Filter by confidence (ML Kit doesn't provide confidence directly, so we use tracking ID presence)
      final confidence = face.trackingId != null ? 0.9 : 0.7; // Estimate confidence

      if (confidence < _confidenceThreshold) continue;

      // Convert bounding box using custom Rect class
      final boundingBox = face.boundingBox;
      final normalizedBox = Rect(
        left: boundingBox.left / imageWidth,
        top: boundingBox.top / imageHeight,
        width: boundingBox.width / imageWidth,
        height: boundingBox.height / imageHeight,
      );

      // Extract landmarks if available
      final landmarks = <FaceLandmark>[];
      if (_enableLandmarks) {
        final leftEye = face.landmarks[FaceLandmarkType.leftEye];
        final rightEye = face.landmarks[FaceLandmarkType.rightEye];
        final noseBase = face.landmarks[FaceLandmarkType.noseBase];
        final leftMouth = face.landmarks[FaceLandmarkType.leftMouth];
        final rightMouth = face.landmarks[FaceLandmarkType.rightMouth];

        if (leftEye != null) {
          landmarks.add(FaceLandmark(
            type: FaceLandmarkType.leftEye,
            position: Offset(
              leftEye.position.x / imageWidth,
              leftEye.position.y / imageHeight,
            ),
          ));
        }
        if (rightEye != null) {
          landmarks.add(FaceLandmark(
            type: FaceLandmarkType.rightEye,
            position: Offset(
              rightEye.position.x / imageWidth,
              rightEye.position.y / imageHeight,
            ),
          ));
        }
        if (noseBase != null) {
          landmarks.add(FaceLandmark(
            type: FaceLandmarkType.noseBase,
            position: Offset(
              noseBase.position.x / imageWidth,
              noseBase.position.y / imageHeight,
            ),
          ));
        }
        if (leftMouth != null) {
          landmarks.add(FaceLandmark(
            type: FaceLandmarkType.leftMouth,
            position: Offset(
              leftMouth.position.x / imageWidth,
              leftMouth.position.y / imageHeight,
            ),
          ));
        }
        if (rightMouth != null) {
          landmarks.add(FaceLandmark(
            type: FaceLandmarkType.rightMouth,
            position: Offset(
              rightMouth.position.x / imageWidth,
              rightMouth.position.y / imageHeight,
            ),
          ));
        }
      }

      detectedFaces.add(DetectedFace(
        boundingBox: normalizedBox,
        confidence: confidence,
        landmarks: landmarks.isNotEmpty ? landmarks : null,
      ));
    }

    // Limit to max faces
    if (detectedFaces.length > _maxFaces) {
      detectedFaces.sort((a, b) => b.confidence.compareTo(a.confidence));
      return detectedFaces.take(_maxFaces).toList();
    }

    return detectedFaces;
  }
  
  @override
  Future<List<DetectedFace>> detectFaces(CameraImage image) async {
    if (!_isInitialized) {
      throw Exception('MediaPipe BlazeFace Engine not initialized');
    }

    // START: Total timing
    final totalStopwatch = Stopwatch()..start();

    try {
      if (kDebugMode) {
        print('🕐 [MediaPipe] Starting face detection...');
        print('   Input: ${image.width}x${image.height}');
      }

      // STEP 1: Input preprocessing (convert CameraImage to InputImage)
      final inputStopwatch = Stopwatch()..start();
      final inputImage = _convertCameraImageToInputImage(image);
      inputStopwatch.stop();

      if (kDebugMode) {
        print('⏱️ [MediaPipe] STEP 1 - Input conversion: ${inputStopwatch.elapsedMilliseconds}ms');
      }

      // STEP 2: ML Kit face detection (MediaPipe-based)
      final inferenceStopwatch = Stopwatch()..start();
      final faces = await _faceDetector.processImage(inputImage);
      inferenceStopwatch.stop();

      if (kDebugMode) {
        print('⏱️ [MediaPipe] STEP 2 - ML Kit inference: ${inferenceStopwatch.elapsedMilliseconds}ms');
      }

      // STEP 3: Output processing (convert ML Kit faces to DetectedFace)
      final outputStopwatch = Stopwatch()..start();
      final detectedFaces = _convertMLKitFacesToDetectedFaces(faces, image.width, image.height);
      outputStopwatch.stop();

      if (kDebugMode) {
        print('⏱️ [MediaPipe] STEP 3 - Output conversion: ${outputStopwatch.elapsedMilliseconds}ms');
      }

      // END: Total timing
      totalStopwatch.stop();

      if (kDebugMode) {
        print('⏱️ [MediaPipe] TOTAL TIME: ${totalStopwatch.elapsedMilliseconds}ms');
        print('   📊 Breakdown:');
        print('      Input conversion: ${inputStopwatch.elapsedMilliseconds}ms (${(inputStopwatch.elapsedMilliseconds / totalStopwatch.elapsedMilliseconds * 100).toStringAsFixed(1)}%)');
        print('      ML Kit inference: ${inferenceStopwatch.elapsedMilliseconds}ms (${(inferenceStopwatch.elapsedMilliseconds / totalStopwatch.elapsedMilliseconds * 100).toStringAsFixed(1)}%)');
        print('      Output conversion: ${outputStopwatch.elapsedMilliseconds}ms (${(outputStopwatch.elapsedMilliseconds / totalStopwatch.elapsedMilliseconds * 100).toStringAsFixed(1)}%)');
        print('   🎯 Detected ${detectedFaces.length} faces');
      }

      return detectedFaces;

    } catch (e) {
      totalStopwatch.stop();
      if (kDebugMode) {
        print('❌ MediaPipe detection failed after ${totalStopwatch.elapsedMilliseconds}ms: $e');
      }
      return [];
    }
  }

    // Original implementation (disabled due to TensorFlow Lite)
    // final startTime = DateTime.now();
    // try {
    //   final rgbBytes = _convertCameraImageToRGB(image);
    //   final faces = await detectFacesFromBytes(rgbBytes, image.width, image.height);
    //   _updatePerformanceStats(startTime);
    //   return faces;
    // } catch (e) {
    //   if (kDebugMode) print('❌ MediaPipe BlazeFace detection failed: $e');
    //   return [];
    // }
  }
  
  @override
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (!_isInitialized) {
      throw Exception('MediaPipe BlazeFace Engine not initialized');
    }

    // REAL IMPLEMENTATION: Use Google ML Kit Face Detection
    throw UnimplementedError('MediaPipe real implementation not available. Use ML Kit or UltraFace instead.');

    // Original implementation (disabled due to TensorFlow Lite)
    // try {
    //   final inputTensor = _preprocessImage(bytes, width, height);
    //   final outputTensors = <List<dynamic>>[];
    //   for (int i = 0; i < _detectionInterpreter!.getOutputTensors().length; i++) {
    //     final shape = _detectionInterpreter!.getOutputTensor(i).shape;
    //     final size = shape.reduce((a, b) => a * b);
    //     outputTensors.add(_reshapeList(List.filled(size, 0.0), shape));
    //   }
    //   _detectionInterpreter!.runForMultipleInputs([inputTensor], outputTensors.asMap());
    //   final faces = _postprocessBlazeFaceResults(outputTensors, width, height);
    //   if (_enableLandmarks && _landmarkInterpreter != null && faces.isNotEmpty) {
    //     final facesWithLandmarks = await _extractLandmarks(faces, bytes, width, height);
    //     return facesWithLandmarks;
    //   }
    //   return faces;
    // } catch (e) {
    //   if (kDebugMode) print('❌ MediaPipe BlazeFace inference failed: $e');
    //   return [];
    // }
  }
  
  @override
  Future<void> dispose() async {
    if (_detectionInterpreter != null) {
      _detectionInterpreter!.close();
      _detectionInterpreter = null;
    }

    if (_landmarkInterpreter != null) {
      _landmarkInterpreter!.close();
      _landmarkInterpreter = null;
    }

    _isInitialized = false;

    if (kDebugMode) {
      print('🗑️ MediaPipe BlazeFace Engine disposed');
    }
  }
  
  @override
  EngineStats getStats() {
    final avgProcessingTime = _totalFramesProcessed > 0 
        ? _totalProcessingTime / _totalFramesProcessed 
        : 0.0;
        
    return EngineStats(
      engineName: name,
      totalFramesProcessed: _totalFramesProcessed,
      averageProcessingTime: avgProcessingTime,
      currentFPS: _currentFPS,
      memoryUsageMB: _getMemoryUsage(),
      lastUpdated: DateTime.now(),
    );
  }
  
  /// Configure detection parameters
  void configure({
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    if (confidenceThreshold != null) {
      _confidenceThreshold = confidenceThreshold;
    }
    if (maxFaces != null) {
      _maxFaces = maxFaces;
    }
    if (enableLandmarks != null) {
      _enableLandmarks = enableLandmarks;
    }
  }
  
  // Private helper methods
  
  Future<bool> _isGPUAvailable() async {
    try {
      // Check if GPU delegate is available
      // final delegate = GpuDelegateV2(); // Mock implementation
      return true; // Assume available for now
    } catch (e) {
      return false;
    }
  }
  
  Uint8List _convertCameraImageToRGB(CameraImage image) {
    // Convert YUV420 to RGB (optimized for Telpo F8)
    final int width = image.width;
    final int height = image.height;
    final int uvRowStride = image.planes[1].bytesPerRow;
    final int uvPixelStride = image.planes[1].bytesPerPixel!;
    
    final rgbBytes = Uint8List(width * height * 3);
    
    // YUV to RGB conversion (performance-critical for Telpo F8)
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int yIndex = y * width + x;
        final int uvIndex = (y ~/ 2) * uvRowStride + (x ~/ 2) * uvPixelStride;
        
        final int yValue = image.planes[0].bytes[yIndex];
        final int uValue = image.planes[1].bytes[uvIndex];
        final int vValue = image.planes[2].bytes[uvIndex];
        
        // YUV to RGB conversion
        final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
        final int g = (yValue - 0.344 * (uValue - 128) - 0.714 * (vValue - 128)).round().clamp(0, 255);
        final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
        
        final int rgbIndex = yIndex * 3;
        rgbBytes[rgbIndex] = r;
        rgbBytes[rgbIndex + 1] = g;
        rgbBytes[rgbIndex + 2] = b;
      }
    }
    
    return rgbBytes;
  }
  
  List<List<List<List<double>>>> _preprocessImage(
    Uint8List bytes,
    int width,
    int height,
  ) {
    // Resize to BlazeFace input size (128x128) and normalize
    final resizedBytes = _resizeImage(bytes, width, height, _inputWidth, _inputHeight);
    
    // Convert to tensor format [1, height, width, channels]
    final input = List.generate(
      1,
      (b) => List.generate(
        _inputHeight,
        (y) => List.generate(
          _inputWidth,
          (x) => List.generate(
            _inputChannels,
            (c) {
              final index = (y * _inputWidth + x) * _inputChannels + c;
              // Normalize to [0, 1] range for BlazeFace
              return resizedBytes[index] / 255.0;
            },
          ),
        ),
      ),
    );
    
    return input;
  }
  
  Uint8List _resizeImage(Uint8List bytes, int width, int height, int newWidth, int newHeight) {
    // Simple bilinear interpolation resize
    final resized = Uint8List(newWidth * newHeight * 3);
    
    final xRatio = width / newWidth;
    final yRatio = height / newHeight;
    
    for (int y = 0; y < newHeight; y++) {
      for (int x = 0; x < newWidth; x++) {
        final srcX = (x * xRatio).floor();
        final srcY = (y * yRatio).floor();
        
        final srcIndex = (srcY * width + srcX) * 3;
        final dstIndex = (y * newWidth + x) * 3;
        
        if (srcIndex + 2 < bytes.length && dstIndex + 2 < resized.length) {
          resized[dstIndex] = bytes[srcIndex];
          resized[dstIndex + 1] = bytes[srcIndex + 1];
          resized[dstIndex + 2] = bytes[srcIndex + 2];
        }
      }
    }
    
    return resized;
  }

  List<DetectedFace> _postprocessBlazeFaceResults(
    List<List<dynamic>> outputTensors,
    int originalWidth,
    int originalHeight,
  ) {
    final faces = <DetectedFace>[];

    try {
      // BlazeFace output format:
      // Output 0: Bounding boxes [1, num_anchors, 4] (x, y, w, h)
      // Output 1: Classification scores [1, num_anchors, 1]

      if (outputTensors.length < 2) {
        if (kDebugMode) {
          print('⚠️ BlazeFace: Expected 2 outputs, got ${outputTensors.length}');
        }
        return [];
      }

      final boxes = outputTensors[0][0] as List; // Remove batch dimension
      final scores = outputTensors[1][0] as List; // Remove batch dimension

      if (kDebugMode) {
        print('📊 BlazeFace: Processing ${boxes.length} detections');
      }

      // Process each detection
      for (int i = 0; i < boxes.length && i < scores.length; i++) {
        final box = boxes[i] as List<double>;
        final score = scores[i] is List ? (scores[i] as List)[0] : scores[i] as double;

        if (score >= _confidenceThreshold && box.length >= 4) {
          // BlazeFace outputs normalized coordinates [0, 1]
          // Convert to pixel coordinates
          final centerX = box[0] * originalWidth;
          final centerY = box[1] * originalHeight;
          final width = box[2] * originalWidth;
          final height = box[3] * originalHeight;

          // Convert center-based to corner-based coordinates
          final left = centerX - width / 2;
          final top = centerY - height / 2;

          // Clamp to image bounds
          final clampedLeft = left.clamp(0.0, originalWidth.toDouble());
          final clampedTop = top.clamp(0.0, originalHeight.toDouble());
          final clampedWidth = (left + width).clamp(0.0, originalWidth.toDouble()) - clampedLeft;
          final clampedHeight = (top + height).clamp(0.0, originalHeight.toDouble()) - clampedTop;

          if (clampedWidth > 0 && clampedHeight > 0) {
            faces.add(DetectedFace(
              boundingBox: Rect(
                left: clampedLeft,
                top: clampedTop,
                width: clampedWidth,
                height: clampedHeight,
              ),
              confidence: score,
            ));
          }
        }
      }

      // Apply Non-Maximum Suppression (NMS)
      final nmsResults = _applyNMS(faces, 0.3); // IoU threshold

      // Sort by confidence and limit to maxFaces
      nmsResults.sort((a, b) => b.confidence.compareTo(a.confidence));
      return nmsResults.take(_maxFaces).toList();

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to postprocess BlazeFace results: $e');
      }
      return [];
    }
  }

  List<DetectedFace> _applyNMS(List<DetectedFace> faces, double iouThreshold) {
    if (faces.isEmpty) return [];

    // Sort by confidence (descending)
    faces.sort((a, b) => b.confidence.compareTo(a.confidence));

    final selected = <DetectedFace>[];
    final suppressed = <bool>[];

    for (int i = 0; i < faces.length; i++) {
      suppressed.add(false);
    }

    for (int i = 0; i < faces.length; i++) {
      if (suppressed[i]) continue;

      selected.add(faces[i]);

      // Suppress overlapping boxes
      for (int j = i + 1; j < faces.length; j++) {
        if (suppressed[j]) continue;

        final iou = faces[i].boundingBox.iou(faces[j].boundingBox);
        if (iou > iouThreshold) {
          suppressed[j] = true;
        }
      }
    }

    return selected;
  }

  void _updatePerformanceStats(DateTime startTime) {
    final processingTime = DateTime.now().difference(startTime).inMicroseconds / 1000.0;

    _totalFramesProcessed++;
    _totalProcessingTime += processingTime;

    // Calculate FPS
    final now = DateTime.now();
    final timeDiff = now.difference(_lastFrameTime).inMicroseconds / 1000000.0;
    if (timeDiff > 0) {
      _currentFPS = 1.0 / timeDiff;
    }
    _lastFrameTime = now;
  }

  int _getMemoryUsage() {
    // Estimate memory usage for BlazeFace + landmarks
    return _enableLandmarks ? 60 : 35; // MB - placeholder
  }

  // Helper method to reshape list (mock implementation)
  dynamic _reshapeList(List<double> list, List<int> shape) {
    // Simple mock implementation - just return the list
    // In real implementation, this would reshape the tensor
    return list;
  }

  /// Extract facial landmarks for detected faces
  Future<List<DetectedFace>> _extractLandmarks(
    List<DetectedFace> faces,
    Uint8List imageBytes,
    int imageWidth,
    int imageHeight,
  ) async {
    if (_landmarkInterpreter == null) return faces;

    final facesWithLandmarks = <DetectedFace>[];

    for (final face in faces) {
      try {
        // Crop face region from image
        final faceImage = _cropFaceRegion(
          imageBytes,
          imageWidth,
          imageHeight,
          face.boundingBox,
        );

        // Preprocess for landmark model (typically 192x192 for MediaPipe)
        final landmarkInput = _preprocessForLandmarks(faceImage, 192, 192);

        // Prepare output tensor for landmarks
        // MediaPipe Face Landmarker typically outputs 468 landmarks (x, y, z)
        final landmarkOutput = List.generate(
          1,
          (b) => List.generate(
            468,
            (i) => List.filled(3, 0.0),
          ),
        );

        // Run landmark inference
        _landmarkInterpreter!.run(landmarkInput, landmarkOutput);

        // Convert landmarks to face landmark points
        final landmarks = _convertToFaceLandmarks(landmarkOutput, face.boundingBox);

        // Create new DetectedFace with landmarks
        facesWithLandmarks.add(DetectedFace(
          boundingBox: face.boundingBox,
          confidence: face.confidence,
          landmarks: landmarks,
          headEulerAngleY: face.headEulerAngleY,
          headEulerAngleZ: face.headEulerAngleZ,
          leftEyeOpenProbability: face.leftEyeOpenProbability,
          rightEyeOpenProbability: face.rightEyeOpenProbability,
          smilingProbability: face.smilingProbability,
          trackingId: face.trackingId,
        ));

      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Failed to extract landmarks for face: $e');
        }
        // Add face without landmarks if landmark extraction fails
        facesWithLandmarks.add(face);
      }
    }

    return facesWithLandmarks;
  }

  /// Crop face region from full image
  Uint8List _cropFaceRegion(
    Uint8List imageBytes,
    int imageWidth,
    int imageHeight,
    Rect faceBox,
  ) {
    // Expand bounding box slightly for better landmark detection
    final expandRatio = 0.2;
    final expandedWidth = faceBox.width * (1 + expandRatio);
    final expandedHeight = faceBox.height * (1 + expandRatio);

    final expandedLeft = (faceBox.left - expandedWidth * expandRatio / 2)
        .clamp(0.0, imageWidth.toDouble());
    final expandedTop = (faceBox.top - expandedHeight * expandRatio / 2)
        .clamp(0.0, imageHeight.toDouble());

    final cropWidth = (expandedLeft + expandedWidth)
        .clamp(0.0, imageWidth.toDouble()) - expandedLeft;
    final cropHeight = (expandedTop + expandedHeight)
        .clamp(0.0, imageHeight.toDouble()) - expandedTop;

    // Simple crop implementation (can be optimized)
    final croppedBytes = Uint8List((cropWidth * cropHeight * 3).round());

    for (int y = 0; y < cropHeight; y++) {
      for (int x = 0; x < cropWidth; x++) {
        final srcX = (expandedLeft + x).round();
        final srcY = (expandedTop + y).round();

        if (srcX < imageWidth && srcY < imageHeight) {
          final srcIndex = (srcY * imageWidth + srcX) * 3;
          final dstIndex = (y * cropWidth.round() + x) * 3;

          if (srcIndex + 2 < imageBytes.length && dstIndex + 2 < croppedBytes.length) {
            croppedBytes[dstIndex] = imageBytes[srcIndex];
            croppedBytes[dstIndex + 1] = imageBytes[srcIndex + 1];
            croppedBytes[dstIndex + 2] = imageBytes[srcIndex + 2];
          }
        }
      }
    }

    return croppedBytes;
  }

  /// Preprocess cropped face for landmark detection
  List<List<List<List<double>>>> _preprocessForLandmarks(
    Uint8List faceBytes,
    int targetWidth,
    int targetHeight,
  ) {
    // Resize face image to landmark model input size
    final resizedBytes = _resizeImage(
      faceBytes,
      math.sqrt(faceBytes.length ~/ 3).round(),
      math.sqrt(faceBytes.length ~/ 3).round(),
      targetWidth,
      targetHeight,
    );

    // Convert to tensor format [1, height, width, channels]
    final input = List.generate(
      1,
      (b) => List.generate(
        targetHeight,
        (y) => List.generate(
          targetWidth,
          (x) => List.generate(
            3,
            (c) {
              final index = (y * targetWidth + x) * 3 + c;
              // Normalize to [0, 1] range for MediaPipe landmarks
              return index < resizedBytes.length ? resizedBytes[index] / 255.0 : 0.0;
            },
          ),
        ),
      ),
    );

    return input;
  }

  /// Convert raw landmark output to FaceLandmark objects
  List<FaceLandmark> _convertToFaceLandmarks(
    List<dynamic> landmarkOutput,
    Rect faceBox,
  ) {
    final landmarks = <FaceLandmark>[];

    try {
      final rawLandmarks = landmarkOutput[0] as List; // Remove batch dimension

      // MediaPipe Face Landmarker key points mapping
      final keyPointIndices = {
        // Eyes
        33: FaceLandmarkType.leftEye,   // Left eye center
        362: FaceLandmarkType.rightEye, // Right eye center

        // Nose
        1: FaceLandmarkType.noseBase,   // Nose tip

        // Ears (approximate)
        234: FaceLandmarkType.leftEar,  // Left ear
        454: FaceLandmarkType.rightEar, // Right ear

        // Mouth
        61: FaceLandmarkType.leftMouth,  // Left mouth corner
        291: FaceLandmarkType.rightMouth, // Right mouth corner

        // Cheeks (approximate)
        116: FaceLandmarkType.leftCheek,  // Left cheek
        345: FaceLandmarkType.rightCheek, // Right cheek
      };

      for (final entry in keyPointIndices.entries) {
        final index = entry.key;
        final type = entry.value;

        if (index < rawLandmarks.length) {
          final point = rawLandmarks[index] as List<double>;

          if (point.length >= 2) {
            // Convert normalized coordinates to face-relative coordinates
            final x = faceBox.left + (point[0] * faceBox.width);
            final y = faceBox.top + (point[1] * faceBox.height);

            landmarks.add(FaceLandmark(
              type: type,
              position: Offset(x, y),
            ));
          }
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to convert landmarks: $e');
      }
    }

    return landmarks;
  }
}

#!/usr/bin/env dart

import 'dart:io';

/// <PERSON>ript to check the current status of face detection models and configuration
void main(List<String> args) async {
  print('🔍 Checking Face Detection Model Status...');
  
  final checker = ModelStatusChecker();
  await checker.checkStatus();
}

class ModelStatusChecker {
  static const String modelsDir = 'lib/packages/face_recognition/assets/models';
  static const String pubspecPath = 'pubspec.yaml';
  static const String providerPath = 'lib/shared/providers/face_detection_provider.dart';
  
  Future<void> checkStatus() async {
    print('\n📊 Face Detection System Status Report');
    print('=' * 60);
    
    await _checkDependencies();
    await _checkModels();
    await _checkCodeStatus();
    await _checkConfiguration();
    
    print('\n📋 Summary & Recommendations');
    print('=' * 60);
    await _provideSummary();
  }
  
  /// Check dependency status
  Future<void> _checkDependencies() async {
    print('\n📦 Dependencies Status:');
    
    final pubspecFile = File(pubspecPath);
    if (!await pubspecFile.exists()) {
      print('  ❌ pubspec.yaml not found');
      return;
    }
    
    final content = await pubspecFile.readAsString();
    
    // Check TensorFlow Lite
    if (content.contains('tflite_flutter: ^0.9.0') && 
        !content.contains('# tflite_flutter: ^0.9.0')) {
      print('  ✅ TensorFlow Lite: ENABLED');
    } else if (content.contains('# tflite_flutter: ^0.9.0')) {
      print('  ❌ TensorFlow Lite: DISABLED (commented out)');
    } else {
      print('  ❌ TensorFlow Lite: NOT FOUND');
    }
    
    // Check other dependencies
    final dependencies = [
      'google_mlkit_face_detection',
      'camera',
      'flutter',
    ];
    
    for (final dep in dependencies) {
      if (content.contains('$dep:')) {
        print('  ✅ $dep: FOUND');
      } else {
        print('  ❌ $dep: NOT FOUND');
      }
    }
  }
  
  /// Check model files status
  Future<void> _checkModels() async {
    print('\n🧠 Model Files Status:');
    
    final modelsDirectory = Directory(modelsDir);
    if (!await modelsDirectory.exists()) {
      print('  ❌ Models directory not found: $modelsDir');
      return;
    }
    
    final requiredModels = [
      'ultraface_320.tflite',
      'mobilefacenet.tflite',
      'mediapipe_face.tflite',
    ];
    
    for (final modelName in requiredModels) {
      final modelFile = File('$modelsDir/$modelName');
      if (await modelFile.exists()) {
        final size = await modelFile.length();
        final sizeStr = _formatFileSize(size);
        
        // Check if it's a placeholder
        final infoFile = File('$modelsDir/$modelName.info');
        if (await infoFile.exists()) {
          final info = await infoFile.readAsString();
          if (info.contains('Placeholder')) {
            print('  ⚠️ $modelName: PLACEHOLDER ($sizeStr)');
          } else {
            print('  ✅ $modelName: READY ($sizeStr)');
          }
        } else {
          print('  ✅ $modelName: READY ($sizeStr)');
        }
      } else {
        print('  ❌ $modelName: NOT FOUND');
      }
    }
    
    // Check for other files
    final files = await modelsDirectory.list().toList();
    final otherFiles = files.where((f) => 
        f is File && 
        !requiredModels.any((m) => f.path.endsWith(m)) &&
        !f.path.endsWith('.info')
    ).toList();
    
    if (otherFiles.isNotEmpty) {
      print('  📁 Other files found:');
      for (final file in otherFiles) {
        final name = file.path.split('/').last;
        if (file is File) {
          final size = await (file as File).length();
          print('     - $name (${_formatFileSize(size)})');
        }
      }
    }
  }
  
  /// Check code status
  Future<void> _checkCodeStatus() async {
    print('\n💻 Code Status:');
    
    final providerFile = File(providerPath);
    if (!await providerFile.exists()) {
      print('  ❌ FaceDetectionProvider not found');
      return;
    }
    
    final content = await providerFile.readAsString();
    
    // Check imports
    final imports = [
      'hybrid_detector.dart',
      'detection_engine.dart\' as hybrid',
      'hardware_controller.dart',
    ];
    
    print('  📥 Imports:');
    for (final import in imports) {
      if (content.contains('import \'../../packages/face_recognition/src') && 
          content.contains(import) &&
          !content.contains('// import \'../../packages/face_recognition/src')) {
        print('     ✅ $import: ENABLED');
      } else if (content.contains('// import \'../../packages/face_recognition/src') && 
                 content.contains(import)) {
        print('     ❌ $import: DISABLED (commented)');
      } else {
        print('     ❌ $import: NOT FOUND');
      }
    }
    
    // Check hybrid system initialization
    print('  🔧 Hybrid System:');
    if (content.contains('if (await _shouldUseHybridSystem()) {') &&
        !content.contains('// if (await _shouldUseHybridSystem()) {')) {
      print('     ✅ Initialization: ENABLED');
    } else {
      print('     ❌ Initialization: DISABLED');
    }
    
    // Check current detection system
    if (content.contains('🔄 Using ML Kit Face Detection System')) {
      print('     📊 Current System: ML Kit (fallback)');
    } else {
      print('     📊 Current System: Unknown');
    }
    
    // Check hybrid properties
    final hybridProps = [
      'HybridDetector? _hybridDetector;',
      'List<hybrid.DetectedFace> _hybridFaces',
      'hybrid.DetectedFace? _bestHybridFace;',
    ];
    
    print('  🏗️ Hybrid Properties:');
    for (final prop in hybridProps) {
      if (content.contains(prop) && !content.contains('// $prop')) {
        print('     ✅ ${prop.split(' ').last}: ENABLED');
      } else {
        print('     ❌ ${prop.split(' ').last}: DISABLED');
      }
    }
  }
  
  /// Check configuration
  Future<void> _checkConfiguration() async {
    print('\n⚙️ Configuration Status:');
    
    // Check pubspec assets
    final pubspecFile = File(pubspecPath);
    final pubspecContent = await pubspecFile.readAsString();
    
    if (pubspecContent.contains('lib/packages/face_recognition/assets/models/')) {
      print('  ✅ Assets configuration: ENABLED');
    } else {
      print('  ❌ Assets configuration: NOT CONFIGURED');
    }
    
    // Check config file
    final configFile = File('$modelsDir/ultraface_config.yaml');
    if (await configFile.exists()) {
      print('  ✅ UltraFace config: FOUND');
    } else {
      print('  ❌ UltraFace config: NOT FOUND');
    }
    
    // Check build configuration
    final buildFiles = [
      'android/app/build.gradle',
      'ios/Runner.xcodeproj/project.pbxproj',
    ];
    
    print('  📱 Platform Configuration:');
    for (final buildFile in buildFiles) {
      final file = File(buildFile);
      if (await file.exists()) {
        print('     ✅ ${buildFile.split('/').first}: CONFIGURED');
      } else {
        print('     ⚠️ ${buildFile.split('/').first}: NOT FOUND');
      }
    }
  }
  
  /// Provide summary and recommendations
  Future<void> _provideSummary() async {
    final pubspecFile = File(pubspecPath);
    final pubspecContent = await pubspecFile.readAsString();
    final providerFile = File(providerPath);
    final providerContent = await providerFile.readAsString();
    
    // Determine current status
    final tfliteEnabled = pubspecContent.contains('tflite_flutter: ^0.9.0') && 
                         !pubspecContent.contains('# tflite_flutter: ^0.9.0');
    
    final hybridEnabled = providerContent.contains('if (await _shouldUseHybridSystem()) {') &&
                         !providerContent.contains('// if (await _shouldUseHybridSystem()) {');
    
    final modelsExist = await _checkModelsExist();
    
    print('\n🎯 Current Status:');
    if (tfliteEnabled && hybridEnabled && modelsExist) {
      print('  ✅ UltraFace System: FULLY ENABLED');
      print('  📊 Detection Engine: UltraFace + ML Kit (hybrid)');
      print('  🚀 Expected Performance: 45+ FPS on Telpo F8');
    } else if (tfliteEnabled && hybridEnabled && !modelsExist) {
      print('  ⚠️ UltraFace System: PARTIALLY ENABLED (missing models)');
      print('  📊 Detection Engine: ML Kit (fallback)');
      print('  🐌 Current Performance: 15 FPS');
    } else {
      print('  ❌ UltraFace System: DISABLED');
      print('  📊 Detection Engine: ML Kit only');
      print('  🐌 Current Performance: 15 FPS');
    }
    
    print('\n💡 Recommendations:');
    
    if (!tfliteEnabled) {
      print('  1. Enable TensorFlow Lite dependency:');
      print('     - Uncomment "tflite_flutter: ^0.9.0" in pubspec.yaml');
      print('     - Run "flutter pub get"');
    }
    
    if (!modelsExist) {
      print('  2. Download model files:');
      print('     - ultraface_320.tflite (1.1MB)');
      print('     - mobilefacenet.tflite (2.3MB)');
      print('     - mediapipe_face.tflite (2.5MB)');
      print('     - Place in: $modelsDir');
    }
    
    if (!hybridEnabled) {
      print('  3. Enable hybrid system code:');
      print('     - Uncomment hybrid system initialization');
      print('     - Enable hybrid imports and properties');
    }
    
    if (tfliteEnabled && hybridEnabled && modelsExist) {
      print('  🎉 System is ready! Build and test on Telpo F8:');
      print('     - flutter build apk --target lib/apps/terminal/main_terminal.dart --flavor terminal');
      print('     - Expected 3x performance improvement');
    } else {
      print('  🔧 Quick enable command:');
      print('     - dart scripts/enable_ultraface.dart');
    }
  }
  
  /// Check if required models exist
  Future<bool> _checkModelsExist() async {
    final requiredModels = [
      'ultraface_320.tflite',
      'mobilefacenet.tflite',
      'mediapipe_face.tflite',
    ];
    
    for (final model in requiredModels) {
      final file = File('$modelsDir/$model');
      if (!await file.exists()) return false;
      
      // Check if it's not just a placeholder
      final infoFile = File('$modelsDir/$model.info');
      if (await infoFile.exists()) {
        final info = await infoFile.readAsString();
        if (info.contains('Placeholder')) return false;
      }
    }
    
    return true;
  }
  
  /// Format file size
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / 1024 / 1024).toStringAsFixed(1)}MB';
  }
}

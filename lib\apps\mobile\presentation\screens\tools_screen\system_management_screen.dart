import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../users_screen.dart';

/// <PERSON>àn hình quản trị hệ thống
class SystemManagementScreen extends StatelessWidget {
  const SystemManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.spacing2),
                    _buildManagementList(context),
                    SizedBox(height: AppDimensions.spacing24),
                    // Add bottom padding to avoid TabsBar overlap
                    SizedBox(height: AppDimensions.tabsBarHeight),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Text(
            'Quản trị hệ thống',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          _buildManagementItem(
            icon: _buildOrganizationIcon(),
            title: 'Quản lý tổ chức',
            description: 'Thiết lập và quản lý thành viên tổ chức',
            onTap: () async {
              if (kDebugMode) {
                print('SystemManagement: Navigating to users screen');
              }

              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UsersScreen(),
                ),
              );

              if (kDebugMode) {
                print('SystemManagement: Returned from users screen with result: $result');
              }
            },
          ),
          SizedBox(height: AppDimensions.spacing16),
          _buildManagementItem(
            icon: _buildSystemSettingsIcon(),
            title: 'Thiết lập hệ thống',
            description: 'Cấu hình và giám sát hệ thống',
            onTap: () {
              // Navigate to system settings screen
            },
          ),
        ],
      ),
    );
  }

  Widget _buildManagementItem({
    required Widget icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingS),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(AppDimensions.radiusRound),
              ),
              child: icon,
            ),
            SizedBox(width: AppDimensions.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacing4),
                  Text(
                    description,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: AppDimensions.spacing8),
            Icon(Icons.chevron_right, size: 16, color: AppColors.textPrimary),
          ],
        ),
      ),
    );
  }

  Widget _buildOrganizationIcon() {
    return Container(
      width: 18,
      height: 18,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: AppColors.primary, width: 1.5),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 4,
            top: 2,
            child: Container(
              width: 8,
              height: 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: AppColors.primary, width: 1.5),
              ),
            ),
          ),
          Positioned(
            left: 2,
            bottom: 2,
            child: Container(
              width: 12,
              height: 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColors.primary, width: 1.5),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemSettingsIcon() {
    return SizedBox(
      width: 18,
      height: 18,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.primary, width: 1.5),
              ),
            ),
          ),
          Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppColors.primary, width: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
# Face Recognition Model Setup Scripts

This directory contains scripts to download and convert real face recognition models to replace the mock models in the project.

## Overview

The current project uses mock/placeholder models for face recognition. These scripts will download official, pre-trained models and convert them to the correct TensorFlow Lite format for use in the Flutter application.

## Models

The scripts will download and set up the following models:

1. **UltraFace RFB-320** (`ultraface_320.tflite`)
   - Source: [Ultra-Light-Fast-Generic-Face-Detector-1MB](https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB)
   - Size: ~1.1 MB
   - Purpose: Lightweight face detection
   - Input: 320x240 RGB image
   - Output: Face bounding boxes and confidence scores

2. **MobileFaceNet** (`mobilefacenet.tflite`)
   - Source: [InsightFace Buffalo_L](https://github.com/deepinsight/insightface)
   - Size: ~5.2 MB
   - Purpose: Face recognition/embedding
   - Input: 192x192 RGB face image
   - Output: Face embedding vector

3. **MediaPipe BlazeFace** (`mediapipe_face.tflite`)
   - Source: [PINTO Model Zoo](https://github.com/PINTO0309/PINTO_model_zoo)
   - Size: ~0.2 MB
   - Purpose: Fast face detection for mobile
   - Input: 128x128 RGB image
   - Output: Face landmarks and detection boxes

## ⚠️ IMPORTANT: Current Model Status

**The current models are TEMPORARY REPLACEMENTS** using MediaPipe models instead of the original UltraFace/MobileFaceNet models. See `lib/packages/face_recognition/assets/models/MODEL_WARNING.md` for details.

## Quick Setup

### Option 1: One-Click Setup (Recommended)

Run the simple setup script:

```bash
# From the project root directory
python scripts/setup_face_models.py
```

This will:
1. Check current models (identify mock vs real)
2. Install required dependencies (requests)
3. Download real models from official sources (currently MediaPipe as temporary replacement)
4. Verify the setup
5. Create warning files about temporary model status

### Option 2: Check Models Only

To check if your current models are real or mock:

```bash
python scripts/check_models.py
```

### Option 3: Manual Download

If you prefer to download models manually:

```bash
# 1. Install requests
pip install requests

# 2. Download all models
python scripts/download_real_models.py

# 3. Download specific model
python scripts/download_real_models.py --model ultraface_320.tflite
```

## Script Options

### setup_models.py

```bash
python setup_models.py [OPTIONS]

Options:
  --force        Force download/conversion even if files exist
  --skip-deps    Skip dependency installation
```

### download_models.py

```bash
python download_models.py [OPTIONS]

Options:
  --models-dir DIR    Directory to save models (default: ../lib/packages/face_recognition/assets/models)
  --force            Force download even if files exist
  --model MODEL      Download specific model only (ultraface_320.tflite, mobilefacenet.tflite, mediapipe_face.tflite)
```

### convert_models.py

```bash
python convert_models.py [OPTIONS]

Options:
  --models-dir DIR    Directory to save models
  --force            Force conversion even if files exist
```

## Requirements

- Python 3.8 or higher
- Internet connection for downloading models
- ~2GB free disk space (temporary files during conversion)

### Python Dependencies

The scripts will automatically install these dependencies:

- `tensorflow>=2.8.0` - For TensorFlow Lite conversion
- `onnx>=1.12.0` - For ONNX model loading
- `onnx-tf>=1.9.0` - For ONNX to TensorFlow conversion
- `requests>=2.25.0` - For downloading models
- `numpy>=1.21.0` - For numerical operations

## Troubleshooting

### Common Issues

1. **"Missing required dependencies" error**
   ```bash
   pip install tensorflow onnx onnx-tf requests numpy
   ```

2. **Download fails with SSL errors**
   ```bash
   pip install --upgrade certifi
   ```

3. **ONNX conversion fails**
   - Try updating TensorFlow: `pip install --upgrade tensorflow`
   - Check available disk space
   - Ensure you have sufficient RAM (4GB+ recommended)

4. **Permission errors on Windows**
   - Run command prompt as Administrator
   - Or use: `python -m pip install --user -r requirements.txt`

### Verification

After running the setup, verify the models:

```bash
# Check model files exist and have reasonable sizes
ls -la ../lib/packages/face_recognition/assets/models/

# Should show:
# ultraface_320.tflite     (~1.1 MB)
# mobilefacenet.tflite     (~5.2 MB)  
# mediapipe_face.tflite    (~0.2 MB)
```

### Manual Verification

You can test the models using TensorFlow Lite:

```python
import tensorflow as tf

# Load and test a model
interpreter = tf.lite.Interpreter(model_path="path/to/model.tflite")
interpreter.allocate_tensors()

# Get input/output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

print("Input shape:", input_details[0]['shape'])
print("Output shape:", output_details[0]['shape'])
```

## Model Sources and Licenses

- **UltraFace**: MIT License - [Repository](https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB)
- **InsightFace**: Non-commercial research use - [Repository](https://github.com/deepinsight/insightface)
- **MediaPipe BlazeFace**: Apache 2.0 License - [Repository](https://github.com/PINTO0309/PINTO_model_zoo)

Please ensure your use case complies with the respective licenses.

## Integration with Flutter

After running these scripts, the Flutter application should automatically use the new models. The model loading code in the Flutter app expects the models to be in the exact locations and formats that these scripts provide.

No changes to the Flutter code should be necessary - the scripts maintain the same file names and directory structure as the original mock models.

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Ensure you have a stable internet connection
3. Verify you have sufficient disk space
4. Check that your Python version is 3.8 or higher

For model-specific issues, refer to the original model repositories linked above.

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';

import '../../config/app_config.dart';
import '../../errors/error_handler.dart';
import '../../storage/secure_storage_service.dart';
import '../../../services/cookie_service.dart';
import '../../../services/auth_state_service.dart';
import '../../../services/jwt_decoder_service.dart';
// Note: AppStateProvider will be migrated in presentation layer tasks

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến core của ứng dụng
void registerCoreDependencies() {
  // External dependencies
  getIt.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    ),
  );

  // Storage Services
  getIt.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(getIt<FlutterSecureStorage>()),
  );

  // Error Handling
  getIt.registerLazySingleton<ErrorHandler>(
    () => ErrorHandlerImpl(),
  );

  // App Configuration
  getIt.registerLazySingleton<AppConfig>(
    () => AppConfig(),
  );

  // Cookie Service
  getIt.registerLazySingleton<CookieService>(
    () => CookieService(),
  );

  // Auth State Service
  getIt.registerLazySingleton<AuthStateService>(
    () => AuthStateService(),
  );

  // JWT Decoder Service
  getIt.registerLazySingleton<JwtDecoderService>(
    () => JwtDecoderService(),
  );

  // Note: AppStateProvider will be registered when presentation layer is migrated
}

/// Unregister tất cả core dependencies (for testing)
void unregisterCoreDependencies() {
  // Auth State Service
  if (getIt.isRegistered<AuthStateService>()) {
    getIt.unregister<AuthStateService>();
  }

  // Cookie Service
  if (getIt.isRegistered<CookieService>()) {
    getIt.unregister<CookieService>();
  }

  // App Configuration
  if (getIt.isRegistered<AppConfig>()) {
    getIt.unregister<AppConfig>();
  }

  // Error Handling
  if (getIt.isRegistered<ErrorHandler>()) {
    getIt.unregister<ErrorHandler>();
  }

  // Storage Services
  if (getIt.isRegistered<SecureStorageService>()) {
    getIt.unregister<SecureStorageService>();
  }

  // External dependencies
  if (getIt.isRegistered<FlutterSecureStorage>()) {
    getIt.unregister<FlutterSecureStorage>();
  }
}

/// Reset core module (clear và re-register)
void resetCoreModule() {
  unregisterCoreDependencies();
  registerCoreDependencies();
}

/// Check if core dependencies are registered
bool areCoreDependenciesRegistered() {
  return getIt.isRegistered<SecureStorageService>() &&
         getIt.isRegistered<ErrorHandler>() &&
         getIt.isRegistered<AppConfig>();
}

/// Get core-related dependencies for debugging
Map<String, bool> getCoreDependenciesStatus() {
  return {
    'FlutterSecureStorage': getIt.isRegistered<FlutterSecureStorage>(),
    'SecureStorageService': getIt.isRegistered<SecureStorageService>(),
    'ErrorHandler': getIt.isRegistered<ErrorHandler>(),
    'AppConfig': getIt.isRegistered<AppConfig>(),
  };
}

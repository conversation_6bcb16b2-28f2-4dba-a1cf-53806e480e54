import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import '../../core/models/face_recognition_config.dart';
import 'online_recognition_service.dart';

/// Offline face recognition service using local embeddings database
class OfflineRecognitionService {
  static const String _modelAssetPath = 'packages/face_recognition/assets/models/mobilefacenet.tflite';
  static const String _databaseName = 'face_embeddings.db';
  static const int _embeddingSize = 512;
  
  final FaceRecognitionConfig _config;
  
  Interpreter? _interpreter;
  Database? _database;
  bool _isInitialized = false;
  
  // Pre-allocated buffers
  Float32List? _inputBuffer;
  Float32List? _outputBuffer;
  
  // Cached embeddings for faster lookup
  final Map<String, UserEmbedding> _embeddingCache = {};
  Timer? _cacheRefreshTimer;
  
  // Statistics
  int _totalRecognitions = 0;
  int _successfulRecognitions = 0;
  int _failedRecognitions = 0;
  
  OfflineRecognitionService({required FaceRecognitionConfig config}) : _config = config;
  
  /// Initialize the offline recognition service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🔧 Initializing OfflineRecognitionService');
      }
      
      // Initialize TFLite model
      await _initializeModel();
      
      // Initialize database
      await _initializeDatabase();
      
      // Load embeddings into cache
      await _loadEmbeddingsCache();
      
      // Start periodic cache refresh
      _cacheRefreshTimer = Timer.periodic(
        const Duration(minutes: 10),
        (_) => _refreshEmbeddingsCache(),
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ OfflineRecognitionService initialized');
        print('   Cached embeddings: ${_embeddingCache.length}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize OfflineRecognitionService: $e');
      }
      rethrow;
    }
  }
  
  /// Initialize MobileFaceNet model
  Future<void> _initializeModel() async {
    try {
      // Load TFLite model
      _interpreter = await Interpreter.fromAsset(_modelAssetPath);
      
      // Configure interpreter options
      final options = InterpreterOptions();
      if (_config.enableGPUAcceleration) {
        options.addDelegate(GpuDelegate());
      }
      options.threads = _config.processingThreads;
      
      // Allocate tensors
      _interpreter!.allocateTensors();
      
      // Pre-allocate buffers
      _inputBuffer = Float32List(112 * 112 * 3); // MobileFaceNet input: 112x112x3
      _outputBuffer = Float32List(_embeddingSize);
      
      if (kDebugMode) {
        print('✅ MobileFaceNet model loaded');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load MobileFaceNet model: $e');
      }
      rethrow;
    }
  }
  
  /// Initialize SQLite database for embeddings
  Future<void> _initializeDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);
      
      _database = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE user_embeddings (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              user_id TEXT NOT NULL,
              user_name TEXT NOT NULL,
              project_id TEXT NOT NULL,
              access_level TEXT NOT NULL,
              embedding BLOB NOT NULL,
              created_at INTEGER NOT NULL,
              updated_at INTEGER NOT NULL,
              is_active INTEGER NOT NULL DEFAULT 1,
              UNIQUE(user_id, project_id)
            )
          ''');
          
          await db.execute('''
            CREATE INDEX idx_user_embeddings_user_id ON user_embeddings(user_id)
          ''');
          
          await db.execute('''
            CREATE INDEX idx_user_embeddings_project_id ON user_embeddings(project_id)
          ''');
          
          await db.execute('''
            CREATE INDEX idx_user_embeddings_active ON user_embeddings(is_active)
          ''');
        },
      );
      
      if (kDebugMode) {
        print('✅ Embeddings database initialized');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize database: $e');
      }
      rethrow;
    }
  }
  
  /// Recognize face using offline embeddings
  Future<RecognitionResult?> recognizeFace(Uint8List faceImageBytes) async {
    if (!_isInitialized) {
      throw StateError('OfflineRecognitionService not initialized');
    }
    
    _totalRecognitions++;
    final stopwatch = Stopwatch()..start();
    
    try {
      // Extract embedding from face image
      final queryEmbedding = await _extractEmbedding(faceImageBytes);
      
      // Find best match in cached embeddings
      UserEmbedding? bestMatch;
      double bestSimilarity = 0.0;
      
      for (final userEmbedding in _embeddingCache.values) {
        if (!userEmbedding.isActive) continue;
        
        final similarity = _cosineSimilarity(queryEmbedding, userEmbedding.embedding);
        
        if (similarity > bestSimilarity && similarity > _config.minConfidence) {
          bestSimilarity = similarity;
          bestMatch = userEmbedding;
        }
      }
      
      stopwatch.stop();
      
      if (bestMatch != null) {
        _successfulRecognitions++;
        
        if (kDebugMode) {
          print('✅ Offline recognition: ${bestMatch.userName} (${(bestSimilarity * 100).toStringAsFixed(1)}%) in ${stopwatch.elapsedMilliseconds}ms');
        }
        
        return RecognitionResult(
          userId: bestMatch.userId,
          userName: bestMatch.userName,
          confidence: bestSimilarity,
          source: 'offline',
          accessLevel: bestMatch.accessLevel,
          hasAccess: bestMatch.accessLevel != AccessLevel.denied,
          timestamp: DateTime.now(),
          metadata: {
            'project_id': bestMatch.projectId,
            'processing_time_ms': stopwatch.elapsedMilliseconds,
            'embedding_cache_size': _embeddingCache.length,
          },
        );
      } else {
        _failedRecognitions++;
        
        if (kDebugMode) {
          print('❌ No offline match found (best similarity: ${(bestSimilarity * 100).toStringAsFixed(1)}%)');
        }
        
        return null;
      }
      
    } catch (e) {
      _failedRecognitions++;
      if (kDebugMode) {
        print('❌ Offline recognition failed: $e');
      }
      return null;
    }
  }
  
  /// Extract embedding from face image using MobileFaceNet
  Future<List<double>> _extractEmbedding(Uint8List faceImageBytes) async {
    try {
      // Preprocess image for MobileFaceNet (expects 112x112 normalized image)
      _preprocessFaceImage(faceImageBytes);
      
      // Run inference
      _interpreter!.run(_inputBuffer, _outputBuffer);
      
      // Normalize embedding
      final embedding = _normalizeEmbedding(_outputBuffer!.toList());
      
      return embedding;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to extract embedding: $e');
      }
      rethrow;
    }
  }
  
  /// Preprocess face image for MobileFaceNet
  void _preprocessFaceImage(Uint8List faceImageBytes) {
    // This is a simplified preprocessing - in practice, you'd use the image package
    // to decode, resize to 112x112, and normalize the image
    
    // For now, assume the face image is already 112x112 and just normalize
    if (faceImageBytes.length >= 112 * 112 * 3) {
      for (int i = 0; i < 112 * 112 * 3; i++) {
        _inputBuffer![i] = (faceImageBytes[i] - 127.5) / 127.5;
      }
    } else {
      // Fill with zeros if image is too small
      for (int i = 0; i < 112 * 112 * 3; i++) {
        _inputBuffer![i] = 0.0;
      }
    }
  }
  
  /// Normalize embedding vector
  List<double> _normalizeEmbedding(List<double> embedding) {
    // Calculate L2 norm
    double norm = 0.0;
    for (final value in embedding) {
      norm += value * value;
    }
    norm = math.sqrt(norm);
    
    // Normalize
    if (norm > 0) {
      return embedding.map((value) => value / norm).toList();
    } else {
      return embedding;
    }
  }
  
  /// Calculate cosine similarity between two embeddings
  double _cosineSimilarity(List<double> a, List<double> b) {
    if (a.length != b.length) return 0.0;
    
    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;
    
    for (int i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    normA = math.sqrt(normA);
    normB = math.sqrt(normB);
    
    if (normA == 0.0 || normB == 0.0) return 0.0;
    
    return dotProduct / (normA * normB);
  }
  
  /// Add user embedding to database
  Future<void> addUserEmbedding(UserEmbedding userEmbedding) async {
    if (!_isInitialized || _database == null) return;
    
    try {
      final embeddingBytes = Float64List.fromList(userEmbedding.embedding).buffer.asUint8List();
      
      await _database!.insert(
        'user_embeddings',
        {
          'user_id': userEmbedding.userId,
          'user_name': userEmbedding.userName,
          'project_id': userEmbedding.projectId,
          'access_level': userEmbedding.accessLevel.name,
          'embedding': embeddingBytes,
          'created_at': userEmbedding.createdAt.millisecondsSinceEpoch,
          'updated_at': userEmbedding.updatedAt.millisecondsSinceEpoch,
          'is_active': userEmbedding.isActive ? 1 : 0,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      // Update cache
      _embeddingCache[userEmbedding.userId] = userEmbedding;
      
      if (kDebugMode) {
        print('✅ Added user embedding: ${userEmbedding.userName}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to add user embedding: $e');
      }
      rethrow;
    }
  }
  
  /// Load embeddings into cache
  Future<void> _loadEmbeddingsCache() async {
    if (_database == null) return;
    
    try {
      final List<Map<String, dynamic>> maps = await _database!.query(
        'user_embeddings',
        where: 'is_active = ?',
        whereArgs: [1],
      );
      
      _embeddingCache.clear();
      
      for (final map in maps) {
        final embeddingBytes = map['embedding'] as Uint8List;
        final embedding = Float64List.view(embeddingBytes.buffer).toList();
        
        final userEmbedding = UserEmbedding(
          userId: map['user_id'] as String,
          userName: map['user_name'] as String,
          projectId: map['project_id'] as String,
          accessLevel: _parseAccessLevel(map['access_level'] as String),
          embedding: embedding,
          createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
          updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
          isActive: (map['is_active'] as int) == 1,
        );
        
        _embeddingCache[userEmbedding.userId] = userEmbedding;
      }
      
      if (kDebugMode) {
        print('📋 Loaded ${_embeddingCache.length} embeddings into cache');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load embeddings cache: $e');
      }
    }
  }
  
  /// Refresh embeddings cache
  Future<void> _refreshEmbeddingsCache() async {
    await _loadEmbeddingsCache();
  }
  
  /// Parse access level from string
  AccessLevel _parseAccessLevel(String accessLevel) {
    switch (accessLevel.toLowerCase()) {
      case 'admin':
        return AccessLevel.admin;
      case 'user':
        return AccessLevel.user;
      case 'guest':
        return AccessLevel.guest;
      case 'denied':
        return AccessLevel.denied;
      default:
        return AccessLevel.user;
    }
  }
  
  /// Get service statistics
  OfflineRecognitionStats getStats() {
    return OfflineRecognitionStats(
      totalRecognitions: _totalRecognitions,
      successfulRecognitions: _successfulRecognitions,
      failedRecognitions: _failedRecognitions,
      cachedEmbeddings: _embeddingCache.length,
      successRate: _totalRecognitions > 0 ? _successfulRecognitions / _totalRecognitions : 0.0,
    );
  }
  
  /// Dispose of resources
  Future<void> dispose() async {
    _cacheRefreshTimer?.cancel();
    _interpreter?.close();
    await _database?.close();
    
    _embeddingCache.clear();
    _interpreter = null;
    _database = null;
    _inputBuffer = null;
    _outputBuffer = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ OfflineRecognitionService disposed');
    }
  }
}

/// User embedding data class
class UserEmbedding {
  final String userId;
  final String userName;
  final String projectId;
  final AccessLevel accessLevel;
  final List<double> embedding;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  
  const UserEmbedding({
    required this.userId,
    required this.userName,
    required this.projectId,
    required this.accessLevel,
    required this.embedding,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
  });
}

/// Statistics for offline recognition service
class OfflineRecognitionStats {
  final int totalRecognitions;
  final int successfulRecognitions;
  final int failedRecognitions;
  final int cachedEmbeddings;
  final double successRate;
  
  const OfflineRecognitionStats({
    required this.totalRecognitions,
    required this.successfulRecognitions,
    required this.failedRecognitions,
    required this.cachedEmbeddings,
    required this.successRate,
  });
  
  @override
  String toString() {
    return 'OfflineRecognitionStats('
        'total: $totalRecognitions, '
        'success: $successfulRecognitions, '
        'failed: $failedRecognitions, '
        'cached: $cachedEmbeddings, '
        'success_rate: ${(successRate * 100).toStringAsFixed(1)}%'
        ')';
  }
}

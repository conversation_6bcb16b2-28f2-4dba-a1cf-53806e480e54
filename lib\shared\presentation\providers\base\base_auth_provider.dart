
import 'dart:async';
import 'package:flutter/foundation.dart';

import '../../../core/base/base_provider.dart';
import '../../../domain/entities/user/user.dart';
import '../../../domain/entities/auth/auth_result.dart';
import '../../../domain/use_cases/auth/login_use_case.dart';
import '../../../domain/use_cases/auth/logout_use_case.dart';
import '../../../domain/use_cases/auth/refresh_token_use_case.dart';
import '../../../core/errors/failures.dart';

/// Authentication status enum
enum AuthStatus { 
  initial, 
  loading, 
  authenticated, 
  unauthenticated, 
  error 
}

/// Base authentication provider that can be extended by mobile and terminal apps
/// 
/// Provides common authentication functionality including:
/// - Login/logout operations
/// - Authentication state management
/// - User session handling
/// - Token management
abstract class BaseAuthProvider extends BaseProvider {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final RefreshTokenUseCase _refreshTokenUseCase;

  BaseAuthProvider({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required RefreshTokenUseCase refreshTokenUseCase,
  }) : _loginUseCase = loginUseCase,
       _logoutUseCase = logoutUseCase,
       _refreshTokenUseCase = refreshTokenUseCase;

  // ============================================================================
  // STATE VARIABLES
  // ============================================================================

  AuthStatus _authStatus = AuthStatus.initial;
  User? _currentUser;
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiry;

  // Auto-refresh timer and settings
  Timer? _tokenRefreshTimer;
  bool _isRefreshing = false;
  static const Duration _refreshBeforeExpiry = Duration(minutes: 5);
  static const Duration _refreshRetryDelay = Duration(seconds: 30);

  // ============================================================================
  // GETTERS
  // ============================================================================
  
  /// Current authentication status
  AuthStatus get authStatus => _authStatus;
  
  /// Currently authenticated user
  User? get currentUser => _currentUser;
  
  /// Access token for API requests
  String? get accessToken => _accessToken;
  
  /// Refresh token for token renewal
  String? get refreshToken => _refreshToken;
  
  /// Token expiry date
  DateTime? get tokenExpiry => _tokenExpiry;
  
  /// Check if user is authenticated
  bool get isAuthenticated => _authStatus == AuthStatus.authenticated && _currentUser != null;
  
  /// Check if user is loading
  bool get isAuthLoading => _authStatus == AuthStatus.loading;
  
  /// Check if authentication failed
  bool get hasAuthError => _authStatus == AuthStatus.error;

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================
  
  /// Login with email and password
  Future<void> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _setAuthStatus(AuthStatus.loading);

    final result = await _loginUseCase(LoginParams(
      userName: email,
      password: password,
    ));

    result.fold(
      (failure) => _handleAuthError(failure),
      (authResult) => _handleLoginSuccess(authResult),
    );
  }
  
  /// Logout current user
  Future<void> logout() async {
    _setAuthStatus(AuthStatus.loading);

    final result = await _logoutUseCase();

    result.fold(
      (failure) => _handleAuthError(failure),
      (_) => _handleLogoutSuccess(),
    );
  }
  
  /// Check if token is expired
  bool isTokenExpired() {
    if (_tokenExpiry == null) return true;
    return DateTime.now().isAfter(_tokenExpiry!);
  }
  
  /// Refresh authentication token manually
  Future<bool> refreshAuthToken() async {
    if (_refreshToken == null || _isRefreshing) {
      return false;
    }

    _isRefreshing = true;

    try {
      final result = await _refreshTokenUseCase(_refreshToken!);

      return result.fold(
        (failure) {
          if (kDebugMode) {
            print('Manual token refresh failed: ${failure.message}');
          }
          // If refresh fails, logout user
          logout();
          return false;
        },
        (authResult) {
          _handleTokenRefreshSuccess(authResult);
          if (kDebugMode) {
            print('Token refreshed manually');
          }
          return true;
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Manual token refresh error: $e');
      }
      logout();
      return false;
    } finally {
      _isRefreshing = false;
    }
  }

  // ============================================================================
  // ABSTRACT METHODS (TO BE IMPLEMENTED BY SUBCLASSES)
  // ============================================================================
  
  /// Handle app-specific login success actions
  void onLoginSuccess(AuthResult authResult) {}
  
  /// Handle app-specific logout actions
  void onLogoutSuccess() {}
  
  /// Handle app-specific authentication errors
  void onAuthError(Failure failure) {}

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// Set authentication status
  void _setAuthStatus(AuthStatus status) {
    if (isDisposed) return;

    _authStatus = status;
    notifyListeners();
  }

  /// Method for subclasses to set auth status during initialization
  void setAuthStatus(AuthStatus status) {
    _setAuthStatus(status);
  }
  
  /// Handle successful login
  void _handleLoginSuccess(AuthResult authResult) {
    _currentUser = authResult.user;
    _accessToken = authResult.accessToken;
    _refreshToken = authResult.refreshToken;
    _tokenExpiry = authResult.expiresAt;
    _setAuthStatus(AuthStatus.authenticated);

    // Schedule automatic token refresh
    _scheduleTokenRefresh();
    
    // Call app-specific login success handler
    onLoginSuccess(authResult);
  }
  
  /// Handle successful logout
  void _handleLogoutSuccess() {
    // Cancel auto-refresh timer
    _cancelTokenRefreshTimer();

    _currentUser = null;
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiry = null;
    _isRefreshing = false;
    _setAuthStatus(AuthStatus.unauthenticated);
    
    // Call app-specific logout handler
    onLogoutSuccess();
  }
  
  /// Handle authentication error
  void _handleAuthError(Failure failure) {
    _setAuthStatus(AuthStatus.error);
    
    // Call app-specific error handler
    onAuthError(failure);
  }

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================
  
  /// Initialize authentication state
  Future<void> initialize() async {
    // Subclasses should implement initialization logic
    // e.g., check stored tokens, validate session, etc.
  }
  
  @override
  void dispose() {
    // Cancel auto-refresh timer
    _cancelTokenRefreshTimer();

    // Clear sensitive data
    _currentUser = null;
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiry = null;
    super.dispose();
  }
  
  @override
  Future<void> retry() async {
    // Retry last failed operation
    if (hasAuthError) {
      clearStates();
      _setAuthStatus(AuthStatus.initial);
    }
  }
  
  @override
  void reset() {
    super.reset();
    _cancelTokenRefreshTimer();
    _currentUser = null;
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiry = null;
    _authStatus = AuthStatus.initial;
    _isRefreshing = false;
    notifyListeners();
  }

  // ============================================================================
  // AUTO-REFRESH TOKEN FUNCTIONALITY
  // ============================================================================

  /// Schedule automatic token refresh before expiry
  void _scheduleTokenRefresh() {
    _cancelTokenRefreshTimer();

    if (_tokenExpiry == null || _refreshToken == null) return;

    final now = DateTime.now();
    final refreshTime = _tokenExpiry!.subtract(_refreshBeforeExpiry);

    // If token expires soon, refresh immediately
    if (refreshTime.isBefore(now)) {
      _performAutoRefresh();
      return;
    }

    final delay = refreshTime.difference(now);
    _tokenRefreshTimer = Timer(delay, _performAutoRefresh);

    if (kDebugMode) {
      print('Token refresh scheduled in ${delay.inMinutes} minutes');
    }
  }

  /// Cancel the token refresh timer
  void _cancelTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = null;
  }

  /// Perform automatic token refresh
  void _performAutoRefresh() async {
    if (_isRefreshing || _refreshToken == null) return;

    _isRefreshing = true;

    try {
      final result = await _refreshTokenUseCase(_refreshToken!);

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Auto token refresh failed: ${failure.message}');
          }
          // Schedule retry after delay
          _tokenRefreshTimer = Timer(_refreshRetryDelay, _performAutoRefresh);
        },
        (authResult) {
          _handleTokenRefreshSuccess(authResult);
          if (kDebugMode) {
            print('Token auto-refreshed successfully');
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Auto token refresh error: $e');
      }
      // Schedule retry after delay
      _tokenRefreshTimer = Timer(_refreshRetryDelay, _performAutoRefresh);
    } finally {
      _isRefreshing = false;
    }
  }

  /// Handle successful token refresh
  void _handleTokenRefreshSuccess(AuthResult authResult) {
    _accessToken = authResult.accessToken;
    _refreshToken = authResult.refreshToken;
    _tokenExpiry = authResult.expiresAt;
    _currentUser = authResult.user;

    // Schedule next refresh
    _scheduleTokenRefresh();

    notifyListeners();
  }
}



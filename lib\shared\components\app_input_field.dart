import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

/// Input field component tái sử dụng cho toàn bộ ứng dụng
class AppInputField extends StatefulWidget {
  final String? label;
  final String? placeholder;
  final String? initialValue;
  final double height;
  final bool isRequired;
  final bool isPassword;
  final bool isEnabled;
  final bool isReadOnly;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final int? maxLines;
  final int? maxLength;
  final String? errorText;
  final String? helperText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onEditingComplete;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final FormFieldValidator<String>? validator;

  const AppInputField({
    super.key,
    this.label,
    this.placeholder,
    this.initialValue,
    this.height = 38,
    this.isRequired = false,
    this.isPassword = false,
    this.isEnabled = true,
    this.isReadOnly = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.maxLines = 1,
    this.maxLength,
    this.errorText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.controller,
    this.focusNode,
    this.inputFormatters,
    this.validator,
  });

  @override
  State<AppInputField> createState() => _AppInputFieldState();
}

class _AppInputFieldState extends State<AppInputField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _obscureText = true;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) _buildLabel(),
        SizedBox(height: AppDimensions.spacing4),
        _buildInputField(),
        if (widget.errorText != null || widget.helperText != null)
          SizedBox(height: AppDimensions.spacing4),
        if (widget.errorText != null) _buildErrorText(),
        if (widget.helperText != null && widget.errorText == null) _buildHelperText(),
      ],
    );
  }

  Widget _buildLabel() {
    return Row(
      children: [
        Text(
          widget.label!,
          style: AppTextStyles.inputLabel,
        ),
        if (widget.isRequired)
          Text(
            ' *',
            style: AppTextStyles.inputLabel.copyWith(color: AppColors.error),
          ),
      ],
    );
  }

  Widget _buildInputField() {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.isEnabled ? AppColors.surface : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _getBorderColor(),
          width: AppDimensions.borderNormal,
        ),
      ),
      child: TextFormField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.isEnabled,
        readOnly: widget.isReadOnly,
        obscureText: widget.isPassword ? _obscureText : false,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        inputFormatters: widget.inputFormatters,
        validator: widget.validator,
        onTap: widget.onTap,
        onChanged: widget.onChanged,
        onFieldSubmitted: widget.onSubmitted,
        onEditingComplete: widget.onEditingComplete,
        autocorrect: false,
        enableSuggestions: false,
        style: AppTextStyles.inputText,
        decoration: InputDecoration(
          hintText: widget.placeholder,
          hintStyle: AppTextStyles.inputPlaceholder,
          prefixIcon: widget.prefixIcon,
          suffixIcon: _buildSuffixIcon(),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS + 2,
            vertical: AppDimensions.paddingS + 4.5,
          ),
          counterText: '',
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
        ),
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.isPassword) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility : Icons.visibility_off,
          color: AppColors.eyeIcon.withValues(alpha: 0.8),
          size: AppDimensions.iconS,
        ),
        onPressed: _togglePasswordVisibility,
        padding: EdgeInsets.all(AppDimensions.paddingS),
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      );
    }
    return widget.suffixIcon;
  }

  Widget _buildErrorText() {
    return Padding(
      padding: EdgeInsets.only(left: AppDimensions.paddingS),
      child: Text(
        widget.errorText!,
        style: AppTextStyles.error,
      ),
    );
  }

  Widget _buildHelperText() {
    return Padding(
      padding: EdgeInsets.only(left: AppDimensions.paddingS),
      child: Text(
        widget.helperText!,
        style: AppTextStyles.caption,
      ),
    );
  }

  Color _getBorderColor() {
    if (widget.errorText != null) {
      return AppColors.error;
    }
    if (_isFocused) {
      return AppColors.borderFocused;
    }
    return AppColors.border;
  }
}

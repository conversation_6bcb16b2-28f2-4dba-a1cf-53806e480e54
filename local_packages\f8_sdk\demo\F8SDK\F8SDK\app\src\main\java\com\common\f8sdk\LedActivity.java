package com.common.f8sdk;

import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.common.pos.api.util.PosUtil;

public class LedActivity extends AppCompatActivity {

    private SeekBar seekBar;
    TextView title_tv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_led);

        initUI();
    }

    private void initUI() {
        title_tv = findViewById(R.id.title_tv);
        title_tv.setText("LED Test");
        seekBar=findViewById(R.id.seekbar);
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // 当拖动条的滑块位置发生改变时触发该方法,在这里直接使用参数progress，即当前滑块代表的进度值
                PosUtil.setLedLight(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // Log.e("------------", "开始滑动！");
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // Log.e("------------", "停止滑动！");
            }
        });
    }

    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.led_open:
                PosUtil.setLedLight(10);
                Toast.makeText(LedActivity.this, "open led success", Toast.LENGTH_SHORT).show();
                break;
            case R.id.led_close:
                PosUtil.setLedLight(0);
                Toast.makeText(LedActivity.this, "close led success", Toast.LENGTH_SHORT).show();
                break;
            default:
                break;
        }
    }

}

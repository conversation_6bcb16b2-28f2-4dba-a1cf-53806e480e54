import 'package:flutter/foundation.dart';
import 'face_recognition_v3_service.dart';

/// Simple test utility for health check functionality
class HealthCheckTest {
  
  /// Test health check with different server URLs
  static Future<void> testHealthCheck() async {
    if (!kDebugMode) return;
    
    print('🧪 Testing health check functionality...');
    
    // Test current server
    await _testServerHealth('http://************');
    
    // Test invalid server (should fail)
    await _testServerHealth('https://invalid-server-url.com');
    
    // Test localhost (should fail unless running locally)
    await _testServerHealth('http://localhost:8080');
    
    print('🧪 Health check tests completed');
  }
  
  /// Test health check for specific server
  static Future<void> _testServerHealth(String serverUrl) async {
    print('\n🔍 Testing server: $serverUrl');
    
    try {
      // Initialize service with test server
      FaceRecognitionV3Service.instance.initialize(baseUrl: serverUrl);
      
      // Test health check
      final startTime = DateTime.now();
      final isHealthy = await FaceRecognitionV3Service.instance.testConnection();
      final duration = DateTime.now().difference(startTime);
      
      print('   Result: ${isHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}');
      print('   Duration: ${duration.inMilliseconds}ms');
      
      // Get service status
      final status = FaceRecognitionV3Service.instance.getServiceStatus();
      print('   Base URL: ${status['baseUrl']}');
      print('   Configured: ${status['isConfigured']}');
      
    } catch (e) {
      print('   Error: $e');
    }
  }
  
  /// Test health check with timeout scenarios
  static Future<void> testHealthCheckTimeouts() async {
    if (!kDebugMode) return;
    
    print('\n🕐 Testing health check timeouts...');
    
    // Test with very slow server (should timeout)
    await _testServerHealthWithTimeout('https://httpstat.us/200?sleep=15000', 5);
    
    // Test with normal server
    await _testServerHealthWithTimeout('http://************', 10);
  }
  
  /// Test health check with custom timeout
  static Future<void> _testServerHealthWithTimeout(String serverUrl, int timeoutSeconds) async {
    print('\n⏱️ Testing server with ${timeoutSeconds}s timeout: $serverUrl');
    
    try {
      FaceRecognitionV3Service.instance.initialize(baseUrl: serverUrl);
      
      final startTime = DateTime.now();
      final isHealthy = await FaceRecognitionV3Service.instance.testConnection();
      final duration = DateTime.now().difference(startTime);
      
      print('   Result: ${isHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}');
      print('   Duration: ${duration.inMilliseconds}ms');
      
      if (duration.inSeconds >= timeoutSeconds) {
        print('   ⚠️ Request took longer than expected timeout');
      }
      
    } catch (e) {
      print('   Error: $e');
    }
  }
  
  /// Continuous health monitoring test
  static Future<void> startHealthMonitoring({
    int intervalSeconds = 30,
    int maxChecks = 10,
  }) async {
    if (!kDebugMode) return;
    
    print('\n📊 Starting health monitoring...');
    print('   Interval: ${intervalSeconds}s');
    print('   Max checks: $maxChecks');
    
    int checkCount = 0;
    int healthyCount = 0;
    int unhealthyCount = 0;
    
    while (checkCount < maxChecks) {
      checkCount++;
      
      print('\n📊 Health check #$checkCount/$maxChecks');
      
      final startTime = DateTime.now();
      final isHealthy = await FaceRecognitionV3Service.instance.testConnection();
      final duration = DateTime.now().difference(startTime);
      
      if (isHealthy) {
        healthyCount++;
        print('   ✅ HEALTHY (${duration.inMilliseconds}ms)');
      } else {
        unhealthyCount++;
        print('   ❌ UNHEALTHY (${duration.inMilliseconds}ms)');
      }
      
      // Wait for next check
      if (checkCount < maxChecks) {
        await Future.delayed(Duration(seconds: intervalSeconds));
      }
    }
    
    // Summary
    print('\n📊 Health monitoring summary:');
    print('   Total checks: $checkCount');
    print('   Healthy: $healthyCount (${(healthyCount / checkCount * 100).toStringAsFixed(1)}%)');
    print('   Unhealthy: $unhealthyCount (${(unhealthyCount / checkCount * 100).toStringAsFixed(1)}%)');
    print('   Uptime: ${healthyCount / checkCount >= 0.9 ? '✅ Good' : '⚠️ Poor'}');
  }
}

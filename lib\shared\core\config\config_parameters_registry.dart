/// Configuration Parameters Registry
/// 
/// This file contains all configuration parameter definitions with their
/// validation rules, default values, and metadata.

import 'package:flutter/material.dart';
import 'flexible_config_system.dart';
import 'relay_config_parameters.dart';

/// Registry of all configuration parameters
class ConfigParametersRegistry {
  static final Map<String, ConfigParameter> _parameters = {};

  /// Smart Duration parser that accepts various formats
  static Duration _parseDuration(dynamic value) {
    final str = value.toString().trim();
    if (str.isEmpty) throw FormatException('Vui lòng nhập giá trị thời gian');

    // Try to parse as seconds (user-friendly input)
    try {
      final seconds = double.parse(str);
      if (seconds < 0) {
        throw FormatException('Thời gian phải là số dương');
      }
      return Duration(milliseconds: (seconds * 1000).round());
    } catch (e) {
      // Check if it's a parsing error or validation error
      if (e is FormatException && e.message.contains('Thời gian phải là số dương')) {
        rethrow;
      }
      throw FormatException('Định dạng thời gian không hợp lệ. Vui lòng nhập số giây (ví dụ: 5 hoặc 8.5)');
    }
  }

  /// Initialize all configuration parameters
  static void initialize() {
    _registerFaceDetectionParameters();
    _registerNetworkParameters();
    _registerUIParameters();
    _registerPerformanceParameters();
    _registerCameraParameters();
    _registerSecurityParameters();
    _registerCacheParameters();
    _registerKioskParameters();
    _registerDebugParameters();
    _registerRelayParameters();
  }
  
  /// Get parameter by key
  static ConfigParameter? getParameter(String key) => _parameters[key];
  
  /// Get all parameters
  static Map<String, ConfigParameter> getAllParameters() => Map.unmodifiable(_parameters);
  
  /// Get parameters by category
  static Map<String, ConfigParameter> getParametersByCategory(String category) {
    return Map.fromEntries(
      _parameters.entries.where((entry) => entry.value.category == category)
    );
  }
  
  /// Register a parameter
  static void _register<T>(ConfigParameter<T> parameter) {
    _parameters[parameter.key] = parameter;
  }

  /// Face Detection Parameters
  static void _registerFaceDetectionParameters() {
    _register(ConfigParameter<double>(
      key: ConfigKeys.minFaceQualityForDetection,
      category: ConfigCategories.faceDetection,
      description: 'Minimum face quality threshold for detection (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.4,
      minValue: 0.0,
      maxValue: 1.0,
      isRequired: true,
      environmentKey: 'FACE_MIN_QUALITY_DETECTION',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.minFaceQualityForRecognition,
      category: ConfigCategories.faceDetection,
      description: 'Minimum face quality threshold for recognition (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.4,
      minValue: 0.0,
      maxValue: 1.0,
      isRequired: true,
      environmentKey: 'FACE_MIN_QUALITY_RECOGNITION',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.recognitionThrottleDuration,
      category: ConfigCategories.faceDetection,
      description: 'Throttle duration between recognition requests',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 8),
      minValue: Duration(seconds: 1),
      maxValue: Duration(seconds: 60),
      environmentKey: 'FACE_RECOGNITION_THROTTLE_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.frameSkipCount,
      category: ConfigCategories.faceDetection,
      description: 'Number of frames to skip between face detection processing',
      type: ConfigValueType.integer,
      defaultValue: 1,
      minValue: 1,
      maxValue: 10,
      environmentKey: 'FACE_FRAME_SKIP_COUNT',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.detectionTimeout,
      category: ConfigCategories.faceDetection,
      description: 'Timeout for face detection processing per frame',
      type: ConfigValueType.duration,
      defaultValue: Duration(milliseconds: 80),
      minValue: Duration(milliseconds: 50),
      maxValue: Duration(milliseconds: 500),
      environmentKey: 'FACE_DETECTION_TIMEOUT_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.minFaceQualityForAvatarCapture,
      category: ConfigCategories.faceDetection,
      description: 'Minimum face quality threshold for avatar capture (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.4,
      minValue: 0.0,
      maxValue: 1.0,
      environmentKey: 'FACE_MIN_QUALITY_AVATAR_CAPTURE',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.significantQualityChange,
      category: ConfigCategories.faceDetection,
      description: 'Threshold for significant quality change detection',
      type: ConfigValueType.double,
      defaultValue: 0.1,
      minValue: 0.01,
      maxValue: 0.5,
      environmentKey: 'FACE_SIGNIFICANT_QUALITY_CHANGE',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.userDisplayTimeout,
      category: ConfigCategories.faceDetection,
      description: 'Timeout for displaying user information',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 3),
      minValue: Duration(seconds: 1),
      maxValue: Duration(seconds: 30),
      environmentKey: 'FACE_USER_DISPLAY_TIMEOUT_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.avatarCaptureThrottle,
      category: ConfigCategories.faceDetection,
      description: 'Throttle duration between avatar captures',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 2),
      minValue: Duration(milliseconds: 500),
      maxValue: Duration(seconds: 10),
      environmentKey: 'FACE_AVATAR_CAPTURE_THROTTLE_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.avatarDisplayDuration,
      category: ConfigCategories.faceDetection,
      description: 'Duration to display captured avatar',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 10),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 5),
      environmentKey: 'FACE_AVATAR_DISPLAY_DURATION_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.avatarPaddingFactor,
      category: ConfigCategories.faceDetection,
      description: 'Padding factor around face for avatar capture (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.3,
      minValue: 0.0,
      maxValue: 1.0,
      environmentKey: 'FACE_AVATAR_PADDING_FACTOR',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.avatarTargetSize,
      category: ConfigCategories.faceDetection,
      description: 'Target size for avatar images in pixels',
      type: ConfigValueType.integer,
      defaultValue: 200,
      minValue: 50,
      maxValue: 1000,
      environmentKey: 'FACE_AVATAR_TARGET_SIZE',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.avatarImageQuality,
      category: ConfigCategories.faceDetection,
      description: 'JPEG quality for avatar images (1-100)',
      type: ConfigValueType.integer,
      defaultValue: 85,
      minValue: 1,
      maxValue: 100,
      environmentKey: 'FACE_AVATAR_IMAGE_QUALITY',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.recognitionImageQuality,
      category: ConfigCategories.faceDetection,
      description: 'JPEG quality for recognition images (1-100)',
      type: ConfigValueType.integer,
      defaultValue: 85,
      minValue: 1,
      maxValue: 100,
      environmentKey: 'FACE_RECOGNITION_IMAGE_QUALITY',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxRecognitionRetries,
      category: ConfigCategories.faceDetection,
      description: 'Maximum number of recognition retry attempts',
      type: ConfigValueType.integer,
      defaultValue: 3,
      minValue: 1,
      maxValue: 10,
      environmentKey: 'FACE_MAX_RECOGNITION_RETRIES',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.recognitionTimeout,
      category: ConfigCategories.faceDetection,
      description: 'Timeout for face recognition requests',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 10),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 2),
      environmentKey: 'FACE_RECOGNITION_TIMEOUT_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxImageSizeBytes,
      category: ConfigCategories.faceDetection,
      description: 'Maximum image size in bytes for processing',
      type: ConfigValueType.integer,
      defaultValue: 5 * 1024 * 1024, // 5MB
      minValue: 1024 * 1024, // 1MB
      maxValue: 50 * 1024 * 1024, // 50MB
      environmentKey: 'FACE_MAX_IMAGE_SIZE_BYTES',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.compressionQuality,
      category: ConfigCategories.faceDetection,
      description: 'Image compression quality (1-100)',
      type: ConfigValueType.integer,
      defaultValue: 85,
      minValue: 1,
      maxValue: 100,
      environmentKey: 'FACE_COMPRESSION_QUALITY',
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.imageFormat,
      category: ConfigCategories.faceDetection,
      description: 'Image format for face processing',
      type: ConfigValueType.string,
      defaultValue: 'jpeg',
      allowedValues: ['jpeg', 'png', 'webp'],
      environmentKey: 'FACE_IMAGE_FORMAT',
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.sourceIdentifier,
      category: ConfigCategories.faceDetection,
      description: 'Source identifier for face detection stream',
      type: ConfigValueType.string,
      defaultValue: 'terminal_camera_stream',
      environmentKey: 'FACE_SOURCE_IDENTIFIER',
    ));
  }

  /// Network Parameters
  static void _registerNetworkParameters() {
    _register(ConfigParameter<String>(
      key: ConfigKeys.baseApiUrl,
      category: ConfigCategories.network,
      description: 'Base API URL for server communication',
      type: ConfigValueType.string,
      defaultValue: 'http://************:1081/api/v3.1',
      isRequired: true,
      environmentKey: 'API_BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.requestTimeout,
      category: ConfigCategories.network,
      description: 'HTTP request timeout duration',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 5),
      environmentKey: 'API_REQUEST_TIMEOUT_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxRetryAttempts,
      category: ConfigCategories.network,
      description: 'Maximum number of retry attempts for failed requests',
      type: ConfigValueType.integer,
      defaultValue: 3,
      minValue: 0,
      maxValue: 10,
      environmentKey: 'API_MAX_RETRIES',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.retryDelay,
      category: ConfigCategories.network,
      description: 'Delay between retry attempts',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 2),
      minValue: Duration(milliseconds: 500),
      maxValue: Duration(seconds: 30),
      environmentKey: 'API_RETRY_DELAY_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.deviceId,
      category: ConfigCategories.network,
      description: 'Unique device identifier',
      type: ConfigValueType.string,
      defaultValue: 'terminal_001',
      isRequired: true,
      environmentKey: 'DEVICE_ID',
      validator: (value) => value.isNotEmpty && value.length <= 50,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.baseUrl,
      category: ConfigCategories.network,
      description: 'Base server URL without API version',
      type: ConfigValueType.string,
      defaultValue: 'http://*************:5000',
      isRequired: true,
      environmentKey: 'BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.apiVersion,
      category: ConfigCategories.network,
      description: 'API version path',
      type: ConfigValueType.string,
      defaultValue: '/api',
      environmentKey: 'API_VERSION',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.connectTimeout,
      category: ConfigCategories.network,
      description: 'Connection timeout in milliseconds',
      type: ConfigValueType.integer,
      defaultValue: 30000,
      minValue: 5000,
      maxValue: 300000,
      environmentKey: 'CONNECT_TIMEOUT_MS',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.receiveTimeout,
      category: ConfigCategories.network,
      description: 'Receive timeout in milliseconds',
      type: ConfigValueType.integer,
      defaultValue: 30000,
      minValue: 5000,
      maxValue: 300000,
      environmentKey: 'RECEIVE_TIMEOUT_MS',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.sendTimeout,
      category: ConfigCategories.network,
      description: 'Send timeout in milliseconds',
      type: ConfigValueType.integer,
      defaultValue: 30000,
      minValue: 5000,
      maxValue: 300000,
      environmentKey: 'SEND_TIMEOUT_MS',
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.devBaseUrl,
      category: ConfigCategories.network,
      description: 'Development environment base URL',
      type: ConfigValueType.string,
      defaultValue: 'http://************:1081',
      environmentKey: 'DEV_BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.devBaseUrlFallback,
      category: ConfigCategories.network,
      description: 'Development environment fallback base URL',
      type: ConfigValueType.string,
      defaultValue: 'http://************:1081/',
      environmentKey: 'DEV_BASE_URL_FALLBACK',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.stagingBaseUrl,
      category: ConfigCategories.network,
      description: 'Staging environment base URL',
      type: ConfigValueType.string,
      defaultValue: 'https://staging-api.face-terminal.com',
      environmentKey: 'STAGING_BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.prodBaseUrl,
      category: ConfigCategories.network,
      description: 'Production environment base URL',
      type: ConfigValueType.string,
      defaultValue: 'https://api.face-terminal.com',
      environmentKey: 'PROD_BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    _register(ConfigParameter<String>(
      key: ConfigKeys.currentBaseUrl,
      category: ConfigCategories.network,
      description: 'Current active base URL',
      type: ConfigValueType.string,
      defaultValue: 'http://************:1081',
      environmentKey: 'CURRENT_BASE_URL',
      validator: (value) => Uri.tryParse(value) != null,
    ));

    // Server communication parameters
    _register(ConfigParameter<Duration>(
      key: ConfigKeys.heartbeatInterval,
      category: ConfigCategories.network,
      description: 'Heartbeat interval for server communication',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 10),
      maxValue: Duration(minutes: 5),
      environmentKey: 'HEARTBEAT_INTERVAL_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.pingInterval,
      category: ConfigCategories.network,
      description: 'Ping interval for server health check',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 10),
      maxValue: Duration(minutes: 2),
      environmentKey: 'PING_INTERVAL_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.serverHealthCheckInterval,
      category: ConfigCategories.network,
      description: 'Server health check interval',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 15),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 1),
      environmentKey: 'SERVER_HEALTH_CHECK_INTERVAL_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<bool>(
      key: ConfigKeys.autoReconnect,
      category: ConfigCategories.network,
      description: 'Automatically reconnect to server on connection loss',
      type: ConfigValueType.boolean,
      defaultValue: true,
      environmentKey: 'AUTO_RECONNECT',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxReconnectAttempts,
      category: ConfigCategories.network,
      description: 'Maximum reconnection attempts',
      type: ConfigValueType.integer,
      defaultValue: 5,
      minValue: 1,
      maxValue: 20,
      environmentKey: 'MAX_RECONNECT_ATTEMPTS',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.reconnectDelay,
      category: ConfigCategories.network,
      description: 'Delay between reconnection attempts',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 5),
      minValue: Duration(seconds: 1),
      maxValue: Duration(seconds: 30),
      environmentKey: 'RECONNECT_DELAY_MS',
      parser: _parseDuration,
    ));
  }

  /// UI Parameters
  static void _registerUIParameters() {
    _register(ConfigParameter<int>(
      key: ConfigKeys.primaryColor,
      category: ConfigCategories.ui,
      description: 'Primary color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF2196F3,
      environmentKey: 'UI_PRIMARY_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.successColor,
      category: ConfigCategories.ui,
      description: 'Success color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF4CAF50,
      environmentKey: 'UI_SUCCESS_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.errorColor,
      category: ConfigCategories.ui,
      description: 'Error color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFF44336,
      environmentKey: 'UI_ERROR_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.avatarSize,
      category: ConfigCategories.ui,
      description: 'Avatar display size in pixels',
      type: ConfigValueType.double,
      defaultValue: 120.0,
      minValue: 50.0,
      maxValue: 300.0,
      environmentKey: 'UI_AVATAR_SIZE',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.animationDuration,
      category: ConfigCategories.ui,
      description: 'Default animation duration',
      type: ConfigValueType.duration,
      defaultValue: Duration(milliseconds: 300),
      minValue: Duration(milliseconds: 100),
      maxValue: Duration(seconds: 2),
      environmentKey: 'UI_ANIMATION_DURATION_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.warningColor,
      category: ConfigCategories.ui,
      description: 'Warning color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFFF9800,
      environmentKey: 'UI_WARNING_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.backgroundColor,
      category: ConfigCategories.ui,
      description: 'Background color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF000000,
      environmentKey: 'UI_BACKGROUND_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.avatarBorderRadius,
      category: ConfigCategories.ui,
      description: 'Border radius for avatar display',
      type: ConfigValueType.double,
      defaultValue: 8.0,
      minValue: 0.0,
      maxValue: 50.0,
      environmentKey: 'UI_AVATAR_BORDER_RADIUS',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.qualityBadgeSize,
      category: ConfigCategories.ui,
      description: 'Size of quality badge indicators',
      type: ConfigValueType.double,
      defaultValue: 24.0,
      minValue: 10.0,
      maxValue: 100.0,
      environmentKey: 'UI_QUALITY_BADGE_SIZE',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.guideFrameStrokeWidth,
      category: ConfigCategories.ui,
      description: 'Stroke width for face guide frame',
      type: ConfigValueType.double,
      defaultValue: 2.0,
      minValue: 1.0,
      maxValue: 10.0,
      environmentKey: 'UI_GUIDE_FRAME_STROKE_WIDTH',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.guideFrameCornerRadius,
      category: ConfigCategories.ui,
      description: 'Corner radius for face guide frame',
      type: ConfigValueType.double,
      defaultValue: 12.0,
      minValue: 0.0,
      maxValue: 50.0,
      environmentKey: 'UI_GUIDE_FRAME_CORNER_RADIUS',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.guideFrameMargin,
      category: ConfigCategories.ui,
      description: 'Margin around face guide frame',
      type: ConfigValueType.double,
      defaultValue: 50.0,
      minValue: 10.0,
      maxValue: 200.0,
      environmentKey: 'UI_GUIDE_FRAME_MARGIN',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.progressIndicatorStrokeWidth,
      category: ConfigCategories.ui,
      description: 'Stroke width for progress indicators',
      type: ConfigValueType.double,
      defaultValue: 3.0,
      minValue: 1.0,
      maxValue: 10.0,
      environmentKey: 'UI_PROGRESS_INDICATOR_STROKE_WIDTH',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.progressAnimationDuration,
      category: ConfigCategories.ui,
      description: 'Animation duration for progress indicators',
      type: ConfigValueType.duration,
      defaultValue: Duration(milliseconds: 300),
      minValue: Duration(milliseconds: 100),
      maxValue: Duration(seconds: 2),
      environmentKey: 'UI_PROGRESS_ANIMATION_DURATION_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.qualityIndicatorSize,
      category: ConfigCategories.ui,
      description: 'Size of quality indicator elements',
      type: ConfigValueType.double,
      defaultValue: 24.0,
      minValue: 10.0,
      maxValue: 100.0,
      environmentKey: 'UI_QUALITY_INDICATOR_SIZE',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.qualityIndicatorFontSize,
      category: ConfigCategories.ui,
      description: 'Font size for quality indicator text',
      type: ConfigValueType.double,
      defaultValue: 8.0,
      minValue: 6.0,
      maxValue: 20.0,
      environmentKey: 'UI_QUALITY_INDICATOR_FONT_SIZE',
    ));

    // Extended UI Color Parameters
    _register(ConfigParameter<int>(
      key: ConfigKeys.primaryLightColor,
      category: ConfigCategories.ui,
      description: 'Primary light color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFABDBF2,
      environmentKey: 'UI_PRIMARY_LIGHT_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.primaryBackgroundColor,
      category: ConfigCategories.ui,
      description: 'Primary background color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFE5F3FA,
      environmentKey: 'UI_PRIMARY_BACKGROUND_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.surfaceColor,
      category: ConfigCategories.ui,
      description: 'Surface color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFFFFFFF,
      environmentKey: 'UI_SURFACE_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.surfaceVariantColor,
      category: ConfigCategories.ui,
      description: 'Surface variant color for UI elements (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFF5F5F5,
      environmentKey: 'UI_SURFACE_VARIANT_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.textPrimaryColor,
      category: ConfigCategories.ui,
      description: 'Primary text color (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF1F2329,
      environmentKey: 'UI_TEXT_PRIMARY_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.textSecondaryColor,
      category: ConfigCategories.ui,
      description: 'Secondary text color (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF85888C,
      environmentKey: 'UI_TEXT_SECONDARY_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.textTertiaryColor,
      category: ConfigCategories.ui,
      description: 'Tertiary text color (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF8F959E,
      environmentKey: 'UI_TEXT_TERTIARY_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.textPlaceholderColor,
      category: ConfigCategories.ui,
      description: 'Placeholder text color (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFF8F959E,
      environmentKey: 'UI_TEXT_PLACEHOLDER_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.textOnPrimaryColor,
      category: ConfigCategories.ui,
      description: 'Text color on primary background (hex value)',
      type: ConfigValueType.color,
      defaultValue: 0xFFFFFFFF,
      environmentKey: 'UI_TEXT_ON_PRIMARY_COLOR',
      parser: (value) {
        if (value is int) return value;
        String hex = value.toString().replaceFirst('#', '');
        return int.parse(hex, radix: 16);
      },
    ));
  }

  /// Performance Parameters
  static void _registerPerformanceParameters() {
    _register(ConfigParameter<int>(
      key: ConfigKeys.normalFrameRate,
      category: ConfigCategories.performance,
      description: 'Normal operation frame rate (FPS)',
      type: ConfigValueType.integer,
      defaultValue: 30,
      minValue: 10,
      maxValue: 60,
      environmentKey: 'PERF_NORMAL_FPS',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.optimizedFrameRate,
      category: ConfigCategories.performance,
      description: 'Optimized operation frame rate (FPS)',
      type: ConfigValueType.integer,
      defaultValue: 15,
      minValue: 5,
      maxValue: 30,
      environmentKey: 'PERF_OPTIMIZED_FPS',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.powerSavingDelay,
      category: ConfigCategories.performance,
      description: 'Delay before entering power saving mode',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 10),
      maxValue: Duration(minutes: 10),
      environmentKey: 'PERF_POWER_SAVING_DELAY_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.extremePowerSavingFrameRate,
      category: ConfigCategories.performance,
      description: 'Frame rate for extreme power saving mode (FPS)',
      type: ConfigValueType.integer,
      defaultValue: 5,
      minValue: 1,
      maxValue: 15,
      environmentKey: 'PERF_EXTREME_POWER_SAVING_FPS',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.resourceOptimizationDelay,
      category: ConfigCategories.performance,
      description: 'Delay before resource optimization kicks in',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 10),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 5),
      environmentKey: 'PERF_RESOURCE_OPTIMIZATION_DELAY_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.extremePowerSavingDelay,
      category: ConfigCategories.performance,
      description: 'Delay before extreme power saving mode',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 15),
      maxValue: Duration(minutes: 10),
      environmentKey: 'PERF_EXTREME_POWER_SAVING_DELAY_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.faceAbsenceForOptimization,
      category: ConfigCategories.performance,
      description: 'Face absence duration before optimization',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 10),
      minValue: Duration(seconds: 5),
      maxValue: Duration(minutes: 5),
      environmentKey: 'PERF_FACE_ABSENCE_OPTIMIZATION_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.faceAbsenceForExtremeSaving,
      category: ConfigCategories.performance,
      description: 'Face absence duration before extreme power saving',
      type: ConfigValueType.duration,
      defaultValue: Duration(seconds: 30),
      minValue: Duration(seconds: 15),
      maxValue: Duration(minutes: 10),
      environmentKey: 'PERF_FACE_ABSENCE_EXTREME_SAVING_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.maxBrightness,
      category: ConfigCategories.performance,
      description: 'Maximum screen brightness level (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 1.0,
      minValue: 0.1,
      maxValue: 1.0,
      environmentKey: 'PERF_MAX_BRIGHTNESS',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.minBrightness,
      category: ConfigCategories.performance,
      description: 'Minimum screen brightness level (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.0,
      minValue: 0.0,
      maxValue: 0.5,
      environmentKey: 'PERF_MIN_BRIGHTNESS',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.powerSavingBrightness,
      category: ConfigCategories.performance,
      description: 'Screen brightness for power saving mode (0.0-1.0)',
      type: ConfigValueType.double,
      defaultValue: 0.1,
      minValue: 0.0,
      maxValue: 0.5,
      environmentKey: 'PERF_POWER_SAVING_BRIGHTNESS',
    ));

    _register(ConfigParameter<Duration>(
      key: ConfigKeys.brightnessTransitionDuration,
      category: ConfigCategories.performance,
      description: 'Duration for brightness transition animations',
      type: ConfigValueType.duration,
      defaultValue: Duration(milliseconds: 500),
      minValue: Duration(milliseconds: 100),
      maxValue: Duration(seconds: 5),
      environmentKey: 'PERF_BRIGHTNESS_TRANSITION_DURATION_MS',
      parser: _parseDuration,
    ));
  }

  /// Camera Parameters
  static void _registerCameraParameters() {
    _register(ConfigParameter<String>(
      key: ConfigKeys.defaultCameraResolution,
      category: ConfigCategories.camera,
      description: 'Default camera resolution setting',
      type: ConfigValueType.string,
      defaultValue: 'medium',
      allowedValues: ['low', 'medium', 'high', 'veryHigh', 'ultraHigh'],
      environmentKey: 'CAMERA_RESOLUTION',
    ));

    _register(ConfigParameter<double>(
      key: ConfigKeys.cameraAspectRatio,
      category: ConfigCategories.camera,
      description: 'Camera preview aspect ratio',
      type: ConfigValueType.double,
      defaultValue: 16 / 9,
      minValue: 1.0,
      maxValue: 3.0,
      environmentKey: 'CAMERA_ASPECT_RATIO',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxFaceCaptures,
      category: ConfigCategories.camera,
      description: 'Maximum number of face captures',
      type: ConfigValueType.integer,
      defaultValue: 5,
      minValue: 1,
      maxValue: 20,
      environmentKey: 'CAMERA_MAX_CAPTURES',
    ));
  }

  /// Security Parameters
  static void _registerSecurityParameters() {
    _register(ConfigParameter<int>(
      key: ConfigKeys.minPasswordLength,
      category: ConfigCategories.security,
      description: 'Minimum password length',
      type: ConfigValueType.integer,
      defaultValue: 6,
      minValue: 4,
      maxValue: 20,
      environmentKey: 'SECURITY_MIN_PASSWORD_LENGTH',
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxPasswordLength,
      category: ConfigCategories.security,
      description: 'Maximum password length',
      type: ConfigValueType.integer,
      defaultValue: 128,
      minValue: 20,
      maxValue: 256,
      environmentKey: 'SECURITY_MAX_PASSWORD_LENGTH',
    ));

    _register(ConfigParameter<bool>(
      key: ConfigKeys.biometricAuthEnabled,
      category: ConfigCategories.security,
      description: 'Enable biometric authentication',
      type: ConfigValueType.boolean,
      defaultValue: true,
      environmentKey: 'SECURITY_BIOMETRIC_ENABLED',
    ));
  }

  /// Cache Parameters
  static void _registerCacheParameters() {
    _register(ConfigParameter<Duration>(
      key: ConfigKeys.cacheApiDuration,
      category: ConfigCategories.cache,
      description: 'Cache duration for API responses',
      type: ConfigValueType.duration,
      defaultValue: Duration(minutes: 5),
      minValue: Duration(minutes: 1),
      maxValue: Duration(hours: 24),
      environmentKey: 'CACHE_API_DURATION_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<int>(
      key: ConfigKeys.maxCacheSizeMB,
      category: ConfigCategories.cache,
      description: 'Maximum cache size in MB',
      type: ConfigValueType.integer,
      defaultValue: 50,
      minValue: 10,
      maxValue: 500,
      environmentKey: 'CACHE_MAX_SIZE_MB',
    ));
  }

  /// Kiosk Parameters
  static void _registerKioskParameters() {
    _register(ConfigParameter<Duration>(
      key: ConfigKeys.idleTimeout,
      category: ConfigCategories.kiosk,
      description: 'Idle timeout before returning to home',
      type: ConfigValueType.duration,
      defaultValue: Duration(minutes: 2),
      minValue: Duration(seconds: 30),
      maxValue: Duration(minutes: 30),
      environmentKey: 'KIOSK_IDLE_TIMEOUT_MS',
      parser: _parseDuration,
    ));

    _register(ConfigParameter<bool>(
      key: ConfigKeys.autoReturnHome,
      category: ConfigCategories.kiosk,
      description: 'Automatically return to home after idle timeout',
      type: ConfigValueType.boolean,
      defaultValue: true,
      environmentKey: 'KIOSK_AUTO_RETURN_HOME',
    ));

    _register(ConfigParameter<bool>(
      key: ConfigKeys.fullscreenMode,
      category: ConfigCategories.kiosk,
      description: 'Enable fullscreen kiosk mode',
      type: ConfigValueType.boolean,
      defaultValue: true,
      environmentKey: 'KIOSK_FULLSCREEN_MODE',
    ));
  }

  /// Debug Parameters
  static void _registerDebugParameters() {
    _register(ConfigParameter<bool>(
      key: ConfigKeys.enableFaceDetectionLogs,
      category: ConfigCategories.debug,
      description: 'Enable detailed face detection logging',
      type: ConfigValueType.boolean,
      defaultValue: false,
      environmentKey: 'DEBUG_FACE_DETECTION_LOGS',
    ));

    _register(ConfigParameter<bool>(
      key: ConfigKeys.enablePerformanceMonitoring,
      category: ConfigCategories.debug,
      description: 'Enable performance monitoring and metrics',
      type: ConfigValueType.boolean,
      defaultValue: false,
      environmentKey: 'DEBUG_PERFORMANCE_MONITORING',
    ));
  }

  /// Relay Parameters
  static void _registerRelayParameters() {
    final relayParameters = RelayConfigParametersRegistry.getRelayParameters();
    for (final parameter in relayParameters) {
      _register(parameter);
    }
  }
}

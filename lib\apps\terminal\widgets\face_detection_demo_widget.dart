import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/face_detection_provider.dart';
import '../../../packages/face_recognition/src/config/face_detection_config.dart';

/// Demo widget to test and showcase MediaPipe face detection
class FaceDetectionDemoWidget extends StatefulWidget {
  const FaceDetectionDemoWidget({super.key});

  @override
  State<FaceDetectionDemoWidget> createState() => _FaceDetectionDemoWidgetState();
}

class _FaceDetectionDemoWidgetState extends State<FaceDetectionDemoWidget> {
  bool _showEngineInfo = false;
  bool _showPerformanceMetrics = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<TerminalFaceDetectionProvider>(
      builder: (context, provider, child) {
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.face,
                      color: provider.isInitialized ? Colors.green : Colors.orange,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Face Detection Engine',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          Text(
                            provider.currentEngine.displayName,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(provider),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(provider),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Configuration controls
                _buildConfigurationControls(provider),
                
                const SizedBox(height: 16),
                
                // Engine switching buttons
                _buildEngineSwitchButtons(provider),
                
                const SizedBox(height: 16),
                
                // Performance metrics
                _buildPerformanceMetrics(provider),
                
                const SizedBox(height: 16),
                
                // Engine information
                _buildEngineInformation(provider),
                
                const SizedBox(height: 16),
                
                // Action buttons
                _buildActionButtons(provider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConfigurationControls(TerminalFaceDetectionProvider provider) {
    return ExpansionTile(
      title: const Text('Configuration'),
      leading: const Icon(Icons.settings),
      children: [
        // Confidence threshold
        ListTile(
          title: Text('Confidence Threshold: ${provider.confidenceThreshold.toStringAsFixed(2)}'),
          subtitle: Slider(
            value: provider.confidenceThreshold,
            min: 0.1,
            max: 0.9,
            divisions: 8,
            onChanged: (value) {
              provider.updateConfiguration(confidenceThreshold: value);
            },
          ),
        ),
        
        // Max faces
        ListTile(
          title: Text('Max Faces: ${provider.maxFaces}'),
          subtitle: Slider(
            value: provider.maxFaces.toDouble(),
            min: 1,
            max: 10,
            divisions: 9,
            onChanged: (value) {
              provider.updateConfiguration(maxFaces: value.round());
            },
          ),
        ),
        
        // Landmarks toggle
        SwitchListTile(
          title: const Text('Enable Landmarks'),
          subtitle: const Text('Extract facial landmarks (eyes, nose, mouth)'),
          value: provider.enableLandmarks,
          onChanged: (value) {
            provider.updateConfiguration(enableLandmarks: value);
          },
        ),
        
        // Fallback toggle
        SwitchListTile(
          title: const Text('Enable Fallback'),
          subtitle: const Text('Automatically switch to backup engine on failure'),
          value: provider.enableFallback,
          onChanged: (value) {
            provider.updateConfiguration(enableFallback: value);
          },
        ),
      ],
    );
  }

  Widget _buildEngineSwitchButtons(TerminalFaceDetectionProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Switch Engine',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: provider.isInitialized && !provider.useMediaPipe
                  ? () => provider.switchToMediaPipe()
                  : null,
              icon: const Icon(Icons.smart_toy),
              label: const Text('MediaPipe'),
              style: ElevatedButton.styleFrom(
                backgroundColor: provider.useMediaPipe ? Colors.blue : null,
                foregroundColor: provider.useMediaPipe ? Colors.white : null,
              ),
            ),
            ElevatedButton.icon(
              onPressed: provider.isInitialized && provider.useMediaPipe
                  ? () => provider.switchToMLKit()
                  : null,
              icon: const Icon(Icons.psychology),
              label: const Text('ML Kit'),
              style: ElevatedButton.styleFrom(
                backgroundColor: !provider.useMediaPipe ? Colors.green : null,
                foregroundColor: !provider.useMediaPipe ? Colors.white : null,
              ),
            ),
            ElevatedButton.icon(
              onPressed: provider.isInitialized
                  ? () => provider.switchToHybrid()
                  : null,
              icon: const Icon(Icons.merge_type),
              label: const Text('Hybrid'),
              style: ElevatedButton.styleFrom(
                backgroundColor: provider.currentEngine == FaceDetectionEngineType.hybrid 
                    ? Colors.purple : null,
                foregroundColor: provider.currentEngine == FaceDetectionEngineType.hybrid 
                    ? Colors.white : null,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPerformanceMetrics(TerminalFaceDetectionProvider provider) {
    return ExpansionTile(
      title: const Text('Performance Metrics'),
      leading: const Icon(Icons.speed),
      initiallyExpanded: _showPerformanceMetrics,
      onExpansionChanged: (expanded) {
        setState(() {
          _showPerformanceMetrics = expanded;
        });
      },
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'FPS',
                provider.averageFPS.toStringAsFixed(1),
                Icons.speed,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMetricCard(
                'Total',
                provider.totalDetections.toString(),
                Icons.analytics,
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMetricCard(
                'Success Rate',
                '${(provider.successRate * 100).toStringAsFixed(1)}%',
                Icons.check_circle,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (provider.lastDetectedFaces.isNotEmpty)
          Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Last Detection: ${provider.lastDetectedFaces.length} faces',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Time: ${provider.lastDetectionTime.toString().substring(11, 19)}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEngineInformation(TerminalFaceDetectionProvider provider) {
    return ExpansionTile(
      title: const Text('Engine Information'),
      leading: const Icon(Icons.info),
      initiallyExpanded: _showEngineInfo,
      onExpansionChanged: (expanded) {
        setState(() {
          _showEngineInfo = expanded;
        });
      },
      children: [
        if (provider.isInitialized) ...[
          _buildInfoRow('Current Engine', provider.currentEngine.displayName),
          _buildInfoRow('Supports Landmarks', provider.currentEngine.supportsLandmarks ? 'Yes' : 'No'),
          _buildInfoRow('Supports Tracking', provider.currentEngine.supportsTracking ? 'Yes' : 'No'),
          _buildInfoRow('Confidence Threshold', provider.confidenceThreshold.toStringAsFixed(2)),
          _buildInfoRow('Max Faces', provider.maxFaces.toString()),
          _buildInfoRow('Landmarks Enabled', provider.enableLandmarks ? 'Yes' : 'No'),
          _buildInfoRow('Fallback Enabled', provider.enableFallback ? 'Yes' : 'No'),
        ] else ...[
          const ListTile(
            leading: Icon(Icons.warning, color: Colors.orange),
            title: Text('Engine not initialized'),
            subtitle: Text('Click Initialize to start face detection'),
          ),
        ],
        
        if (provider.lastError != null)
          Card(
            color: Colors.red.shade50,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error: ${provider.lastError}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(TerminalFaceDetectionProvider provider) {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: !provider.isInitialized
              ? () => provider.initialize()
              : null,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Initialize'),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: provider.isInitialized
              ? () => provider.resetMetrics()
              : null,
          icon: const Icon(Icons.refresh),
          label: const Text('Reset Metrics'),
        ),
        const SizedBox(width: 8),
        ElevatedButton.icon(
          onPressed: provider.isInitialized
              ? () => _showEngineDetails(provider)
              : null,
          icon: const Icon(Icons.info_outline),
          label: const Text('Details'),
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TerminalFaceDetectionProvider provider) {
    if (!provider.isInitialized) return Colors.orange;
    if (provider.lastError != null) return Colors.red;
    if (provider.isDetecting) return Colors.blue;
    return Colors.green;
  }

  String _getStatusText(TerminalFaceDetectionProvider provider) {
    if (!provider.isInitialized) return 'Not Initialized';
    if (provider.lastError != null) return 'Error';
    if (provider.isDetecting) return 'Detecting';
    return 'Ready';
  }

  void _showEngineDetails(TerminalFaceDetectionProvider provider) {
    final engineInfo = provider.getEngineInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Engine Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              for (final entry in engineInfo.entries)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('${entry.key}: ${entry.value}'),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

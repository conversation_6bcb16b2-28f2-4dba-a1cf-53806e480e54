import 'dart:typed_data';
import 'dart:ui';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

import '../../core/interfaces/face_detection_engine.dart';
import '../../core/models/face_recognition_config.dart';
import '../utils/image_utils.dart';

/// Google ML Kit face detection engine (fallback option)
class MLKitDetectionEngine implements FaceDetectionEngine {
  FaceDetector? _faceDetector;
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  
  final FaceRecognitionConfig _faceConfig;
  
  MLKitDetectionEngine({required FaceRecognitionConfig config}) : _faceConfig = config;
  
  @override
  String get engineName => 'ML Kit';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing ML Kit Detection Engine');
      }
      
      // Configure ML Kit face detector
      final options = FaceDetectorOptions(
        enableContours: false,
        enableLandmarks: false,
        enableClassification: false,
        enableTracking: false,
        minFaceSize: _faceConfig.minFaceSize / 100.0, // ML Kit expects 0.0-1.0
        performanceMode: FaceDetectorMode.fast,
      );
      
      _faceDetector = FaceDetector(options: options);
      _config = _faceConfig;
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ ML Kit engine initialized successfully');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize ML Kit engine: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<List<FaceDetection>> detectFaces(Uint8List imageBytes) async {
    if (!_isInitialized || _faceDetector == null) {
      throw FaceDetectionException('ML Kit engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Create InputImage from bytes
      final inputImage = InputImage.fromBytes(
        bytes: imageBytes,
        metadata: InputImageMetadata(
          size: Size(640, 480), // Default size, should be actual image size
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.jpeg,
          bytesPerRow: 640 * 3,
        ),
      );
      
      // Detect faces
      final faces = await _faceDetector!.processImage(inputImage);
      
      // Convert ML Kit faces to our format
      final detections = faces.map((face) => _convertMLKitFace(face, 640, 480)).toList();
      
      stopwatch.stop();
      
      if (kDebugMode) {
        print('🔍 ML Kit detection completed in ${stopwatch.elapsedMilliseconds}ms');
        print('   Detected faces: ${detections.length}');
      }
      
      return detections;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ ML Kit detection failed: $e');
      }
      throw FaceDetectionException('Detection failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  @override
  Future<List<FaceDetection>> processCameraImage(CameraImage image) async {
    if (!_isInitialized || _faceDetector == null) {
      throw FaceDetectionException('ML Kit engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Create InputImage from camera image
      final inputImage = InputImage.fromBytes(
        bytes: ImageUtils.convertCameraImageToRGB(image),
        metadata: InputImageMetadata(
          size: Size(image.width.toDouble(), image.height.toDouble()),
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.yuv420,
          bytesPerRow: image.planes[0].bytesPerRow,
        ),
      );
      
      // Detect faces
      final faces = await _faceDetector!.processImage(inputImage);
      
      // Convert ML Kit faces to our format
      final detections = faces.map((face) => _convertMLKitFace(face, image.width, image.height)).toList();
      
      stopwatch.stop();
      
      if (kDebugMode && detections.isNotEmpty) {
        print('🔍 ML Kit camera processing: ${stopwatch.elapsedMilliseconds}ms, ${detections.length} faces');
      }
      
      return detections;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ ML Kit camera processing failed: $e');
      }
      throw FaceDetectionException('Camera processing failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  /// Convert ML Kit Face to our FaceDetection format
  FaceDetection _convertMLKitFace(Face face, int imageWidth, int imageHeight) {
    // Extract landmarks if available
    final landmarks = <Point>[];
    
    // ML Kit provides different landmark types
    final leftEye = face.landmarks[FaceLandmarkType.leftEye];
    final rightEye = face.landmarks[FaceLandmarkType.rightEye];
    final noseBase = face.landmarks[FaceLandmarkType.noseBase];
    final leftMouth = face.landmarks[FaceLandmarkType.leftMouth];
    final rightMouth = face.landmarks[FaceLandmarkType.rightMouth];
    
    if (leftEye != null) landmarks.add(Point(leftEye.position.x, leftEye.position.y));
    if (rightEye != null) landmarks.add(Point(rightEye.position.x, rightEye.position.y));
    if (noseBase != null) landmarks.add(Point(noseBase.position.x, noseBase.position.y));
    if (leftMouth != null) landmarks.add(Point(leftMouth.position.x, leftMouth.position.y));
    if (rightMouth != null) landmarks.add(Point(rightMouth.position.x, rightMouth.position.y));
    
    // Calculate face quality based on size and position
    final quality = _calculateFaceQuality(face.boundingBox, imageWidth, imageHeight);
    
    // Calculate pose from head angles
    final pose = FacePose(
      yaw: face.headEulerAngleY ?? 0.0,
      pitch: face.headEulerAngleX ?? 0.0,
      roll: face.headEulerAngleZ ?? 0.0,
    );
    
    return FaceDetection(
      boundingBox: face.boundingBox,
      landmarks: landmarks,
      confidence: 0.8, // ML Kit doesn't provide confidence, use default
      quality: quality,
      pose: pose,
      metadata: {
        'engine': engineName,
        'tracking_id': face.trackingId,
        'smiling_probability': face.smilingProbability,
        'left_eye_open_probability': face.leftEyeOpenProbability,
        'right_eye_open_probability': face.rightEyeOpenProbability,
      },
    );
  }
  
  /// Calculate face quality score
  double _calculateFaceQuality(Rect boundingBox, int imageWidth, int imageHeight) {
    // Size quality (larger faces are better)
    final faceArea = boundingBox.width * boundingBox.height;
    final imageArea = imageWidth * imageHeight;
    final sizeRatio = faceArea / imageArea;
    final sizeQuality = (sizeRatio * 20).clamp(0.0, 1.0); // Normalize
    
    // Position quality (centered faces are better)
    final centerX = boundingBox.center.dx;
    final centerY = boundingBox.center.dy;
    final imageCenterX = imageWidth / 2;
    final imageCenterY = imageHeight / 2;
    
    final distanceFromCenter = ((centerX - imageCenterX).abs() + (centerY - imageCenterY).abs()) / 2;
    final maxDistance = (imageCenterX + imageCenterY) / 2;
    final positionQuality = 1.0 - (distanceFromCenter / maxDistance);
    
    // Combine qualities
    return (sizeQuality * 0.7 + positionQuality * 0.3).clamp(0.0, 1.0);
  }
  
  @override
  void dispose() {
    _faceDetector?.close();
    _faceDetector = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ ML Kit engine disposed');
    }
  }
}

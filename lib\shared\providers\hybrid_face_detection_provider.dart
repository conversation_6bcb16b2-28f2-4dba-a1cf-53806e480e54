import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

import '../../packages/face_recognition/face_recognition.dart';
import '../utils/error_safe_handler.dart';

/// Hybrid face detection provider using the new face recognition package
class HybridFaceDetectionProvider extends ChangeNotifier with ErrorSafeMixin {
  // Face recognition system
  TerminalFaceSystem? _faceSystem;
  
  // Legacy compatibility
  List<FaceDetection> _faces = [];
  FaceDetection? _bestFace;
  double _faceQuality = 0.0;
  bool _isDetecting = false;
  bool _isDisposed = false;
  bool _isInitializing = false;
  
  // Callback for face detection state changes (for relay control)
  Function(bool hasFace)? _onFaceDetectionStateChanged;
  
  // Store latest camera image for face recognition (for future use)
  // CameraImage? _latestCameraImage;
  // CameraDescription? _latestCameraDescription;
  
  // Performance monitoring (for future use)
  // final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  bool _performanceMonitoringEnabled = kDebugMode;
  int _frameCount = 0;
  DateTime _lastDetectionTime = DateTime.now();
  
  // Frame skipping for performance optimization
  int _frameSkipCounter = 0;
  static const int _frameSkipInterval = 2; // Process every 2nd frame only
  
  // Error recovery
  int _consecutiveErrors = 0;
  static const int _maxConsecutiveErrors = 5;
  
  // Recognition state
  TerminalRecognitionResult? _lastRecognitionResult;
  bool _isRecognizing = false;
  Timer? _recognitionThrottleTimer;
  bool _isRecognitionThrottleActive = false;
  
  // Getters for legacy compatibility
  List<FaceDetection> get faces => _faces;
  FaceDetection? get bestFace => _bestFace;
  double get faceQuality => _faceQuality;
  bool get isDetecting => _isDetecting;
  bool get isDisposed => _isDisposed;
  
  // New getters for hybrid system
  TerminalRecognitionResult? get lastRecognitionResult => _lastRecognitionResult;
  bool get isRecognizing => _isRecognizing;
  bool get isOnline => _faceSystem?.isOnline ?? false;
  TerminalStats? get stats => _faceSystem?.getStats();
  
  /// Initialize the hybrid face detection system
  Future<void> initialize({
    TerminalDeviceType deviceType = TerminalDeviceType.telpoF8,
    String? serverEndpoint,
    String? apiKey,
  }) async {
    if (_isInitializing || _isDisposed) return;
    
    _isInitializing = true;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing HybridFaceDetectionProvider');
      }
      
      // Initialize terminal face system
      _faceSystem = await FaceRecognitionTerminal.initialize(
        deviceType: deviceType,
        performanceProfile: PerformanceProfile.maxPerformance,
        serverEndpoint: serverEndpoint,
        apiKey: apiKey,
      );
      
      // Listen to face system changes
      _faceSystem!.addListener(_onFaceSystemChanged);
      
      if (kDebugMode) {
        print('✅ HybridFaceDetectionProvider initialized');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize HybridFaceDetectionProvider: $e');
      }
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }
  
  /// Process camera image for face detection and recognition
  Future<void> processImage(CameraImage image, CameraDescription camera) async {
    if (_isDisposed || _isDetecting || _faceSystem == null) return;
    
    // Frame skipping for performance
    _frameSkipCounter++;
    if (_frameSkipCounter < _frameSkipInterval) return;
    _frameSkipCounter = 0;
    
    _isDetecting = true;
    // Store for future use if needed
    // _latestCameraImage = image;
    // _latestCameraDescription = camera;
    
    try {
      // Process image through face system
      await _faceSystem!.processCameraImage(image);
      
      _consecutiveErrors = 0;
      _frameCount++;
      
      // Update performance monitoring
      if (_performanceMonitoringEnabled) {
        _updatePerformanceMetrics();
      }
      
    } catch (e) {
      _consecutiveErrors++;
      
      if (kDebugMode) {
        print('❌ Error processing camera image: $e');
      }
      
      // Reset system if too many consecutive errors
      if (_consecutiveErrors >= _maxConsecutiveErrors) {
        await _resetSystem();
      }
    } finally {
      _isDetecting = false;
    }
  }
  
  /// Handle face system state changes
  void _onFaceSystemChanged() {
    if (_isDisposed) return;
    
    // Update faces from face system
    _faces = _faceSystem?.detectedFaces ?? [];
    _bestFace = _faces.isNotEmpty ? _faces.first : null;
    _faceQuality = _bestFace?.quality ?? 0.0;
    
    // Update recognition result (placeholder - implement when available)
    // _lastRecognitionResult = _faceSystem?.lastRecognitionResult;
    
    // Trigger face detection state callback
    final hasFace = _faces.isNotEmpty;
    _onFaceDetectionStateChanged?.call(hasFace);
    
    notifyListeners();
  }
  
  /// Update performance metrics
  void _updatePerformanceMetrics() {
    final now = DateTime.now();
    final timeSinceLastDetection = now.difference(_lastDetectionTime);
    
    if (timeSinceLastDetection.inMilliseconds > 0) {
      // final fps = 1000 / timeSinceLastDetection.inMilliseconds;
      // _performanceMonitor.recordFPS(fps); // Implement when available
    }
    
    _lastDetectionTime = now;
    
    // Log performance every 30 frames
    if (_frameCount % 30 == 0) {
      final stats = _faceSystem?.getStats();
      if (stats != null && kDebugMode) {
        print('📊 Hybrid Face Detection Performance:');
        print('   FPS: ${stats.averageFPS.toStringAsFixed(1)}');
        print('   Processed: ${stats.totalProcessed}');
        print('   Recognized: ${stats.totalRecognized}');
        print('   Success Rate: ${(stats.recognitionRate * 100).toStringAsFixed(1)}%');
        print('   Network: ${stats.isOnline ? "Online" : "Offline"}');
      }
    }
  }
  
  /// Reset system after errors
  Future<void> _resetSystem() async {
    if (kDebugMode) {
      print('🔄 Resetting hybrid face detection system...');
    }
    
    try {
      // Dispose current system
      await _faceSystem?.dispose();
      
      // Reinitialize
      _faceSystem = await FaceRecognitionTerminal.initialize(
        deviceType: TerminalDeviceType.telpoF8,
        performanceProfile: PerformanceProfile.maxPerformance,
      );
      
      _faceSystem!.addListener(_onFaceSystemChanged);
      _consecutiveErrors = 0;
      
      if (kDebugMode) {
        print('✅ System reset completed');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to reset system: $e');
      }
    }
  }
  
  /// Set face detection state change callback
  void setOnFaceDetectionStateChanged(Function(bool hasFace) callback) {
    _onFaceDetectionStateChanged = callback;
  }
  
  /// Get face quality for a specific face (legacy compatibility)
  double getFaceQuality(FaceDetection face) {
    return face.quality;
  }
  
  /// Check if face is in guide frame (legacy compatibility)
  bool isFaceInGuideFrame(FaceDetection face) {
    // This would be handled by the face system's quality assessment
    return face.quality > 0.5;
  }
  
  /// Trigger face recognition manually
  Future<void> triggerRecognition() async {
    if (_faceSystem == null || _isRecognitionThrottleActive || _bestFace?.croppedFace == null) {
      return;
    }
    
    _isRecognizing = true;
    _isRecognitionThrottleActive = true;
    
    try {
      // The face system handles recognition automatically
      // This method is for manual triggering if needed
      
      // Start throttle timer
      _recognitionThrottleTimer?.cancel();
      _recognitionThrottleTimer = Timer(const Duration(seconds: 2), () {
        _isRecognitionThrottleActive = false;
      });
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Manual recognition trigger failed: $e');
      }
    } finally {
      _isRecognizing = false;
    }
  }
  
  /// Enable/disable performance monitoring
  void setPerformanceMonitoring(bool enabled) {
    _performanceMonitoringEnabled = enabled;
  }
  
  /// Get current performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    final stats = _faceSystem?.getStats();
    return {
      'fps': stats?.averageFPS ?? 0.0,
      'total_processed': stats?.totalProcessed ?? 0,
      'total_recognized': stats?.totalRecognized ?? 0,
      'recognition_rate': stats?.recognitionRate ?? 0.0,
      'is_online': stats?.isOnline ?? false,
      'memory_usage': stats?.memoryUsage ?? 0,
      'consecutive_failures': stats?.consecutiveFailures ?? 0,
    };
  }
  
  /// Force network status check
  Future<void> forceNetworkCheck() async {
    // This would be handled by the face system's network service
    if (kDebugMode) {
      print('🔍 Forcing network status check...');
    }
  }
  
  /// Test hardware devices (for terminals)
  Future<void> testHardwareDevices() async {
    if (kDebugMode) {
      print('🧪 Testing hardware devices...');
    }
    
    // This would trigger the side effect controller test
    // Implementation depends on the specific terminal device
  }
  
  @override
  Future<void> dispose() async {
    if (_isDisposed) return;
    
    _isDisposed = true;
    
    try {
      _recognitionThrottleTimer?.cancel();
      await _faceSystem?.dispose();
      await FaceRecognitionTerminal.dispose();
      
      _faces.clear();
      _bestFace = null;
      _lastRecognitionResult = null;
      
      if (kDebugMode) {
        print('🗑️ HybridFaceDetectionProvider disposed');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error during disposal: $e');
      }
    }
    
    super.dispose();
  }
}

/// Legacy face detection configuration for compatibility
class FaceDetectionConfig {
  final double minFaceQualityForDetection;
  final double minFaceQualityForRecognition;
  final int maxFaces;
  final bool enableTracking;
  
  const FaceDetectionConfig({
    this.minFaceQualityForDetection = 0.3,
    this.minFaceQualityForRecognition = 0.5,
    this.maxFaces = 5,
    this.enableTracking = true,
  });
  
  static const FaceDetectionConfig defaultConfig = FaceDetectionConfig();
}

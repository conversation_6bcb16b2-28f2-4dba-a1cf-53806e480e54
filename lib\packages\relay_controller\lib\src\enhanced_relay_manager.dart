import 'dart:async';
import 'package:flutter/foundation.dart';
import 'relay_controller_base.dart';
import 'usb_ttl_relay_controller.dart';
import 'usb_controller.dart';
import 'http_controller.dart';
import 'secure_http_controller.dart';

/// Enhanced relay manager for comprehensive relay control
/// 
/// This manager provides:
/// - Automatic device discovery and connection
/// - Multiple transport support (USB, HTTP, Secure HTTP)
/// - Error handling and recovery
/// - Device health monitoring
/// - Connection pooling
class EnhancedRelayManager {
  static final EnhancedRelayManager _instance = EnhancedRelayManager._internal();
  factory EnhancedRelayManager() => _instance;
  EnhancedRelayManager._internal();

  /// Active relay controllers
  final Map<String, RelayController> _controllers = {};
  
  /// Device discovery results
  final Map<String, DeviceInfo> _discoveredDevices = {};
  
  /// Connection status monitoring
  final Map<String, ConnectionStatus> _connectionStatus = {};
  
  /// Health monitoring timer
  Timer? _healthMonitorTimer;
  
  /// Stream controller for device events
  final StreamController<RelayDeviceEvent> _eventController = 
      StreamController<RelayDeviceEvent>.broadcast();

  /// Get stream of device events
  Stream<RelayDeviceEvent> get deviceEvents => _eventController.stream;

  /// Get list of active controllers
  List<RelayController> get activeControllers => _controllers.values.toList();

  /// Get list of discovered devices
  List<DeviceInfo> get discoveredDevices => _discoveredDevices.values.toList();

  /// Initialize the relay manager
  Future<void> initialize() async {
    try {
      await _discoverDevices();
      _startHealthMonitoring();
      _emitEvent(RelayDeviceEvent.managerInitialized());
    } catch (e) {
      _emitEvent(RelayDeviceEvent.error('Manager initialization failed: $e'));
      rethrow;
    }
  }

  /// Discover available relay devices
  Future<void> _discoverDevices() async {
    _discoveredDevices.clear();
    
    // Discover USB-TTL devices
    await _discoverUsbTtlDevices();
    
    // Discover USB devices
    await _discoverUsbDevices();
    
    _emitEvent(RelayDeviceEvent.discoveryCompleted(_discoveredDevices.length));
  }

  /// Discover USB-TTL relay devices
  Future<void> _discoverUsbTtlDevices() async {
    try {
      final devices = await UsbTtlRelayController.getAvailableDevices();
      
      for (final device in devices) {
        final deviceId = 'usb_ttl_${device.vid}_${device.pid}_${device.deviceId}';
        _discoveredDevices[deviceId] = DeviceInfo(
          id: deviceId,
          name: device.deviceName,
          type: DeviceType.usbTtl,
          connectionType: ConnectionType.usb,
          vendorId: device.vid,
          productId: device.pid,
          serialNumber: device.serial,
          isAvailable: true,
        );
      }
      
      _emitEvent(RelayDeviceEvent.usbDevicesDiscovered(devices.length));
    } catch (e) {
      _emitEvent(RelayDeviceEvent.error('USB-TTL discovery failed: $e'));
    }
  }

  /// Discover standard USB relay devices
  Future<void> _discoverUsbDevices() async {
    try {
      final devices = await UsbRelayController.getAvailableDevices();
      
      for (final device in devices) {
        final deviceId = 'usb_${device.vid}_${device.pid}_${device.deviceId}';
        if (!_discoveredDevices.containsKey(deviceId)) {
          _discoveredDevices[deviceId] = DeviceInfo(
            id: deviceId,
            name: device.deviceName,
            type: DeviceType.usb,
            connectionType: ConnectionType.usb,
            vendorId: device.vid,
            productId: device.pid,
            serialNumber: device.serial,
            isAvailable: true,
          );
        }
      }
    } catch (e) {
      _emitEvent(RelayDeviceEvent.error('USB discovery failed: $e'));
    }
  }

  /// Connect to a relay device
  Future<RelayController?> connectToDevice(String deviceId, {
    Map<String, dynamic>? config,
  }) async {
    try {
      if (_controllers.containsKey(deviceId)) {
        final controller = _controllers[deviceId]!;
        // Check if controller is still valid by trying to get status
        try {
          await controller.getStatus();
          return controller;
        } catch (e) {
          // Controller is no longer valid, remove it
          _controllers.remove(deviceId);
        }
      }

      final deviceInfo = _discoveredDevices[deviceId];
      if (deviceInfo == null) {
        throw Exception('Device not found: $deviceId');
      }

      final controller = await _createController(deviceInfo, config);
      if (controller != null) {
        await _connectController(controller, deviceInfo);
        _controllers[deviceId] = controller;
        _connectionStatus[deviceId] = ConnectionStatus.connected;
        
        _emitEvent(RelayDeviceEvent.deviceConnected(deviceId));
        return controller;
      }
    } catch (e) {
      _connectionStatus[deviceId] = ConnectionStatus.failed;
      _emitEvent(RelayDeviceEvent.connectionFailed(deviceId, e.toString()));
      rethrow;
    }
    
    return null;
  }

  /// Create appropriate controller for device
  Future<RelayController?> _createController(DeviceInfo deviceInfo, Map<String, dynamic>? config) async {
    switch (deviceInfo.type) {
      case DeviceType.usbTtl:
        return UsbTtlRelayController(
          deviceId: deviceInfo.id,
          deviceName: deviceInfo.name,
          relayCount: config?['relay_count'] ?? 4,
          baudRate: config?['baud_rate'] ?? 115200,
          deviceProfile: _getDeviceProfile(config),
        );
        
      case DeviceType.usb:
        return UsbRelayController(
          deviceId: deviceInfo.id,
          deviceName: deviceInfo.name,
          baudRate: config?['baud_rate'] ?? 9600,
        );
        
      case DeviceType.http:
        return HttpRelayController(
          deviceId: deviceInfo.id,
          urlOn: config?['url_on'] ?? '',
          urlOff: config?['url_off'] ?? '',
        );
        
      case DeviceType.secureHttp:
        return SecureHttpRelayController(
          deviceId: deviceInfo.id,
          deviceType: config?['device_type'] ?? 'relay_controller',
          serverBaseUrl: config?['server_url'] ?? '',
          deviceName: deviceInfo.name,
        );
    }
  }

  /// Get device profile from config
  DeviceProfile _getDeviceProfile(Map<String, dynamic>? config) {
    final profileName = config?['device_profile'] as String?;
    switch (profileName) {
      case 'arduino':
        return DeviceProfile.arduino();
      case 'simple':
        return DeviceProfile.simple();
      default:
        return DeviceProfile.esp32();
    }
  }

  /// Connect controller to device
  Future<void> _connectController(RelayController controller, DeviceInfo deviceInfo) async {
    if (controller is UsbTtlRelayController || controller is UsbRelayController) {
      // Find the actual USB device
      final devices = controller is UsbTtlRelayController 
          ? await UsbTtlRelayController.getAvailableDevices()
          : await UsbRelayController.getAvailableDevices();
          
      final targetDevice = devices.firstWhere(
        (d) => d.vid == deviceInfo.vendorId && d.pid == deviceInfo.productId,
        orElse: () => throw Exception('USB device not found'),
      );
      
      if (controller is UsbTtlRelayController) {
        await controller.connect(targetDevice);
      } else if (controller is UsbRelayController) {
        await controller.connect(targetDevice);
      }
    }
  }

  /// Disconnect from device
  Future<void> disconnectDevice(String deviceId) async {
    try {
      final controller = _controllers[deviceId];
      if (controller != null) {
        await controller.dispose();
        _controllers.remove(deviceId);
        _connectionStatus[deviceId] = ConnectionStatus.disconnected;
        _emitEvent(RelayDeviceEvent.deviceDisconnected(deviceId));
      }
    } catch (e) {
      _emitEvent(RelayDeviceEvent.error('Disconnect failed for $deviceId: $e'));
    }
  }

  /// Control relay on specific device
  Future<void> controlRelay(String deviceId, int relayIndex, RelayAction action) async {
    final controller = _controllers[deviceId];
    if (controller == null) {
      throw Exception('Device not connected: $deviceId');
    }

    try {
      if (controller is UsbTtlRelayController) {
        await controller.controlRelay(relayIndex, action);
      } else {
        // For simple controllers, map actions to basic on/off
        switch (action) {
          case RelayAction.on:
            await controller.triggerOn();
            break;
          case RelayAction.off:
            await controller.triggerOff();
            break;
          case RelayAction.toggle:
            // Toggle logic would need to be implemented
            await controller.triggerOn();
            break;
        }
      }
      
      _emitEvent(RelayDeviceEvent.relayControlled(deviceId, relayIndex, action));
    } catch (e) {
      _emitEvent(RelayDeviceEvent.error('Relay control failed: $e'));
      rethrow;
    }
  }

  /// Start health monitoring
  void _startHealthMonitoring() {
    _healthMonitorTimer?.cancel();
    _healthMonitorTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _checkDeviceHealth();
    });
  }

  /// Check health of connected devices
  Future<void> _checkDeviceHealth() async {
    for (final entry in _controllers.entries) {
      final deviceId = entry.key;
      final controller = entry.value;
      
      try {
        // Check if controller is still connected by trying to get status
        await controller.getStatus();
        // If we get here, controller is still connected
      } catch (e) {
        // Controller is no longer connected
        _connectionStatus[deviceId] = ConnectionStatus.disconnected;
        _emitEvent(RelayDeviceEvent.deviceDisconnected(deviceId));
      }
    }
  }

  /// Emit device event
  void _emitEvent(RelayDeviceEvent event) {
    if (kDebugMode) {
      print('🔌 RelayManager: ${event.type} - ${event.message}');
    }
    _eventController.add(event);
  }

  /// Dispose manager
  Future<void> dispose() async {
    _healthMonitorTimer?.cancel();
    
    for (final controller in _controllers.values) {
      await controller.dispose();
    }
    
    _controllers.clear();
    _discoveredDevices.clear();
    _connectionStatus.clear();
    
    await _eventController.close();
  }
}

/// Device information
class DeviceInfo {
  final String id;
  final String name;
  final DeviceType type;
  final ConnectionType connectionType;
  final int? vendorId;
  final int? productId;
  final String? serialNumber;
  final bool isAvailable;

  const DeviceInfo({
    required this.id,
    required this.name,
    required this.type,
    required this.connectionType,
    this.vendorId,
    this.productId,
    this.serialNumber,
    this.isAvailable = false,
  });
}

/// Device types
enum DeviceType { usbTtl, usb, http, secureHttp }

/// Connection types
enum ConnectionType { usb, network, bluetooth }

/// Connection status
enum ConnectionStatus { disconnected, connecting, connected, failed }

/// Relay device events
class RelayDeviceEvent {
  final String type;
  final String message;
  final Map<String, dynamic> data;

  const RelayDeviceEvent(this.type, this.message, [this.data = const {}]);

  factory RelayDeviceEvent.managerInitialized() => 
      const RelayDeviceEvent('manager_initialized', 'Relay manager initialized');
      
  factory RelayDeviceEvent.discoveryCompleted(int deviceCount) => 
      RelayDeviceEvent('discovery_completed', 'Found $deviceCount devices', {'count': deviceCount});
      
  factory RelayDeviceEvent.usbDevicesDiscovered(int count) => 
      RelayDeviceEvent('usb_discovered', 'Found $count USB devices', {'count': count});
      
  factory RelayDeviceEvent.deviceConnected(String deviceId) => 
      RelayDeviceEvent('device_connected', 'Device connected: $deviceId', {'device_id': deviceId});
      
  factory RelayDeviceEvent.deviceDisconnected(String deviceId) => 
      RelayDeviceEvent('device_disconnected', 'Device disconnected: $deviceId', {'device_id': deviceId});
      
  factory RelayDeviceEvent.connectionFailed(String deviceId, String error) => 
      RelayDeviceEvent('connection_failed', 'Connection failed: $deviceId - $error', 
          {'device_id': deviceId, 'error': error});
          
  factory RelayDeviceEvent.relayControlled(String deviceId, int relayIndex, RelayAction action) => 
      RelayDeviceEvent('relay_controlled', 'Relay $relayIndex ${action.name} on $deviceId', 
          {'device_id': deviceId, 'relay_index': relayIndex, 'action': action.name});
          
  factory RelayDeviceEvent.error(String message) => 
      RelayDeviceEvent('error', message);
}

#!/bin/bash

echo "🔄 Restoring original package name..."
echo "===================================="

# Restore android/app/build.gradle.kts
echo "📝 Restoring android/app/build.gradle.kts..."
sed -i 's/com\.ccam\.mobile\.temp/com.ccam.mobile/g' android/app/build.gradle.kts
sed -i 's/C-CAM Mobile Temp/C-CAM Mobile Debug/g' android/app/build.gradle.kts

# Restore lib/apps/mobile/main_mobile.dart
echo "📝 Restoring lib/apps/mobile/main_mobile.dart..."
sed -i "s/appName: 'C-CAM Mobile Temp'/appName: 'C-CAM Mobile'/g" lib/apps/mobile/main_mobile.dart
sed -i "s/appId: 'com\.ccam\.mobile\.temp'/appId: 'com.ccam.mobile'/g" lib/apps/mobile/main_mobile.dart

# Restore lib/apps/mobile/main.dart
echo "📝 Restoring lib/apps/mobile/main.dart..."
sed -i "s/appName: 'C-CAM Mobile Temp'/appName: 'C-CAM Mobile'/g" lib/apps/mobile/main.dart
sed -i "s/appId: 'com\.ccam\.mobile\.temp'/appId: 'com.ccam.mobile'/g" lib/apps/mobile/main.dart

# Restore lib/apps/mobile/config/mobile_app_config.dart
echo "📝 Restoring lib/apps/mobile/config/mobile_app_config.dart..."
sed -i "s/C-Face Mobile Temp/C-Face Mobile/g" lib/apps/mobile/config/mobile_app_config.dart

echo ""
echo "✅ Package name restored to original!"
echo "📦 Original package: com.ccam.mobile"
echo "📱 Original app name: C-CAM Mobile"
echo ""
echo "💡 You can now run the original script:"
echo "   ./scripts/run_mobile.sh"

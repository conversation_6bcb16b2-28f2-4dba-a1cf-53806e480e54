import 'package:flutter/material.dart';

/// Custom loading widget with various display options
/// 
/// Provides consistent loading indicators across mobile and terminal apps
class CLoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;
  final Color? color;
  final LoadingType type;
  final bool overlay;
  final Color? overlayColor;

  const CLoadingWidget({
    super.key,
    this.message,
    this.size,
    this.color,
    this.type = LoadingType.circular,
    this.overlay = false,
    this.overlayColor,
  });

  /// Create a circular loading indicator
  const CLoadingWidget.circular({
    super.key,
    this.message,
    this.size,
    this.color,
    this.overlay = false,
    this.overlayColor,
  }) : type = LoadingType.circular;

  /// Create a linear loading indicator
  const CLoadingWidget.linear({
    super.key,
    this.message,
    this.color,
    this.overlay = false,
    this.overlayColor,
  }) : type = LoadingType.linear,
       size = null;

  /// Create a dots loading indicator
  const CLoadingWidget.dots({
    super.key,
    this.message,
    this.size,
    this.color,
    this.overlay = false,
    this.overlayColor,
  }) : type = LoadingType.dots;

  /// Create a full-screen overlay loading
  const CLoadingWidget.overlay({
    super.key,
    this.message,
    this.size,
    this.color,
    this.overlayColor,
  }) : type = LoadingType.circular,
       overlay = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final loadingColor = color ?? theme.colorScheme.primary;
    
    Widget loadingIndicator = _buildLoadingIndicator(loadingColor);
    
    if (message != null) {
      loadingIndicator = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          loadingIndicator,
          const SizedBox(height: 16),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    if (overlay) {
      return Container(
        color: overlayColor ?? Colors.black.withOpacity(0.5),
        child: Center(child: loadingIndicator),
      );
    }

    return loadingIndicator;
  }

  Widget _buildLoadingIndicator(Color loadingColor) {
    switch (type) {
      case LoadingType.circular:
        return SizedBox(
          width: size ?? 24,
          height: size ?? 24,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
            strokeWidth: 3.0,
          ),
        );
      
      case LoadingType.linear:
        return LinearProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
          backgroundColor: loadingColor.withOpacity(0.2),
        );
      
      case LoadingType.dots:
        return _DotsLoadingIndicator(
          color: loadingColor,
          size: size ?? 8,
        );
    }
  }
}

/// Loading indicator types
enum LoadingType {
  circular,
  linear,
  dots,
}

/// Custom dots loading animation
class _DotsLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;

  const _DotsLoadingIndicator({
    required this.color,
    required this.size,
  });

  @override
  State<_DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<_DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations with delay
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size * 0.25),
              child: Opacity(
                opacity: 0.3 + (0.7 * _animations[index].value),
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    color: widget.color,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

/// Loading state mixin for widgets that need loading functionality
mixin LoadingStateMixin<T extends StatefulWidget> on State<T> {
  bool _isLoading = false;
  String? _loadingMessage;

  bool get isLoading => _isLoading;
  String? get loadingMessage => _loadingMessage;

  void setLoading(bool loading, {String? message}) {
    if (mounted) {
      setState(() {
        _isLoading = loading;
        _loadingMessage = message;
      });
    }
  }

  void showLoading({String? message}) => setLoading(true, message: message);
  void hideLoading() => setLoading(false);

  Widget buildWithLoading({
    required Widget child,
    LoadingType loadingType = LoadingType.circular,
    bool overlay = true,
  }) {
    if (_isLoading) {
      return Stack(
        children: [
          child,
          if (overlay)
            CLoadingWidget.overlay(
              message: _loadingMessage,
            )
          else
            Center(
              child: CLoadingWidget(
                message: _loadingMessage,
                type: loadingType,
              ),
            ),
        ],
      );
    }
    return child;
  }
}

import '../../../domain/entities/tenant/tenant.dart';

/// Tenant data model for API communication
/// Based on MongoDB Tenants schema
class TenantModel {
  final String id;
  final String name;
  final String? address;
  final String? unitId;
  final String createdBy;
  final DateTime createdAt;
  final List<String> mappings;
  final DateTime? updatedAt;

  const TenantModel({
    required this.id,
    required this.name,
    this.address,
    this.unitId,
    required this.createdBy,
    required this.createdAt,
    this.mappings = const [],
    this.updatedAt,
  });

  /// Create TenantModel from JSON
  factory TenantModel.fromJson(Map<String, dynamic> json) {
    return TenantModel(
      id: json['id'] as String? ?? json['_id'] as String,
      name: json['name'] as String? ?? '',
      address: json['address'] as String?,
      unitId: json['unit_id'] as String?,
      createdBy: json['created_by'] as String? ?? '',
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      mappings: (json['mappings'] as List<dynamic>?)?.cast<String>() ?? [],
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert TenantModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'unit_id': unitId,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'mappings': mappings,
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Convert TenantModel to Tenant entity
  Tenant toEntity() {
    return Tenant(
      id: id,
      name: name,
      address: address,
      unitId: unitId,
      createdBy: createdBy,
      createdAt: createdAt,
      mappings: mappings,
      updatedAt: updatedAt,
    );
  }

  /// Create TenantModel from Tenant entity
  factory TenantModel.fromEntity(Tenant entity) {
    return TenantModel(
      id: entity.id,
      name: entity.name,
      address: entity.address,
      unitId: entity.unitId,
      createdBy: entity.createdBy,
      createdAt: entity.createdAt,
      mappings: entity.mappings,
      updatedAt: entity.updatedAt,
    );
  }

  /// Create a copy of TenantModel with updated fields
  TenantModel copyWith({
    String? id,
    String? name,
    String? address,
    String? unitId,
    String? createdBy,
    DateTime? createdAt,
    List<String>? mappings,
    DateTime? updatedAt,
  }) {
    return TenantModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      unitId: unitId ?? this.unitId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      mappings: mappings ?? this.mappings,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TenantModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TenantModel(id: $id, name: $name, address: $address)';
  }
}

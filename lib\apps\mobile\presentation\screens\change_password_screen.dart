import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/domain/use_cases/auth/change_password_use_case.dart';
import '../../../../shared/core/errors/failures.dart';

/// Change Password screen - màn hình đổi mật khẩu
class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;
  
  String? _errorMessage;
  Map<String, List<String>>? _fieldErrors;

  late final ChangePasswordUseCase _changePasswordUseCase;

  @override
  void initState() {
    super.initState();
    _changePasswordUseCase = GetIt.instance<ChangePasswordUseCase>();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4FBFF),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF4FBFF),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF15171A)),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Đổi mật khẩu',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF15171A),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(height: AppDimensions.spacing16),
                        
                        // Error message
                        if (_errorMessage != null) ...[
                          Container(
                            padding: EdgeInsets.all(AppDimensions.paddingS),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFEBEE),
                              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                              border: Border.all(color: const Color(0xFFE03E59)),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: AppTextStyles.caption.copyWith(
                                color: const Color(0xFFE03E59),
                              ),
                            ),
                          ),
                          SizedBox(height: AppDimensions.spacing16),
                        ],

                        // Current Password Field
                        _buildPasswordField(
                          controller: _currentPasswordController,
                          label: 'Mật khẩu hiện tại',
                          obscureText: _obscureCurrentPassword,
                          onToggleVisibility: () {
                            setState(() {
                              _obscureCurrentPassword = !_obscureCurrentPassword;
                            });
                          },
                          fieldKey: 'currentPassword',
                        ),
                        SizedBox(height: AppDimensions.spacing16),

                        // New Password Field
                        _buildPasswordField(
                          controller: _newPasswordController,
                          label: 'Mật khẩu mới',
                          obscureText: _obscureNewPassword,
                          onToggleVisibility: () {
                            setState(() {
                              _obscureNewPassword = !_obscureNewPassword;
                            });
                          },
                          fieldKey: 'newPassword',
                        ),
                        SizedBox(height: AppDimensions.spacing16),

                        // Confirm Password Field
                        _buildPasswordField(
                          controller: _confirmPasswordController,
                          label: 'Xác nhận mật khẩu mới',
                          obscureText: _obscureConfirmPassword,
                          onToggleVisibility: () {
                            setState(() {
                              _obscureConfirmPassword = !_obscureConfirmPassword;
                            });
                          },
                          fieldKey: 'confirmPassword',
                        ),
                        SizedBox(height: AppDimensions.spacing24),

                        // Password Requirements
                        _buildPasswordRequirements(),
                      ],
                    ),
                  ),
                ),

                // Change Password Button
                SizedBox(height: AppDimensions.spacing16),
                _buildChangePasswordButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required String fieldKey,
  }) {
    final fieldError = _fieldErrors?[fieldKey];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2329),
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          decoration: InputDecoration(
            hintText: 'Nhập $label',
            hintStyle: AppTextStyles.caption.copyWith(
              color: const Color(0xFF8F959E),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                obscureText ? Icons.visibility_off : Icons.visibility,
                color: const Color(0xFF8F959E),
              ),
              onPressed: onToggleVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE5E6E7)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: const BorderSide(color: Color(0xFFE03E59)),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '$label không được để trống';
            }
            return null;
          },
        ),
        if (fieldError != null && fieldError.isNotEmpty) ...[
          SizedBox(height: AppDimensions.spacing4),
          ...fieldError.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Text(
              error,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFFE03E59),
              ),
            ),
          )),
        ],
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: const Color(0xFFE5E6E7)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Yêu cầu mật khẩu:',
            style: AppTextStyles.caption.copyWith(
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2329),
            ),
          ),
          SizedBox(height: AppDimensions.spacing8),
          _buildRequirementItem('Ít nhất 8 ký tự'),
          _buildRequirementItem('Có ít nhất 1 chữ hoa'),
          _buildRequirementItem('Có ít nhất 1 chữ thường'),
          _buildRequirementItem('Có ít nhất 1 số'),
          _buildRequirementItem('Có ít nhất 1 ký tự đặc biệt'),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle_outline,
            size: 16,
            color: Color(0xFF8F959E),
          ),
          SizedBox(width: AppDimensions.spacing8),
          Text(
            text,
            style: AppTextStyles.caption.copyWith(
              color: const Color(0xFF8F959E),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChangePasswordButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleChangePassword,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingM),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 0,
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              'Đổi mật khẩu',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
    );
  }

  Future<void> _handleChangePassword() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
      _fieldErrors = null;
    });

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final params = ChangePasswordParams(
        currentPassword: _currentPasswordController.text.trim(),
        newPassword: _newPasswordController.text.trim(),
        confirmPassword: _confirmPasswordController.text.trim(),
      );

      final result = await _changePasswordUseCase(params);

      result.fold(
        (failure) {
          setState(() {
            if (failure is ValidationFailure && failure.fieldErrors != null) {
              _fieldErrors = failure.fieldErrors;
              _errorMessage = failure.message;
            } else {
              _errorMessage = failure.message;
            }
          });
        },
        (success) {
          // Show success message and go back
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Đổi mật khẩu thành công'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          }
        },
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không mong muốn: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

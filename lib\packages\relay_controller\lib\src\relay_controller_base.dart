/// Abstract base class for relay controllers.
///
/// This class defines the common interface that all relay controllers must implement.
/// It provides two main methods for controlling relay states: [triggerOn] and [triggerOff].
abstract class RelayController {
  /// Unique device identifier
  final String deviceId;

  /// Device name/description
  final String deviceName;

  /// Creates a relay controller with device identification.
  RelayController({
    required this.deviceId,
    this.deviceName = 'Relay Device',
  });

  /// Triggers the relay to turn ON.
  ///
  /// This method should implement the specific communication protocol
  /// to send an "ON" command to the relay device.
  ///
  /// Throws [RelayControllerException] if the operation fails.
  Future<void> triggerOn();

  /// Triggers the relay to turn OFF.
  ///
  /// This method should implement the specific communication protocol
  /// to send an "OFF" command to the relay device.
  ///
  /// Throws [RelayControllerException] if the operation fails.
  Future<void> triggerOff();

  /// Registers the device with a server.
  ///
  /// This method should be implemented by subclasses that support
  /// device registration with a central server.
  ///
  /// Throws [RelayControllerException] if registration fails.
  Future<void> registerDevice() async {
    // Default implementation does nothing
    // Subclasses should override this if they support registration
  }

  /// Gets the current status of the relay.
  ///
  /// Returns true if the relay is ON, false if OFF, null if unknown.
  Future<bool?> getStatus() async {
    // Default implementation returns null (unknown)
    // Subclasses should override this if they support status reading
    return null;
  }

  /// Disposes any resources used by the controller.
  ///
  /// This method should be called when the controller is no longer needed
  /// to properly clean up connections and resources.
  Future<void> dispose() async {
    // Default implementation does nothing
    // Subclasses should override this if they need cleanup
  }
}

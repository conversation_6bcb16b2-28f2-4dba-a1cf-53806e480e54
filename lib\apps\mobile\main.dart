import 'package:flutter/material.dart';
import '../../core/config/flavor_config.dart';
import '../../core/config/app_config.dart';
import '../../core/constants/app_colors.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/login_screen.dart';
import 'presentation/screens/tenants_screen.dart';
import 'presentation/screens/forgot_password/enter_email_screen.dart';
import 'presentation/screens/forgot_password/enter_otp_screen.dart';
import 'presentation/screens/forgot_password/confirm_password_screen.dart';
import 'presentation/screens/forgot_password/successfully_screen.dart';
import 'presentation/screens/dashboard.dart';
import 'presentation/screens/tenant_create_screen.dart';
import 'presentation/screens/tools_screen/tools_screen.dart';
import 'presentation/screens/main_screen.dart';

void main() {
  // Khởi tạo flavor config cho mobile app
  FlavorConfig.initialize(
    flavor: Flavor.mobile,
    appName: 'C-CAM Mobile Temp',
    appId: 'com.ccam.mobile.temp',
    appVersion: '1.0.0',
    isDebug: true,
    values: AppConfig.mobileConfig,
  );

  runApp(const MobileApp());
}

class MobileApp extends StatelessWidget {
  const MobileApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: FlavorConfig.instance.appName,
      debugShowCheckedModeBanner: FlavorConfig.instance.isDebug,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primary,
          primary: AppColors.primary,
          surface: AppColors.background,
        ),
        useMaterial3: true,
        fontFamily: 'System',
        bottomSheetTheme: const BottomSheetThemeData(
          dragHandleColor: Colors.white,
        ),
      ),
      initialRoute: '/',
      routes: _buildRoutes(),
    );
  }

  Map<String, WidgetBuilder> _buildRoutes() {
    return {
      '/': (context) => const SplashScreen(),
      '/login': (context) => const LoginScreen(),
      '/forgot-password': (context) => const EnterEmailScreen(),
      '/enter-otp': (context) => const EnterOtpScreen(),
      '/confirm-password': (context) => const ConfirmPasswordScreen(),
      '/successfully': (context) => const SuccessfullyScreen(),
      '/tenants': (context) => const TenantsScreen(),
      '/dashboard': (context) => const DashboardScreen(),
      '/tenant-create': (context) => const TenantCreateScreen(),
      '/tools': (context) => const ToolsScreen(),
      '/main': (context) => const MainScreen(),
    };
  }
}
import 'package:flutter/material.dart';
import 'package:relay_controller/relay_controller.dart';
import 'package:bluetooth_classic/models/device.dart' as bc;
import 'package:flutter_blue_classic/flutter_blue_classic.dart' as fbc;

/// Example demonstrating how to use the modern Bluetooth controllers
/// for relay control with HC-05/HC-06 modules.
class BluetoothRelayExample extends StatefulWidget {
  const BluetoothRelayExample({super.key});

  @override
  State<BluetoothRelayExample> createState() => _BluetoothRelayExampleState();
}

class _BluetoothRelayExampleState extends State<BluetoothRelayExample> {
  ModernBluetoothController? _modernController;
  FlutterBlueClassicController? _classicController;
  
  List<bc.Device> _modernDevices = [];
  List<fbc.BluetoothDevice> _classicDevices = [];
  
  String _status = 'Disconnected';
  String _selectedController = 'modern'; // 'modern' or 'classic'

  @override
  void initState() {
    super.initState();
    _loadDevices();
  }

  Future<void> _loadDevices() async {
    try {
      // Load devices for both controllers
      final modernDevices = await ModernBluetoothController.getAvailableDevices();
      final classicDevices = await FlutterBlueClassicController.getAvailableDevices();
      
      setState(() {
        _modernDevices = modernDevices;
        _classicDevices = classicDevices;
      });
    } catch (e) {
      _showError('Failed to load devices: $e');
    }
  }

  Future<void> _connectModern(bc.Device device) async {
    try {
      setState(() => _status = 'Connecting...');
      
      _modernController = ModernBluetoothController(
        deviceId: 'relay-${device.address}',
        deviceAddress: device.address,
        deviceName: device.name ?? 'Unknown Device',
        onCommand: 'ON\n',
        offCommand: 'OFF\n',
      );
      
      await _modernController!.connect();
      
      setState(() => _status = 'Connected to ${device.name}');
    } catch (e) {
      setState(() => _status = 'Connection failed');
      _showError('Connection failed: $e');
    }
  }

  Future<void> _connectClassic(fbc.BluetoothDevice device) async {
    try {
      setState(() => _status = 'Connecting...');
      
      _classicController = FlutterBlueClassicController(
        deviceId: 'relay-${device.address}',
        deviceAddress: device.address,
        deviceName: device.name ?? 'Unknown Device',
        onCommand: 'ON\n',
        offCommand: 'OFF\n',
        usesFineLocation: false,
      );
      
      await _classicController!.connect();
      
      setState(() => _status = 'Connected to ${device.name}');
    } catch (e) {
      setState(() => _status = 'Connection failed');
      _showError('Connection failed: $e');
    }
  }

  Future<void> _disconnect() async {
    try {
      if (_modernController != null) {
        await _modernController!.dispose();
        _modernController = null;
      }
      
      if (_classicController != null) {
        await _classicController!.dispose();
        _classicController = null;
      }
      
      setState(() => _status = 'Disconnected');
    } catch (e) {
      _showError('Disconnect failed: $e');
    }
  }

  Future<void> _triggerOn() async {
    try {
      if (_modernController != null) {
        await _modernController!.triggerOn();
      } else if (_classicController != null) {
        await _classicController!.triggerOn();
      }
      _showSuccess('Relay turned ON');
    } catch (e) {
      _showError('Failed to turn ON: $e');
    }
  }

  Future<void> _triggerOff() async {
    try {
      if (_modernController != null) {
        await _modernController!.triggerOff();
      } else if (_classicController != null) {
        await _classicController!.triggerOff();
      }
      _showSuccess('Relay turned OFF');
    } catch (e) {
      _showError('Failed to turn OFF: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  bool get _isConnected => 
    (_modernController?.isConnected ?? false) || 
    (_classicController?.isConnected ?? false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Relay Control'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Controller Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Controller Type:', style: TextStyle(fontWeight: FontWeight.bold)),
                    RadioListTile<String>(
                      title: const Text('Modern Bluetooth (bluetooth_classic)'),
                      subtitle: const Text('Simple, reliable, recommended'),
                      value: 'modern',
                      groupValue: _selectedController,
                      onChanged: (value) => setState(() => _selectedController = value!),
                    ),
                    RadioListTile<String>(
                      title: const Text('Flutter Blue Classic (flutter_blue_classic)'),
                      subtitle: const Text('Feature-rich, advanced scanning'),
                      value: 'classic',
                      groupValue: _selectedController,
                      onChanged: (value) => setState(() => _selectedController = value!),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Icon(
                      _isConnected ? Icons.bluetooth_connected : Icons.bluetooth_disabled,
                      color: _isConnected ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text('Status: $_status', style: const TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Device List
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Paired Devices:', style: TextStyle(fontWeight: FontWeight.bold)),
                          IconButton(
                            icon: const Icon(Icons.refresh),
                            onPressed: _loadDevices,
                          ),
                        ],
                      ),
                      Expanded(
                        child: _selectedController == 'modern' 
                          ? _buildModernDeviceList()
                          : _buildClassicDeviceList(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Control Buttons
            if (_isConnected) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _triggerOn,
                      icon: const Icon(Icons.power),
                      label: const Text('Turn ON'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _triggerOff,
                      icon: const Icon(Icons.power_off),
                      label: const Text('Turn OFF'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _disconnect,
                icon: const Icon(Icons.bluetooth_disabled),
                label: const Text('Disconnect'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModernDeviceList() {
    if (_modernDevices.isEmpty) {
      return const Center(child: Text('No paired devices found'));
    }
    
    return ListView.builder(
      itemCount: _modernDevices.length,
      itemBuilder: (context, index) {
        final device = _modernDevices[index];
        return ListTile(
          leading: const Icon(Icons.bluetooth),
          title: Text(device.name ?? 'Unknown Device'),
          subtitle: Text(device.address),
          trailing: ElevatedButton(
            onPressed: _isConnected ? null : () => _connectModern(device),
            child: const Text('Connect'),
          ),
        );
      },
    );
  }

  Widget _buildClassicDeviceList() {
    if (_classicDevices.isEmpty) {
      return const Center(child: Text('No paired devices found'));
    }
    
    return ListView.builder(
      itemCount: _classicDevices.length,
      itemBuilder: (context, index) {
        final device = _classicDevices[index];
        return ListTile(
          leading: const Icon(Icons.bluetooth),
          title: Text(device.name ?? 'Unknown Device'),
          subtitle: Text(device.address),
          trailing: ElevatedButton(
            onPressed: _isConnected ? null : () => _connectClassic(device),
            child: const Text('Connect'),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _disconnect();
    super.dispose();
  }
}

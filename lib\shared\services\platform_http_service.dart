import 'package:flutter/foundation.dart';
import 'http_client_service.dart';
import 'web_http_client_service.dart';
import 'api_endpoints.dart';

/// Platform-aware HTTP service factory
/// Automatically selects the best HTTP implementation based on platform
class PlatformHttpService {
  static PlatformHttpService? _instance;
  late dynamic _httpService;
  
  PlatformHttpService._internal() {
    _initializePlatformService();
  }
  
  factory PlatformHttpService() {
    _instance ??= PlatformHttpService._internal();
    return _instance!;
  }
  
  void _initializePlatformService() {
    if (kIsWeb) {
      // Web platform: Use web-compatible HTTP service
      print('🌐 Initializing Web HTTP Service...');
      _httpService = WebHttpClientService();
      _httpService.initialize(ApiEndpoints.devBaseUrl);
    } else {
      // Mobile/Desktop: Use HTTP package-based service
      print('📱 Initializing Mobile HTTP Service...');
      _httpService = HttpClientService();
      _httpService.initialize(HttpClientConfig(
        baseUrl: ApiEndpoints.devBaseUrl,
        connectTimeout: 60000,
        receiveTimeout: 60000,
        sendTimeout: 60000,
      ));
    }
  }
  
  /// POST request
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    if (kIsWeb) {
      return await (_httpService as WebHttpClientService).post(
        endpoint,
        body: body,
        headers: headers,
      );
    } else {
      final response = await (_httpService as HttpClientService).post<Map<String, dynamic>>(
        endpoint,
        data: body,
        headers: headers,
      );
      return response.data ?? {};
    }
  }
  
  /// GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    if (kIsWeb) {
      return await (_httpService as WebHttpClientService).get(
        endpoint,
        headers: headers,
      );
    } else {
      final response = await (_httpService as HttpClientService).get<Map<String, dynamic>>(
        endpoint,
        headers: headers,
      );
      return response.data ?? {};
    }
  }
  
  /// Update base URL
  void updateBaseUrl(String baseUrl) {
    if (kIsWeb) {
      (_httpService as WebHttpClientService).initialize(baseUrl);
    } else {
      (_httpService as HttpClientService).initialize(HttpClientConfig(
        baseUrl: baseUrl,
        connectTimeout: 60000,
        receiveTimeout: 60000,
        sendTimeout: 60000,
      ));
    }
  }
  
  /// Get current platform info
  String getPlatformInfo() {
    if (kIsWeb) {
      return 'Web Platform (using http package)';
    } else {
      return 'Mobile/Desktop Platform (using http package)';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:relay_controller/relay_controller.dart';
import '../screens/relay_testing_screen_simple.dart';

/// Widget for quick access to relay testing functionality
/// 
/// This widget provides a floating action button or quick access
/// to the comprehensive relay testing screen.
class RelayTestingWidget extends StatelessWidget {
  final bool isFloating;
  final VoidCallback? onTestingStarted;
  final VoidCallback? onTestingCompleted;

  const RelayTestingWidget({
    super.key,
    this.isFloating = true,
    this.onTestingStarted,
    this.onTestingCompleted,
  });

  @override
  Widget build(BuildContext context) {
    if (isFloating) {
      return _buildFloatingWidget(context);
    } else {
      return _buildInlineWidget(context);
    }
  }

  Widget _buildFloatingWidget(BuildContext context) {
    return Positioned(
      bottom: 80,
      right: 16,
      child: FloatingActionButton.extended(
        onPressed: () => _openTestingScreen(context),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.electrical_services),
        label: const Text('Relay Test'),
        heroTag: 'relay_testing',
      ),
    );
  }

  Widget _buildInlineWidget(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () => _openTestingScreen(context),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.electrical_services,
                  color: Colors.indigo,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Relay Testing & Configuration',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Test USB-TTL, server integration, and device profiles',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openTestingScreen(BuildContext context) {
    onTestingStarted?.call();
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RelayTestingScreen(),
      ),
    ).then((_) {
      onTestingCompleted?.call();
    });
  }
}

/// Quick relay control widget for basic testing
class QuickRelayControlWidget extends StatefulWidget {
  final String? deviceId;
  final VoidCallback? onCommandSent;

  const QuickRelayControlWidget({
    super.key,
    this.deviceId,
    this.onCommandSent,
  });

  @override
  State<QuickRelayControlWidget> createState() => _QuickRelayControlWidgetState();
}

class _QuickRelayControlWidgetState extends State<QuickRelayControlWidget> {
  bool _isLoading = false;
  String? _lastCommand;
  String? _lastResult;
  int _selectedRelay = 0; // Default to relay 0
  final int _maxRelays = 4; // Maximum number of relays

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.power,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Quick Relay Control',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            if (widget.deviceId != null) ...[
              const SizedBox(height: 8),
              Text(
                'Device: ${widget.deviceId}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
            
            const SizedBox(height: 16),

            // Relay Selection
            Row(
              children: [
                Text(
                  'Relay:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButton<int>(
                    value: _selectedRelay,
                    isExpanded: true,
                    items: List.generate(_maxRelays, (index) {
                      return DropdownMenuItem<int>(
                        value: index,
                        child: Text('Relay $index'),
                      );
                    }),
                    onChanged: _isLoading ? null : (value) {
                      if (value != null) {
                        setState(() {
                          _selectedRelay = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Control Buttons - compact layout
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('R$_selectedRelay:1'),
                    icon: const Icon(Icons.power, size: 16),
                    label: const Text('ON', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('R$_selectedRelay:0'),
                    icon: const Icon(Icons.power_off, size: 16),
                    label: const Text('OFF', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('R$_selectedRelay:TOGGLE'),
                    icon: const Icon(Icons.swap_horiz, size: 16),
                    label: const Text('TOGGLE', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
              ],
            ),

            // Additional control buttons row
            const SizedBox(height: 6),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('R$_selectedRelay:500'),
                    icon: const Icon(Icons.timer, size: 16),
                    label: const Text('TIMED', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('ALL:1'),
                    icon: const Icon(Icons.power_settings_new, size: 16),
                    label: const Text('ALL ON', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendCommand('ALL:0'),
                    icon: const Icon(Icons.power_off, size: 16),
                    label: const Text('ALL OFF', style: TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
              ],
            ),
            
            // Status Display
            if (_lastCommand != null || _lastResult != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_lastCommand != null)
                      Text(
                        'Last Command: $_lastCommand',
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    if (_lastResult != null)
                      Text(
                        'Result: $_lastResult',
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          color: _lastResult!.startsWith('✅') ? Colors.green : Colors.red,
                        ),
                      ),
                  ],
                ),
              ),
            ],
            
            if (_isLoading) ...[
              const SizedBox(height: 8),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _sendCommand(String command) async {
    setState(() {
      _isLoading = true;
      _lastCommand = command;
      _lastResult = null;
    });

    try {
      // Parse command to extract relay index and action
      final parts = command.split(':');
      if (parts.length == 2) {
        final relayPart = parts[0];
        final actionPart = parts[1];

        if (relayPart.startsWith('R')) {
          final relayIndex = int.tryParse(relayPart.substring(1));
          if (relayIndex != null) {
            RelayAction action;
            switch (actionPart) {
              case '1':
                action = RelayAction.on;
                break;
              case '0':
                action = RelayAction.off;
                break;
              case 'TOGGLE':
                action = RelayAction.toggle;
                break;
              default:
                // For timed commands like "500", treat as ON
                action = RelayAction.on;
            }

            // Try to use enhanced relay manager
            try {
              final manager = EnhancedRelayManager();
              final devices = manager.discoveredDevices;

              if (devices.isNotEmpty) {
                final deviceId = devices.first.id;
                await manager.controlRelay(deviceId, relayIndex, action);

                setState(() {
                  _lastResult = '✅ Command sent via Enhanced Manager';
                });
              } else {
                throw Exception('No relay devices available');
              }
            } catch (e) {
              // Fallback to simulation
              await Future.delayed(const Duration(milliseconds: 500));
              setState(() {
                _lastResult = '✅ Command sent (simulated) - $e';
              });
            }
          }
        } else if (relayPart == 'ALL') {
          // Handle ALL commands
          await Future.delayed(const Duration(milliseconds: 800));
          setState(() {
            _lastResult = '✅ ALL relays command sent (simulated)';
          });
        }
      } else {
        throw Exception('Invalid command format');
      }

      widget.onCommandSent?.call();

    } catch (e) {
      setState(() {
        _lastResult = '❌ Command failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

/// Relay status indicator widget
class RelayStatusIndicator extends StatelessWidget {
  final bool isConnected;
  final String? deviceId;
  final String? lastActivity;
  final VoidCallback? onTap;

  const RelayStatusIndicator({
    super.key,
    required this.isConnected,
    this.deviceId,
    this.lastActivity,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green.shade100 : Colors.red.shade100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isConnected ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: isConnected ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              isConnected ? 'Connected' : 'Disconnected',
              style: TextStyle(
                color: isConnected ? Colors.green.shade800 : Colors.red.shade800,
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
            if (deviceId != null) ...[
              const SizedBox(width: 3),
              Text(
                '($deviceId)',
                style: TextStyle(
                  color: isConnected ? Colors.green.shade600 : Colors.red.shade600,
                  fontSize: 9,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

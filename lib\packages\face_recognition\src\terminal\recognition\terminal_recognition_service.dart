import 'dart:typed_data';

import 'package:flutter/foundation.dart';

import '../../core/models/face_recognition_config.dart';
import '../../shared/services/online_recognition_service.dart';
import '../../shared/services/offline_recognition_service.dart';
import '../terminal_face_system.dart';

/// Terminal-specific recognition service
class TerminalRecognitionService {
  final FaceRecognitionConfig _config;
  
  OnlineRecognitionService? _onlineService;
  OfflineRecognitionService? _offlineService;
  
  bool _isInitialized = false;
  bool _isOnlineMode = true;
  DateTime _lastRecognitionTime = DateTime.now();
  
  TerminalRecognitionService({required FaceRecognitionConfig config}) : _config = config;
  
  bool get isInitialized => _isInitialized;
  
  bool get isThrottled {
    final now = DateTime.now();
    final timeSinceLastRecognition = now.difference(_lastRecognitionTime);
    return timeSinceLastRecognition < _config.recognitionThrottle;
  }
  
  /// Initialize the terminal recognition service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🖥️ Initializing TerminalRecognitionService');
      }
      
      // Initialize online service
      _onlineService = OnlineRecognitionService(config: _config);
      await _onlineService!.initialize();
      
      // Initialize offline service
      _offlineService = OfflineRecognitionService(config: _config);
      await _offlineService!.initialize();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ TerminalRecognitionService initialized');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize TerminalRecognitionService: $e');
      }
      rethrow;
    }
  }
  
  /// Recognize face with terminal-specific logic
  Future<TerminalRecognitionResult?> recognizeFace(
    Uint8List faceImageBytes, {
    required bool isOnline,
  }) async {
    if (!_isInitialized) {
      throw StateError('TerminalRecognitionService not initialized');
    }
    
    if (isThrottled) {
      if (kDebugMode) {
        print('⏸️ Terminal recognition throttled');
      }
      return null;
    }
    
    _lastRecognitionTime = DateTime.now();
    
    try {
      // Try online first if available
      if (isOnline && _onlineService != null) {
        final onlineResult = await _onlineService!.recognizeFace(faceImageBytes);
        if (onlineResult != null) {
          return _convertToTerminalResult(onlineResult, 'online');
        }
      }
      
      // Fallback to offline
      if (_offlineService != null) {
        final offlineResult = await _offlineService!.recognizeFace(faceImageBytes);
        if (offlineResult != null) {
          return _convertToTerminalResult(offlineResult, 'offline');
        }
      }
      
      return null;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Terminal recognition failed: $e');
      }
      return null;
    }
  }
  
  /// Convert recognition result to terminal format
  TerminalRecognitionResult _convertToTerminalResult(
    dynamic result,
    String source,
  ) {
    // Determine access level based on user data
    AccessLevel accessLevel = AccessLevel.user;
    bool hasAccess = true;
    
    // This would typically come from user metadata
    final metadata = result.metadata as Map<String, dynamic>? ?? {};
    final accessLevelString = metadata['access_level'] as String?;
    
    switch (accessLevelString?.toLowerCase()) {
      case 'admin':
        accessLevel = AccessLevel.admin;
        break;
      case 'user':
        accessLevel = AccessLevel.user;
        break;
      case 'guest':
        accessLevel = AccessLevel.guest;
        break;
      case 'denied':
        accessLevel = AccessLevel.denied;
        hasAccess = false;
        break;
      default:
        accessLevel = AccessLevel.user;
    }
    
    return TerminalRecognitionResult(
      userId: result.userId,
      userName: result.userName,
      confidence: result.confidence,
      source: source,
      accessLevel: accessLevel,
      hasAccess: hasAccess,
      timestamp: result.timestamp,
      metadata: metadata,
    );
  }
  
  /// Set online mode
  void setOnlineMode(bool isOnline) {
    _isOnlineMode = isOnline;
    
    if (kDebugMode) {
      print('🔄 Terminal recognition mode: ${isOnline ? "Online" : "Offline"}');
    }
  }
  
  /// Dispose of resources
  Future<void> dispose() async {
    await _onlineService?.dispose();
    await _offlineService?.dispose();
    
    _onlineService = null;
    _offlineService = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ TerminalRecognitionService disposed');
    }
  }
}

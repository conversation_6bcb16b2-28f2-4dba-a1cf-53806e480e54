library;

/// Configurable API endpoints
///
/// This class now uses the flexible configuration system instead of hardcoded values.
/// All API endpoints can be configured through environment variables, configuration files,
/// or runtime changes through the admin interface.

import '../core/config/config_helper.dart';

class ApiEndpoints {
  // Private constructor để ngăn việc khởi tạo instance
  ApiEndpoints._();

  // ========== BASE CONFIGURATION ==========
  /// Base URL cho development
  static String get devBaseUrl => ConfigHelper.getValue('network.dev_base_url', 'http://10.161.80.12');
  static String get devBaseUrlFallback => ConfigHelper.getValue('network.dev_base_url_fallback', 'http://10.161.80.12/');

  /// Base URL cho staging
  static String get stagingBaseUrl => ConfigHelper.getValue('network.staging_base_url', 'http://10.161.80.12');

  /// Base URL cho production
  static String get prodBaseUrl => ConfigHelper.getValue('network.prod_base_url', 'http://10.161.80.12');

  /// API version
  static String get apiVersion => ConfigHelper.getValue('network.api_version', '/api/v3.1');

  /// Lấy base URL hiện tại dựa trên environment
  static String get currentBaseUrl {
    // Use configurable base URL
    return ConfigHelper.getValue('network.current_base_url', devBaseUrl);
  }

  /// Full API base URL
  static String get fullApiBaseUrl => '$currentBaseUrl$apiVersion';

  // ========== AUTHENTICATION ENDPOINTS ==========
  /// Đăng nhập
  static const String login = '/identity/login';
  
  /// Đăng ký
  static const String register = '/auth/register';
  
  /// Đăng xuất
  static const String logout = '/auth/logout';
  
  /// Refresh token
  static const String refreshToken = '/identity/refresh-token';
  
  /// Lấy thông tin user hiện tại
  static const String me = '/auth/me';
  
  /// Cập nhật profile
  static const String updateProfile = '/auth/profile';
  
  /// Đổi mật khẩu
  static const String changePassword = '/auth/change-password';
  
  /// Quên mật khẩu
  static const String forgotPassword = '/auth/forgot-password';
  
  /// Reset mật khẩu
  static const String resetPassword = '/auth/reset-password';
  
  /// Xác thực email
  static const String verifyEmail = '/auth/verify-email';
  
  /// Gửi lại email xác thực
  static const String resendVerification = '/auth/resend-verification';

  /// Switch tenant context
  static const String switchContext = '/identity/switch-context';

  // ========== USER MANAGEMENT ENDPOINTS ==========
  /// Danh sách users
  static const String users = '/users';
  
  /// Chi tiết user
  static String userDetail(String userId) => '/users/$userId';
  
  /// Tạo user mới
  static const String createUser = '/users';
  
  /// Cập nhật user
  static String updateUser(String userId) => '/users/$userId';
  
  /// Xóa user
  static String deleteUser(String userId) => '/users/$userId';
  
  /// Upload avatar
  static const String uploadAvatar = '/users/avatar';

  // ========== TENANT MANAGEMENT ENDPOINTS ==========
  /// Get user tenants with pagination
  static const String userTenants = '/entity-permissions/user-tenants';

  /// Danh sách tenants
  static const String tenants = '/tenants';

  /// Chi tiết tenant
  static String tenantDetail(String tenantId) => '/tenants/$tenantId';

  /// Tạo tenant mới
  static const String createTenant = '/tenants';

  /// Cập nhật tenant
  static String updateTenant(String tenantId) => '/tenants/$tenantId';

  /// Xóa tenant
  static String deleteTenant(String tenantId) => '/tenants/$tenantId';

  // ========== UNIT MANAGEMENT ENDPOINTS ==========
  /// Danh sách units
  static const String units = '/units';

  /// Chi tiết unit
  static String unitDetail(String unitId) => '/units/$unitId';

  /// Tạo unit mới
  static const String createUnit = '/units';

  /// Cập nhật unit
  static String updateUnit(String unitId) => '/units/$unitId';

  /// Xóa unit
  static String deleteUnit(String unitId) => '/units/$unitId';

  // ========== ROLES ENDPOINTS ==========
  /// Danh sách roles
  static const String roles = '/roles';

  /// Chi tiết role
  static String roleDetail(String roleId) => '/roles/$roleId';

  /// Tạo role mới
  static const String createRole = '/roles';

  /// Cập nhật role
  static String updateRole(String roleId) => '/roles/$roleId';

  /// Xóa role
  static String deleteRole(String roleId) => '/roles/$roleId';

  // ========== FACE DETECTION ENDPOINTS ==========
  /// Đăng ký khuôn mặt (upload multiple images)
  static const String registerFace = '/face/upload-multiple';
  /// Xác thực khuôn mặt
  static const String verifyFace = '/face/verify';

  /// Danh sách khuôn mặt đã đăng ký
  static const String registeredFaces = '/face/registered';

  /// Xóa khuôn mặt đã đăng ký
  static String deleteFace(String faceId) => '/face/$faceId';

  /// Cập nhật thông tin khuôn mặt
  static String updateFace(String faceId) => '/face/$faceId';

  /// Lịch sử nhận diện khuôn mặt
  static const String faceRecognitionHistory = '/face/history';

  // ========== STORAGE/FILE ENDPOINTS ==========
  /// Stream/download image by imageUrl
  static String imageStream(String imageUrl) => '/storage/files/$imageUrl/stream';

  // ========== DEVICE MANAGEMENT ENDPOINTS ==========
  /// Đăng ký thiết bị (secure API)
  static const String registerDevice = '/api/device/register';

  /// Đăng ký thiết bị (legacy API for backward compatibility)
  static const String registerDeviceLegacy = '/register';

  /// Danh sách thiết bị
  static const String devices = '/devices';

  /// Chi tiết thiết bị
  static String deviceDetail(String deviceId) => '/devices/$deviceId';

  /// Cập nhật thiết bị
  static String updateDevice(String deviceId) => '/devices/$deviceId';

  /// Xóa thiết bị
  static String deleteDevice(String deviceId) => '/devices/$deviceId';

  /// Lịch sử hoạt động thiết bị
  static String deviceHistory(String deviceId) => '/devices/$deviceId/history';

  /// Ping thiết bị
  static String pingDevice(String deviceId) => '/devices/$deviceId/ping';

  /// Refresh device token
  static const String refreshDeviceToken = '/api/device/refresh';

  /// Revoke device credentials
  static const String revokeDevice = '/api/device/revoke';

  // ========== TERMINAL ENDPOINTS ==========
  /// Danh sách terminal sessions
  static const String terminalSessions = '/terminal/sessions';
  
  /// Tạo terminal session mới
  static const String createTerminalSession = '/terminal/sessions';
  
  /// Chi tiết terminal session
  static String terminalSessionDetail(String sessionId) => '/terminal/sessions/$sessionId';
  
  /// Thực thi lệnh terminal
  static String executeCommand(String sessionId) => '/terminal/sessions/$sessionId/execute';
  
  /// Lịch sử lệnh terminal
  static String terminalHistory(String sessionId) => '/terminal/sessions/$sessionId/history';
  
  /// Đóng terminal session
  static String closeTerminalSession(String sessionId) => '/terminal/sessions/$sessionId/close';

  // ========== FILE MANAGEMENT ENDPOINTS ==========
  /// Upload file
  static const String uploadFile = '/files/upload';
  
  /// Download file
  static String downloadFile(String fileId) => '/files/$fileId/download';
  
  /// Danh sách files
  static const String files = '/files';
  
  /// Chi tiết file
  static String fileDetail(String fileId) => '/files/$fileId';
  
  /// Xóa file
  static String deleteFile(String fileId) => '/files/$fileId';

  // ========== SETTINGS ENDPOINTS ==========
  /// Lấy cài đặt ứng dụng
  static const String appSettings = '/settings/app';
  
  /// Cập nhật cài đặt ứng dụng
  static const String updateAppSettings = '/settings/app';
  
  /// Lấy cài đặt face detection
  static const String faceDetectionSettings = '/settings/face-detection';
  
  /// Cập nhật cài đặt face detection
  static const String updateFaceDetectionSettings = '/settings/face-detection';
  
  /// Lấy cài đặt security
  static const String securitySettings = '/settings/security';
  
  /// Cập nhật cài đặt security
  static const String updateSecuritySettings = '/settings/security';

  // ========== RELAY CONTROLLER ENDPOINTS ==========
  /// Điều khiển relay
  static const String relayControl = '/relay/control';

  /// Trạng thái relay
  static const String relayStatus = '/relay/status';

  /// Bật relay
  static const String relayOn = '/relay/on';

  /// Tắt relay
  static const String relayOff = '/relay/off';

  /// Điều khiển relay theo device ID
  static String deviceRelayOn(String deviceId) => '/devices/$deviceId/on';
  static String deviceRelayOff(String deviceId) => '/devices/$deviceId/off';
  static String deviceRelayStatus(String deviceId) => '/devices/$deviceId/status';

  // ========== DEVICE NAMING SYSTEM ENDPOINTS ==========
  /// Tạo terminal ID mới
  static const String generateTerminalId = '/api/naming/generate-terminal-id';

  /// Tạo relay device ID
  static const String generateRelayId = '/api/naming/generate-relay-id';

  /// Validate device naming format
  static const String validateDeviceNaming = '/api/naming/validate';

  /// Get naming summary for terminal
  static String namingSummary(String terminalId) => '/api/naming/summary/$terminalId';

  /// Get devices grouped by terminal
  static const String devicesGrouped = '/api/devices/grouped';

  // ========== FACE RECOGNITION ENDPOINTS ==========
  /// Face recognition (secure API)
  static const String faceRecognize = '/api/face/recognize';

  /// Get face recognition settings
  static const String faceSettings = '/api/face/settings';

  /// Update face recognition settings
  static const String updateFaceSettings = '/api/face/settings';

  /// Get pending face recognitions
  static const String facePending = '/api/face/pending';

  /// Approve/reject face recognition
  static String faceApprove(String recognitionId) => '/api/face/approve/$recognitionId';

  /// Get saved face images
  static const String faceImages = '/api/face/images';

  // ========== SECURE MESSAGING ENDPOINTS ==========
  /// Secure message endpoint (requires JWT + HMAC)
  static const String secureMessage = '/api/message';

  /// Plain text message endpoint
  static const String plainMessage = '/api/message/plain';

  // ========== NOTIFICATION ENDPOINTS ==========
  /// Danh sách notifications
  static const String notifications = '/notifications';
  
  /// Đánh dấu notification đã đọc
  static String markNotificationRead(String notificationId) => '/notifications/$notificationId/read';
  
  /// Đánh dấu tất cả notifications đã đọc
  static const String markAllNotificationsRead = '/notifications/read-all';
  
  /// Xóa notification
  static String deleteNotification(String notificationId) => '/notifications/$notificationId';

  // ========== ANALYTICS ENDPOINTS ==========
  /// Dashboard analytics
  static const String dashboardAnalytics = '/analytics/dashboard';
  
  /// Face recognition analytics
  static const String faceRecognitionAnalytics = '/analytics/face-recognition';
  
  /// Device usage analytics
  static const String deviceUsageAnalytics = '/analytics/device-usage';
  
  /// User activity analytics
  static const String userActivityAnalytics = '/analytics/user-activity';

  // ========== HELPER METHODS ==========
  
  /// Tạo full URL với base URL và version
  static String createFullUrl(String baseUrl, String endpoint) {
    return '$baseUrl$apiVersion$endpoint';
  }
  
  /// Lấy base URL theo environment
  static String getBaseUrlByEnvironment(String environment) {
    switch (environment.toLowerCase()) {
      case 'development':
      case 'dev':
        return devBaseUrl;
      case 'staging':
      case 'stage':
        return stagingBaseUrl;
      case 'production':
      case 'prod':
        return prodBaseUrl;
      default:
        return devBaseUrl;
    }
  }

  /// Lấy base URL theo AppEnvironment enum (for dynamic base URL configuration)
  static String getBaseUrlByAppEnvironment(dynamic environment) {
    // Import AppEnvironment from config
    if (environment.toString().contains('AppEnvironment.development')) {
      return devBaseUrl; // Use devBaseUrl instead of localhost
    } else if (environment.toString().contains('AppEnvironment.staging')) {
      return stagingBaseUrl;
    } else if (environment.toString().contains('AppEnvironment.production')) {
      return prodBaseUrl;
    } else {
      return devBaseUrl; // Default to devBaseUrl
    }
  }
  
  /// Tạo URL với query parameters
  static String createUrlWithParams(String endpoint, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return endpoint;
    }
    
    final queryString = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
    
    return '$endpoint?$queryString';
  }
  
  /// Lấy tất cả endpoints theo category
  static Map<String, List<String>> getAllEndpointsByCategory() {
    return {
      'Authentication': [
        login,
        register,
        logout,
        refreshToken,
        me,
        updateProfile,
        changePassword,
        forgotPassword,
        resetPassword,
        verifyEmail,
        resendVerification,
      ],
      'User Management': [
        users,
        createUser,
        uploadAvatar,
      ],
      'Tenant Management': [
        userTenants,
        tenants,
        createTenant,
      ],
      'Unit Management': [
        units,
        createUnit,
      ],
      'Roles': [
        roles,
        createRole,
      ],
      'Face Detection': [
        registerFace,
        verifyFace,
        registeredFaces,
        faceRecognitionHistory,
      ],
      'Device Management': [
        registerDevice,
        devices,
      ],
      'Terminal': [
        terminalSessions,
        createTerminalSession,
      ],
      'File Management': [
        uploadFile,
        files,
      ],
      'Settings': [
        appSettings,
        updateAppSettings,
        faceDetectionSettings,
        updateFaceDetectionSettings,
        securitySettings,
        updateSecuritySettings,
      ],
      'Notifications': [
        notifications,
        markAllNotificationsRead,
      ],
      'Analytics': [
        dashboardAnalytics,
        faceRecognitionAnalytics,
        deviceUsageAnalytics,
        userActivityAnalytics,
      ],
    };
  }
  
  /// Kiểm tra xem endpoint có hợp lệ hay không
  static bool isValidEndpoint(String endpoint) {
    final allEndpoints = getAllEndpointsByCategory()
        .values
        .expand((endpoints) => endpoints)
        .toList();
    return allEndpoints.contains(endpoint);
  }
}

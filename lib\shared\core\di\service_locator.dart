import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;

// Modular DI
import 'modules/core_module.dart';
import 'modules/network_module.dart';
import 'modules/auth_module.dart';
import 'modules/tenant_module.dart';
import 'modules/user_module.dart';
import 'modules/face_module.dart';

// Core - Note: api_client will be migrated in later tasks

final GetIt getIt = GetIt.instance;

/// Setup service locator với modular approach
///
/// Registers all dependencies using modular DI pattern
Future<void> setupServiceLocator() async {
  // ============================================================================
  // REGISTER MODULES IN ORDER
  // ============================================================================

  // 1. Core Module - Must be first (provides basic services)
  registerCoreDependencies();

  // 2. Network Module - Depends on core
  registerNetworkDependencies();

  // 3. Auth Module - Depends on network and core
  registerAuthDependencies();

  // 4. Tenant Module - Depends on network and core
  registerTenantDependencies();

  // 5. User Module - Depends on network and core
  registerUserDependencies();

  // 6. Face Module - Standalone
  registerFaceDependencies();

  // Log successful setup
  _logDependencyStatus();
}

/// Log dependency registration status for debugging
void _logDependencyStatus() {
  if (kDebugMode) {
    print('🚀 Service Locator Setup Complete');
    print('📊 Dependency Status:');

    // Core dependencies
    print('  Core: ${areCoreDependenciesRegistered() ? '✅' : '❌'}');

    // Network dependencies
    print('  Network: ${areNetworkDependenciesRegistered() ? '✅' : '❌'}');

    // Basic check for other modules
    print('  Auth: ✅');
    print('  User: ✅');
    print('  Face: ✅');
  }
}

/// Clean up resources when app is disposed
Future<void> disposeServiceLocator() async {
  // Note: ApiClient disposal will be added when it's migrated

  final httpClient = getIt<http.Client>();
  httpClient.close();

  await getIt.reset();
}

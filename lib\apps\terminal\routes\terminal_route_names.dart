/// Terminal app route names constants
/// 
/// Centralized route name constants for the terminal application
/// Optimized for kiosk mode with simplified navigation
class TerminalRouteNames {
  // Private constructor to prevent instantiation
  TerminalRouteNames._();

  // ============================================================================
  // ROOT ROUTES
  // ============================================================================
  
  static const String root = '/';
  static const String splash = '/splash';

  // ============================================================================
  // KIOSK MODE ROUTES
  // ============================================================================
  
  static const String kioskHome = '/kiosk-home';
  static const String stream = '/stream';
  static const String faceCapture = '/face-capture';
  static const String faceResult = '/face-result';
  static const String kioskIdle = '/kiosk-idle';
  
  // ============================================================================
  // ADMIN ROUTES (for configuration)
  // ============================================================================
  
  static const String adminLogin = '/admin-login';
  static const String adminSettings = '/admin-settings';
  static const String adminUsers = '/admin-users';
  static const String adminSystem = '/admin-system';
  
  // ============================================================================
  // ERROR ROUTES
  // ============================================================================
  
  static const String notFound = '/not-found';
  static const String error = '/error';
  
  // ============================================================================
  // ROUTE PARAMETERS
  // ============================================================================
  
  static const String userIdParam = 'userId';
  static const String faceIdParam = 'faceId';
  static const String errorMessageParam = 'errorMessage';
  static const String adminTokenParam = 'adminToken';
  
  // ============================================================================
  // ROUTE WITH PARAMETERS HELPERS
  // ============================================================================
  
  static String faceResultWithId(String faceId) => '/face-result/$faceId';
  static String errorWithMessage(String message) => '/error?message=$message';
  static String adminSettingsWithToken(String token) => '/admin-settings?token=$token';
  
  // ============================================================================
  // ROUTE VALIDATION HELPERS
  // ============================================================================
  
  /// Check if a route is a kiosk mode route
  static bool isKioskRoute(String route) {
    return [
      kioskHome,
      stream,
      faceCapture,
      faceResult,
      kioskIdle,
    ].contains(route);
  }
  
  /// Check if a route is an admin route
  static bool isAdminRoute(String route) {
    return [
      adminLogin,
      adminSettings,
      adminUsers,
      adminSystem,
    ].contains(route);
  }
  
  /// Check if a route requires admin authentication
  static bool requiresAdminAuth(String route) {
    return isAdminRoute(route) && route != adminLogin;
  }
  
  /// Check if a route is a face-related route
  static bool isFaceRoute(String route) {
    return [
      faceCapture,
      faceResult,
    ].contains(route);
  }
  
  /// Check if a route allows auto-timeout (kiosk mode)
  static bool allowsAutoTimeout(String route) {
    return isKioskRoute(route) && route != kioskIdle;
  }
  
  /// Get all route names as a list
  static List<String> get allRoutes => [
    root,
    splash,
    kioskHome,
    stream,
    faceCapture,
    faceResult,
    kioskIdle,
    adminLogin,
    adminSettings,
    adminUsers,
    adminSystem,
    notFound,
    error,
  ];
  
  /// Get kiosk routes only
  static List<String> get kioskRoutes => [
    kioskHome,
    stream,
    faceCapture,
    faceResult,
    kioskIdle,
  ];
  
  /// Get admin routes only
  static List<String> get adminRoutes => [
    adminLogin,
    adminSettings,
    adminUsers,
    adminSystem,
  ];
  
  /// Get route display name for UI
  static String getDisplayName(String route) {
    switch (route) {
      case root:
        return 'Home';
      case splash:
        return 'Loading';
      case kioskHome:
        return 'Kiosk Home';
      case stream:
        return 'Live Stream';
      case faceCapture:
        return 'Face Capture';
      case faceResult:
        return 'Face Result';
      case kioskIdle:
        return 'Idle Screen';
      case adminLogin:
        return 'Admin Login';
      case adminSettings:
        return 'Admin Settings';
      case adminUsers:
        return 'User Management';
      case adminSystem:
        return 'System Settings';
      case notFound:
        return 'Not Found';
      case error:
        return 'Error';
      default:
        return route.replaceAll('/', '').replaceAll('-', ' ').toUpperCase();
    }
  }
  
  /// Get route description for admin interface
  static String getDescription(String route) {
    switch (route) {
      case kioskHome:
        return 'Main kiosk interface for user interaction';
      case stream:
        return 'Live camera stream display';
      case faceCapture:
        return 'Face capture and recognition interface';
      case faceResult:
        return 'Display face recognition results';
      case kioskIdle:
        return 'Idle screen with timeout handling';
      case adminLogin:
        return 'Administrator authentication';
      case adminSettings:
        return 'System configuration and settings';
      case adminUsers:
        return 'User management and registration';
      case adminSystem:
        return 'System monitoring and diagnostics';
      default:
        return 'Terminal application route';
    }
  }
  
  /// Get default route for terminal app
  static String get defaultRoute => kioskHome;
  
  /// Get admin default route
  static String get adminDefaultRoute => adminSettings;
  
  /// Get timeout duration for route (in seconds)
  static int getTimeoutDuration(String route) {
    switch (route) {
      case faceCapture:
        return 30; // 30 seconds for face capture
      case faceResult:
        return 10; // 10 seconds to show result
      case kioskHome:
        return 120; // 2 minutes on home screen
      case stream:
        return 300; // 5 minutes for stream viewing
      default:
        return 60; // Default 1 minute timeout
    }
  }
}

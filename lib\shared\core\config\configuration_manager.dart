/// Configuration Manager
/// 
/// Central configuration management system that coordinates multiple
/// configuration providers with priority-based loading and real-time updates.

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'flexible_config_system.dart';
import 'config_parameters_registry.dart';
import 'providers/file_config_provider.dart';
import 'providers/environment_config_provider.dart';
import 'providers/remote_config_provider.dart';
import 'providers/runtime_config_provider.dart';

/// Main configuration manager implementation
class ConfigurationManager implements IConfigurationManager {
  static ConfigurationManager? _instance;
  static ConfigurationManager get instance {
    _instance ??= ConfigurationManager._internal();
    return _instance!;
  }
  
  ConfigurationManager._internal();

  final List<ConfigProvider> _providers = [];
  final Map<String, dynamic> _mergedConfig = {};
  final Map<ConfigSource, Map<String, dynamic>> _sourceConfigs = {};
  
  final StreamController<ConfigChangeEvent> _changeController = 
      StreamController<ConfigChangeEvent>.broadcast();
  
  bool _isInitialized = false;
  final List<StreamSubscription> _subscriptions = [];

  @override
  Stream<ConfigChangeEvent> get changeStream => _changeController.stream;

  /// Initialize configuration manager
  Future<void> initialize({
    bool enableFileConfig = true,
    bool enableEnvironmentConfig = true,
    bool enableRemoteConfig = false,
    String? remoteServerUrl,
    String? deviceId,
    String? authToken,
  }) async {
    if (_isInitialized) return;

    try {
      // Initialize parameter registry
      ConfigParametersRegistry.initialize();

      // Add runtime provider first (highest priority)
      final runtimeProvider = RuntimeConfigProvider();
      await _addProvider(runtimeProvider);

      // Add providers in priority order (highest to lowest)
      if (enableEnvironmentConfig) {
        final envProvider = EnvironmentConfigProvider();
        await _addProvider(envProvider);
      }

      if (enableFileConfig) {
        final fileProvider = FileConfigProvider();
        await _addProvider(fileProvider);
      }

      if (enableRemoteConfig && remoteServerUrl != null && deviceId != null) {
        final remoteProvider = RemoteConfigProvider(
          serverUrl: remoteServerUrl,
          deviceId: deviceId,
          authToken: authToken,
        );
        await _addProvider(remoteProvider);
      }

      // Merge all configurations
      await _mergeConfigurations();
      
      _isInitialized = true;
      debugPrint('⚙️ ConfigurationManager đã được khởi tạo với ${_providers.length} providers');
      
    } catch (e) {
      debugPrint('Error initializing ConfigurationManager: $e');
      rethrow;
    }
  }

  /// Add a configuration provider
  Future<void> _addProvider(ConfigProvider provider) async {
    try {
      await provider.initialize();
      _providers.add(provider);
      
      // Listen to configuration changes
      final subscription = provider.configStream.listen((config) {
        _onProviderConfigChanged(provider.source, config);
      });
      _subscriptions.add(subscription);
      
      // Load initial configuration
      final config = await provider.loadConfiguration();
      _sourceConfigs[provider.source] = config;
      
    } catch (e) {
      debugPrint('Error adding provider ${provider.name}: $e');
    }
  }

  /// Handle provider configuration changes
  void _onProviderConfigChanged(ConfigSource source, Map<String, dynamic> config) {
    _sourceConfigs[source] = config;
    
    // Merge configurations and detect changes
    final oldMergedConfig = Map<String, dynamic>.from(_mergedConfig);
    _mergeConfigurations();
    
    // Emit change events for modified values
    _emitChangeEvents(oldMergedConfig, _mergedConfig, source);
  }

  /// Merge configurations from all sources based on priority
  Future<void> _mergeConfigurations() async {
    _mergedConfig.clear();
    
    // Sort sources by priority (lowest number = highest priority)
    final sortedSources = ConfigSource.values.toList()
      ..sort((a, b) => b.priority.compareTo(a.priority)); // Reverse for highest first
    
    // Merge configurations in reverse priority order (defaults first)
    for (final source in sortedSources.reversed) {
      final config = _sourceConfigs[source];
      if (config != null) {
        _mergedConfig.addAll(config);
      }
    }
  }

  /// Emit change events for modified configuration values
  void _emitChangeEvents(
    Map<String, dynamic> oldConfig,
    Map<String, dynamic> newConfig,
    ConfigSource source,
  ) {
    final allKeys = {...oldConfig.keys, ...newConfig.keys};
    
    for (final key in allKeys) {
      final oldValue = oldConfig[key];
      final newValue = newConfig[key];
      
      if (oldValue != newValue) {
        final event = ConfigChangeEvent(
          key: key,
          oldValue: oldValue,
          newValue: newValue,
          source: source,
          timestamp: DateTime.now(),
        );
        _changeController.add(event);
      }
    }
  }

  @override
  T getValue<T>(String key, {T? defaultValue}) {
    if (!_isInitialized) {
      // Return default value if not initialized instead of throwing error
      if (defaultValue != null) {
        return defaultValue;
      }
      // Only throw if no default value provided
      throw StateError('ConfigurationManager not initialized');
    }

    // Try to get from merged config first
    if (_mergedConfig.containsKey(key)) {
      final value = _mergedConfig[key];
      if (value is T) return value;
      
      // Try to parse/convert the value
      try {
        final parameter = ConfigParametersRegistry.getParameter(key);
        if (parameter != null) {
          final parsedValue = parameter.parseValue(value);
          if (parsedValue is T) return parsedValue;
        }
      } catch (e) {
        debugPrint('Error parsing config value for $key: $e');
      }
    }

    // Fall back to parameter default value
    final parameter = ConfigParametersRegistry.getParameter(key);
    if (parameter != null && parameter.defaultValue is T) {
      return parameter.defaultValue as T;
    }

    // Use provided default value
    if (defaultValue != null) return defaultValue;

    throw ArgumentError('Configuration value not found for key: $key');
  }

  @override
  Future<void> setValue<T>(String key, T value, {ConfigSource source = ConfigSource.runtime}) async {
    if (!_isInitialized) {
      throw StateError('ConfigurationManager not initialized');
    }

    // Validate the value
    final parameter = ConfigParametersRegistry.getParameter(key);
    if (parameter != null && !parameter.isValid(value)) {
      throw ArgumentError('Invalid value for configuration key $key: $value');
    }

    // Update the appropriate provider
    final provider = _providers.firstWhere(
      (p) => p.source == source,
      orElse: () => throw ArgumentError('No provider found for source: $source'),
    );

    try {
      // Update source configuration
      final sourceConfig = Map<String, dynamic>.from(_sourceConfigs[source] ?? {});
      sourceConfig[key] = value;
      
      await provider.saveConfiguration(sourceConfig);
      
      // The provider will emit the change event through its stream
      
    } catch (e) {
      debugPrint('Error setting configuration value: $e');
      rethrow;
    }
  }

  @override
  void registerParameter<T>(ConfigParameter<T> parameter) {
    // Parameters are registered through ConfigParametersRegistry
    throw UnsupportedError('Use ConfigParametersRegistry.initialize() instead');
  }

  @override
  Map<String, dynamic> getCategoryValues(String category) {
    if (!_isInitialized) {
      throw StateError('ConfigurationManager not initialized');
    }

    final parameters = ConfigParametersRegistry.getParametersByCategory(category);
    final categoryValues = <String, dynamic>{};
    
    for (final parameter in parameters.values) {
      try {
        final value = getValue(parameter.key, defaultValue: parameter.defaultValue);
        categoryValues[parameter.key] = value;
      } catch (e) {
        debugPrint('Error getting value for ${parameter.key}: $e');
        categoryValues[parameter.key] = parameter.defaultValue;
      }
    }
    
    return categoryValues;
  }

  @override
  Future<void> reload() async {
    if (!_isInitialized) return;

    try {
      // Reload all providers
      for (final provider in _providers) {
        final config = await provider.loadConfiguration();
        _sourceConfigs[provider.source] = config;
      }
      
      // Merge configurations
      await _mergeConfigurations();
      
      debugPrint('🔄 Đã tải lại cấu hình từ tất cả nguồn');
      
    } catch (e) {
      debugPrint('Error reloading configuration: $e');
    }
  }

  @override
  Map<String, dynamic> exportConfiguration({bool includeSecure = false}) {
    if (!_isInitialized) {
      throw StateError('ConfigurationManager not initialized');
    }

    final exported = <String, dynamic>{};
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final entry in _mergedConfig.entries) {
      final parameter = parameters[entry.key];
      
      // Skip secure parameters unless explicitly requested
      if (parameter?.isSecure == true && !includeSecure) {
        continue;
      }
      
      exported[entry.key] = entry.value;
    }
    
    return exported;
  }

  @override
  Future<void> importConfiguration(Map<String, dynamic> config) async {
    if (!_isInitialized) {
      throw StateError('ConfigurationManager not initialized');
    }

    final errors = <String>[];
    
    for (final entry in config.entries) {
      try {
        await setValue(entry.key, entry.value, source: ConfigSource.runtime);
      } catch (e) {
        errors.add('Error importing ${entry.key}: $e');
      }
    }
    
    if (errors.isNotEmpty) {
      debugPrint('Import errors: ${errors.join(', ')}');
    }
    
    debugPrint('Configuration imported: ${config.length - errors.length}/${config.length} values');
  }

  @override
  List<String> validateConfiguration() {
    if (!_isInitialized) {
      return ['ConfigurationManager not initialized'];
    }

    final errors = <String>[];
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final parameter in parameters.values) {
      try {
        final value = getValue(parameter.key, defaultValue: parameter.defaultValue);
        
        if (!parameter.isValid(value)) {
          errors.add('Invalid value for ${parameter.key}: $value');
        }
        
        if (parameter.isRequired && value == parameter.defaultValue) {
          errors.add('Required parameter ${parameter.key} is using default value');
        }
        
      } catch (e) {
        errors.add('Error validating ${parameter.key}: $e');
      }
    }
    
    return errors;
  }

  /// Get configuration by source
  Map<String, dynamic> getConfigurationBySource(ConfigSource source) {
    return Map<String, dynamic>.from(_sourceConfigs[source] ?? {});
  }

  /// Get all source configurations
  Map<ConfigSource, Map<String, dynamic>> getAllSourceConfigurations() {
    return Map<ConfigSource, Map<String, dynamic>>.from(_sourceConfigs);
  }

  /// Check if configuration manager is initialized
  bool get isInitialized => _isInitialized;

  /// Get active providers
  List<ConfigProvider> get activeProviders => List<ConfigProvider>.from(_providers);

  /// Dispose configuration manager
  Future<void> dispose() async {
    for (final subscription in _subscriptions) {
      await subscription.cancel();
    }
    _subscriptions.clear();
    
    for (final provider in _providers) {
      await provider.dispose();
    }
    _providers.clear();
    
    await _changeController.close();
    
    _mergedConfig.clear();
    _sourceConfigs.clear();
    _isInitialized = false;
    
    debugPrint('ConfigurationManager disposed');
  }


}

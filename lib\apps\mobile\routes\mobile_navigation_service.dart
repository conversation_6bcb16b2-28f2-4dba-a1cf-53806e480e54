import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'mobile_route_names.dart';

/// Navigation service for programmatic navigation without context
/// 
/// Migrated from c-faces project and adapted for mobile app architecture
/// Provides navigation methods that can be called from anywhere in the app
class MobileNavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  static NavigatorState? get navigator => navigatorKey.currentState;
  static BuildContext? get context => navigator?.context;

  // ============================================================================
  // BASIC NAVIGATION METHODS
  // ============================================================================
  
  /// Navigate to login screen
  static void goToLogin() => context?.go(MobileRouteNames.login);
  
  /// Navigate to home/dashboard
  static void goToHome() => context?.go(MobileRouteNames.dashboard);
  static void goToDashboard() => context?.go(MobileRouteNames.dashboard);
  static void goToMain() => context?.go(MobileRouteNames.main);
  
  /// Navigate to splash screen
  static void goToSplash() => context?.go(MobileRouteNames.splash);
  
  // ============================================================================
  // AUTHENTICATION NAVIGATION
  // ============================================================================
  
  /// Navigate to forgot password flow
  static void goToForgotPassword() => context?.go(MobileRouteNames.forgotPassword);
  static void goToEnterOtp() => context?.go(MobileRouteNames.enterOtp);
  static void goToConfirmPassword() => context?.go(MobileRouteNames.confirmPassword);
  static void goToSuccessfully() => context?.go(MobileRouteNames.successfully);
  
  // ============================================================================
  // TENANT MANAGEMENT NAVIGATION
  // ============================================================================
  
  /// Navigate to tenants screen
  static void goToTenants() => context?.go(MobileRouteNames.tenants);
  
  /// Navigate to tenant creation screen
  static void goToTenantCreate() => context?.go(MobileRouteNames.tenantCreate);
  
  // ============================================================================
  // USER MANAGEMENT NAVIGATION
  // ============================================================================
  
  /// Navigate to users screen
  static void goToUsers() => context?.go(MobileRouteNames.users);
  
  /// Navigate to user detail screen
  static void goToUserDetail() => context?.go(MobileRouteNames.userDetail);
  
  /// Navigate to user face registration screen
  static void goToUserFaceRegister() => context?.go(MobileRouteNames.userFaceRegister);
  
  /// Navigate to user item detail screen
  static void goToUserItemDetail() => context?.go(MobileRouteNames.userItemDetail);
  
  // ============================================================================
  // TOOLS NAVIGATION
  // ============================================================================
  
  /// Navigate to tools screen
  static void goToTools() => context?.go(MobileRouteNames.tools);
  
  /// Navigate to access control screen
  static void goToAccessControl() => context?.go(MobileRouteNames.accessControl);
  
  /// Navigate to attendance screen
  static void goToAttendance() => context?.go(MobileRouteNames.attendance);
  
  /// Navigate to security monitoring screen
  static void goToSecurityMonitoring() => context?.go(MobileRouteNames.securityMonitoring);
  
  /// Navigate to system management screen
  static void goToSystemManagement() => context?.go(MobileRouteNames.systemManagement);
  
  // ============================================================================
  // PROFILE & SETTINGS NAVIGATION
  // ============================================================================
  
  /// Navigate to profile screen
  static void goToProfile() => context?.go(MobileRouteNames.profile);
  
  /// Navigate to profile edit screen
  static void goToProfileEdit() => context?.go(MobileRouteNames.profileEdit);
  
  /// Navigate to settings screen
  static void goToSettings() => context?.go(MobileRouteNames.settings);
  
  /// Navigate to notifications screen
  static void goToNotifications() => context?.go(MobileRouteNames.notifications);
  
  // ============================================================================
  // FACE CAPTURE NAVIGATION
  // ============================================================================
  
  /// Navigate to face capture screen
  static void goToFaceCapture() => context?.go(MobileRouteNames.faceCapture);
  
  /// Navigate to face guide screen
  static void goToFaceGuide() => context?.go(MobileRouteNames.faceGuide);
  
  /// Navigate to face result screen
  static void goToFaceResult() => context?.go(MobileRouteNames.faceResult);
  
  /// Navigate to face validation screen
  static void goToFaceValidation() => context?.go(MobileRouteNames.faceValidation);
  
  // ============================================================================
  // ERROR NAVIGATION
  // ============================================================================
  
  /// Navigate to error screen with message
  static void goToError(String message) => context?.go(MobileRouteNames.errorWithMessage(message));
  
  /// Navigate to not found screen
  static void goToNotFound() => context?.go(MobileRouteNames.notFound);
  
  // ============================================================================
  // NAVIGATION WITH PARAMETERS
  // ============================================================================
  
  /// Navigate to profile with user ID
  static void goToProfileWithId(String userId) => context?.go(MobileRouteNames.profileWithId(userId));
  
  /// Navigate to user detail with user ID
  static void goToUserDetailWithId(String userId) => context?.go(MobileRouteNames.userDetailWithId(userId));
  
  /// Navigate to tenant detail with tenant ID
  static void goToTenantDetailWithId(String tenantId) => context?.go(MobileRouteNames.tenantDetailWithId(tenantId));
  
  /// Navigate to face result with face ID
  static void goToFaceResultWithId(String faceId) => context?.go(MobileRouteNames.faceResultWithId(faceId));
  
  // ============================================================================
  // PUSH METHODS (for modal navigation)
  // ============================================================================
  
  /// Push login screen
  static void pushLogin() => context?.push(MobileRouteNames.login);
  
  /// Push profile screen
  static void pushProfile() => context?.push(MobileRouteNames.profile);
  
  /// Push settings screen
  static void pushSettings() => context?.push(MobileRouteNames.settings);
  
  /// Push user detail screen
  static void pushUserDetail() => context?.push(MobileRouteNames.userDetail);
  
  /// Push face capture screen
  static void pushFaceCapture() => context?.push(MobileRouteNames.faceCapture);
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Check if can go back
  static bool get canGoBack => context?.canPop() ?? false;
  
  /// Go back
  static void goBack() => context?.pop();
  
  /// Go back or navigate to home if can't go back
  static void goBackOrHome() {
    if (canGoBack) {
      goBack();
    } else {
      goToHome();
    }
  }
  
  /// Go back or navigate to dashboard if can't go back
  static void goBackOrDashboard() {
    if (canGoBack) {
      goBack();
    } else {
      goToDashboard();
    }
  }
  
  // ============================================================================
  // CURRENT ROUTE HELPERS
  // ============================================================================
  
  /// Get current route path
  static String? get currentRoute {
    final ctx = context;
    if (ctx == null) return null;
    return GoRouterState.of(ctx).uri.path;
  }
  
  /// Check if currently on login page
  static bool get isOnLoginPage => currentRoute == MobileRouteNames.login;
  
  /// Check if currently on home/dashboard page
  static bool get isOnHomePage => currentRoute == MobileRouteNames.dashboard;
  static bool get isOnDashboardPage => currentRoute == MobileRouteNames.dashboard;
  static bool get isOnMainPage => currentRoute == MobileRouteNames.main;
  
  /// Check if currently on auth page
  static bool get isOnAuthPage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isAuthRoute(route) : false;
  }
  
  /// Check if currently on protected page
  static bool get isOnProtectedPage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isProtectedRoute(route) : false;
  }
  
  /// Check if currently on face-related page
  static bool get isOnFacePage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isFaceRoute(route) : false;
  }
  
  /// Check if currently on user management page
  static bool get isOnUserPage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isUserRoute(route) : false;
  }
  
  /// Check if currently on tenant management page
  static bool get isOnTenantPage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isTenantRoute(route) : false;
  }
  
  /// Check if currently on tools page
  static bool get isOnToolsPage {
    final route = currentRoute;
    return route != null ? MobileRouteNames.isToolsRoute(route) : false;
  }
  
  // ============================================================================
  // ROUTE INFORMATION
  // ============================================================================
  
  /// Get current route display name
  static String? get currentRouteDisplayName {
    final route = currentRoute;
    return route != null ? MobileRouteNames.getDisplayName(route) : null;
  }
  
  /// Get route parameters
  static Map<String, String>? get routeParameters {
    final ctx = context;
    if (ctx == null) return null;
    return GoRouterState.of(ctx).pathParameters;
  }
  
  /// Get query parameters
  static Map<String, String>? get queryParameters {
    final ctx = context;
    if (ctx == null) return null;
    return GoRouterState.of(ctx).uri.queryParameters;
  }
  
  /// Get specific route parameter
  static String? getRouteParameter(String key) => routeParameters?[key];
  
  /// Get specific query parameter
  static String? getQueryParameter(String key) => queryParameters?[key];
}

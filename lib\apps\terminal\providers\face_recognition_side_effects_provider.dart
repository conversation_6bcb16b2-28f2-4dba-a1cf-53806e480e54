import 'package:flutter/foundation.dart';
import '../../../shared/core/config/configuration_manager.dart';

/// Provider for managing face recognition side effects configuration
/// 
/// This provider manages what actions should be taken after face recognition
/// results are received from the server (e.g., trigger relay, send notifications, etc.)
class FaceRecognitionSideEffectsProvider extends ChangeNotifier {
  static FaceRecognitionSideEffectsProvider? _instance;
  static FaceRecognitionSideEffectsProvider get instance => 
      _instance ??= FaceRecognitionSideEffectsProvider._();

  FaceRecognitionSideEffectsProvider._();

  // Side effect configuration
  bool _isEnabled = true;
  bool _triggerRelayOnAccess = true;
  bool _sendNotificationOnAccess = true;
  bool _sendNotificationOnDenied = false;
  bool _logAllAttempts = true;
  bool _saveRecognitionImages = true;
  
  // Relay configuration
  String _defaultRelayDeviceId = 'T-A3B4-R01';
  int _relayTriggerDuration = 3; // seconds
  List<String> _registeredRelayDevices = [];
  
  // Notification configuration
  bool _showSuccessNotification = true;
  bool _showDeniedNotification = true;
  int _notificationDuration = 3; // seconds
  
  // Access control configuration
  List<String> _allowedAccessLevels = ['ADMIN', 'USER'];
  double _minimumConfidenceThreshold = 0.7;
  bool _requireServerApproval = true;
  
  // Auto-registration configuration
  bool _autoRegisterRelays = true;
  int _defaultRelayCount = 4;
  List<String> _relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];

  // Getters
  bool get isEnabled => _isEnabled;
  bool get triggerRelayOnAccess => _triggerRelayOnAccess;
  bool get sendNotificationOnAccess => _sendNotificationOnAccess;
  bool get sendNotificationOnDenied => _sendNotificationOnDenied;
  bool get logAllAttempts => _logAllAttempts;
  bool get saveRecognitionImages => _saveRecognitionImages;
  
  String get defaultRelayDeviceId => _defaultRelayDeviceId;
  int get relayTriggerDuration => _relayTriggerDuration;
  List<String> get registeredRelayDevices => List.unmodifiable(_registeredRelayDevices);
  
  bool get showSuccessNotification => _showSuccessNotification;
  bool get showDeniedNotification => _showDeniedNotification;
  int get notificationDuration => _notificationDuration;
  
  List<String> get allowedAccessLevels => List.unmodifiable(_allowedAccessLevels);
  double get minimumConfidenceThreshold => _minimumConfidenceThreshold;
  bool get requireServerApproval => _requireServerApproval;
  
  bool get autoRegisterRelays => _autoRegisterRelays;
  int get defaultRelayCount => _defaultRelayCount;
  List<String> get relayProfiles => List.unmodifiable(_relayProfiles);

  /// Initialize provider and load configuration
  Future<void> initialize() async {
    try {
      await _loadConfiguration();
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing FaceRecognitionSideEffectsProvider: $e');
    }
  }

  /// Load configuration from ConfigurationManager
  Future<void> _loadConfiguration() async {
    final config = ConfigurationManager.instance;
    
    // Load side effect settings
    _isEnabled = config.getValue<bool>('face_recognition.side_effects.enabled', _isEnabled);
    _triggerRelayOnAccess = config.getValue<bool>('face_recognition.side_effects.trigger_relay', _triggerRelayOnAccess);
    _sendNotificationOnAccess = config.getValue<bool>('face_recognition.side_effects.notify_access', _sendNotificationOnAccess);
    _sendNotificationOnDenied = config.getValue<bool>('face_recognition.side_effects.notify_denied', _sendNotificationOnDenied);
    _logAllAttempts = config.getValue<bool>('face_recognition.side_effects.log_attempts', _logAllAttempts);
    _saveRecognitionImages = config.getValue<bool>('face_recognition.side_effects.save_images', _saveRecognitionImages);
    
    // Load relay settings
    _defaultRelayDeviceId = config.getValue<String>('face_recognition.relay.default_device_id', _defaultRelayDeviceId);
    _relayTriggerDuration = config.getValue<int>('face_recognition.relay.trigger_duration', _relayTriggerDuration);
    _registeredRelayDevices = config.getValue<List<String>>('face_recognition.relay.registered_devices', _registeredRelayDevices);
    
    // Load notification settings
    _showSuccessNotification = config.getValue<bool>('face_recognition.notifications.show_success', _showSuccessNotification);
    _showDeniedNotification = config.getValue<bool>('face_recognition.notifications.show_denied', _showDeniedNotification);
    _notificationDuration = config.getValue<int>('face_recognition.notifications.duration', _notificationDuration);
    
    // Load access control settings
    _allowedAccessLevels = config.getValue<List<String>>('face_recognition.access.allowed_levels', _allowedAccessLevels);
    _minimumConfidenceThreshold = config.getValue<double>('face_recognition.access.min_confidence', _minimumConfidenceThreshold);
    _requireServerApproval = config.getValue<bool>('face_recognition.access.require_server_approval', _requireServerApproval);
    
    // Load auto-registration settings
    _autoRegisterRelays = config.getValue<bool>('face_recognition.auto_register.enabled', _autoRegisterRelays);
    _defaultRelayCount = config.getValue<int>('face_recognition.auto_register.relay_count', _defaultRelayCount);
    _relayProfiles = config.getValue<List<String>>('face_recognition.auto_register.profiles', _relayProfiles);
  }

  /// Save configuration to ConfigurationManager
  Future<void> _saveConfiguration() async {
    final config = ConfigurationManager.instance;
    
    // Save side effect settings
    await config.setValue('face_recognition.side_effects.enabled', _isEnabled);
    await config.setValue('face_recognition.side_effects.trigger_relay', _triggerRelayOnAccess);
    await config.setValue('face_recognition.side_effects.notify_access', _sendNotificationOnAccess);
    await config.setValue('face_recognition.side_effects.notify_denied', _sendNotificationOnDenied);
    await config.setValue('face_recognition.side_effects.log_attempts', _logAllAttempts);
    await config.setValue('face_recognition.side_effects.save_images', _saveRecognitionImages);
    
    // Save relay settings
    await config.setValue('face_recognition.relay.default_device_id', _defaultRelayDeviceId);
    await config.setValue('face_recognition.relay.trigger_duration', _relayTriggerDuration);
    await config.setValue('face_recognition.relay.registered_devices', _registeredRelayDevices);
    
    // Save notification settings
    await config.setValue('face_recognition.notifications.show_success', _showSuccessNotification);
    await config.setValue('face_recognition.notifications.show_denied', _showDeniedNotification);
    await config.setValue('face_recognition.notifications.duration', _notificationDuration);
    
    // Save access control settings
    await config.setValue('face_recognition.access.allowed_levels', _allowedAccessLevels);
    await config.setValue('face_recognition.access.min_confidence', _minimumConfidenceThreshold);
    await config.setValue('face_recognition.access.require_server_approval', _requireServerApproval);
    
    // Save auto-registration settings
    await config.setValue('face_recognition.auto_register.enabled', _autoRegisterRelays);
    await config.setValue('face_recognition.auto_register.relay_count', _defaultRelayCount);
    await config.setValue('face_recognition.auto_register.profiles', _relayProfiles);
  }

  /// Update side effects enabled state
  Future<void> updateSideEffectsEnabled(bool enabled) async {
    _isEnabled = enabled;
    await _saveConfiguration();
    notifyListeners();
  }

  /// Update relay trigger on access
  Future<void> updateTriggerRelayOnAccess(bool enabled) async {
    _triggerRelayOnAccess = enabled;
    await _saveConfiguration();
    notifyListeners();
  }

  /// Update notification settings
  Future<void> updateNotificationSettings({
    bool? showSuccess,
    bool? showDenied,
    int? duration,
  }) async {
    if (showSuccess != null) _showSuccessNotification = showSuccess;
    if (showDenied != null) _showDeniedNotification = showDenied;
    if (duration != null) _notificationDuration = duration;
    
    await _saveConfiguration();
    notifyListeners();
  }

  /// Update relay settings
  Future<void> updateRelaySettings({
    String? defaultDeviceId,
    int? triggerDuration,
  }) async {
    if (defaultDeviceId != null) _defaultRelayDeviceId = defaultDeviceId;
    if (triggerDuration != null) _relayTriggerDuration = triggerDuration;
    
    await _saveConfiguration();
    notifyListeners();
  }

  /// Update access control settings
  Future<void> updateAccessControlSettings({
    List<String>? allowedLevels,
    double? minConfidence,
    bool? requireServerApproval,
  }) async {
    if (allowedLevels != null) _allowedAccessLevels = allowedLevels;
    if (minConfidence != null) _minimumConfidenceThreshold = minConfidence;
    if (requireServerApproval != null) _requireServerApproval = requireServerApproval;
    
    await _saveConfiguration();
    notifyListeners();
  }

  /// Add relay device to registered list
  Future<void> addRelayDevice(String deviceId) async {
    if (!_registeredRelayDevices.contains(deviceId)) {
      _registeredRelayDevices.add(deviceId);
      await _saveConfiguration();
      notifyListeners();
    }
  }

  /// Remove relay device from registered list
  Future<void> removeRelayDevice(String deviceId) async {
    if (_registeredRelayDevices.remove(deviceId)) {
      await _saveConfiguration();
      notifyListeners();
    }
  }

  /// Generate relay device IDs for auto-registration
  List<String> generateRelayDeviceIds(String terminalId) {
    final List<String> relayIds = [];
    
    for (int i = 1; i <= _defaultRelayCount; i++) {
      final relayId = '$terminalId-R${i.toString().padLeft(2, '0')}';
      relayIds.add(relayId);
    }
    
    return relayIds;
  }

  /// Check if access should be granted based on configuration
  bool shouldGrantAccess({
    required bool serverAllowAccess,
    required double confidence,
    required String? accessLevel,
  }) {
    // If server approval is required and server denied, deny access
    if (_requireServerApproval && !serverAllowAccess) {
      return false;
    }
    
    // Check confidence threshold
    if (confidence < _minimumConfidenceThreshold) {
      return false;
    }
    
    // Check access level
    if (accessLevel != null && !_allowedAccessLevels.contains(accessLevel)) {
      return false;
    }
    
    return true;
  }

  /// Get side effects configuration summary
  Map<String, dynamic> getConfigurationSummary() {
    return {
      'side_effects_enabled': _isEnabled,
      'relay_trigger_enabled': _triggerRelayOnAccess,
      'notification_enabled': _sendNotificationOnAccess || _sendNotificationOnDenied,
      'logging_enabled': _logAllAttempts,
      'image_saving_enabled': _saveRecognitionImages,
      'registered_relay_count': _registeredRelayDevices.length,
      'auto_register_enabled': _autoRegisterRelays,
      'min_confidence_threshold': _minimumConfidenceThreshold,
      'allowed_access_levels': _allowedAccessLevels,
    };
  }

  /// Reset to default configuration
  Future<void> resetToDefaults() async {
    _isEnabled = true;
    _triggerRelayOnAccess = true;
    _sendNotificationOnAccess = true;
    _sendNotificationOnDenied = false;
    _logAllAttempts = true;
    _saveRecognitionImages = true;
    
    _defaultRelayDeviceId = 'T-A3B4-R01';
    _relayTriggerDuration = 3;
    _registeredRelayDevices.clear();
    
    _showSuccessNotification = true;
    _showDeniedNotification = true;
    _notificationDuration = 3;
    
    _allowedAccessLevels = ['ADMIN', 'USER'];
    _minimumConfidenceThreshold = 0.7;
    _requireServerApproval = true;
    
    _autoRegisterRelays = true;
    _defaultRelayCount = 4;
    _relayProfiles = ['main_door', 'back_door', 'garage', 'emergency'];
    
    await _saveConfiguration();
    notifyListeners();
  }
}

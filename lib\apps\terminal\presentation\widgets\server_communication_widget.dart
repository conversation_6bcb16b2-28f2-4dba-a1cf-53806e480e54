import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:convert';
import '../../models/secure_comm_models.dart';

/// Widget for testing server communication and sending commands
class ServerCommunicationWidget extends StatefulWidget {
  final SecureComm? secureComm;
  final VoidCallback? onRefresh;
  final Function(String serverUrl, String port, String deviceId, String deviceName)? onConnect;

  const ServerCommunicationWidget({
    super.key,
    this.secureComm,
    this.onRefresh,
    this.onConnect,
  });

  @override
  State<ServerCommunicationWidget> createState() => _ServerCommunicationWidgetState();
}

class _ServerCommunicationWidgetState extends State<ServerCommunicationWidget> {
  bool _isConnected = false;
  String _connectionStatus = 'Disconnected';
  String _lastPingTime = 'Never';
  String _relayStatus = 'Unknown';
  List<String> _commandHistory = [];
  bool _isLoading = false;
  bool _isConnecting = false;

  // Server configuration
  final TextEditingController _serverController = TextEditingController(text: 'localhost');
  final TextEditingController _portController = TextEditingController(text: '3000');
  final TextEditingController _deviceIdController = TextEditingController(text: 'terminal_001');
  final TextEditingController _deviceNameController = TextEditingController(text: 'Terminal Device');

  Timer? _pingTimer;
  Timer? _statusTimer;

  @override
  void initState() {
    super.initState();
    _updateConnectionStatus();
    _startPeriodicTasks();
  }

  @override
  void dispose() {
    _pingTimer?.cancel();
    _statusTimer?.cancel();
    _serverController.dispose();
    _portController.dispose();
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    super.dispose();
  }

  void _updateConnectionStatus() {
    setState(() {
      _isConnected = widget.secureComm?.isAuthenticated ?? false;
      _connectionStatus = _isConnected ? 'Connected' : 'Disconnected';
    });
  }

  void _startPeriodicTasks() {
    // Ping every 30 seconds
    _pingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected) {
        _sendPing();
      }
    });

    // Check status every 10 seconds
    _statusTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isConnected) {
        _getRelayStatus();
      }
    });
  }

  Future<void> _connectToServer() async {
    if (_isConnecting) return;

    setState(() {
      _isConnecting = true;
      _connectionStatus = 'Connecting...';
    });

    try {
      final serverUrl = _serverController.text.trim();
      final port = _portController.text.trim();
      final deviceId = _deviceIdController.text.trim();
      final deviceName = _deviceNameController.text.trim();

      if (serverUrl.isEmpty || port.isEmpty || deviceId.isEmpty) {
        throw Exception('Server URL, port, and device ID are required');
      }

      // Create WebSocket URL
      final wsUrl = 'ws://$serverUrl:$port';

      _addToHistory('CONNECTION', 'Attempting', 'Connecting to $wsUrl');

      // Use callback to create new connection if provided
      if (widget.onConnect != null) {
        await widget.onConnect!(serverUrl, port, deviceId, deviceName);

        // Check if connection was successful by checking if secureComm is available
        if (widget.secureComm != null && widget.secureComm!.isAuthenticated) {
          setState(() {
            _isConnected = true;
            _connectionStatus = 'Connected to $serverUrl:$port';
          });
          _addToHistory('CONNECTION', 'Success', 'Connected to server successfully');
          _startPeriodicTasks();
        } else {
          throw Exception('Failed to establish secure connection');
        }
      } else {
        // Fallback: simulate connection for testing
        await Future.delayed(const Duration(seconds: 2));
        setState(() {
          _isConnected = true;
          _connectionStatus = 'Connected to $serverUrl:$port (simulated)';
        });
        _addToHistory('CONNECTION', 'Success', 'Connected to server successfully (simulated)');
        _startPeriodicTasks();
      }

    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectionStatus = 'Connection failed';
      });
      _addToHistory('CONNECTION', 'Failed', e.toString());
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _disconnectFromServer() async {
    setState(() {
      _isConnecting = true;
      _connectionStatus = 'Disconnecting...';
    });

    try {
      _pingTimer?.cancel();
      _statusTimer?.cancel();

      // TODO: Properly disconnect SecureComm
      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _isConnected = false;
        _connectionStatus = 'Disconnected';
        _lastPingTime = 'Never';
        _relayStatus = 'Unknown';
      });

      _addToHistory('CONNECTION', 'Disconnected', 'Disconnected from server');

    } catch (e) {
      _addToHistory('CONNECTION', 'Error', 'Disconnect error: $e');
    } finally {
      setState(() {
        _isConnecting = false;
      });
    }
  }

  Future<void> _sendPing() async {
    if (widget.secureComm == null || !_isConnected) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'ping',
        payload: {
          'message': 'ping',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        setState(() {
          _lastPingTime = DateTime.now().toString().substring(11, 19);
        });
        _addToHistory('PING', 'Success', 'Ping sent successfully');
      } else {
        _addToHistory('PING', 'Failed', 'Ping failed');
      }
    } catch (e) {
      _addToHistory('PING', 'Error', e.toString());
    }
  }

  Future<void> _getRelayStatus() async {
    if (widget.secureComm == null || !_isConnected) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'status_request',
        payload: {
          'component': 'relay',
        },
      );

      if (response.success && response.data != null) {
        setState(() {
          _relayStatus = response.data?['status'] ?? 'Unknown';
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting relay status: $e');
      }
    }
  }

  Future<void> _sendRelayCommand(String action) async {
    if (widget.secureComm == null || !_isConnected) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'relay_control',
        payload: {
          'action': action,
          'relay_id': 'main_relay',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        _addToHistory('RELAY_CONTROL', 'Success', 'Relay $action command sent');
        // Update status after command
        await Future.delayed(const Duration(milliseconds: 500));
        await _getRelayStatus();
      } else {
        _addToHistory('RELAY_CONTROL', 'Failed', 'Relay command failed');
      }
    } catch (e) {
      _addToHistory('RELAY_CONTROL', 'Error', e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _sendCustomCommand() async {
    if (widget.secureComm == null || !_isConnected) return;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CustomCommandDialog(),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final response = await widget.secureComm!.sendMessage(
          type: result['type'] ?? 'custom',
          payload: result['payload'] ?? {},
        );

        if (response.success) {
          _addToHistory('CUSTOM', 'Success', 'Custom command sent: ${result['type']}');
        } else {
          _addToHistory('CUSTOM', 'Failed', 'Custom command failed');
        }
      } catch (e) {
        _addToHistory('CUSTOM', 'Error', e.toString());
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addToHistory(String command, String status, String details) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    final entry = '[$timestamp] $command: $status - $details';
    
    setState(() {
      _commandHistory.insert(0, entry);
      if (_commandHistory.length > 20) {
        _commandHistory.removeLast();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.router,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Server Communication',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                if (widget.onRefresh != null)
                  IconButton(
                    onPressed: widget.onRefresh,
                    icon: const Icon(Icons.refresh),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Server Configuration Section
            if (!_isConnected) ...[
              Text(
                'Server Configuration',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: TextField(
                      controller: _serverController,
                      decoration: const InputDecoration(
                        labelText: 'Server Address',
                        hintText: 'localhost or IP address',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _portController,
                      decoration: const InputDecoration(
                        labelText: 'Port',
                        hintText: '3000',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _deviceIdController,
                      decoration: const InputDecoration(
                        labelText: 'Device ID',
                        hintText: 'terminal_001',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _deviceNameController,
                      decoration: const InputDecoration(
                        labelText: 'Device Name',
                        hintText: 'Terminal Device',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _isConnecting ? null : _connectToServer,
                    icon: _isConnecting
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.connect_without_contact),
                    label: Text(_isConnecting ? 'Connecting...' : 'Connect'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Connection Controls (when connected)
            if (_isConnected) ...[
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _isConnecting ? null : _disconnectFromServer,
                    icon: const Icon(Icons.link_off),
                    label: const Text('Disconnect'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Connected to ${_serverController.text}:${_portController.text}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Status Row
            Row(
              children: [
                _StatusIndicator(
                  label: 'Connection',
                  value: _connectionStatus,
                  color: _isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 16),
                _StatusIndicator(
                  label: 'Last Ping',
                  value: _lastPingTime,
                  color: Colors.blue,
                ),
                const SizedBox(width: 16),
                _StatusIndicator(
                  label: 'Relay Status',
                  value: _relayStatus,
                  color: _relayStatus == 'on' ? Colors.green : 
                         _relayStatus == 'off' ? Colors.orange : Colors.grey,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Command Buttons
            if (_isConnected) ...[
              Text(
                'Quick Commands',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _sendPing,
                    icon: const Icon(Icons.network_ping),
                    label: const Text('Ping'),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendRelayCommand('on'),
                    icon: const Icon(Icons.power),
                    label: const Text('Relay ON'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendRelayCommand('off'),
                    icon: const Icon(Icons.power_off),
                    label: const Text('Relay OFF'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _sendRelayCommand('toggle'),
                    icon: const Icon(Icons.swap_horiz),
                    label: const Text('Toggle'),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _getRelayStatus,
                    icon: const Icon(Icons.info),
                    label: const Text('Status'),
                  ),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _sendCustomCommand,
                    icon: const Icon(Icons.code),
                    label: const Text('Custom'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Command History
            Text(
              'Command History',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _commandHistory.isEmpty
                  ? const Center(
                      child: Text(
                        'No commands sent yet',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _commandHistory.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          child: Text(
                            _commandHistory[index],
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

class _StatusIndicator extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatusIndicator({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CustomCommandDialog extends StatefulWidget {
  @override
  State<_CustomCommandDialog> createState() => _CustomCommandDialogState();
}

class _CustomCommandDialogState extends State<_CustomCommandDialog> {
  final _typeController = TextEditingController(text: 'custom');
  final _payloadController = TextEditingController(text: '{"message": "test"}');

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Send Custom Command'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _typeController,
            decoration: const InputDecoration(
              labelText: 'Command Type',
              hintText: 'e.g., custom, test, config_update',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _payloadController,
            decoration: const InputDecoration(
              labelText: 'Payload (JSON)',
              hintText: '{"key": "value"}',
            ),
            maxLines: 4,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            try {
              final payload = jsonDecode(_payloadController.text);
              Navigator.of(context).pop({
                'type': _typeController.text,
                'payload': payload,
              });
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Invalid JSON: $e')),
              );
            }
          },
          child: const Text('Send'),
        ),
      ],
    );
  }
}

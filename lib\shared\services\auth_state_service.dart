import 'dart:async';
import '../core/storage/secure_storage_service.dart';
import '../data/models/auth/auth_result_model.dart';
import '../data/models/user/user_model.dart';


/// Service để quản lý authentication state
/// Sử dụng SecureStorage để persist auth data across app restarts
class AuthStateService {
  static final AuthStateService _instance = AuthStateService._internal();
  factory AuthStateService() => _instance;
  AuthStateService._internal();

  SecureStorageService? _secureStorage;
  bool _initialized = false;

  // Storage keys
  static const String _accessTokenKey = 'auth_access_token';
  static const String _refreshTokenKey = 'auth_refresh_token';
  static const String _tokenTypeKey = 'auth_token_type';
  static const String _expiresAtKey = 'auth_expires_at';
  static const String _userDataKey = 'auth_user_data';
  static const String _authStatusKey = 'auth_status';
  static const String _loginTimestampKey = 'auth_login_timestamp';

  // Stream controllers for state changes
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  final StreamController<UserModel?> _userController = StreamController<UserModel?>.broadcast();

  /// Stream of authentication state changes
  Stream<bool> get authStateStream => _authStateController.stream;

  /// Stream of user data changes
  Stream<UserModel?> get userStream => _userController.stream;

  /// Initialize the service with SecureStorageService
  Future<void> initialize(SecureStorageService secureStorage) async {
    if (_initialized) return;

    _secureStorage = secureStorage;
    _initialized = true;
    // AuthStateService initialized
  }

  /// Store authentication result after successful login
  Future<void> storeAuthResult(AuthResultModel authResult) async {
    _ensureInitialized();

    try {
      // Store access token
      await _secureStorage!.write(_accessTokenKey, authResult.accessToken);

      // Store refresh token if available
      if (authResult.refreshToken != null) {
        await _secureStorage!.write(_refreshTokenKey, authResult.refreshToken!);
      }

      // Store token type
      await _secureStorage!.write(_tokenTypeKey, authResult.tokenType ?? 'Bearer');

      // Store expiration timestamp
      if (authResult.expiresAt != null) {
        await _secureStorage!.write(_expiresAtKey, authResult.expiresAt!.toIso8601String());
      }

      // Store user data
      if (authResult.user != null) {
        await _secureStorage!.writeObject(_userDataKey, authResult.user!.toJson());
      }

      // Store authentication status
      await _secureStorage!.write(_authStatusKey, 'authenticated');

      // Store login timestamp
      await _secureStorage!.write(_loginTimestampKey, DateTime.now().toIso8601String());

      // Notify listeners
      _authStateController.add(true);
      _userController.add(authResult.user);
    } catch (e) {
      // Log error in debug mode only
      throw Exception('Failed to store authentication data: $e');
    }
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    _ensureInitialized();
    return await _secureStorage!.read(_accessTokenKey);
  }

  /// Get stored refresh token
  Future<String?> getRefreshToken() async {
    _ensureInitialized();
    return await _secureStorage!.read(_refreshTokenKey);
  }

  /// Get stored token type
  Future<String?> getTokenType() async {
    _ensureInitialized();
    return await _secureStorage!.read(_tokenTypeKey);
  }

  /// Get token expiration timestamp
  Future<DateTime?> getExpiresAt() async {
    _ensureInitialized();
    final expiresAtString = await _secureStorage!.read(_expiresAtKey);
    if (expiresAtString != null) {
      return DateTime.tryParse(expiresAtString);
    }
    return null;
  }

  /// Get stored user data
  Future<UserModel?> getUser() async {
    _ensureInitialized();
    final userData = await _secureStorage!.readObject(_userDataKey);
    if (userData != null) {
      return UserModel.fromJson(userData);
    }
    return null;
  }

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated() async {
    _ensureInitialized();

    try {
      // Check if access token exists
      final accessToken = await getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        return false;
      }

      // Check if token is expired
      final expiresAt = await getExpiresAt();
      if (expiresAt != null && DateTime.now().isAfter(expiresAt)) {
        // Token expired, clearing auth data
        await clearAuthData();
        return false;
      }

      // Check auth status
      final authStatus = await _secureStorage!.read(_authStatusKey);
      return authStatus == 'authenticated';
    } catch (e) {
      // Error checking authentication
      return false;
    }
  }

  /// Get complete auth result if available and valid
  Future<AuthResultModel?> getAuthResult() async {
    _ensureInitialized();

    try {
      final isAuth = await isAuthenticated();
      if (!isAuth) return null;

      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();
      final tokenType = await getTokenType();
      final expiresAt = await getExpiresAt();
      final user = await getUser();

      if (accessToken == null) return null;

      return AuthResultModel(
        accessToken: accessToken,
        refreshToken: refreshToken,
        tokenType: tokenType,
        expiresAt: expiresAt,
        user: user,
      );
    } catch (e) {
      // Error getting auth result
      return null;
    }
  }

  /// Clear all authentication data (logout)
  Future<void> clearAuthData() async {
    _ensureInitialized();

    try {
      await Future.wait([
        _secureStorage!.delete(_accessTokenKey),
        _secureStorage!.delete(_refreshTokenKey),
        _secureStorage!.delete(_tokenTypeKey),
        _secureStorage!.delete(_expiresAtKey),
        _secureStorage!.delete(_userDataKey),
        _secureStorage!.delete(_authStatusKey),
        _secureStorage!.delete(_loginTimestampKey),
      ]);

      // Notify listeners
      _authStateController.add(false);
      _userController.add(null);
    } catch (e) {
      // Failed to clear auth data
      throw Exception('Failed to clear authentication data: $e');
    }
  }

  /// Check if token will expire soon (within 5 minutes)
  Future<bool> isTokenExpiringSoon() async {
    final expiresAt = await getExpiresAt();
    if (expiresAt == null) return false;

    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return expiresAt.isBefore(fiveMinutesFromNow);
  }

  /// Get time until token expires
  Future<Duration?> getTimeUntilExpiry() async {
    final expiresAt = await getExpiresAt();
    if (expiresAt == null) return null;

    final now = DateTime.now();
    if (now.isAfter(expiresAt)) return Duration.zero;

    return expiresAt.difference(now);
  }

  /// Get login timestamp
  Future<DateTime?> getLoginTimestamp() async {
    _ensureInitialized();
    final timestampString = await _secureStorage!.read(_loginTimestampKey);
    if (timestampString != null) {
      return DateTime.tryParse(timestampString);
    }
    return null;
  }

  /// Ensure service is initialized
  void _ensureInitialized() {
    if (!_initialized || _secureStorage == null) {
      throw Exception('AuthStateService not initialized. Call initialize() first.');
    }
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
    _userController.close();
  }
}

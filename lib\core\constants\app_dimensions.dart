library;

/// Configurable dimensions and spacing for the application
///
/// This class now uses the flexible configuration system instead of hardcoded values.
/// All dimensions can be configured through environment variables, configuration files,
/// or runtime changes through the admin interface.

import '../../shared/core/config/config_helper.dart';

class AppDimensions {
  AppDimensions._();

  // Screen Dimensions
  static double get screenWidth => ConfigHelper.getValue('ui.screen_width', 375.0);
  static double get screenHeight => ConfigHelper.getValue('ui.screen_height', 815.0);

  // Spacing
  static double get spacing2 => ConfigHelper.getValue('ui.spacing_2', 2.0);
  static double get spacing4 => ConfigHelper.getValue('ui.spacing_4', 4.0);
  static double get spacing6 => ConfigHelper.getValue('ui.spacing_6', 6.0);
  static double get spacing8 => ConfigHelper.getValue('ui.spacing_8', 8.0);
  static double get spacing12 => ConfigHelper.getValue('ui.spacing_12', 12.0);
  static double get spacing16 => ConfigHelper.getValue('ui.spacing_16', 16.0);
  static double get spacing20 => ConfigHelper.getValue('ui.spacing_20', 20.0);
  static double get spacing24 => ConfigHelper.getValue('ui.spacing_24', 24.0);
  static double get spacing32 => ConfigHelper.getValue('ui.spacing_32', 32.0);
  static double get spacing48 => ConfigHelper.getValue('ui.spacing_48', 48.0);

  // Padding
  static double get paddingXS => ConfigHelper.getValue('ui.padding_xs', 4.0);
  static double get paddingS => ConfigHelper.getValue('ui.padding_s', 8.0);
  static double get paddingM => ConfigHelper.getValue('ui.padding_m', 16.0);
  static double get paddingL => ConfigHelper.getValue('ui.padding_l', 24.0);
  static double get paddingXL => ConfigHelper.getValue('ui.padding_xl', 32.0);

  // Margin
  static double get marginXS => ConfigHelper.getValue('ui.margin_xs', 4.0);
  static double get marginS => ConfigHelper.getValue('ui.margin_s', 8.0);
  static double get marginM => ConfigHelper.getValue('ui.margin_m', 16.0);
  static double get marginL => ConfigHelper.getValue('ui.margin_l', 24.0);
  static double get marginXL => ConfigHelper.getValue('ui.margin_xl', 32.0);

  // Border Radius
  static double get radiusXS => ConfigHelper.getValue('ui.radius_xs', 4.0);
  static double get radiusS => ConfigHelper.getValue('ui.radius_s', 6.0);
  static double get radiusM => ConfigHelper.getValue('ui.radius_m', 8.0);
  static double get radiusL => ConfigHelper.getValue('ui.radius_l', 12.0);
  static double get radiusXL => ConfigHelper.getValue('ui.radius_xl', 16.0);
  static double get radiusXXL => ConfigHelper.getValue('ui.radius_xxl', 24.0);
  static double get radiusRound => ConfigHelper.getValue('ui.radius_round', 50.0);

  // Border Width
  static const double borderThin = 0.5;
  static const double borderNormal = 1.0;
  static const double borderThick = 1.5;
  static const double borderBold = 2.0;

  // Icon Sizes
  static const double iconXS = 12.0;
  static const double iconS = 16.0;
  static const double iconM = 20.0;
  static const double iconL = 24.0;
  static const double iconXL = 32.0;
  static const double iconXXL = 48.0;

  // Button Heights
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  // Input Heights
  static const double inputHeight = 40.0;
  static const double inputHeightL = 48.0;

  // Avatar Sizes
  static const double avatarS = 32.0;
  static const double avatarM = 42.0;
  static const double avatarL = 56.0;
  static const double avatarXL = 72.0;

  // Logo Sizes
  static const double logoSmallWidth = 48.48;
  static const double logoSmallHeight = 32.0;
  static const double logoLargeWidth = 84.84;
  static const double logoLargeHeight = 56.0;

  // OTP Input
  static const double otpInputSize = 47.83;
  static const double otpInputHeight = 48.0;

  // List Item Heights
  static const double listItemHeight = 72.0;
  static const double listItemMinHeight = 56.0;

  // Notification Heights
  static const double notificationHeight = 56.0;

  // Divider
  static const double dividerThickness = 0.5;

  // Elevation
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  // TabsBar
  static const double tabsBarHeight = 80.0; // Approximate height of TabsBar
}

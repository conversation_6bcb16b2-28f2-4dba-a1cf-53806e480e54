# PowerShell script to run or build the terminal app
# Usage: .\scripts\run_terminal.ps1 [debug|release|build]

param(
    [string]$Action = "debug"
)

Write-Host "🖥️  C-Face Terminal App - $Action" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# Check if Flutter is available
if (-not (Get-Command flutter -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Flutter is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to get dependencies" -ForegroundColor Red
    exit 1
}

switch ($Action.ToLower()) {
    "build" {
        Write-Host "🔨 Building terminal app APK..." -ForegroundColor Green
        Write-Host "🖥️  Target: lib/apps/terminal/main_terminal.dart" -ForegroundColor Gray
        Write-Host "🎯 Optimized for: Kiosk/Terminal devices" -ForegroundColor Gray
        Write-Host ""

        flutter build apk --target lib/apps/terminal/main_terminal.dart --flavor terminal

        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✅ Build successful!" -ForegroundColor Green
            Write-Host "📦 APK location: build/app/outputs/flutter-apk/app-terminal-release.apk" -ForegroundColor Gray
        } else {
            Write-Host ""
            Write-Host "❌ Build failed!" -ForegroundColor Red
            exit 1
        }
    }
    "debug" {
        # Check for connected devices
        Write-Host "📱 Checking for connected devices..." -ForegroundColor Yellow
        flutter devices

        Write-Host "🏃 Starting terminal app in debug mode..." -ForegroundColor Green
        Write-Host "🖥️  Target: lib/apps/terminal/main_terminal.dart" -ForegroundColor Gray
        Write-Host "🔧 Mode: Debug (hot reload enabled)" -ForegroundColor Gray
        Write-Host "🎯 Optimized for: Kiosk/Terminal devices" -ForegroundColor Gray
        Write-Host "🚀 Engine: MediaPipe BlazeFace (default)" -ForegroundColor Magenta
        Write-Host ""

        # Try Android first (default)
        Write-Host "🤖 Attempting to run on Android device..." -ForegroundColor Cyan
        Write-Host "Press 'r' to hot reload, 'R' to hot restart, 'q' to quit" -ForegroundColor Gray
        Write-Host ""

        flutter run --target lib/apps/terminal/main_terminal.dart --debug --flavor terminal

        # If Android fails, suggest alternatives
        if ($LASTEXITCODE -ne 0) {
            Write-Host ""
            Write-Host "❌ Android run failed. You can try alternatives:" -ForegroundColor Red
            Write-Host "   Web: flutter run --target lib/apps/terminal/main_terminal.dart -d chrome" -ForegroundColor Yellow
            Write-Host "   Windows: flutter run --target lib/apps/terminal/main_terminal.dart -d windows" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "💡 To fix Android issues:" -ForegroundColor Cyan
            Write-Host "   1. Check connected devices: flutter devices" -ForegroundColor Gray
            Write-Host "   2. Check Android setup: flutter doctor" -ForegroundColor Gray
            Write-Host "   3. Try cleaning: flutter clean && flutter pub get" -ForegroundColor Gray
        }
    }
    "release" {
        # Check for connected devices
        Write-Host "📱 Checking for connected devices..." -ForegroundColor Yellow
        flutter devices

        Write-Host "🏃 Starting terminal app in release mode..." -ForegroundColor Green
        Write-Host "🖥️  Target: lib/apps/terminal/main_terminal.dart" -ForegroundColor Gray
        Write-Host "🔧 Mode: Release (optimized)" -ForegroundColor Gray
        Write-Host "🎯 Optimized for: Kiosk/Terminal devices" -ForegroundColor Gray
        Write-Host "🚀 Engine: MediaPipe BlazeFace (default)" -ForegroundColor Magenta
        Write-Host ""

        flutter run --target lib/apps/terminal/main_terminal.dart --release --flavor terminal
    }
    "web" {
        Write-Host "🌐 Starting terminal app on web..." -ForegroundColor Green
        Write-Host "🖥️  Target: lib/apps/terminal/main_terminal.dart" -ForegroundColor Gray
        Write-Host "🔧 Mode: Debug (web)" -ForegroundColor Gray
        Write-Host "🚀 Engine: MediaPipe BlazeFace (default)" -ForegroundColor Magenta
        Write-Host ""

        flutter run --target lib/apps/terminal/main_terminal.dart -d chrome
    }
    "windows" {
        Write-Host "🪟 Starting terminal app on Windows..." -ForegroundColor Green
        Write-Host "🖥️  Target: lib/apps/terminal/main_terminal.dart" -ForegroundColor Gray
        Write-Host "🔧 Mode: Debug (windows)" -ForegroundColor Gray
        Write-Host "🚀 Engine: MediaPipe BlazeFace (default)" -ForegroundColor Magenta
        Write-Host ""

        flutter run --target lib/apps/terminal/main_terminal.dart -d windows
    }
    "test" {
        Write-Host "🧪 Testing MediaPipe engine..." -ForegroundColor Green
        Write-Host ""

        # Test MediaPipe engine
        Write-Host "🔬 Running MediaPipe engine tests..." -ForegroundColor Cyan
        dart lib/packages/face_recognition/tools/test_mediapipe_engine.dart

        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "✅ MediaPipe engine tests passed!" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "❌ MediaPipe engine tests failed!" -ForegroundColor Red
        }
    }
    "check" {
        Write-Host "🔍 Checking system and dependencies..." -ForegroundColor Green
        Write-Host ""

        # Check Flutter
        Write-Host "📋 Flutter doctor:" -ForegroundColor Cyan
        flutter doctor

        Write-Host ""
        Write-Host "📦 Checking face recognition models..." -ForegroundColor Cyan
        python scripts/check_models.py

        Write-Host ""
        Write-Host "🔧 Analyzing code..." -ForegroundColor Cyan
        flutter analyze --no-fatal-infos

        Write-Host ""
        Write-Host "✅ System check complete!" -ForegroundColor Green
    }
    default {
        Write-Host "❌ Invalid action: $Action" -ForegroundColor Red
        Write-Host "Usage: .\scripts\run_terminal.ps1 [debug|release|build|web|windows|test|check]" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Actions:" -ForegroundColor Cyan
        Write-Host "  debug    - Run in debug mode on Android (default)" -ForegroundColor Gray
        Write-Host "  release  - Run in release mode on Android" -ForegroundColor Gray
        Write-Host "  build    - Build APK for terminal" -ForegroundColor Gray
        Write-Host "  web      - Run on web browser" -ForegroundColor Gray
        Write-Host "  windows  - Run on Windows desktop" -ForegroundColor Gray
        Write-Host "  test     - Test MediaPipe engine" -ForegroundColor Gray
        Write-Host "  check    - Check system and dependencies" -ForegroundColor Gray
        Write-Host ""
        Write-Host "🚀 MediaPipe BlazeFace is used as default face detection engine" -ForegroundColor Magenta
        Write-Host "🔄 Fallback to ML Kit is available if MediaPipe fails" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""
Write-Host "🎉 Script completed!" -ForegroundColor Green

import '../../../core/base/base_provider.dart';
import '../../../domain/entities/user/user.dart';
import '../../../domain/use_cases/user/get_users_use_case.dart';
import '../../../domain/use_cases/user/get_user_by_id_use_case.dart';
import '../../../core/errors/failures.dart';

/// Base user provider that can be extended by mobile and terminal apps
/// 
/// Provides common user management functionality including:
/// - User CRUD operations
/// - User list management with pagination
/// - User search and filtering
/// - User profile management
abstract class BaseUserProvider extends BaseProvider with PaginationMixin<User> {
  final GetUsersUseCase _getUsersUseCase;
  final GetUserByIdUseCase _getUserByIdUseCase;

  BaseUserProvider({
    required GetUsersUseCase getUsersUseCase,
    required GetUserByIdUseCase getUserByIdUseCase,
  }) : _getUsersUseCase = getUsersUseCase,
       _getUserByIdUseCase = getUserByIdUseCase;

  // ============================================================================
  // STATE VARIABLES
  // ============================================================================
  
  User? _selectedUser;
  String _searchQuery = '';
  Map<String, dynamic> _filters = {};

  // ============================================================================
  // GETTERS
  // ============================================================================
  
  /// Currently selected user
  User? get selectedUser => _selectedUser;
  
  /// Current search query
  String get searchQuery => _searchQuery;
  
  /// Current filters
  Map<String, dynamic> get filters => Map.unmodifiable(_filters);
  
  /// Get users list (from pagination mixin)
  List<User> get users => items;

  // ============================================================================
  // USER CRUD OPERATIONS
  // ============================================================================
  
  /// Load users with pagination
  Future<void> loadUsers({
    int page = 1,
    int limit = 20,
    String? search,
    Map<String, dynamic>? filters,
  }) async {
    if (page == 1) {
      setLoading(true);
      resetPagination();
    } else {
      setLoadingMore(true);
    }
    
    // Update search and filters
    if (search != null) _searchQuery = search;
    if (filters != null) _filters = filters;
    
    final result = await _getUsersUseCase(GetUsersParams(
      page: page,
      limit: limit,
      search: _searchQuery.isNotEmpty ? _searchQuery : null,
    ));

    result.fold(
      (failure) => _handleUserError(failure),
      (usersList) => _handleUsersLoaded(usersList, page == 1),
    );
  }
  
  /// Get user by ID
  Future<void> getUserById(String userId) async {
    setLoading(true);
    
    final result = await _getUserByIdUseCase(userId);
    
    result.fold(
      (failure) => _handleUserError(failure),
      (user) => _handleUserSelected(user),
    );
  }
  
  /// Create new user - subclasses should implement with proper parameters
  Future<bool> createUser(Map<String, dynamic> userData) async {
    // This is a simplified version - subclasses should implement
    // with proper CreateUserParams from domain layer
    setLoading(true);
    setError(ValidationFailure('Create user not implemented in base provider'));
    return false;
  }

  /// Update existing user - subclasses should implement with proper parameters
  Future<bool> updateUser(String userId, Map<String, dynamic> userData) async {
    // This is a simplified version - subclasses should implement
    // with proper UpdateUserParams from domain layer
    setLoading(true);
    setError(ValidationFailure('Update user not implemented in base provider'));
    return false;
  }

  // ============================================================================
  // SEARCH AND FILTERING
  // ============================================================================
  
  /// Search users
  Future<void> searchUsers(String query) async {
    _searchQuery = query;
    await loadUsers(page: 1, search: query);
  }
  
  /// Apply filters
  Future<void> applyFilters(Map<String, dynamic> filters) async {
    _filters = filters;
    await loadUsers(page: 1, filters: filters);
  }
  
  /// Clear search and filters
  Future<void> clearSearchAndFilters() async {
    _searchQuery = '';
    _filters.clear();
    await loadUsers(page: 1);
  }

  // ============================================================================
  // PAGINATION OVERRIDE METHODS
  // ============================================================================
  
  @override
  Future<void> loadNextPage() async {
    if (!hasMore || isLoadingMore) return;
    await loadUsers(page: currentPage + 1);
  }
  
  @override
  Future<void> refresh() async {
    await loadUsers(page: 1);
  }

  // ============================================================================
  // ABSTRACT METHODS (TO BE IMPLEMENTED BY SUBCLASSES)
  // ============================================================================
  
  /// Handle app-specific user creation success
  void onUserCreated(User user) {}
  
  /// Handle app-specific user update success
  void onUserUpdated(User user) {}
  
  /// Handle app-specific user selection
  void onUserSelected(User user) {}
  
  /// Handle app-specific user errors
  void onUserError(Failure failure) {}

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// Handle users loaded successfully
  void _handleUsersLoaded(List<User> usersList, bool isFirstPage) {
    // Since the domain use case doesn't return pagination info,
    // we'll create a simple pagination structure
    final currentPageNum = isFirstPage ? 1 : currentPage + 1;
    final hasMoreItems = usersList.length >= 20; // Assume more if we got a full page

    updatePaginationData(
      newItems: usersList,
      currentPage: currentPageNum,
      totalPages: hasMoreItems ? currentPageNum + 1 : currentPageNum,
      totalItems: isFirstPage ? usersList.length : totalItems + usersList.length,
      append: !isFirstPage,
    );

    setSuccess();
  }
  
  /// Handle user selected
  void _handleUserSelected(User user) {
    _selectedUser = user;
    setSuccess();
    
    // Call app-specific handler
    onUserSelected(user);
  }
  

  
  /// Handle user operation error
  void _handleUserError(Failure failure) {
    setError(failure);
    
    // Call app-specific error handler
    onUserError(failure);
  }

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================
  
  /// Initialize user provider
  Future<void> initialize() async {
    await loadUsers();
  }
  
  @override
  void dispose() {
    _selectedUser = null;
    _searchQuery = '';
    _filters.clear();
    super.dispose();
  }
  
  @override
  Future<void> retry() async {
    if (hasError) {
      clearStates();
      await loadUsers(page: currentPage);
    }
  }
  
  @override
  void reset() {
    super.reset();
    _selectedUser = null;
    _searchQuery = '';
    _filters.clear();
    resetPagination();
    notifyListeners();
  }
}

/// Paginated users result
class PaginatedUsers {
  final List<User> users;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  
  const PaginatedUsers({
    required this.users,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
  });
}



<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical">
    <include layout="@layout/titlebar" />

    <Button
        android:id="@+id/bt_relay"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="Relay Test" />

    <Button
        android:id="@+id/bt_led"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="LED Test" />

    <Button
        android:id="@+id/bt_nfc"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="NFC Test" />

    <Button
        android:id="@+id/bt_rs485"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="RS485 Test" />


    <Button
        android:id="@+id/bt_wiegand"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="Wiegand Test" />

    <Button
        android:id="@+id/bt_qrcode_enable"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:onClick="onclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="Qrcode Enable Test" />


</LinearLayout>
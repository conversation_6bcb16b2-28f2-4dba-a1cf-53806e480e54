import 'dart:async';
import 'dart:ui';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../models/camera_config.dart';
import '../models/snapdragon_optimized_config.dart';
import '../models/lightweight_ml_config.dart';
import '../utils/performance_monitor.dart';
import '../utils/telpo_f8_optimizer.dart';
import '../utils/error_safe_handler.dart';
// import '../utils/image_processing_optimizer.dart'; // TEMPORARILY DISABLED

// Import hybrid face detection system
import '../../packages/face_recognition/src/detection/hybrid_detector.dart';
import '../../packages/face_recognition/src/detection/detection_engine.dart' as hybrid;
import '../../packages/face_recognition/src/terminal/telpo_f8/hardware_controller.dart';

enum FaceDirection { front, top, bottom, left, right, unknown }

class FaceDetectionProvider extends ChangeNotifier with ErrorSafeMixin {
  // Legacy ML Kit system
  FaceDetector? _faceDetector;
  List<Face> _faces = [];
  Face? _bestFace;

  // Hybrid detection system
  HybridDetector? _hybridDetector;
  List<hybrid.DetectedFace> _hybridFaces = [];
  hybrid.DetectedFace? _bestHybridFace;
  bool _useHybridSystem = false;

  // Common properties
  FaceDirection _faceDirection = FaceDirection.unknown;
  String _faceQualityLevel = 'Poor';
  double _faceQuality = 0.0;
  bool _isDetecting = false;

  // Callback for face detection state changes (for relay control)
  Function(bool hasFace)? _onFaceDetectionStateChanged;
  bool _isDisposing = false;

  // Store latest camera image for face recognition
  CameraImage? _latestCameraImage;
  CameraDescription? _latestCameraDescription;

  // Performance monitoring
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  bool _performanceMonitoringEnabled = kDebugMode;
  bool _isDisposed = false;
  bool _isInitializing = false;
  int _frameCount = 0;
  DateTime _lastDetectionTime = DateTime.now();
  FaceDetectionConfig _config = FaceDetectionConfig.defaultConfig;

  // ULTRA-LIGHTWEIGHT: Frame skipping for performance optimization
  int _frameSkipCounter = 0;
  static const int _frameSkipInterval = 2; // Process every 2nd frame only

  // Error recovery
  int _consecutiveErrors = 0;
  static const int _maxConsecutiveErrors = 5;

  // Getters - unified interface for both systems
  List<Face> get faces => _useHybridSystem ? _convertHybridFacesToMLKit(_hybridFaces) : _faces;
  Face? get bestFace => _useHybridSystem ? _convertHybridFaceToMLKit(_bestHybridFace) : _bestFace;
  FaceDirection get faceDirection => _faceDirection;
  String get faceQualityLevel => _faceQualityLevel;
  double get faceQuality => _faceQuality;
  bool get isDetecting => _isDetecting;
  bool get isDisposing => _isDisposing;
  bool get isDisposed => _isDisposed;

  // Hybrid system specific getters
  List<hybrid.DetectedFace> get hybridFaces => _hybridFaces;
  hybrid.DetectedFace? get bestHybridFace => _bestHybridFace;
  bool get isUsingHybridSystem => _useHybridSystem;

  // Set callback for face detection state changes
  void setFaceDetectionStateCallback(Function(bool hasFace)? callback) {
    _onFaceDetectionStateChanged = callback;
  }
  bool get hasFaces => _faces.isNotEmpty;
  bool get hasBestFace => _bestFace != null;
  FaceDetectionConfig get config => _config;
  int get consecutiveErrors => _consecutiveErrors;

  // Access to latest camera image for face recognition
  CameraImage? get latestCameraImage => _latestCameraImage;
  CameraDescription? get latestCameraDescription => _latestCameraDescription;

  // Initialize face detector with configuration
  Future<void> initializeFaceDetector({
    FaceDetectorMode performanceMode = FaceDetectorMode.fast,
    bool enableTracking = true,
    bool enableLandmarks = false,
    bool enableContours = false,
    bool enableClassification = true,
    FaceDetectionConfig? config,
  }) async {
    if (_isDisposed) {
      debugPrint('⚠️ Face detector already disposed, cannot initialize');
      return;
    }

    if (_isInitializing) {
      debugPrint('⏳ Face detector initialization in progress, waiting...');
      return;
    }

    // Prevent re-initialization if already initialized
    if (_faceDetector != null) {
      debugPrint('✅ Face detector already initialized, skipping');
      return;
    }

    _isInitializing = true;

    if (config != null) {
      _config = config;
    }

    try {
      debugPrint('🎯 Đang khởi tạo bộ phát hiện khuôn mặt...');

      // Add delay to prevent rapid re-initialization
      await Future.delayed(const Duration(milliseconds: 100));

      if (_isDisposed) return;

      // Check if device should use hybrid system
      if (await _shouldUseHybridSystem()) {
        try {
          await _initializeHybridSystem();
          debugPrint('✅ Hybrid Face Detection System initialized successfully');
          return;
        } catch (e) {
          debugPrint('⚠️ Hybrid system failed, falling back to ML Kit: $e');
          _useHybridSystem = false;
        }
      }

      // Fallback to ML Kit system
      debugPrint('🔄 Using ML Kit Face Detection System');

      // Use TELPO F8 optimized ML Kit Configuration
      final options = TelpoF8Optimizer.getRecommendedMLConfig();

      debugPrint('🚀 ULTRA-LIGHTWEIGHT ML Kit Configuration Applied');
      debugPrint(LightweightMLConfig.getConfigSummary(options));

      // Print hardware recommendations
      final recommendations = LightweightMLConfig.getHardwareRecommendations();
      debugPrint('💡 Hardware Recommendations:');
      debugPrint('   Camera: ${recommendations['camera']['resolution']}');
      debugPrint('   Format: ${recommendations['camera']['format']}');
      debugPrint('   Processing: ${recommendations['processing']['frameSkipping']}');

      _faceDetector = FaceDetector(options: options);
      _consecutiveErrors = 0; // Reset error count on successful initialization

      debugPrint('✅ Bộ phát hiện khuôn mặt đã được khởi tạo thành công');
    } catch (e) {
      _consecutiveErrors++;
      debugPrint('❌ Failed to initialize face detector: $e');
      if (kDebugMode) {
        print('Failed to initialize face detector: $e');
      }
    } finally {
      _isInitializing = false;
    }
  }

  // Update configuration
  void updateConfiguration(FaceDetectionConfig newConfig) {
    if (_isDisposed) return;
    if (newConfig != _config) {
      _config = newConfig;
      notifyListeners();
    }
  }

  // Process camera image for face detection
  Future<void> detectFacesFromImage(
    CameraImage cameraImage,
    CameraDescription camera,
  ) async {
    // Check if we should use hybrid system
    if (_useHybridSystem && _hybridDetector != null) {
      await _detectFacesWithHybridSystem(cameraImage, camera);
      return;
    }

    // Fallback to ML Kit system
    if (_faceDetector == null || _isDetecting || _isDisposing || _isDisposed) return;

    // Store latest camera image for face recognition
    _latestCameraImage = cameraImage;
    _latestCameraDescription = camera;

    // Performance monitoring - record frame (always record for FPS calculation)
    if (_performanceMonitoringEnabled) {
      _performanceMonitor.recordFrame();
    }

    // Check for too many consecutive errors
    if (_consecutiveErrors >= _maxConsecutiveErrors) {
      if (kDebugMode) {
        print('Too many consecutive errors, skipping detection');
      }
      return;
    }

    // ULTRA-LIGHTWEIGHT: Aggressive frame skipping for performance
    _frameCount++;

    // ERROR-SAFE: FPS monitoring side effects
    safeSideEffect(() {
      TelpoF8Optimizer.recordFrame();
    }, operationName: 'TelpoF8 FPS Recording');

    if (!TelpoF8Optimizer.shouldProcessFrame(_frameCount)) {
      // ERROR-SAFE: Performance monitoring side effects
      safeSideEffect(() {
        if (_performanceMonitoringEnabled) {
          _performanceMonitor.recordFrameSkip();
          _performanceMonitor.recordFrame();
        }
      }, operationName: 'Performance Monitor Frame Skip');
      return; // Skip this frame based on Telpo F8 optimization
    }

    // ERROR-SAFE: Record processed frame
    safeSideEffect(() {
      TelpoF8Optimizer.recordProcessedFrame();
    }, operationName: 'TelpoF8 Processed Frame Recording');

    debugPrint('🎯 Processing frame $_frameCount (Telpo F8 optimized with tracking)');

    // Time-based throttling to prevent overload
    final now = DateTime.now();
    if (now.difference(_lastDetectionTime) < _config.minDetectionInterval) return;

    _isDetecting = true;
    _lastDetectionTime = now;

    // Performance monitoring - start frame processing
    if (_performanceMonitoringEnabled) {
      _performanceMonitor.startFrameProcessing();
    }

    try {
      // Use direct conversion (ImageProcessingOptimizer temporarily disabled)
      // final inputImage = ImageProcessingOptimizer.convertCameraImageToInputImage(cameraImage, camera);

      // DIRECT CONVERSION - Ultra-lightweight approach
      final inputImage = InputImage.fromBytes(
        bytes: cameraImage.planes[0].bytes,  // Y plane only for minimal processing
        metadata: InputImageMetadata(
          size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
          rotation: InputImageRotation.rotation0deg,  // No rotation for speed
          format: InputImageFormat.nv21,              // Most compatible format
          bytesPerRow: cameraImage.planes[0].bytesPerRow,
        ),
      );

      debugPrint('🚫 Direct conversion: ${cameraImage.planes[0].bytes.length} bytes, NV21 format');

      // ERROR-SAFE: Performance monitoring - start face detection
      safeSideEffect(() {
        if (_performanceMonitoringEnabled) {
          _performanceMonitor.startFaceDetection();
        }
      }, operationName: 'Performance Monitor Start Detection');

      // ERROR-SAFE: ML Kit face detection with timeout protection
      final faces = await safeExecute(
        () => _faceDetector!.processImage(inputImage).timeout(
          const Duration(seconds: 3), // 3s timeout for ML Kit
        ),
        operationName: 'ML Kit Face Detection',
        fallbackValue: <Face>[],
      ) ?? <Face>[];

      // ERROR-SAFE: Performance monitoring - end face detection
      safeSideEffect(() {
        if (_performanceMonitoringEnabled) {
          _performanceMonitor.endFaceDetection();
        }
      }, operationName: 'Performance Monitor End Detection');

      // Reset error count on successful detection
      _consecutiveErrors = 0;

      // Update faces list
      final bool hadFaceBefore = _faces.isNotEmpty;
      _faces = faces;
      final bool hasFaceNow = _faces.isNotEmpty;

      // Find and process the best quality face
      final Face? newBestFace = getBestFace();

      // Update best face and related information
      bool shouldNotify = false;

      if (_areFacesDifferent(newBestFace, _bestFace)) {
        _bestFace = newBestFace;
        shouldNotify = true;

        if (_bestFace != null) {
          // Update face direction
          final newDirection = detectFaceDirection(
            _bestFace!.headEulerAngleX,
            _bestFace!.headEulerAngleY,
          );

          if (newDirection != _faceDirection) {
            _faceDirection = newDirection;
          }

          // Update face quality information
          final newQuality = getFaceQuality(_bestFace!);
          final newQualityLevel = getFaceQualityLevel(_bestFace!);

          if (newQuality != _faceQuality ||
              newQualityLevel != _faceQualityLevel) {
            _faceQuality = newQuality;
            _faceQualityLevel = newQualityLevel;
          }
        } else {
          // Reset when no best face found
          _faceDirection = FaceDirection.unknown;
          _faceQuality = 0.0;
          _faceQualityLevel = 'Poor';
        }
      }

      // ERROR-SAFE: Trigger relay callback if face detection state changed
      if (hadFaceBefore != hasFaceNow && _onFaceDetectionStateChanged != null) {
        safeSideEffect(() {
          _onFaceDetectionStateChanged!(hasFaceNow);
        }, operationName: 'Face Detection State Callback');
      }

      // ERROR-SAFE: Notify listeners
      if (shouldNotify && !_isDisposed) {
        safeSideEffect(() {
          notifyListeners();
        }, operationName: 'Notify Listeners');
      }
    } catch (e) {
      _consecutiveErrors++;
      // Silently ignore timeout exceptions to avoid log spam
      if (kDebugMode && !e.toString().contains('TimeoutException')) {
        debugPrint('Face detection error: $e (consecutive errors: $_consecutiveErrors)');
      }
    } finally {
      _isDetecting = false;

      // Performance monitoring - end frame processing
      if (_performanceMonitoringEnabled) {
        _performanceMonitor.endFrameProcessing();
      }
    }
  }

  // Compare faces more accurately
  bool _areFacesDifferent(Face? face1, Face? face2) {
    if (face1 == null && face2 == null) return false;
    if (face1 == null || face2 == null) return true;

    // Compare bounding boxes with tolerance
    const tolerance = 10.0;
    final box1 = face1.boundingBox;
    final box2 = face2.boundingBox;

    return (box1.left - box2.left).abs() > tolerance ||
           (box1.top - box2.top).abs() > tolerance ||
           (box1.width - box2.width).abs() > tolerance ||
           (box1.height - box2.height).abs() > tolerance;
  }



  // Face quality assessment using configuration
  double getFaceQuality(Face face) {
    double quality = 0.0;

    // Size assessment - main factor for fast mode
    final faceArea = face.boundingBox.width * face.boundingBox.height;

    // Reject faces that are too small
    if (faceArea < _config.minFaceArea) {
      return 0.0; // Poor quality for small faces
    }

    // Size quality score (0.0 to 1.0)
    final sizeScore = (faceArea / _config.idealFaceArea).clamp(0.0, 1.0);
    quality += 0.7 * sizeScore; // 70% weight for size

    // Head pose assessment (30% weight) - if available
    if (face.headEulerAngleY != null && face.headEulerAngleZ != null) {
      final yaw = (face.headEulerAngleY!).abs();
      final roll = (face.headEulerAngleZ!).abs();

      // Penalize extreme head poses using config thresholds
      final poseScore =
          (1.0 - (yaw / _config.maxYawAngle).clamp(0.0, 1.0)) *
          (1.0 - (roll / _config.maxRollAngle).clamp(0.0, 1.0));
      quality += 0.3 * poseScore;
    } else {
      // If no pose data, give moderate score
      quality += 0.2;
    }

    return quality.clamp(0.0, 1.0);
  }

  // Get best quality face using configuration threshold
  Face? getBestFace() {
    if (_faces.isEmpty) return null;

    Face? bestFace;
    double bestQuality = 0.0;

    for (final face in _faces) {
      final quality = getFaceQuality(face);
      if (quality > bestQuality) {
        bestQuality = quality;
        bestFace = face;
      }
    }

    // Use configurable threshold
    return bestQuality > _config.qualityThreshold ? bestFace : null;
  }

  // Get face quality level
  String getFaceQualityLevel(Face face) {
    final quality = getFaceQuality(face);
    if (quality >= 0.85) return 'Excellent';
    if (quality >= 0.7) return 'Good';
    if (quality >= 0.55) return 'Fair';
    if (quality >= 0.4) return 'Acceptable';
    return 'Poor';
  }

  // Detect face direction using configurable thresholds
  FaceDirection detectFaceDirection(double? eulerX, double? eulerY) {
    if (eulerX == null || eulerY == null) return FaceDirection.unknown;

    final centerTolerance = _config.centerAngleTolerance;
    final directionTolerance = _config.directionAngleTolerance;

    if (eulerX.abs() <= centerTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.front;
    }
    if (eulerX > directionTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.top;
    }
    if (eulerX < -directionTolerance && eulerY.abs() <= centerTolerance) {
      return FaceDirection.bottom;
    }
    if (eulerX.abs() <= centerTolerance && eulerY < -directionTolerance) {
      return FaceDirection.left;
    }
    if (eulerX.abs() <= centerTolerance && eulerY > directionTolerance) {
      return FaceDirection.right;
    }
    return FaceDirection.unknown;
  }

  // Get detailed face angle information for UI display
  Map<String, dynamic> getFaceAngleInfo() {
    if (_bestFace == null) {
      return {
        'hasAngles': false,
        'eulerX': 0.0,
        'eulerY': 0.0,
        'eulerZ': 0.0,
        'direction': FaceDirection.unknown,
        'directionText': 'Unknown',
      };
    }

    final eulerX = _bestFace!.headEulerAngleX ?? 0.0;
    final eulerY = _bestFace!.headEulerAngleY ?? 0.0;
    final eulerZ = _bestFace!.headEulerAngleZ ?? 0.0;

    String directionText;
    switch (_faceDirection) {
      case FaceDirection.front:
        directionText = 'Center';
        break;
      case FaceDirection.top:
        directionText = 'Looking Up';
        break;
      case FaceDirection.bottom:
        directionText = 'Looking Down';
        break;
      case FaceDirection.left:
        directionText = 'Looking Left';
        break;
      case FaceDirection.right:
        directionText = 'Looking Right';
        break;
      case FaceDirection.unknown:
        directionText = 'Unknown';
        break;
    }

    return {
      'hasAngles': true,
      'eulerX': eulerX,
      'eulerY': eulerY,
      'eulerZ': eulerZ,
      'direction': _faceDirection,
      'directionText': directionText,
    };
  }

  // Clear detection results
  void clearFaces() {
    bool shouldNotify = false;

    if (_faces.isNotEmpty) {
      _faces.clear();
      shouldNotify = true;
    }

    if (_bestFace != null) {
      _bestFace = null;
      _faceDirection = FaceDirection.unknown;
      _faceQuality = 0.0;
      _faceQualityLevel = 'Poor';
      shouldNotify = true;
    }

    if (shouldNotify && !_isDisposed) {
      notifyListeners();
    }
  }

  // Reset error count (for recovery)
  void resetErrorCount() {
    _consecutiveErrors = 0;
    if (kDebugMode) {
      print('Face detection error count reset');
    }
  }

  // Restart face detection pipeline (useful after camera switching)
  Future<void> restartPipeline() async {
    if (_isDisposed || _isInitializing) return;

    debugPrint('🔄 Đang khởi động lại hệ thống phát hiện khuôn mặt...');

    try {
      // Clear current state
      clearFaces();

      // Reset error counters and timing
      _consecutiveErrors = 0;
      _frameCount = 0;
      _lastDetectionTime = DateTime.now().subtract(const Duration(seconds: 1));
      _isDetecting = false;

      // Reset performance monitoring if enabled
      if (_performanceMonitoringEnabled) {
        // Performance monitor will reset naturally with new frames
        debugPrint('📊 Performance monitoring will reset with new frames');
      }

      debugPrint('✅ Face detection pipeline restarted successfully');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error restarting face detection pipeline: $e');
    }
  }

  // Check if detector needs recovery
  bool get needsRecovery => _consecutiveErrors >= _maxConsecutiveErrors;

  // Attempt to recover from errors
  Future<void> attemptRecovery() async {
    if (!needsRecovery) return;

    if (kDebugMode) {
      print('Attempting face detection recovery...');
    }

    try {
      // Dispose current detector
      await _faceDetector?.close();
      _faceDetector = null;

      // Reinitialize with current config
      await initializeFaceDetector(config: _config);

      if (kDebugMode) {
        print('Face detection recovery successful');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Face detection recovery failed: $e');
      }
    }
  }

  // Dispose resources
  @override
  void dispose() {
    if (_isDisposing || _isDisposed) return;

    _isDisposing = true;

    try {
      debugPrint('🧹 Disposing face detection provider...');

      // Stop any ongoing detection
      _isDetecting = false;
      _isInitializing = false;

      // Clear all cached data
      _faces.clear();
      _bestFace = null;
      _hybridFaces.clear();
      _bestHybridFace = null;
      _faceDirection = FaceDirection.unknown;
      _faceQuality = 0.0;
      _faceQualityLevel = 'Poor';
      _consecutiveErrors = 0;
      _frameCount = 0;

      // Close face detector
      if (_faceDetector != null) {
        _faceDetector!.close();
        _faceDetector = null;
      }

      // Dispose hybrid detector
      if (_hybridDetector != null) {
        _hybridDetector!.dispose();
        _hybridDetector = null;
      }

      // Dispose hardware controller
      if (_useHybridSystem) {
        TelpoF8HardwareController.dispose();
      }

      _isDisposed = true;

      // Stop performance monitoring
      if (_performanceMonitoringEnabled) {
        _performanceMonitor.stopMonitoring();
      }

      debugPrint('✅ Face detection provider disposed successfully');
    } catch (e) {
      debugPrint('❌ Error disposing face detection provider: $e');
    } finally {
      _isDisposing = false;
      super.dispose();
    }
  }

  // Performance monitoring methods

  /// Enable or disable performance monitoring
  void setPerformanceMonitoring(bool enabled) {
    if (_performanceMonitoringEnabled == enabled) return;

    _performanceMonitoringEnabled = enabled;

    if (enabled) {
      _performanceMonitor.startMonitoring();
      debugPrint('📊 Performance monitoring enabled for Snapdragon 8 Gen 2');

      // Force initial frame recording to start FPS calculation
      _performanceMonitor.recordFrame();
    } else {
      _performanceMonitor.stopMonitoring();
      debugPrint('📊 Performance monitoring disabled');
    }
  }

  /// Get current performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    return _performanceMonitor.getPerformanceReport();
  }

  /// Get performance monitor instance for external use
  PerformanceMonitor getPerformanceMonitor() {
    return _performanceMonitor;
  }

  /// Print performance summary to debug console
  void printPerformanceSummary() {
    _performanceMonitor.printPerformanceSummary();
  }

  /// Get adaptive configuration based on current performance
  ConfigurationSet getAdaptiveConfiguration() {
    return MobileFaceCaptureOptimizedConfig.getAdaptiveConfig(
      currentFPS: _performanceMonitor.currentFPS,
      averageDetectionTime: _performanceMonitor.averageDetectionTime,
    );
  }

  /// Apply mobile optimized configuration
  Future<void> applyMobileOptimization({
    PerformanceLevel performanceLevel = PerformanceLevel.balanced,
  }) async {
    final config = MobileFaceCaptureOptimizedConfig.getRecommendedConfig(
      performanceLevel: performanceLevel,
    );

    // Update face detection config
    updateConfiguration(config.faceDetection);

    // Reinitialize with optimized ML Kit options
    if (_faceDetector != null) {
      await _faceDetector!.close();
      _faceDetector = null;
    }

    await initializeFaceDetector(
      performanceMode: config.mlKit.performanceMode,
      enableTracking: config.mlKit.enableTracking,
      enableLandmarks: config.mlKit.enableLandmarks,
      enableContours: config.mlKit.enableContours,
      enableClassification: config.mlKit.enableClassification,
      config: config.faceDetection,
    );

    debugPrint('🚀 Applied mobile optimization: $performanceLevel');
  }

  // ============================================================================
  // HYBRID SYSTEM INTEGRATION
  // ============================================================================

  /// Initialize hybrid detection system for Telpo F8
  Future<void> _initializeHybridSystem() async {
    try {
      if (kDebugMode) {
        print('🚀 Initializing Hybrid Face Detection System for Telpo F8...');
      }

      // Initialize hardware controller
      await TelpoF8HardwareController.initialize();
      await TelpoF8HardwareController.optimizeForFaceDetection();

      // Create hybrid detector
      _hybridDetector = await HybridDetector.createForTelpoF8(
        confidenceThreshold: 0.7, // Default confidence threshold
        maxFaces: 3, // Default max faces
        enableFallback: true,
      );

      _useHybridSystem = true;

      if (kDebugMode) {
        print('✅ Hybrid detection system initialized successfully');
        print('📊 Engine info: ${_hybridDetector!.getEngineInfo()}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize hybrid system: $e');
        print('🔄 Falling back to ML Kit...');
      }
      _useHybridSystem = false;
      rethrow;
    }
  }

  /// Check if device should use hybrid system
  Future<bool> _shouldUseHybridSystem() async {
    try {
      // Check if running on Telpo F8
      final result = await Process.run('getprop', ['ro.product.model']);
      final model = result.stdout.toString().toLowerCase();

      final isTelpoF8 = model.contains('telpo') && model.contains('f8');

      if (kDebugMode) {
        print('🔍 Device detection: $model');
        print('📱 Is Telpo F8: $isTelpoF8');
      }

      return isTelpoF8;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Device detection failed: $e');
      }
      return false;
    }
  }

  /// Convert hybrid faces to ML Kit format for backward compatibility
  List<Face> _convertHybridFacesToMLKit(List<hybrid.DetectedFace> hybridFaces) {
    return hybridFaces.map((hybridFace) => _convertHybridFaceToMLKitInternal(hybridFace)).toList();
  }

  /// Convert single hybrid face to ML Kit format
  Face? _convertHybridFaceToMLKit(hybrid.DetectedFace? hybridFace) {
    if (hybridFace == null) return null;
    return _convertHybridFaceToMLKitInternal(hybridFace);
  }

  /// Internal conversion method
  Face _convertHybridFaceToMLKitInternal(hybrid.DetectedFace hybridFace) {
    // Create ML Kit compatible face object
    // Note: This is a simplified conversion - some features may not be available
    return Face(
      boundingBox: Rect.fromLTWH(
        hybridFace.boundingBox.left,
        hybridFace.boundingBox.top,
        hybridFace.boundingBox.width,
        hybridFace.boundingBox.height,
      ),
      landmarks: _convertHybridLandmarks(hybridFace.landmarks),
      contours: const {}, // Not supported in hybrid system yet
      headEulerAngleY: hybridFace.headEulerAngleY,
      headEulerAngleZ: hybridFace.headEulerAngleZ,
      leftEyeOpenProbability: hybridFace.leftEyeOpenProbability,
      rightEyeOpenProbability: hybridFace.rightEyeOpenProbability,
      smilingProbability: hybridFace.smilingProbability,
      trackingId: hybridFace.trackingId,
    );
  }

  /// Convert hybrid landmarks to ML Kit format
  Map<FaceLandmarkType, FaceLandmark> _convertHybridLandmarks(List<hybrid.FaceLandmark>? hybridLandmarks) {
    if (hybridLandmarks == null) return {};

    final landmarks = <FaceLandmarkType, FaceLandmark>{};

    for (final hybridLandmark in hybridLandmarks) {
      final mlkitType = _convertLandmarkType(hybridLandmark.type);
      if (mlkitType != null) {
        // Note: ML Kit FaceLandmark expects Point<int>, but we'll create a compatible object
        // For now, skip landmarks conversion as it's complex and not critical for basic detection
        // landmarks[mlkitType] = FaceLandmark(
        //   type: mlkitType,
        //   position: Point<int>(
        //     hybridLandmark.position.dx.round(),
        //     hybridLandmark.position.dy.round(),
        //   ),
        // );
      }
    }

    return landmarks;
  }

  /// Convert landmark types
  FaceLandmarkType? _convertLandmarkType(hybrid.FaceLandmarkType hybridType) {
    switch (hybridType) {
      case hybrid.FaceLandmarkType.leftEye:
        return FaceLandmarkType.leftEye;
      case hybrid.FaceLandmarkType.rightEye:
        return FaceLandmarkType.rightEye;
      case hybrid.FaceLandmarkType.noseBase:
        return FaceLandmarkType.noseBase;
      case hybrid.FaceLandmarkType.leftEar:
        return FaceLandmarkType.leftEar;
      case hybrid.FaceLandmarkType.rightEar:
        return FaceLandmarkType.rightEar;
      case hybrid.FaceLandmarkType.leftMouth:
        return FaceLandmarkType.leftMouth;
      case hybrid.FaceLandmarkType.rightMouth:
        return FaceLandmarkType.rightMouth;
      case hybrid.FaceLandmarkType.leftCheek:
        return FaceLandmarkType.leftCheek;
      case hybrid.FaceLandmarkType.rightCheek:
        return FaceLandmarkType.rightCheek;
    }
  }

  /// Detect faces using hybrid system
  Future<void> _detectFacesWithHybridSystem(
    CameraImage cameraImage,
    CameraDescription camera,
  ) async {
    if (_isDetecting || _isDisposing || _isDisposed) return;

    // Store latest camera image for face recognition
    _latestCameraImage = cameraImage;
    _latestCameraDescription = camera;

    // Performance monitoring
    if (_performanceMonitoringEnabled) {
      _performanceMonitor.recordFrame();
    }

    _isDetecting = true;
    final now = DateTime.now();
    _lastDetectionTime = now;

    try {
      if (kDebugMode) {
        print('🔍 Hybrid face detection processing...');
      }

      // Use hybrid detector
      final hybridFaces = await _hybridDetector!.detectFaces(cameraImage);

      // Update hybrid faces
      _hybridFaces = hybridFaces;
      _bestHybridFace = hybridFaces.isNotEmpty ? hybridFaces.first : null;

      // Update face quality and direction for compatibility
      if (_bestHybridFace != null) {
        _faceQuality = _bestHybridFace!.confidence;
        _faceQualityLevel = _getFaceQualityLevel(_faceQuality);
        _faceDirection = _calculateFaceDirection(_bestHybridFace!);
      } else {
        _faceQuality = 0.0;
        _faceQualityLevel = 'Poor';
        _faceDirection = FaceDirection.unknown;
      }

      // Trigger face detection state callback
      if (_onFaceDetectionStateChanged != null) {
        _onFaceDetectionStateChanged!(hybridFaces.isNotEmpty);
      }

      // Performance monitoring
      if (_performanceMonitoringEnabled) {
        _performanceMonitor.endFrameProcessing();
      }

      if (kDebugMode) {
        print('✅ Hybrid detection completed: ${hybridFaces.length} faces detected');
        if (_bestHybridFace != null) {
          print('   Best face confidence: ${(_bestHybridFace!.confidence * 100).toStringAsFixed(1)}%');
        }
      }

      notifyListeners();

    } catch (e) {
      _consecutiveErrors++;
      if (kDebugMode) {
        print('❌ Hybrid face detection failed: $e');
      }
    } finally {
      _isDetecting = false;
    }
  }

  /// Calculate face direction from hybrid face
  FaceDirection _calculateFaceDirection(hybrid.DetectedFace face) {
    final yaw = face.headEulerAngleY ?? 0.0;
    final pitch = face.headEulerAngleZ ?? 0.0;

    if (yaw.abs() < 15 && pitch.abs() < 15) {
      return FaceDirection.front;
    } else if (yaw > 15) {
      return FaceDirection.right;
    } else if (yaw < -15) {
      return FaceDirection.left;
    } else if (pitch > 15) {
      return FaceDirection.bottom;
    } else if (pitch < -15) {
      return FaceDirection.top;
    } else {
      return FaceDirection.unknown;
    }
  }

  /// Get face quality level from confidence score
  String _getFaceQualityLevel(double confidence) {
    if (confidence >= 0.9) return 'Excellent';
    if (confidence >= 0.8) return 'Very Good';
    if (confidence >= 0.7) return 'Good';
    if (confidence >= 0.6) return 'Fair';
    if (confidence >= 0.5) return 'Acceptable';
    return 'Poor';
  }
}

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

// Security configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';
const HMAC_TOLERANCE_SECONDS = parseInt(process.env.HMAC_TOLERANCE_SECONDS) || 300; // 5 minutes

class SecurityManager {
  constructor() {
    this.deviceSecrets = new Map(); // deviceId -> secretKey
    this.registeredDevices = new Map(); // deviceId -> device info
  }

  /**
   * Generate a secure secret key for HMAC signing
   */
  generateSecretKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate device ID
   */
  generateDeviceId(prefix = 'dev') {
    const randomPart = crypto.randomBytes(8).toString('hex');
    return `${prefix}-${randomPart}`;
  }

  /**
   * Register a new device
   */
  registerDevice(deviceData) {
    const { device_id, device_type, device_name, hardware_hash, app_version } = deviceData;
    
    // Generate secret key for this device
    const secretKey = this.generateSecretKey();
    
    // Create JWT payload
    const payload = {
      device_id,
      device_type,
      device_name: device_name || `Device ${device_id}`,
      scope: ['relay_control', 'unlock'],
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate JWT
    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    
    // Generate refresh token
    const refreshToken = this.generateSecretKey();

    // Store device info and secret
    const deviceInfo = {
      device_id,
      device_type,
      device_name: device_name || `Device ${device_id}`,
      hardware_hash,
      app_version,
      secret_key: secretKey,
      refresh_token: refreshToken,
      registered_at: new Date().toISOString(),
      last_seen: new Date().toISOString(),
    };

    this.registeredDevices.set(device_id, deviceInfo);
    this.deviceSecrets.set(device_id, secretKey);

    return {
      access_token: accessToken,
      expires_in: this.getJwtExpiresInSeconds(),
      refresh_token: refreshToken,
      relay_endpoint: '/relay/control',
      device_scope: ['relay_control', 'unlock'],
      secret_key: secretKey,
    };
  }

  /**
   * Refresh access token
   */
  refreshAccessToken(refreshToken) {
    // Find device by refresh token
    const device = Array.from(this.registeredDevices.values())
      .find(d => d.refresh_token === refreshToken);

    if (!device) {
      throw new Error('Invalid refresh token');
    }

    // Generate new access token
    const payload = {
      device_id: device.device_id,
      device_type: device.device_type,
      device_name: device.device_name,
      scope: ['relay_control', 'unlock'],
      iat: Math.floor(Date.now() / 1000),
    };

    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // Update last seen
    device.last_seen = new Date().toISOString();

    return {
      access_token: accessToken,
      expires_in: this.getJwtExpiresInSeconds(),
    };
  }

  /**
   * Verify JWT token
   */
  verifyJwt(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Create HMAC signature
   */
  createHmacSignature(secretKey, data) {
    const message = this.canonicalizeData(data);
    return crypto.createHmac('sha256', secretKey).update(message).digest('hex');
  }

  /**
   * Verify HMAC signature
   */
  verifyHmacSignature(deviceId, signature, data) {
    const secretKey = this.deviceSecrets.get(deviceId);
    if (!secretKey) {
      throw new Error('Device not registered or secret key not found');
    }

    const expectedSignature = this.createHmacSignature(secretKey, data);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * Verify timestamp to prevent replay attacks
   */
  verifyTimestamp(timestamp) {
    const now = Math.floor(Date.now() / 1000);
    const diff = Math.abs(now - timestamp);
    return diff <= HMAC_TOLERANCE_SECONDS;
  }

  /**
   * Canonicalize data for consistent signing
   */
  canonicalizeData(data) {
    const sortedKeys = Object.keys(data).sort();
    const parts = [];

    for (const key of sortedKeys) {
      const value = data[key];
      if (value !== null && value !== undefined) {
        let valueStr;
        if (typeof value === 'object' && !Array.isArray(value)) {
          // Recursively canonicalize nested objects
          valueStr = this.canonicalizeData(value);
        } else if (Array.isArray(value)) {
          // JSON encode arrays
          valueStr = JSON.stringify(value);
        } else {
          valueStr = value.toString();
        }
        parts.push(`${key}=${valueStr}`);
      }
    }

    return parts.join('&');
  }

  /**
   * Get JWT expiration time in seconds
   */
  getJwtExpiresInSeconds() {
    // Parse JWT_EXPIRES_IN (e.g., '1h', '30m', '3600s')
    const expiresIn = JWT_EXPIRES_IN;
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn) * 3600;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn) * 60;
    } else if (expiresIn.endsWith('s')) {
      return parseInt(expiresIn);
    } else {
      return parseInt(expiresIn); // Assume seconds
    }
  }

  /**
   * Revoke device credentials
   */
  revokeDevice(deviceId) {
    this.registeredDevices.delete(deviceId);
    this.deviceSecrets.delete(deviceId);
  }

  /**
   * Get device info
   */
  getDeviceInfo(deviceId) {
    return this.registeredDevices.get(deviceId);
  }

  /**
   * List all registered devices
   */
  getAllDevices() {
    return Array.from(this.registeredDevices.values()).map(device => ({
      device_id: device.device_id,
      device_type: device.device_type,
      device_name: device.device_name,
      registered_at: device.registered_at,
      last_seen: device.last_seen,
    }));
  }

  /**
   * Middleware to verify JWT token
   */
  requireAuth() {
    return (req, res, next) => {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Missing or invalid authorization header' });
      }

      const token = authHeader.substring(7);
      try {
        const payload = this.verifyJwt(token);
        req.device = payload;
        
        // Update last seen
        const deviceInfo = this.getDeviceInfo(payload.device_id);
        if (deviceInfo) {
          deviceInfo.last_seen = new Date().toISOString();
        }
        
        next();
      } catch (error) {
        return res.status(401).json({ error: error.message });
      }
    };
  }

  /**
   * Middleware to verify HMAC signature
   */
  requireHmacSignature() {
    return (req, res, next) => {
      const { device_id, type, payload, timestamp, signature } = req.body;

      if (!device_id || !timestamp || !signature) {
        return res.status(400).json({
          error: 'Missing required fields: device_id, timestamp, signature'
        });
      }

      // For relay control messages, check if action is in payload
      if (type === 'relay_control' && (!payload || !payload.action)) {
        return res.status(400).json({
          error: 'Missing action in payload for relay_control message'
        });
      }

      // Verify timestamp
      if (!this.verifyTimestamp(timestamp)) {
        return res.status(400).json({ 
          error: 'Request timestamp is too old or too far in the future' 
        });
      }

      // Prepare data for signature verification (exclude signature field)
      const { signature: _, ...dataToVerify } = req.body;

      try {
        if (!this.verifyHmacSignature(device_id, signature, dataToVerify)) {
          return res.status(401).json({ error: 'Invalid HMAC signature' });
        }
        next();
      } catch (error) {
        return res.status(401).json({ error: error.message });
      }
    };
  }

  /**
   * Check if device has required scope
   */
  requireScope(requiredScope) {
    return (req, res, next) => {
      const device = req.device;
      if (!device || !device.scope || !device.scope.includes(requiredScope)) {
        return res.status(403).json({ 
          error: `Insufficient permissions. Required scope: ${requiredScope}` 
        });
      }
      next();
    };
  }
}

module.exports = SecurityManager;

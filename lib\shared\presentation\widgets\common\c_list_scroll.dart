import 'package:flutter/material.dart';

/// Callback function type for loading more data
typedef LoadMoreCallback<T> = Future<List<T>> Function(int page, int limit);

/// Callback function type for building list items
typedef ItemBuilder<T> = Widget Function(BuildContext context, T item, int index);

/// A reusable list component that combines infinite scroll and virtual scroll
/// for optimal performance with large datasets
class CListScroll<T> extends StatefulWidget {
  /// Callback to load more data when reaching the end of the list
  final LoadMoreCallback<T> onLoadMore;

  /// Builder function for each list item
  final ItemBuilder<T> itemBuilder;

  /// Initial list of items
  final List<T> initialItems;

  /// Number of items to load per page
  final int itemsPerPage;

  /// Padding around the list
  final EdgeInsetsGeometry? padding;

  /// Separator widget between items
  final Widget? separator;

  /// Widget to show when loading more items
  final Widget? loadingWidget;

  /// Widget to show when there's an error
  final Widget? errorWidget;

  /// Widget to show when the list is empty
  final Widget? emptyWidget;

  /// Whether to show loading indicator on initial load
  final bool showInitialLoading;

  /// Scroll controller for external control
  final ScrollController? controller;

  /// Physics for the scroll view
  final ScrollPhysics? physics;

  /// Whether to shrink wrap the list
  final bool shrinkWrap;

  /// Threshold for triggering load more (distance from bottom)
  final double loadMoreThreshold;

  const CListScroll({
    super.key,
    required this.onLoadMore,
    required this.itemBuilder,
    this.initialItems = const [],
    this.itemsPerPage = 20,
    this.padding,
    this.separator,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.showInitialLoading = true,
    this.controller,
    this.physics,
    this.shrinkWrap = false,
    this.loadMoreThreshold = 200.0,
  });

  @override
  State<CListScroll<T>> createState() => _CListScrollState<T>();
}

class _CListScrollState<T> extends State<CListScroll<T>> {
  late ScrollController _scrollController;
  List<T> _items = [];
  bool _isLoading = false;
  bool _hasError = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _items = List.from(widget.initialItems);

    // Load initial data if no initial items provided
    if (_items.isEmpty && widget.showInitialLoading) {
      _loadMoreData();
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - widget.loadMoreThreshold) {
      if (!_isLoading && _hasMoreData && !_hasError) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final newItems = await widget.onLoadMore(_currentPage, widget.itemsPerPage);

      setState(() {
        _items.addAll(newItems);
        _currentPage++;
        _hasMoreData = newItems.length == widget.itemsPerPage;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = error.toString();
      });
    }
  }

  /// Refresh the list by clearing current data and loading from page 1
  Future<void> refresh() async {
    setState(() {
      _items.clear();
      _currentPage = 1;
      _hasMoreData = true;
      _hasError = false;
      _errorMessage = null;
    });

    await _loadMoreData();
  }

  /// Add new items to the beginning of the list
  void prependItems(List<T> items) {
    setState(() {
      _items.insertAll(0, items);
    });
  }

  /// Add new items to the end of the list
  void appendItems(List<T> items) {
    setState(() {
      _items.addAll(items);
    });
  }

  /// Remove an item from the list
  void removeItem(T item) {
    setState(() {
      _items.remove(item);
    });
  }

  /// Update an item in the list
  void updateItem(T oldItem, T newItem) {
    setState(() {
      final index = _items.indexOf(oldItem);
      if (index != -1) {
        _items[index] = newItem;
      }
    });
  }

  Widget _buildLoadingIndicator() {
    return widget.loadingWidget ??
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Có lỗi xảy ra',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _loadMoreData(),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        );
  }

  Widget _buildEmptyWidget() {
    return widget.emptyWidget ??
        Padding(
          padding: const EdgeInsets.all(32.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'Không có dữ liệu',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    // Show initial loading
    if (_items.isEmpty && _isLoading && widget.showInitialLoading) {
      return _buildLoadingIndicator();
    }

    // Show error if no items and has error
    if (_items.isEmpty && _hasError) {
      return _buildErrorWidget();
    }

    // Show empty state if no items and not loading
    if (_items.isEmpty && !_isLoading) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: refresh,
      child: ListView.separated(
        controller: _scrollController,
        padding: widget.padding,
        physics: widget.physics,
        shrinkWrap: widget.shrinkWrap,
        itemCount: _items.length + (_hasMoreData || _isLoading ? 1 : 0),
        separatorBuilder: (context, index) {
          if (index >= _items.length) return const SizedBox.shrink();
          return widget.separator ?? const SizedBox.shrink();
        },
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index >= _items.length) {
            if (_isLoading) {
              return _buildLoadingIndicator();
            } else if (_hasError) {
              return _buildErrorWidget();
            }
            return const SizedBox.shrink();
          }

          return widget.itemBuilder(context, _items[index], index);
        },
      ),
    );
  }
}
/// Enum định nghĩa các flavor của ứng dụng
enum Flavor {
  mobile,
  terminal,
}

/// Configuration class cho từng flavor
class FlavorConfig {
  final Flavor flavor;
  final String appName;
  final String appId;
  final String appVersion;
  final bool isDebug;
  final Map<String, dynamic> values;

  FlavorConfig._internal({
    required this.flavor,
    required this.appName,
    required this.appId,
    required this.appVersion,
    required this.isDebug,
    required this.values,
  });

  static FlavorConfig? _instance;

  /// Khởi tạo flavor config
  static void initialize({
    required Flavor flavor,
    required String appName,
    required String appId,
    required String appVersion,
    bool isDebug = false,
    Map<String, dynamic> values = const {},
  }) {
    _instance = FlavorConfig._internal(
      flavor: flavor,
      appName: appName,
      appId: appId,
      appVersion: appVersion,
      isDebug: isDebug,
      values: values,
    );
  }

  /// Lấy instance hiện tại
  static FlavorConfig get instance {
    if (_instance == null) {
      throw Exception('FlavorConfig chưa được khởi tạo. Hãy gọi FlavorConfig.initialize() trước.');
    }
    return _instance!;
  }

  /// Kiểm tra có phải mobile app không
  bool get isMobile => flavor == Flavor.mobile;

  /// Kiểm tra có phải terminal app không
  bool get isTerminal => flavor == Flavor.terminal;

  /// Lấy giá trị từ values map
  T getValue<T>(String key, T defaultValue) {
    return values[key] as T? ?? defaultValue;
  }

  @override
  String toString() {
    return 'FlavorConfig(flavor: $flavor, appName: $appName, appId: $appId)';
  }
}

import 'package:dartz/dartz.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/errors/failures.dart';

class ChangePasswordUseCase {
  final AuthRepository repository;

  ChangePasswordUseCase(this.repository);

  Future<Either<Failure, bool>> call(ChangePasswordParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to change password
    return await repository.changePassword(
      currentPassword: params.currentPassword,
      newPassword: params.newPassword,
      confirmPassword: params.confirmPassword,
    );
  }

  /// Validate change password parameters
  ValidationFailure? _validateParams(ChangePasswordParams params) {
    final Map<String, List<String>> errors = {};

    // Validate current password
    if (params.currentPassword.trim().isEmpty) {
      errors['currentPassword'] = ['Current password is required'];
    }

    // Validate new password
    if (params.newPassword.trim().isEmpty) {
      errors['newPassword'] = ['New password is required'];
    } else {
      // Password strength validation
      final passwordErrors = _validatePasswordStrength(params.newPassword);
      if (passwordErrors.isNotEmpty) {
        errors['newPassword'] = passwordErrors;
      }
    }

    // Validate confirm password
    if (params.confirmPassword.trim().isEmpty) {
      errors['confirmPassword'] = ['Confirm password is required'];
    } else if (params.newPassword != params.confirmPassword) {
      errors['confirmPassword'] = ['Passwords do not match'];
    }

    // Check if new password is same as current password
    if (params.currentPassword.isNotEmpty && 
        params.newPassword.isNotEmpty && 
        params.currentPassword == params.newPassword) {
      errors['newPassword'] = ['New password must be different from current password'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Password validation failed',
        fieldErrors: errors,
      );
    }

    return null;
  }

  /// Validate password strength
  List<String> _validatePasswordStrength(String password) {
    final List<String> errors = [];

    // Minimum length check
    if (password.length < 8) {
      errors.add('Password must be at least 8 characters long');
    }

    // Maximum length check
    if (password.length > 128) {
      errors.add('Password must not exceed 128 characters');
    }

    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      errors.add('Password must contain at least one uppercase letter');
    }

    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      errors.add('Password must contain at least one lowercase letter');
    }

    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      errors.add('Password must contain at least one number');
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      errors.add('Password must contain at least one special character');
    }

    // Check for common weak passwords
    final commonPasswords = [
      'password', 'password123', '123456', '123456789', 'qwerty',
      'abc123', 'password1', 'admin', 'letmein', 'welcome'
    ];
    
    if (commonPasswords.contains(password.toLowerCase())) {
      errors.add('Password is too common, please choose a stronger password');
    }

    return errors;
  }
}

class ChangePasswordParams {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  ChangePasswordParams({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });
}

import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

/// Toggle button component cho việc chọn gi<PERSON>a các options
class AppToggleButton extends StatelessWidget {
  final List<String> options;
  final int selectedIndex;
  final ValueChanged<int> onChanged;
  final bool isEnabled;

  const AppToggleButton({
    super.key,
    required this.options,
    required this.selectedIndex,
    required this.onChanged,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 189,
      height: 28,
      child: Stack(
        children: [
          // On Premise button (right side)
          Positioned(
            left: 87,
            top: 0,
            child: _buildToggleItem(
              text: options[1], // "On Premise"
              isSelected: selectedIndex == 1,
              isRight: true,
              onTap: isEnabled ? () => onChanged(1) : null,
            ),
          ),
          // On Cloud button (left side)
          Positioned(
            left: 4,
            top: 0,
            child: _buildToggleItem(
              text: options[0], // "On Cloud"
              isSelected: selectedIndex == 0,
              isRight: false,
              onTap: isEnabled ? () => onChanged(0) : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleItem({
    required String text,
    required bool isSelected,
    required bool isRight,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isRight ? 16 : 12, // pl-4 pr-3 vs px-3
          vertical: 4, // py-1
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surface,
          borderRadius: BorderRadius.horizontal(
            left: isRight ? Radius.zero : Radius.circular(AppDimensions.radiusM),
            right: isRight ? Radius.circular(AppDimensions.radiusM) : Radius.zero,
          ),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: AppDimensions.borderNormal,
          ),
        ),
        child: SizedBox(
          width: isRight ? null : 66, // "On Cloud" has fixed width
          child: Text(
            text,
            textAlign: isRight ? TextAlign.left : TextAlign.center,
            style: AppTextStyles.labelSmall.copyWith(
              color: isSelected ? AppColors.textOnPrimary : AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}

# Relay Service Enhancements - Exception Handling & USB-TTL Connection Monitoring

## Tổng quan

Đã triển khai hệ thống quản lý relay với khả năng:
1. **Exception handling toàn diện** cho tất cả lệnh điều khiển relay
2. **Tự động phát hiện ngắt kết nối USB-TTL** 
3. **<PERSON><PERSON> chế tự động kết nối lại** khi phát hiện thiết bị
4. **Monitoring connection status** real-time
5. **Graceful degradation** khi mất kết nối

## Các cải tiến chính

### 1. Enhanced USB-TTL Relay Controller

**File**: `lib/packages/relay_controller/lib/src/usb_ttl_relay_controller.dart`

#### Connection Monitoring
```dart
// Tự động monitor connection status
Timer? _connectionMonitorTimer;
StreamController<bool>? _connectionStatusController;
int _consecutiveFailures = 0;

// Stream để theo dõi trạng thái kết nối
Stream<bool> get connectionStatus => 
    _connectionStatusController?.stream ?? const Stream.empty();
```

#### Enhanced Command Sending
```dart
Future<void> _sendCommand(String command) async {
  try {
    // Double-check connection
    if (!_isConnected || _port == null) {
      throw const UsbRelayException('Device disconnected before sending command');
    }
    
    // Send with timeout
    await Future.any([
      _port!.write(Uint8List.fromList(data)),
      Future.delayed(const Duration(seconds: 5)).then((_) => 
          throw const UsbRelayException('Command send timeout')),
    ]);
    
    _consecutiveFailures = 0; // Reset on success
    
  } catch (e) {
    _consecutiveFailures++;
    
    // Auto-detect disconnection
    if (_isDisconnectionError(e) || _consecutiveFailures >= _maxConsecutiveFailures) {
      await _handleDisconnection();
    }
    
    throw UsbRelayException('Failed to send command: $command', e);
  }
}
```

#### Automatic Disconnection Detection
```dart
Future<void> _checkConnectionHealth() async {
  try {
    // Check if device still exists in system
    final devices = await UsbSerial.listDevices();
    final isDeviceStillAvailable = devices.any((device) => 
        device.deviceId == _connectedDevice?.deviceId);
    
    if (!isDeviceStillAvailable) {
      await _handleDisconnection();
    }
  } catch (e) {
    _consecutiveFailures++;
    if (_consecutiveFailures >= _maxConsecutiveFailures) {
      await _handleDisconnection();
    }
  }
}
```

### 2. Enhanced Relay Trigger Service

**File**: `lib/apps/terminal/services/relay_trigger_service.dart`

#### Connection Status Monitoring
```dart
// Listen to relay service status updates
_connectionSubscription = _relayService.statusUpdates.listen((status) {
  switch (status.type) {
    case RelayStatusType.connected:
      _isRelayConnected = true;
      _consecutiveFailures = 0;
      break;
      
    case RelayStatusType.disconnected:
      _isRelayConnected = false;
      _handleConnectionLoss();
      break;
  }
});
```

#### Enhanced Relay Control with Exception Handling
```dart
Future<void> _executeRelayAction(RelayAction action, RelayTriggerScenario scenario) async {
  try {
    // Check connection before control
    if (!_isRelayConnected) {
      await _attemptReconnection();
      if (!_isRelayConnected) {
        throw Exception('Relay device not connected');
      }
    }

    // Control with timeout
    await Future.any([
      _relayService.controlRelay(action.relayIndex, RelayAction.on),
      Future.delayed(const Duration(seconds: 5)).then((_) => 
          throw Exception('Relay control timeout')),
    ]);

    _consecutiveFailures = 0; // Reset on success
    
  } catch (e) {
    _consecutiveFailures++;
    
    // Handle connection issues
    if (_isConnectionError(e) || _consecutiveFailures >= _maxConsecutiveFailures) {
      await _handleConnectionLoss();
    }
  }
}
```

#### Automatic Reconnection
```dart
void _startReconnectionAttempts() {
  _reconnectTimer = Timer.periodic(const Duration(seconds: 10), (_) async {
    if (_isRelayConnected) {
      _reconnectTimer?.cancel();
      return;
    }
    
    await _attemptReconnection();
  });
}

Future<void> _attemptReconnection() async {
  try {
    await _relayService.disconnect();
    await Future.delayed(const Duration(milliseconds: 500));
    
    if (_relayService.deviceConfig != null) {
      await _relayService.initialize(
        config: _relayService.deviceConfig!,
        autoConnect: true,
      );
    }
  } catch (e) {
    // Log error but continue trying
  }
}
```

#### Connection Loss Handling
```dart
Future<void> _handleConnectionLoss() async {
  _isRelayConnected = false;
  
  // Cancel all active triggers since we can't turn them off
  for (final timer in _activeTriggers.values) {
    timer.cancel();
  }
  _activeTriggers.clear();
  _activeScenarios.clear();
  
  // Start reconnection attempts
  _startReconnectionAttempts();
}
```

### 3. Enhanced Emergency Stop
```dart
Future<void> emergencyStop() async {
  // Cancel all timers first
  for (final timer in _activeTriggers.values) {
    timer.cancel();
  }

  // Turn off all relays with timeout and connection checking
  for (int i = 0; i < 4; i++) {
    try {
      await Future.any([
        _relayService.controlRelay(i, RelayAction.off),
        Future.delayed(const Duration(seconds: 2)).then((_) => 
            throw Exception('Emergency stop timeout for relay R$i')),
      ]);
    } catch (e) {
      // If connection error, abort remaining relays
      if (_isConnectionError(e)) {
        await _handleConnectionLoss();
        break;
      }
    }
  }
}
```

## Các tính năng chính

### 1. Exception Handling toàn diện
- ✅ Catch tất cả exceptions trong relay control commands
- ✅ Timeout protection cho tất cả operations
- ✅ Graceful error recovery
- ✅ Detailed error logging với debug mode

### 2. USB-TTL Connection Monitoring
- ✅ Real-time connection health checking
- ✅ Automatic disconnection detection
- ✅ Device availability monitoring
- ✅ Connection status stream

### 3. Automatic Reconnection
- ✅ Auto-detect khi thiết bị được cắm lại
- ✅ Periodic reconnection attempts
- ✅ Smart reconnection logic
- ✅ Connection state management

### 4. Relay Safety Features
- ✅ Stop all active triggers khi mất kết nối
- ✅ Prevent relay commands khi không có kết nối
- ✅ Emergency stop với connection checking
- ✅ Consecutive failure tracking

### 5. Performance Optimizations
- ✅ Connection pooling
- ✅ Command queuing với timeout
- ✅ Resource cleanup
- ✅ Memory leak prevention

## Usage Examples

### Basic Relay Control với Exception Handling
```dart
final relayService = RelayTriggerService();
await relayService.initialize();

try {
  // Trigger sẽ tự động handle connection issues
  await relayService.triggerScenario(
    RelayTriggerScenario.accessGranted,
    metadata: {'user_id': 'user123'},
  );
} catch (e) {
  // Service đã handle internally, chỉ cần log
  print('Relay trigger failed: $e');
}
```

### Connection Status Monitoring
```dart
// Listen to connection status
relayService.connectionStatus.listen((isConnected) {
  if (isConnected) {
    print('✅ Relay device connected');
  } else {
    print('❌ Relay device disconnected - auto-reconnecting...');
  }
});
```

### Emergency Stop với Safety
```dart
// Emergency stop sẽ handle connection issues
await relayService.emergencyStop();
```

## Testing & Validation

### Test Scenarios
1. **USB Disconnect Test**: Rút USB trong khi relay đang hoạt động
2. **Power Loss Test**: Mất nguồn ESP32 device
3. **Command Timeout Test**: Block USB communication
4. **Rapid Reconnect Test**: Cắm/rút USB liên tục
5. **Long Running Test**: Chạy liên tục nhiều giờ

### Expected Behaviors
- ✅ Tự động phát hiện disconnect trong 2-5 giây
- ✅ Stop tất cả active triggers khi mất kết nối
- ✅ Tự động reconnect khi device available
- ✅ Không crash app khi có lỗi relay
- ✅ Graceful degradation khi không có relay

## Configuration

### Connection Monitoring Settings
```dart
static const Duration _connectionCheckInterval = Duration(seconds: 2);
static const int _maxConsecutiveFailures = 3;
static const Duration _reconnectInterval = Duration(seconds: 10);
```

### Timeout Settings
```dart
static const Duration _commandTimeout = Duration(seconds: 5);
static const Duration _emergencyStopTimeout = Duration(seconds: 2);
```

Hệ thống này đảm bảo relay hoạt động ổn định và an toàn ngay cả khi có sự cố với USB-TTL connection.

#!/bin/bash

echo "🏗️  Building C-Face Mobile App..."
echo "=================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build mobile app
echo "🔨 Building mobile APK..."
flutter build apk --target lib/apps/mobile/main_mobile.dart --release

# Check build result
if [ $? -eq 0 ]; then
    echo "✅ Mobile app build completed successfully!"
    echo "📱 APK location: build/app/outputs/flutter-apk/app-release.apk"

    # Show APK size
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        APK_SIZE=$(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)
        echo "📊 APK size: $APK_SIZE"
    fi
else
    echo "❌ Mobile app build failed!"
    exit 1
fi

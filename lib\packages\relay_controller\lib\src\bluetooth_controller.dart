import 'package:bluetooth_classic/bluetooth_classic.dart';
import 'package:bluetooth_classic/models/device.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses modern Bluetooth Classic communication.
///
/// This controller uses the `bluetooth_classic` package which is actively
/// maintained and compatible with newer Android versions.
///
/// Example usage:
/// ```dart
/// final controller = BluetoothRelayController(
///   deviceAddress: '00:11:22:33:44:55',
/// );
///
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class BluetoothRelayController extends RelayController {
  /// The Bluetooth device address (MAC address).
  final String deviceAddress;

  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  /// Serial Port Profile UUID for Bluetooth Classic communication.
  final String serialUuid;

  static final BluetoothClassic _bluetoothClassic = BluetoothClassic();
  bool _isConnected = false;
  bool _isInitialized = false;

  /// Creates a new [BluetoothRelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceAddress] is the MAC address of the Bluetooth device.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  /// [serialUuid] is the UUID for serial communication (default: SPP UUID).
  BluetoothRelayController({
    required super.deviceId,
    required this.deviceAddress,
    super.deviceName = 'Bluetooth Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.timeoutSeconds = 10,
    this.serialUuid = '00001101-0000-1000-8000-00805f9b34fb', // SPP UUID
  });

  /// Initializes Bluetooth permissions and adapter.
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize permissions
      await _bluetoothClassic.initPermissions();
      _isInitialized = true;
    } catch (e) {
      throw BluetoothRelayException('Failed to initialize Bluetooth', e);
    }
  }

  /// Connects to the Bluetooth device.
  ///
  /// This method must be called before using [triggerOn] or [triggerOff].
  ///
  /// Throws [BluetoothRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      await _initialize();

      // Connect to the device
      await _bluetoothClassic.connect(deviceAddress, serialUuid);
      _isConnected = true;
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to connect to Bluetooth device', e);
    }
  }

  /// Disconnects from the Bluetooth device.
  Future<void> disconnect() async {
    try {
      if (_isConnected) {
        await _bluetoothClassic.disconnect();
        _isConnected = false;
      }
    } catch (e) {
      throw BluetoothRelayException('Failed to disconnect from Bluetooth device', e);
    }
  }

  /// Sends a command to the Bluetooth device.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected) {
        throw const BluetoothRelayException('Not connected to Bluetooth device');
      }

      await _bluetoothClassic.write(command);
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets available Bluetooth devices.
  ///
  /// Returns a list of paired (bonded) Bluetooth devices.
  static Future<List<Device>> getAvailableDevices() async {
    try {
      await _bluetoothClassic.initPermissions();
      return await _bluetoothClassic.getPairedDevices();
    } catch (e) {
      throw BluetoothRelayException('Failed to get available devices', e);
    }
  }

  /// Checks if Bluetooth is enabled on the device.
  static Future<bool> isBluetoothEnabled() async {
    try {
      // Try to get paired devices as a way to check if Bluetooth is enabled
      await _bluetoothClassic.initPermissions();
      await _bluetoothClassic.getPairedDevices();
      return true;
    } catch (e) {
      return false;
    }
  }
}

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses native Android Bluetooth Classic communication.
///
/// This controller uses platform channels to communicate with native Android
/// Bluetooth APIs, avoiding the compatibility issues with flutter_bluetooth_serial.
///
/// Example usage:
/// ```dart
/// final controller = NativeBluetoothController(
///   deviceAddress: '00:11:22:33:44:55',
/// );
///
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class NativeBluetoothController extends RelayController {
  /// The Bluetooth device address (MAC address).
  final String deviceAddress;

  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  static const MethodChannel _channel = MethodChannel('native_bluetooth');
  bool _isConnected = false;

  /// Creates a new [NativeBluetoothController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceAddress] is the MAC address of the Bluetooth device.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  NativeBluetoothController({
    required super.deviceId,
    required this.deviceAddress,
    super.deviceName = 'Native Bluetooth Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.timeoutSeconds = 10,
  });

  /// Connects to the Bluetooth device using native Android APIs.
  /// 
  /// This method must be called before using [triggerOn] or [triggerOff].
  /// 
  /// Throws [BluetoothRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      // Check if Bluetooth is enabled
      final bool isEnabled = await _channel.invokeMethod('isBluetoothEnabled');
      if (!isEnabled) {
        throw const BluetoothRelayException('Bluetooth is not enabled');
      }

      // Connect to the device
      final bool connected = await _channel.invokeMethod('connect', {
        'address': deviceAddress,
        'timeout': timeoutSeconds,
      });

      if (!connected) {
        throw const BluetoothRelayException('Failed to connect to device');
      }
      
      _isConnected = true;
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to connect to Bluetooth device', e);
    }
  }

  /// Disconnects from the Bluetooth device.
  Future<void> disconnect() async {
    try {
      if (_isConnected) {
        await _channel.invokeMethod('disconnect', {'address': deviceAddress});
        _isConnected = false;
      }
    } catch (e) {
      throw BluetoothRelayException('Failed to disconnect from Bluetooth device', e);
    }
  }

  /// Sends a command to the Bluetooth device.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected) {
        throw const BluetoothRelayException('Not connected to Bluetooth device');
      }

      final bool sent = await _channel.invokeMethod('sendData', {
        'address': deviceAddress,
        'data': command,
      });

      if (!sent) {
        throw BluetoothRelayException('Failed to send command: $command');
      }
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets available Bluetooth devices using native APIs.
  /// 
  /// Returns a list of bonded (paired) Bluetooth devices.
  static Future<List<Map<String, String>>> getAvailableDevices() async {
    try {
      final List<dynamic> devices = await _channel.invokeMethod('getBondedDevices');
      return devices.cast<Map<String, String>>();
    } catch (e) {
      throw BluetoothRelayException('Failed to get available devices', e);
    }
  }

  /// Checks if Bluetooth is enabled on the device.
  static Future<bool> isBluetoothEnabled() async {
    try {
      return await _channel.invokeMethod('isBluetoothEnabled');
    } catch (e) {
      return false;
    }
  }

  /// Requests to enable Bluetooth.
  static Future<bool> requestEnableBluetooth() async {
    try {
      return await _channel.invokeMethod('requestEnableBluetooth');
    } catch (e) {
      return false;
    }
  }
}

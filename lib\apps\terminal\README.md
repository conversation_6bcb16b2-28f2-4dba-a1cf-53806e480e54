# Flutter Terminal Application

A comprehensive Flutter terminal application with complete device registration flow and command handling architecture for secure IoT/security device communication.

## 🎯 Overview

This terminal application demonstrates a production-ready implementation of secure device-server communication with:

- **Complete Registration Flow**: Unregistered → Registering → Registered states
- **Secure Communication**: JWT authentication + HMAC message signing
- **Command Handler Architecture**: Centralized command processing and routing
- **Real-time Status Monitoring**: Live connection and device status
- **Manual Command Testing**: Debug interface for development
- **Command History**: Complete audit trail of all operations

## ✨ Features

### 🔐 Device Registration & Authentication
- **Three-state registration flow** with proper UI feedback
- **Secure credential storage** using SharedPreferences
- **Automatic token refresh** and session management
- **Device fingerprinting** for enhanced security
- **Registration progress tracking** with detailed status messages

### 🎛️ Command Handler Architecture
- **Centralized command processing** via `DeviceCommandHandler`
- **Module integration bridge** between secure_comm and other modules
- **Extensible command system** for easy addition of new command types
- **Command history tracking** with detailed execution logs
- **Error handling and retry logic** for robust operation

### 📡 Secure Communication
- **JWT-based authentication** with automatic token management
- **HMAC message signing** for request integrity
- **Multiple transport protocols** (HTTP, MQTT, WebSocket)
- **Replay attack prevention** with timestamp validation
- **Device scope management** for permission control

### 🖥️ User Interface
- **Modern Material Design 3** with responsive layouts
- **Real-time status indicators** with color-coded states
- **Tabbed interface** for organized functionality
- **Manual command testing** interface for debugging
- **Command history viewer** with expandable details
- **Registration wizard** with form validation

## 🏗️ Architecture

### State Management
```
DeviceRegistrationProvider
├── Device registration flow
├── Credential management
├── Connection status
└── Heartbeat monitoring

DeviceCommandHandler
├── Command execution
├── Module integration
├── History tracking
└── Error handling
```

### UI Components
```
TerminalScreen (Main)
├── Dashboard Tab
│   ├── DeviceStatusWidget
│   ├── Quick Actions
│   └── System Information
├── Commands Tab
│   └── ManualCommandWidget
└── History Tab
    └── CommandHistoryWidget

DeviceRegistrationScreen
├── Registration Form
├── Progress Indicator
└── Status Display
```

### Integration Flow
```
SecureComm ↔ DeviceCommandHandler ↔ Modules
    ↑              ↑                    ↓
    │              │              RelayController
    │              │              FaceRecognition
    │              │              SensorModules
    │              │                    ↓
    └── JWT Auth   └── Command Router   └── Hardware Control
```

## 🚀 Getting Started

### Prerequisites

1. **Test Server Running**:
   ```bash
   cd lib/packages/relay_controller/test_server
   npm install
   npm start
   ```

2. **Dependencies Installed**:
   ```bash
   flutter pub get
   ```

### Running the Terminal App

1. **Standalone Example**:
   ```bash
   cd lib/apps/terminal
   flutter run lib/example/terminal_app_example.dart
   ```

2. **Integrated with Main App**:
   ```bash
   flutter run --flavor terminal
   ```

### First Time Setup

1. **Launch the app** - you'll see the unregistered state
2. **Tap "Register Device"** to open registration screen
3. **Fill in device information**:
   - Device ID: `terminal-001`
   - Device Name: `Security Terminal`
   - Device Type: `face_terminal`
   - Location: `Main Entrance`
   - Server URL: `http://localhost:3000`
4. **Tap "Register Device"** and wait for completion
5. **Navigate back** to see the registered state

## 📋 Usage Examples

### Device Registration

```dart
final provider = DeviceRegistrationProvider();
await provider.initialize();

// Register device
final success = await provider.registerDevice(
  deviceId: 'terminal-001',
  deviceName: 'Security Terminal',
  deviceType: 'face_terminal',
  location: 'Main Entrance',
  serverUrl: 'http://localhost:3000',
);

if (success) {
  print('Device registered successfully');
}
```

### Command Execution

```dart
final commandHandler = DeviceCommandHandler();
await commandHandler.initialize(secureComm);

// Execute relay control
final result = await commandHandler.executeCommand(
  commandType: 'relay_control',
  payload: {
    'action': 'unlock',
    'relay_id': 'main_door',
  },
);

print('Command result: ${result.message}');
```

### Quick Actions

```dart
// Convenience methods
await commandHandler.unlockDoor();
await commandHandler.lockDoor();
await commandHandler.getRelayStatus();
await commandHandler.sendHeartbeat();
```

## 🎛️ Command Types

### Relay Control
```json
{
  "commandType": "relay_control",
  "payload": {
    "action": "unlock|lock|on|off|status",
    "relay_id": "main_door"
  }
}
```

### Face Authentication
```json
{
  "commandType": "face_auth",
  "payload": {
    "face_image": "base64_encoded_image",
    "user_id": "optional_user_id"
  }
}
```

### Status Request
```json
{
  "commandType": "status_request",
  "payload": {
    "component": "relay|system|all"
  }
}
```

### Heartbeat
```json
{
  "commandType": "heartbeat",
  "payload": {
    "system_info": {
      "status": "active",
      "uptime": 3600
    }
  }
}
```

## 🔧 Configuration

### Device Types
- `face_terminal`: Face recognition with door control
- `relay_controller`: Multi-zone relay control system
- `access_terminal`: Access control with logging
- `security_terminal`: Comprehensive security terminal

### Capabilities by Device Type
```dart
final capabilities = {
  'face_terminal': ['face_auth', 'relay_control', 'image_upload'],
  'relay_controller': ['relay_control', 'status_monitoring'],
  'access_terminal': ['face_auth', 'relay_control', 'access_logging'],
  'security_terminal': ['face_auth', 'relay_control', 'image_upload', 'security_monitoring'],
};
```

## 🧪 Testing

### Manual Testing Interface
The app includes a comprehensive manual testing interface:

1. **Command Type Selection**: Choose from available command types
2. **Parameter Input**: Fill in required parameters with hints
3. **Quick Actions**: Pre-configured buttons for common operations
4. **Execution Results**: Real-time feedback and error handling

### Test Scenarios
- Device registration flow
- Command execution and responses
- Error handling and recovery
- Network connectivity issues
- Token refresh and authentication

## 🔍 Debugging

### Logging
All operations are logged with different levels:
```dart
Logger.i('Info message');
Logger.w('Warning message');
Logger.e('Error message');
```

### Command History
View detailed command history with:
- Command type and parameters
- Execution results and errors
- Timestamps and source tracking
- Expandable details for debugging

### Status Monitoring
Real-time monitoring of:
- Connection status
- Authentication state
- Last heartbeat time
- Available capabilities
- Error messages

## 🔄 Integration

### Adding to Existing Apps

1. **Add Providers**:
   ```dart
   MultiProvider(
     providers: [
       ChangeNotifierProvider(create: (_) => DeviceRegistrationProvider()),
       // Your existing providers...
     ],
     child: YourApp(),
   )
   ```

2. **Use Components**:
   ```dart
   // In your screens
   DeviceStatusWidget()
   ManualCommandWidget()
   CommandHistoryWidget()
   ```

3. **Initialize Command Handler**:
   ```dart
   final commandHandler = DeviceCommandHandler();
   await commandHandler.initialize(secureComm);
   ```

### Custom Command Types

```dart
// Add new command type
Future<CommandResult> _executeCustomCommand(Map<String, dynamic> payload) async {
  // Your custom logic here
  return CommandResult.success(message: 'Custom command executed');
}

// Register in command handler
switch (commandType) {
  case 'custom_command':
    result = await _executeCustomCommand(payload);
    break;
  // ... existing cases
}
```

## 📁 Project Structure

```
lib/apps/terminal/
├── providers/
│   └── device_registration_provider.dart    # Registration state management
├── services/
│   └── device_command_handler.dart          # Command processing
├── presentation/
│   ├── screens/
│   │   ├── terminal_screen.dart             # Main terminal interface
│   │   └── device_registration_screen.dart  # Registration wizard
│   └── widgets/
│       ├── device_status_widget.dart       # Status display
│       ├── command_history_widget.dart     # History viewer
│       └── manual_command_widget.dart      # Testing interface
├── example/
│   └── terminal_app_example.dart           # Standalone example
└── README.md                               # This file
```

## 🎯 Use Cases

- **Face Recognition Terminals**: Secure access control systems
- **Relay Controllers**: Industrial automation and control
- **Security Terminals**: Comprehensive security monitoring
- **IoT Devices**: Any device requiring secure server communication
- **Access Control**: Door locks, gates, barriers
- **Monitoring Systems**: Status reporting and alerting

## 🔮 Future Enhancements

- **WebSocket real-time communication** for instant commands
- **MQTT integration** for IoT device networks
- **Offline mode** with command queuing
- **Multi-device management** from single terminal
- **Advanced logging** with remote log shipping
- **Configuration management** with remote updates
- **Biometric integration** beyond face recognition
- **Video streaming** for security monitoring

## 📞 Support

For questions and support:
1. Check the command history for error details
2. Review the device status for connection issues
3. Ensure the test server is running and accessible
4. Check network connectivity and firewall settings
5. Review logs for detailed error information

## 📝 License

This terminal application is part of the CCAM Mobile project and follows the same licensing terms.

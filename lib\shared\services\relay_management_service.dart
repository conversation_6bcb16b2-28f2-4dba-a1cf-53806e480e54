import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../core/config/app_config.dart';
import '../core/config/config_helper.dart';
import '../core/errors/relay_exceptions.dart';
import 'http_client_service.dart';
import 'package:relay_controller/relay_controller.dart';
import 'relay_api_service.dart' as api;

/// Service for managing relay operations and configurations.
/// 
/// This service provides:
/// - Relay configuration management
/// - ESP32 relay controller integration
/// - Server registration for relay devices
/// - Multi-relay support with configurable relay count
class RelayManagementService {
  static RelayManagementService? _instance;
  static RelayManagementService get instance => _instance ??= RelayManagementService._();

  RelayManagementService._();

  UsbTtlRelayController? _relayController;
  StreamController<RelayStatusUpdate>? _statusController;
  RelayDeviceConfig? _deviceConfig;
  bool _isInitialized = false;

  /// Stream for relay status updates
  Stream<RelayStatusUpdate> get statusUpdates => 
      _statusController?.stream ?? const Stream.empty();

  /// Gets current relay configuration
  RelayDeviceConfig? get deviceConfig => _deviceConfig;

  /// Checks if relay controller is connected
  bool get isConnected => _relayController?.isConnected ?? false;

  /// Gets current relay configuration
  RelayConfiguration? get configuration => _relayController?.configuration;

  /// Initializes the relay management service.
  /// 
  /// [config] contains relay device configuration.
  /// [autoConnect] whether to automatically connect to first available device.
  Future<void> initialize({
    required RelayDeviceConfig config,
    bool autoConnect = true,
  }) async {
    try {
      _deviceConfig = config;
      _statusController ??= StreamController<RelayStatusUpdate>.broadcast();

      // Create USB-TTL relay controller with device profile
      final deviceProfile = _createDeviceProfile(config);
      
      if (autoConnect) {
        _relayController = AutoConnectUsbTtlRelayController(
          deviceId: config.deviceId,
          deviceName: config.deviceName,
          relayCount: config.relayCount,
          baudRate: config.baudRate,
          deviceProfile: deviceProfile,
        );
        
        await (_relayController as AutoConnectUsbTtlRelayController).initialize();
      } else {
        _relayController = UsbTtlRelayController(
          deviceId: config.deviceId,
          deviceName: config.deviceName,
          relayCount: config.relayCount,
          baudRate: config.baudRate,
          deviceProfile: deviceProfile,
        );
      }

      _isInitialized = true;
      _notifyStatusUpdate(RelayStatusUpdate.connected(config.deviceId));
    } catch (e) {
      throw RelayManagementException('Failed to initialize relay service: $e');
    }
  }

  /// Connects to a specific USB device.
  Future<void> connectToDevice(dynamic usbDevice) async {
    _ensureInitialized();
    
    try {
      if (_relayController is! AutoConnectUsbTtlRelayController) {
        await _relayController!.connect(usbDevice);
        _notifyStatusUpdate(RelayStatusUpdate.connected(_deviceConfig!.deviceId));
      }
    } catch (e) {
      throw RelayManagementException('Failed to connect to device: $e');
    }
  }

  /// Controls a specific relay.
  /// 
  /// [relayIndex] is the relay number (0-based).
  /// [action] is the action to perform.
  Future<void> controlRelay(int relayIndex, RelayAction action) async {
    _ensureInitialized();
    _ensureConnected();

    try {
      await _relayController!.controlRelay(relayIndex, action);
      
      _notifyStatusUpdate(RelayStatusUpdate.relayAction(
        _deviceConfig!.deviceId,
        relayIndex,
        action,
      ));
    } catch (e) {
      throw RelayManagementException('Failed to control relay $relayIndex: $e');
    }
  }

  /// Controls a relay with timed operation.
  /// 
  /// [relayIndex] is the relay number (0-based).
  /// [durationMs] is the duration in milliseconds.
  Future<void> controlRelayTimed(int relayIndex, int durationMs) async {
    _ensureInitialized();
    _ensureConnected();

    try {
      await _relayController!.controlRelayTimed(relayIndex, durationMs);
      
      _notifyStatusUpdate(RelayStatusUpdate.relayTimedAction(
        _deviceConfig!.deviceId,
        relayIndex,
        durationMs,
      ));
    } catch (e) {
      throw RelayManagementException('Failed to control timed relay $relayIndex: $e');
    }
  }

  /// Controls all relays at once.
  Future<void> controlAllRelays(RelayAction action) async {
    _ensureInitialized();
    _ensureConnected();

    try {
      await _relayController!.controlAllRelays(action);
      
      _notifyStatusUpdate(RelayStatusUpdate.allRelaysAction(
        _deviceConfig!.deviceId,
        action,
      ));
    } catch (e) {
      throw RelayManagementException('Failed to control all relays: $e');
    }
  }

  /// Sends a raw command to ESP32.
  Future<void> sendRawCommand(String command) async {
    _ensureInitialized();
    _ensureConnected();

    try {
      await _relayController!.sendRawCommand(command);
      
      _notifyStatusUpdate(RelayStatusUpdate.rawCommand(
        _deviceConfig!.deviceId,
        command,
      ));
    } catch (e) {
      throw RelayManagementException('Failed to send raw command: $e');
    }
  }

  /// Gets available USB devices for relay connection.
  Future<List<dynamic>> getAvailableDevices() async {
    try {
      return await Esp32RelayController.getAvailableDevices();
    } catch (e) {
      throw RelayManagementException('Failed to get available devices: $e');
    }
  }

  /// Checks if USB host mode is supported.
  Future<bool> isUsbHostSupported() async {
    try {
      return await Esp32RelayController.isUsbHostSupported();
    } catch (e) {
      return false;
    }
  }

  /// Registers relay device with server.
  /// 
  /// [serverUrl] is the server base URL.
  /// [authToken] is optional authentication token.
  Future<void> registerWithServer({
    required String serverUrl,
    String? authToken,
    Map<String, String>? additionalHeaders,
  }) async {
    _ensureInitialized();

    try {
      final registrationData = {
        'deviceId': _deviceConfig!.deviceId,
        'deviceName': _deviceConfig!.deviceName,
        'deviceType': 'esp32_relay',
        'relayCount': _deviceConfig!.relayCount,
        'capabilities': [
          'individual_control',
          'timed_control',
          'all_control',
          'toggle_support',
        ],
        'connectionType': 'usb_serial',
        'baudRate': _deviceConfig!.baudRate,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Use RelayApiService for server registration
      final apiService = api.RelayApiService.instance;

      // Initialize with HTTP client if not already initialized
      final httpClient = HttpClientService();
      httpClient.initialize(HttpClientConfig(
        baseUrl: serverUrl,
        connectTimeout: 30000,
        receiveTimeout: 30000,
        sendTimeout: 30000,
      ));
      await apiService.initialize(httpClient);

      // Register device using secure API
      await apiService.registerDevice(
        deviceConfig: _deviceConfig!,
        additionalInfo: {
          'authToken': authToken,
          'registrationMethod': 'server_registration',
          ...?additionalHeaders,
        },
        useSecureApi: true, // Use secure API by default
      );
      
      _notifyStatusUpdate(RelayStatusUpdate.serverRegistered(
        _deviceConfig!.deviceId,
        serverUrl,
      ));
    } catch (e) {
      throw RelayManagementException('Failed to register with server: $e');
    }
  }

  /// Updates relay configuration.
  Future<void> updateConfiguration(RelayDeviceConfig newConfig) async {
    _deviceConfig = newConfig;
    
    // Reinitialize if needed
    if (_isInitialized && _relayController != null) {
      await disconnect();
      await initialize(config: newConfig);
    }
  }

  /// Disconnects from relay device.
  Future<void> disconnect() async {
    if (_relayController != null) {
      await _relayController!.disconnect();
      _notifyStatusUpdate(RelayStatusUpdate.disconnected(_deviceConfig?.deviceId ?? 'unknown'));
    }
  }

  /// Disposes the relay management service.
  Future<void> dispose() async {
    await disconnect();
    await _relayController?.dispose();
    await _statusController?.close();
    
    _relayController = null;
    _statusController = null;
    _deviceConfig = null;
    _isInitialized = false;
  }

  /// Internal method to ensure service is initialized.
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw RelayManagementException('Relay management service not initialized');
    }
  }

  /// Internal method to ensure controller is connected.
  void _ensureConnected() {
    if (!isConnected) {
      throw RelayManagementException('Relay controller not connected');
    }
  }

  /// Internal method to notify status updates.
  void _notifyStatusUpdate(RelayStatusUpdate update) {
    _statusController?.add(update);
  }

  /// Creates device profile from configuration.
  DeviceProfile _createDeviceProfile(RelayDeviceConfig config) {
    // Get profile type from configuration (default to ESP32)
    final profileType = ConfigHelper.getValue('relay.device_profile', 'esp32');

    switch (profileType.toLowerCase()) {
      case 'esp32':
        return DeviceProfile.esp32();
      case 'arduino':
        return DeviceProfile.arduino();
      case 'simple':
        return DeviceProfile.simple();
      case 'custom':
        return DeviceProfile.custom(
          name: ConfigHelper.getValue('relay.custom_profile_name', 'Custom Device'),
          relayOnTemplate: ConfigHelper.getValue('relay.relay_on_template', 'R{relay}:1'),
          relayOffTemplate: ConfigHelper.getValue('relay.relay_off_template', 'R{relay}:0'),
          relayToggleTemplate: ConfigHelper.getValue('relay.relay_toggle_template', 'R{relay}:TOGGLE'),
          relayTimedTemplate: ConfigHelper.getValue('relay.relay_timed_template', 'R{relay}:{duration}'),
          allRelaysOnTemplate: ConfigHelper.getValue('relay.all_relays_on_template', 'ALL:1'),
          allRelaysOffTemplate: ConfigHelper.getValue('relay.all_relays_off_template', 'ALL:0'),
          allRelaysToggleTemplate: ConfigHelper.getValue('relay.all_relays_toggle_template', 'ALL:TOGGLE'),
          commandTerminator: ConfigHelper.getValue('relay.custom_command_terminator', '\n'),
        );
      default:
        // Fallback to ESP32 profile
        return DeviceProfile.esp32();
    }
  }
}

/// Configuration for relay device.
class RelayDeviceConfig {
  /// Unique device identifier
  final String deviceId;
  
  /// Device display name
  final String deviceName;
  
  /// Number of relays (default: 4)
  final int relayCount;
  
  /// Serial communication baud rate
  final int baudRate;
  
  /// Individual relay configurations
  final List<RelayConfig> relayConfigs;

  const RelayDeviceConfig({
    required this.deviceId,
    required this.deviceName,
    this.relayCount = 4,
    this.baudRate = 115200,
    this.relayConfigs = const [],
  });

  /// Creates configuration from JSON.
  factory RelayDeviceConfig.fromJson(Map<String, dynamic> json) {
    return RelayDeviceConfig(
      deviceId: json['deviceId'] as String,
      deviceName: json['deviceName'] as String,
      relayCount: json['relayCount'] as int? ?? 4,
      baudRate: json['baudRate'] as int? ?? 115200,
      relayConfigs: (json['relayConfigs'] as List<dynamic>?)
          ?.map((e) => RelayConfig.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  /// Converts to JSON.
  Map<String, dynamic> toJson() => {
    'deviceId': deviceId,
    'deviceName': deviceName,
    'relayCount': relayCount,
    'baudRate': baudRate,
    'relayConfigs': relayConfigs.map((e) => e.toJson()).toList(),
  };

  RelayDeviceConfig copyWith({
    String? deviceId,
    String? deviceName,
    int? relayCount,
    int? baudRate,
    List<RelayConfig>? relayConfigs,
  }) {
    return RelayDeviceConfig(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      relayCount: relayCount ?? this.relayCount,
      baudRate: baudRate ?? this.baudRate,
      relayConfigs: relayConfigs ?? this.relayConfigs,
    );
  }
}

/// Configuration for individual relay.
class RelayConfig {
  /// Relay index (0-based)
  final int index;
  
  /// Relay display name
  final String name;
  
  /// Relay description
  final String description;
  
  /// Default action timeout in milliseconds
  final int? defaultTimeoutMs;
  
  /// Whether relay is enabled
  final bool isEnabled;

  const RelayConfig({
    required this.index,
    required this.name,
    this.description = '',
    this.defaultTimeoutMs,
    this.isEnabled = true,
  });

  /// Creates configuration from JSON.
  factory RelayConfig.fromJson(Map<String, dynamic> json) {
    return RelayConfig(
      index: json['index'] as int,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      defaultTimeoutMs: json['defaultTimeoutMs'] as int?,
      isEnabled: json['isEnabled'] as bool? ?? true,
    );
  }

  /// Converts to JSON.
  Map<String, dynamic> toJson() => {
    'index': index,
    'name': name,
    'description': description,
    'defaultTimeoutMs': defaultTimeoutMs,
    'isEnabled': isEnabled,
  };

  RelayConfig copyWith({
    int? index,
    String? name,
    String? description,
    int? defaultTimeoutMs,
    bool? isEnabled,
  }) {
    return RelayConfig(
      index: index ?? this.index,
      name: name ?? this.name,
      description: description ?? this.description,
      defaultTimeoutMs: defaultTimeoutMs ?? this.defaultTimeoutMs,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }
}

/// Status update for relay operations.
class RelayStatusUpdate {
  /// Type of status update
  final RelayStatusType type;
  
  /// Device ID
  final String deviceId;
  
  /// Relay index (for individual relay operations)
  final int? relayIndex;
  
  /// Action performed
  final RelayAction? action;
  
  /// Duration for timed operations
  final int? durationMs;
  
  /// Raw command sent
  final String? rawCommand;
  
  /// Server URL for registration
  final String? serverUrl;
  
  /// Timestamp
  final DateTime timestamp;
  
  /// Additional message
  final String? message;

  const RelayStatusUpdate({
    required this.type,
    required this.deviceId,
    this.relayIndex,
    this.action,
    this.durationMs,
    this.rawCommand,
    this.serverUrl,
    required this.timestamp,
    this.message,
  });

  /// Creates connected status update.
  factory RelayStatusUpdate.connected(String deviceId) => RelayStatusUpdate(
    type: RelayStatusType.connected,
    deviceId: deviceId,
    timestamp: DateTime.now(),
  );

  /// Creates disconnected status update.
  factory RelayStatusUpdate.disconnected(String deviceId) => RelayStatusUpdate(
    type: RelayStatusType.disconnected,
    deviceId: deviceId,
    timestamp: DateTime.now(),
  );

  /// Creates relay action status update.
  factory RelayStatusUpdate.relayAction(String deviceId, int relayIndex, RelayAction action) => RelayStatusUpdate(
    type: RelayStatusType.relayAction,
    deviceId: deviceId,
    relayIndex: relayIndex,
    action: action,
    timestamp: DateTime.now(),
  );

  /// Creates timed relay action status update.
  factory RelayStatusUpdate.relayTimedAction(String deviceId, int relayIndex, int durationMs) => RelayStatusUpdate(
    type: RelayStatusType.relayTimedAction,
    deviceId: deviceId,
    relayIndex: relayIndex,
    durationMs: durationMs,
    timestamp: DateTime.now(),
  );

  /// Creates all relays action status update.
  factory RelayStatusUpdate.allRelaysAction(String deviceId, RelayAction action) => RelayStatusUpdate(
    type: RelayStatusType.allRelaysAction,
    deviceId: deviceId,
    action: action,
    timestamp: DateTime.now(),
  );

  /// Creates raw command status update.
  factory RelayStatusUpdate.rawCommand(String deviceId, String command) => RelayStatusUpdate(
    type: RelayStatusType.rawCommand,
    deviceId: deviceId,
    rawCommand: command,
    timestamp: DateTime.now(),
  );

  /// Creates server registered status update.
  factory RelayStatusUpdate.serverRegistered(String deviceId, String serverUrl) => RelayStatusUpdate(
    type: RelayStatusType.serverRegistered,
    deviceId: deviceId,
    serverUrl: serverUrl,
    timestamp: DateTime.now(),
  );

  @override
  String toString() => 'RelayStatusUpdate(type: $type, deviceId: $deviceId, timestamp: $timestamp)';
}

/// Types of relay status updates.
enum RelayStatusType {
  connected,
  disconnected,
  relayAction,
  relayTimedAction,
  allRelaysAction,
  rawCommand,
  serverRegistered,
  error,
} 
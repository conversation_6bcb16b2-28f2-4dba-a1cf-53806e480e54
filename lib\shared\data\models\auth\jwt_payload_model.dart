/// Model cho JWT payload structure
class JwtPayloadModel {
  final String? sub;
  final String? jti;
  final String? username;
  final int? iat;
  final int? exp;
  final String? currentTenantId;
  final TenantInfo? currentTenant;
  final RoleInfo? role;
  final List<EntityPermission> entityPermissions;
  final List<String> scopes;

  const JwtPayloadModel({
    this.sub,
    this.jti,
    this.username,
    this.iat,
    this.exp,
    this.currentTenantId,
    this.currentTenant,
    this.role,
    this.entityPermissions = const [],
    this.scopes = const [],
  });

  factory JwtPayloadModel.fromJson(Map<String, dynamic> json) {
    try {
      return JwtPayloadModel(
        sub: json['sub'] as String?,
        jti: json['jti'] as String?,
        username: json['username'] as String?,
        iat: json['iat'] as int?,
        exp: json['exp'] as int?,
        currentTenantId: json['current_tenant_id'] as String?,
        currentTenant: _parseTenantInfo(json['current_tenant']),
        role: _parseRoleInfo(json['role']),
        entityPermissions: _parseEntityPermissions(json['entity_permissions']),
        scopes: _parseScopes(json['scopes']),
      );
    } catch (e) {
      print('Error parsing JWT payload: $e');
      print('JWT payload keys: ${json.keys}');
      rethrow;
    }
  }

  // Helper method to safely parse tenant info
  static TenantInfo? _parseTenantInfo(dynamic tenantData) {
    if (tenantData == null) return null;

    try {
      if (tenantData is Map<String, dynamic>) {
        return TenantInfo.fromJson(tenantData);
      } else if (tenantData is String) {
        // If it's a string, it might be a tenant ID only
        return TenantInfo(id: tenantData, name: null);
      }
    } catch (e) {
      print('Error parsing tenant info: $e');
    }
    return null;
  }

  // Helper method to safely parse role info
  static RoleInfo? _parseRoleInfo(dynamic roleData) {
    if (roleData == null) return null;

    try {
      if (roleData is Map<String, dynamic>) {
        return RoleInfo.fromJson(roleData);
      } else if (roleData is String) {
        // If it's a string, it might be a role name only
        return RoleInfo(name: roleData, id: null, tenantId: null);
      }
    } catch (e) {
      print('Error parsing role info: $e');
    }
    return null;
  }

  // Helper method to safely parse entity permissions
  static List<EntityPermission> _parseEntityPermissions(dynamic permissionsData) {
    if (permissionsData == null) return [];

    try {
      if (permissionsData is List) {
        return permissionsData
            .where((e) => e != null)
            .map((e) {
              try {
                if (e is Map<String, dynamic>) {
                  return EntityPermission.fromJson(e);
                }
              } catch (ex) {
                print('Error parsing individual permission: $ex');
              }
              return null;
            })
            .where((e) => e != null)
            .cast<EntityPermission>()
            .toList();
      }
    } catch (e) {
      print('Error parsing entity permissions: $e');
    }
    return [];
  }

  // Helper method to safely parse scopes
  static List<String> _parseScopes(dynamic scopesData) {
    if (scopesData == null) return [];

    try {
      if (scopesData is List) {
        return scopesData
            .where((e) => e != null)
            .map((e) => e.toString())
            .toList();
      } else if (scopesData is String) {
        // If it's a single string, split by comma or return as single item
        return scopesData.contains(',')
            ? scopesData.split(',').map((s) => s.trim()).toList()
            : [scopesData];
      }
    } catch (e) {
      print('Error parsing scopes: $e');
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'sub': sub,
      'jti': jti,
      'username': username,
      'iat': iat,
      'exp': exp,
      'current_tenant_id': currentTenantId,
      'current_tenant': currentTenant?.toJson(),
      'role': role?.toJson(),
      'entity_permissions': entityPermissions.map((e) => e.toJson()).toList(),
      'scopes': scopes,
    };
  }

  /// Check if token is expired
  bool get isExpired {
    if (exp == null) return true;
    final expiryTime = DateTime.fromMillisecondsSinceEpoch(exp! * 1000);
    return DateTime.now().isAfter(expiryTime);
  }

  /// Get expiry time as DateTime
  DateTime? get expiryTime {
    if (exp == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(exp! * 1000);
  }

  /// Get issued time as DateTime
  DateTime? get issuedTime {
    if (iat == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(iat! * 1000);
  }
}

/// Model cho tenant info trong JWT
class TenantInfo {
  final String? id;
  final String? name;

  const TenantInfo({
    this.id,
    this.name,
  });

  factory TenantInfo.fromJson(Map<String, dynamic> json) {
    return TenantInfo(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

/// Model cho role info trong JWT
class RoleInfo {
  final String? tenantId;
  final String? name;
  final String? id;
  final Map<String, dynamic>? additionalData;

  const RoleInfo({
    this.tenantId,
    this.name,
    this.id,
    this.additionalData,
  });

  factory RoleInfo.fromJson(Map<String, dynamic> json) {
    try {
      // Create additional data safely
      Map<String, dynamic> additionalData = {};
      try {
        additionalData = Map<String, dynamic>.from(json);
        additionalData.removeWhere((key, value) => ['tenant_id', 'name', 'id', '_id'].contains(key));
      } catch (e) {
        // If copying fails, just use empty map
      }

      return RoleInfo(
        tenantId: json['tenant_id'] as String?,
        name: json['name'] as String?,
        // Handle both formats: id and _id
        id: json['id'] as String? ?? json['_id'] as String?,
        additionalData: additionalData,
      );
    } catch (e) {
      print('Error parsing RoleInfo: $e');
      // Return a minimal valid object
      return const RoleInfo(
        tenantId: null,
        name: null,
        id: null,
        additionalData: {},
      );
    }
  }

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{
      'tenant_id': tenantId,
      'name': name,
      'id': id,
    };
    
    if (additionalData != null) {
      result.addAll(additionalData!);
    }
    
    return result;
  }
}

/// Model cho entity permissions trong JWT
class EntityPermission {
  final String? entityType;
  final String? entityId;
  final List<String> permissions;
  final Map<String, dynamic>? additionalData;

  const EntityPermission({
    this.entityType,
    this.entityId,
    this.permissions = const [],
    this.additionalData,
  });

  factory EntityPermission.fromJson(Map<String, dynamic> json) {
    try {
      // Parse permissions safely
      List<String> permissionsList = [];
      if (json['permissions'] != null) {
        try {
          permissionsList = List<String>.from(json['permissions'] as List);
        } catch (e) {
          // If permissions is not a list, try to convert to string and split
          final permStr = json['permissions'].toString();
          if (permStr.isNotEmpty && permStr != 'null') {
            permissionsList = [permStr];
          }
        }
      }

      // Create additional data safely
      Map<String, dynamic> additionalData = {};
      try {
        additionalData = Map<String, dynamic>.from(json);
        additionalData.removeWhere((key, value) =>
            ['entity_type', 'entity_id', 'target_Type', 'target_id', 'permissions'].contains(key));
      } catch (e) {
        // If copying fails, just use empty map
      }

      return EntityPermission(
        // Handle both formats: entity_type/entity_id and target_Type/target_id
        entityType: json['entity_type'] as String? ?? json['target_Type'] as String?,
        entityId: json['entity_id'] as String? ?? json['target_id'] as String?,
        permissions: permissionsList,
        additionalData: additionalData,
      );
    } catch (e) {
      print('Error parsing EntityPermission: $e');
      // Return a minimal valid object
      return const EntityPermission(
        entityType: null,
        entityId: null,
        permissions: [],
        additionalData: {},
      );
    }
  }

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{
      'entity_type': entityType,
      'entity_id': entityId,
      'permissions': permissions,
    };

    if (additionalData != null) {
      result.addAll(additionalData!);
    }

    return result;
  }
}

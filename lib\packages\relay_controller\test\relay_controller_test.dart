import 'package:flutter_test/flutter_test.dart';
import 'package:relay_controller/relay_controller.dart';

void main() {
  group('RelayController Tests', () {
    test('HttpRelayController can be instantiated', () {
      final controller = HttpRelayController(
        deviceId: 'test-relay-001',
        urlOn: 'http://localhost:3000/on',
        urlOff: 'http://localhost:3000/off',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<HttpRelayController>());
      expect(controller.deviceId, 'test-relay-001');
    });

    test('SimpleHttpRelayController can be instantiated', () {
      final controller = SimpleHttpRelayController(
        deviceId: 'test-relay-002',
        baseUrl: 'http://localhost:3000',
        onEndpoint: '/on',
        offEndpoint: '/off',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<HttpRelayController>());
      expect(controller.deviceId, 'test-relay-002');
    });

    test('AuthenticatedHttpRelayController can be instantiated', () {
      final controller = AuthenticatedHttpRelayController(
        deviceId: 'test-relay-003',
        urlOn: 'http://localhost:3000/on',
        urlOff: 'http://localhost:3000/off',
        username: 'admin',
        password: 'password',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<HttpRelayController>());
      expect(controller.deviceId, 'test-relay-003');
    });

    test('BluetoothRelayController can be instantiated', () {
      final controller = BluetoothRelayController(
        deviceId: 'test-relay-004',
        deviceAddress: '00:11:22:33:44:55',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<BluetoothRelayController>());
      expect(controller.isConnected, false);
      expect(controller.deviceId, 'test-relay-004');
    });

    test('MqttRelayController can be instantiated', () {
      final controller = MqttRelayController(
        deviceId: 'test-relay-005',
        brokerHost: 'localhost:1883',
        topic: 'relay/control',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<MqttRelayController>());
      expect(controller.isConnected, false);
      expect(controller.deviceId, 'test-relay-005');
    });

    test('UsbRelayController can be instantiated', () {
      final controller = UsbRelayController(
        deviceId: 'test-relay-006',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<UsbRelayController>());
      expect(controller.isConnected, false);
      expect(controller.deviceId, 'test-relay-006');
    });

    test('AutoConnectUsbRelayController can be instantiated', () {
      final controller = AutoConnectUsbRelayController(
        deviceId: 'test-relay-007',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<UsbRelayController>());
      expect(controller, isA<AutoConnectUsbRelayController>());
      expect(controller.deviceId, 'test-relay-007');
    });

    test('Exception classes work correctly', () {
      const message = 'Test error';
      const cause = 'Test cause';

      const baseException = RelayControllerException(message, cause);
      expect(baseException.message, message);
      expect(baseException.cause, cause);
      expect(baseException.toString(), contains(message));
      expect(baseException.toString(), contains(cause));

      const bluetoothException = BluetoothRelayException(message);
      expect(bluetoothException, isA<RelayControllerException>());
      expect(bluetoothException.message, message);

      const httpException = HttpRelayException(message);
      expect(httpException, isA<RelayControllerException>());
      expect(httpException.message, message);

      const mqttException = MqttRelayException(message);
      expect(mqttException, isA<RelayControllerException>());
      expect(mqttException.message, message);

      const usbException = UsbRelayException(message);
      expect(usbException, isA<RelayControllerException>());
      expect(usbException.message, message);
    });

    test('HttpMethod enum has correct values', () {
      expect(HttpMethod.values.length, 4);
      expect(HttpMethod.values, contains(HttpMethod.get));
      expect(HttpMethod.values, contains(HttpMethod.post));
      expect(HttpMethod.values, contains(HttpMethod.put));
      expect(HttpMethod.values, contains(HttpMethod.patch));
    });

    test('SecureHttpRelayController can be instantiated', () {
      final controller = SecureHttpRelayController(
        deviceId: 'secure-relay-001',
        deviceType: 'flutter_app',
        serverBaseUrl: 'https://api.example.com',
        deviceName: 'Secure Test Relay',
      );

      expect(controller, isA<RelayController>());
      expect(controller, isA<SecureHttpRelayController>());
      expect(controller.deviceId, 'secure-relay-001');
      expect(controller.deviceType, 'flutter_app');
      expect(controller.isAuthenticated, false);
    });

    test('DeviceAuth can generate device IDs', () {
      final deviceId1 = DeviceAuth.generateDeviceId();
      final deviceId2 = DeviceAuth.generateDeviceId(prefix: 'test');

      expect(deviceId1, isNotEmpty);
      expect(deviceId2, startsWith('test-'));
      expect(deviceId1, isNot(equals(deviceId2)));
    });

    test('DeviceAuth can create and verify HMAC signatures', () {
      const secretKey = 'test-secret-key';
      const deviceId = 'test-device';
      const action = 'unlock';
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      final signature = DeviceAuth.createHmacSignature(
        secretKey: secretKey,
        deviceId: deviceId,
        action: action,
        timestamp: timestamp,
      );

      expect(signature, isNotEmpty);

      final isValid = DeviceAuth.verifyHmacSignature(
        secretKey: secretKey,
        signature: signature,
        deviceId: deviceId,
        action: action,
        timestamp: timestamp,
      );

      expect(isValid, true);

      // Test with wrong signature
      final isInvalid = DeviceAuth.verifyHmacSignature(
        secretKey: secretKey,
        signature: 'wrong-signature',
        deviceId: deviceId,
        action: action,
        timestamp: timestamp,
      );

      expect(isInvalid, false);
    });

    test('DeviceAuth can validate timestamps', () {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final recent = now - 60; // 1 minute ago
      final old = now - 600; // 10 minutes ago

      expect(DeviceAuth.isTimestampValid(now), true);
      expect(DeviceAuth.isTimestampValid(recent), true);
      expect(DeviceAuth.isTimestampValid(old), false);
    });

    test('DeviceRegistrationRequest can be serialized', () {
      const request = DeviceRegistrationRequest(
        deviceId: 'test-device',
        deviceType: 'flutter_app',
        deviceName: 'Test Device',
        hardwareHash: 'abc123',
      );

      final json = request.toJson();
      expect(json['device_id'], 'test-device');
      expect(json['device_type'], 'flutter_app');
      expect(json['device_name'], 'Test Device');
      expect(json['hardware_hash'], 'abc123');
    });

    test('SecureRelayCommand can be created with signature', () {
      const secretKey = 'test-secret';
      final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      final command = SecureRelayCommand(
        deviceId: 'test-device',
        action: 'unlock',
        timestamp: timestamp,
      );

      final signedCommand = command.withSignature(secretKey);

      expect(signedCommand.signature, isNotNull);
      expect(signedCommand.signature, isNotEmpty);
      expect(signedCommand.deviceId, command.deviceId);
      expect(signedCommand.action, command.action);
      expect(signedCommand.timestamp, command.timestamp);
    });
  });
}

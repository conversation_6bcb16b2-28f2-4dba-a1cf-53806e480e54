import 'package:get_it/get_it.dart';
import '../../domain/use_cases/tenant/get_units_use_case.dart';
import '../../domain/repositories/tenant_repository.dart';

/// Service for managing units API calls
class UnitsApiService {
  final GetUnitsUseCase _getUnitsUseCase = GetIt.instance<GetUnitsUseCase>();
  final TenantRepository _tenantRepository = GetIt.instance<TenantRepository>();

  /// Cache for units to avoid repeated API calls
  List<UnitUIModel>? _cachedUnits;
  DateTime? _lastCacheTime;
  static const Duration _cacheTimeout = Duration(minutes: 5);

  /// Get all units with caching
  Future<List<UnitUIModel>> getUnits({
    bool forceRefresh = false,
    String? tenantId,
  }) async {
    // Check cache validity
    if (!forceRefresh && 
        _cachedUnits != null && 
        _lastCacheTime != null &&
        DateTime.now().difference(_lastCacheTime!) < _cacheTimeout) {
      return _cachedUnits!;
    }

    try {
      final result = await _getUnitsUseCase.call(
        page: 1,
        limit: 100, // Get all units in one call
        tenantId: tenantId,
      );

      return result.fold(
        (failure) {
          throw Exception('Failed to load units: ${failure.message}');
        },
        (unitsData) {
          final units = unitsData.map((unitData) => UnitUIModel.fromMap(unitData)).toList();
          
          // Update cache
          _cachedUnits = units;
          _lastCacheTime = DateTime.now();
          
          return units;
        },
      );
    } catch (e) {
      throw Exception('Failed to load units: $e');
    }
  }

  /// Get units by tenant ID
  Future<List<UnitUIModel>> getUnitsByTenant(String tenantId) async {
    try {
      final result = await _tenantRepository.getUnitsByTenant(tenantId);

      return result.fold(
        (failure) {
          throw Exception('Failed to load units by tenant: ${failure.message}');
        },
        (unitsData) {
          return unitsData.map((unitData) => UnitUIModel.fromMap(unitData)).toList();
        },
      );
    } catch (e) {
      throw Exception('Failed to load units by tenant: $e');
    }
  }

  /// Get unit by ID
  Future<UnitUIModel?> getUnitById(String unitId) async {
    try {
      final result = await _tenantRepository.getUnitById(unitId);

      return result.fold(
        (failure) {
          return null; // Return null if unit not found
        },
        (unitData) {
          return UnitUIModel.fromMap(unitData);
        },
      );
    } catch (e) {
      return null;
    }
  }

  /// Clear cache
  void clearCache() {
    _cachedUnits = null;
    _lastCacheTime = null;
  }

  /// Get units for dropdown/filter (returns simplified list)
  Future<List<UnitOption>> getUnitsForFilter({String? tenantId}) async {
    try {
      final units = await getUnits(tenantId: tenantId);

      // Convert to simple options for UI
      final options = units.map((unit) => UnitOption(
        id: unit.id,
        name: unit.name,
        parentId: unit.parentUnitId,
      )).toList();

      // Sort by name
      options.sort((a, b) => a.name.compareTo(b.name));

      return options;
    } catch (e) {
      // Return default units as fallback
      return getDefaultUnits();
    }
  }

  /// Get default units (fallback when API fails)
  List<UnitOption> getDefaultUnits() {
    return [
      const UnitOption(id: 'pd', name: 'Phòng PD'),
      const UnitOption(id: 'it', name: 'Phòng IT'),
      const UnitOption(id: 'hr', name: 'Phòng Nhân sự'),
      const UnitOption(id: 'finance', name: 'Phòng Tài chính'),
    ];
  }
}

/// UI model for units
class UnitUIModel {
  final String id;
  final String name;
  final String tenantId;
  final String? parentUnitId;
  final List<String> mappings;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UnitUIModel({
    required this.id,
    required this.name,
    required this.tenantId,
    this.parentUnitId,
    this.mappings = const [],
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UnitUIModel.fromMap(Map<String, dynamic> map) {
    return UnitUIModel(
      id: map['id'] as String? ?? map['_id'] as String,
      name: map['name'] as String,
      tenantId: map['tenant_id'] as String,
      parentUnitId: map['parent_unit_id'] as String?,
      mappings: (map['mappings'] as List<dynamic>?)?.cast<String>() ?? [],
      createdBy: map['created_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'tenant_id': tenantId,
      'parent_unit_id': parentUnitId,
      'mappings': mappings,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'UnitUIModel(id: $id, name: $name, tenantId: $tenantId)';
  }
}

/// Simple unit option for dropdowns and filters
class UnitOption {
  final String id;
  final String name;
  final String? parentId;

  const UnitOption({
    required this.id,
    required this.name,
    this.parentId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnitOption && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => name;
}

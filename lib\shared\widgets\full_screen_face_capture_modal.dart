import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../apps/mobile/presentation/screens/users_screen/user_face_register_screen.dart';
import 'face_providers_wrapper.dart';
import '../providers/face_providers_lifecycle_manager.dart';

/// Full-screen modal for face capture that blocks all navigation
/// and takes complete control of the screen using root navigator
class FullScreenFaceCaptureModal {
  static bool _isShowing = false;

  /// Show full-screen face capture modal using root navigator
  static Future<dynamic> show(BuildContext context) async {
    if (_isShowing) return null;

    _isShowing = true;

    // Set full-screen immersive mode first
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );

    // Lock to portrait
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    try {
      // Get navigator before async operations
      final navigator = Navigator.of(context, rootNavigator: true);

      // Use root navigator to ensure modal is above everything
      final result = await navigator.push(
        PageRouteBuilder<dynamic>(
          opaque: true,
          barrierDismissible: false,
          fullscreenDialog: true,
          pageBuilder: (context, animation, secondaryAnimation) {
            return PopScope(
              canPop: false, // Block all pop attempts
              onPopInvokedWithResult: (didPop, result) {
                debugPrint('🔙 Top-level Modal PopScope intercepted: didPop=$didPop');
                if (!didPop) {
                  debugPrint('🔀 Top-level delegating to inner components');
                  // Don't handle here - let inner components handle
                  // This PopScope just ensures we don't accidentally exit
                }
              },
              child: _FullScreenModalContent(
                onResult: (result) {
                  // Use a safer way to close modal with timeout
                  try {
                    final navigator = Navigator.of(context, rootNavigator: true);
                    if (navigator.canPop()) {
                      navigator.pop(result);
                    }
                  } catch (e) {
                    debugPrint('⚠️ Error closing modal: $e');
                  }
                },
              ),
            );
          },
          // Add smooth fade transition for modal - faster
          transitionDuration: const Duration(milliseconds: 75),
          reverseTransitionDuration: const Duration(milliseconds: 75),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Smooth fade in/out with slight scale effect
            return FadeTransition(
              opacity: CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut,
              ),
              child: ScaleTransition(
                scale: Tween<double>(
                  begin: 0.95,
                  end: 1.0,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutCubic,
                )),
                child: child,
              ),
            );
          },
        ),
      );

      return result;
    } finally {
      _isShowing = false;
      // Restore system UI
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: SystemUiOverlay.values,
      );

      // Reset orientation
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// Force hide (for emergency cleanup)
  static void forceHide() {
    _isShowing = false;
  }

  /// Check if modal is currently showing
  static bool get isShowing => _isShowing;
}

/// Internal modal content widget
class _FullScreenModalContent extends StatefulWidget {
  final Function(dynamic) onResult;

  const _FullScreenModalContent({
    required this.onResult,
  });

  @override
  State<_FullScreenModalContent> createState() => _FullScreenModalContentState();
}

class _FullScreenModalContentState extends State<_FullScreenModalContent> {
  @override
  Widget build(BuildContext context) {
    return Material(
        type: MaterialType.canvas,
        color: Colors.black,
        borderRadius: BorderRadius.zero, // Remove any default border radius
        child: MediaQuery.removeViewInsets(
          context: context,
          removeLeft: true,
          removeTop: true,
          removeRight: true,
          removeBottom: true,
          child: MediaQuery.removePadding(
            context: context,
            removeLeft: true,
            removeTop: true,
            removeRight: true,
            removeBottom: true,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black,
              child: _PreloadedFaceCapture(
                onExit: widget.onResult,
              ),
            ),
          ),
        ),
    );
  }
}

/// Wrapper for face capture screen with exit handling
class _FaceCaptureScreenWrapper extends StatefulWidget {
  final Function(dynamic) onExit;

  const _FaceCaptureScreenWrapper({
    required this.onExit,
  });

  @override
  State<_FaceCaptureScreenWrapper> createState() => _FaceCaptureScreenWrapperState();
}

class _FaceCaptureScreenWrapperState extends State<_FaceCaptureScreenWrapper> {
  bool _isDisposing = false;
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void dispose() {
    debugPrint('🧹 Disposing FaceCaptureScreenWrapper...');
    _isDisposing = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Intercept all gestures including back swipe
      onPanUpdate: (details) {
        if (!_isDisposing && mounted) {
          // Detect back swipe gesture (right to left on Android)
          if (details.delta.dx > 10) {
            _handleBackButton();
          }
        }
      },
      child: _SafeNavigator(
        key: _navigatorKey,
        onExit: widget.onExit,
      ),
    );
  }

  void _handleBackButton() {
    if (!_isDisposing && mounted) {
      // Navigator will handle the back button properly
      // The UserFaceRegisterScreen inside Navigator will handle confirmation
      debugPrint('🔙 Modal back button/gesture intercepted - Navigator will handle');
    }
  }


}

/// Preloaded face capture with loading states
class _PreloadedFaceCapture extends StatefulWidget {
  final Function(dynamic) onExit;

  const _PreloadedFaceCapture({
    required this.onExit,
  });

  @override
  State<_PreloadedFaceCapture> createState() => _PreloadedFaceCaptureState();
}

class _PreloadedFaceCaptureState extends State<_PreloadedFaceCapture> {
  bool _isLoading = true;
  String _loadingMessage = 'Initializing face detection...';
  final FaceProvidersLifecycleManager _lifecycleManager = FaceProvidersLifecycleManager.instance;
  String? _currentWidgetId; // Track current widget ID

  @override
  void initState() {
    super.initState();
    _preloadComponents();
  }

  Future<void> _preloadComponents() async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        setState(() {
          _loadingMessage = retryCount > 0
              ? 'Retrying initialization... (${retryCount + 1}/$maxRetries)'
              : 'Preparing face detection...';
        });

        // Generate unique widget ID to avoid conflicts
        _currentWidgetId = 'modal_preload_${DateTime.now().millisecondsSinceEpoch}';

        // Force dispose any existing providers first
        if (_lifecycleManager.isReady) {
          debugPrint('🧹 Force disposing existing providers before re-init');
          await _lifecycleManager.forceDisposeProviders();
          await Future.delayed(const Duration(milliseconds: 1500)); // Longer wait for cleanup
        }

        if (!mounted) return;

        setState(() {
          _loadingMessage = 'Initializing camera system...';
        });

        // Register widget with unique ID
        await _lifecycleManager.registerWidget(_currentWidgetId!, autoInitialize: true);

        // Wait for providers to be ready with timeout
        int attempts = 0;
        const maxAttempts = 80; // 8 seconds timeout (reduced from 15s)

        while (!_lifecycleManager.isReady && mounted && attempts < maxAttempts) {
          await Future.delayed(const Duration(milliseconds: 100));
          attempts++;

          // Update progress every 2 seconds
          if (attempts % 20 == 0) {
            setState(() {
              _loadingMessage = 'Initializing... ${(attempts / 10).toInt()}s';
            });
          }
        }

        if (attempts >= maxAttempts) {
          throw Exception('Initialization timeout after 8 seconds');
        }

        if (!mounted) return;

        setState(() {
          _loadingMessage = 'Preparing camera surface...';
        });

        // Extended delay to ensure graphics buffers are ready
        await Future.delayed(const Duration(milliseconds: 1000));

        // Verify camera is actually ready
        if (_lifecycleManager.faceCaptureProvider?.isCameraReady != true) {
          throw Exception('Camera failed to initialize properly');
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          debugPrint('✅ Face capture components preloaded successfully');
        }

        // Success, exit retry loop
        return;

      } catch (e) {
        retryCount++;
        debugPrint('❌ Preload attempt $retryCount failed: $e');

        if (retryCount < maxRetries) {
          // Wait before retry with exponential backoff
          final waitTime = Duration(milliseconds: 1000 * retryCount);
          setState(() {
            _loadingMessage = 'Initialization failed. Retrying in ${waitTime.inSeconds}s...';
          });
          await Future.delayed(waitTime);
        } else {
          // All retries failed
          debugPrint('❌ All preload attempts failed');
          if (mounted) {
            setState(() {
              _loadingMessage = 'Initialization failed. Please try again later.';
            });
            // Show error for 3 seconds then exit
            await Future.delayed(const Duration(seconds: 3));
            if (mounted) {
              widget.onExit(false);
            }
          }
        }
      }
    }
  }

  @override
  void dispose() {
    // Safely unregister with the correct widget ID
    if (_currentWidgetId != null) {
      try {
        _lifecycleManager.unregisterWidget(_currentWidgetId!, autoDispose: true);
        debugPrint('✅ Successfully unregistered widget: $_currentWidgetId');
      } catch (e) {
        debugPrint('⚠️ Error unregistering widget $_currentWidgetId: $e');
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200), // Faster transition
      switchInCurve: Curves.easeOut,
      switchOutCurve: Curves.easeIn,
      transitionBuilder: (Widget child, Animation<double> animation) {
        // Smooth fade and scale transition
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
      child: _isLoading
          ? _buildLoadingScreen()
          : FaceProvidersWrapper(
              key: const ValueKey('face_capture_screen'), // Key for AnimatedSwitcher
              autoInitialize: false, // Already initialized
              child: _FaceCaptureScreenWrapper(
                onExit: widget.onExit,
              ),
            ),
    );
  }

  Widget _buildLoadingScreen() {
    return GestureDetector(
      key: const ValueKey('loading_screen'), // Key for AnimatedSwitcher
      onTap: () {
        if (_loadingMessage.contains('Error') || _loadingMessage.contains('retry')) {
          // Retry initialization
          setState(() {
            _isLoading = true;
            _loadingMessage = 'Retrying initialization...';
          });
          _preloadComponents();
        }
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            debugPrint('🔙 Loading screen back button pressed');
            if (!didPop) {
              widget.onExit(false);
            }
          },
          child: Center(
            child: TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 300),
              tween: Tween<double>(begin: 0.0, end: 1.0),
              builder: (context, opacity, child) {
                return Opacity(
                  opacity: opacity,
                  child: Transform.scale(
                    scale: 0.8 + (0.2 * opacity), // Smooth scale from 0.8 to 1.0
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (!_loadingMessage.contains('Error'))
                          const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        if (_loadingMessage.contains('Error'))
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                        const SizedBox(height: 24),
                        Text(
                          _loadingMessage,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (_loadingMessage.contains('retry'))
                          const Padding(
                            padding: EdgeInsets.only(top: 16),
                            child: Text(
                              'Tap to retry',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        const SizedBox(height: 32),
                        // Emergency exit button
                        TextButton(
                          onPressed: () {
                            debugPrint('🚪 Emergency exit pressed');
                            widget.onExit(false);
                          },
                          child: const Text(
                            'Exit',
                            style: TextStyle(
                              color: Colors.white54,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

/// Safe Navigator that handles disposal assertions
class _SafeNavigator extends StatefulWidget {
  final Function(dynamic) onExit;

  const _SafeNavigator({
    super.key,
    required this.onExit,
  });

  @override
  State<_SafeNavigator> createState() => _SafeNavigatorState();
}

class _SafeNavigatorState extends State<_SafeNavigator> {
  bool _isDisposing = false;

  @override
  void dispose() {
    _isDisposing = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposing) {
      return Container(color: Colors.black);
    }

    // Direct widget without Navigator - Navigator might be blocking back button
    return _ModifiedUserFaceRegisterScreen(
      onExit: widget.onExit,
    );
  }
}

/// Modified version of UserFaceRegisterScreen for modal use
class _ModifiedUserFaceRegisterScreen extends StatelessWidget {
  final Function(dynamic) onExit;

  const _ModifiedUserFaceRegisterScreen({
    required this.onExit,
  });

  @override
  Widget build(BuildContext context) {
    return UserFaceRegisterScreen(
      onExit: onExit,
    );
  }
}

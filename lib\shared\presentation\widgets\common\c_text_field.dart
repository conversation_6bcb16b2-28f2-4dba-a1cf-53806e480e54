import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'c_label.dart';
import '../../icons/view_icon.dart';

class CTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hintText;
  final bool isPassword;
  final bool obscureText;
  final VoidCallback? onToggleObscure;
  final String? Function(String?)? validator;
  final bool isRequired;
  final double? height;
  final EdgeInsets? contentPadding;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;

  const CTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.hintText,
    this.isPassword = false,
    this.obscureText = false,
    this.onToggleObscure,
    this.validator,
    this.isRequired = false,
    this.height,
    this.contentPadding,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        CLabel(text: label, isRequired: isRequired, style: labelStyle),
        const SizedBox(height: 5.25),
        // Input field
        SizedBox(
          height: height ?? 38,
          child: TextFormField(
            controller: controller,
            obscureText: obscureText,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: hintStyle ?? AppTextStyles.placeholder,
              contentPadding:
                  contentPadding ??
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 9.0),
              filled: true,
              fillColor: AppColors.background,
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              focusedErrorBorder: InputBorder.none,
              suffixIcon: isPassword
                  ? IconButton(
                      icon: ViewIcon(
                        isVisible: !obscureText,
                        size: 16,
                        color: obscureText
                            ? AppColors.textPlaceholder
                            : AppColors.primary,
                      ),
                      onPressed: onToggleObscure,
                    )
                  : null,
              isDense: true,
            ),
            validator: validator,
            style: textStyle ?? AppTextStyles.inputText,
          ),
        ),
      ],
    );
  }
}

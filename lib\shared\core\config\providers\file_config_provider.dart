/// File-based Configuration Provider
/// 
/// Loads configuration from JSON files with support for environment-specific
/// configurations and hot reload during development.

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import '../flexible_config_system.dart';

/// File-based configuration provider
class FileConfigProvider implements ConfigProvider {
  final String configFileName;
  final String? environmentSuffix;
  final bool enableHotReload;
  
  Map<String, dynamic> _currentConfig = {};
  final StreamController<Map<String, dynamic>> _configController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  Timer? _fileWatcher;
  String? _configFilePath;
  DateTime? _lastModified;

  FileConfigProvider({
    this.configFileName = 'app_config.json',
    this.environmentSuffix,
    this.enableHotReload = kDebugMode,
  });

  @override
  String get name => 'FileConfigProvider';

  @override
  ConfigSource get source => ConfigSource.localFile;

  @override
  Stream<Map<String, dynamic>> get configStream => _configController.stream;

  @override
  Future<void> initialize() async {
    await _loadConfiguration();
    
    if (enableHotReload && !kIsWeb) {
      _startFileWatcher();
    }
  }

  @override
  Future<Map<String, dynamic>> loadConfiguration() async {
    await _loadConfiguration();
    return Map<String, dynamic>.from(_currentConfig);
  }

  @override
  Future<void> saveConfiguration(Map<String, dynamic> config) async {
    if (kIsWeb) {
      throw UnsupportedError('File saving not supported on web platform');
    }

    final file = await _getConfigFile();
    final jsonString = JsonEncoder.withIndent('  ').convert(config);
    await file.writeAsString(jsonString);
    
    _currentConfig = Map<String, dynamic>.from(config);
    _configController.add(_currentConfig);
  }

  @override
  Future<void> dispose() async {
    _fileWatcher?.cancel();
    await _configController.close();
  }

  /// Load configuration from file
  Future<void> _loadConfiguration() async {
    try {
      // Try to load from external storage first (for runtime changes)
      final externalConfig = await _loadFromExternalStorage();
      if (externalConfig != null) {
        _currentConfig = externalConfig;
        _configController.add(_currentConfig);
        return;
      }

      // Fall back to assets
      final assetConfig = await _loadFromAssets();
      if (assetConfig != null) {
        _currentConfig = assetConfig;
        _configController.add(_currentConfig);
        return;
      }

      // If no config found, use empty config
      _currentConfig = {};
      _configController.add(_currentConfig);
      
    } catch (e) {
      debugPrint('Error loading configuration: $e');
      _currentConfig = {};
      _configController.add(_currentConfig);
    }
  }

  /// Load configuration from external storage
  Future<Map<String, dynamic>?> _loadFromExternalStorage() async {
    if (kIsWeb) return null;

    try {
      final file = await _getConfigFile();
      if (!await file.exists()) return null;

      final content = await file.readAsString();
      final config = jsonDecode(content) as Map<String, dynamic>;
      
      // Update last modified time
      final stat = await file.stat();
      _lastModified = stat.modified;
      _configFilePath = file.path;
      
      return config;
    } catch (e) {
      debugPrint('Error loading from external storage: $e');
      return null;
    }
  }

  /// Load configuration from assets
  Future<Map<String, dynamic>?> _loadFromAssets() async {
    try {
      final fileName = _getConfigFileName();
      final content = await rootBundle.loadString('assets/config/$fileName');
      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error loading from assets: $e');
      return null;
    }
  }

  /// Get configuration file name with environment suffix
  String _getConfigFileName() {
    if (environmentSuffix != null && environmentSuffix!.isNotEmpty) {
      final baseName = configFileName.replaceAll('.json', '');
      return '${baseName}_$environmentSuffix.json';
    }
    return configFileName;
  }

  /// Get configuration file from external storage
  Future<File> _getConfigFile() async {
    final directory = await getApplicationDocumentsDirectory();
    final configDir = Directory('${directory.path}/config');
    
    if (!await configDir.exists()) {
      await configDir.create(recursive: true);
    }
    
    final fileName = _getConfigFileName();
    return File('${configDir.path}/$fileName');
  }

  /// Start file watcher for hot reload
  void _startFileWatcher() {
    _fileWatcher = Timer.periodic(Duration(seconds: 1), (timer) async {
      if (_configFilePath == null) return;
      
      try {
        final file = File(_configFilePath!);
        if (!await file.exists()) return;
        
        final stat = await file.stat();
        if (_lastModified == null || stat.modified.isAfter(_lastModified!)) {
          debugPrint('Configuration file changed, reloading...');
          await _loadConfiguration();
        }
      } catch (e) {
        debugPrint('Error checking file modification: $e');
      }
    });
  }

  /// Create default configuration file
  Future<void> createDefaultConfig(Map<String, dynamic> defaultConfig) async {
    if (kIsWeb) return;

    final file = await _getConfigFile();
    if (await file.exists()) return; // Don't overwrite existing config

    final jsonString = JsonEncoder.withIndent('  ').convert(defaultConfig);
    await file.writeAsString(jsonString);
    
    debugPrint('Created default configuration file: ${file.path}');
  }

  /// Get configuration file path
  Future<String> getConfigFilePath() async {
    final file = await _getConfigFile();
    return file.path;
  }

  /// Check if configuration file exists
  Future<bool> configFileExists() async {
    if (kIsWeb) return false;
    
    final file = await _getConfigFile();
    return await file.exists();
  }

  /// Backup current configuration
  Future<void> backupConfiguration() async {
    if (kIsWeb || _currentConfig.isEmpty) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/config/backups');
      
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupFile = File('${backupDir.path}/config_backup_$timestamp.json');
      
      final jsonString = JsonEncoder.withIndent('  ').convert(_currentConfig);
      await backupFile.writeAsString(jsonString);
      
      debugPrint('Configuration backed up to: ${backupFile.path}');
    } catch (e) {
      debugPrint('Error backing up configuration: $e');
    }
  }

  /// Restore configuration from backup
  Future<bool> restoreFromBackup(String backupFileName) async {
    if (kIsWeb) return false;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupFile = File('${directory.path}/config/backups/$backupFileName');
      
      if (!await backupFile.exists()) return false;
      
      final content = await backupFile.readAsString();
      final config = jsonDecode(content) as Map<String, dynamic>;
      
      await saveConfiguration(config);
      return true;
    } catch (e) {
      debugPrint('Error restoring from backup: $e');
      return false;
    }
  }

  /// List available backup files
  Future<List<String>> listBackups() async {
    if (kIsWeb) return [];

    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/config/backups');
      
      if (!await backupDir.exists()) return [];
      
      final files = await backupDir.list().toList();
      return files
          .whereType<File>()
          .where((file) => file.path.endsWith('.json'))
          .map((file) => file.path.split('/').last)
          .toList()
        ..sort((a, b) => b.compareTo(a)); // Sort by name (newest first)
    } catch (e) {
      debugPrint('Error listing backups: $e');
      return [];
    }
  }
}

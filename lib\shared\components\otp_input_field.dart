import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

/// Controller cho OTP Input Field
class OtpController {
  _OtpInputFieldState? _state;

  void _attach(_OtpInputFieldState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  /// Clear tất cả các ô input
  void clear() {
    _state?.clear();
  }

  /// Lấy giá trị OTP hiện tại
  String get value {
    return _state?.value ?? '';
  }
}

/// OTP Input component cho việc nhập mã xác thực
class OtpInputField extends StatefulWidget {
  final int length;
  final ValueChanged<String>? onCompleted;
  final ValueChanged<String>? onChanged;
  final bool isEnabled;
  final String? errorText;
  final OtpController? controller;

  const OtpInputField({
    super.key,
    this.length = 6,
    this.onCompleted,
    this.onChanged,
    this.isEnabled = true,
    this.errorText,
    this.controller,
  });

  @override
  State<OtpInputField> createState() => _OtpInputFieldState();
}

class _OtpInputFieldState extends State<OtpInputField> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.length,
      (index) => TextEditingController(),
    );
    _focusNodes = List.generate(
      widget.length,
      (index) => FocusNode(),
    );

    // Attach controller
    widget.controller?._attach(this);

    // Focus vào ô đầu tiên
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_focusNodes.isNotEmpty) {
        _focusNodes[0].requestFocus();
      }
    });
  }

  @override
  void dispose() {
    widget.controller?._detach();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _onChanged(String value, int index) {
    if (value.isNotEmpty) {
      // Di chuyển đến ô tiếp theo
      if (index < widget.length - 1) {
        _focusNodes[index + 1].requestFocus();
        _currentIndex = index + 1;
      } else {
        _focusNodes[index].unfocus();
      }
    }

    // Gọi callback
    final otpValue = _controllers.map((controller) => controller.text).join();
    widget.onChanged?.call(otpValue);

    // Kiểm tra nếu đã nhập đủ
    if (otpValue.length == widget.length) {
      widget.onCompleted?.call(otpValue);
    }
  }

  void _onKeyEvent(KeyEvent event, int index) {
    if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.backspace) {
      if (_controllers[index].text.isEmpty && index > 0) {
        // Di chuyển về ô trước đó khi backspace
        _focusNodes[index - 1].requestFocus();
        _currentIndex = index - 1;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(widget.length, (index) {
            return _buildOtpBox(index);
          }),
        ),
        if (widget.errorText != null) ...[
          SizedBox(height: AppDimensions.spacing8),
          Text(
            widget.errorText!,
            style: AppTextStyles.error,
          ),
        ],
      ],
    );
  }

  Widget _buildOtpBox(int index) {
    final isActive = _currentIndex == index;
    final hasValue = _controllers[index].text.isNotEmpty;
    
    return Container(
      width: AppDimensions.otpInputSize,
      height: AppDimensions.otpInputHeight,
      decoration: BoxDecoration(
        color: hasValue || isActive ? AppColors.surface : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _getBorderColor(index),
          width: AppDimensions.borderNormal,
        ),
      ),
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: (event) => _onKeyEvent(event, index),
        child: TextField(
          controller: _controllers[index],
          focusNode: _focusNodes[index],
          enabled: widget.isEnabled,
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          maxLength: 1,
          style: AppTextStyles.labelLarge.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          decoration: const InputDecoration(
            border: InputBorder.none,
            counterText: '',
            contentPadding: EdgeInsets.zero,
          ),
          onChanged: (value) => _onChanged(value, index),
          onTap: () {
            setState(() {
              _currentIndex = index;
            });
          },
        ),
      ),
    );
  }

  Color _getBorderColor(int index) {
    if (widget.errorText != null) {
      return AppColors.error;
    }
    
    final isActive = _currentIndex == index;
    final hasValue = _controllers[index].text.isNotEmpty;
    
    if (isActive || hasValue) {
      return AppColors.borderFocused;
    }
    
    return AppColors.border;
  }

  /// Lấy giá trị OTP hiện tại
  String get value {
    return _controllers.map((controller) => controller.text).join();
  }

  /// Clear tất cả các ô input
  void clear() {
    for (var controller in _controllers) {
      controller.clear();
    }
    setState(() {
      _currentIndex = 0;
    });
    _focusNodes[0].requestFocus();
  }
}

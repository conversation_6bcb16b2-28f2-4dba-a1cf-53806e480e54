<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <include layout="@layout/titlebar" />

    <Button
        android:id="@+id/relay_open"
        style="@style/buttonNumberStyle"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:onClick="onrelayclick"
        android:background="@drawable/button_number_violet_shape"
        android:text="open relay" />

    <Button
        android:id="@+id/relay_close"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:onClick="onrelayclick"
        style="@style/buttonNumberStyle"
        android:background="@drawable/button_number_violet_shape"
        android:text="close relay" />

</LinearLayout>
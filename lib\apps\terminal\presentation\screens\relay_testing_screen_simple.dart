import 'package:flutter/material.dart';
import 'usb_ttl_relay_testing_screen.dart';

/// Simplified Relay Testing Screen for build compatibility
///
/// This is a simplified version that doesn't depend on services
/// that may not be fully implemented yet.
class RelayTestingScreen extends StatefulWidget {
  const RelayTestingScreen({super.key});

  @override
  State<RelayTestingScreen> createState() => _RelayTestingScreenState();
}

class _RelayTestingScreenState extends State<RelayTestingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;



  // Test state
  bool _isInitialized = false;
  String _testStatus = 'Ready for testing';
  List<String> _testLogs = [];

  // Configuration controllers
  final TextEditingController _deviceIdController = TextEditingController();
  final TextEditingController _deviceNameController = TextEditingController();
  final TextEditingController _serverUrlController = TextEditingController();
  final TextEditingController _rawCommandController = TextEditingController();

  // Device profile selection
  String _selectedProfile = 'esp32';
  final List<String> _availableProfiles = [
    'esp32',
    'arduino',
    'simple',
    'custom',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadConfiguration();
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    _serverUrlController.dispose();
    _rawCommandController.dispose();
    super.dispose();
  }

  void _loadConfiguration() {
    // Load default configuration
    _deviceIdController.text = 'T-A3B4-R01';
    _deviceNameController.text = 'Test Relay Device';
    _serverUrlController.text = 'http://localhost:3000';
    _selectedProfile = 'esp32';
  }

  Future<void> _initializeServices() async {
    try {
      _addLog('Initializing relay testing services...');

      // Simulate initialization
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isInitialized = true;
        _testStatus = 'Services initialized successfully (simulated)';
      });
      _addLog('✅ Services initialized successfully (simulated)');
    } catch (e) {
      setState(() => _testStatus = 'Initialization failed: $e');
      _addLog('❌ Initialization failed: $e');
    }
  }

  void _addLog(String message) {
    setState(() {
      _testLogs.add(
        '${DateTime.now().toLocal().toString().substring(11, 19)} - $message',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relay Testing & Configuration'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'Config'),
            Tab(icon: Icon(Icons.usb), text: 'USB-TTL'),
            Tab(icon: Icon(Icons.cloud), text: 'Server'),
            Tab(icon: Icon(Icons.bug_report), text: 'Testing'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Status Bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: _isInitialized
                ? Colors.green.shade100
                : Colors.orange.shade100,
            child: Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.warning,
                  color: _isInitialized ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _testStatus,
                    style: TextStyle(
                      color: _isInitialized
                          ? Colors.green.shade800
                          : Colors.orange.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (!_isInitialized)
                  IconButton(
                    onPressed: _initializeServices,
                    icon: const Icon(Icons.refresh),
                    color: Colors.orange.shade800,
                  ),
              ],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildConfigurationTab(),
                _buildUsbTtlTab(),
                _buildServerTab(),
                _buildTestingTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigurationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Device Configuration
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Device Configuration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _deviceIdController,
                    decoration: const InputDecoration(
                      labelText: 'Device ID',
                      hintText: 'T-A3B4-R01',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _deviceNameController,
                    decoration: const InputDecoration(
                      labelText: 'Device Name',
                      hintText: 'Main Door Relay',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                    value: _selectedProfile,
                    decoration: const InputDecoration(
                      labelText: 'Device Profile',
                      border: OutlineInputBorder(),
                    ),
                    items: _availableProfiles.map((profile) {
                      return DropdownMenuItem(
                        value: profile,
                        child: Text(profile.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedProfile = value!);
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Save Configuration
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _addLog('✅ Configuration saved (simulated)');
                setState(() => _testStatus = 'Configuration saved');
              },
              icon: const Icon(Icons.save),
              label: const Text('Save Configuration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsbTtlTab() {
    // Embed UsbTtlRelayTestingScreen content directly without AppBar
    return const UsbTtlRelayTestingScreen(showAppBar: false);
  }

  Widget _buildServerTab() {
    return Center(
      child: Text(
        'Server testing will be implemented when services are ready',
        style: Theme.of(context).textTheme.titleMedium,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTestingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Controls
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Comprehensive Testing',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _runFullTest,
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('Run Full Test'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _clearLogs,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear Logs'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Test Logs
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Logs',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 300,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _testLogs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _testLogs[index],
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Simple test methods for Testing tab
  Future<void> _runFullTest() async {
    _addLog('🚀 Starting comprehensive test suite...');
    await Future.delayed(const Duration(seconds: 1));
    _addLog('✅ Comprehensive test suite completed (simulated)');
    setState(() => _testStatus = 'All tests completed');
  }

  void _clearLogs() {
    setState(() {
      _testLogs.clear();
      _testStatus = 'Logs cleared';
    });
  }
}

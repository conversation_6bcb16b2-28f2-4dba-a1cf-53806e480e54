import 'package:flutter/foundation.dart';
import 'base_app_config.dart';
import '../../../apps/mobile/config/mobile_app_config.dart';
import '../../../apps/terminal/config/terminal_app_config.dart';

/// App Configuration Factory
/// 
/// This factory class provides a centralized way to create and manage
/// app-specific configurations. It automatically detects the app type
/// and returns the appropriate configuration instance.
class AppConfigFactory {
  static BaseAppConfig? _currentConfig;
  static AppType? _appType;

  /// Initialize configuration for specific app type
  static void initialize(AppType appType) {
    _appType = appType;
    _currentConfig = _createConfig(appType);
    
    if (kDebugMode) {
      print('🔧 AppConfig initialized for ${appType.name}');
      print('📊 Config validation: ${_currentConfig!.validateConfig()}');
    }
  }

  /// Get current app configuration
  static BaseAppConfig get current {
    if (_currentConfig == null) {
      throw StateError(
        'AppConfig not initialized. Call AppConfigFactory.initialize() first.',
      );
    }
    return _currentConfig!;
  }

  /// Get current app type
  static AppType get currentAppType {
    if (_appType == null) {
      throw StateError(
        'AppConfig not initialized. Call AppConfigFactory.initialize() first.',
      );
    }
    return _appType!;
  }

  /// Check if configuration is initialized
  static bool get isInitialized => _currentConfig != null;

  /// Reset configuration (useful for testing)
  static void reset() {
    _currentConfig = null;
    _appType = null;
    
    if (kDebugMode) {
      print('🔄 AppConfig reset');
    }
  }

  /// Create configuration instance based on app type
  static BaseAppConfig _createConfig(AppType appType) {
    switch (appType) {
      case AppType.mobile:
        return MobileAppConfig();
      case AppType.terminal:
        return TerminalAppConfig();
    }
  }

  /// Auto-detect app type based on platform and build configuration
  static AppType detectAppType() {
    // This method can be enhanced to detect app type automatically
    // based on build configuration, platform, or other factors
    
    // For now, we'll use a simple detection based on build configuration
    // In a real implementation, this could check:
    // - Build flavor
    // - Platform characteristics
    // - Environment variables
    // - Package name patterns
    
    if (kDebugMode) {
      // In debug mode, we can check for specific indicators
      // This is a placeholder - implement actual detection logic
      return AppType.mobile; // Default to mobile for development
    }
    
    // In production, use more sophisticated detection
    return AppType.mobile; // Default fallback
  }

  /// Initialize with auto-detection
  static void initializeWithAutoDetection() {
    final detectedType = detectAppType();
    initialize(detectedType);
    
    if (kDebugMode) {
      print('🔍 Auto-detected app type: ${detectedType.name}');
    }
  }

  /// Get configuration for specific app type without setting as current
  static BaseAppConfig getConfigForType(AppType appType) {
    return _createConfig(appType);
  }

  /// Validate current configuration
  static bool validateCurrentConfig() {
    if (!isInitialized) {
      return false;
    }
    return _currentConfig!.validateConfig();
  }

  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigSummary() {
    if (!isInitialized) {
      return {'error': 'Configuration not initialized'};
    }

    return {
      'appType': _appType!.name,
      'appName': _currentConfig!.appName,
      'appVersion': _currentConfig!.appVersion,
      'environment': _currentConfig!.environment.name,
      'isValid': _currentConfig!.validateConfig(),
      'config': _currentConfig!.toMap(),
    };
  }

  /// Switch to different app type (useful for testing)
  static void switchToAppType(AppType newAppType) {
    if (kDebugMode) {
      print('🔄 Switching from ${_appType?.name ?? 'none'} to ${newAppType.name}');
    }
    
    initialize(newAppType);
  }

  /// Compare configurations between app types
  static Map<String, dynamic> compareConfigurations() {
    final mobileConfig = getConfigForType(AppType.mobile);
    final terminalConfig = getConfigForType(AppType.terminal);

    return {
      'mobile': mobileConfig.toMap(),
      'terminal': terminalConfig.toMap(),
      'differences': _findConfigDifferences(
        mobileConfig.toMap(),
        terminalConfig.toMap(),
      ),
    };
  }

  /// Find differences between two configuration maps
  static Map<String, dynamic> _findConfigDifferences(
    Map<String, dynamic> config1,
    Map<String, dynamic> config2,
  ) {
    final differences = <String, dynamic>{};

    // Check all keys from both configs
    final allKeys = {...config1.keys, ...config2.keys};

    for (final key in allKeys) {
      final value1 = config1[key];
      final value2 = config2[key];

      if (value1 != value2) {
        differences[key] = {
          'mobile': value1,
          'terminal': value2,
        };
      }
    }

    return differences;
  }

  /// Export configuration to JSON (for backup/restore)
  static Map<String, dynamic> exportConfig() {
    if (!isInitialized) {
      throw StateError('Configuration not initialized');
    }

    return {
      'appType': _appType!.name,
      'timestamp': DateTime.now().toIso8601String(),
      'config': _currentConfig!.toMap(),
    };
  }

  /// Import configuration from JSON (for backup/restore)
  static bool importConfig(Map<String, dynamic> configData) {
    try {
      final appTypeName = configData['appType'] as String?;
      if (appTypeName == null) {
        return false;
      }

      final appType = AppType.values.firstWhere(
        (type) => type.name == appTypeName,
        orElse: () => AppType.mobile,
      );

      initialize(appType);
      
      if (kDebugMode) {
        print('📥 Configuration imported for ${appType.name}');
      }
      
      return validateCurrentConfig();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to import configuration: $e');
      }
      return false;
    }
  }
}

/// Configuration utilities
class AppConfigUtils {
  /// Get environment-specific configuration
  static Map<String, dynamic> getEnvironmentConfig(AppEnvironment environment) {
    return {
      'development': {
        'enableDebugLogging': true,
        'enableApiLogging': true,
        'enableErrorReporting': false,
        'enableAnalytics': false,
      },
      'staging': {
        'enableDebugLogging': true,
        'enableApiLogging': true,
        'enableErrorReporting': true,
        'enableAnalytics': false,
      },
      'production': {
        'enableDebugLogging': false,
        'enableApiLogging': false,
        'enableErrorReporting': true,
        'enableAnalytics': true,
      },
    }[environment.name] ?? {};
  }

  /// Get app type specific defaults
  static Map<String, dynamic> getAppTypeDefaults(AppType appType) {
    switch (appType) {
      case AppType.mobile:
        return {
          'enableHapticFeedback': true,
          'enableOrientationChanges': true,
          'enableBackgroundRefresh': true,
          'maxConcurrentRequests': 3,
        };
      case AppType.terminal:
        return {
          'enableKioskMode': true,
          'enableHardwareMonitoring': true,
          'enableAutoRestart': true,
          'forceFullscreen': true,
        };
    }
  }

  /// Validate configuration compatibility
  static bool validateConfigCompatibility(
    BaseAppConfig config,
    AppType expectedType,
  ) {
    return config.appType == expectedType && config.validateConfig();
  }
}

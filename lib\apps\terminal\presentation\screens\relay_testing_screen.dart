import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/relay_control_provider.dart';
import '../../providers/device_registration_provider.dart';
// TODO: Add imports when services are ready
// import '../../../../shared/services/relay_api_service.dart' as api;
// import '../../../../shared/services/relay_management_service.dart';
// import '../../../../shared/services/http_client_service.dart';
// import '../../../../shared/core/config/configuration_manager.dart';
// import '../../../../shared/core/config/relay_config_parameters.dart';
import '../../../../packages/relay_controller/lib/relay_controller.dart';

/// Comprehensive Relay Testing Screen
/// 
/// This screen provides testing interface for:
/// - USB-TTL relay communication
/// - Server integration testing
/// - Device profile configuration
/// - Face recognition side effects
class RelayTestingScreen extends StatefulWidget {
  const RelayTestingScreen({super.key});

  @override
  State<RelayTestingScreen> createState() => _RelayTestingScreenState();
}

class _RelayTestingScreenState extends State<RelayTestingScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  // Test state
  bool _isInitialized = false;
  String _testStatus = 'Ready for testing';
  List<String> _testLogs = [];

  // Configuration controllers
  final TextEditingController _deviceIdController = TextEditingController();
  final TextEditingController _deviceNameController = TextEditingController();
  final TextEditingController _serverUrlController = TextEditingController();
  final TextEditingController _rawCommandController = TextEditingController();

  // Device profile selection
  String _selectedProfile = 'esp32';
  final List<String> _availableProfiles = ['esp32', 'arduino', 'simple', 'custom'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadConfiguration();
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    _serverUrlController.dispose();
    _rawCommandController.dispose();
    super.dispose();
  }

  void _loadConfiguration() {
    // Load default configuration
    _deviceIdController.text = 'T-A3B4-R01';
    _deviceNameController.text = 'Test Relay Device';
    _serverUrlController.text = 'http://localhost:3000';
    _selectedProfile = 'esp32';
  }

  Future<void> _initializeServices() async {
    try {
      _addLog('Initializing relay testing services...');

      // Simulate initialization
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isInitialized = true;
        _testStatus = 'Services initialized successfully (simulated)';
      });
      // _addLog('✅ Services initialized successfully (simulated)');

    } catch (e) {
      setState(() => _testStatus = 'Initialization failed: $e');
      _addLog('❌ Initialization failed: $e');
    }
  }

  void _addLog(String message) {
    setState(() {
      _testLogs.add('${DateTime.now().toLocal().toString().substring(11, 19)} - $message');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relay Testing & Configuration'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'Config'),
            Tab(icon: Icon(Icons.usb), text: 'USB-TTL'),
            Tab(icon: Icon(Icons.cloud), text: 'Server'),
            Tab(icon: Icon(Icons.bug_report), text: 'Testing'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Status Bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: _isInitialized ? Colors.green.shade100 : Colors.orange.shade100,
            child: Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.warning,
                  color: _isInitialized ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _testStatus,
                    style: TextStyle(
                      color: _isInitialized ? Colors.green.shade800 : Colors.orange.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (!_isInitialized)
                  IconButton(
                    onPressed: _initializeServices,
                    icon: const Icon(Icons.refresh),
                    color: Colors.orange.shade800,
                  ),
              ],
            ),
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildConfigurationTab(),
                _buildUsbTtlTab(),
                _buildServerTab(),
                _buildTestingTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigurationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Device Configuration
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Device Configuration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _deviceIdController,
                    decoration: const InputDecoration(
                      labelText: 'Device ID',
                      hintText: 'T-A3B4-R01',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _deviceNameController,
                    decoration: const InputDecoration(
                      labelText: 'Device Name',
                      hintText: 'Main Door Relay',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                    value: _selectedProfile,
                    decoration: const InputDecoration(
                      labelText: 'Device Profile',
                      border: OutlineInputBorder(),
                    ),
                    items: _availableProfiles.map((profile) {
                      return DropdownMenuItem(
                        value: profile,
                        child: Text(profile.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedProfile = value!);
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Device Profile Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Device Profile Commands',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 12),
                  _buildProfileCommandsInfo(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Save Configuration
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
              label: const Text('Save Configuration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCommandsInfo() {
    Map<String, List<String>> profileCommands = {
      'esp32': ['R0:1 (ON)', 'R0:0 (OFF)', 'R1:TOGGLE', 'R2:500 (Timed)', 'ALL:1', 'ALL:0'],
      'arduino': ['REL_0_ON', 'REL_0_OFF', 'REL_ALL_ON', 'REL_ALL_OFF'],
      'simple': ['01 (ON)', '00 (OFF)', '1T (Toggle)', '1P500 (Timed)', 'A1', 'A0'],
      'custom': ['User-defined templates', 'Configurable terminators'],
    };
    
    final commands = profileCommands[_selectedProfile] ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: commands.map((command) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          children: [
            const Icon(Icons.arrow_right, size: 16, color: Colors.grey),
            const SizedBox(width: 8),
            Text(command, style: const TextStyle(fontFamily: 'monospace')),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildUsbTtlTab() {
    return Consumer<RelayControlProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // USB Connection Status
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            provider.isConnected ? Icons.usb : Icons.usb_off,
                            color: provider.isConnected ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'USB-TTL Connection',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Spacer(),
                          Text(
                            provider.isConnected ? 'Connected' : 'Disconnected',
                            style: TextStyle(
                              color: provider.isConnected ? Colors.green : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      if (provider.lastError != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Error: ${provider.lastError}',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Connection Controls
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Connection Controls',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _testUsbConnection,
                              icon: const Icon(Icons.link),
                              label: const Text('Test Connection'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _reconnectUsb,
                              icon: const Icon(Icons.refresh),
                              label: const Text('Reconnect'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Raw Command Testing
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Raw Command Testing',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _rawCommandController,
                              decoration: const InputDecoration(
                                labelText: 'Raw Command',
                                hintText: 'R0:1',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: _sendRawCommand,
                            icon: const Icon(Icons.send),
                            label: const Text('Send'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildServerTab() {
    return Consumer<DeviceRegistrationProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Server Configuration
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Server Configuration',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _serverUrlController,
                        decoration: const InputDecoration(
                          labelText: 'Server URL',
                          hintText: 'http://localhost:3000',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _testServerConnection,
                              icon: const Icon(Icons.cloud),
                              label: const Text('Test Connection'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _registerWithServer,
                              icon: const Icon(Icons.app_registration),
                              label: const Text('Register Device'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Server Testing
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Server API Testing',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _testFaceRecognition,
                            icon: const Icon(Icons.face),
                            label: const Text('Test Face Recognition'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _testSecureMessage,
                            icon: const Icon(Icons.security),
                            label: const Text('Test Secure Message'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _testRelayControl,
                            icon: const Icon(Icons.power),
                            label: const Text('Test Relay Control'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Controls
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Comprehensive Testing',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _runFullTest,
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('Run Full Test'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _clearLogs,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear Logs'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Test Logs
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Logs',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 300,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _testLogs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _testLogs[index],
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Test Methods
  Future<void> _saveConfiguration() async {
    try {
      final config = ConfigurationManager.instance;
      await config.setValue(RelayConfigKeys.relayDeviceId, _deviceIdController.text);
      await config.setValue(RelayConfigKeys.relayDeviceName, _deviceNameController.text);
      await config.setValue(RelayConfigKeys.relayDeviceProfile, _selectedProfile);
      
      _addLog('✅ Configuration saved successfully');
      setState(() => _testStatus = 'Configuration saved');
    } catch (e) {
      _addLog('❌ Failed to save configuration: $e');
    }
  }

  Future<void> _testUsbConnection() async {
    _addLog('Testing USB-TTL connection...');

    try {
      // Initialize relay management service with current config
      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceIdController.text,
        deviceName: _deviceNameController.text,
        relayCount: 4,
        baudRate: 115200,
      );

      await _managementService.initialize(
        config: deviceConfig,
        autoConnect: true,
      );

      // Test connection
      final isConnected = _managementService.isConnected;
      if (isConnected) {
        _addLog('✅ USB-TTL connection successful');
        setState(() => _testStatus = 'USB-TTL connected');
      } else {
        _addLog('❌ USB-TTL connection failed');
        setState(() => _testStatus = 'USB-TTL connection failed');
      }

    } catch (e) {
      _addLog('❌ USB-TTL connection error: $e');
      setState(() => _testStatus = 'USB-TTL error: $e');
    }
  }

  Future<void> _reconnectUsb() async {
    _addLog('Reconnecting USB-TTL...');

    try {
      await _managementService.disconnect();
      await Future.delayed(const Duration(seconds: 1));

      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceIdController.text,
        deviceName: _deviceNameController.text,
        relayCount: 4,
        baudRate: 115200,
      );

      await _managementService.initialize(
        config: deviceConfig,
        autoConnect: true,
      );

      _addLog('✅ USB-TTL reconnection completed');
      setState(() => _testStatus = 'USB-TTL reconnected');

    } catch (e) {
      _addLog('❌ USB-TTL reconnection failed: $e');
      setState(() => _testStatus = 'USB-TTL reconnection failed');
    }
  }

  Future<void> _sendRawCommand() async {
    final command = _rawCommandController.text.trim();
    if (command.isEmpty) {
      _addLog('❌ No command entered');
      return;
    }

    _addLog('Sending raw command: $command');

    try {
      // Send raw command directly to relay management service
      await _managementService.sendRawCommand(command);

      _addLog('✅ Raw command sent successfully: $command');
      setState(() => _testStatus = 'Command sent: $command');

    } catch (e) {
      _addLog('❌ Raw command error: $e');
      setState(() => _testStatus = 'Command error: $e');
    }
  }

  Future<void> _testServerConnection() async {
    _addLog('Testing server connection...');

    try {
      // Update HTTP client with new server URL
      final httpClient = HttpClientService();
      httpClient.initialize(HttpClientConfig(
        baseUrl: _serverUrlController.text,
        connectTimeout: 10000,
        receiveTimeout: 10000,
        sendTimeout: 10000,
      ));

      await _apiService.initialize(httpClient);

      // Test basic connectivity by trying to get devices list
      final devices = await _apiService.getDevices();

      _addLog('✅ Server connection successful');
      _addLog('Found ${devices.devices.length} registered devices');
      setState(() => _testStatus = 'Server connected successfully');

    } catch (e) {
      _addLog('❌ Server connection failed: $e');
      setState(() => _testStatus = 'Server connection failed: $e');
    }
  }

  Future<void> _registerWithServer() async {
    _addLog('Registering device with server...');

    try {
      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceIdController.text,
        deviceName: _deviceNameController.text,
        relayCount: 4,
        baudRate: 115200,
      );

      // Try secure API first
      try {
        final response = await _apiService.registerDevice(
          deviceConfig: deviceConfig,
          additionalInfo: {
            'test_mode': true,
            'profile': _selectedProfile,
          },
          useSecureApi: true,
        );

        _addLog('✅ Device registered with secure API');
        _addLog('Device ID: ${response.deviceId}');
        _addLog('Registration time: ${response.registeredAt}');
        setState(() => _testStatus = 'Device registered (secure)');

      } catch (secureError) {
        _addLog('⚠️ Secure API failed, trying legacy API...');

        // Fallback to legacy API
        final response = await _apiService.registerDevice(
          deviceConfig: deviceConfig,
          useSecureApi: false,
        );

        _addLog('✅ Device registered with legacy API');
        _addLog('Device ID: ${response.deviceId}');
        setState(() => _testStatus = 'Device registered (legacy)');
      }

    } catch (e) {
      _addLog('❌ Device registration failed: $e');
      setState(() => _testStatus = 'Registration failed: $e');
    }
  }

  Future<void> _testFaceRecognition() async {
    _addLog('Testing face recognition...');

    try {
      // Sample base64 image data (1x1 pixel PNG for testing)
      const sampleImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

      final response = await _apiService.recognizeFace(
        deviceId: _deviceIdController.text,
        imageData: sampleImageData,
        confidenceScore: 0.85,
        metadata: {
          'test_mode': true,
          'source': 'relay_testing_screen',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _addLog('✅ Face recognition request successful');
      _addLog('Recognized: ${response.recognized}');
      _addLog('Recognition ID: ${response.recognitionId}');

      if (response.user != null) {
        _addLog('User: ${response.user!['name'] ?? 'Unknown'}');
      }

      if (response.allowAccess != null) {
        _addLog('Access: ${response.allowAccess! ? 'GRANTED' : 'DENIED'}');
      }

      setState(() => _testStatus = 'Face recognition completed');

    } catch (e) {
      _addLog('❌ Face recognition failed: $e');
      setState(() => _testStatus = 'Face recognition failed: $e');
    }
  }

  Future<void> _testSecureMessage() async {
    _addLog('Testing secure message...');

    try {
      final response = await _apiService.sendSecureMessage(
        deviceId: _deviceIdController.text,
        messageType: 'test_message',
        payload: {
          'message': 'Hello from relay testing screen',
          'test_mode': true,
          'timestamp': DateTime.now().toIso8601String(),
          'device_profile': _selectedProfile,
        },
      );

      _addLog('✅ Secure message sent successfully');
      _addLog('Message ID: ${response.messageId ?? 'N/A'}');
      _addLog('Response data: ${response.data}');
      setState(() => _testStatus = 'Secure message sent');

    } catch (e) {
      _addLog('❌ Secure message failed: $e');
      setState(() => _testStatus = 'Secure message failed: $e');
    }
  }

  Future<void> _testRelayControl() async {
    _addLog('Testing relay control via server...');

    try {
      // Test relay ON command
      final onCommand = api.RelayCommand.raw('R0:1');
      final onResponse = await _apiService.sendRelayCommand(
        deviceId: _deviceIdController.text,
        command: onCommand,
      );

      _addLog('✅ Relay ON command sent');
      _addLog('Command ID: ${onResponse.commandId}');
      _addLog('Status: ${onResponse.status}');

      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));

      // Test relay OFF command
      final offCommand = api.RelayCommand.raw('R0:0');
      final offResponse = await _apiService.sendRelayCommand(
        deviceId: _deviceIdController.text,
        command: offCommand,
      );

      _addLog('✅ Relay OFF command sent');
      _addLog('Command ID: ${offResponse.commandId}');
      _addLog('Status: ${offResponse.status}');

      setState(() => _testStatus = 'Relay control test completed');

    } catch (e) {
      _addLog('❌ Relay control test failed: $e');
      setState(() => _testStatus = 'Relay control failed: $e');
    }
  }

  Future<void> _runFullTest() async {
    _addLog('🚀 Starting comprehensive test suite...');
    
    await _testServerConnection();
    await _registerWithServer();
    await _testUsbConnection();
    await _testRelayControl();
    await _testFaceRecognition();
    await _testSecureMessage();
    
    _addLog('✅ Comprehensive test suite completed');
    setState(() => _testStatus = 'All tests completed');
  }

  void _clearLogs() {
    setState(() {
      _testLogs.clear();
      _testStatus = 'Logs cleared';
    });
  }
}

import 'package:flutter_test/flutter_test.dart';

import '../lib/packages/face_recognition/src/config/face_detection_config.dart';
// import '../lib/packages/face_recognition/src/providers/unified_face_detection_provider.dart';
// import '../lib/apps/terminal/providers/face_detection_provider.dart';
import '../lib/apps/terminal/config/face_detection_terminal_config.dart';

void main() {
  group('MediaPipe Engine Tests', () {
    
    test('FaceDetectionConfig creation', () {
      // Test basic config creation
      final config = FaceDetectionConfig.forTerminal(
        useMediaPipe: true,
        enableLandmarks: true,
        confidenceThreshold: 0.6,
        maxFaces: 3,
      );
      
      expect(config.primaryEngine, FaceDetectionEngineType.mediaPipe);
      expect(config.enableLandmarks, true);
      expect(config.confidenceThreshold, 0.6);
      expect(config.maxFaces, 3);
      expect(config.enableFallback, true);
    });
    
    test('Terminal config validation', () {
      final config = FaceDetectionTerminalConfig.getTerminalConfig(
        useMediaPipe: true,
        enableLandmarks: true,
        confidenceThreshold: 0.6,
        maxFaces: 3,
      );
      
      final isValid = FaceDetectionTerminalConfig.validateTerminalConfig(config);
      expect(isValid, true);
      
      // Test invalid config
      final invalidConfig = config.copyWith(
        confidenceThreshold: 1.5, // Invalid threshold
        maxFaces: 20, // Too many faces
      );
      
      final isInvalid = FaceDetectionTerminalConfig.validateTerminalConfig(invalidConfig);
      expect(isInvalid, false);
    });
    
    test('Engine type extensions', () {
      expect(FaceDetectionEngineType.mediaPipe.displayName, 'MediaPipe BlazeFace');
      expect(FaceDetectionEngineType.mediaPipe.supportsLandmarks, true);
      expect(FaceDetectionEngineType.mediaPipe.supportsTracking, false);
      
      expect(FaceDetectionEngineType.mlKit.displayName, 'Google ML Kit');
      expect(FaceDetectionEngineType.mlKit.supportsLandmarks, true);
      expect(FaceDetectionEngineType.mlKit.supportsTracking, true);
      
      expect(FaceDetectionEngineType.ultraFace.displayName, 'UltraFace TensorFlow');
      expect(FaceDetectionEngineType.ultraFace.supportsLandmarks, false);
      expect(FaceDetectionEngineType.ultraFace.supportsTracking, false);
    });
    
    test('Performance mode extensions', () {
      expect(PerformanceMode.maxAccuracy.confidenceAdjustment, 0.1);
      expect(PerformanceMode.balanced.confidenceAdjustment, 0.0);
      expect(PerformanceMode.maxPerformance.confidenceAdjustment, -0.1);
      expect(PerformanceMode.powerSaver.confidenceAdjustment, -0.05);
    });
    
    test('Hardware type recommendations', () {
      expect(TerminalHardwareType.telpoF8.supportsGPU, true);
      expect(TerminalHardwareType.telpoF8.recommendedEngine, FaceDetectionEngineType.mediaPipe);
      
      expect(TerminalHardwareType.embedded.supportsGPU, false);
      expect(TerminalHardwareType.embedded.recommendedEngine, FaceDetectionEngineType.ultraFace);
    });
    
    test('Use case recommendations', () {
      expect(TerminalUseCase.attendance.recommendedConfidence, 0.6);
      expect(TerminalUseCase.attendance.recommendedMaxFaces, 3);
      
      expect(TerminalUseCase.accessControl.recommendedConfidence, 0.8);
      expect(TerminalUseCase.accessControl.recommendedMaxFaces, 1);
      
      expect(TerminalUseCase.enrollment.recommendedConfidence, 0.9);
      expect(TerminalUseCase.enrollment.recommendedMaxFaces, 1);
    });
    
    test('Config for different hardware types', () {
      final telpoConfig = FaceDetectionTerminalConfig.getConfigForHardware(
        hardwareType: TerminalHardwareType.telpoF8,
        useMediaPipe: true,
      );
      expect(telpoConfig.primaryEngine, FaceDetectionEngineType.mediaPipe);
      expect(telpoConfig.enableGPUAcceleration, true);
      
      final embeddedConfig = FaceDetectionTerminalConfig.getConfigForHardware(
        hardwareType: TerminalHardwareType.embedded,
      );
      expect(embeddedConfig.primaryEngine, FaceDetectionEngineType.ultraFace);
      expect(embeddedConfig.enableGPUAcceleration, false);
    });
    
    test('Config for different use cases', () {
      final attendanceConfig = FaceDetectionTerminalConfig.getConfigForUseCase(
        useCase: TerminalUseCase.attendance,
        useMediaPipe: true,
      );
      expect(attendanceConfig.primaryEngine, FaceDetectionEngineType.mediaPipe);
      expect(attendanceConfig.confidenceThreshold, 0.6);
      expect(attendanceConfig.maxFaces, 3);
      
      final enrollmentConfig = FaceDetectionTerminalConfig.getConfigForUseCase(
        useCase: TerminalUseCase.enrollment,
      );
      expect(enrollmentConfig.primaryEngine, FaceDetectionEngineType.hybrid);
      expect(enrollmentConfig.confidenceThreshold, 0.9);
      expect(enrollmentConfig.maxFaces, 1);
    });
    
    test('Config debug info', () {
      final config = FaceDetectionTerminalConfig.getTerminalConfig(
        useMediaPipe: true,
        enableLandmarks: true,
      );
      
      final debugInfo = FaceDetectionTerminalConfig.getConfigDebugInfo(config);
      
      expect(debugInfo['primaryEngine'], 'MediaPipe BlazeFace');
      expect(debugInfo['enableLandmarks'], true);
      expect(debugInfo['isValidForTerminal'], true);
      expect(debugInfo['recommendedForTerminal'], true);
    });
    
    test('Config copyWith functionality', () {
      final originalConfig = FaceDetectionConfig.forTerminal();
      
      final modifiedConfig = originalConfig.copyWith(
        confidenceThreshold: 0.8,
        maxFaces: 5,
        enableLandmarks: false,
      );
      
      expect(modifiedConfig.confidenceThreshold, 0.8);
      expect(modifiedConfig.maxFaces, 5);
      expect(modifiedConfig.enableLandmarks, false);
      
      // Original should be unchanged
      expect(originalConfig.confidenceThreshold, 0.6);
      expect(originalConfig.maxFaces, 3);
      expect(originalConfig.enableLandmarks, true);
    });
    
    test('Recommended config for platform', () {
      final defaultConfig = FaceDetectionConfig.getRecommended();
      expect(defaultConfig, isNotNull);
      
      final mediaPipeConfig = FaceDetectionConfig.getRecommended(preferMediaPipe: true);
      expect(mediaPipeConfig.primaryEngine, FaceDetectionEngineType.mediaPipe);
      
      final mlKitConfig = FaceDetectionConfig.getRecommended(preferMediaPipe: false);
      expect(mlKitConfig.primaryEngine, FaceDetectionEngineType.mlKit);
    });
  });
  
  // group('TerminalFaceDetectionProvider Tests', () {
  //   // Temporarily disabled due to TensorFlow Lite dependency issues
  //   // These tests will be enabled once TensorFlow Lite compatibility is resolved
  // });
}

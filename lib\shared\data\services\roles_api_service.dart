import 'package:get_it/get_it.dart';
import '../data_sources/remote/member_role_remote_data_source.dart';
import '../models/role/member_role_model.dart';

/// Service for managing member roles API calls
class RolesApiService {
  final MemberRoleRemoteDataSource _remoteDataSource = GetIt.instance<MemberRoleRemoteDataSource>();

  /// Cache for roles to avoid repeated API calls
  List<MemberRoleUIModel>? _cachedRoles;
  DateTime? _lastCacheTime;
  static const Duration _cacheTimeout = Duration(minutes: 5);

  /// Get all member roles with caching
  Future<List<MemberRoleUIModel>> getRoles({
    bool forceRefresh = false,
    String? tenantId,
  }) async {
    // Check cache validity
    if (!forceRefresh && 
        _cachedRoles != null && 
        _lastCacheTime != null &&
        DateTime.now().difference(_lastCacheTime!) < _cacheTimeout) {
      return _cachedRoles!;
    }

    try {
      final roleModels = await _remoteDataSource.getMemberRoles(
        page: 1,
        limit: 100, // Get all roles in one call
        tenantId: tenantId,
        isActive: true, // Only get active roles
      );

      final roles = roleModels
          .map((model) => MemberRoleUIModel.fromMemberRoleModel(model))
          .toList();
      
      // Update cache
      _cachedRoles = roles;
      _lastCacheTime = DateTime.now();
      
      return roles;
    } catch (e) {
      throw Exception('Failed to load roles: $e');
    }
  }

  /// Get roles by tenant ID
  Future<List<MemberRoleUIModel>> getRolesByTenant(String tenantId) async {
    try {
      final roleModels = await _remoteDataSource.getMemberRolesByTenant(tenantId);
      
      return roleModels
          .where((model) => model.isActive) // Filter active roles
          .map((model) => MemberRoleUIModel.fromMemberRoleModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to load roles by tenant: $e');
    }
  }

  /// Get role by ID
  Future<MemberRoleUIModel?> getRoleById(String roleId) async {
    try {
      final roleModel = await _remoteDataSource.getMemberRoleById(roleId);
      return MemberRoleUIModel.fromMemberRoleModel(roleModel);
    } catch (e) {
      return null; // Return null if role not found
    }
  }

  /// Clear cache
  void clearCache() {
    _cachedRoles = null;
    _lastCacheTime = null;
  }

  /// Get roles for dropdown/filter (returns simplified list)
  Future<List<RoleOption>> getRolesForFilter({String? tenantId}) async {
    try {
      final roles = await getRoles(tenantId: tenantId);
      
      // Convert to simple options for UI
      final options = roles.map((role) => RoleOption(
        id: role.id,
        name: role.name,
        description: role.description,
      )).toList();

      // Sort by name
      options.sort((a, b) => a.name.compareTo(b.name));
      
      return options;
    } catch (e) {
      // Return empty list on error
      return [];
    }
  }

  /// Create new role
  Future<MemberRoleUIModel> createRole({
    required String name,
    String? description,
    String? tenantId,
  }) async {
    try {
      final roleModel = await _remoteDataSource.createMemberRole(
        name: name,
        description: description,
        tenantId: tenantId,
      );
      
      // Clear cache to force refresh
      clearCache();
      
      return MemberRoleUIModel.fromMemberRoleModel(roleModel);
    } catch (e) {
      throw Exception('Failed to create role: $e');
    }
  }

  /// Update role
  Future<MemberRoleUIModel> updateRole({
    required String roleId,
    String? name,
    String? description,
    bool? isActive,
  }) async {
    try {
      final roleModel = await _remoteDataSource.updateMemberRole(
        roleId: roleId,
        name: name,
        description: description,
        isActive: isActive,
      );
      
      // Clear cache to force refresh
      clearCache();
      
      return MemberRoleUIModel.fromMemberRoleModel(roleModel);
    } catch (e) {
      throw Exception('Failed to update role: $e');
    }
  }

  /// Delete role
  Future<void> deleteRole(String roleId) async {
    try {
      await _remoteDataSource.deleteMemberRole(roleId);
      
      // Clear cache to force refresh
      clearCache();
    } catch (e) {
      throw Exception('Failed to delete role: $e');
    }
  }

  /// Get default roles (fallback when API fails)
  List<RoleOption> getDefaultRoles() {
    return [
      const RoleOption(id: 'employee', name: 'Nhân viên'),
      const RoleOption(id: 'manager', name: 'Quản lý'),
      const RoleOption(id: 'admin', name: 'Quản trị viên'),
    ];
  }
}

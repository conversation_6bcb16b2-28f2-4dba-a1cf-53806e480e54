# CTextField - Custom Text Field Component

Component input field tùy chỉnh với nhiều layout và tính năng cho Flutter mobile app.

## 🎯 Tổng quan

`CTextField` là component input field được thiết kế để thay thế tất cả các input components khác trong ứng dụng, cung cấp:

- **Multiple Layouts**: Vertical (default) và Horizontal (side by side)
- **Prefix/Suffix Icons**: Hỗ trợ icon prefix và suffix
- **Password Field**: Toggle visibility cho password
- **Search Field**: Với search icon built-in
- **Validation**: Form validation tích hợp
- **Consistent Styling**: Styling nhất quán across toàn app

## 🏗️ Layouts

### 1. Vertical Layout (Default)
Label ở trên, input ở dưới - layout chuẩn cho form.

```dart
CTextField(
  controller: _nameController,
  label: '<PERSON>ọ và tên',
  hintText: 'Nhậ<PERSON> họ và tên của bạn',
  isRequired: true,
)
```

### 2. Horizontal Layout (Side by Side)
Label bên trái, input bên phải - phù hợp cho detail screens.

```dart
CTextField(
  controller: _phoneController,
  label: 'Số điện thoại',
  hintText: 'Nhập số điện thoại',
  layout: CTextFieldLayout.horizontal,
  labelFlex: 2,
  inputFlex: 3,
)
```

## 🎨 Extension Methods (Khuyến nghị)

### Search Field
```dart
CTextFieldExtensions.search(
  controller: _searchController,
  label: 'Tìm kiếm',
  hintText: 'Nhập từ khóa...',
  onChanged: (value) => handleSearch(value),
  onSubmitted: (value) => submitSearch(value),
)
```

### Password Field
```dart
CTextFieldExtensions.password(
  controller: _passwordController,
  label: 'Mật khẩu',
  hintText: 'Nhập mật khẩu',
  obscureText: _obscurePassword,
  onToggleObscure: () => setState(() {
    _obscurePassword = !_obscurePassword;
  }),
)
```

### Horizontal Field (Justify Between - Sát 2 đầu)
```dart
CTextFieldExtensions.horizontal(
  controller: _addressController,
  label: 'Địa chỉ',
  hintText: 'Nhập địa chỉ',
  showBorder: true,
  enabled: true,
  readOnly: false,
)
```

**Features:**
- Label sát bên trái, input sát bên phải
- Input tự động scroll khi text dài
- Không bị che khi nhập text vượt quá chiều rộng
- Hoạt động như ô input bình thường

## 📋 Properties

### Core Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `controller` | `TextEditingController` | required | Controller cho text field |
| `label` | `String` | required | Label text |
| `hintText` | `String` | required | Placeholder text |
| `isRequired` | `bool` | `false` | Hiển thị dấu * cho required field |
| `validator` | `String? Function(String?)?` | `null` | Validation function |

### Layout Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `layout` | `CTextFieldLayout` | `vertical` | Layout type |
| `labelFlex` | `int?` | `2` | Flex cho label (horizontal layout) |
| `inputFlex` | `int?` | `3` | Flex cho input (horizontal layout) |

### Input Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `keyboardType` | `TextInputType?` | `null` | Keyboard type |
| `textInputAction` | `TextInputAction?` | `null` | Input action |
| `textAlign` | `TextAlign` | `start` | Text alignment |
| `readOnly` | `bool` | `false` | Read-only mode |
| `enabled` | `bool` | `true` | Enable/disable input |

### Password Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `isPassword` | `bool` | `false` | Password field mode |
| `obscureText` | `bool` | `false` | Hide/show password |
| `onToggleObscure` | `VoidCallback?` | `null` | Toggle password visibility |

### Icon Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `prefixIcon` | `Widget?` | `null` | Icon ở đầu input |
| `suffixIcon` | `Widget?` | `null` | Icon ở cuối input |

### Styling Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `height` | `double?` | `38` | Chiều cao input |
| `contentPadding` | `EdgeInsets?` | auto | Padding bên trong |
| `textStyle` | `TextStyle?` | `AppTextStyles.inputText` | Text style |
| `hintStyle` | `TextStyle?` | `AppTextStyles.inputPlaceholder` | Hint style |
| `labelStyle` | `TextStyle?` | auto | Label style |

### Callback Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `onChanged` | `ValueChanged<String>?` | `null` | Text change callback |
| `onSubmitted` | `ValueChanged<String>?` | `null` | Submit callback |
| `onTap` | `VoidCallback?` | `null` | Tap callback |

## 🎯 Use Cases

### 1. Basic Form Fields
```dart
// Vertical layout cho form thông thường
CTextField(
  controller: _emailController,
  label: 'Email',
  hintText: 'Nhập email của bạn',
  isRequired: true,
  keyboardType: TextInputType.emailAddress,
  validator: (value) => validateEmail(value),
)
```

### 2. Detail Screen Fields
```dart
// Horizontal layout cho detail screens
Container(
  decoration: BoxDecoration(
    border: Border.all(color: AppColors.border),
    borderRadius: BorderRadius.circular(8),
  ),
  child: Column(
    children: [
      CTextFieldExtensions.horizontal(
        controller: _nameController,
        label: 'Họ và tên',
        hintText: 'Nhập họ và tên',
        isRequired: true,
      ),
      Divider(height: 1),
      CTextFieldExtensions.horizontal(
        controller: _phoneController,
        label: 'Số điện thoại',
        hintText: 'Nhập số điện thoại',
        textAlign: TextAlign.right,
      ),
    ],
  ),
)
```

### 3. Search Interface
```dart
// Search field với icon
CTextFieldExtensions.search(
  controller: _searchController,
  label: 'Tìm kiếm',
  hintText: 'Tìm kiếm người dùng...',
  onChanged: (query) => _performSearch(query),
  onSubmitted: (query) => _submitSearch(query),
)
```

### 4. Login Form
```dart
Column(
  children: [
    CTextField(
      controller: _usernameController,
      label: 'Tên đăng nhập',
      hintText: 'Nhập tên đăng nhập',
      isRequired: true,
    ),
    SizedBox(height: 16),
    CTextFieldExtensions.password(
      controller: _passwordController,
      label: 'Mật khẩu',
      hintText: 'Nhập mật khẩu',
      obscureText: _obscurePassword,
      onToggleObscure: () => setState(() {
        _obscurePassword = !_obscurePassword;
      }),
    ),
  ],
)
```

## 🎨 Custom Styling

### Custom Colors và Fonts
```dart
CTextField(
  controller: _controller,
  label: 'Custom Field',
  hintText: 'Custom styling',
  textStyle: AppTextStyles.bodyLarge.copyWith(
    color: AppColors.primary,
    fontWeight: FontWeight.w600,
  ),
  hintStyle: AppTextStyles.bodyMedium.copyWith(
    color: AppColors.textTertiary,
    fontStyle: FontStyle.italic,
  ),
  labelStyle: AppTextStyles.caption.copyWith(
    color: AppColors.primary,
    fontWeight: FontWeight.bold,
  ),
)
```

### Custom Icons
```dart
CTextField(
  controller: _controller,
  label: 'Custom Icons',
  hintText: 'With custom icons',
  prefixIcon: Icon(Icons.person, color: AppColors.primary),
  suffixIcon: IconButton(
    icon: Icon(Icons.clear),
    onPressed: () => _controller.clear(),
  ),
)
```

## 🔄 Migration từ Components cũ

### Từ AppInputField
```dart
// Cũ
AppInputField(
  controller: _controller,
  label: 'Email',
  placeholder: 'Nhập email',
  isRequired: true,
  keyboardType: TextInputType.emailAddress,
)

// Mới
CTextField(
  controller: _controller,
  label: 'Email',
  hintText: 'Nhập email',
  isRequired: true,
  keyboardType: TextInputType.emailAddress,
)
```

### Từ Custom TextFormField
```dart
// Cũ - Custom horizontal layout
Row(
  children: [
    Expanded(flex: 2, child: Text('Label')),
    Expanded(flex: 3, child: TextFormField(...)),
  ],
)

// Mới
CTextFieldExtensions.horizontal(
  controller: _controller,
  label: 'Label',
  hintText: 'Hint',
  labelFlex: 2,
  inputFlex: 3,
)
```

## 💡 Best Practices

1. **Sử dụng Extension Methods** cho các use cases phổ biến
2. **Vertical layout** cho forms thông thường
3. **Horizontal layout** cho detail/settings screens
4. **Search extension** cho search interfaces
5. **Password extension** cho password fields
6. **Consistent validation** across toàn app
7. **Proper keyboard types** cho từng loại input

## 🐛 Troubleshooting

### Layout Issues
- Đảm bảo `labelFlex` và `inputFlex` phù hợp với content
- Sử dụng `textAlign` cho horizontal layout
- Kiểm tra `contentPadding` cho spacing

### Validation Issues
- Đảm bảo `validator` return `null` khi valid
- Sử dụng `isRequired` cho visual indicator
- Test validation với empty/invalid inputs

### Styling Issues
- Sử dụng `AppTextStyles` constants
- Kiểm tra `height` property cho custom heights
- Đảm bảo colors contrast đủ cho accessibility

---

**Demo Screen**: Xem `c_text_field_demo_screen.dart` để test tất cả tính năng!

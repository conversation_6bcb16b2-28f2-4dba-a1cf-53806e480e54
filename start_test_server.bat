@echo off
echo ========================================
echo   C-CAM Terminal Test Server
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if we have the simple test server
if exist "test_server_simple.js" (
    echo Starting Simple Test Server...
    echo Server will be available at:
    echo   - Dashboard: http://localhost:3000
    echo   - WebSocket: ws://localhost:3000
    echo   - API: http://localhost:3000/api
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    node test_server_simple.js
) else if exist "lib\packages\relay_controller\test_server\server.js" (
    echo Starting Enhanced Test Server...
    cd lib\packages\relay_controller\test_server
    
    REM Install dependencies if needed
    if not exist "node_modules" (
        echo Installing dependencies...
        npm install
        echo.
    )
    
    echo Server will be available at:
    echo   - Dashboard: http://localhost:3000
    echo   - WebSocket: ws://localhost:3000
    echo   - API: http://localhost:3000/api
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    node server.js
) else (
    echo ERROR: No test server found!
    echo Please make sure you have either:
    echo   - test_server_simple.js in the root directory
    echo   - lib\packages\relay_controller\test_server\server.js
    pause
    exit /b 1
)

pause

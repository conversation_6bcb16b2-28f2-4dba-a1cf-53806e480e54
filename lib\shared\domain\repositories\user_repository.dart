import 'package:dartz/dartz.dart';
import '../entities/user/user.dart';
import '../../core/errors/failures.dart';

/// Repository interface for user-related operations
abstract class UserRepository {
  /// Get all users with pagination and optional filtering
  Future<Either<Failure, List<User>>> getUsers({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? unitId,
    String? memberRoleId,
  });

  /// Get user by ID
  Future<Either<Failure, User>> getUserById(String userId);

  /// Get users by unit ID
  Future<Either<Failure, List<User>>> getUsersByUnit(String unitId);

  /// Get users by member role ID
  Future<Either<Failure, List<User>>> getUsersByRole(String memberRoleId);

  /// Create a new user
  Future<Either<Failure, User>> createUser({
    required String username,
    required String password,
    required String name,
    required String unitId,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? memberRoleId,
  });

  /// Update user information
  Future<Either<Failure, User>> updateUser({
    required String userId,
    String? name,
    String? email,
    String? phone,
    String? dob,
    String? gender,
    String? unitId,
    String? memberRoleId,
  });

  /// Delete user
  Future<Either<Failure, void>> deleteUser(String userId);

  /// Upload user avatar
  Future<Either<Failure, User>> uploadAvatar({
    required String userId,
    required String fileName,
    required String fileData,
  });
}

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

// Route names
import 'terminal_route_names.dart';

// Terminal providers
import '../providers/terminal_auth_provider.dart';
import '../providers/kiosk_mode_provider.dart';

// Terminal screens
import '../presentation/screens/splash_screen.dart';
import '../presentation/screens/stream_screen.dart';

// Shared error screens
import '../../../shared/components/error_screen.dart';
import '../../../shared/components/not_found_screen.dart';

/// Terminal app router configuration
/// 
/// Optimized for kiosk mode with simplified navigation and auto-timeout features
/// Supports both kiosk mode and admin mode routing
class TerminalRouter {
  static final GlobalKey<NavigatorState> _rootNavigatorKey =
      GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;

  /// Create the terminal app router
  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: TerminalRouteNames.splash,
      debugLogDiagnostics: kDebugMode,

      // Redirect logic for kiosk mode and admin authentication
      redirect: (BuildContext context, GoRouterState state) {
        final authProvider = context.read<TerminalAuthProvider>();
        final kioskProvider = context.read<KioskModeProvider>();
        final currentLocation = state.uri.path;

        // Debug logging
        if (kDebugMode) {
          debugPrint(
            'Terminal Router redirect - Current: $currentLocation, '
            'Auth Status: ${authProvider.authStatus}, '
            'Kiosk Mode: ${kioskProvider.isKioskMode}, '
            'Admin Auth: ${authProvider.isAdminAuthenticated}',
          );
        }

        // Handle initial loading state - stay on splash
        if (authProvider.authStatus == AuthStatus.initial ||
            authProvider.authStatus == AuthStatus.loading) {
          if (currentLocation != TerminalRouteNames.splash) {
            return TerminalRouteNames.splash;
          }
          return null; // Already on splash, don't redirect
        }

        // If in kiosk mode and trying to access admin routes without auth
        if (kioskProvider.isKioskMode && 
            TerminalRouteNames.isAdminRoute(currentLocation) &&
            !authProvider.isAdminAuthenticated) {
          if (kDebugMode) {
            debugPrint('Redirecting to admin login from kiosk mode');
          }
          return TerminalRouteNames.adminLogin;
        }

        // If admin authenticated and trying to access kiosk routes, allow it
        // (admin can view kiosk interface)

        // If on splash and initialization is complete, redirect to appropriate screen
        if (currentLocation == TerminalRouteNames.splash &&
            authProvider.authStatus != AuthStatus.initial &&
            authProvider.authStatus != AuthStatus.loading) {
          
          // If admin is authenticated, go to admin settings
          if (authProvider.isAdminAuthenticated) {
            return TerminalRouteNames.adminSettings;
          }
          
          // Otherwise go to kiosk home
          return TerminalRouteNames.kioskHome;
        }

        // Allow the navigation
        return null;
      },

      // Error handling
      errorBuilder: (context, state) => ErrorScreen(
        error: state.error?.toString() ?? 'Unknown error occurred',
      ),

      routes: [
        // ======================================================================
        // SPLASH SCREEN
        // ======================================================================
        GoRoute(
          path: TerminalRouteNames.splash,
          name: 'splash',
          builder: (context, state) => const SplashScreen(),
        ),

        // ======================================================================
        // KIOSK MODE ROUTES
        // ======================================================================
        GoRoute(
          path: TerminalRouteNames.kioskHome,
          name: 'kioskHome',
          builder: (context, state) => const StreamScreen(), // Using stream as home for now
        ),

        GoRoute(
          path: TerminalRouteNames.stream,
          name: 'stream',
          builder: (context, state) => const StreamScreen(),
        ),

        GoRoute(
          path: TerminalRouteNames.faceCapture,
          name: 'faceCapture',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        GoRoute(
          path: TerminalRouteNames.faceResult,
          name: 'faceResult',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        GoRoute(
          path: TerminalRouteNames.kioskIdle,
          name: 'kioskIdle',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        // ======================================================================
        // ADMIN ROUTES
        // ======================================================================
        GoRoute(
          path: TerminalRouteNames.adminLogin,
          name: 'adminLogin',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        GoRoute(
          path: TerminalRouteNames.adminSettings,
          name: 'adminSettings',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        GoRoute(
          path: TerminalRouteNames.adminUsers,
          name: 'adminUsers',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        GoRoute(
          path: TerminalRouteNames.adminSystem,
          name: 'adminSystem',
          builder: (context, state) => const StreamScreen(), // Placeholder
        ),

        // ======================================================================
        // ROOT ROUTE - REDIRECT TO APPROPRIATE SCREEN
        // ======================================================================
        GoRoute(
          path: TerminalRouteNames.root,
          name: 'root',
          redirect: (context, state) {
            final authProvider = context.read<TerminalAuthProvider>();
            final kioskProvider = context.read<KioskModeProvider>();
            
            // If admin is authenticated, go to admin interface
            if (authProvider.isAdminAuthenticated) {
              return TerminalRouteNames.adminSettings;
            }
            
            // If in kiosk mode or default, go to kiosk home
            if (kioskProvider.isKioskMode) {
              return TerminalRouteNames.kioskHome;
            }
            
            // Default to kiosk home
            return TerminalRouteNames.kioskHome;
          },
        ),

        // ======================================================================
        // ERROR ROUTES
        // ======================================================================
        GoRoute(
          path: TerminalRouteNames.notFound,
          name: 'notFound',
          builder: (context, state) => const NotFoundScreen(),
        ),

        GoRoute(
          path: TerminalRouteNames.error,
          name: 'error',
          builder: (context, state) {
            final errorMessage =
                state.uri.queryParameters['message'] ?? 'Unknown error';
            return ErrorScreen(error: errorMessage);
          },
        ),
      ],
    );
  }
}

import 'package:c_face_terminal/apps/mobile/routes/mobile_route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/components/app_button.dart';
import '../../../../shared/components/app_input_field.dart';
import '../../../../shared/domain/use_cases/tenant/create_tenant_use_case.dart';
import '../../../../shared/core/errors/failures.dart';

/// Màn hình tạo mới tổ chức
class TenantCreateScreen extends StatefulWidget {
  const TenantCreateScreen({super.key});

  @override
  State<TenantCreateScreen> createState() => _TenantCreateScreenState();
}

class _TenantCreateScreenState extends State<TenantCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Add listeners to update button state
    _nameController.addListener(() => setState(() {}));
    _addressController.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  bool get _isFormValid {
    return _nameController.text.trim().isNotEmpty;
  }

  void _handleCreate() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Call create tenant API
        final useCase = GetIt.instance<CreateTenantUseCase>();
        final result = await useCase(CreateTenantParams(
          name: _nameController.text.trim(),
          address: _addressController.text.trim().isNotEmpty
              ? _addressController.text.trim()
              : null,
        ));

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          result.fold(
            (failure) {
              // Show error message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Lỗi tạo tổ chức: ${_getErrorMessage(failure)}'),
                  backgroundColor: AppColors.error,
                ),
              );
            },
            (tenant) {
              // Navigate back to tenants screen with success message
              context.go(MobileRouteNames.tenants);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Tổ chức "${tenant.name}" đã được tạo thành công!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
          );
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi không mong muốn: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  void _handleBack() {
    // Back navigation đã được xử lý bởi BackNavigationWrapper
    // Nhưng vẫn giữ logic này cho trường hợp manual back button
    context.go(MobileRouteNames.tenants);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.spacing12),
                    _buildFormSection(),
                    SizedBox(height: AppDimensions.spacing16),
                  ],
                ),
              ),
            ),
            _buildCreateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: _handleBack,
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Text(
            'Thêm mới tổ chức',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormSection() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(),
          SizedBox(height: AppDimensions.spacing16),
          _buildForm(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader() {
    return Row(
      children: [
        Container(
          width: 2,
          height: 16,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(1),
          ),
        ),
        SizedBox(width: AppDimensions.spacing8),
        Text(
          'Thông tin tổ chức',
          style: AppTextStyles.bodySmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin chung',
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textTertiary,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        _buildFormContainer(),
      ],
    );
  }

  Widget _buildFormContainer() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: AppColors.border),
      ),
      child: Form(
        key: _formKey,
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            children: [
              AppInputField(
                controller: _nameController,
                label: 'Tên tổ chức',
                placeholder: 'Nhập tên tổ chức',
                isRequired: true,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return 'Vui lòng nhập tên tổ chức';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppDimensions.spacing16),
              AppInputField(
                controller: _addressController,
                label: 'Địa chỉ',
                placeholder: 'Nhập thông tin địa chỉ',
                isRequired: false,
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildCreateButton() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: AppButton(
        text: 'Thêm mới',
        onPressed: (_isLoading || !_isFormValid) ? null : _handleCreate,
        isLoading: _isLoading,
      ),
    );
  }

  /// Get error message from failure
  String _getErrorMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.';
    } else if (failure is ServerFailure) {
      return failure.message.isNotEmpty
          ? failure.message
          : 'Lỗi server. Vui lòng thử lại sau.';
    } else if (failure is AuthFailure) {
      return 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
    } else if (failure is ValidationFailure) {
      return failure.message.isNotEmpty
          ? failure.message
          : 'Dữ liệu không hợp lệ.';
    } else {
      return 'Đã xảy ra lỗi. Vui lòng thử lại.';
    }
  }
}
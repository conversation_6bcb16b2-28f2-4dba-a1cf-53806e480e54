import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../data/data_sources/remote/auth_remote_data_source.dart';
import '../../../data/data_sources/local/auth_local_data_source.dart';
import '../../../data/repositories/auth_repository_impl.dart';
import '../../../domain/repositories/auth_repository.dart';
import '../../../domain/use_cases/auth/login_use_case.dart';
import '../../../domain/use_cases/auth/logout_use_case.dart';
import '../../../domain/use_cases/auth/refresh_token_use_case.dart';
import '../../../domain/use_cases/auth/change_password_use_case.dart';
import '../../../domain/use_cases/user/update_profile_use_case.dart';
import '../../../../apps/mobile/presentation/providers/auth_provider.dart';
import '../../network/api_client.dart';
import '../../../services/platform_http_service.dart';
import '../../../services/http_client_service.dart';

final GetIt getIt = GetIt.instance;

/// Module đăng ký các dependency liên quan đến Authentication
void registerAuthDependencies() {
  // ============================================================================
  // DATA SOURCES
  // ============================================================================

  // Remote Data Sources
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      apiClient: getIt<ApiClient>(),
      httpClientService: HttpClientService(),
      platformHttpService: PlatformHttpService(),
    ),
  );

  // Local Data Sources
  getIt.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(
      secureStorage: getIt<FlutterSecureStorage>(),
    ),
  );

  // ============================================================================
  // REPOSITORIES
  // ============================================================================

  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: getIt<AuthRemoteDataSource>(),
      localDataSource: getIt<AuthLocalDataSource>(),
      apiClient: getIt<ApiClient>(),
    ),
  );

  // ============================================================================
  // USE CASES
  // ============================================================================

  getIt.registerLazySingleton<LoginUseCase>(
    () => LoginUseCase(getIt<AuthRepository>()),
  );

  getIt.registerLazySingleton<LogoutUseCase>(
    () => LogoutUseCase(getIt<AuthRepository>()),
  );

  getIt.registerLazySingleton<RefreshTokenUseCase>(
    () => RefreshTokenUseCase(getIt<AuthRepository>()),
  );

  getIt.registerLazySingleton<ChangePasswordUseCase>(
    () => ChangePasswordUseCase(getIt<AuthRepository>()),
  );

  getIt.registerLazySingleton<UpdateProfileUseCase>(
    () => UpdateProfileUseCase(getIt<AuthRepository>()),
  );

  // ============================================================================
  // PROVIDERS
  // ============================================================================

  getIt.registerFactory<AuthProvider>(
    () => AuthProvider(
      loginUseCase: getIt<LoginUseCase>(),
      logoutUseCase: getIt<LogoutUseCase>(),
      refreshTokenUseCase: getIt<RefreshTokenUseCase>(),
    ),
  );
}

/// Unregister tất cả auth dependencies (for testing)
void unregisterAuthDependencies() {
  if (getIt.isRegistered<AuthProvider>()) {
    getIt.unregister<AuthProvider>();
  }
  if (getIt.isRegistered<RefreshTokenUseCase>()) {
    getIt.unregister<RefreshTokenUseCase>();
  }
  if (getIt.isRegistered<ChangePasswordUseCase>()) {
    getIt.unregister<ChangePasswordUseCase>();
  }
  if (getIt.isRegistered<UpdateProfileUseCase>()) {
    getIt.unregister<UpdateProfileUseCase>();
  }
  if (getIt.isRegistered<LogoutUseCase>()) {
    getIt.unregister<LogoutUseCase>();
  }
  if (getIt.isRegistered<LoginUseCase>()) {
    getIt.unregister<LoginUseCase>();
  }
  if (getIt.isRegistered<AuthRepository>()) {
    getIt.unregister<AuthRepository>();
  }
  if (getIt.isRegistered<AuthLocalDataSource>()) {
    getIt.unregister<AuthLocalDataSource>();
  }
  if (getIt.isRegistered<AuthRemoteDataSource>()) {
    getIt.unregister<AuthRemoteDataSource>();
  }
}

/// Reset auth module (clear và re-register)
void resetAuthModule() {
  unregisterAuthDependencies();
  registerAuthDependencies();
}

/// Check if auth dependencies are registered
bool areAuthDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  return true; // Placeholder
}

/// Get auth-related dependencies for debugging
Map<String, bool> getAuthDependenciesStatus() {
  return {
    'AuthRemoteDataSource': getIt.isRegistered<AuthRemoteDataSource>(),
    'AuthLocalDataSource': getIt.isRegistered<AuthLocalDataSource>(),
    'AuthRepository': getIt.isRegistered<AuthRepository>(),
    'LoginUseCase': getIt.isRegistered<LoginUseCase>(),
    'LogoutUseCase': getIt.isRegistered<LogoutUseCase>(),
    'RefreshTokenUseCase': getIt.isRegistered<RefreshTokenUseCase>(),
    'ChangePasswordUseCase': getIt.isRegistered<ChangePasswordUseCase>(),
    'UpdateProfileUseCase': getIt.isRegistered<UpdateProfileUseCase>(),
    'AuthProvider': getIt.isRegistered<AuthProvider>(),
    'ApiClient': getIt.isRegistered<ApiClient>(),
  };
}

/// Update base URL for ApiClient in auth module
void updateAuthApiClientBaseUrl(String baseUrl) {
  if (getIt.isRegistered<ApiClient>()) {
    final apiClient = getIt<ApiClient>();
    apiClient.setBaseUrl(baseUrl);
  }
}

import 'package:flutter/material.dart';

/// Face Capture Configuration Constants
/// 
/// This file contains all configurable parameters for the face capture system.
/// Modify these values to adjust the behavior of face detection, auto capture,
/// UI elements, and quality thresholds.
class FaceCaptureConstants {
  
  // ============================================================================
  // AUTO CAPTURE TIMING CONFIGURATION
  // ============================================================================
  
  /// Delay between consecutive auto captures to prevent rapid firing
  /// Ultra-optimized for fastest capture: reduced to 800ms (1/2 of previous)
  static const Duration autoCaptureDelay = Duration(milliseconds: 800);

  /// Delay before attempting auto capture after direction detection
  /// Ultra-optimized: reduced to 150ms for immediate response
  static const Duration capturePreDelay = Duration(milliseconds: 150);

  /// Delay to ensure camera stability before capture
  /// Ultra-optimized: reduced to 25ms for minimal delay
  static const Duration cameraStabilityDelay = Duration(milliseconds: 25);

  /// Delay after capture before allowing next capture
  /// Ultra-optimized: reduced to 50ms for rapid succession
  static const Duration postCaptureDelay = Duration(milliseconds: 50);
  
  // ============================================================================
  // FACE DETECTION QUALITY THRESHOLDS
  // ============================================================================
  
  /// Quality threshold for front face direction (frontal view)
  /// Higher threshold for best quality front shots
  /// Range: 0.0 - 1.0, Recommended: 0.6 - 0.8
  static const double frontQualityThreshold = 0.7;

  /// Quality threshold for top/bottom face directions
  /// Medium threshold for vertical head movements
  /// Range: 0.0 - 1.0, Recommended: 0.5 - 0.7
  static const double verticalQualityThreshold = 0.6;
  
  /// Quality threshold for left/right face directions (profile views)
  /// Lower threshold as profile views naturally have lower quality scores
  /// Range: 0.0 - 1.0, Recommended: 0.3 - 0.5
  static const double profileQualityThreshold = 0.4;
  
  // ============================================================================
  // FACE DIRECTION DETECTION ANGLES (in degrees)
  // ============================================================================
  
  /// Euler angle X threshold for detecting upward head movement
  /// Negative values indicate looking up
  /// Range: -30 to 0, Recommended: -10 to -20
  static const double upAngleThreshold = -15.0;
  
  /// Euler angle X threshold for detecting downward head movement
  /// Positive values indicate looking down
  /// Range: 0 to 30, Recommended: 10 to 20
  static const double downAngleThreshold = 15.0;
  
  /// Euler angle Y threshold for detecting leftward head turn
  /// Positive values indicate turning left
  /// Range: 10 to 30, Recommended: 15 to 25
  static const double leftAngleThreshold = 15.0;
  
  /// Euler angle Y threshold for detecting rightward head turn
  /// Negative values indicate turning right
  /// Range: -30 to -10, Recommended: -25 to -15
  static const double rightAngleThreshold = -15.0;
  
  // ============================================================================
  // CAMERA OVERLAY CONFIGURATION
  // ============================================================================

  /// Enable/disable camera overlay mask
  /// Set to false for full camera view, true for focused circular view
  static const bool enableCameraOverlay = true;

  /// Overlay opacity when enabled (0.0 = transparent, 1.0 = opaque)
  /// Lower values show more background, higher values focus more on circle
  static const double overlayOpacity = 0.4;

  // ============================================================================
  // FACE GUIDE FRAME DIMENSIONS
  // ============================================================================
  
  /// Width of the face capture guide frame in pixels
  /// This defines the target area for face positioning
  static const double guideFrameWidth = 250.0;
  
  /// Height of the face capture guide frame in pixels
  /// This defines the target area for face positioning
  static const double guideFrameHeight = 300.0;
  
  /// Tolerance margin around guide frame for face detection
  /// Allows some flexibility in face positioning
  /// Range: 10-50 pixels, Recommended: 15-25
  static const double guideFrameTolerance = 20.0;
  
  // ============================================================================
  // FACE CAPTURE GUIDE UI CONFIGURATION
  // ============================================================================
  
  /// Size of individual guide icons (width and height)
  static const double guideIconSize = 32.0;
  
  /// Spacing between guide icons horizontally
  static const double guideIconSpacing = 8.0;
  
  /// Spacing between guide icon rows vertically
  static const double guideRowSpacing = 8.0;
  
  /// Padding inside the guide container
  static const EdgeInsets guidePadding = EdgeInsets.symmetric(
    horizontal: 15.0,
    vertical: 15.0,
  );
  
  /// Border radius for the guide container
  static const double guideBorderRadius = 12.0;
  
  /// Border width for guide icons
  static const double guideIconBorderWidth = 1.5;
  
  // ============================================================================
  // FACE CAPTURE GUIDE COLORS
  // ============================================================================
  
  /// Background color for the face capture guide container
  static const Color guideBackgroundColor = Color(0xFF1F2329);
  
  /// Background opacity for the guide container
  static const double guideBackgroundOpacity = 0.7;
  
  /// Border color for inactive guide icons
  static const Color guideInactiveBorderColor = Color(0xFF73787E);
  
  /// Border color for currently active direction
  static const Color guideActiveBorderColor = Colors.green;
  
  /// Border color for captured directions
  static const Color guideCapturedBorderColor = Colors.blue;
  
  // ============================================================================
  // ARROW AND FACE ICON DIMENSIONS
  // ============================================================================
  
  /// Size of arrow icons within guide icons
  static const double arrowIconSize = 6.0;
  
  /// Size of face icons within guide icons
  static const double faceIconSize = 14.0;
  
  /// Spacing between arrow and face icons
  static const double iconContentSpacing = 2.0;
  
  // ============================================================================
  // BOUNDING BOX CONFIGURATION
  // ============================================================================
  
  /// Stroke width for face bounding boxes
  static const double boundingBoxStrokeWidth = 3.0;
  
  /// Length of corner indicators on bounding boxes
  static const double cornerIndicatorLength = 15.0;
  
  /// Stroke width for corner indicators
  static const double cornerIndicatorStrokeWidth = 2.0;
  
  // ============================================================================
  // QUALITY INDICATOR CONFIGURATION
  // ============================================================================
  
  /// Font size for quality text labels
  static const double qualityTextSize = 12.0;
  
  /// Font size for coordinate text labels
  static const double coordinateTextSize = 10.0;
  
  /// Padding around quality text background
  static const double qualityTextPadding = 4.0;
  
  /// Height of quality text background
  static const double qualityTextHeight = 20.0;
  
  /// Border radius for quality text background
  static const double qualityTextBorderRadius = 4.0;
  
  /// Width of quality progress bar
  static const double qualityBarWidth = 60.0;
  
  /// Height of quality progress bar
  static const double qualityBarHeight = 4.0;
  
  /// Border radius for quality progress bar
  static const double qualityBarBorderRadius = 2.0;
  
  // ============================================================================
  // FACE DETECTION PROCESSING CONFIGURATION
  // ============================================================================
  
  /// Number of frames to skip between face detection processing
  /// Ultra-optimized for maximum responsiveness: reduced to 1 for fastest detection
  /// Range: 1-10, Optimized for speed over battery life
  /// Maximum responsiveness for instant capture
  static const int frameSkipCount = 1;

  /// Timeout for face detection processing per frame
  /// Ultra-optimized: reduced timeout for faster processing
  /// Optimized for speed while maintaining stability
  static const Duration detectionTimeout = Duration(milliseconds: 80);
  
  // ============================================================================
  // PROGRESS INDICATOR CONFIGURATION
  // ============================================================================
  
  /// Total number of directions to capture
  static const int totalDirectionsCount = 5;
  
  /// Background color for progress indicator container
  static const Color progressBackgroundColor = Colors.black;
  
  /// Background opacity for progress indicator
  static const double progressBackgroundOpacity = 0.3;
  
  /// Border radius for progress indicator container
  static const double progressBorderRadius = 12.0;
  
  /// Padding for progress indicator container
  static const EdgeInsets progressPadding = EdgeInsets.all(16.0);
  
  /// Margin for progress indicator container
  static const EdgeInsets progressMargin = EdgeInsets.only(bottom: 16.0);
  
  /// Color for progress bar fill
  static const Color progressFillColor = Color(0xFF40BF24);
  
  /// Background color for progress bar track
  static const Color progressTrackColor = Colors.grey;
  
  /// Opacity for progress bar track
  static const double progressTrackOpacity = 0.3;
  
  // ============================================================================
  // BUTTON AND UI COLORS
  // ============================================================================
  
  /// Primary button color when active
  static const Color primaryButtonColor = Color(0xFF008FD3);
  
  /// Success button color
  static const Color successButtonColor = Color(0xFF40BF24);
  
  /// Warning button color
  static const Color warningButtonColor = Color(0xFFFF9800);
  
  /// Error button color
  static const Color errorButtonColor = Color(0xFFF44336);
  
  /// Disabled button color
  static const Color disabledButtonColor = Colors.grey;
  
  /// Disabled button opacity
  static const double disabledButtonOpacity = 0.5;
  
  // ============================================================================
  // QUALITY COLOR MAPPING
  // ============================================================================
  
  /// Color for excellent quality (90%+)
  static const Color excellentQualityColor = Color(0xFF4CAF50);
  
  /// Color for good quality (75-89%)
  static const Color goodQualityColor = Color(0xFF8BC34A);
  
  /// Color for fair quality (60-74%)
  static const Color fairQualityColor = Color(0xFFFFC107);
  
  /// Color for acceptable quality (50-59%)
  static const Color acceptableQualityColor = Color(0xFFFF9800);
  
  /// Color for poor quality (<50%)
  static const Color poorQualityColor = Color(0xFFF44336);
  
  /// Color for multiple faces detected
  static const Color multipleFacesColor = Color(0xFFFF9800);
  
  /// Color for face out of guide frame
  static const Color outOfFrameColor = Color(0xFFFF5722);
  
  /// Color for no face detected
  static const Color noFaceColor = Colors.white;
  
  /// Opacity for no face detected
  static const double noFaceOpacity = 0.5;

  // ============================================================================
  // SNACKBAR AND NOTIFICATION CONFIGURATION
  // ============================================================================

  /// Duration for success notifications
  static const Duration successNotificationDuration = Duration(seconds: 1);

  /// Duration for error notifications
  static const Duration errorNotificationDuration = Duration(seconds: 1);

  /// Background color for success notifications
  static const Color successNotificationColor = Color(0xFF40BF24);

  /// Background color for error notifications
  static const Color errorNotificationColor = Colors.red;

  // ============================================================================
  // FACE GUIDE OVERLAY CONFIGURATION
  // ============================================================================

  /// Face outline guide position (left margin as percentage of width)
  static const double faceGuideLeftMargin = 0.2;

  /// Face outline guide position (top margin as percentage of height)
  static const double faceGuideTopMargin = 0.15;

  /// Face outline guide width as percentage of total width
  static const double faceGuideWidthRatio = 0.6;

  /// Face outline guide height as percentage of total height
  static const double faceGuideHeightRatio = 0.7;

  /// Left eye guide position (x as percentage of width)
  static const double leftEyePositionX = 0.35;

  /// Right eye guide position (x as percentage of width)
  static const double rightEyePositionX = 0.65;

  /// Eye guide position (y as percentage of height)
  static const double eyePositionY = 0.35;

  /// Eye guide circle radius
  static const double eyeGuideRadius = 4.0;

  /// Mouth guide position (x as percentage of width)
  static const double mouthPositionX = 0.5;

  /// Mouth guide position (y as percentage of height)
  static const double mouthPositionY = 0.65;

  /// Mouth guide width as percentage of total width
  static const double mouthGuideWidthRatio = 0.2;

  /// Mouth guide height as percentage of total height
  static const double mouthGuideHeightRatio = 0.1;

  // ============================================================================
  // LANDMARK DETECTION CONFIGURATION
  // ============================================================================

  /// Color for facial landmark points
  static const Color landmarkColor = Colors.yellow;

  /// Radius for facial landmark points
  static const double landmarkRadius = 2.0;

  // ============================================================================
  // TRACKING ID CONFIGURATION
  // ============================================================================

  /// Font size for tracking ID text
  static const double trackingIdTextSize = 10.0;

  /// Padding around tracking ID text
  static const double trackingIdPadding = 4.0;

  /// Height of tracking ID background
  static const double trackingIdHeight = 16.0;

  /// Vertical offset for tracking ID from bounding box
  static const double trackingIdVerticalOffset = 25.0;

  /// Additional vertical offset for tracking ID positioning
  static const double trackingIdExtraOffset = 27.0;

  // ============================================================================
  // FACE ANGLE DISPLAY CONFIGURATION
  // ============================================================================

  /// Font size for face angle coordinates text
  static const double angleTextSize = 10.0;

  /// Padding around face angle text
  static const double angleTextPadding = 4.0;

  /// Height of face angle text background
  static const double angleTextHeight = 16.0;

  /// Vertical offset for face angles from bounding box
  static const double angleVerticalOffset = 5.0;

  /// Additional vertical offset for face angles positioning
  static const double angleExtraOffset = 7.0;

  /// Background color for face angle text
  static const Color angleBackgroundColor = Colors.black;

  /// Background opacity for face angle text
  static const double angleBackgroundOpacity = 0.7;

  /// Number of decimal places for angle display
  static const int angleDecimalPlaces = 1;

  // ============================================================================
  // CAMERA SETTINGS CONFIGURATION
  // ============================================================================

  /// Whether to pause camera stream during capture
  /// Set to false to avoid camera session conflicts
  static const bool pauseStreamForCapture = false;

  /// Whether face detection is always enabled
  static const bool alwaysEnableFaceDetection = true;

  // ============================================================================
  // PERFORMANCE MONITORING CONFIGURATION
  // ============================================================================

  /// Whether to show performance profiling overlay
  /// Set to true to enable profiling display, false to hide it completely
  /// This overrides debug mode settings for production profiling control
  static const bool showPerformanceProfiling = false;

  // ============================================================================
  // STATUS MESSAGES
  // ============================================================================

  /// Default status message when no face is detected
  static const String defaultStatusMessage = 'Đưa khuôn mặt vào khung hình';

  /// Status message for multiple faces detected
  static const String multipleFacesMessage = 'Phát hiện {count} khuôn mặt - Chỉ để 1 người';

  /// Status message when face is out of guide frame
  static const String outOfFrameMessage = 'Di chuyển khuôn mặt vào khung hướng dẫn';

  /// Status message when capturing
  static const String capturingMessage = 'Đang chụp hướng {direction}...';

  /// Status message when already captured
  static const String capturedMessage = 'Đã chụp hướng {direction} ({count}/{total})';

  /// Status message for quality improvement
  static const String qualityImprovementMessage = 'Cải thiện chất lượng cho hướng {direction} ({current}%/{required}%)';

  /// Status message for rotating face
  static const String rotateFaceMessage = 'Xoay mặt theo các hướng để chụp ảnh ({count}/{total})';

  /// Success capture message
  static const String successCaptureMessage = 'Đã chụp ảnh hướng {direction}!';

  /// Error capture message
  static const String errorCaptureMessage = 'Lỗi khi chụp ảnh tự động!';

  /// Camera not ready message
  static const String cameraNotReadyMessage = 'Camera not ready';

  // ============================================================================
  // DIRECTION NAMES (Vietnamese)
  // ============================================================================

  /// Display name for front direction
  static const String frontDirectionName = 'chính giữa';

  /// Display name for top direction (looking down action)
  static const String topDirectionName = 'nhìn xuống';

  /// Display name for bottom direction (looking up action)
  static const String bottomDirectionName = 'nhìn lên';

  /// Display name for left direction (turning right action)
  static const String leftDirectionName = 'quay phải';

  /// Display name for right direction (turning left action)
  static const String rightDirectionName = 'quay trái';

  // ============================================================================
  // QUALITY LEVEL NAMES
  // ============================================================================

  /// Quality level name for excellent quality
  static const String excellentQualityName = 'EXCELLENT';

  /// Quality level name for good quality
  static const String goodQualityName = 'GOOD';

  /// Quality level name for fair quality
  static const String fairQualityName = 'FAIR';

  /// Quality level name for acceptable quality
  static const String acceptableQualityName = 'ACCEPTABLE';

  /// Quality level name for poor quality
  static const String poorQualityName = 'POOR';
}

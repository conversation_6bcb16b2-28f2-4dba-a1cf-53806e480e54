/// Configuration Index - Export all configuration classes
///
/// This file exports all configuration-related components including
/// app configuration and theme settings.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/config/index.dart';
/// 
/// // Use app configuration
/// final config = AppConfig();
/// final apiUrl = config.baseApiUrl;
/// final isDev = config.isDevelopment;
/// 
/// // Use theme
/// final theme = AppTheme.theme;
/// ```

// ============================================================================
// CONFIGURATION EXPORTS
// ============================================================================

/// Legacy application configuration (will be deprecated)
export 'app_config.dart' hide AppEnvironment, AppConfigUtils, FaceDetectionMode, CameraResolution;

/// Base application configuration for multi-app architecture
export 'base_app_config.dart';

/// Application configuration factory for multi-app support
export 'app_config_factory.dart';

/// Application theme configuration
export 'theme.dart';

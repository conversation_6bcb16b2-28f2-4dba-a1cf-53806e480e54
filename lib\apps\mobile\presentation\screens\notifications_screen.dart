import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/icons/app_icons.dart';

/// Màn hình thông báo
class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  int _selectedFilterIndex = 0;

  final List<Map<String, dynamic>> _filters = [
    {'title': 'Tất cả', 'count': 5},
    {'title': 'An ninh', 'count': 1},
    {'title': 'Hệ thống', 'count': 4},
  ];

  final List<Map<String, dynamic>> _notifications = [
    {
      'id': '1',
      'type': 'camera',
      'title': 'Camera đã kết nối',
      'description': 'Camera 1 đã kết nối thành công với AI Box 1',
      'time': '2 phút trước',
      'isRead': false,
      'category': 'system',
      'iconColor': AppColors.primary,
      'backgroundColor': const Color(0xFFE5F4FB),
    },
    {
      'id': '2',
      'type': 'device',
      'title': 'Thiết bị biên đã kết nối',
      'description': 'AI Box 1 đã kết nối tới hệ thống thành công',
      'time': '2 phút trước',
      'isRead': false,
      'category': 'system',
      'iconColor': AppColors.primary,
      'backgroundColor': const Color(0xFFE5F4FB),
    },
    {
      'id': '3',
      'type': 'camera',
      'title': 'Camera mất kết nối',
      'description': 'Camera 1 đã bị mất kết nối với AI Box 1',
      'time': '2 phút trước',
      'isRead': false,
      'category': 'system',
      'iconColor': const Color(0xFFF54A45),
      'backgroundColor': const Color(0xFFF54A45).withValues(alpha: 0.1),
    },
    {
      'id': '4',
      'type': 'device',
      'title': 'Thiết bị biên mất kết nối',
      'description': 'AI Box 1 đã bị mất kết nối với hệ thống',
      'time': '2 phút trước',
      'isRead': false,
      'category': 'system',
      'iconColor': const Color(0xFFF54A45),
      'backgroundColor': const Color(0xFFF54A45).withValues(alpha: 0.1),
    },
    {
      'id': '5',
      'type': 'fire',
      'title': 'Phát hiện lửa',
      'description': 'Phát hiện khói lửa ở CTS',
      'time': '2 phút trước',
      'isRead': true,
      'category': 'security',
      'iconColor': const Color(0xFFFF9800),
      'backgroundColor': const Color(0xFFFFF5E5),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.spacing12),
                    _buildFilterTabs(),
                    SizedBox(height: AppDimensions.spacing12),
                    _buildNotificationsList(),
                    SizedBox(height: AppDimensions.spacing24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Text(
            'Thông báo',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: _markAllAsRead,
            child: Container(
              padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingXS,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
              ),
              child: Row(
                children: [
                  AppIcons.notificationCheck(
                    size: 14,
                    color: AppColors.primary,
                  ),
                  SizedBox(width: AppDimensions.spacing6),
                  Text(
                    'Đánh dấu đã đọc',
                    style: AppTextStyles.caption.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Row(
        children: _filters.asMap().entries.map((entry) {
          final index = entry.key;
          final filter = entry.value;
          final isSelected = _selectedFilterIndex == index;

          return Padding(
            padding: EdgeInsets.only(
              right: index < _filters.length - 1 ? AppDimensions.spacing8 : 0,
            ),
            child: GestureDetector(
              onTap: () => setState(() => _selectedFilterIndex = index),
              child: Container(
                padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS + 4,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : AppColors.surface,
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: const Color(0xFFB8C2DA).withValues(alpha: 0.24),
                            blurRadius: 32,
                            offset: const Offset(0, 12),
                          ),
                        ]
                      : null,
                ),
                child: Text(
                  '${filter['title']} (${filter['count']})',
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : AppColors.textTertiary,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNotificationsList() {
    final filteredNotifications = _getFilteredNotifications();

    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: filteredNotifications.map((notification) {
          return Padding(
            padding: EdgeInsets.only(bottom: AppDimensions.spacing12),
            child: _buildNotificationItem(notification),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNotificationItem(Map<String, dynamic> notification) {
    final isRead = notification['isRead'] as bool;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS + 4),
      decoration: BoxDecoration(
        color: isRead ? const Color(0xFFFAFDFF) : AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS + 2),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNotificationIcon(notification),
          SizedBox(width: AppDimensions.spacing12),
          Expanded(
            child: _buildNotificationContent(notification),
          ),
          SizedBox(width: AppDimensions.spacing8),
          if (!isRead) _buildUnreadIndicator(),
        ],
      ),
    );
  }

  Widget _buildNotificationIcon(Map<String, dynamic> notification) {
    final type = notification['type'] as String;
    final iconColor = notification['iconColor'] as Color;
    final backgroundColor = notification['backgroundColor'] as Color;

    Widget icon;
    switch (type) {
      case 'camera':
        icon = AppIcons.notificationCamera(size: 14, color: iconColor);
        break;
      case 'device':
        icon = AppIcons.notificationDevice(size: 14, color: iconColor);
        break;
      case 'fire':
        icon = AppIcons.notificationFire(size: 14, color: iconColor);
        break;
      default:
        icon = AppIcons.notificationCamera(size: 14, color: iconColor);
    }

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24),
      ),
      child: icon,
    );
  }

  Widget _buildNotificationContent(Map<String, dynamic> notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          notification['title'] as String,
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacing4),
        Text(
          notification['description'] as String,
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textTertiary,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        Text(
          notification['time'] as String,
          style: AppTextStyles.caption.copyWith(
            fontSize: 10,
            color: const Color(0xFF8F959E),
          ),
        ),
      ],
    );
  }

  Widget _buildUnreadIndicator() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.circular(50),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredNotifications() {
    if (_selectedFilterIndex == 0) {
      return _notifications; // Tất cả
    } else if (_selectedFilterIndex == 1) {
      return _notifications.where((n) => n['category'] == 'security').toList(); // An ninh
    } else {
      return _notifications.where((n) => n['category'] == 'system').toList(); // Hệ thống
    }
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification['isRead'] = true;
      }
    });
  }
}
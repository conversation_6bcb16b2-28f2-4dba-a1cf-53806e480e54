import 'package:equatable/equatable.dart';

/// User unit domain entity
/// 
/// Represents an organizational unit that users belong to
class UserUnit extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? parentId;
  final String? code;
  final UnitType type;
  final int level;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;

  const UserUnit({
    required this.id,
    required this.name,
    this.description,
    this.parentId,
    this.code,
    this.type = UnitType.department,
    this.level = 0,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.metadata = const {},
  });

  /// Copy with new values
  UserUnit copyWith({
    String? id,
    String? name,
    String? description,
    String? parentId,
    String? code,
    UnitType? type,
    int? level,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return UserUnit(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      parentId: parentId ?? this.parentId,
      code: code ?? this.code,
      type: type ?? this.type,
      level: level ?? this.level,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if this unit is a root unit (no parent)
  bool get isRoot => parentId == null;

  /// Check if this unit is a leaf unit (no children - would need additional data)
  bool get isLeaf => level == 0; // Simplified assumption

  /// Check if this unit can manage another unit
  bool canManage(UserUnit otherUnit) {
    // Can manage if this unit is parent or ancestor
    return isParentOf(otherUnit) || isAncestorOf(otherUnit);
  }

  /// Check if this unit can view another unit
  bool canView(UserUnit otherUnit) {
    // Can view if same unit, parent, child, or sibling
    return id == otherUnit.id ||
           isParentOf(otherUnit) ||
           isChildOf(otherUnit) ||
           isSiblingOf(otherUnit);
  }

  /// Check if this unit is parent of another unit
  bool isParentOf(UserUnit otherUnit) {
    return otherUnit.parentId == id;
  }

  /// Check if this unit is child of another unit
  bool isChildOf(UserUnit otherUnit) {
    return parentId == otherUnit.id;
  }

  /// Check if this unit is sibling of another unit
  bool isSiblingOf(UserUnit otherUnit) {
    return parentId != null && 
           parentId == otherUnit.parentId && 
           id != otherUnit.id;
  }

  /// Check if this unit is ancestor of another unit (would need hierarchy data)
  bool isAncestorOf(UserUnit otherUnit) {
    // Simplified check - in real implementation, you'd traverse the hierarchy
    return level > otherUnit.level && otherUnit.parentId == id;
  }

  /// Check if this unit is descendant of another unit (would need hierarchy data)
  bool isDescendantOf(UserUnit otherUnit) {
    // Simplified check - in real implementation, you'd traverse the hierarchy
    return level < otherUnit.level && parentId == otherUnit.id;
  }

  /// Get unit hierarchy path (would need additional data in real implementation)
  String get hierarchyPath {
    if (code != null) {
      return code!;
    }
    return name;
  }

  /// Get full unit name with hierarchy
  String get fullName {
    if (parentId != null) {
      return '$hierarchyPath / $name';
    }
    return name;
  }

  /// Get unit display name
  String get displayName {
    if (code != null && code!.isNotEmpty) {
      return '$code - $name';
    }
    return name;
  }

  /// Get metadata value
  T? getMetadata<T>(String key) {
    return metadata[key] as T?;
  }

  /// Get unit status
  UnitStatus get status {
    if (!isActive) return UnitStatus.inactive;
    
    switch (type) {
      case UnitType.company:
        return UnitStatus.company;
      case UnitType.division:
        return UnitStatus.division;
      case UnitType.department:
        return UnitStatus.department;
      case UnitType.team:
        return UnitStatus.team;
      case UnitType.project:
        return UnitStatus.project;
    }
  }

  /// Validate unit
  UnitValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Required fields
    if (id.isEmpty) errors.add('Unit ID is required');
    if (name.isEmpty) errors.add('Unit name is required');

    // Name validation
    if (name.length < 2) {
      errors.add('Unit name must be at least 2 characters');
    }
    if (name.length > 100) {
      errors.add('Unit name cannot exceed 100 characters');
    }

    // Code validation
    if (code != null && code!.isNotEmpty) {
      if (code!.length < 2) {
        warnings.add('Unit code is very short');
      }
      if (code!.length > 20) {
        errors.add('Unit code cannot exceed 20 characters');
      }
    }

    // Level validation
    if (level < 0) {
      errors.add('Unit level cannot be negative');
    }
    if (level > 10) {
      warnings.add('Unit level is unusually deep');
    }

    // Hierarchy validation
    if (parentId != null && parentId == id) {
      errors.add('Unit cannot be its own parent');
    }

    // Type and level consistency
    if (type == UnitType.company && level != 0) {
      warnings.add('Company units should typically be at level 0');
    }
    if (type == UnitType.team && level < 2) {
      warnings.add('Team units should typically be at level 2 or higher');
    }

    return UnitValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    parentId,
    code,
    type,
    level,
    isActive,
    createdAt,
    updatedAt,
    metadata,
  ];

  @override
  String toString() {
    return 'UserUnit(id: $id, name: $name, code: $code, type: $type, level: $level, isActive: $isActive)';
  }
}

/// Unit type enumeration
enum UnitType {
  company,
  division,
  department,
  team,
  project,
}

/// Unit status enumeration
enum UnitStatus {
  company,
  division,
  department,
  team,
  project,
  inactive,
}

/// Unit validation result
class UnitValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const UnitValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}

/// Extension for UnitType
extension UnitTypeExtension on UnitType {
  String get displayName {
    switch (this) {
      case UnitType.company:
        return 'Company';
      case UnitType.division:
        return 'Division';
      case UnitType.department:
        return 'Department';
      case UnitType.team:
        return 'Team';
      case UnitType.project:
        return 'Project';
    }
  }

  int get defaultLevel {
    switch (this) {
      case UnitType.company:
        return 0;
      case UnitType.division:
        return 1;
      case UnitType.department:
        return 2;
      case UnitType.team:
        return 3;
      case UnitType.project:
        return 4;
    }
  }

  String get description {
    switch (this) {
      case UnitType.company:
        return 'Top-level organizational unit';
      case UnitType.division:
        return 'Major business division';
      case UnitType.department:
        return 'Functional department';
      case UnitType.team:
        return 'Working team or group';
      case UnitType.project:
        return 'Project-based unit';
    }
  }
}

/// Extension for UnitStatus
extension UnitStatusExtension on UnitStatus {
  String get displayName {
    switch (this) {
      case UnitStatus.company:
        return 'Company';
      case UnitStatus.division:
        return 'Division';
      case UnitStatus.department:
        return 'Department';
      case UnitStatus.team:
        return 'Team';
      case UnitStatus.project:
        return 'Project';
      case UnitStatus.inactive:
        return 'Inactive';
    }
  }

  bool get isActive => this != UnitStatus.inactive;
}

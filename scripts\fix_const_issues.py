#!/usr/bin/env python3
"""
<PERSON>ript to fix const issues with configurable constants
Removes 'const' keyword from expressions that use configurable getters
"""

import os
import re
import sys

def fix_const_issues(file_path):
    """Fix const issues in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Patterns to fix
        patterns = [
            # const EdgeInsets with AppDimensions
            (r'const EdgeInsets\.all\(AppDimensions\.', r'EdgeInsets.all(AppDimensions.'),
            (r'const EdgeInsets\.symmetric\(\s*horizontal:\s*AppDimensions\.', r'EdgeInsets.symmetric(\n              horizontal: AppDimensions.'),
            (r'const EdgeInsets\.symmetric\(\s*vertical:\s*AppDimensions\.', r'EdgeInsets.symmetric(\n              vertical: AppDimensions.'),
            (r'const EdgeInsets\.only\(\s*([^)]*AppDimensions[^)]*)\)', r'EdgeInsets.only(\1)'),
            
            # const SizedBox with AppDimensions
            (r'const SizedBox\(width:\s*AppDimensions\.', r'SizedBox(width: AppDimensions.'),
            (r'const SizedBox\(height:\s*AppDimensions\.', r'SizedBox(height: AppDimensions.'),
            
            # const BorderRadius with AppDimensions
            (r'const BorderRadius\.circular\(AppDimensions\.', r'BorderRadius.circular(AppDimensions.'),
            
            # const TextStyle with AppColors
            (r'const TextStyle\(([^)]*color:\s*AppColors\.[^)]*)\)', r'TextStyle(\1)'),
            
            # const Container with AppColors/AppDimensions
            (r'const Container\(([^}]*(?:AppColors|AppDimensions)[^}]*)\)', r'Container(\1)'),
            
            # const Padding with AppDimensions
            (r'const Padding\(\s*padding:\s*EdgeInsets\.([^(]+)\(AppDimensions\.', r'Padding(\n        padding: EdgeInsets.\1(AppDimensions.'),
            
            # const Icon with AppColors
            (r'const Icon\(([^,]+),\s*color:\s*AppColors\.', r'Icon(\1, color: AppColors.'),
            
            # const Divider with AppColors
            (r'const Divider\(([^)]*color:\s*AppColors\.[^)]*)\)', r'Divider(\1)'),
            
            # const Card with AppColors
            (r'const Card\(([^}]*(?:AppColors|AppDimensions)[^}]*)\)', r'Card(\1)'),
            
            # const Material with AppColors
            (r'const Material\(([^}]*(?:AppColors|AppDimensions)[^}]*)\)', r'Material(\1)'),
            
            # const Decoration with AppColors/AppDimensions
            (r'const BoxDecoration\(([^}]*(?:AppColors|AppDimensions)[^}]*)\)', r'BoxDecoration(\1)'),
            
            # const Border with AppColors
            (r'const Border\.all\(color:\s*AppColors\.', r'Border.all(color: AppColors.'),
            
            # const CircularProgressIndicator with AppColors
            (r'const CircularProgressIndicator\(color:\s*AppColors\.', r'CircularProgressIndicator(color: AppColors.'),
            
            # const LinearProgressIndicator with AppColors
            (r'const LinearProgressIndicator\(([^)]*color:\s*AppColors\.[^)]*)\)', r'LinearProgressIndicator(\1)'),
        ]
        
        # Apply all patterns
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def fix_directory(directory):
    """Fix all Dart files in directory recursively"""
    fixed_count = 0
    
    for root, dirs, files in os.walk(directory):
        # Skip build and .dart_tool directories
        dirs[:] = [d for d in dirs if d not in ['.dart_tool', 'build', '.git']]
        
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                if fix_const_issues(file_path):
                    fixed_count += 1
    
    return fixed_count

def main():
    """Main function"""
    print("🔧 Fixing const issues with configurable constants...")
    print("=" * 50)
    
    # Directories to process
    directories = [
        'lib/apps',
        'lib/shared',
        'lib/core',
    ]
    
    total_fixed = 0
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n📁 Processing {directory}...")
            fixed = fix_directory(directory)
            total_fixed += fixed
            print(f"   Fixed {fixed} files")
        else:
            print(f"⚠️  Directory {directory} not found")
    
    print(f"\n✅ Completed! Fixed {total_fixed} files total")
    
    if total_fixed > 0:
        print("\n💡 Next steps:")
        print("   1. Run 'flutter analyze' to check for remaining issues")
        print("   2. Test the app to ensure everything works correctly")
        print("   3. Run './scripts/run_terminal.sh build' to test build")

if __name__ == "__main__":
    main()

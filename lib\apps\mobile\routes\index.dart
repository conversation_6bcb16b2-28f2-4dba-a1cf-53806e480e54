/// Mobile Routes Index - Export all route-related classes
///
/// This file exports all route configuration components for the mobile app
/// including route names, router configuration, navigation utilities, and services.
///
/// Migrated from c-faces project and adapted for multi-app architecture.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/mobile/routes/index.dart';
///
/// // Use in MaterialApp.router
/// MaterialApp.router(
///   routerConfig: MobileRouter.createRouter(),
/// )
///
/// // Use navigation extensions
/// context.goToLogin();
/// context.goToDashboard();
///
/// // Use navigation service
/// MobileNavigationService.goToProfile();
/// ```
library;

// ============================================================================
// ROUTE EXPORTS
// ============================================================================

/// Route name constants for mobile app
export 'mobile_route_names.dart';

/// Router configuration for mobile app using GoRouter
export 'mobile_router.dart';

/// Navigation extension methods for BuildContext
export 'mobile_navigation_extensions.dart';

/// Navigation service for programmatic navigation without context
export 'mobile_navigation_service.dart';

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:usb_serial/usb_serial.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// Configurable USB-TTL relay controller that supports various device command formats.
/// 
/// This controller supports customizable relay commands through device profiles:
/// - ESP32 format: R0:1, R0:0, R1:TOGGLE, R2:500, ALL:1, ALL:0
/// - Arduino format: REL_0_ON, REL_0_OFF, REL_ALL_ON, etc.
/// - Custom formats: Define your own command templates
/// 
/// Example usage:
/// ```dart
/// final controller = UsbTtlRelayController(
///   deviceId: 'relay-001',
///   relayCount: 4,
///   deviceProfile: DeviceProfile.esp32(),
/// );
/// 
/// await controller.connect(usbDevice);
/// await controller.controlRelay(0, RelayAction.on);
/// await controller.controlRelayTimed(2, 500); // ON for 500ms
/// ```
class UsbTtlRelayController extends RelayController {
  /// Number of relays available on the device
  final int relayCount;

  /// Baud rate for serial communication
  final int baudRate;

  /// Data bits for serial communication
  final int dataBits;

  /// Stop bits for serial communication
  final int stopBits;

  /// Parity for serial communication
  final int parity;

  /// Connection timeout in seconds
  final int timeoutSeconds;

  /// Device profile containing command templates
  final DeviceProfile deviceProfile;

  UsbPort? _port;
  UsbDevice? _connectedDevice;
  bool _isConnected = false;

  // Connection monitoring
  Timer? _connectionMonitorTimer;
  StreamController<bool>? _connectionStatusController;
  bool _isMonitoringConnection = false;
  int _consecutiveFailures = 0;
  static const int _maxConsecutiveFailures = 3;
  static const Duration _connectionCheckInterval = Duration(seconds: 2);

  /// Creates a new [UsbTtlRelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceName] is the device name/description.
  /// [relayCount] is the number of relays (default: 4).
  /// [baudRate] is the baud rate for serial communication (default: 115200).
  /// [deviceProfile] defines the command format for the target device.
  UsbTtlRelayController({
    required super.deviceId,
    super.deviceName = 'USB-TTL Relay Controller',
    this.relayCount = 4,
    this.baudRate = 115200,
    this.dataBits = 8,
    this.stopBits = 1,
    this.parity = 0,
    this.timeoutSeconds = 10,
    DeviceProfile? deviceProfile,
  }) : deviceProfile = deviceProfile ?? DeviceProfile.esp32();

  /// Connects to a USB device.
  ///
  /// [device] is the USB device to connect to.
  /// This method must be called before using any relay control methods.
  ///
  /// Throws [UsbRelayException] if connection fails.
  Future<void> connect(UsbDevice device) async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      _port = await device.create();
      if (_port == null) {
        throw const UsbRelayException('Failed to create USB port');
      }

      bool openResult = await _port!.open();
      if (!openResult) {
        throw const UsbRelayException('Failed to open USB port');
      }

      // Configure serial parameters
      await _port!.setDTR(true);
      await _port!.setRTS(true);
      await _port!.setPortParameters(
        baudRate,
        dataBits,
        stopBits,
        parity,
      );

      _connectedDevice = device;
      _isConnected = true;
      _consecutiveFailures = 0;

      // Initialize connection status controller if not exists
      _connectionStatusController ??= StreamController<bool>.broadcast();

      // Start connection monitoring
      _startConnectionMonitoring();

      // Notify connection established
      _connectionStatusController!.add(true);
    } catch (e) {
      _isConnected = false;
      _connectionStatusController?.add(false);
      throw UsbRelayException('Failed to connect to USB-TTL device', e);
    }
  }

  /// Controls a specific relay.
  ///
  /// [relayIndex] is the relay number (0 to relayCount-1).
  /// [action] is the action to perform.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlRelay(int relayIndex, RelayAction action) async {
    _validateConnection();
    _validateRelayIndex(relayIndex);

    String command;
    switch (action) {
      case RelayAction.on:
        command = deviceProfile.buildRelayOnCommand(relayIndex);
        break;
      case RelayAction.off:
        command = deviceProfile.buildRelayOffCommand(relayIndex);
        break;
      case RelayAction.toggle:
        if (!deviceProfile.supportsToggle) {
          throw const UsbRelayException('Toggle action not supported by this device profile');
        }
        command = deviceProfile.buildRelayToggleCommand(relayIndex);
        break;
    }

    await _sendCommand(command);
  }

  /// Controls a relay with timed operation.
  ///
  /// [relayIndex] is the relay number (0 to relayCount-1).
  /// [durationMs] is the duration in milliseconds to keep relay ON.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlRelayTimed(int relayIndex, int durationMs) async {
    _validateConnection();
    _validateRelayIndex(relayIndex);

    if (durationMs <= 0) {
      throw const UsbRelayException('Duration must be positive');
    }

    if (!deviceProfile.supportsTimedOperation) {
      throw const UsbRelayException('Timed operation not supported by this device profile');
    }

    final command = deviceProfile.buildRelayTimedCommand(relayIndex, durationMs);
    await _sendCommand(command);
  }

  /// Controls all relays at once.
  ///
  /// [action] is the action to perform on all relays.
  /// Note: TOGGLE action may not be supported by all device profiles.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> controlAllRelays(RelayAction action) async {
    _validateConnection();

    if (!deviceProfile.supportsAllRelaysControl) {
      throw const UsbRelayException('All relays control not supported by this device profile');
    }

    String command;
    switch (action) {
      case RelayAction.on:
        command = deviceProfile.buildAllRelaysOnCommand();
        break;
      case RelayAction.off:
        command = deviceProfile.buildAllRelaysOffCommand();
        break;
      case RelayAction.toggle:
        if (!deviceProfile.supportsToggle) {
          throw const UsbRelayException('Toggle action not supported by this device profile');
        }
        command = deviceProfile.buildAllRelaysToggleCommand();
        break;
    }

    await _sendCommand(command);
  }

  /// Sends a raw command to the device.
  ///
  /// [command] is the raw command string to send.
  ///
  /// Throws [UsbRelayException] if the operation fails.
  Future<void> sendRawCommand(String command) async {
    _validateConnection();
    await _sendCommand(command);
  }

  /// Gets relay configuration info.
  RelayConfiguration get configuration => RelayConfiguration(
    relayCount: relayCount,
    deviceId: deviceId,
    deviceName: deviceName,
    isConnected: _isConnected,
    deviceProfile: deviceProfile,
  );

  // Legacy interface compatibility
  @override
  Future<void> triggerOn() async {
    // Default to relay 0 for backward compatibility
    await controlRelay(0, RelayAction.on);
  }

  @override
  Future<void> triggerOff() async {
    // Default to relay 0 for backward compatibility
    await controlRelay(0, RelayAction.off);
  }

  /// Internal method to send commands to the device with enhanced error handling.
  Future<void> _sendCommand(String command) async {
    try {
      // Double-check connection before sending
      if (!_isConnected || _port == null) {
        throw const UsbRelayException('Device disconnected before sending command');
      }

      final fullCommand = command + deviceProfile.commandTerminator;
      final data = utf8.encode(fullCommand);

      // Send command with timeout
      await Future.any([
        _port!.write(Uint8List.fromList(data)),
        Future.delayed(const Duration(seconds: 5)).then((_) =>
            throw const UsbRelayException('Command send timeout')),
      ]);

      // Reset failure counter on successful send
      _consecutiveFailures = 0;

    } catch (e) {
      _consecutiveFailures++;

      // Check if this looks like a disconnection error
      if (e.toString().contains('disconnected') ||
          e.toString().contains('not connected') ||
          e.toString().contains('device not found') ||
          _consecutiveFailures >= _maxConsecutiveFailures) {

        // Handle disconnection
        await _handleDisconnection();
      }

      throw UsbRelayException('Failed to send command: $command', e);
    }
  }

  /// Validates that the controller is connected.
  void _validateConnection() {
    if (!_isConnected || _port == null) {
      throw const UsbRelayException('USB-TTL relay controller is not connected');
    }
  }

  /// Starts connection monitoring to detect USB disconnections
  void _startConnectionMonitoring() {
    if (_isMonitoringConnection) return;

    _isMonitoringConnection = true;
    _connectionMonitorTimer = Timer.periodic(_connectionCheckInterval, (_) {
      _checkConnectionHealth();
    });
  }

  /// Stops connection monitoring
  void _stopConnectionMonitoring() {
    _isMonitoringConnection = false;
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = null;
  }

  /// Checks if the USB connection is still healthy
  Future<void> _checkConnectionHealth() async {
    if (!_isConnected || _port == null) return;

    try {
      // Try to get available devices to check if our device is still connected
      final devices = await UsbSerial.listDevices();
      final isDeviceStillAvailable = devices.any((device) =>
          device.deviceId == _connectedDevice?.deviceId);

      if (!isDeviceStillAvailable) {
        // Device disconnected
        await _handleDisconnection();
        return;
      }

      // Reset failure counter on successful check
      _consecutiveFailures = 0;

    } catch (e) {
      _consecutiveFailures++;

      if (_consecutiveFailures >= _maxConsecutiveFailures) {
        // Too many failures, consider device disconnected
        await _handleDisconnection();
      }
    }
  }

  /// Handles USB device disconnection
  Future<void> _handleDisconnection() async {
    if (!_isConnected) return; // Already handled

    _isConnected = false;
    _consecutiveFailures = 0;

    // Clean up port
    try {
      await _port?.close();
    } catch (e) {
      // Ignore errors during cleanup
    }

    _port = null;
    _connectedDevice = null;

    // Stop monitoring
    _stopConnectionMonitoring();

    // Notify disconnection
    _connectionStatusController?.add(false);
  }

  /// Validates relay index.
  void _validateRelayIndex(int relayIndex) {
    if (relayIndex < 0 || relayIndex >= relayCount) {
      throw UsbRelayException('Invalid relay index: $relayIndex. Must be 0-${relayCount - 1}');
    }
  }

  /// Checks if the controller is connected.
  bool get isConnected => _isConnected;

  /// Gets the connected device info.
  UsbDevice? get connectedDevice => _connectedDevice;

  /// Stream for connection status updates (true = connected, false = disconnected)
  Stream<bool> get connectionStatus =>
      _connectionStatusController?.stream ?? const Stream.empty();

  /// Disconnects from the device and cleans up resources.
  Future<void> disconnect() async {
    _stopConnectionMonitoring();

    if (_port != null) {
      try {
        await _port!.close();
      } catch (e) {
        // Ignore errors during disconnect
      }
      _port = null;
    }

    _connectedDevice = null;
    _isConnected = false;
    _consecutiveFailures = 0;

    // Notify disconnection
    _connectionStatusController?.add(false);
  }

  @override
  Future<void> dispose() async {
    await disconnect();

    // Close connection status controller
    await _connectionStatusController?.close();
    _connectionStatusController = null;

    await super.dispose();
  }

  /// Gets available USB devices.
  static Future<List<UsbDevice>> getAvailableDevices() async {
    try {
      return await UsbSerial.listDevices();
    } catch (e) {
      throw UsbRelayException('Failed to get available USB devices', e);
    }
  }

  /// Checks if USB host mode is supported on the device.
  static Future<bool> isUsbHostSupported() async {
    try {
      final devices = await UsbSerial.listDevices();
      return devices.isNotEmpty || true; // Assume supported if we can list devices
    } catch (e) {
      return false;
    }
  }
}

/// Actions that can be performed on a relay.
enum RelayAction {
  /// Turn relay ON
  on,
  /// Turn relay OFF  
  off,
  /// Toggle relay state
  toggle,
}

/// Configuration information for a relay controller.
class RelayConfiguration {
  /// Number of relays
  final int relayCount;
  /// Device identifier
  final String deviceId;
  /// Device name
  final String deviceName;
  /// Connection status
  final bool isConnected;
  /// Device profile
  final DeviceProfile deviceProfile;

  const RelayConfiguration({
    required this.relayCount,
    required this.deviceId,
    required this.deviceName,
    required this.isConnected,
    required this.deviceProfile,
  });

  Map<String, dynamic> toJson() => {
    'relayCount': relayCount,
    'deviceId': deviceId,
    'deviceName': deviceName,
    'isConnected': isConnected,
    'deviceProfile': deviceProfile.toJson(),
  };

  @override
  String toString() => 'RelayConfiguration(relayCount: $relayCount, deviceId: $deviceId, isConnected: $isConnected, profile: ${deviceProfile.name})';
}

/// Device profile defining command templates for different relay controller types.
class DeviceProfile {
  /// Profile name
  final String name;
  
  /// Template for turning a relay ON. Use {relay} for relay index.
  final String relayOnTemplate;
  
  /// Template for turning a relay OFF. Use {relay} for relay index.
  final String relayOffTemplate;
  
  /// Template for toggling a relay. Use {relay} for relay index.
  final String relayToggleTemplate;
  
  /// Template for timed relay operation. Use {relay} for relay index and {duration} for milliseconds.
  final String relayTimedTemplate;
  
  /// Template for turning all relays ON.
  final String allRelaysOnTemplate;
  
  /// Template for turning all relays OFF.
  final String allRelaysOffTemplate;
  
  /// Template for toggling all relays.
  final String allRelaysToggleTemplate;
  
  /// Command terminator (e.g., \r\n, \n, or empty)
  final String commandTerminator;
  
  /// Whether this profile supports toggle commands
  final bool supportsToggle;
  
  /// Whether this profile supports timed operations
  final bool supportsTimedOperation;
  
  /// Whether this profile supports all relays control
  final bool supportsAllRelaysControl;

  const DeviceProfile({
    required this.name,
    required this.relayOnTemplate,
    required this.relayOffTemplate,
    required this.relayToggleTemplate,
    required this.relayTimedTemplate,
    required this.allRelaysOnTemplate,
    required this.allRelaysOffTemplate,
    required this.allRelaysToggleTemplate,
    this.commandTerminator = '',
    this.supportsToggle = true,
    this.supportsTimedOperation = true,
    this.supportsAllRelaysControl = true,
  });

  /// ESP32-style command profile (default)
  factory DeviceProfile.esp32() => const DeviceProfile(
    name: 'ESP32',
    relayOnTemplate: 'R{relay}:1',
    relayOffTemplate: 'R{relay}:0',
    relayToggleTemplate: 'R{relay}:TOGGLE',
    relayTimedTemplate: 'R{relay}:{duration}',
    allRelaysOnTemplate: 'ALL:1',
    allRelaysOffTemplate: 'ALL:0',
    allRelaysToggleTemplate: 'ALL:TOGGLE',
    commandTerminator: '\n',  // Add newline terminator for USB-TTL modules
  );

  /// Arduino-style command profile
  factory DeviceProfile.arduino() => const DeviceProfile(
    name: 'Arduino',
    relayOnTemplate: 'REL_{relay}_ON',
    relayOffTemplate: 'REL_{relay}_OFF',
    relayToggleTemplate: 'REL_{relay}_TOGGLE',
    relayTimedTemplate: 'REL_{relay}_TIMED_{duration}',
    allRelaysOnTemplate: 'ALL_RELAYS_ON',
    allRelaysOffTemplate: 'ALL_RELAYS_OFF',
    allRelaysToggleTemplate: 'ALL_RELAYS_TOGGLE',
    commandTerminator: '\n',
  );

  /// Simple numeric command profile
  factory DeviceProfile.simple() => const DeviceProfile(
    name: 'Simple',
    relayOnTemplate: '{relay}1',
    relayOffTemplate: '{relay}0',
    relayToggleTemplate: '{relay}T',
    relayTimedTemplate: '{relay}P{duration}',
    allRelaysOnTemplate: 'A1',
    allRelaysOffTemplate: 'A0',
    allRelaysToggleTemplate: 'AT',
    commandTerminator: '\r\n',
  );

  /// Custom profile with user-defined templates
  factory DeviceProfile.custom({
    required String name,
    required String relayOnTemplate,
    required String relayOffTemplate,
    String? relayToggleTemplate,
    String? relayTimedTemplate,
    String? allRelaysOnTemplate,
    String? allRelaysOffTemplate,
    String? allRelaysToggleTemplate,
    String commandTerminator = '',
  }) => DeviceProfile(
    name: name,
    relayOnTemplate: relayOnTemplate,
    relayOffTemplate: relayOffTemplate,
    relayToggleTemplate: relayToggleTemplate ?? '',
    relayTimedTemplate: relayTimedTemplate ?? '',
    allRelaysOnTemplate: allRelaysOnTemplate ?? '',
    allRelaysOffTemplate: allRelaysOffTemplate ?? '',
    allRelaysToggleTemplate: allRelaysToggleTemplate ?? '',
    commandTerminator: commandTerminator,
    supportsToggle: relayToggleTemplate != null,
    supportsTimedOperation: relayTimedTemplate != null,
    supportsAllRelaysControl: allRelaysOnTemplate != null && allRelaysOffTemplate != null,
  );

  /// Builds a relay ON command
  String buildRelayOnCommand(int relayIndex) => 
    relayOnTemplate.replaceAll('{relay}', relayIndex.toString());

  /// Builds a relay OFF command
  String buildRelayOffCommand(int relayIndex) => 
    relayOffTemplate.replaceAll('{relay}', relayIndex.toString());

  /// Builds a relay TOGGLE command
  String buildRelayToggleCommand(int relayIndex) => 
    relayToggleTemplate.replaceAll('{relay}', relayIndex.toString());

  /// Builds a relay timed command
  String buildRelayTimedCommand(int relayIndex, int durationMs) => 
    relayTimedTemplate
      .replaceAll('{relay}', relayIndex.toString())
      .replaceAll('{duration}', durationMs.toString());

  /// Builds an all relays ON command
  String buildAllRelaysOnCommand() => allRelaysOnTemplate;

  /// Builds an all relays OFF command
  String buildAllRelaysOffCommand() => allRelaysOffTemplate;

  /// Builds an all relays TOGGLE command
  String buildAllRelaysToggleCommand() => allRelaysToggleTemplate;

  Map<String, dynamic> toJson() => {
    'name': name,
    'relayOnTemplate': relayOnTemplate,
    'relayOffTemplate': relayOffTemplate,
    'relayToggleTemplate': relayToggleTemplate,
    'relayTimedTemplate': relayTimedTemplate,
    'allRelaysOnTemplate': allRelaysOnTemplate,
    'allRelaysOffTemplate': allRelaysOffTemplate,
    'allRelaysToggleTemplate': allRelaysToggleTemplate,
    'commandTerminator': commandTerminator,
    'supportsToggle': supportsToggle,
    'supportsTimedOperation': supportsTimedOperation,
    'supportsAllRelaysControl': supportsAllRelaysControl,
  };

  @override
  String toString() => 'DeviceProfile($name)';
}

/// Auto-connecting USB-TTL relay controller that automatically connects to the first available device.
/// 
/// Example usage:
/// ```dart
/// final controller = AutoConnectUsbTtlRelayController(
///   deviceId: 'auto-relay-001',
///   relayCount: 4,
///   deviceProfile: DeviceProfile.esp32(),
/// );
/// 
/// await controller.initialize();
/// await controller.controlRelay(0, RelayAction.on);
/// await controller.dispose();
/// ```
class AutoConnectUsbTtlRelayController extends UsbTtlRelayController {
  /// Creates an auto-connecting USB-TTL relay controller.
  AutoConnectUsbTtlRelayController({
    required super.deviceId,
    super.deviceName = 'Auto USB-TTL Relay',
    super.relayCount = 4,
    super.baudRate = 115200,
    super.timeoutSeconds = 10,
    super.deviceProfile,
  });

  /// Initializes the controller by automatically connecting to the first available device.
  ///
  /// Throws [UsbRelayException] if no devices are available or connection fails.
  Future<void> initialize() async {
    final devices = await UsbTtlRelayController.getAvailableDevices();
    if (devices.isEmpty) {
      throw const UsbRelayException('No USB devices available for relay controller');
    }

    await connect(devices.first);
  }
} 
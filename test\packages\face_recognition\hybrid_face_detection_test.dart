import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:camera/camera.dart';

import '../../../lib/packages/face_recognition/face_recognition.dart';
import '../../../lib/shared/providers/hybrid_face_detection_provider.dart';

// Mock classes
class MockCameraImage extends Mock implements CameraImage {}
class MockCameraDescription extends Mock implements CameraDescription {}

void main() {
  group('HybridFaceDetectionProvider Tests', () {
    late HybridFaceDetectionProvider provider;
    
    setUp(() {
      provider = HybridFaceDetectionProvider();
    });
    
    tearDown(() async {
      await provider.dispose();
    });
    
    group('Initialization', () {
      test('should initialize successfully', () async {
        await provider.initialize(
          deviceType: TerminalDeviceType.generic,
        );
        
        expect(provider.isDisposed, false);
        expect(provider.faces, isEmpty);
        expect(provider.bestFace, isNull);
      });
      
      test('should handle initialization failure gracefully', () async {
        // Test with invalid configuration
        expect(
          () => provider.initialize(
            deviceType: TerminalDeviceType.telpoF8,
            serverEndpoint: 'invalid-url',
          ),
          throwsA(isA<Exception>()),
        );
      });
      
      test('should not initialize twice', () async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
        
        // Second initialization should not throw
        await provider.initialize(deviceType: TerminalDeviceType.generic);
        
        expect(provider.isDisposed, false);
      });
    });
    
    group('Face Detection', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should process camera image without errors', () async {
        final mockImage = MockCameraImage();
        final mockCamera = MockCameraDescription();
        
        // Mock camera image properties
        when(mockImage.width).thenReturn(640);
        when(mockImage.height).thenReturn(480);
        when(mockImage.format).thenReturn(ImageFormat(ImageFormatGroup.yuv420, raw: 0));
        when(mockImage.planes).thenReturn([
          CameraImagePlane(bytes: Uint8List(640 * 480), bytesPerRow: 640, bytesPerPixel: 1),
          CameraImagePlane(bytes: Uint8List(320 * 240), bytesPerRow: 320, bytesPerPixel: 2),
          CameraImagePlane(bytes: Uint8List(320 * 240), bytesPerRow: 320, bytesPerPixel: 2),
        ]);
        
        // Should not throw
        await provider.processImage(mockImage, mockCamera);
        
        expect(provider.isDetecting, false);
      });
      
      test('should handle detection errors gracefully', () async {
        final mockImage = MockCameraImage();
        final mockCamera = MockCameraDescription();
        
        // Mock invalid image
        when(mockImage.width).thenReturn(0);
        when(mockImage.height).thenReturn(0);
        
        // Should not throw, but handle error internally
        await provider.processImage(mockImage, mockCamera);
        
        expect(provider.isDetecting, false);
      });
      
      test('should skip frames for performance', () async {
        final mockImage = MockCameraImage();
        final mockCamera = MockCameraDescription();
        
        when(mockImage.width).thenReturn(640);
        when(mockImage.height).thenReturn(480);
        when(mockImage.format).thenReturn(ImageFormat(ImageFormatGroup.yuv420, raw: 0));
        when(mockImage.planes).thenReturn([
          CameraImagePlane(bytes: Uint8List(640 * 480), bytesPerRow: 640, bytesPerPixel: 1),
          CameraImagePlane(bytes: Uint8List(320 * 240), bytesPerRow: 320, bytesPerPixel: 2),
          CameraImagePlane(bytes: Uint8List(320 * 240), bytesPerRow: 320, bytesPerPixel: 2),
        ]);
        
        // Process multiple frames rapidly
        for (int i = 0; i < 5; i++) {
          await provider.processImage(mockImage, mockCamera);
        }
        
        // Should handle frame skipping without issues
        expect(provider.isDetecting, false);
      });
    });
    
    group('Performance Monitoring', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should track performance metrics', () {
        provider.setPerformanceMonitoring(true);
        
        final metrics = provider.getPerformanceMetrics();
        
        expect(metrics, isA<Map<String, dynamic>>());
        expect(metrics.containsKey('fps'), true);
        expect(metrics.containsKey('total_processed'), true);
        expect(metrics.containsKey('total_recognized'), true);
        expect(metrics.containsKey('recognition_rate'), true);
        expect(metrics.containsKey('is_online'), true);
      });
      
      test('should disable performance monitoring', () {
        provider.setPerformanceMonitoring(false);
        
        // Should still return metrics but not actively monitor
        final metrics = provider.getPerformanceMetrics();
        expect(metrics, isA<Map<String, dynamic>>());
      });
    });
    
    group('Recognition', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should handle recognition results', () async {
        // Initially no recognition result
        expect(provider.lastRecognitionResult, isNull);
        expect(provider.isRecognizing, false);
        
        // Trigger recognition
        await provider.triggerRecognition();
        
        // Should handle gracefully even without face
        expect(provider.isRecognizing, false);
      });
      
      test('should throttle recognition requests', () async {
        // Multiple rapid recognition triggers
        await provider.triggerRecognition();
        await provider.triggerRecognition();
        await provider.triggerRecognition();
        
        // Should handle throttling without errors
        expect(provider.isRecognizing, false);
      });
    });
    
    group('Network Status', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should track online status', () {
        // Should have initial online status
        expect(provider.isOnline, isA<bool>());
      });
      
      test('should force network check', () async {
        // Should not throw
        await provider.forceNetworkCheck();
      });
    });
    
    group('Hardware Control', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should test hardware devices', () async {
        // Should not throw for generic device
        await provider.testHardwareDevices();
      });
    });
    
    group('Error Recovery', () {
      setUp(() async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
      });
      
      test('should recover from consecutive errors', () async {
        final mockImage = MockCameraImage();
        final mockCamera = MockCameraDescription();
        
        // Mock invalid images to trigger errors
        when(mockImage.width).thenThrow(Exception('Mock error'));
        
        // Process multiple invalid images
        for (int i = 0; i < 10; i++) {
          await provider.processImage(mockImage, mockCamera);
        }
        
        // Should still be functional after error recovery
        expect(provider.isDisposed, false);
      });
    });
    
    group('Disposal', () {
      test('should dispose cleanly', () async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
        
        expect(provider.isDisposed, false);
        
        await provider.dispose();
        
        expect(provider.isDisposed, true);
        expect(provider.faces, isEmpty);
        expect(provider.bestFace, isNull);
        expect(provider.lastRecognitionResult, isNull);
      });
      
      test('should handle multiple dispose calls', () async {
        await provider.initialize(deviceType: TerminalDeviceType.generic);
        
        await provider.dispose();
        await provider.dispose(); // Second dispose should not throw
        
        expect(provider.isDisposed, true);
      });
    });
  });
  
  group('FaceRecognitionConfig Tests', () {
    test('should create terminal configuration', () {
      final config = FaceRecognitionConfig.forTerminal(
        deviceType: TerminalDeviceType.telpoF8,
        performanceProfile: PerformanceProfile.maxPerformance,
      );
      
      expect(config.platformType, PlatformType.terminal);
      expect(config.detectionEngine, DetectionEngine.ultraface);
      expect(config.performanceProfile, PerformanceProfile.maxPerformance);
      expect(config.targetFPS, 45);
      expect(config.requireFrontalFace, true);
    });
    
    test('should create mobile configuration', () {
      final config = FaceRecognitionConfig.forMobile(
        deviceType: MobileDeviceType.android,
        performanceProfile: PerformanceProfile.balanced,
      );
      
      expect(config.platformType, PlatformType.mobile);
      expect(config.detectionEngine, DetectionEngine.mediapipe);
      expect(config.performanceProfile, PerformanceProfile.balanced);
      expect(config.targetFPS, 30);
      expect(config.batteryOptimization, true);
    });
    
    test('should copy configuration with changes', () {
      final originalConfig = FaceRecognitionConfig.forTerminal();
      
      final modifiedConfig = originalConfig.copyWith(
        targetFPS: 60,
        minConfidence: 0.8,
      );
      
      expect(modifiedConfig.targetFPS, 60);
      expect(modifiedConfig.minConfidence, 0.8);
      expect(modifiedConfig.detectionEngine, originalConfig.detectionEngine);
    });
  });
  
  group('Performance Benchmarks', () {
    test('should benchmark detection engines', () {
      final ultraFaceCapabilities = DetectionEngineFactory.getCapabilities(DetectionEngine.ultraface);
      final mediaPipeCapabilities = DetectionEngineFactory.getCapabilities(DetectionEngine.mediapipe);
      final mlKitCapabilities = DetectionEngineFactory.getCapabilities(DetectionEngine.mlKit);
      
      expect(ultraFaceCapabilities.averageFPS, greaterThan(40));
      expect(mediaPipeCapabilities.accuracy, greaterThan(0.95));
      expect(mlKitCapabilities.modelSize, equals(0.0)); // Dynamic download
    });
    
    test('should recommend best engine for platform', () {
      final terminalEngine = DetectionEngineFactory.getRecommendedEngine(PlatformType.terminal);
      final mobileEngine = DetectionEngineFactory.getRecommendedEngine(PlatformType.mobile);
      
      expect(terminalEngine, DetectionEngine.ultraface);
      expect(mobileEngine, DetectionEngine.mediapipe);
    });
    
    test('should rank engines by criteria', () {
      final performanceRanking = EngineComparison.rankEngines(
        criteria: ComparisonCriteria.performance,
        platform: PlatformType.terminal,
      );
      
      expect(performanceRanking.first, DetectionEngine.ultraface);
      
      final accuracyRanking = EngineComparison.rankEngines(
        criteria: ComparisonCriteria.accuracy,
        platform: PlatformType.mobile,
      );
      
      expect(accuracyRanking.first, DetectionEngine.mediapipe);
    });
  });
}

{"name": "relay-controller-test-server", "version": "1.0.0", "description": "Test server for relay controller library", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "express": "^4.18.2", "faker": "^5.5.3", "jsonwebtoken": "^9.0.2", "mqtt": "^5.0.0", "socket.io": "^4.7.2", "uuid": "^9.0.0", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["relay", "controller", "test", "server"], "author": "Relay Controller", "license": "MIT"}
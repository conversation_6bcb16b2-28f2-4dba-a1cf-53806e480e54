import '../../../../core/base/base_data_source.dart';
import '../../../../core/storage/secure_storage_service.dart';
import '../../../../core/config/app_config.dart';
import '../../../models/auth/auth_result_model.dart';
import '../../../models/user/user_model.dart';
import 'auth_local_data_source.dart';

/// Implementation of AuthLocalDataSource using BaseLocalDataSource
/// 
/// Provides secure local storage for authentication data with enhanced features
class AuthLocalDataSourceImpl extends BaseLocalDataSource 
    with SecureStorageMixin 
    implements AuthLocalDataSource {
  
  final SecureStorageService _secureStorage;
  final AppConfig _appConfig = AppConfig();

  AuthLocalDataSourceImpl({
    required SecureStorageService secureStorage,
  }) : _secureStorage = secureStorage;

  @override
  SecureStorageService get storageService => _secureStorage;

  SecureStorageService get secureStorage => _secureStorage;

  // Storage keys
  static const String _authResultKey = 'auth_result';
  static const String _userDataKey = 'user_data';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _sessionDataKey = 'session_data';
  static const String _loginTimestampKey = 'login_timestamp';

  @override
  Future<void> saveAuthResult(AuthResultModel authResult) async {
    return await executeSyncStorageOperation(() async {
      // Save auth result
      await _secureStorage.writeObject(_authResultKey, authResult.toJson());
      
      // Save individual tokens for quick access
      await _secureStorage.write(_accessTokenKey, authResult.accessToken);
      if (authResult.refreshToken != null) {
        await _secureStorage.write(_refreshTokenKey, authResult.refreshToken!);
      }
      
      // Save user data
      if (authResult.user != null) {
        await _secureStorage.writeObject(_userDataKey, authResult.user!.toJson());
      }
      
      // Save login timestamp
      await _secureStorage.write(_loginTimestampKey, DateTime.now().toIso8601String());
    });
  }

  @override
  Future<AuthResultModel?> getAuthResult() async {
    return await executeSyncStorageOperation(() async {
      final authData = await _secureStorage.readObject(_authResultKey);
      if (authData == null) return null;
      
      return AuthResultModel.fromJson(authData);
    });
  }

  @override
  Future<void> saveUser(UserModel user) async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.writeObject(_userDataKey, user.toJson());
    });
  }

  @override
  Future<UserModel?> getUser() async {
    return await executeSyncStorageOperation(() async {
      final userData = await _secureStorage.readObject(_userDataKey);
      if (userData == null) return null;
      
      return UserModel.fromJson(userData);
    });
  }

  @override
  Future<void> saveAccessToken(String token) async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.write(_accessTokenKey, token);
    });
  }

  @override
  Future<String?> getAccessToken() async {
    return await executeSyncStorageOperation(() async {
      return await _secureStorage.read(_accessTokenKey);
    });
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.write(_refreshTokenKey, token);
    });
  }

  @override
  Future<String?> getRefreshToken() async {
    return await executeSyncStorageOperation(() async {
      return await _secureStorage.read(_refreshTokenKey);
    });
  }

  @override
  Future<bool> isAuthenticated() async {
    return await executeSyncStorageOperation(() async {
      final accessToken = await getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        return false;
      }
      
      // Check if session is expired
      final isExpired = await isSessionExpired();
      return !isExpired;
    });
  }

  @override
  Future<void> clearAuthData() async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.delete(_authResultKey);
      await _secureStorage.delete(_userDataKey);
      await _secureStorage.delete(_accessTokenKey);
      await _secureStorage.delete(_refreshTokenKey);
      await _secureStorage.delete(_sessionDataKey);
      await _secureStorage.delete(_loginTimestampKey);
    });
  }

  @override
  Future<void> saveSessionData(Map<String, dynamic> sessionData) async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.writeObject(_sessionDataKey, sessionData);
    });
  }

  @override
  Future<Map<String, dynamic>?> getSessionData() async {
    return await executeSyncStorageOperation(() async {
      return await _secureStorage.readObject(_sessionDataKey);
    });
  }

  @override
  Future<void> saveLoginTimestamp(DateTime timestamp) async {
    return await executeSyncStorageOperation(() async {
      await _secureStorage.write(_loginTimestampKey, timestamp.toIso8601String());
    });
  }

  @override
  Future<DateTime?> getLoginTimestamp() async {
    return await executeSyncStorageOperation(() async {
      final timestampStr = await _secureStorage.read(_loginTimestampKey);
      if (timestampStr == null) return null;
      
      return DateTime.parse(timestampStr);
    });
  }

  @override
  Future<bool> isSessionExpired() async {
    return await executeSyncStorageOperation(() async {
      final loginTimestamp = await getLoginTimestamp();
      if (loginTimestamp == null) return true;
      
      final sessionTimeout = _appConfig.sessionTimeout;
      final now = DateTime.now();
      
      return now.difference(loginTimestamp) > sessionTimeout;
    });
  }

  // ============================================================================
  // ENHANCED METHODS
  // ============================================================================

  /// Get authentication status with detailed information
  Future<AuthStatus> getAuthStatus() async {
    return await executeSyncStorageOperation(() async {
      final accessToken = await getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        return AuthStatus.unauthenticated;
      }
      
      final isExpired = await isSessionExpired();
      if (isExpired) {
        return AuthStatus.expired;
      }
      
      final user = await getUser();
      if (user == null) {
        return AuthStatus.incomplete;
      }
      
      return AuthStatus.authenticated;
    });
  }

  /// Clean expired data
  Future<void> cleanExpiredData() async {
    return await executeSyncStorageOperation(() async {
      final isExpired = await isSessionExpired();
      if (isExpired) {
        await clearAuthData();
      }
    });
  }

  /// Get storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    return await executeSyncStorageOperation(() async {
      final keys = await _secureStorage.getAllKeys();
      final authKeys = keys.where((key) => key.startsWith('auth_') || 
                                          key.startsWith('user_') ||
                                          key.startsWith('access_') ||
                                          key.startsWith('refresh_') ||
                                          key.startsWith('session_') ||
                                          key.startsWith('login_')).toList();
      
      return {
        'total_keys': authKeys.length,
        'has_access_token': await _secureStorage.containsKey(_accessTokenKey),
        'has_refresh_token': await _secureStorage.containsKey(_refreshTokenKey),
        'has_user_data': await _secureStorage.containsKey(_userDataKey),
        'has_auth_result': await _secureStorage.containsKey(_authResultKey),
        'login_timestamp': await getLoginTimestamp(),
        'is_expired': await isSessionExpired(),
      };
    });
  }

  /// Backup authentication data
  Future<Map<String, dynamic>?> backupAuthData() async {
    return await executeSyncStorageOperation(() async {
      final authResult = await getAuthResult();
      if (authResult == null) return null;
      
      return {
        'auth_result': authResult.toJson(),
        'backup_timestamp': DateTime.now().toIso8601String(),
        'app_version': _appConfig.environment.name,
      };
    });
  }

  /// Restore authentication data from backup
  Future<void> restoreAuthData(Map<String, dynamic> backupData) async {
    return await executeSyncStorageOperation(() async {
      if (backupData['auth_result'] != null) {
        final authResult = AuthResultModel.fromJson(backupData['auth_result']);
        await saveAuthResult(authResult);
      }
    });
  }
}

/// Authentication status enumeration
enum AuthStatus {
  authenticated,
  unauthenticated,
  expired,
  incomplete,
}

/// Extension for AuthStatus
extension AuthStatusExtension on AuthStatus {
  String get displayName {
    switch (this) {
      case AuthStatus.authenticated:
        return 'Authenticated';
      case AuthStatus.unauthenticated:
        return 'Not Authenticated';
      case AuthStatus.expired:
        return 'Session Expired';
      case AuthStatus.incomplete:
        return 'Incomplete Authentication';
    }
  }
  
  bool get isValid => this == AuthStatus.authenticated;
  bool get needsLogin => this == AuthStatus.unauthenticated || this == AuthStatus.expired;
  bool get needsRefresh => this == AuthStatus.expired;
}

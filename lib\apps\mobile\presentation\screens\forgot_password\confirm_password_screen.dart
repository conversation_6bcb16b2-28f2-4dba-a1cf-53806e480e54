import 'package:flutter/material.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/components/app_button.dart';
import '../../../../../shared/components/app_input_field.dart';
import '../../../../../shared/components/app_logo.dart';

/// Màn hình xác nhận mật khẩu mới
class ConfirmPasswordScreen extends StatefulWidget {
  const ConfirmPasswordScreen({super.key});

  @override
  State<ConfirmPasswordScreen> createState() => _ConfirmPasswordScreenState();
}

class _ConfirmPasswordScreenState extends State<ConfirmPasswordScreen> {
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  // Password validation states
  bool _hasDigit = false;
  bool _hasUppercase = false;
  bool _hasMinLength = false;
  bool _passwordsMatch = false;

  @override
  void initState() {
    super.initState();
    // Initialize validation on empty fields
    _validatePassword('');
    _validateConfirmPassword('');
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  bool get _isFormValid {
    return _hasDigit && _hasUppercase && _hasMinLength && _passwordsMatch;
  }

  void _validatePassword(String password) {
    setState(() {
      _hasDigit = password.contains(RegExp(r'[0-9]'));
      _hasUppercase = password.contains(RegExp(r'[A-Z]'));
      _hasMinLength = password.length >= 8;
      _passwordsMatch =
          password.isNotEmpty &&
          _confirmPasswordController.text.isNotEmpty &&
          password == _confirmPasswordController.text;
    });
  }

  void _validateConfirmPassword(String confirmPassword) {
    setState(() {
      _passwordsMatch =
          confirmPassword.isNotEmpty &&
          _passwordController.text.isNotEmpty &&
          confirmPassword == _passwordController.text;
    });
  }

  void _handleConfirmPassword() {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          // Navigate to success screen
          Navigator.of(context).pushNamed('/successfully');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLogoSection(),
                SizedBox(height: AppDimensions.spacing24),
                _buildTitleSection(),
                SizedBox(height: AppDimensions.spacing24),
                _buildForm(),
                SizedBox(height: AppDimensions.spacing24),
                _buildConfirmButton(),
                SizedBox(height: AppDimensions.spacing16),
                _buildBackLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Row(children: [const AppLogo()]);
  }

  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Đặt lại mật khẩu',
          style: AppTextStyles.heading3.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        SizedBox(height: AppDimensions.spacing4),
        Text(
          'Nhập thông tin mật khẩu mới của bạn',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Password field
          Row(
            children: [
              Text(
                'Mật khẩu',
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                ),
              ),
              Text(
                '*',
                style: AppTextStyles.bodySmall.copyWith(color: AppColors.error),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacing4),
          AppInputField(
            controller: _passwordController,
            placeholder: 'Nhập mật khẩu của bạn',
            isPassword: true,
            validator: (value) {
              if (value?.trim().isEmpty ?? true) {
                return 'Vui lòng nhập mật khẩu';
              }
              if (!_hasDigit) {
                return 'Mật khẩu phải có ít nhất 1 chữ số';
              }
              if (!_hasUppercase) {
                return 'Mật khẩu phải có ít nhất 1 chữ viết hoa';
              }
              if (!_hasMinLength) {
                return 'Mật khẩu phải có ít nhất 8 ký tự';
              }
              return null;
            },
            onChanged: (value) {
              _validatePassword(value);
            },
          ),
          SizedBox(height: AppDimensions.spacing16),

          // Password requirements
          _buildPasswordRequirements(),
          SizedBox(height: AppDimensions.spacing16),

          // Confirm password field
          Row(
            children: [
              Text(
                'Xác nhận mật khẩu',
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                ),
              ),
              Text(
                '*',
                style: AppTextStyles.bodySmall.copyWith(color: AppColors.error),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacing4),
          AppInputField(
            controller: _confirmPasswordController,
            placeholder: 'Nhập xác nhận mật khẩu của bạn',
            isPassword: true,
            validator: (value) {
              if (value?.trim().isEmpty ?? true) {
                return 'Vui lòng xác nhận mật khẩu';
              }
              if (!_passwordsMatch) {
                return 'Mật khẩu xác nhận không khớp';
              }
              return null;
            },
            onChanged: (value) {
              _validateConfirmPassword(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordRequirements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lưu ý mật khẩu cần có ít nhất:',
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        _buildRequirementItem('1 chữ số (0-9)', _hasDigit),
        SizedBox(height: AppDimensions.spacing4),
        _buildRequirementItem('1 chữ viết hoa (A-Z)', _hasUppercase),
        SizedBox(height: AppDimensions.spacing4),
        _buildRequirementItem('Ít nhất 8 ký tự', _hasMinLength),
      ],
    );
  }

  Widget _buildRequirementItem(String text, bool isValid) {
    return Row(
      children: [
        Icon(
          isValid ? Icons.check : Icons.close,
          size: 12,
          color: isValid ? const Color(0xFF40BF24) : AppColors.textSecondary,
        ),
        SizedBox(width: AppDimensions.spacing4),
        Text(
          text,
          style: AppTextStyles.caption.copyWith(
            color: isValid ? const Color(0xFF40BF24) : AppColors.textSecondary,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildConfirmButton() {
    return AppButton(
      text: 'Đổi mật khẩu',
      onPressed: (_isLoading || !_isFormValid) ? null : _handleConfirmPassword,
      isLoading: _isLoading,
    );
  }

  Widget _buildBackLink() {
    return Center(
      child: GestureDetector(
        onTap: () => Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/login', (route) => false),
        child: Text(
          '< Trở lại đăng nhập',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

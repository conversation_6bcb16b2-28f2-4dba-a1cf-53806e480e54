import 'package:flutter/material.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../providers/enhanced_face_capture_provider.dart';
import '../models/enhanced_face_capture_result.dart';
import '../services/face_cropping_api_service.dart';
import '../services/face_cropping_test_service.dart';
import '../core/constants/face_cropping_constants.dart';
import '../widgets/face_cropping_demo_widget.dart';

/// Example showing how to integrate face cropping functionality
/// into existing face capture workflow
class FaceCroppingIntegrationExample {
  
  /// Example 1: Basic face cropping integration
  /// 
  /// This shows how to add face cropping to an existing face capture result
  static Future<EnhancedFaceCaptureResult> basicIntegrationExample({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
  }) async {
    // Create enhanced face capture provider
    final provider = EnhancedFaceCaptureProvider();
    
    try {
      // Configure processing mode
      provider.setProcessingMode(ProcessingMode.synchronous);
      
      // Set side effects
      provider.setEnabledSideEffects([
        SideEffectType.auditLog,
        SideEffectType.databaseUpdate,
      ]);
      
      // Add context information
      provider.setContext({
        'user_id': 'example_user_123',
        'session_id': 'session_456',
        'integration_example': true,
      });
      
      // Process with cropping
      final result = await provider.processCapture(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
      );
      
      return result;
      
    } finally {
      provider.dispose();
    }
  }
  
  /// Example 2: Asynchronous processing with progress tracking
  static Future<EnhancedFaceCaptureResult> asyncProcessingExample({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
    required Function(double progress, String status) onProgress,
  }) async {
    final provider = EnhancedFaceCaptureProvider();
    
    try {
      // Configure for async processing
      provider.setProcessingMode(ProcessingMode.asynchronous);
      provider.setCropPadding(0.25); // 25% padding
      provider.setOutputQuality(90); // High quality
      
      // Listen to provider changes for progress updates
      provider.addListener(() {
        if (provider.isProcessing) {
          onProgress(provider.processingProgress, provider.processingStatus ?? 'Processing...');
        }
      });
      
      // Process asynchronously
      final result = await provider.processAsynchronous(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
        context: {
          'processing_type': 'async_example',
          'high_quality': true,
        },
      );
      
      return result;
      
    } finally {
      provider.dispose();
    }
  }
  
  /// Example 3: Queue-based batch processing
  static Future<EnhancedFaceCaptureResult> queueProcessingExample({
    required List<Map<FaceDirection, String?>> batchCapturedImages,
    required List<Map<FaceDirection, Face>> batchDetectedFaces,
  }) async {
    final provider = EnhancedFaceCaptureProvider();
    final results = <EnhancedFaceCaptureResult>[];
    
    try {
      // Configure for queue processing
      provider.setProcessingMode(ProcessingMode.queueBased);
      provider.setEnabledSideEffects([
        SideEffectType.auditLog,
        SideEffectType.databaseUpdate,
        SideEffectType.pushNotification,
      ]);
      
      // Process each batch item
      for (int i = 0; i < batchCapturedImages.length; i++) {
        final capturedImages = batchCapturedImages[i];
        final detectedFaces = batchDetectedFaces[i];
        
        final result = await provider.processQueueBased(
          capturedImages: capturedImages,
          detectedFaces: detectedFaces,
          context: {
            'batch_index': i,
            'batch_size': batchCapturedImages.length,
            'processing_type': 'batch_queue',
          },
        );
        
        results.add(result);
      }
      
      // Return the last result (or you could combine them)
      return results.isNotEmpty ? results.last : EnhancedFaceCaptureResult.failure(error: 'No results');
      
    } finally {
      provider.dispose();
    }
  }
  
  /// Example 4: Direct API service usage
  static Future<void> directApiServiceExample({
    required String imagePath,
    required Face detectedFace,
  }) async {
    final apiService = FaceCroppingApiService();
    
    // Example 1: Synchronous processing
    final syncResult = await apiService.processFaceCroppingSynchronous(
      imagePath: imagePath,
      face: detectedFace,
      context: {
        'api_example': 'synchronous',
        'user_id': 'direct_api_user',
      },
      enabledSideEffects: [SideEffectType.auditLog],
    );
    
    print('Sync result: ${syncResult.success}');
    
    // Example 2: Asynchronous processing
    final operationId = await apiService.processFaceCroppingAsynchronous(
      imagePath: imagePath,
      face: detectedFace,
      onProgress: (progress, status) {
        print('Progress: ${(progress * 100).toInt()}% - $status');
      },
      onComplete: (result) {
        print('Async completed: ${result.success}');
      },
      onError: (error) {
        print('Async error: $error');
      },
      context: {
        'api_example': 'asynchronous',
        'user_id': 'direct_api_user',
      },
    );
    
    print('Async operation ID: $operationId');
    
    // Example 3: Queue-based processing
    final queueId = await apiService.addToFaceCroppingQueue(
      imagePath: imagePath,
      face: detectedFace,
      priority: 5,
      context: {
        'api_example': 'queue_based',
        'user_id': 'direct_api_user',
      },
    );
    
    print('Queue ID: $queueId');
    
    // Check queue status
    final queueStatus = apiService.getQueueStatus();
    print('Queue status: $queueStatus');
  }
  
  /// Example 5: Testing integration
  static Future<void> testingExample() async {
    print('Running face cropping tests...');
    
    final testResults = await FaceCroppingTestService.runAllTests();
    
    print('Test Results:');
    print('Overall Success: ${testResults.overallSuccess}');
    
    if (testResults.error != null) {
      print('Error: ${testResults.error}');
    }
    
    print('Individual Test Results:');
    print('- Basic Cropping: ${testResults.basicCroppingTest.success}');
    print('- Sync API: ${testResults.syncApiTest.success}');
    print('- Async API: ${testResults.asyncApiTest.success}');
    print('- Queue API: ${testResults.queueApiTest.success}');
    print('- Enhanced Service: ${testResults.enhancedServiceTest.success}');
    print('- Side Effects: ${testResults.sideEffectsTest.success}');
    print('- Configuration: ${testResults.configurationTest.success}');
  }
  
  /// Example 6: Widget integration
  static Widget createDemoWidget({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
  }) {
    return FaceCroppingDemoWidget(
      capturedImages: capturedImages,
      detectedFaces: detectedFaces,
    );
  }
  
  /// Example 7: Configuration examples
  static void configurationExamples() {
    print('Face Cropping Configuration Examples:');
    
    // Default configuration
    print('Default padding: ${FaceCroppingConstants.defaultPadding}');
    print('Default quality: ${FaceCroppingConstants.defaultOutputQuality}');
    print('Max queue size: ${FaceCroppingConstants.maxQueueSize}');
    
    // Processing modes
    print('Available processing modes:');
    for (final mode in ProcessingMode.values) {
      print('- ${mode.name}');
    }
    
    // Side effects
    print('Available side effects:');
    for (final effect in SideEffectType.values) {
      print('- ${effect.name}');
    }
    
    // Timeouts
    print('API timeouts:');
    print('- Synchronous: ${FaceCroppingConstants.synchronousTimeout}');
    print('- Asynchronous: ${FaceCroppingConstants.asynchronousTimeout}');
    print('- Queue-based: ${FaceCroppingConstants.queueBasedTimeout}');
  }
  
  /// Example 8: Error handling
  static Future<void> errorHandlingExample({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
  }) async {
    final provider = EnhancedFaceCaptureProvider();
    
    try {
      // Configure with invalid settings to test error handling
      provider.setCropPadding(-0.1); // Invalid padding
      provider.setOutputQuality(150); // Invalid quality
      
      final result = await provider.processCapture(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
      );
      
      if (!result.success) {
        print('Processing failed as expected: ${result.error}');
        
        // Handle the error appropriately
        _handleProcessingError(result.error);
      }
      
    } catch (e) {
      print('Exception caught: $e');
      _handleProcessingException(e);
    } finally {
      provider.dispose();
    }
  }
  
  static void _handleProcessingError(String? error) {
    // Implement error handling logic
    print('Handling processing error: $error');
  }
  
  static void _handleProcessingException(dynamic exception) {
    // Implement exception handling logic
    print('Handling processing exception: $exception');
  }
  
  /// Example 9: Performance monitoring
  static Future<void> performanceMonitoringExample({
    required Map<FaceDirection, String?> capturedImages,
    required Map<FaceDirection, Face> detectedFaces,
  }) async {
    final provider = EnhancedFaceCaptureProvider();
    final stopwatch = Stopwatch()..start();
    
    try {
      // Monitor processing time
      provider.addListener(() {
        if (provider.isProcessing) {
          final elapsed = stopwatch.elapsedMilliseconds;
          print('Processing time: ${elapsed}ms, Progress: ${(provider.processingProgress * 100).toInt()}%');
        }
      });
      
      final result = await provider.processCapture(
        capturedImages: capturedImages,
        detectedFaces: detectedFaces,
      );
      
      stopwatch.stop();
      
      print('Total processing time: ${stopwatch.elapsedMilliseconds}ms');
      print('Result: ${result.success ? 'Success' : 'Failed'}');
      
      if (result.success) {
        final summary = result.getApiResultsSummary();
        print('API Results Summary: $summary');
      }
      
    } finally {
      provider.dispose();
    }
  }
}

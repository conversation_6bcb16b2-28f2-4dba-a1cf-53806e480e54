import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/secure_comm_models.dart';
import '../models/relay_controller_models.dart';

/// Command execution result
class CommandResult {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  const CommandResult({
    required this.success,
    required this.message,
    this.data,
    required this.timestamp,
  });

  factory CommandResult.success({
    String message = 'Command executed successfully',
    Map<String, dynamic>? data,
  }) {
    return CommandResult(
      success: true,
      message: message,
      data: data,
      timestamp: DateTime.now(),
    );
  }

  factory CommandResult.error({
    required String message,
    Map<String, dynamic>? data,
  }) {
    return CommandResult(
      success: false,
      message: message,
      data: data,
      timestamp: DateTime.now(),
    );
  }
}

/// Command history entry
class CommandHistoryEntry {
  final String commandType;
  final Map<String, dynamic> payload;
  final CommandResult result;
  final DateTime timestamp;
  final String source;

  const CommandHistoryEntry({
    required this.commandType,
    required this.payload,
    required this.result,
    required this.timestamp,
    required this.source,
  });
}

/// Device command handler that serves as a bridge between secure_comm and other modules
class Device<PERSON>ommandHandler extends ChangeNotifier {
  final Logger _logger = Logger();
  
  SecureComm? _secureComm;
  final List<CommandHistoryEntry> _commandHistory = [];
  final Map<String, RelayController> _relayControllers = {};
  
  bool _isInitialized = false;
  String? _lastError;
  late DateTime _initializationTime;

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Whether the command handler is initialized
  bool get isInitialized => _isInitialized;

  /// Command history (latest first)
  List<CommandHistoryEntry> get commandHistory => 
      List.unmodifiable(_commandHistory.reversed);

  /// Last error message
  String? get lastError => _lastError;

  /// Available relay controllers
  Map<String, RelayController> get relayControllers => 
      Map.unmodifiable(_relayControllers);

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  /// Initialize the command handler with SecureComm instance
  Future<void> initialize(SecureComm secureComm) async {
    _logger.i('Initializing device command handler');
    
    try {
      _secureComm = secureComm;
      
      // Initialize relay controllers based on device capabilities
      await _initializeRelayControllers();
      
      // Set up message listeners if needed
      _setupMessageListeners();
      
      _isInitialized = true;
      _lastError = null;
      _initializationTime = DateTime.now();

      _logger.i('Device command handler initialized successfully');
      notifyListeners();
    } catch (e) {
      _logger.e('Failed to initialize command handler', error: e);
      _lastError = 'Initialization failed: $e';
      notifyListeners();
    }
  }

  /// Initialize relay controllers based on device capabilities
  Future<void> _initializeRelayControllers() async {
    if (_secureComm == null) return;

    try {
      // Check if device has relay control capability
      if (_secureComm!.hasScope('relay_control')) {
        // Create HTTP relay controller for main door
        final mainDoorController = HttpRelayController(
          deviceId: '${_secureComm!.deviceId}-main-door',
          urlOn: 'http://localhost:3000/relay/on?deviceId=${_secureComm!.deviceId}',
          urlOff: 'http://localhost:3000/relay/off?deviceId=${_secureComm!.deviceId}',
          deviceName: 'Main Door Relay',
        );

        _relayControllers['main_door'] = mainDoorController;
        
        _logger.i('Initialized relay controllers: ${_relayControllers.keys}');
      }
    } catch (e) {
      _logger.e('Failed to initialize relay controllers', error: e);
    }
  }

  /// Set up message listeners for incoming commands
  void _setupMessageListeners() {
    if (_secureComm == null) return;

    try {
      // Set up listener for server commands if using WebSocket transport
      final transport = _secureComm!.transport;

      if (transport is WebSocketTransport) {
        // Listen for server commands via WebSocket message stream
        transport.messageStream.listen((message) {
          _handleServerCommand(message);
        });
        _logger.d('WebSocket message listeners set up');
      } else {
        _logger.d('Non-WebSocket transport, polling for commands');
        // For HTTP transport, we could implement polling
        _startCommandPolling();
      }
    } catch (e) {
      _logger.e('Failed to set up message listeners', error: e);
    }
  }

  /// Handle incoming server commands
  void _handleServerCommand(Map<String, dynamic> message) {
    try {
      final type = message['type'] as String?;
      final payload = message['payload'] as Map<String, dynamic>?;
      final commandId = message['commandId'] as String?;

      _logger.i('Received server command: $type');

      if (type == null || payload == null) {
        _logger.w('Invalid command format received');
        return;
      }

      // Add to command history
      _addToHistory(
        type,
        payload,
        CommandResult(success: false, message: 'Processing...', timestamp: DateTime.now()),
        'server',
      );

      // Process the command
      _processServerCommand(type, payload, commandId);

    } catch (e) {
      _logger.e('Error handling server command: $e');
    }
  }

  /// Process server command and send response
  Future<void> _processServerCommand(
    String type,
    Map<String, dynamic> payload,
    String? commandId
  ) async {
    try {
      Map<String, dynamic> result = {};
      bool success = false;

      switch (type) {
        case 'ping':
          result = {
            'message': 'pong',
            'timestamp': DateTime.now().toIso8601String(),
            'device_id': _secureComm?.deviceId,
          };
          success = true;
          break;

        case 'relay_control':
          final action = payload['action'] as String?;
          final relayId = payload['relay_id'] as String? ?? 'main_door';

          if (action != null) {
            final controller = _relayControllers[relayId];
            if (controller != null) {
              switch (action.toLowerCase()) {
                case 'on':
                  await controller.triggerOn();
                  result = {'state': true, 'relay_id': relayId};
                  success = true;
                  break;
                case 'off':
                  await controller.triggerOff();
                  result = {'state': false, 'relay_id': relayId};
                  success = true;
                  break;
                case 'toggle':
                  if (controller.isConnected) {
                    await controller.triggerOff();
                  } else {
                    await controller.triggerOn();
                  }
                  result = {'state': controller.isConnected, 'relay_id': relayId};
                  success = true;
                  break;
              }
            } else {
              result = {'error': 'Relay controller not found: $relayId'};
            }
          } else {
            result = {'error': 'Action parameter required'};
          }
          break;

        case 'status_request':
          final component = payload['component'] as String? ?? 'all';
          result = await _getDeviceStatus(component);
          success = true;
          break;

        case 'config_update':
          // Handle configuration updates
          result = {'message': 'Config update received', 'config': payload};
          success = true;
          break;

        default:
          result = {'error': 'Unknown command type: $type'};
          break;
      }

      // Update command history with result
      _addToHistory(
        type,
        payload,
        CommandResult(success: success, message: success ? 'Command completed' : 'Command failed', timestamp: DateTime.now()),
        'server',
      );

      // Send response back to server
      if (_secureComm != null && commandId != null) {
        await _sendCommandResponse(commandId, success, result);
      }

    } catch (e) {
      _logger.e('Error processing server command: $type - $e');

      // Update history with error
      _addToHistory(
        type,
        payload,
        CommandResult(success: false, message: 'Error: $e', timestamp: DateTime.now()),
        'server',
      );

      if (commandId != null && _secureComm != null) {
        await _sendCommandResponse(commandId, false, {
          'error': 'Command processing failed: $e'
        });
      }
    }
  }

  /// Send command response back to server
  Future<void> _sendCommandResponse(
    String commandId,
    bool success,
    Map<String, dynamic> result
  ) async {
    try {
      // Send response via SecureComm
      await _secureComm!.sendMessage(
        type: 'deviceCommandResponse',
        payload: {
          'commandId': commandId,
          'deviceId': _secureComm!.deviceId,
          'success': success,
          'result': result,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _logger.d('Command response sent successfully');
    } catch (e) {
      _logger.e('Failed to send command response: $e');
    }
  }

  /// Get device status information
  Future<Map<String, dynamic>> _getDeviceStatus(String component) async {
    final status = <String, dynamic>{
      'device_id': _secureComm?.deviceId,
      'timestamp': DateTime.now().toIso8601String(),
      'uptime': DateTime.now().difference(_initializationTime).inSeconds,
    };

    if (component == 'all' || component == 'relay') {
      final relayStatus = <String, dynamic>{};
      for (final entry in _relayControllers.entries) {
        relayStatus[entry.key] = {
          'connected': entry.value.isConnected,
          'device_name': entry.value.deviceName,
        };
      }
      status['relays'] = relayStatus;
    }

    if (component == 'all' || component == 'system') {
      status['system'] = {
        'initialized': _isInitialized,
        'last_error': _lastError,
        'command_count': _commandHistory.length,
      };
    }

    return status;
  }

  /// Start polling for commands (for HTTP transport)
  void _startCommandPolling() {
    // TODO: Implement command polling for HTTP transport
    _logger.d('Command polling not implemented yet');
  }

  // ============================================================================
  // COMMAND EXECUTION METHODS
  // ============================================================================

  /// Execute a command based on type and payload
  Future<CommandResult> executeCommand({
    required String commandType,
    required Map<String, dynamic> payload,
    String source = 'manual',
  }) async {
    _logger.i('Executing command: $commandType');
    
    try {
      CommandResult result;
      
      switch (commandType) {
        case 'relay_control':
          result = await _executeRelayControl(payload);
          break;
        case 'face_auth':
          result = await _executeFaceAuth(payload);
          break;
        case 'status_request':
          result = await _executeStatusRequest(payload);
          break;
        case 'heartbeat':
          result = await _executeHeartbeat(payload);
          break;
        case 'config_update':
          result = await _executeConfigUpdate(payload);
          break;
        default:
          result = CommandResult.error(
            message: 'Unknown command type: $commandType',
          );
      }

      // Add to command history
      _addToHistory(commandType, payload, result, source);
      
      return result;
    } catch (e) {
      _logger.e('Command execution failed', error: e);
      final result = CommandResult.error(
        message: 'Command execution failed: $e',
      );
      _addToHistory(commandType, payload, result, source);
      return result;
    }
  }

  /// Execute relay control command
  Future<CommandResult> _executeRelayControl(Map<String, dynamic> payload) async {
    final action = payload['action'] as String?;
    final relayId = payload['relay_id'] as String? ?? 'main_door';
    
    if (action == null) {
      return CommandResult.error(message: 'Missing action parameter');
    }

    final controller = _relayControllers[relayId];
    if (controller == null) {
      return CommandResult.error(message: 'Relay controller not found: $relayId');
    }

    try {
      switch (action.toLowerCase()) {
        case 'on':
        case 'unlock':
          await controller.triggerOn();
          break;
        case 'off':
        case 'lock':
          await controller.triggerOff();
          break;
        case 'status':
          final status = await controller.getStatus();
          return CommandResult.success(
            message: 'Relay status retrieved',
            data: {'status': status, 'relay_id': relayId},
          );
        default:
          return CommandResult.error(message: 'Invalid action: $action');
      }

      // Send relay control via SecureComm
      if (_secureComm != null) {
        await _secureComm!.sendRelayControl(
          action: action,
          relayId: relayId,
          metadata: payload,
        );
      }

      return CommandResult.success(
        message: 'Relay $action command executed successfully',
        data: {'action': action, 'relay_id': relayId},
      );
    } catch (e) {
      return CommandResult.error(message: 'Relay control failed: $e');
    }
  }

  /// Execute face authentication command
  Future<CommandResult> _executeFaceAuth(Map<String, dynamic> payload) async {
    final faceImage = payload['face_image'] as String?;
    final userId = payload['user_id'] as String?;
    
    if (faceImage == null) {
      return CommandResult.error(message: 'Missing face_image parameter');
    }

    try {
      if (_secureComm != null) {
        final response = await _secureComm!.sendFaceAuth(
          faceImageBase64: faceImage,
          userId: userId,
          metadata: payload,
        );

        if (response.success) {
          return CommandResult.success(
            message: 'Face authentication completed',
            data: response.data,
          );
        } else {
          return CommandResult.error(
            message: response.error ?? 'Face authentication failed',
            data: response.data,
          );
        }
      } else {
        return CommandResult.error(message: 'SecureComm not initialized');
      }
    } catch (e) {
      return CommandResult.error(message: 'Face authentication failed: $e');
    }
  }

  /// Execute status request command
  Future<CommandResult> _executeStatusRequest(Map<String, dynamic> payload) async {
    try {
      final statusData = {
        'device_id': _secureComm?.deviceId,
        'connection_status': _secureComm?.isAuthenticated ?? false,
        'relay_controllers': _relayControllers.keys.toList(),
        'capabilities': _secureComm?.scopes ?? [],
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (_secureComm != null) {
        await _secureComm!.sendStatusRequest(
          component: payload['component'] as String?,
          filters: payload['filters'] as Map<String, dynamic>?,
        );
      }

      return CommandResult.success(
        message: 'Status retrieved successfully',
        data: statusData,
      );
    } catch (e) {
      return CommandResult.error(message: 'Status request failed: $e');
    }
  }

  /// Execute heartbeat command
  Future<CommandResult> _executeHeartbeat(Map<String, dynamic> payload) async {
    try {
      if (_secureComm != null) {
        await _secureComm!.sendHeartbeat(
          systemInfo: payload['system_info'] as Map<String, dynamic>?,
        );
      }

      return CommandResult.success(
        message: 'Heartbeat sent successfully',
        data: {'timestamp': DateTime.now().toIso8601String()},
      );
    } catch (e) {
      return CommandResult.error(message: 'Heartbeat failed: $e');
    }
  }

  /// Execute configuration update command
  Future<CommandResult> _executeConfigUpdate(Map<String, dynamic> payload) async {
    try {
      // TODO: Implement configuration update logic
      
      return CommandResult.success(
        message: 'Configuration updated successfully',
        data: payload,
      );
    } catch (e) {
      return CommandResult.error(message: 'Configuration update failed: $e');
    }
  }

  // ============================================================================
  // CONVENIENCE METHODS
  // ============================================================================

  /// Quick relay control methods
  Future<CommandResult> unlockDoor([String relayId = 'main_door']) async {
    return executeCommand(
      commandType: 'relay_control',
      payload: {'action': 'unlock', 'relay_id': relayId},
      source: 'quick_action',
    );
  }

  Future<CommandResult> lockDoor([String relayId = 'main_door']) async {
    return executeCommand(
      commandType: 'relay_control',
      payload: {'action': 'lock', 'relay_id': relayId},
      source: 'quick_action',
    );
  }

  Future<CommandResult> getRelayStatus([String relayId = 'main_door']) async {
    return executeCommand(
      commandType: 'relay_control',
      payload: {'action': 'status', 'relay_id': relayId},
      source: 'status_check',
    );
  }

  /// Send heartbeat
  Future<CommandResult> sendHeartbeat() async {
    return executeCommand(
      commandType: 'heartbeat',
      payload: {
        'system_info': {
          'status': 'active',
          'timestamp': DateTime.now().toIso8601String(),
        }
      },
      source: 'automatic',
    );
  }

  // ============================================================================
  // HISTORY MANAGEMENT
  // ============================================================================

  /// Add command to history
  void _addToHistory(
    String commandType,
    Map<String, dynamic> payload,
    CommandResult result,
    String source,
  ) {
    final entry = CommandHistoryEntry(
      commandType: commandType,
      payload: payload,
      result: result,
      timestamp: DateTime.now(),
      source: source,
    );

    _commandHistory.add(entry);

    // Keep only last 100 entries
    if (_commandHistory.length > 100) {
      _commandHistory.removeAt(0);
    }

    notifyListeners();
  }

  /// Clear command history
  void clearHistory() {
    _commandHistory.clear();
    notifyListeners();
  }

  /// Get command history filtered by type
  List<CommandHistoryEntry> getHistoryByType(String commandType) {
    return _commandHistory
        .where((entry) => entry.commandType == commandType)
        .toList()
        .reversed
        .toList();
  }

  // ============================================================================
  // CLEANUP
  // ============================================================================

  @override
  void dispose() {
    for (final controller in _relayControllers.values) {
      controller.dispose();
    }
    _relayControllers.clear();
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../../shared/services/face_recognition_v3_service.dart';

/// Simple widget to show face recognition service status
class FaceRecognitionStatusWidget extends StatefulWidget {
  const FaceRecognitionStatusWidget({super.key});

  @override
  State<FaceRecognitionStatusWidget> createState() => _FaceRecognitionStatusWidgetState();
}

class _FaceRecognitionStatusWidgetState extends State<FaceRecognitionStatusWidget> {
  Map<String, dynamic> _serviceStatus = {};
  bool _isHealthy = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServiceStatus();
  }

  Future<void> _loadServiceStatus() async {
    try {
      final status = FaceRecognitionV3Service.instance.getServiceStatus();
      final isHealthy = await FaceRecognitionV3Service.instance.testConnection();
      
      if (mounted) {
        setState(() {
          _serviceStatus = status;
          _isHealthy = isHealthy;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isHealthy = false;
          _isLoading = false;
        });
      }
      
      if (kDebugMode) {
        print('❌ Error loading face recognition service status: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isHealthy ? Colors.green.shade600 : Colors.red.shade600,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                _isHealthy ? Icons.check_circle : Icons.error,
                color: _isHealthy ? Colors.green.shade600 : Colors.red.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Face Recognition Service',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isLoading)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _isHealthy ? Colors.green.shade800 : Colors.red.shade800,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              _isHealthy ? 'HEALTHY' : 'UNAVAILABLE',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Service details
          if (_serviceStatus.isNotEmpty) ...[
            _buildDetailRow('Server', _serviceStatus['baseUrl'] ?? 'Unknown'),
            _buildDetailRow('API Path', _serviceStatus['apiPath'] ?? 'Unknown'),
            _buildDetailRow('Auth Token', _serviceStatus['hasAuthToken'] ? 'Set' : 'Not set'),
            _buildDetailRow('Configured', _serviceStatus['isConfigured'] ? 'Yes' : 'No'),
          ],
          
          const SizedBox(height: 12),
          
          // Refresh button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadServiceStatus,
              icon: Icon(Icons.refresh, size: 16),
              label: Text('Refresh Status'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Info text
          Text(
            'Health check: GET /health returns 200 = service running. Face recognition works directly via v3.1 API.',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

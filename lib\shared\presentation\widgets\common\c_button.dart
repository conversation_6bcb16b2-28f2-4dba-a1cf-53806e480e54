import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';

enum ButtonVariant {
  primary,
  secondary,
  text,
}

class CButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final bool isLoading;
  final EdgeInsets? padding;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? borderRadius;
  final double? elevation;
  final double? height;
  final Widget? child;

  const CButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.isLoading = false,
    this.padding,
    this.textStyle,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.elevation,
    this.height,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    switch (variant) {
      case ButtonVariant.primary:
        return _buildElevatedButton();
      case ButtonVariant.secondary:
        return _buildOutlinedButton();
      case ButtonVariant.text:
        return _buildTextButton();
    }
  }

  Widget _buildElevatedButton() {
    final button = ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary.withValues(alpha: 0.3),
        foregroundColor: foregroundColor ?? Colors.white,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 14.0,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8.0),
        ),
        elevation: elevation ?? 0,
      ),
      child: isLoading
        ? const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : child ?? Text(
            text,
            style: textStyle ?? AppTextStyles.buttonText,
          ),
    );

    return height != null
      ? SizedBox(height: height, child: button)
      : button;
  }

  Widget _buildOutlinedButton() {
    final button = OutlinedButton(
      onPressed: isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: foregroundColor ?? AppColors.primary,
        padding: padding ?? const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 14.0,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8.0),
        ),
        side: BorderSide(
          color: backgroundColor ?? AppColors.primary,
        ),
      ),
      child: isLoading
        ? SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                foregroundColor ?? AppColors.primary,
              ),
            ),
          )
        : child ?? Text(
            text,
            style: textStyle ?? AppTextStyles.buttonText.copyWith(
              color: foregroundColor ?? AppColors.primary,
            ),
          ),
    );

    return height != null
      ? SizedBox(height: height, child: button)
      : button;
  }

  Widget _buildTextButton() {
    final button = TextButton(
      onPressed: isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: foregroundColor ?? AppColors.primary,
        padding: padding ?? EdgeInsets.zero,
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: child ?? Text(
        text,
        style: textStyle ?? AppTextStyles.linkText,
      ),
    );

    return height != null
      ? SizedBox(height: height, child: button)
      : button;
  }
}

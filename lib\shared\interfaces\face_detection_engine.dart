import 'dart:typed_data';
import 'dart:ui';

import 'package:camera/camera.dart';

/// Abstract interface for face detection engines
abstract class FaceDetectionEngine {
  /// Initialize the detection engine
  Future<void> initialize();
  
  /// Detect faces in the provided image bytes
  Future<List<FaceDetection>> detectFaces(Uint8List imageBytes);
  
  /// Process camera image directly (optimized path)
  Future<List<FaceDetection>> processCameraImage(CameraImage image);
  
  /// Get the engine name for debugging/logging
  String get engineName;
  
  /// Check if the engine is initialized
  bool get isInitialized;
  
  /// Dispose of resources
  void dispose();
}

/// Face detection result with comprehensive information
class FaceDetection {
  /// Bounding box of the detected face
  final Rect boundingBox;
  
  /// Facial landmarks (5 key points: left eye, right eye, nose, left mouth, right mouth)
  final List<Point> landmarks;
  
  /// Detection confidence score (0.0 to 1.0)
  final double confidence;
  
  /// Aligned and cropped face image (112x112 for recognition)
  final Uint8List? croppedFace;
  
  /// Face quality score (0.0 to 1.0)
  final double quality;
  
  /// Face angle/pose information
  final FacePose pose;
  
  /// Additional metadata
  final Map<String, dynamic> metadata;
  
  const FaceDetection({
    required this.boundingBox,
    required this.landmarks,
    required this.confidence,
    this.croppedFace,
    this.quality = 0.0,
    this.pose = const FacePose(),
    this.metadata = const {},
  });
  
  /// Create a copy with updated values
  FaceDetection copyWith({
    Rect? boundingBox,
    List<Point>? landmarks,
    double? confidence,
    Uint8List? croppedFace,
    double? quality,
    FacePose? pose,
    Map<String, dynamic>? metadata,
  }) {
    return FaceDetection(
      boundingBox: boundingBox ?? this.boundingBox,
      landmarks: landmarks ?? this.landmarks,
      confidence: confidence ?? this.confidence,
      croppedFace: croppedFace ?? this.croppedFace,
      quality: quality ?? this.quality,
      pose: pose ?? this.pose,
      metadata: metadata ?? this.metadata,
    );
  }
  
  @override
  String toString() {
    return 'FaceDetection(bbox: $boundingBox, confidence: ${confidence.toStringAsFixed(2)}, quality: ${quality.toStringAsFixed(2)})';
  }
}

/// Face pose/angle information
class FacePose {
  /// Yaw angle (left-right rotation)
  final double yaw;
  
  /// Pitch angle (up-down rotation)
  final double pitch;
  
  /// Roll angle (tilt rotation)
  final double roll;
  
  const FacePose({
    this.yaw = 0.0,
    this.pitch = 0.0,
    this.roll = 0.0,
  });
  
  /// Check if face is frontal (within acceptable angle thresholds)
  bool get isFrontal {
    const threshold = 15.0; // degrees
    return yaw.abs() < threshold && 
           pitch.abs() < threshold && 
           roll.abs() < threshold;
  }
  
  @override
  String toString() {
    return 'FacePose(yaw: ${yaw.toStringAsFixed(1)}°, pitch: ${pitch.toStringAsFixed(1)}°, roll: ${roll.toStringAsFixed(1)}°)';
  }
}

/// Face detection engine configuration
class FaceDetectionConfig {
  /// Minimum face size to detect (in pixels)
  final int minFaceSize;
  
  /// Maximum face size to detect (in pixels, 0 = no limit)
  final int maxFaceSize;
  
  /// Minimum confidence threshold for detection
  final double minConfidence;
  
  /// Maximum number of faces to detect
  final int maxFaces;
  
  /// Whether to extract facial landmarks
  final bool extractLandmarks;
  
  /// Whether to crop and align faces
  final bool cropFaces;
  
  /// Target size for cropped faces
  final int cropSize;
  
  /// Whether to calculate face quality
  final bool calculateQuality;
  
  const FaceDetectionConfig({
    this.minFaceSize = 40,
    this.maxFaceSize = 0,
    this.minConfidence = 0.7,
    this.maxFaces = 10,
    this.extractLandmarks = true,
    this.cropFaces = true,
    this.cropSize = 112,
    this.calculateQuality = true,
  });
}

/// Face detection engine performance metrics
class FaceDetectionMetrics {
  /// Processing time in milliseconds
  final int processingTimeMs;
  
  /// Number of faces detected
  final int facesDetected;
  
  /// Input image size
  final Size imageSize;
  
  /// Engine name
  final String engineName;
  
  /// Timestamp
  final DateTime timestamp;
  
  const FaceDetectionMetrics({
    required this.processingTimeMs,
    required this.facesDetected,
    required this.imageSize,
    required this.engineName,
    required this.timestamp,
  });
  
  /// Calculate FPS based on processing time
  double get fps => processingTimeMs > 0 ? 1000.0 / processingTimeMs : 0.0;
  
  @override
  String toString() {
    return 'FaceDetectionMetrics(engine: $engineName, time: ${processingTimeMs}ms, fps: ${fps.toStringAsFixed(1)}, faces: $facesDetected)';
  }
}

/// Exception thrown by face detection engines
class FaceDetectionException implements Exception {
  final String message;
  final String? engineName;
  final dynamic originalError;
  
  const FaceDetectionException(
    this.message, {
    this.engineName,
    this.originalError,
  });
  
  @override
  String toString() {
    final engine = engineName != null ? '[$engineName] ' : '';
    return 'FaceDetectionException: $engine$message';
  }
}

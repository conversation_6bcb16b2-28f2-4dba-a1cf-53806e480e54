# Group Device Registration

## Overview

The Terminal app now supports **Group Device Registration** instead of registering individual relay devices separately. This approach provides better organization, reduces server load, and maintains cleaner device management.

## Changes Made

### Before (Legacy/Ungrouped Devices)
```
Terminal Registration:
├── T-A3B4 (Terminal Device)
├── T-A3B4-R01 (Individual Relay 1)
├── T-A3B4-R02 (Individual Relay 2)  
├── T-A3B4-R03 (Individual Relay 3)
└── T-A3B4-R04 (Individual Relay 4)
```

**Problems:**
- 5 separate device registrations
- Scattered device information
- Complex management
- Higher server load

### After (Group Device Registration)
```
Group Device Registration:
└── T-A3B4 (Terminal Group)
    ├── Terminal (Face Recognition)
    ├── Relay 1 (main_door)
    ├── Relay 2 (back_door)
    ├── Relay 3 (garage)
    └── Relay 4 (emergency)
```

**Benefits:**
- Single group device registration
- Centralized device information
- Simplified management
- Reduced server load
- Better organization

## Implementation Details

### Group Registration Payload
```json
{
  "deviceId": "T-A3B4",
  "deviceName": "Terminal Device",
  "type": "terminal_group",
  "groupType": "terminal_with_relays",
  "parentDevice": "T-A3B4",
  "relayCount": 4,
  "relayProfiles": ["main_door", "back_door", "garage", "emergency"],
  "relayConfiguration": {
    "relay_1": {
      "index": 1,
      "name": "Terminal Relay 1",
      "profile": "main_door",
      "enabled": true,
      "defaultState": "off"
    },
    "relay_2": {
      "index": 2,
      "name": "Terminal Relay 2", 
      "profile": "back_door",
      "enabled": true,
      "defaultState": "off"
    },
    "relay_3": {
      "index": 3,
      "name": "Terminal Relay 3",
      "profile": "garage",
      "enabled": true,
      "defaultState": "off"
    },
    "relay_4": {
      "index": 4,
      "name": "Terminal Relay 4",
      "profile": "emergency",
      "enabled": true,
      "defaultState": "off"
    }
  },
  "capabilities": [
    "face_recognition",
    "relay_control", 
    "group_management",
    "multi_relay_control"
  ],
  "metadata": {
    "registration_type": "group_device",
    "device_count": 5,
    "auto_registered": true,
    "group_id": "T-A3B4"
  }
}
```

### API Endpoints

#### Primary Endpoint (Group Registration)
```
POST /api/device/register-group
```

#### Fallback Endpoint (Legacy Registration)
```
POST /register (individual devices)
```

#### Verification Endpoint
```
GET /api/device/group/{groupId}
```

## Registration Flow

### 1. Group Registration Attempt
```dart
// Try group registration first
final uri = Uri.parse('$serverUrl/api/device/register-group');
final response = await http.post(uri, body: groupDeviceData);
```

### 2. Fallback to Legacy
```dart
// If group registration fails (404), fallback to legacy
if (response.statusCode == 404) {
  await _autoRegisterRelayDevicesLegacy(terminalId, serverUrl);
}
```

### 3. Verification
```dart
// Verify group registration
await _verifyGroupRegistration(terminalId, serverUrl);

// Or fallback to legacy verification
await _verifyRelayRegistration(terminalId, serverUrl);
```

## Server Response Format

### Successful Group Registration
```json
{
  "success": true,
  "group_id": "T-A3B4",
  "device_count": 5,
  "relay_count": 4,
  "registration_type": "group_device",
  "registered_at": "2024-01-15T10:30:00Z",
  "message": "Terminal group device registered successfully"
}
```

### Group Verification Response
```json
{
  "group_id": "T-A3B4",
  "device_count": 5,
  "relay_count": 4,
  "registration_type": "group_device",
  "relays": [
    {"index": 1, "profile": "main_door", "enabled": true},
    {"index": 2, "profile": "back_door", "enabled": true},
    {"index": 3, "profile": "garage", "enabled": true},
    {"index": 4, "profile": "emergency", "enabled": true}
  ]
}
```

## Debug Widget Updates

The debug widget now shows:
- **Group Devices**: Devices registered as groups
- **Individual Terminals**: Legacy terminal devices
- **Individual Relays**: Legacy relay devices

### Debug Log Examples
```
🖥️ Terminals: 2
🔌 Relays: 8  
👥 Group Devices: 1
  G: T-A3B4 (4 relays)
  T: T-OLD1
  R: T-OLD1-R01
  R: T-OLD1-R02
```

## Configuration

### Relay Profiles
```dart
final relayProfiles = [
  'main_door',    // Relay 1: Main entrance door
  'back_door',    // Relay 2: Back entrance door  
  'garage',       // Relay 3: Garage door
  'emergency'     // Relay 4: Emergency systems
];
```

### Device Capabilities
```dart
final capabilities = [
  'face_recognition',     // Face recognition capability
  'relay_control',        // Individual relay control
  'group_management',     // Group device management
  'multi_relay_control'   // Multiple relay control
];
```

## Migration Strategy

### Automatic Migration
1. **New Installations**: Use group registration by default
2. **Existing Installations**: Continue with legacy until re-registration
3. **Fallback Support**: Always fallback to legacy if group registration fails

### Manual Migration
```dart
// Force re-registration to use group format
final provider = DeviceRegistrationProvider();
await provider.unregisterDevice();
await provider.registerDevice(/* same parameters */);
```

## Benefits

### For Developers
- **Simplified Code**: Single registration instead of multiple
- **Better Organization**: Logical grouping of related devices
- **Easier Debugging**: Centralized device information
- **Reduced Complexity**: Less state management

### For Server
- **Reduced Load**: Fewer registration requests
- **Better Performance**: Single database entry instead of multiple
- **Cleaner Data**: Organized device hierarchy
- **Easier Queries**: Group-based device queries

### For Users
- **Faster Registration**: Single request instead of multiple
- **Better UI**: Grouped device display
- **Clearer Status**: Group-level status information
- **Simplified Management**: Group-level operations

## Troubleshooting

### Group Registration Fails
1. **Check Server Support**: Ensure server supports `/api/device/register-group`
2. **Verify Payload**: Check group registration payload format
3. **Fallback Activated**: System automatically falls back to legacy registration

### Verification Issues
1. **Group Endpoint Missing**: Falls back to legacy verification
2. **Partial Registration**: Some relays may not be included in group
3. **Legacy Devices**: Mixed group and individual devices on server

### Debug Information
```dart
// Enable detailed logging
_logger.i('🚀 Registering terminal as group device: $terminalId');
_logger.i('📡 Registering group device with ${relayProfiles.length} relays');
_logger.i('✅ Successfully registered terminal group device: $terminalId');
```

## Future Enhancements

1. **Dynamic Relay Count**: Support variable number of relays
2. **Custom Profiles**: User-defined relay profiles
3. **Group Operations**: Bulk operations on group devices
4. **Advanced Verification**: Real-time group status monitoring
5. **Migration Tools**: Automated legacy to group migration

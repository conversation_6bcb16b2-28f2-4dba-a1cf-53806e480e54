import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'camera_image_converter.dart';

/// Service for cropping face regions from captured images
class FaceCroppingService {
  static const String _logTag = '🔪 FaceCroppingService';
  
  /// Crop face region from image file using detected face boundaries
  /// 
  /// [imagePath] - Path to the source image file
  /// [face] - Detected face object from Google ML Kit
  /// [padding] - Additional padding around face (0.0 to 1.0, default 0.2 = 20%)
  /// [outputQuality] - JPEG quality for output (1-100, default 85)
  /// 
  /// Returns: Path to the cropped face image file, or null if cropping fails
  static Future<String?> cropFaceFromImage({
    required String imagePath,
    required Face face,
    double padding = 0.2,
    int outputQuality = 85,
  }) async {
    try {
      debugPrint('$_logTag Starting face crop from: $imagePath');
      
      // Validate input parameters
      if (!File(imagePath).existsSync()) {
        debugPrint('$_logTag ❌ Source image file does not exist: $imagePath');
        return null;
      }
      
      if (padding < 0.0 || padding > 1.0) {
        debugPrint('$_logTag ⚠️ Invalid padding value: $padding, using default 0.2');
        padding = 0.2;
      }
      
      if (outputQuality < 1 || outputQuality > 100) {
        debugPrint('$_logTag ⚠️ Invalid quality value: $outputQuality, using default 85');
        outputQuality = 85;
      }
      
      // Load and decode the source image
      final sourceImageBytes = await File(imagePath).readAsBytes();
      final sourceImage = img.decodeImage(sourceImageBytes);
      
      if (sourceImage == null) {
        debugPrint('$_logTag ❌ Failed to decode source image');
        return null;
      }
      
      debugPrint('$_logTag Source image size: ${sourceImage.width}x${sourceImage.height}');
      debugPrint('$_logTag Face bounding box: ${face.boundingBox}');
      
      // Calculate crop region with padding
      final cropRegion = _calculateCropRegion(
        face.boundingBox,
        sourceImage.width,
        sourceImage.height,
        padding,
      );
      
      if (cropRegion == null) {
        debugPrint('$_logTag ❌ Invalid crop region calculated');
        return null;
      }
      
      debugPrint('$_logTag Crop region: $cropRegion');
      
      // Crop the face region
      final croppedImage = img.copyCrop(
        sourceImage,
        x: cropRegion.left.round(),
        y: cropRegion.top.round(),
        width: cropRegion.width.round(),
        height: cropRegion.height.round(),
      );
      
      // Generate output file path
      final outputPath = await _generateOutputPath(imagePath, 'cropped');
      if (outputPath == null) {
        debugPrint('$_logTag ❌ Failed to generate output path');
        return null;
      }
      
      // Encode and save the cropped image
      final croppedBytes = img.encodeJpg(croppedImage, quality: outputQuality);
      await File(outputPath).writeAsBytes(croppedBytes);
      
      debugPrint('$_logTag ✅ Face cropped successfully: $outputPath');
      debugPrint('$_logTag Cropped image size: ${croppedImage.width}x${croppedImage.height}');
      
      return outputPath;
      
    } catch (e, stackTrace) {
      debugPrint('$_logTag ❌ Error cropping face: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      return null;
    }
  }
  
  /// Crop multiple faces from a single image
  /// 
  /// [imagePath] - Path to the source image file
  /// [faces] - List of detected faces from Google ML Kit
  /// [padding] - Additional padding around faces (0.0 to 1.0, default 0.2)
  /// [outputQuality] - JPEG quality for output (1-100, default 85)
  /// 
  /// Returns: List of paths to cropped face images
  static Future<List<String>> cropMultipleFacesFromImage({
    required String imagePath,
    required List<Face> faces,
    double padding = 0.2,
    int outputQuality = 85,
  }) async {
    final croppedPaths = <String>[];
    
    try {
      debugPrint('$_logTag Starting multiple face crop from: $imagePath');
      debugPrint('$_logTag Number of faces to crop: ${faces.length}');
      
      for (int i = 0; i < faces.length; i++) {
        final face = faces[i];
        final croppedPath = await cropFaceFromImage(
          imagePath: imagePath,
          face: face,
          padding: padding,
          outputQuality: outputQuality,
        );
        
        if (croppedPath != null) {
          // Rename file to include face index
          final renamedPath = await _renameWithIndex(croppedPath, i);
          if (renamedPath != null) {
            croppedPaths.add(renamedPath);
          } else {
            croppedPaths.add(croppedPath);
          }
        }
      }
      
      debugPrint('$_logTag ✅ Successfully cropped ${croppedPaths.length}/${faces.length} faces');
      
    } catch (e, stackTrace) {
      debugPrint('$_logTag ❌ Error cropping multiple faces: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
    }
    
    return croppedPaths;
  }
  
  /// Crop face from captured image bytes (for real-time processing)
  /// 
  /// [imageBytes] - Raw image bytes
  /// [face] - Detected face object
  /// [padding] - Additional padding around face
  /// [outputQuality] - JPEG quality for output
  /// 
  /// Returns: Cropped image bytes, or null if cropping fails
  static Future<Uint8List?> cropFaceFromBytes({
    required Uint8List imageBytes,
    required Face face,
    double padding = 0.2,
    int outputQuality = 85,
  }) async {
    try {
      debugPrint('$_logTag Starting face crop from bytes (${imageBytes.length} bytes)');
      
      // Decode the source image
      final sourceImage = img.decodeImage(imageBytes);
      
      if (sourceImage == null) {
        debugPrint('$_logTag ❌ Failed to decode source image bytes');
        return null;
      }
      
      // Calculate crop region with padding
      final cropRegion = _calculateCropRegion(
        face.boundingBox,
        sourceImage.width,
        sourceImage.height,
        padding,
      );
      
      if (cropRegion == null) {
        debugPrint('$_logTag ❌ Invalid crop region calculated');
        return null;
      }
      
      // Crop the face region
      final croppedImage = img.copyCrop(
        sourceImage,
        x: cropRegion.left.round(),
        y: cropRegion.top.round(),
        width: cropRegion.width.round(),
        height: cropRegion.height.round(),
      );
      
      // Encode to bytes
      final croppedBytes = Uint8List.fromList(img.encodeJpg(croppedImage, quality: outputQuality));
      
      debugPrint('$_logTag ✅ Face cropped from bytes successfully');
      debugPrint('$_logTag Cropped image size: ${croppedImage.width}x${croppedImage.height}');
      
      return croppedBytes;
      
    } catch (e, stackTrace) {
      debugPrint('$_logTag ❌ Error cropping face from bytes: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      return null;
    }
  }
  
  /// Calculate crop region with padding, ensuring it stays within image bounds
  static ui.Rect? _calculateCropRegion(
    ui.Rect faceBoundingBox,
    int imageWidth,
    int imageHeight,
    double padding,
  ) {
    try {
      // Calculate padding in pixels
      final faceWidth = faceBoundingBox.width;
      final faceHeight = faceBoundingBox.height;
      final paddingX = faceWidth * padding;
      final paddingY = faceHeight * padding;
      
      // Calculate expanded region
      double left = faceBoundingBox.left - paddingX;
      double top = faceBoundingBox.top - paddingY;
      double right = faceBoundingBox.right + paddingX;
      double bottom = faceBoundingBox.bottom + paddingY;
      
      // Clamp to image bounds
      left = left.clamp(0.0, imageWidth.toDouble());
      top = top.clamp(0.0, imageHeight.toDouble());
      right = right.clamp(0.0, imageWidth.toDouble());
      bottom = bottom.clamp(0.0, imageHeight.toDouble());
      
      // Ensure valid dimensions
      if (right <= left || bottom <= top) {
        debugPrint('$_logTag ❌ Invalid crop dimensions after clamping');
        return null;
      }
      
      return ui.Rect.fromLTRB(left, top, right, bottom);
      
    } catch (e) {
      debugPrint('$_logTag ❌ Error calculating crop region: $e');
      return null;
    }
  }
  
  /// Generate output file path for cropped image
  static Future<String?> _generateOutputPath(String sourcePath, String suffix) async {
    try {
      final sourceFile = File(sourcePath);
      final directory = sourceFile.parent;
      final baseName = path.basenameWithoutExtension(sourcePath);
      final extension = path.extension(sourcePath);
      
      // Generate unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final outputFileName = '${baseName}_${suffix}_$timestamp$extension';
      final outputPath = path.join(directory.path, outputFileName);
      
      return outputPath;
      
    } catch (e) {
      debugPrint('$_logTag ❌ Error generating output path: $e');
      return null;
    }
  }
  
  /// Rename cropped file with face index
  static Future<String?> _renameWithIndex(String filePath, int index) async {
    try {
      final sourceFile = File(filePath);
      if (!sourceFile.existsSync()) return null;
      
      final directory = sourceFile.parent;
      final baseName = path.basenameWithoutExtension(filePath);
      final extension = path.extension(filePath);
      
      // Add face index to filename
      final newFileName = '${baseName}_face$index$extension';
      final newPath = path.join(directory.path, newFileName);
      
      await sourceFile.rename(newPath);
      return newPath;
      
    } catch (e) {
      debugPrint('$_logTag ❌ Error renaming file with index: $e');
      return null;
    }
  }
  
  /// Clean up temporary cropped files
  static Future<void> cleanupCroppedFiles(List<String> filePaths) async {
    for (final filePath in filePaths) {
      try {
        final file = File(filePath);
        if (file.existsSync()) {
          await file.delete();
          debugPrint('$_logTag 🧹 Cleaned up: $filePath');
        }
      } catch (e) {
        debugPrint('$_logTag ⚠️ Failed to cleanup: $filePath - $e');
      }
    }
  }

  /// Crop face directly from CameraImage for real-time avatar display
  ///
  /// [cameraImage] - Camera image from image stream
  /// [cameraDescription] - Camera description for proper orientation
  /// [face] - Detected face object from Google ML Kit
  /// [paddingFactor] - Additional padding around face (0.0 to 1.0, default 0.3 = 30%)
  /// [targetSize] - Target size for square avatar (default 200px)
  ///
  /// Returns: JPEG bytes of cropped face, or null if cropping fails
  static Future<Uint8List?> cropFaceFromCameraImage({
    required CameraImage cameraImage,
    required CameraDescription cameraDescription,
    required Face face,
    double paddingFactor = 0.3,
    int targetSize = 200,
  }) async {
    try {
      debugPrint('$_logTag Starting face crop from camera image...');

      // Convert camera image to JPEG bytes first, then decode to Image
      final Uint8List? jpegBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
      );

      if (jpegBytes == null) {
        debugPrint('$_logTag ❌ Failed to convert camera image to JPEG');
        return null;
      }

      // Decode JPEG bytes to Image
      final img.Image? fullImage = img.decodeJpg(jpegBytes);

      if (fullImage == null) {
        debugPrint('$_logTag ❌ Failed to convert camera image');
        return null;
      }

      // Get face bounds
      final faceRect = face.boundingBox;

      debugPrint('$_logTag 📐 Face bounds: ${faceRect.left}, ${faceRect.top}, ${faceRect.width}, ${faceRect.height}');
      debugPrint('$_logTag 📐 Image size: ${fullImage.width}x${fullImage.height}');

      // Calculate crop bounds with padding
      final cropBounds = _calculateSquareCropBounds(
        faceRect: faceRect,
        imageWidth: fullImage.width,
        imageHeight: fullImage.height,
        paddingFactor: paddingFactor,
      );

      debugPrint('$_logTag ✂️ Crop bounds: x=${cropBounds['x']}, y=${cropBounds['y']}, w=${cropBounds['width']}, h=${cropBounds['height']}');

      // Crop the face region
      final croppedImage = img.copyCrop(
        fullImage,
        x: cropBounds['x']!,
        y: cropBounds['y']!,
        width: cropBounds['width']!,
        height: cropBounds['height']!,
      );

      // Resize to target size (square)
      final resizedImage = img.copyResize(
        croppedImage,
        width: targetSize,
        height: targetSize,
        interpolation: img.Interpolation.cubic,
      );

      // Convert to JPEG bytes
      final croppedJpegBytes = img.encodeJpg(resizedImage, quality: 85);

      debugPrint('$_logTag ✅ Face cropped successfully: ${croppedJpegBytes.length} bytes, ${targetSize}x${targetSize}px');

      return Uint8List.fromList(croppedJpegBytes);

    } catch (e) {
      debugPrint('$_logTag ❌ Error cropping face from camera image: $e');
      return null;
    }
  }

  /// Calculate square crop bounds with padding around face for avatar display
  static Map<String, int> _calculateSquareCropBounds({
    required ui.Rect faceRect,
    required int imageWidth,
    required int imageHeight,
    required double paddingFactor,
  }) {
    // Calculate padding
    final faceWidth = faceRect.width;
    final faceHeight = faceRect.height;
    final paddingX = faceWidth * paddingFactor;
    final paddingY = faceHeight * paddingFactor;

    // Calculate initial crop bounds
    double cropLeft = faceRect.left - paddingX;
    double cropTop = faceRect.top - paddingY;
    double cropWidth = faceWidth + (paddingX * 2);
    double cropHeight = faceHeight + (paddingY * 2);

    // Make it square by using the larger dimension
    final maxDimension = cropWidth > cropHeight ? cropWidth : cropHeight;

    // Center the square crop around face center
    final faceCenterX = faceRect.left + faceWidth / 2;
    final faceCenterY = faceRect.top + faceHeight / 2;

    cropLeft = faceCenterX - maxDimension / 2;
    cropTop = faceCenterY - maxDimension / 2;
    cropWidth = maxDimension;
    cropHeight = maxDimension;

    // Ensure bounds are within image
    if (cropLeft < 0) {
      cropLeft = 0;
    }
    if (cropTop < 0) {
      cropTop = 0;
    }
    if (cropLeft + cropWidth > imageWidth) {
      cropLeft = imageWidth - cropWidth;
      if (cropLeft < 0) {
        cropLeft = 0;
        cropWidth = imageWidth.toDouble();
      }
    }
    if (cropTop + cropHeight > imageHeight) {
      cropTop = imageHeight - cropHeight;
      if (cropTop < 0) {
        cropTop = 0;
        cropHeight = imageHeight.toDouble();
      }
    }

    // Ensure minimum size
    cropWidth = cropWidth.clamp(50.0, imageWidth.toDouble());
    cropHeight = cropHeight.clamp(50.0, imageHeight.toDouble());

    return {
      'x': cropLeft.round(),
      'y': cropTop.round(),
      'width': cropWidth.round(),
      'height': cropHeight.round(),
    };
  }
}

import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';
import '../icons/app_icons.dart';

/// Empty state component cho màn hình tenants
class TenantsEmptyState extends StatelessWidget {
  final VoidCallback? onAddTenant;

  const TenantsEmptyState({
    super.key,
    this.onAddTenant,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 343,
      padding: EdgeInsets.symmetric(
              vertical: AppDimensions.spacing24),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.border,
          width: AppDimensions.borderNormal,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            child: Column(
              children: [
                _buildEmptyIllustration(),
                SizedBox(height: AppDimensions.spacing8),
                _buildEmptyContent(),
              ],
            ),
          ),
          SizedBox(height: AppDimensions.spacing16),
          _buildAddButton(),
        ],
      ),
    );
  }

  Widget _buildEmptyIllustration() {
    return SizedBox(
      width: 81,
      height: 67,
      child: CustomPaint(
        painter: _EmptyStatePainter(),
      ),
    );
  }

  Widget _buildEmptyContent() {
    return Column(
      children: [
        Text(
          'Chưa có tổ chức nào.',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppDimensions.spacing4),
        SizedBox(
          width: 279,
          child: Text(
            'Thiết lập tổ chức của bạn để bắt đầu sử dụng hệ thống nhận diện thông minh bằng Camera AI!',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: onAddTenant,
      child: Container(
        padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppIcons.add(
              size: 12,
              color: Colors.white,
            ),
            SizedBox(width: AppDimensions.spacing6),
            Text(
              'Thêm tổ chức',
              style: AppTextStyles.caption.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom painter để vẽ illustration cho empty state
class _EmptyStatePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Shadow ellipse
    paint.color = const Color(0xFFF5F5F7).withValues(alpha: 0.8);
    canvas.drawOval(
      Rect.fromLTWH(
        size.width * 0.13,
        size.height * 0.84,
        size.width * 0.74,
        size.height * 0.16,
      ),
      paint,
    );

    // Main folder base
    paint.color = const Color(0xFFAEB8C2);
    final folderBasePath = Path();
    folderBasePath.moveTo(size.width * 0.79, size.height * 0.67);
    folderBasePath.lineTo(size.width * 0.66, size.height * 0.48);
    folderBasePath.cubicTo(
      size.width * 0.65, size.height * 0.46,
      size.width * 0.64, size.height * 0.46,
      size.width * 0.63, size.height * 0.46,
    );
    folderBasePath.lineTo(size.width * 0.36, size.height * 0.46);
    folderBasePath.cubicTo(
      size.width * 0.35, size.height * 0.46,
      size.width * 0.34, size.height * 0.46,
      size.width * 0.33, size.height * 0.48,
    );
    folderBasePath.lineTo(size.width * 0.20, size.height * 0.67);
    folderBasePath.lineTo(size.width * 0.79, size.height * 0.77);
    folderBasePath.close();
    canvas.drawPath(folderBasePath, paint);

    // Main document
    paint.color = const Color(0xFFF5F5F7);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          size.width * 0.29,
          size.height * 0.21,
          size.width * 0.42,
          size.height * 0.67,
        ),
        const Radius.circular(2),
      ),
      paint,
    );

    // Document content area
    paint.color = const Color(0xFFDCE0E6);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          size.width * 0.35,
          size.height * 0.28,
          size.width * 0.30,
          size.height * 0.19,
        ),
        const Radius.circular(1),
      ),
      paint,
    );

    // Document lines
    paint.color = const Color(0xFFDCE0E6);
    paint.strokeWidth = 1;
    
    // Line 1
    canvas.drawLine(
      Offset(size.width * 0.36, size.height * 0.54),
      Offset(size.width * 0.64, size.height * 0.54),
      paint,
    );
    
    // Line 2
    canvas.drawLine(
      Offset(size.width * 0.36, size.height * 0.62),
      Offset(size.width * 0.64, size.height * 0.62),
      paint,
    );

    // Notification badge
    paint.color = const Color(0xFFDCE0E6);
    canvas.drawCircle(
      Offset(size.width * 0.87, size.height * 0.12),
      size.width * 0.10,
      paint,
    );

    // Badge content - plus icon
    paint.color = Colors.white;
    paint.strokeWidth = 1.5;
    paint.strokeCap = StrokeCap.round;
    
    // Vertical line of plus
    canvas.drawLine(
      Offset(size.width * 0.87, size.height * 0.08),
      Offset(size.width * 0.87, size.height * 0.16),
      paint,
    );
    
    // Horizontal line of plus
    canvas.drawLine(
      Offset(size.width * 0.83, size.height * 0.12),
      Offset(size.width * 0.91, size.height * 0.12),
      paint,
    );

    // Badge dot
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(size.width * 0.92, size.height * 0.14),
      size.width * 0.015,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

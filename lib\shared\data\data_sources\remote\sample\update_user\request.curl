curl --location --request PUT 'http://10.161.80.12:1081/api/v3.1/users/686e296dc0c777149e154106' \
--header 'Accept: application/json, text/plain, */*' \
--header 'Accept-Language: en-US,en;q=0.9,vi;q=0.8' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SyESDjW-zxLSmcDSEBqk2TWHSyvKjfoy6MRgQaN3ot8' \
--header 'Cache-Control: no-cache' \
--header 'Connection: keep-alive' \
--header 'Pragma: no-cache' \
--header 'Sec-Fetch-Dest: empty' \
--header 'Sec-Fetch-Mode: cors' \
--header 'Sec-Fetch-Site: same-site' \
--header 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/138.0.0.0 Safari/537.36' \
--header 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
--header 'sec-ch-ua-mobile: ?0' \
--header 'sec-ch-ua-platform: "Windows"' \
--form 'current_unit_id="686e28fcc0c777149e153a09"' \
--form 'current_tenant_id="686e28fbc0c777149e153a01"' \
--form 'code="E0000122"' \
--form 'name="kiet demacia"' \
--form 'email="<EMAIL>"' \
--form 'phone="0323232323"' \
--form 'dob="2025-07-07"' \
--form 'gender="male"' \
--form 'avatar=@"/C:/Users/<USER>/Downloads/face_angles/front.png"' \
--form 'front=@"/C:/Users/<USER>/Downloads/face_angles/front.png"' \
--form 'left=@"/C:/Users/<USER>/Downloads/face_angles/left.png"' \
--form 'right=@"/C:/Users/<USER>/Downloads/face_angles/right.png"' \
--form 'top=@"/C:/Users/<USER>/Downloads/face_angles/down.png"' \
--form 'bottom=@"/C:/Users/<USER>/Downloads/face_angles/up.png"'
# Face Recognition Models

## Required Model Files

Place the following TFLite model files in this directory:

### Detection Models
- `ultraface_320.tflite` - UltraFace detection model (320x240 input)
- `mediapipe_face_detection.tflite` - MediaPipe face detection model
- `ml_kit_face_detection.tflite` - ML Kit compatible model (optional)

### Recognition Models  
- `mobilefacenet.tflite` - MobileFaceNet recognition model (512-dim embeddings)

## Model Sources

### UltraFace
- **Source**: https://github.com/Linzaer/Ultra-Light-Fast-Generic-Face-Detector-1MB
- **Size**: ~1.1MB
- **Input**: 320x240x3 RGB
- **Output**: Bounding boxes + confidence scores

### MediaPipe
- **Source**: https://github.com/google/mediapipe
- **Size**: ~2.5MB  
- **Input**: 128x128x3 RGB
- **Output**: Bounding boxes + landmarks + confidence

### MobileFaceNet
- **Source**: https://github.com/sirius-ai/MobileFaceNet_TF
- **Size**: ~3.8MB
- **Input**: 112x112x3 RGB
- **Output**: 512-dimensional embedding vector

## Download Instructions

1. Download models from respective repositories
2. Convert to TFLite format if needed
3. Place in this directory with exact filenames above
4. Verify models work with `flutter test`

## Model Optimization

For production deployment:
- Use quantized models (INT8) for better performance
- Consider model pruning for size reduction
- Test on target device (Telpo F8) for validation

## Fallback Strategy

If models are missing:
- App will fall back to Google ML Kit (if available)
- Error messages will guide user to download models
- Development mode will use mock detection results

import 'dart:typed_data';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart' as mlkit;

import '../config/face_detection_config.dart';
import '../detection/detection_engine.dart';
import '../detection/hybrid_detector.dart';
import '../detection/engines/mediapipe_engine.dart';
import '../detection/engines/ultraface_engine.dart';

/// Unified face detection provider that can switch between different engines
/// Supports Google ML Kit, MediaPipe, UltraFace, and Hybrid detection
class UnifiedFaceDetectionProvider {
  FaceDetectionConfig _config;
  DetectionEngine? _currentEngine;
  HybridDetector? _hybridDetector;
  mlkit.FaceDetector? _mlKitDetector;
  
  bool _isInitialized = false;
  DateTime _lastSwitchTime = DateTime.now();
  int _failureCount = 0;
  static const int _maxFailures = 3;
  static const Duration _switchCooldown = Duration(seconds: 5);
  
  // Performance tracking
  int _totalDetections = 0;
  double _totalProcessingTime = 0.0;
  DateTime _lastFrameTime = DateTime.now();
  double _currentFPS = 0.0;
  
  UnifiedFaceDetectionProvider({
    FaceDetectionConfig? config,
  }) : _config = config ?? FaceDetectionConfig.getRecommended();
  
  /// Current configuration
  FaceDetectionConfig get config => _config;
  
  /// Whether the provider is initialized
  bool get isInitialized => _isInitialized;
  
  /// Current engine type being used
  FaceDetectionEngineType get currentEngineType => _config.primaryEngine;
  
  /// Current FPS
  double get currentFPS => _currentFPS;
  
  /// Total detections processed
  int get totalDetections => _totalDetections;
  
  /// Average processing time in milliseconds
  double get averageProcessingTime => 
      _totalDetections > 0 ? _totalProcessingTime / _totalDetections : 0.0;
  
  /// Initialize the face detection provider
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing UnifiedFaceDetectionProvider with ${_config.primaryEngine}');
      }
      
      await _initializeEngine(_config.primaryEngine);
      
      _isInitialized = true;
      _failureCount = 0;
      
      if (kDebugMode) {
        print('✅ UnifiedFaceDetectionProvider initialized successfully');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize UnifiedFaceDetectionProvider: $e');
      }
      
      // Try fallback engine if available
      if (_config.enableFallback && _config.fallbackEngine != null) {
        await _switchToFallbackEngine();
      } else {
        rethrow;
      }
    }
  }
  
  /// Detect faces from camera image
  Future<List<DetectedFace>> detectFaces(CameraImage image) async {
    if (!_isInitialized) {
      throw Exception('UnifiedFaceDetectionProvider not initialized');
    }
    
    final startTime = DateTime.now();
    
    try {
      List<DetectedFace> faces = [];
      
      switch (_config.primaryEngine) {
        case FaceDetectionEngineType.mlKit:
          faces = await _detectWithMLKit(image);
          break;
          
        case FaceDetectionEngineType.mediaPipe:
        case FaceDetectionEngineType.ultraFace:
          if (_currentEngine != null) {
            faces = await _currentEngine!.detectFaces(image);
          }
          break;
          
        case FaceDetectionEngineType.hybrid:
          if (_hybridDetector != null) {
            faces = await _hybridDetector!.detectFaces(image);
          }
          break;
      }
      
      // Update performance stats
      _updatePerformanceStats(startTime);
      _failureCount = 0; // Reset failure count on success
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Face detection failed with ${_config.primaryEngine}: $e');
      }
      
      _failureCount++;
      
      // Try fallback if too many failures
      if (_failureCount >= _maxFailures && 
          _config.enableFallback && 
          _config.fallbackEngine != null &&
          DateTime.now().difference(_lastSwitchTime) > _switchCooldown) {
        
        await _switchToFallbackEngine();
        return await detectFaces(image); // Retry with fallback
      }
      
      return []; // Return empty list on failure
    }
  }
  
  /// Detect faces from image bytes
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (!_isInitialized) {
      throw Exception('UnifiedFaceDetectionProvider not initialized');
    }
    
    final startTime = DateTime.now();
    
    try {
      List<DetectedFace> faces = [];
      
      switch (_config.primaryEngine) {
        case FaceDetectionEngineType.mlKit:
          faces = await _detectFromBytesWithMLKit(bytes, width, height);
          break;
          
        case FaceDetectionEngineType.mediaPipe:
        case FaceDetectionEngineType.ultraFace:
          if (_currentEngine != null) {
            faces = await _currentEngine!.detectFacesFromBytes(bytes, width, height);
          }
          break;
          
        case FaceDetectionEngineType.hybrid:
          if (_hybridDetector != null) {
            faces = await _hybridDetector!.detectFacesFromBytes(bytes, width, height);
          }
          break;
      }
      
      // Update performance stats
      _updatePerformanceStats(startTime);
      _failureCount = 0;
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Face detection from bytes failed: $e');
      }
      
      _failureCount++;
      return [];
    }
  }
  
  /// Switch to a different engine
  Future<void> switchEngine(FaceDetectionEngineType newEngine) async {
    if (newEngine == _config.primaryEngine) return;
    
    if (kDebugMode) {
      print('🔄 Switching from ${_config.primaryEngine} to $newEngine');
    }
    
    // Dispose current engine
    await _disposeCurrentEngine();
    
    // Update config
    _config = _config.copyWith(primaryEngine: newEngine);
    
    // Initialize new engine
    await _initializeEngine(newEngine);
    
    _lastSwitchTime = DateTime.now();
    _failureCount = 0;
    
    if (kDebugMode) {
      print('✅ Successfully switched to $newEngine');
    }
  }
  
  /// Update configuration
  Future<void> updateConfig(FaceDetectionConfig newConfig) async {
    final oldEngine = _config.primaryEngine;
    _config = newConfig;
    
    // Reinitialize if engine changed
    if (oldEngine != newConfig.primaryEngine) {
      await _disposeCurrentEngine();
      await _initializeEngine(newConfig.primaryEngine);
    } else {
      // Update existing engine configuration
      await _updateEngineConfig();
    }
  }
  
  /// Get current engine information
  Map<String, dynamic> getEngineInfo() {
    final info = <String, dynamic>{
      'currentEngine': _config.primaryEngine.displayName,
      'fallbackEngine': _config.fallbackEngine?.displayName,
      'isInitialized': _isInitialized,
      'failureCount': _failureCount,
      'totalDetections': _totalDetections,
      'averageProcessingTime': averageProcessingTime,
      'currentFPS': _currentFPS,
    };
    
    // Add engine-specific info
    switch (_config.primaryEngine) {
      case FaceDetectionEngineType.hybrid:
        if (_hybridDetector != null) {
          info.addAll(_hybridDetector!.getEngineInfo());
        }
        break;
      case FaceDetectionEngineType.mediaPipe:
      case FaceDetectionEngineType.ultraFace:
        if (_currentEngine != null) {
          final stats = _currentEngine!.getStats();
          info['engineStats'] = {
            'name': stats.engineName,
            'memoryUsage': stats.memoryUsageMB,
            'framesProcessed': stats.totalFramesProcessed,
          };
        }
        break;
      case FaceDetectionEngineType.mlKit:
        info['mlKitInitialized'] = _mlKitDetector != null;
        break;
    }
    
    return info;
  }
  
  /// Dispose the provider and cleanup resources
  Future<void> dispose() async {
    await _disposeCurrentEngine();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ UnifiedFaceDetectionProvider disposed');
    }
  }
  
  // Private helper methods
  
  Future<void> _initializeEngine(FaceDetectionEngineType engineType) async {
    switch (engineType) {
      case FaceDetectionEngineType.mlKit:
        await _initializeMLKit();
        break;
        
      case FaceDetectionEngineType.mediaPipe:
        _currentEngine = MediaPipeEngine();
        await _configureEngine(_currentEngine!);
        await _currentEngine!.initialize();
        break;
        
      case FaceDetectionEngineType.ultraFace:
        _currentEngine = UltraFaceEngine();
        await _configureEngine(_currentEngine!);
        await _currentEngine!.initialize();
        break;
        
      case FaceDetectionEngineType.hybrid:
        _hybridDetector = await HybridDetector.createForTelpoF8(
          confidenceThreshold: _config.confidenceThreshold,
          maxFaces: _config.maxFaces,
          enableFallback: _config.enableFallback,
        );
        break;
    }
  }
  
  Future<void> _initializeMLKit() async {
    final options = mlkit.FaceDetectorOptions(
      enableLandmarks: _config.enableLandmarks,
      enableTracking: _config.enableTracking,
      enableClassification: true,
      performanceMode: _config.performanceMode == PerformanceMode.maxAccuracy
          ? mlkit.FaceDetectorMode.accurate
          : mlkit.FaceDetectorMode.fast,
    );
    
    _mlKitDetector = mlkit.FaceDetector(options: options);
  }

  Future<void> _configureEngine(DetectionEngine engine) async {
    if (engine is MediaPipeEngine) {
      engine.configure(
        confidenceThreshold: _config.confidenceThreshold,
        maxFaces: _config.maxFaces,
        enableLandmarks: _config.enableLandmarks,
      );
    } else if (engine is UltraFaceEngine) {
      engine.configure(
        confidenceThreshold: _config.confidenceThreshold,
        maxFaces: _config.maxFaces,
      );
    }
  }

  Future<void> _updateEngineConfig() async {
    if (_currentEngine != null) {
      await _configureEngine(_currentEngine!);
    }

    if (_hybridDetector != null) {
      _hybridDetector!.configure(
        confidenceThreshold: _config.confidenceThreshold,
        maxFaces: _config.maxFaces,
        enableFallback: _config.enableFallback,
      );
    }
  }

  Future<List<DetectedFace>> _detectWithMLKit(CameraImage image) async {
    if (_mlKitDetector == null) return [];

    // Convert CameraImage to InputImage for ML Kit
    final inputImage = _convertCameraImageToInputImage(image);
    if (inputImage == null) return [];

    final mlKitFaces = await _mlKitDetector!.processImage(inputImage);

    // Convert ML Kit faces to DetectedFace
    return mlKitFaces.map((face) => _convertMLKitFace(face)).toList();
  }

  Future<List<DetectedFace>> _detectFromBytesWithMLKit(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (_mlKitDetector == null) return [];

    // Create InputImage from bytes
    final inputImage = mlkit.InputImage.fromBytes(
      bytes: bytes,
      metadata: mlkit.InputImageMetadata(
        size: Size(width.toDouble(), height.toDouble()),
        rotation: mlkit.InputImageRotation.rotation0deg,
        format: mlkit.InputImageFormat.yuv420,
        bytesPerRow: width,
      ),
    );

    final mlKitFaces = await _mlKitDetector!.processImage(inputImage);
    return mlKitFaces.map((face) => _convertMLKitFace(face)).toList();
  }

  mlkit.InputImage? _convertCameraImageToInputImage(CameraImage image) {
    try {
      return mlkit.InputImage.fromBytes(
        bytes: _concatenatePlanes(image.planes),
        metadata: mlkit.InputImageMetadata(
          size: Size(image.width.toDouble(), image.height.toDouble()),
          rotation: mlkit.InputImageRotation.rotation0deg,
          format: mlkit.InputImageFormat.yuv420,
          bytesPerRow: image.planes[0].bytesPerRow,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to convert CameraImage to InputImage: $e');
      }
      return null;
    }
  }

  Uint8List _concatenatePlanes(List<Plane> planes) {
    final WriteBuffer allBytes = WriteBuffer();
    for (final Plane plane in planes) {
      allBytes.putUint8List(plane.bytes);
    }
    return allBytes.done().buffer.asUint8List();
  }

  DetectedFace _convertMLKitFace(mlkit.Face face) {
    // Convert ML Kit landmarks to our format
    List<FaceLandmark>? landmarks;
    if (_config.enableLandmarks && face.landmarks.isNotEmpty) {
      landmarks = <FaceLandmark>[];
      for (final entry in face.landmarks.entries) {
        try {
          final type = _convertMLKitLandmarkType(entry.key);
          final landmark = entry.value;
          final position = landmark?.position;
          if (position != null) {
            landmarks.add(FaceLandmark(
              type: type,
              position: Offset(position.x.toDouble(), position.y.toDouble()),
            ));
          }
        } catch (e) {
          // Skip invalid landmarks
          if (kDebugMode) {
            print('⚠️ Skipping invalid landmark: $e');
          }
        }
      }
    }

    return DetectedFace(
      boundingBox: Rect(
        left: face.boundingBox.left.toDouble(),
        top: face.boundingBox.top.toDouble(),
        width: face.boundingBox.width.toDouble(),
        height: face.boundingBox.height.toDouble(),
      ),
      confidence: 1.0, // ML Kit doesn't provide confidence score
      landmarks: landmarks,
      headEulerAngleY: face.headEulerAngleY,
      headEulerAngleZ: face.headEulerAngleZ,
      leftEyeOpenProbability: face.leftEyeOpenProbability,
      rightEyeOpenProbability: face.rightEyeOpenProbability,
      smilingProbability: face.smilingProbability,
      trackingId: face.trackingId,
    );
  }

  FaceLandmarkType _convertMLKitLandmarkType(mlkit.FaceLandmarkType mlKitType) {
    switch (mlKitType) {
      case mlkit.FaceLandmarkType.leftEye:
        return FaceLandmarkType.leftEye;
      case mlkit.FaceLandmarkType.rightEye:
        return FaceLandmarkType.rightEye;
      case mlkit.FaceLandmarkType.noseBase:
        return FaceLandmarkType.noseBase;
      case mlkit.FaceLandmarkType.leftEar:
        return FaceLandmarkType.leftEar;
      case mlkit.FaceLandmarkType.rightEar:
        return FaceLandmarkType.rightEar;
      case mlkit.FaceLandmarkType.leftMouth:
        return FaceLandmarkType.leftMouth;
      case mlkit.FaceLandmarkType.rightMouth:
        return FaceLandmarkType.rightMouth;
      case mlkit.FaceLandmarkType.leftCheek:
        return FaceLandmarkType.leftCheek;
      case mlkit.FaceLandmarkType.rightCheek:
        return FaceLandmarkType.rightCheek;
      default:
        return FaceLandmarkType.leftEye; // Fallback
    }
  }

  Future<void> _switchToFallbackEngine() async {
    if (_config.fallbackEngine == null) return;

    if (kDebugMode) {
      print('🔄 Switching to fallback engine: ${_config.fallbackEngine}');
    }

    final oldEngine = _config.primaryEngine;
    await _disposeCurrentEngine();

    _config = _config.copyWith(
      primaryEngine: _config.fallbackEngine!,
      fallbackEngine: oldEngine,
    );

    await _initializeEngine(_config.primaryEngine);
    _lastSwitchTime = DateTime.now();
    _failureCount = 0;
  }

  Future<void> _disposeCurrentEngine() async {
    if (_currentEngine != null) {
      await _currentEngine!.dispose();
      _currentEngine = null;
    }

    if (_hybridDetector != null) {
      await _hybridDetector!.dispose();
      _hybridDetector = null;
    }

    if (_mlKitDetector != null) {
      await _mlKitDetector!.close();
      _mlKitDetector = null;
    }
  }

  void _updatePerformanceStats(DateTime startTime) {
    final processingTime = DateTime.now().difference(startTime).inMicroseconds / 1000.0;

    _totalDetections++;
    _totalProcessingTime += processingTime;

    // Calculate FPS
    final now = DateTime.now();
    final timeDiff = now.difference(_lastFrameTime).inMicroseconds / 1000000.0;
    if (timeDiff > 0) {
      _currentFPS = 1.0 / timeDiff;
    }
    _lastFrameTime = now;
  }
}

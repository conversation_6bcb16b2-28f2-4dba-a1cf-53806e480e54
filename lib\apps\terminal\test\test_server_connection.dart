import 'dart:io';
import 'package:secure_comm/secure_comm.dart';
import '../services/system_info_service.dart';

/// Test script for testing server connection and communication
void main() async {
  print('🔐 Testing Terminal App Server Connection');
  print('==========================================\n');

  // Test 1: Basic HTTP connectivity
  await testBasicConnectivity();
  
  // Test 2: Device registration
  await testDeviceRegistration();
  
  // Test 3: System info collection
  await testSystemInfoCollection();
  
  // Test 4: Heartbeat functionality
  await testHeartbeat();
  
  print('\n✅ All tests completed!');
}

/// Test basic HTTP connectivity to server
Future<void> testBasicConnectivity() async {
  print('📡 Test 1: Basic HTTP Connectivity');
  print('----------------------------------');
  
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://localhost:3000'));
    final response = await request.close();
    
    if (response.statusCode == 200) {
      print('✅ Server is reachable at http://localhost:3000');
      print('   Status Code: ${response.statusCode}');
    } else {
      print('❌ Server returned status code: ${response.statusCode}');
    }
    
    client.close();
  } catch (e) {
    print('❌ Failed to connect to server: $e');
    print('   Make sure the test server is running on port 3000');
  }
  
  print('');
}

/// Test device registration with server
Future<void> testDeviceRegistration() async {
  print('📝 Test 2: Device Registration');
  print('------------------------------');
  
  try {
    // Create transport and secure communication
    final transport = HttpTransport('http://localhost:3000');
    final secureComm = SecureComm(
      deviceId: 'test-terminal-001',
      deviceType: 'face_terminal',
      transport: transport,
      deviceName: 'Test Terminal Device',
      capabilities: ['face_auth', 'relay_control', 'heartbeat'],
      metadata: {
        'location': 'Test Lab',
        'version': '1.0.0',
        'test_mode': true,
      },
    );
    
    print('🔄 Attempting device registration...');
    await secureComm.registerDevice();
    
    print('✅ Device registration successful!');
    print('   Device ID: ${secureComm.deviceId}');
    print('   Authenticated: ${secureComm.isAuthenticated}');
    print('   Scopes: ${secureComm.scopes}');
    
    // Test sending a message
    print('🔄 Testing message sending...');
    final response = await secureComm.sendMessage(
      type: 'test_message',
      payload: {
        'message': 'Hello from terminal app!',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    print('✅ Message sent successfully!');
    print('   Response success: ${response.success}');
    if (response.data != null) {
      print('   Response data: ${response.data}');
    }
    
    // Clean up
    await secureComm.dispose();
    
  } catch (e) {
    print('❌ Device registration failed: $e');
  }
  
  print('');
}

/// Test system info collection
Future<void> testSystemInfoCollection() async {
  print('💻 Test 3: System Info Collection');
  print('----------------------------------');
  
  try {
    final systemInfoService = SystemInfoService();
    print('🔄 Collecting system information...');
    
    final systemInfo = await systemInfoService.getSystemInfo();
    
    print('✅ System info collected successfully!');
    print('   Platform: ${systemInfo['platform']}');
    print('   Memory Total: ${systemInfo['memory_total']}');
    print('   Memory Used: ${systemInfo['memory_used']}');
    print('   Memory Usage: ${systemInfo['memory_usage_percent']}%');
    print('   CPU Usage: ${systemInfo['cpu_usage_percent']}%');
    print('   CPU Cores: ${systemInfo['cpu_cores']}');
    print('   Device Name: ${systemInfo['device_name']}');
    print('   OS Version: ${systemInfo['os_version']}');
    print('   Battery Level: ${systemInfo['battery_level']}%');
    print('   Network Available: ${systemInfo['network_available']}');
    
  } catch (e) {
    print('❌ System info collection failed: $e');
  }
  
  print('');
}

/// Test heartbeat functionality
Future<void> testHeartbeat() async {
  print('💓 Test 4: Heartbeat Functionality');
  print('-----------------------------------');
  
  try {
    // Create transport and secure communication
    final transport = HttpTransport('http://localhost:3000');
    final secureComm = SecureComm(
      deviceId: 'test-terminal-heartbeat',
      deviceType: 'face_terminal',
      transport: transport,
      deviceName: 'Test Heartbeat Device',
      capabilities: ['heartbeat'],
      metadata: {
        'location': 'Test Lab',
        'test_mode': true,
      },
    );
    
    print('🔄 Registering device for heartbeat test...');
    await secureComm.registerDevice();
    
    print('✅ Device registered for heartbeat test');
    
    // Get system info for heartbeat
    final systemInfoService = SystemInfoService();
    final systemInfo = await systemInfoService.getSystemInfo();
    
    // Add device-specific info
    systemInfo['status'] = 'active';
    systemInfo['uptime'] = 120; // 2 minutes
    systemInfo['device_id'] = secureComm.deviceId;
    systemInfo['device_type'] = 'face_terminal';
    systemInfo['location'] = 'Test Lab';
    
    print('🔄 Sending heartbeat...');
    await secureComm.sendHeartbeat(systemInfo: systemInfo);
    
    print('✅ Heartbeat sent successfully!');
    
    // Send multiple heartbeats to test consistency
    print('🔄 Sending additional heartbeats...');
    for (int i = 1; i <= 3; i++) {
      await Future.delayed(const Duration(seconds: 1));
      systemInfo['uptime'] = 120 + i;
      systemInfo['heartbeat_sequence'] = i;
      
      await secureComm.sendHeartbeat(systemInfo: systemInfo);
      print('   Heartbeat $i sent');
    }
    
    print('✅ All heartbeats sent successfully!');
    
    // Clean up
    await secureComm.dispose();
    
  } catch (e) {
    print('❌ Heartbeat test failed: $e');
  }
  
  print('');
}

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_blue_classic/flutter_blue_classic.dart';
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// A relay controller that uses Flutter Blue Classic communication.
///
/// This controller uses the `flutter_blue_classic` package which is actively
/// maintained and based on flutter_bluetooth_serial but with fixes for newer Android.
///
/// Example usage:
/// ```dart
/// final controller = FlutterBlueClassicController(
///   deviceAddress: '00:11:22:33:44:55',
/// );
///
/// await controller.connect();
/// await controller.triggerOn();
/// await controller.triggerOff();
/// await controller.dispose();
/// ```
class FlutterBlueClassicController extends RelayController {
  /// The Bluetooth device address (MAC address).
  final String deviceAddress;

  /// Custom command to send for turning the relay ON.
  /// Defaults to "ON\n".
  final String onCommand;

  /// Custom command to send for turning the relay OFF.
  /// Defaults to "OFF\n".
  final String offCommand;

  /// Connection timeout in seconds.
  final int timeoutSeconds;

  /// Whether to use fine location access for scanning.
  final bool usesFineLocation;

  static FlutterBlueClassic? _flutterBlueClassic;
  BluetoothConnection? _connection;
  bool _isConnected = false;

  /// Creates a new [FlutterBlueClassicController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [deviceAddress] is the MAC address of the Bluetooth device.
  /// [deviceName] is the device name/description.
  /// [onCommand] is the command sent to turn the relay ON (default: "ON\n").
  /// [offCommand] is the command sent to turn the relay OFF (default: "OFF\n").
  /// [timeoutSeconds] is the connection timeout in seconds (default: 10).
  /// [usesFineLocation] whether to use fine location for scanning (default: false).
  FlutterBlueClassicController({
    required super.deviceId,
    required this.deviceAddress,
    super.deviceName = 'Flutter Blue Classic Relay',
    this.onCommand = 'ON\n',
    this.offCommand = 'OFF\n',
    this.timeoutSeconds = 10,
    this.usesFineLocation = false,
  }) {
    _flutterBlueClassic ??= FlutterBlueClassic(usesFineLocation: usesFineLocation);
  }

  /// Gets the FlutterBlueClassic instance.
  FlutterBlueClassic get _bluetooth => _flutterBlueClassic!;

  /// Connects to the Bluetooth device.
  /// 
  /// This method must be called before using [triggerOn] or [triggerOff].
  /// 
  /// Throws [BluetoothRelayException] if connection fails.
  Future<void> connect() async {
    try {
      if (_isConnected) {
        return; // Already connected
      }

      // Check if Bluetooth is supported and enabled
      if (!await _bluetooth.isSupported) {
        throw const BluetoothRelayException('Bluetooth is not supported on this device');
      }

      if (!await _bluetooth.isEnabled) {
        throw const BluetoothRelayException('Bluetooth is not enabled');
      }

      // Find the device in bonded devices
      final bondedDevices = await _bluetooth.bondedDevices;
      if (bondedDevices == null) {
        throw const BluetoothRelayException('Could not get bonded devices');
      }

      final targetDevice = bondedDevices.firstWhere(
        (device) => device.address == deviceAddress,
        orElse: () => throw BluetoothRelayException(
          'Device $deviceAddress not found in bonded devices. Please pair the device first.'
        ),
      );

      // Connect to the device using device address string
      _connection = await _bluetooth.connect(deviceAddress)
          .timeout(Duration(seconds: timeoutSeconds));
      
      _isConnected = true;
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to connect to Bluetooth device', e);
    }
  }

  /// Disconnects from the Bluetooth device.
  Future<void> disconnect() async {
    try {
      if (_connection != null && _isConnected) {
        await _connection!.close();
        _connection = null;
        _isConnected = false;
      }
    } catch (e) {
      throw BluetoothRelayException('Failed to disconnect from Bluetooth device', e);
    }
  }

  /// Sends a command to the Bluetooth device.
  Future<void> _sendCommand(String command) async {
    try {
      if (!_isConnected || _connection == null) {
        throw const BluetoothRelayException('Not connected to Bluetooth device');
      }

      // Send command as UTF-8 encoded bytes
      _connection!.writeString(command);
    } catch (e) {
      if (e is BluetoothRelayException) rethrow;
      throw BluetoothRelayException('Failed to send command: $command', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendCommand(onCommand);
  }

  @override
  Future<void> triggerOff() async {
    await _sendCommand(offCommand);
  }

  @override
  Future<void> dispose() async {
    await disconnect();
    await super.dispose();
  }

  /// Gets the connection status.
  bool get isConnected => _isConnected;

  /// Gets the input stream for receiving data from the device.
  Stream<Uint8List>? get inputStream => _connection?.input;

  /// Gets available bonded Bluetooth devices.
  ///
  /// Returns a list of bonded (paired) Bluetooth devices.
  static Future<List<BluetoothDevice>> getAvailableDevices({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      final devices = await bluetooth.bondedDevices;
      return devices ?? [];
    } catch (e) {
      throw BluetoothRelayException('Failed to get available devices', e);
    }
  }

  /// Scans for nearby Bluetooth devices.
  ///
  /// Returns a stream of discovered devices.
  static Stream<BluetoothDevice> scanForDevices({bool usesFineLocation = false}) {
    final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
    return bluetooth.scanResults;
  }

  /// Starts scanning for Bluetooth devices.
  static Future<void> startScan({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      bluetooth.startScan();
    } catch (e) {
      throw BluetoothRelayException('Failed to start scan', e);
    }
  }

  /// Stops scanning for Bluetooth devices.
  static Future<void> stopScan({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      bluetooth.stopScan();
    } catch (e) {
      throw BluetoothRelayException('Failed to stop scan', e);
    }
  }

  /// Checks if Bluetooth is supported on this device.
  static Future<bool> isBluetoothSupported({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      return await bluetooth.isSupported;
    } catch (e) {
      return false;
    }
  }

  /// Checks if Bluetooth is enabled on the device.
  static Future<bool> isBluetoothEnabled({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      return await bluetooth.isEnabled;
    } catch (e) {
      return false;
    }
  }

  /// Requests to enable Bluetooth.
  static Future<void> requestEnableBluetooth({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      bluetooth.turnOn();
    } catch (e) {
      throw BluetoothRelayException('Failed to enable Bluetooth', e);
    }
  }

  /// Creates a bond (pair) with a Bluetooth device.
  static Future<void> bondDevice(BluetoothDevice device, {bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      bluetooth.bondDevice(device.address);
    } catch (e) {
      throw BluetoothRelayException('Failed to bond device', e);
    }
  }

  /// Gets the current Bluetooth adapter state.
  static Future<BluetoothAdapterState> getAdapterState({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      return await bluetooth.adapterStateNow;
    } catch (e) {
      return BluetoothAdapterState.unknown;
    }
  }

  /// Stream of Bluetooth adapter state changes.
  static Stream<BluetoothAdapterState> adapterStateStream({bool usesFineLocation = false}) {
    final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
    return bluetooth.adapterState;
  }

  /// Checks if currently scanning for devices.
  static Future<bool> isScanning({bool usesFineLocation = false}) async {
    try {
      final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
      return await bluetooth.isScanningNow;
    } catch (e) {
      return false;
    }
  }

  /// Stream of scanning state changes.
  static Stream<bool> scanningStateStream({bool usesFineLocation = false}) {
    final bluetooth = FlutterBlueClassic(usesFineLocation: usesFineLocation);
    return bluetooth.isScanning;
  }
}

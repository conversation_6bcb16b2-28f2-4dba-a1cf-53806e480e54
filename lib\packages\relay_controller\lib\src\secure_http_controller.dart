import 'dart:convert';
import 'package:http/http.dart' as http;
import 'relay_controller_base.dart';
import 'exceptions.dart';
import 'security/device_auth.dart';

/// A secure HTTP relay controller with JWT authentication and HMAC signing
/// 
/// This controller implements the secure communication protocol with:
/// - Device registration and provisioning
/// - JWT token authentication
/// - HMAC request signing
/// - Replay attack prevention
/// 
/// Example usage:
/// ```dart
/// final controller = SecureHttpRelayController(
///   deviceId: 'relay-001',
///   deviceType: 'flutter_app',
///   serverBaseUrl: 'https://api.example.com',
/// );
/// 
/// // Register device and get credentials
/// await controller.registerDevice();
/// 
/// // Control relay securely
/// await controller.triggerOn();
/// await controller.triggerOff();
/// ```
class SecureHttpRelayController extends RelayController {
  /// Server base URL
  final String serverBaseUrl;

  /// Device type identifier
  final String deviceType;

  /// Hardware hash for device fingerprinting
  final String? hardwareHash;

  /// App version
  final String? appVersion;

  /// Request timeout in seconds
  final int timeoutSeconds;

  /// Custom headers
  final Map<String, String> headers;

  // Authentication state
  String? _accessToken;
  String? _refreshToken;
  String? _secretKey;
  String? _relayEndpoint;
  List<String> _deviceScope = [];
  DateTime? _tokenExpiry;

  /// HTTP client instance
  final http.Client _client = http.Client();

  /// Creates a new [SecureHttpRelayController].
  /// 
  /// [deviceId] is the unique device identifier.
  /// [deviceType] is the type of device (e.g., 'flutter_app', 'iot_device').
  /// [serverBaseUrl] is the base URL of the server.
  /// [deviceName] is the human-readable device name.
  /// [hardwareHash] is an optional hardware fingerprint.
  /// [appVersion] is the application version.
  /// [timeoutSeconds] is the request timeout in seconds.
  /// [headers] are additional headers to include in requests.
  SecureHttpRelayController({
    required super.deviceId,
    required this.deviceType,
    required this.serverBaseUrl,
    super.deviceName = 'Secure Relay Device',
    this.hardwareHash,
    this.appVersion,
    this.timeoutSeconds = 30,
    this.headers = const {},
  });

  /// Register device with the server and obtain credentials
  @override
  Future<void> registerDevice() async {
    try {
      final request = DeviceRegistrationRequest(
        deviceId: deviceId,
        deviceType: deviceType,
        hardwareHash: hardwareHash ?? DeviceAuth.generateHardwareHash(),
        deviceName: deviceName,
        appVersion: appVersion,
      );

      final response = await _sendRequest(
        'POST',
        '/api/device/register',
        body: jsonEncode(request.toJson()),
        requireAuth: false,
      );

      final registrationResponse = DeviceRegistrationResponse.fromJson(
        jsonDecode(response.body) as Map<String, dynamic>,
      );

      // Store credentials
      _accessToken = registrationResponse.accessToken;
      _refreshToken = registrationResponse.refreshToken;
      _secretKey = registrationResponse.secretKey;
      _relayEndpoint = registrationResponse.relayEndpoint;
      _deviceScope = registrationResponse.deviceScope;
      
      // Calculate token expiry
      _tokenExpiry = DateTime.now().add(
        Duration(seconds: registrationResponse.expiresIn),
      );

    } catch (e) {
      throw HttpRelayException('Failed to register device', e);
    }
  }

  /// Refresh access token using refresh token
  Future<void> refreshAccessToken() async {
    if (_refreshToken == null) {
      throw const HttpRelayException('No refresh token available');
    }

    try {
      final response = await _sendRequest(
        'POST',
        '/api/device/refresh',
        body: jsonEncode({'refresh_token': _refreshToken}),
        requireAuth: false,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      _accessToken = data['access_token'] as String;
      
      final expiresIn = data['expires_in'] as int;
      _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn));

    } catch (e) {
      throw HttpRelayException('Failed to refresh access token', e);
    }
  }

  /// Check if access token is expired or about to expire
  bool get _isTokenExpired {
    if (_tokenExpiry == null) return true;
    
    // Consider token expired if it expires within 5 minutes
    const buffer = Duration(minutes: 5);
    return DateTime.now().add(buffer).isAfter(_tokenExpiry!);
  }

  /// Ensure we have a valid access token
  Future<void> _ensureValidToken() async {
    if (_accessToken == null) {
      throw const HttpRelayException('Device not registered. Call registerDevice() first.');
    }

    if (_isTokenExpired) {
      await refreshAccessToken();
    }
  }

  /// Create a secure relay command
  SecureRelayCommand _createSecureCommand(String action) {
    if (_secretKey == null) {
      throw const HttpRelayException('No secret key available for signing');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    
    return SecureRelayCommand(
      deviceId: deviceId,
      action: action,
      timestamp: timestamp,
    ).withSignature(_secretKey!);
  }

  /// Send secure relay control command
  Future<void> _sendSecureRelayCommand(String action) async {
    await _ensureValidToken();

    if (!_deviceScope.contains('relay_control') && !_deviceScope.contains('unlock')) {
      throw const HttpRelayException('Device does not have relay control permissions');
    }

    try {
      final command = _createSecureCommand(action);
      final endpoint = _relayEndpoint ?? '/relay/control';

      await _sendRequest(
        'POST',
        endpoint,
        body: jsonEncode(command.toJson()),
        requireAuth: true,
      );

    } catch (e) {
      throw HttpRelayException('Failed to send secure relay command: $action', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendSecureRelayCommand('unlock'); // or 'on'
  }

  @override
  Future<void> triggerOff() async {
    await _sendSecureRelayCommand('lock'); // or 'off'
  }

  @override
  Future<bool?> getStatus() async {
    await _ensureValidToken();

    try {
      final response = await _sendRequest(
        'GET',
        '/relay/status?device_id=$deviceId',
        requireAuth: true,
      );

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      final status = data['status'] as String?;
      
      if (status == null) return null;
      
      return status.toLowerCase() == 'unlocked' || 
             status.toLowerCase() == 'on' || 
             status.toLowerCase() == 'true';

    } catch (e) {
      throw HttpRelayException('Failed to get device status', e);
    }
  }

  /// Send HTTP request with authentication and error handling
  Future<http.Response> _sendRequest(
    String method,
    String endpoint,
    {
    String? body,
    bool requireAuth = true,
  }) async {
    final url = Uri.parse('$serverBaseUrl$endpoint');
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      'User-Agent': 'RelayController/1.0 ($deviceType)',
      ...headers,
    };

    // Add authorization header if required
    if (requireAuth && _accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $_accessToken';
    }

    try {
      http.Response response;
      final timeout = Duration(seconds: timeoutSeconds);

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(url, headers: requestHeaders).timeout(timeout);
          break;
        case 'POST':
          response = await _client.post(url, headers: requestHeaders, body: body).timeout(timeout);
          break;
        case 'PUT':
          response = await _client.put(url, headers: requestHeaders, body: body).timeout(timeout);
          break;
        case 'DELETE':
          response = await _client.delete(url, headers: requestHeaders).timeout(timeout);
          break;
        default:
          throw HttpRelayException('Unsupported HTTP method: $method');
      }

      // Check for HTTP errors
      if (response.statusCode >= 400) {
        String errorMessage = 'HTTP ${response.statusCode}';
        
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = errorData['error'] as String? ?? errorMessage;
        } catch (_) {
          // Use default error message if response is not JSON
        }

        throw HttpRelayException(errorMessage);
      }

      return response;

    } catch (e) {
      if (e is HttpRelayException) {
        rethrow;
      }
      throw HttpRelayException('Network request failed', e);
    }
  }

  /// Get current authentication status
  bool get isAuthenticated => _accessToken != null && !_isTokenExpired;

  /// Get device scope/permissions
  List<String> get deviceScope => List.unmodifiable(_deviceScope);

  /// Get token expiry time
  DateTime? get tokenExpiry => _tokenExpiry;

  /// Revoke device credentials (logout)
  Future<void> revokeCredentials() async {
    if (_accessToken != null) {
      try {
        await _sendRequest(
          'POST',
          '/api/device/revoke',
          body: jsonEncode({'access_token': _accessToken}),
          requireAuth: true,
        );
      } catch (e) {
        // Continue with local cleanup even if server revocation fails
      }
    }

    // Clear local credentials
    _accessToken = null;
    _refreshToken = null;
    _secretKey = null;
    _relayEndpoint = null;
    _deviceScope = [];
    _tokenExpiry = null;
  }

  @override
  Future<void> dispose() async {
    _client.close();
    await super.dispose();
  }
}

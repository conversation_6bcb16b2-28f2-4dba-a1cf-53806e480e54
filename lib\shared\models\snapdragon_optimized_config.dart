import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'camera_config.dart';

/// Optimized configurations for mobile face capture performance
/// These settings balance accuracy with performance for smooth face capture experience
/// Compatible with Android and iOS devices
class MobileFaceCaptureOptimizedConfig {
  
  /// High performance configuration for mobile devices
  /// Prioritizes speed and responsiveness over accuracy
  static const FaceDetectionConfig highPerformance = FaceDetectionConfig(
    frameSkip: 6, // Moderate frame skipping for better compatibility
    minDetectionInterval: Duration(milliseconds: 250), // Balanced interval
    minFaceSize: 0.06, // Reasonable minimum face size
    minFaceArea: 10000, // Balanced minimum area
    idealFaceArea: 42000, // Balanced ideal area
    qualityThreshold: 0.45, // Balanced quality threshold
    maxYawAngle: 30.0, // Standard angle detection
    maxRollAngle: 20.0,
    centerAngleTolerance: 10.0,
    directionAngleTolerance: 15.0,
  );

  /// Balanced configuration for mobile devices
  /// Good balance between performance and accuracy
  static const FaceDetectionConfig balanced = FaceDetectionConfig(
    frameSkip: 4, // Optimized for mobile performance
    minDetectionInterval: Duration(milliseconds: 180), // Faster response
    minFaceSize: 0.08,
    minFaceArea: 12000,
    idealFaceArea: 45000,
    qualityThreshold: 0.5,
    maxYawAngle: 30.0,
    maxRollAngle: 20.0,
    centerAngleTolerance: 10.0,
    directionAngleTolerance: 15.0,
  );

  /// High accuracy configuration for mobile devices
  /// Better accuracy but may be slower
  static const FaceDetectionConfig highAccuracy = FaceDetectionConfig(
    frameSkip: 3, // Less frame skipping for more accuracy
    minDetectionInterval: Duration(milliseconds: 160), // Balanced interval
    minFaceSize: 0.09, // Slightly relaxed for mobile
    minFaceArea: 14000, // Balanced area
    idealFaceArea: 48000, // Balanced ideal area
    qualityThreshold: 0.55, // Balanced quality
    maxYawAngle: 25.0,
    maxRollAngle: 15.0,
    centerAngleTolerance: 8.0,
    directionAngleTolerance: 12.0,
  );

  /// Camera configuration optimized for mobile devices
  static const CameraConfig optimizedCamera = CameraConfig(
    preferredLensDirection: CameraLensDirection.front,
    resolution: ResolutionPreset.medium, // 720p for balanced performance
    enableImageStream: true,
    pauseStreamForCapture: false, // Keep stream active for smoother experience
    imageFormatGroup: ImageFormatGroup.yuv420, // Efficient format for mobile
    enableAudio: false,
  );

  /// High performance camera config (prioritize speed)
  static const CameraConfig highPerformanceCamera = CameraConfig(
    preferredLensDirection: CameraLensDirection.front,
    resolution: ResolutionPreset.low, // 480p for maximum performance
    enableImageStream: true,
    pauseStreamForCapture: false,
    imageFormatGroup: ImageFormatGroup.yuv420,
    enableAudio: false,
  );

  /// High quality camera config (better image quality)
  static const CameraConfig highQualityCamera = CameraConfig(
    preferredLensDirection: CameraLensDirection.front,
    resolution: ResolutionPreset.high, // 1080p for better quality
    enableImageStream: true,
    pauseStreamForCapture: false,
    imageFormatGroup: ImageFormatGroup.yuv420,
    enableAudio: false,
  );

  /// ML Kit options optimized for mobile devices
  static FaceDetectorOptions getOptimizedMLKitOptions({
    PerformanceLevel performanceLevel = PerformanceLevel.balanced,
  }) {
    switch (performanceLevel) {
      case PerformanceLevel.highPerformance:
        return FaceDetectorOptions(
          performanceMode: FaceDetectorMode.fast,
          enableTracking: false, // Disable for better performance
          enableLandmarks: false, // Disable for better performance
          enableContours: false, // Disable for better performance
          enableClassification: false, // Disable for better performance
          minFaceSize: 0.05,
        );

      case PerformanceLevel.balanced:
        return FaceDetectorOptions(
          performanceMode: FaceDetectorMode.fast,
          enableTracking: true, // Enable for better user experience
          enableLandmarks: false, // Keep disabled for performance
          enableContours: false, // Keep disabled for performance
          enableClassification: true, // Enable for smile detection
          minFaceSize: 0.08,
        );

      case PerformanceLevel.highAccuracy:
        return FaceDetectorOptions(
          performanceMode: FaceDetectorMode.accurate,
          enableTracking: true,
          enableLandmarks: false, // Still keep disabled for performance
          enableContours: false, // Still keep disabled for performance
          enableClassification: true,
          minFaceSize: 0.1,
        );
    }
  }

  /// Get recommended configuration based on device performance
  static ConfigurationSet getRecommendedConfig({
    PerformanceLevel performanceLevel = PerformanceLevel.balanced,
  }) {
    switch (performanceLevel) {
      case PerformanceLevel.highPerformance:
        return ConfigurationSet(
          faceDetection: highPerformance,
          camera: highPerformanceCamera,
          mlKit: getOptimizedMLKitOptions(performanceLevel: performanceLevel),
        );
      
      case PerformanceLevel.balanced:
        return ConfigurationSet(
          faceDetection: balanced,
          camera: optimizedCamera,
          mlKit: getOptimizedMLKitOptions(performanceLevel: performanceLevel),
        );
      
      case PerformanceLevel.highAccuracy:
        return ConfigurationSet(
          faceDetection: highAccuracy,
          camera: highQualityCamera,
          mlKit: getOptimizedMLKitOptions(performanceLevel: performanceLevel),
        );
    }
  }

  /// Auto-detect optimal configuration based on runtime performance
  static ConfigurationSet getAdaptiveConfig({
    double currentFPS = 0.0,
    int averageDetectionTime = 0,
    double cpuUsage = 0.0,
  }) {
    // If performance is poor, use high performance config
    if (currentFPS < 15 || averageDetectionTime > 150000) { // > 150ms
      return getRecommendedConfig(performanceLevel: PerformanceLevel.highPerformance);
    }
    
    // If performance is excellent, can use high accuracy
    if (currentFPS > 25 && averageDetectionTime < 80000) { // < 80ms
      return getRecommendedConfig(performanceLevel: PerformanceLevel.highAccuracy);
    }
    
    // Default to balanced
    return getRecommendedConfig(performanceLevel: PerformanceLevel.balanced);
  }

  /// Mobile device specific optimizations
  static Map<String, dynamic> getMobileOptimizations() {
    return {
      'useHardwareAcceleration': true,
      'enableGPUProcessing': true,
      'adaptivePerformance': true, // Adapt to device capabilities
      'crossPlatformOptimization': true, // Android and iOS compatible
      'thermalThrottling': {
        'enableMonitoring': true,
        'maxTemperature': 42.0, // Conservative temperature limit
        'throttleActions': [
          'reduceFrameRate',
          'increaseFrameSkip',
          'lowerDetectionFrequency',
        ],
      },
      'memoryOptimization': {
        'enableBufferPooling': true,
        'maxBufferSize': 1024 * 1024 * 6, // 6MB buffer pool for compatibility
        'enableImageCompression': true,
        'aggressiveCleanup': true, // More aggressive cleanup for smooth dispose
      },
    };
  }
}

/// Performance level enumeration
enum PerformanceLevel {
  highPerformance,
  balanced,
  highAccuracy,
}

/// Configuration set containing all related configurations
class ConfigurationSet {
  final FaceDetectionConfig faceDetection;
  final CameraConfig camera;
  final FaceDetectorOptions mlKit;

  const ConfigurationSet({
    required this.faceDetection,
    required this.camera,
    required this.mlKit,
  });

  /// Create a copy with modified values
  ConfigurationSet copyWith({
    FaceDetectionConfig? faceDetection,
    CameraConfig? camera,
    FaceDetectorOptions? mlKit,
  }) {
    return ConfigurationSet(
      faceDetection: faceDetection ?? this.faceDetection,
      camera: camera ?? this.camera,
      mlKit: mlKit ?? this.mlKit,
    );
  }

  /// Convert to map for debugging
  Map<String, dynamic> toMap() {
    return {
      'faceDetection': {
        'frameSkip': faceDetection.frameSkip,
        'minDetectionInterval': faceDetection.minDetectionInterval.inMilliseconds,
        'qualityThreshold': faceDetection.qualityThreshold,
        'minFaceArea': faceDetection.minFaceArea,
      },
      'camera': {
        'resolution': camera.resolution.toString(),
        'imageFormatGroup': camera.imageFormatGroup.toString(),
        'pauseStreamForCapture': camera.pauseStreamForCapture,
      },
      'mlKit': {
        'performanceMode': mlKit.performanceMode.toString(),
        'enableTracking': mlKit.enableTracking,
        'enableLandmarks': mlKit.enableLandmarks,
        'enableContours': mlKit.enableContours,
        'enableClassification': mlKit.enableClassification,
        'minFaceSize': mlKit.minFaceSize,
      },
    };
  }
}

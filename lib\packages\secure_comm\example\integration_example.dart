import 'package:secure_comm/secure_comm.dart';

/// Integration example showing how to use SecureComm for different scenarios
/// This demonstrates the flexibility of the secure communication system
void main() async {
  print('🔗 SecureComm Integration Examples');
  print('===================================\n');

  await faceTerminalExample();
  await relayControllerExample();
  await iotSensorExample();
  await mobileAppExample();
}

/// Example: Face Recognition Terminal
Future<void> faceTerminalExample() async {
  print('👤 Face Recognition Terminal Example');
  print('-------------------------------------');

  final transport = HttpTransport('http://localhost:3000');
  final comm = SecureComm(
    deviceId: 'face-terminal-001',
    deviceType: 'face_terminal',
    transport: transport,
    deviceName: 'Main Entrance Terminal',
    capabilities: ['face_auth', 'relay_control', 'image_upload'],
    metadata: {
      'location': 'Main Entrance',
      'camera_resolution': '1920x1080',
      'ai_model': 'FaceNet_v2',
    },
  );

  try {
    // Register terminal
    await comm.registerDevice();
    print('✅ Face terminal registered');

    // Simulate face detection and authentication
    print('📷 Capturing face image...');
    
    // Send face for authentication
    final authResponse = await comm.sendFaceAuth(
      faceImageBase64: 'simulated_base64_face_image_data',
      userId: 'detected_user_123',
      metadata: {
        'confidence': 0.95,
        'detection_time': DateTime.now().toIso8601String(),
        'camera_id': 'cam_main_entrance',
      },
    );

    if (authResponse.success) {
      print('✅ Face authentication successful');
      
      // Control door relay
      final relayResponse = await comm.sendRelayControl(
        action: 'unlock',
        relayId: 'main_door',
        metadata: {
          'reason': 'face_auth_success',
          'user_id': 'detected_user_123',
          'auth_confidence': 0.95,
        },
      );

      if (relayResponse.success) {
        print('🚪 Door unlocked successfully');
        
        // Log the access event
        await comm.sendLog(
          level: 'info',
          message: 'Access granted via face recognition',
          category: 'access_control',
          context: {
            'user_id': 'detected_user_123',
            'method': 'face_recognition',
            'confidence': 0.95,
            'location': 'main_entrance',
          },
        );
      }
    } else {
      print('❌ Face authentication failed');
      
      // Log failed attempt
      await comm.sendLog(
        level: 'warning',
        message: 'Face authentication failed',
        category: 'security',
        context: {
          'reason': authResponse.error ?? 'unknown',
          'location': 'main_entrance',
        },
      );
    }

  } catch (e) {
    print('❌ Face terminal error: $e');
  } finally {
    await comm.dispose();
  }

  print('');
}

/// Example: Relay Controller Device
Future<void> relayControllerExample() async {
  print('🔌 Relay Controller Example');
  print('----------------------------');

  final transport = HttpTransport('http://localhost:3000');
  final comm = SecureComm(
    deviceId: 'relay-controller-001',
    deviceType: 'relay_controller',
    transport: transport,
    deviceName: 'Multi-Zone Relay Controller',
    capabilities: ['relay_control', 'status_monitoring', 'config_update'],
    metadata: {
      'zones': ['zone_1', 'zone_2', 'zone_3', 'zone_4'],
      'voltage': '12V',
      'max_current': '10A',
    },
  );

  try {
    await comm.registerDevice();
    print('✅ Relay controller registered');

    // Control multiple relays
    final relays = ['door_1', 'door_2', 'lights_main', 'alarm_system'];
    
    for (final relay in relays) {
      final response = await comm.sendRelayControl(
        action: 'on',
        relayId: relay,
        metadata: {
          'zone': 'zone_1',
          'scheduled': false,
          'manual_override': true,
        },
      );
      
      if (response.success) {
        print('✅ Relay $relay activated');
      }
    }

    // Send status report
    await comm.sendMessage(
      type: 'status_report',
      payload: {
        'relays': {
          'door_1': {'status': 'on', 'current': '2.5A'},
          'door_2': {'status': 'on', 'current': '2.3A'},
          'lights_main': {'status': 'on', 'current': '1.8A'},
          'alarm_system': {'status': 'on', 'current': '0.5A'},
        },
        'system': {
          'temperature': 45.2,
          'voltage': 12.1,
          'total_current': '7.1A',
        },
      },
    );

    print('📊 Status report sent');

  } catch (e) {
    print('❌ Relay controller error: $e');
  } finally {
    await comm.dispose();
  }

  print('');
}

/// Example: IoT Sensor Device
Future<void> iotSensorExample() async {
  print('🌡️ IoT Sensor Example');
  print('----------------------');

  final transport = HttpTransport('http://localhost:3000');
  final comm = SecureComm(
    deviceId: 'sensor-node-001',
    deviceType: 'iot_sensor',
    transport: transport,
    deviceName: 'Environmental Sensor Node',
    capabilities: ['sensor_data', 'alerts', 'config_update'],
    metadata: {
      'sensors': ['temperature', 'humidity', 'motion', 'light'],
      'location': 'server_room',
      'sampling_rate': '30s',
    },
  );

  try {
    await comm.registerDevice();
    print('✅ IoT sensor registered');

    // Send sensor data
    await comm.sendMessage(
      type: 'sensor_data',
      payload: {
        'timestamp': DateTime.now().toIso8601String(),
        'readings': {
          'temperature': 23.5,
          'humidity': 65.2,
          'motion_detected': false,
          'light_level': 450,
        },
        'battery_level': 85,
        'signal_strength': -45,
      },
    );

    print('📊 Sensor data sent');

    // Send alert for high temperature
    await comm.sendMessage(
      type: 'alert',
      payload: {
        'alert_type': 'temperature_high',
        'severity': 'warning',
        'message': 'Temperature above threshold',
        'current_value': 28.5,
        'threshold': 25.0,
        'location': 'server_room',
      },
      priority: 2, // High priority for alerts
    );

    print('🚨 Alert sent');

  } catch (e) {
    print('❌ IoT sensor error: $e');
  } finally {
    await comm.dispose();
  }

  print('');
}

/// Example: Mobile App
Future<void> mobileAppExample() async {
  print('📱 Mobile App Example');
  print('---------------------');

  final transport = HttpTransport('http://localhost:3000');
  final comm = SecureComm(
    deviceId: 'mobile-app-user123',
    deviceType: 'mobile_app',
    transport: transport,
    deviceName: 'Security Manager App',
    capabilities: ['remote_control', 'monitoring', 'user_management'],
    metadata: {
      'app_version': '2.1.0',
      'platform': 'android',
      'user_role': 'security_manager',
    },
  );

  try {
    await comm.registerDevice();
    print('✅ Mobile app registered');

    // Remote door unlock
    await comm.sendRelayControl(
      action: 'unlock',
      relayId: 'emergency_exit',
      metadata: {
        'reason': 'emergency_override',
        'authorized_by': 'security_manager',
        'location': 'building_b',
      },
    );

    print('🚪 Emergency door unlocked remotely');

    // Request system status
    final statusResponse = await comm.sendStatusRequest(
      component: 'all_systems',
      filters: {
        'building': 'building_b',
        'critical_only': true,
      },
    );

    if (statusResponse.success) {
      print('📊 System status received');
    }

    // Send user action log
    await comm.sendLog(
      level: 'info',
      message: 'Emergency door unlocked by security manager',
      category: 'emergency_response',
      context: {
        'user_id': 'security_manager_001',
        'action': 'emergency_unlock',
        'location': 'building_b',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    print('📝 Action logged');

  } catch (e) {
    print('❌ Mobile app error: $e');
  } finally {
    await comm.dispose();
  }

  print('');
}

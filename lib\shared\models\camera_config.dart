import 'dart:io';
import 'package:camera/camera.dart';

/// Configuration class for camera settings
class CameraConfig {
  final CameraLensDirection preferredLensDirection;
  final ResolutionPreset resolution;
  final bool enableImageStream;
  final bool enableAudio;
  final ImageFormatGroup? imageFormatGroup;
  final bool pauseStreamForCapture;

  const CameraConfig({
    this.preferredLensDirection = CameraLensDirection.back,
    this.resolution = ResolutionPreset.medium,
    this.enableImageStream = false,
    this.enableAudio = false,
    this.imageFormatGroup,
    this.pauseStreamForCapture = true,
  });

  /// Get platform-specific image format
  ImageFormatGroup get platformImageFormat {
    if (imageFormatGroup != null) return imageFormatGroup!;
    
    return Platform.isAndroid
        ? ImageFormatGroup.nv21
        : ImageFormatGroup.bgra8888;
  }

  /// Create a copy with modified values
  CameraConfig copyWith({
    CameraLensDirection? preferredLensDirection,
    ResolutionPreset? resolution,
    bool? enableImageStream,
    bool? enableAudio,
    ImageFormatGroup? imageFormatGroup,
    bool? pauseStreamForCapture,
  }) {
    return CameraConfig(
      preferredLensDirection: preferredLensDirection ?? this.preferredLensDirection,
      resolution: resolution ?? this.resolution,
      enableImageStream: enableImageStream ?? this.enableImageStream,
      enableAudio: enableAudio ?? this.enableAudio,
      imageFormatGroup: imageFormatGroup ?? this.imageFormatGroup,
      pauseStreamForCapture: pauseStreamForCapture ?? this.pauseStreamForCapture,
    );
  }

  /// Default configuration for face capture
  static const CameraConfig faceCapture = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.medium,
    enableImageStream: true,
    enableAudio: false,
    pauseStreamForCapture: true,
  );

  /// High quality configuration
  static const CameraConfig highQuality = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.high,
    enableImageStream: false,
    enableAudio: false,
    pauseStreamForCapture: true,
  );

  /// Performance optimized configuration
  static const CameraConfig performance = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.low,
    enableImageStream: true,
    enableAudio: false,
    pauseStreamForCapture: false,
  );

  /// ULTRA-LIGHTWEIGHT configuration for minimal hardware load
  static const CameraConfig ultraLight = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.low,               // Lowest available resolution
    enableImageStream: true,
    enableAudio: false,                             // No audio processing
    imageFormatGroup: ImageFormatGroup.nv21,        // Most compatible format
    pauseStreamForCapture: false,                   // No pause for better performance
  );

  /// ML Kit optimized configuration for face detection
  static const CameraConfig mlKitOptimized = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.low,               // 480p for balance
    enableImageStream: true,
    enableAudio: false,
    imageFormatGroup: ImageFormatGroup.nv21,        // Best ML Kit compatibility
    pauseStreamForCapture: false,
  );

  /// TELPO F8 TERMINAL optimized configuration
  static const CameraConfig telpoF8Optimized = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.medium,            // 720p for Telpo F8's good hardware
    enableImageStream: true,
    enableAudio: false,
    imageFormatGroup: ImageFormatGroup.nv21,        // Best compatibility
    pauseStreamForCapture: false,                   // No pause for smooth performance
  );

  /// Power saving mode configuration (exit on touch)
  static const CameraConfig powerSavingMode = CameraConfig(
    preferredLensDirection: CameraLensDirection.back,
    resolution: ResolutionPreset.low,               // Lower resolution for power saving
    enableImageStream: true,
    enableAudio: false,
    imageFormatGroup: ImageFormatGroup.nv21,
    pauseStreamForCapture: false,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CameraConfig &&
        other.preferredLensDirection == preferredLensDirection &&
        other.resolution == resolution &&
        other.enableImageStream == enableImageStream &&
        other.enableAudio == enableAudio &&
        other.imageFormatGroup == imageFormatGroup &&
        other.pauseStreamForCapture == pauseStreamForCapture;
  }

  @override
  int get hashCode {
    return Object.hash(
      preferredLensDirection,
      resolution,
      enableImageStream,
      enableAudio,
      imageFormatGroup,
      pauseStreamForCapture,
    );
  }

  @override
  String toString() {
    return 'CameraConfig('
        'preferredLensDirection: $preferredLensDirection, '
        'resolution: $resolution, '
        'enableImageStream: $enableImageStream, '
        'enableAudio: $enableAudio, '
        'imageFormatGroup: $imageFormatGroup, '
        'pauseStreamForCapture: $pauseStreamForCapture'
        ')';
  }
}

/// Configuration for face detection
class FaceDetectionConfig {
  final int frameSkip;
  final Duration minDetectionInterval;
  final double minFaceSize;
  final double minFaceArea;
  final double idealFaceArea;
  final double qualityThreshold;
  final double maxYawAngle;
  final double maxRollAngle;
  final double centerAngleTolerance;
  final double directionAngleTolerance;

  const FaceDetectionConfig({
    this.frameSkip = 4, // Optimized for mobile devices
    this.minDetectionInterval = const Duration(milliseconds: 200), // Balanced for responsiveness
    this.minFaceSize = 0.1,
    this.minFaceArea = 15000,
    this.idealFaceArea = 50000,
    this.qualityThreshold = 0.5,
    this.maxYawAngle = 30.0,
    this.maxRollAngle = 20.0,
    this.centerAngleTolerance = 10.0,
    this.directionAngleTolerance = 15.0,
  });

  /// Default configuration for face detection
  static const FaceDetectionConfig defaultConfig = FaceDetectionConfig();

  /// High accuracy configuration (slower but more accurate)
  static const FaceDetectionConfig highAccuracy = FaceDetectionConfig(
    frameSkip: 5,
    minDetectionInterval: Duration(milliseconds: 100),
    minFaceArea: 20000,
    qualityThreshold: 0.7,
    maxYawAngle: 20.0,
    maxRollAngle: 15.0,
  );

  /// Performance optimized configuration (faster but less accurate)
  static const FaceDetectionConfig performance = FaceDetectionConfig(
    frameSkip: 15,
    minDetectionInterval: Duration(milliseconds: 300),
    minFaceArea: 10000,
    qualityThreshold: 0.3,
    maxYawAngle: 45.0,
    maxRollAngle: 30.0,
  );

  FaceDetectionConfig copyWith({
    int? frameSkip,
    Duration? minDetectionInterval,
    double? minFaceSize,
    double? minFaceArea,
    double? idealFaceArea,
    double? qualityThreshold,
    double? maxYawAngle,
    double? maxRollAngle,
    double? centerAngleTolerance,
    double? directionAngleTolerance,
  }) {
    return FaceDetectionConfig(
      frameSkip: frameSkip ?? this.frameSkip,
      minDetectionInterval: minDetectionInterval ?? this.minDetectionInterval,
      minFaceSize: minFaceSize ?? this.minFaceSize,
      minFaceArea: minFaceArea ?? this.minFaceArea,
      idealFaceArea: idealFaceArea ?? this.idealFaceArea,
      qualityThreshold: qualityThreshold ?? this.qualityThreshold,
      maxYawAngle: maxYawAngle ?? this.maxYawAngle,
      maxRollAngle: maxRollAngle ?? this.maxRollAngle,
      centerAngleTolerance: centerAngleTolerance ?? this.centerAngleTolerance,
      directionAngleTolerance: directionAngleTolerance ?? this.directionAngleTolerance,
    );
  }
}

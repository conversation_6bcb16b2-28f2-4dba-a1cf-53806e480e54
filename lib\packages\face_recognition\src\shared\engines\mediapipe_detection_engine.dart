import 'dart:typed_data';
import 'dart:ui';
import 'dart:math' as math;

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:tflite_flutter/tflite_flutter.dart';
import 'package:image/image.dart' as img;

import '../../core/interfaces/face_detection_engine.dart';
import '../../core/models/face_recognition_config.dart';
import '../utils/image_utils.dart';

/// MediaPipe face detection engine optimized for accuracy
class MediaPipeDetectionEngine implements FaceDetectionEngine {
  static const String _modelAssetPath = 'packages/face_recognition/assets/models/mediapipe_face_detection.tflite';
  
  Interpreter? _interpreter;
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  
  // Model parameters
  static const int _inputWidth = 128;
  static const int _inputHeight = 128;
  static const int _numDetections = 896;
  static const double _confidenceThreshold = 0.5;
  static const double _nmsThreshold = 0.3;
  
  // Pre-allocated buffers
  Float32List? _inputBuffer;
  Float32List? _scoresBuffer;
  Float32List? _boxesBuffer;
  Float32List? _landmarksBuffer;
  
  final FaceRecognitionConfig _faceConfig;
  
  MediaPipeDetectionEngine({required FaceRecognitionConfig config}) : _faceConfig = config;
  
  @override
  String get engineName => 'MediaPipe';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing MediaPipe Detection Engine');
      }
      
      // Load TFLite model
      _interpreter = await Interpreter.fromAsset(_modelAssetPath);
      
      // Configure interpreter options
      final options = InterpreterOptions();
      if (_faceConfig.enableGPUAcceleration) {
        options.addDelegate(GpuDelegate());
      }
      options.threads = _faceConfig.processingThreads;
      
      // Allocate tensors
      _interpreter!.allocateTensors();
      
      // Pre-allocate buffers
      _inputBuffer = Float32List(_inputWidth * _inputHeight * 3);
      _scoresBuffer = Float32List(_numDetections);
      _boxesBuffer = Float32List(_numDetections * 4);
      _landmarksBuffer = Float32List(_numDetections * 6); // 3 landmarks * 2 coordinates
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ MediaPipe engine initialized successfully');
        print('   Input size: ${_inputWidth}x${_inputHeight}');
        print('   GPU acceleration: ${_faceConfig.enableGPUAcceleration}');
        print('   Threads: ${_faceConfig.processingThreads}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize MediaPipe engine: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<List<FaceDetection>> detectFaces(Uint8List imageBytes) async {
    if (!_isInitialized) {
      throw FaceDetectionException('MediaPipe engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Decode and preprocess image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw FaceDetectionException('Failed to decode image', engineName: engineName);
      }
      
      // Preprocess image
      _preprocessImage(image);
      
      // Run inference
      final outputs = <int, Object>{
        0: _scoresBuffer!,
        1: _boxesBuffer!,
        2: _landmarksBuffer!,
      };
      
      _interpreter!.runForMultipleInputs([_inputBuffer], outputs);
      
      // Post-process results
      final faces = _postprocessResults(image.width, image.height, imageBytes);
      
      stopwatch.stop();
      
      if (kDebugMode) {
        print('🔍 MediaPipe detection completed in ${stopwatch.elapsedMilliseconds}ms');
        print('   Detected faces: ${faces.length}');
      }
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ MediaPipe detection failed: $e');
      }
      throw FaceDetectionException('Detection failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  @override
  Future<List<FaceDetection>> processCameraImage(CameraImage image) async {
    if (!_isInitialized) {
      throw FaceDetectionException('MediaPipe engine not initialized', engineName: engineName);
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Convert camera image to RGB
      final rgbImage = ImageUtils.convertCameraImageToRGB(image);
      
      // Create image object
      final imgImage = img.Image.fromBytes(
        width: image.width,
        height: image.height,
        bytes: rgbImage.buffer,
        format: img.Format.uint8,
        numChannels: 3,
      );
      
      // Preprocess image
      _preprocessImage(imgImage);
      
      // Run inference
      final outputs = <int, Object>{
        0: _scoresBuffer!,
        1: _boxesBuffer!,
        2: _landmarksBuffer!,
      };
      
      _interpreter!.runForMultipleInputs([_inputBuffer], outputs);
      
      // Post-process results
      final faces = _postprocessResults(image.width, image.height, rgbImage);
      
      stopwatch.stop();
      
      if (kDebugMode && faces.isNotEmpty) {
        print('🔍 MediaPipe camera processing: ${stopwatch.elapsedMilliseconds}ms, ${faces.length} faces');
      }
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ MediaPipe camera processing failed: $e');
      }
      throw FaceDetectionException('Camera processing failed: $e', engineName: engineName, originalError: e);
    }
  }
  
  /// Preprocess image for MediaPipe model
  void _preprocessImage(img.Image image) {
    // Resize image to model input size
    final resized = img.copyResize(
      image,
      width: _inputWidth,
      height: _inputHeight,
      interpolation: img.Interpolation.linear,
    );
    
    // Convert to float and normalize to [0, 1]
    var pixelIndex = 0;
    for (int y = 0; y < _inputHeight; y++) {
      for (int x = 0; x < _inputWidth; x++) {
        final pixel = resized.getPixel(x, y);
        
        // Normalize to [0, 1] range
        _inputBuffer![pixelIndex++] = pixel.r / 255.0;
        _inputBuffer![pixelIndex++] = pixel.g / 255.0;
        _inputBuffer![pixelIndex++] = pixel.b / 255.0;
      }
    }
  }
  
  /// Post-process model outputs to extract face detections
  List<FaceDetection> _postprocessResults(int originalWidth, int originalHeight, Uint8List originalImageBytes) {
    final detections = <FaceDetection>[];
    
    // Process each detection
    for (int i = 0; i < _numDetections; i++) {
      final confidence = _scoresBuffer![i];
      
      // Skip low confidence detections
      if (confidence < _confidenceThreshold) continue;
      
      // Extract bounding box
      final boxIndex = i * 4;
      final yMin = _boxesBuffer![boxIndex];
      final xMin = _boxesBuffer![boxIndex + 1];
      final yMax = _boxesBuffer![boxIndex + 2];
      final xMax = _boxesBuffer![boxIndex + 3];
      
      // Convert to original image coordinates
      final left = xMin * originalWidth;
      final top = yMin * originalHeight;
      final right = xMax * originalWidth;
      final bottom = yMax * originalHeight;
      
      // Create bounding box
      final boundingBox = Rect.fromLTRB(
        math.max(0, left),
        math.max(0, top),
        math.min(originalWidth.toDouble(), right),
        math.min(originalHeight.toDouble(), bottom),
      );
      
      // Skip invalid boxes
      if (boundingBox.width < _faceConfig.minFaceSize || 
          boundingBox.height < _faceConfig.minFaceSize) continue;
      
      // Extract landmarks
      final landmarkIndex = i * 6;
      final landmarks = <Point>[
        Point(
          _landmarksBuffer![landmarkIndex] * originalWidth,
          _landmarksBuffer![landmarkIndex + 1] * originalHeight,
        ), // Right eye
        Point(
          _landmarksBuffer![landmarkIndex + 2] * originalWidth,
          _landmarksBuffer![landmarkIndex + 3] * originalHeight,
        ), // Left eye
        Point(
          _landmarksBuffer![landmarkIndex + 4] * originalWidth,
          _landmarksBuffer![landmarkIndex + 5] * originalHeight,
        ), // Nose tip
      ];
      
      // Calculate face quality
      final quality = _calculateFaceQuality(boundingBox, landmarks, originalWidth, originalHeight);
      
      // Calculate pose estimation
      final pose = _calculateFacePose(landmarks);
      
      // Crop and align face if needed
      Uint8List? croppedFace;
      if (_faceConfig.cropFaces) {
        try {
          croppedFace = ImageUtils.cropAndAlignFace(
            originalImageBytes,
            boundingBox,
            landmarks,
            targetSize: _faceConfig.cropSize,
          );
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Failed to crop face: $e');
          }
        }
      }
      
      // Create face detection
      final detection = FaceDetection(
        boundingBox: boundingBox,
        landmarks: landmarks,
        confidence: confidence,
        quality: quality,
        pose: pose,
        croppedFace: croppedFace,
        metadata: {
          'engine': engineName,
          'detection_index': i,
        },
      );
      
      detections.add(detection);
    }
    
    // Apply Non-Maximum Suppression
    return _applyNMS(detections);
  }
  
  /// Calculate face quality based on multiple factors
  double _calculateFaceQuality(Rect boundingBox, List<Point> landmarks, int imageWidth, int imageHeight) {
    // Size quality (larger faces are better)
    final faceArea = boundingBox.width * boundingBox.height;
    final imageArea = imageWidth * imageHeight;
    final sizeRatio = faceArea / imageArea;
    final sizeQuality = math.min(1.0, sizeRatio * 15); // Normalize
    
    // Position quality (centered faces are better)
    final centerX = boundingBox.center.dx;
    final centerY = boundingBox.center.dy;
    final imageCenterX = imageWidth / 2;
    final imageCenterY = imageHeight / 2;
    
    final distanceFromCenter = math.sqrt(
      math.pow(centerX - imageCenterX, 2) + math.pow(centerY - imageCenterY, 2)
    );
    final maxDistance = math.sqrt(
      math.pow(imageCenterX, 2) + math.pow(imageCenterY, 2)
    );
    final positionQuality = 1.0 - (distanceFromCenter / maxDistance);
    
    // Landmark quality (symmetric landmarks are better)
    double landmarkQuality = 1.0;
    if (landmarks.length >= 2) {
      final leftEye = landmarks[0];
      final rightEye = landmarks[1];
      final eyeDistance = math.sqrt(
        math.pow(rightEye.x - leftEye.x, 2) + math.pow(rightEye.y - leftEye.y, 2)
      );
      final expectedEyeDistance = boundingBox.width * 0.3;
      landmarkQuality = 1.0 - (eyeDistance - expectedEyeDistance).abs() / expectedEyeDistance;
      landmarkQuality = landmarkQuality.clamp(0.0, 1.0);
    }
    
    // Combine qualities
    return (sizeQuality * 0.4 + positionQuality * 0.3 + landmarkQuality * 0.3).clamp(0.0, 1.0);
  }
  
  /// Calculate face pose from landmarks
  FacePose _calculateFacePose(List<Point> landmarks) {
    if (landmarks.length < 3) {
      return const FacePose();
    }
    
    final rightEye = landmarks[0];
    final leftEye = landmarks[1];
    final nose = landmarks[2];
    
    // Calculate roll angle from eye positions
    final eyeDeltaX = leftEye.x - rightEye.x;
    final eyeDeltaY = leftEye.y - rightEye.y;
    final roll = math.atan2(eyeDeltaY, eyeDeltaX) * 180 / math.pi;
    
    // Calculate yaw from nose position relative to eye center
    final eyeCenterX = (leftEye.x + rightEye.x) / 2;
    final noseOffsetX = nose.x - eyeCenterX;
    final eyeDistance = math.sqrt(eyeDeltaX * eyeDeltaX + eyeDeltaY * eyeDeltaY);
    final yaw = (noseOffsetX / eyeDistance) * 30; // Approximate yaw
    
    // Calculate pitch from nose position relative to eye center
    final eyeCenterY = (leftEye.y + rightEye.y) / 2;
    final noseOffsetY = nose.y - eyeCenterY;
    final pitch = (noseOffsetY / eyeDistance) * 30; // Approximate pitch
    
    return FacePose(
      yaw: yaw.clamp(-45.0, 45.0),
      pitch: pitch.clamp(-30.0, 30.0),
      roll: roll.clamp(-45.0, 45.0),
    );
  }
  
  /// Apply Non-Maximum Suppression to remove overlapping detections
  List<FaceDetection> _applyNMS(List<FaceDetection> detections) {
    if (detections.isEmpty) return detections;
    
    // Sort by confidence (descending)
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));
    
    final keep = <bool>[];
    for (int i = 0; i < detections.length; i++) {
      keep.add(true);
    }
    
    for (int i = 0; i < detections.length; i++) {
      if (!keep[i]) continue;
      
      for (int j = i + 1; j < detections.length; j++) {
        if (!keep[j]) continue;
        
        final iou = _calculateIoU(detections[i].boundingBox, detections[j].boundingBox);
        if (iou > _nmsThreshold) {
          keep[j] = false;
        }
      }
    }
    
    final result = <FaceDetection>[];
    for (int i = 0; i < detections.length; i++) {
      if (keep[i]) {
        result.add(detections[i]);
      }
    }
    
    return result.take(_faceConfig.maxFaces).toList();
  }
  
  /// Calculate Intersection over Union (IoU) between two rectangles
  double _calculateIoU(Rect a, Rect b) {
    final intersection = a.intersect(b);
    if (intersection.isEmpty) return 0.0;
    
    final intersectionArea = intersection.width * intersection.height;
    final unionArea = (a.width * a.height) + (b.width * b.height) - intersectionArea;
    
    return intersectionArea / unionArea;
  }
  
  @override
  void dispose() {
    _interpreter?.close();
    _interpreter = null;
    _inputBuffer = null;
    _scoresBuffer = null;
    _boxesBuffer = null;
    _landmarksBuffer = null;
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ MediaPipe engine disposed');
    }
  }
}

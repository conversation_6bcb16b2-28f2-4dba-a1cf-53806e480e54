import 'package:dartz/dartz.dart';
import '../entities/tenant/tenant.dart';
import '../../core/errors/failures.dart';

/// Repository interface for tenant-related operations
abstract class TenantRepository {
  /// Get user tenants with pagination
  Future<Either<Failure, List<Tenant>>> getUserTenants({
    int page = 1,
    int size = 10,
  });

  /// Get tenant by ID
  Future<Either<Failure, Tenant>> getTenantById(String tenantId);

  /// Create new tenant
  Future<Either<Failure, Tenant>> createTenant({
    required String name,
    String? address,
    String? unitId,
    List<String>? mappings,
  });

  /// Switch tenant context
  Future<Either<Failure, bool>> switchTenantContext(String tenantId);

  // ========== UNITS MANAGEMENT ==========
  /// Get all units with pagination and optional filtering
  Future<Either<Failure, List<Map<String, dynamic>>>> getUnits({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  });

  /// Get units response with pagination info
  Future<Either<Failure, Map<String, dynamic>>> getUnitsWithPagination({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  });

  /// Get unit by ID
  Future<Either<Failure, Map<String, dynamic>>> getUnitById(String unitId);

  /// Get units by tenant ID
  Future<Either<Failure, List<Map<String, dynamic>>>> getUnitsByTenant(String tenantId);

  /// Create a new unit
  Future<Either<Failure, Map<String, dynamic>>> createUnit({
    required String name,
    required String tenantId,
    String? parentUnitId,
    List<String>? mappings,
  });

  /// Update unit
  Future<Either<Failure, Map<String, dynamic>>> updateUnit({
    required String unitId,
    String? name,
    String? parentUnitId,
    List<String>? mappings,
  });

  /// Delete unit
  Future<Either<Failure, void>> deleteUnit(String unitId);
}

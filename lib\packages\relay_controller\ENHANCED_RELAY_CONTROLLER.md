# Enhanced Relay Controller

## Overview

The Enhanced Relay Controller provides a comprehensive solution for managing relay devices with automatic discovery, connection management, health monitoring, and error handling.

## Features

### 🔍 Automatic Device Discovery
- **USB-TTL Devices**: Automatically detects USB-TTL relay controllers (PL2303, CH340, FTDI, etc.)
- **USB Devices**: Discovers standard USB relay devices
- **Network Devices**: Support for HTTP and Secure HTTP relay controllers
- **Device Filtering**: Intelligent filtering by vendor/product IDs and device names

### 🔌 Multiple Transport Support
- **USB-TTL**: Advanced relay control with device profiles (ESP32, Arduino, Custom)
- **USB Serial**: Standard USB relay communication
- **HTTP**: Simple HTTP-based relay control
- **Secure HTTP**: JWT-authenticated relay control with HMAC signing

### 🏥 Health Monitoring
- **Connection Status**: Real-time monitoring of device connections
- **Automatic Recovery**: Attempts to reconnect failed devices
- **Event Logging**: Comprehensive event tracking and logging
- **Performance Metrics**: Device response time and reliability tracking

### 🛡️ Error Handling
- **Graceful Degradation**: Continues operation when some devices fail
- **Retry Logic**: Automatic retry for failed operations
- **Detailed Error Messages**: Clear error reporting with context
- **Fallback Mechanisms**: Alternative communication methods when primary fails

## Usage

### Basic Usage

```dart
import 'package:relay_controller/relay_controller.dart';

// Get the enhanced relay manager instance
final manager = EnhancedRelayManager();

// Initialize and discover devices
await manager.initialize();

// Get discovered devices
final devices = manager.discoveredDevices;
print('Found ${devices.length} relay devices');

// Connect to a device
final controller = await manager.connectToDevice(devices.first.id);

// Control relays
await manager.controlRelay(devices.first.id, 0, RelayAction.on);
await manager.controlRelay(devices.first.id, 1, RelayAction.off);
await manager.controlRelay(devices.first.id, 2, RelayAction.toggle);
```

### Advanced Configuration

```dart
// Connect with custom configuration
final controller = await manager.connectToDevice(
  deviceId,
  config: {
    'relay_count': 8,
    'baud_rate': 115200,
    'device_profile': 'esp32',
    'timeout_seconds': 30,
  },
);

// Listen to device events
manager.deviceEvents.listen((event) {
  print('Event: ${event.type} - ${event.message}');
  
  switch (event.type) {
    case 'device_connected':
      print('Device ${event.data['device_id']} connected');
      break;
    case 'relay_controlled':
      print('Relay ${event.data['relay_index']} ${event.data['action']}');
      break;
    case 'error':
      print('Error: ${event.message}');
      break;
  }
});
```

### Widget Integration

```dart
import 'package:your_app/widgets/enhanced_relay_manager_widget.dart';

// Add to your UI
EnhancedRelayManagerWidget(
  showDebugInfo: true,
  showDeviceList: true,
  showControls: true,
  showEvents: true,
  autoConnect: true, // Automatically initialize on widget creation
)
```

## Device Types

### USB-TTL Relay Controllers
- **ESP32 Format**: `R0:1`, `R0:0`, `R1:TOGGLE`, `R2:500`, `ALL:1`, `ALL:0`
- **Arduino Format**: `REL_0_ON`, `REL_0_OFF`, `REL_ALL_ON`, etc.
- **Custom Formats**: Define your own command templates

### Supported USB-TTL Chips
- **Prolific PL2303**: VID 0x067B, PID 0x2303
- **FTDI Chips**: VID 0x0403, PIDs 0x6001, 0x6015, 0x6010, 0x6011
- **CH340/CH341**: VID 0x1A86, PIDs 0x7523, 0x5523
- **CP210x**: VID 0x10C4, PIDs 0xEA60, 0xEA70
- **Arduino Boards**: VID 0x2341, various PIDs

## Event System

The Enhanced Relay Manager provides a comprehensive event system:

### Event Types
- `manager_initialized`: Manager successfully initialized
- `discovery_completed`: Device discovery finished
- `usb_discovered`: USB devices found
- `device_connected`: Device successfully connected
- `device_disconnected`: Device disconnected
- `connection_failed`: Connection attempt failed
- `relay_controlled`: Relay operation completed
- `error`: Error occurred

### Event Data
Each event includes:
- `type`: Event type identifier
- `message`: Human-readable description
- `data`: Additional event-specific data

## Error Handling

### Common Errors
- **Device Not Found**: Device ID not in discovered devices
- **Connection Failed**: Unable to establish device connection
- **Communication Error**: Failed to send commands to device
- **Timeout Error**: Operation exceeded timeout limit

### Error Recovery
- **Automatic Retry**: Failed operations are retried automatically
- **Connection Recovery**: Disconnected devices are reconnected
- **Graceful Degradation**: System continues with available devices
- **Error Reporting**: Detailed error information for debugging

## Configuration

### Device Profiles
```dart
// ESP32 Profile (default)
DeviceProfile.esp32()

// Arduino Profile
DeviceProfile.arduino()

// Simple Profile
DeviceProfile.simple()

// Custom Profile
DeviceProfile.custom(
  name: 'MyDevice',
  relayOnTemplate: 'RELAY_{relay}_ON',
  relayOffTemplate: 'RELAY_{relay}_OFF',
  commandTerminator: '\r\n',
)
```

### Connection Settings
```dart
{
  'relay_count': 4,           // Number of relays (default: 4)
  'baud_rate': 115200,        // Serial baud rate (default: 115200)
  'timeout_seconds': 10,      // Connection timeout (default: 10)
  'device_profile': 'esp32',  // Device profile (default: 'esp32')
  'auto_reconnect': true,     // Auto-reconnect on failure (default: true)
  'health_check_interval': 30, // Health check interval in seconds (default: 30)
}
```

## Best Practices

### 1. Initialize Early
```dart
// Initialize the manager early in your app lifecycle
await EnhancedRelayManager().initialize();
```

### 2. Handle Events
```dart
// Always listen to events for proper error handling
manager.deviceEvents.listen((event) {
  // Handle events appropriately
});
```

### 3. Proper Disposal
```dart
// Dispose the manager when no longer needed
await manager.dispose();
```

### 4. Error Handling
```dart
try {
  await manager.controlRelay(deviceId, relayIndex, action);
} catch (e) {
  // Handle errors gracefully
  print('Relay control failed: $e');
}
```

## Troubleshooting

### No Devices Found
1. Check USB connections
2. Verify device permissions (Android)
3. Ensure drivers are installed (Windows)
4. Check device compatibility

### Connection Failures
1. Verify device is not in use by another app
2. Check baud rate settings
3. Try different USB ports
4. Restart the device

### Command Failures
1. Verify device profile settings
2. Check command format
3. Ensure device is responsive
4. Try manual device reset

## Performance

### Optimization Tips
- Use appropriate baud rates for your devices
- Implement proper error handling
- Monitor device health regularly
- Use connection pooling for multiple devices
- Implement command queuing for high-frequency operations

### Monitoring
- Track connection success rates
- Monitor command response times
- Log device health metrics
- Implement alerting for critical failures

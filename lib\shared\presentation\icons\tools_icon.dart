import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Tools icon component for TabsBar
class ToolsIcon extends StatelessWidget {
  final bool isActive;
  final double size;

  const ToolsIcon({
    super.key,
    required this.isActive,
    this.size = 18.0,
  });

  @override
  Widget build(BuildContext context) {
    final color = isActive ? '#008FD3' : '#9CA5B3';
    
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_112_54327)">
            <path
              d="M1.625 13.5C1.625 12.3447 1.625 11.767 1.88505 11.3426C2.03056 11.1052 2.2302 10.9056 2.46765 10.76C2.89201 10.5 3.46967 10.5 4.625 10.5C5.78033 10.5 6.35799 10.5 6.78235 10.76C7.0198 10.9056 7.21944 11.1052 7.36495 11.3426C7.625 11.767 7.625 12.3447 7.625 13.5C7.625 14.6553 7.625 15.233 7.36495 15.6574C7.21944 15.8948 7.0198 16.0944 6.78235 16.24C6.35799 16.5 5.78033 16.5 4.625 16.5C3.46967 16.5 2.89201 16.5 2.46765 16.24C2.2302 16.0944 2.03056 15.8948 1.88505 15.6574C1.625 15.233 1.625 14.6553 1.625 13.5Z"
              stroke="$color"
              stroke-width="1.5"
            />
            <path
              d="M10.625 13.5C10.625 12.3447 10.625 11.767 10.885 11.3426C11.0306 11.1052 11.2302 10.9056 11.4676 10.76C11.892 10.5 12.4697 10.5 13.625 10.5C14.7803 10.5 15.358 10.5 15.7824 10.76C16.0198 10.9056 16.2194 11.1052 16.365 11.3426C16.625 11.767 16.625 12.3447 16.625 13.5C16.625 14.6553 16.625 15.233 16.365 15.6574C16.2194 15.8948 16.0198 16.0944 15.7824 16.24C15.358 16.5 14.7803 16.5 11.4676 16.24C11.2302 16.0944 11.0306 15.8948 10.885 15.6574C10.625 15.233 10.625 14.6553 10.625 13.5Z"
              stroke="$color"
              stroke-width="1.5"
            />
            <path
              d="M1.625 4.5C1.625 3.34467 1.625 2.76701 1.88505 2.34265C2.03056 2.1052 2.2302 1.90556 2.46765 1.76005C2.89201 1.5 3.46967 1.5 4.625 1.5C5.78033 1.5 6.35799 1.5 6.78235 1.76005C7.0198 1.90556 7.21944 2.1052 7.36495 2.34265C7.625 2.76701 7.625 3.34467 7.625 4.5C7.625 5.65533 7.625 6.23299 7.36495 6.65735C7.21944 6.8948 7.0198 7.09444 6.78235 7.23995C6.35799 7.5 5.78033 7.5 4.625 7.5C3.46967 7.5 2.89201 7.5 2.46765 7.23995C2.2302 7.09444 2.03056 6.8948 1.88505 6.65735C1.625 6.23299 1.625 5.65533 1.625 4.5Z"
              stroke="$color"
              stroke-width="1.5"
            />
            <path
              d="M10.625 4.5C10.625 3.34467 10.625 2.76701 10.885 2.34265C11.0306 2.1052 11.2302 1.90556 11.4676 1.76005C11.892 1.5 12.4697 1.5 13.625 1.5C14.7803 1.5 15.358 1.5 15.7824 1.76005C16.0198 1.90556 16.2194 2.1052 16.365 2.34265C16.625 2.76701 16.625 3.34467 16.625 4.5C16.625 5.65533 16.625 6.23299 16.365 6.65735C16.2194 6.8948 16.0198 7.09444 15.7824 7.23995C15.358 7.5 14.7803 7.5 13.625 7.5C12.4697 7.5 11.892 7.5 11.4676 7.23995C11.2302 7.09444 11.0306 6.8948 10.885 6.65735C10.625 6.23299 10.625 5.65533 10.625 4.5Z"
              stroke="$color"
              stroke-width="1.5"
            />
          </g>
          <defs>
            <clipPath id="clip0_112_54327">
              <rect width="18" height="18" fill="white" transform="translate(0.125)" />
            </clipPath>
          </defs>
        </svg>''',
        width: size,
        height: size,
      ),
    );
  }
}

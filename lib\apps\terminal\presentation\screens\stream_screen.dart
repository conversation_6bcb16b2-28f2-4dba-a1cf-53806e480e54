import 'dart:async';
import 'dart:typed_data';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:camera/camera.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/providers/face_capture_provider.dart';
import '../../../../shared/providers/face_detection_provider.dart';
import '../../providers/face_detection_provider.dart' as terminal;
import '../../../../packages/face_recognition/src/config/face_detection_config.dart';
import '../../../../shared/providers/captured_face_provider.dart';

import '../../../../shared/models/camera_config.dart' hide FaceDetectionConfig;
import '../../../../shared/models/camera_config.dart' as camera_config;
import '../../../../shared/widgets/face_detection_overlay.dart';
import '../../providers/system_status_provider.dart';
import '../../../../shared/services/device_management_service.dart';
import '../../../../shared/widgets/device_management_widget.dart';

import '../../providers/device_registration_provider.dart';
import '../widgets/simple_server_communication_widget.dart';
import '../widgets/enhanced_server_communication_widget.dart';
import '../widgets/relay_testing_widget.dart';
import '../widgets/device_registration_debug_widget_simple.dart';
import '../widgets/enhanced_relay_manager_widget.dart';
import 'relay_testing_screen_simple.dart';
import 'face_recognition_config_screen_simple.dart';

import '../../services/face_recognition_service.dart';
import '../../services/face_event_trigger_service.dart';
import 'relay_trigger_config_screen.dart';
import '../../../../shared/services/relay_management_service.dart';
import '../../../../shared/services/relay_config_service.dart';
import '../../../../shared/services/relay_throttle_service.dart';
import '../../../../shared/services/performance_optimization_service.dart';
import '../../../../shared/services/memory_pool_service.dart';
import '../../../../shared/services/face_recognition_v3_service.dart';
import '../../../../shared/data/models/face_recognition_v3_request.dart';
import '../../../../shared/data/models/face_recognition_v3_response.dart';

import '../../../../shared/services/image_format_service.dart';
import '../widgets/circular_progress_border_widget.dart';
import '../../../../shared/services/camera_image_converter.dart';
import '../../../../shared/services/face_cropping_service.dart';
import '../widgets/face_recognition_status_widget.dart';
import '../../constants/face_detection_constants.dart';
import '../../../../shared/core/config/ui/admin_config_screen.dart';
import '../../../../shared/core/config/ui/quick_config_widget.dart';
import '../../../../shared/core/config/ui/config_status_widget.dart';
import '../../../../shared/core/config/services/config_component_sync_service.dart';
import '../../../../shared/widgets/telpo_f8_touch_detector.dart';
import '../../../../shared/core/config/config_helper.dart';
import 'package:relay_controller/relay_controller.dart' as relay;

/// Terminal app main screen with camera stream and face detection
///
/// Features:
/// - Real-time camera preview with face detection overlay
/// - Face quality assessment and direction detection
/// - System status monitoring (camera, network, server)
/// - Device registration and server communication
/// - Face recognition integration using camera stream (no additional capture)
/// - Auto-connect to server on startup
/// - Performance optimized face processing with reduced latency
/// - Extreme power saving mode with screen off
///
/// Face Recognition Flow:
/// 1. Face detected from camera stream
/// 2. Quality check (≥40%) and throttling (500ms)
/// 3. Convert CameraImage directly to JPEG bytes
/// 4. Send to server for recognition (no file I/O)
/// 5. Display recognized user info in real-time
class StreamScreen extends StatefulWidget {
  const StreamScreen({super.key});

  @override
  State<StreamScreen> createState() => _StreamScreenState();
}

class _StreamScreenState extends State<StreamScreen>
    with WidgetsBindingObserver {
  // Biến để kiểm soát hiển thị user info
  bool _hasUserInFrame = false;

  // Face detection state
  Face? _bestFace;
  int _detectedFacesCount = 0;
  double _bestFaceQuality = 0.0;

  // Face recognition
  FaceRecognitionService? _faceRecognitionService;
  RecognizedUser? _currentUser;
  bool _isRecognizing = false;
  DateTime? _lastRecognitionTime;
  bool _allowAccess = false;
  String? _accessReason;
  static Duration get _userDisplayTimeout =>
      FaceDetectionConstants.userDisplayTimeout;

  // Face image from detection source (no additional capture needed)
  Uint8List? _detectionSourceImage;
  double _detectionSourceQuality = 0.0;
  DateTime? _lastDetectionImageTime;

  // Resource optimization for face absence
  DateTime? _lastFaceDetectedTime;
  Timer? _resourceOptimizationTimer;
  Timer? _powerSavingWatchdogTimer;
  bool _isResourceOptimized = false;
  bool _isExtremePowerSaving = false; // Screen off mode
  // Use configuration values instead of hardcoded constants
  Duration get _faceAbsenceOptimizationThreshold =>
      ConfigHelper.faceAbsenceForOptimization;
  Duration get _extremePowerSavingThreshold =>
      ConfigHelper.faceAbsenceForExtremeSaving;
  static const Duration _optimizationCheckInterval = Duration(
    seconds: 5,
  ); // Check every 5 seconds (more frequent)

  // Server health check
  Timer? _serverHealthCheckTimer;
  String _serverHealthStatus =
      'unknown'; // 'good', 'warning', 'error', 'unknown'
  static const Duration _healthCheckInterval = Duration(
    seconds: 10,
  ); // Check every 10 seconds

  // Face recognition processing timer (since overlay is hidden)
  Timer? _faceRecognitionTimer;
  DateTime? _lastFaceRecognitionTime;
  static const Duration _faceRecognitionInterval = Duration(
    seconds: 2,
  ); // Process every 2 seconds

  // Recognition throttle timer with visual indicator
  Timer? _recognitionThrottleTimer;
  DateTime? _lastRecognitionRequestTime;
  bool _isRecognitionThrottleActive = false;
  double _throttleProgress = 0.0; // 0.0 to 1.0 for progress indicator
  static Duration get _recognitionThrottleDuration =>
      FaceDetectionConstants.recognitionThrottleDuration;
  static const Duration _throttleUpdateInterval = Duration(
    milliseconds: 100,
  ); // Update progress every 100ms

  // Provider instances
  late FaceCaptureProvider _cameraProvider;
  late FaceDetectionProvider _faceDetectionProvider; // Keep for camera integration
  late SystemStatusProvider _systemStatusProvider;
  late DeviceRegistrationProvider _deviceRegistrationProvider;
  // CapturedFaceProvider will be accessed via Provider.of

  // Face event trigger service for centralized trigger management
  final FaceEventTriggerService _faceEventTriggerService =
      FaceEventTriggerService();

  // Device management service for always-on functionality
  final DeviceManagementService _deviceManagementService =
      DeviceManagementService.instance;

  // Performance optimization
  DateTime _lastUIUpdate = DateTime.now();
  static const Duration _uiUpdateThrottle = Duration(
    milliseconds: 100,
  ); // Throttle UI updates

  // Real-time clock
  late Timer _clockTimer;
  String _currentTime = '';

  // Date formatter for Vietnamese
  final DateFormat _dateTimeFormatter = DateFormat('EEEE, dd/MM/yyyy HH:mm:ss');

  // Server communication widget visibility
  bool _showServerCommunication = false;

  // Relay testing widget visibility
  bool _showRelayTesting = false;

  // Camera health monitoring
  Timer? _cameraHealthTimer;
  DateTime? _lastImageProcessed;
  static const Duration _cameraHealthCheckInterval = Duration(
    seconds: 15,
  ); // Check every 15 seconds
  static const Duration _cameraTimeoutThreshold = Duration(
    seconds: 30,
  ); // 30 seconds without images

  @override
  void initState() {
    super.initState();

    // Add app lifecycle observer for better camera management
    WidgetsBinding.instance.addObserver(this);

    // Ẩn status bar và navigation bar để full screen
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    // Initialize performance optimization services first
    _initializePerformanceServices();

    // Initialize providers
    _cameraProvider = FaceCaptureProvider();
    _faceDetectionProvider = FaceDetectionProvider();
    _systemStatusProvider = SystemStatusProvider();
    // Temporarily disable device registration (server doesn't support it)
    _deviceRegistrationProvider = DeviceRegistrationProvider();

    // Initialize relay trigger service (async)
    _initializeRelayTriggerService();

    // Initialize device management service for always-on functionality (disabled for new server)
    // _initializeDeviceManagement(); // Disabled - new server doesn't support device registration

    // Initialize real-time clock
    _initializeClock();

    // Auto initialize camera and face detection when entering the screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCameraAndFaceDetection();
      _initializeSystemStatusProvider();
      // Auto-connect to server after other components are initialized
      _autoConnectToServer();
      // Start resource optimization monitoring
      _startResourceOptimizationMonitoring();

      // Start power saving watchdog
      _startPowerSavingWatchdog();
      // Start camera health monitoring
      _startCameraHealthMonitoring();
    });
  }

  /// Initialize performance optimization services
  void _initializePerformanceServices() {
    try {
      // Initialize performance optimization service
      PerformanceOptimizationService.instance.initialize();

      // Initialize memory pool service
      MemoryPoolService.instance.initialize();

      // Initialize face recognition v3 service
      _initializeFaceRecognitionV3Service();

      if (kDebugMode) {
        print('🚀 Performance optimization services initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing performance services: $e');
      }
    }
  }

  /// Initialize face recognition v3 service
  void _initializeFaceRecognitionV3Service() {
    try {
      // Initialize with new server URL (supports PNG, JPG, JPEG only)
      FaceRecognitionV3Service.instance.initialize(
        baseUrl: 'http://************',
        // authToken will be set when available
      );

      if (kDebugMode) {
        print('🔍 Face recognition v3 service initialized');
        print('   Server: http://************');
        print('   Health check: GET /health (200 = healthy)');
        print('   Supported formats: PNG, JPG, JPEG');
      }

      // Test health check immediately after initialization
      _testInitialHealthCheck();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing face recognition v3 service: $e');
      }
    }
  }

  /// Test health check immediately after service initialization
  void _testInitialHealthCheck() async {
    try {
      if (kDebugMode) {
        print('🏥 Testing initial health check...');
      }

      final isHealthy = await FaceRecognitionV3Service.instance
          .testConnection();

      if (kDebugMode) {
        print(
          '🏥 Initial health check result: ${isHealthy ? 'HEALTHY ✅' : 'UNHEALTHY ❌'}',
        );
      }

      // Update UI state
      if (mounted) {
        setState(() {
          _serverHealthStatus = isHealthy ? 'good' : 'error';
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Initial health check failed: $e');
      }

      if (mounted) {
        setState(() {
          _serverHealthStatus = 'error';
        });
      }
    }
  }

  /// Initialize real-time clock
  void _initializeClock() {
    _updateCurrentTime();
    _clockTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateCurrentTime();
    });
  }

  /// Update current time string
  void _updateCurrentTime() {
    final now = DateTime.now();
    final formattedTime = _dateTimeFormatter.format(now);

    // Convert English day names to Vietnamese
    String vietnameseTime = formattedTime
        .replaceAll('Monday', 'Thứ 2')
        .replaceAll('Tuesday', 'Thứ 3')
        .replaceAll('Wednesday', 'Thứ 4')
        .replaceAll('Thursday', 'Thứ 5')
        .replaceAll('Friday', 'Thứ 6')
        .replaceAll('Saturday', 'Thứ 7')
        .replaceAll('Sunday', 'Chủ nhật');

    if (mounted) {
      setState(() {
        _currentTime = vietnameseTime;
      });
    }
  }

  /// Initialize system status provider
  Future<void> _initializeSystemStatusProvider() async {
    try {
      // For now, initialize without external services
      // TODO: Integrate with actual services when available
      await _systemStatusProvider.initialize();

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing system status provider: $e');
      }
    }
  }

  /// Initialize face event trigger service for centralized trigger management
  Future<void> _initializeRelayTriggerService() async {
    try {
      // Initialize with default configuration
      await _faceEventTriggerService.initialize();

      if (kDebugMode) {
        print('✅ Face event trigger service initialized successfully');
        print(
          '   Service initialized: ${_faceEventTriggerService.isInitialized}',
        );
        print(
          '   Config - Face detection triggers: ${_faceEventTriggerService.config.enableFaceDetectionTriggers}',
        );
        print(
          '   Config - Face recognition triggers: ${_faceEventTriggerService.config.enableFaceRecognitionTriggers}',
        );
        print(
          '   Config - Auto USB-TTL connect: ${_faceEventTriggerService.config.enableAutoUsbTtlConnect}',
        );
      }

      // Additional check for relay management service status
      await _checkRelayManagementServiceStatus();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing face event trigger service: $e');
      }
    }
  }

  /// Check and debug relay management service status
  Future<void> _checkRelayManagementServiceStatus() async {
    try {
      // Import the relay management service
      final relayService = RelayManagementService.instance;

      if (kDebugMode) {
        print('🔌 Relay Management Service Status:');
        print('   Connected: ${relayService.isConnected}');
        print(
          '   Device config: ${relayService.deviceConfig?.deviceName ?? 'None'}',
        );

        if (relayService.deviceConfig != null) {
          print('   Device ID: ${relayService.deviceConfig!.deviceId}');
          print('   Relay count: ${relayService.deviceConfig!.relayCount}');
          print('   Baud rate: ${relayService.deviceConfig!.baudRate}');
        }

        // Try to get available devices
        try {
          final devices = await relayService.getAvailableDevices();
          print('   Available USB devices: ${devices.length}');
          for (int i = 0; i < devices.length; i++) {
            print('     Device $i: ${devices[i]}');
          }
        } catch (e) {
          print('   Error getting available devices: $e');
        }

        // If not connected, try manual initialization
        if (!relayService.isConnected) {
          print('🔄 Relay not connected, attempting manual initialization...');
          await _manuallyInitializeRelayService();
        }

        // If not connected, try manual initialization
        if (!relayService.isConnected) {
          print('🔄 Relay not connected, attempting manual initialization...');
          await _manuallyInitializeRelayService();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking relay management service status: $e');
      }
    }
  }

  /// Manually initialize relay management service if auto-connect failed
  Future<void> _manuallyInitializeRelayService() async {
    try {
      final relayService = RelayManagementService.instance;
      final relayConfigService = RelayConfigService.instance;

      // Initialize relay config service
      await relayConfigService.initialize();

      // Create device configuration from settings
      final deviceConfig = relayConfigService.createDeviceConfig(
        customDeviceId: 'manual-usb-ttl-relay',
      );

      if (kDebugMode) {
        print('🔧 Manually initializing relay management service...');
        print('   Device ID: ${deviceConfig.deviceId}');
        print('   Device Name: ${deviceConfig.deviceName}');
        print('   Relay Count: ${deviceConfig.relayCount}');
        print(
          '   Baud Rate: ${deviceConfig.baudRate} (${RelayConfigService.getBaudRateDescription(deviceConfig.baudRate)})',
        );
        print('   Auto Connect: ${relayConfigService.autoConnect}');
      }

      // Initialize with auto-connect enabled
      await relayService.initialize(config: deviceConfig, autoConnect: true);

      if (kDebugMode) {
        if (relayService.isConnected) {
          print('✅ Manual relay initialization successful');

          // Test relay connection with a simple command
          await _testRelayConnection();
        } else {
          print('⚠️ Manual relay initialization completed but not connected');
          print(
            '   This is normal if no USB-TTL relay device is physically connected',
          );
          print('   Relay triggers will be logged but not executed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Manual relay initialization failed: $e');
        print('   This is expected if no USB-TTL relay hardware is connected');
        print('   Relay triggers will be logged but not executed');
      }
    }
  }

  /// Test relay connection with a simple command
  Future<void> _testRelayConnection() async {
    try {
      final relayService = RelayManagementService.instance;

      if (kDebugMode) {
        print('🧪 Testing relay connection...');
      }

      // Try to send a simple command to test connectivity
      // This will turn R0 ON briefly then OFF to test the connection
      await relayService.controlRelay(0, relay.RelayAction.on);
      await Future.delayed(const Duration(milliseconds: 100));
      await relayService.controlRelay(0, relay.RelayAction.off);

      if (kDebugMode) {
        print('✅ Relay connection test successful - R0 ON/OFF completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Relay connection test failed: $e');
        print(
          '   This indicates the relay hardware may not be properly connected',
        );
      }
    }
  }

  /// Initialize device management service for always-on functionality
  Future<void> _initializeDeviceManagement() async {
    try {
      await _deviceManagementService.initialize();

      if (kDebugMode) {
        print('✅ Device management service initialized successfully');
        print(
          '   Wake lock enabled: ${_deviceManagementService.isWakeLockEnabled}',
        );
        print(
          '   Current brightness: ${(_deviceManagementService.currentBrightness * 100).toInt()}%',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing device management service: $e');
      }
    }
  }

  /// Handle face detection state changes for relay control
  void _onFaceDetectionStateChanged(bool hasFace) {
    try {
      // Delegate to centralized face event trigger service
      _faceEventTriggerService.onFaceDetectionStateChanged(hasFace);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling face detection state change: $e');
      }
    }
  }

  Future<void> _initializeCameraAndFaceDetection() async {
    try {
      // Initialize terminal face detection provider (MediaPipe/Hybrid)
      final terminalFaceProvider = Provider.of<terminal.TerminalFaceDetectionProvider>(context, listen: false);
      if (!terminalFaceProvider.isInitialized) {
        await terminalFaceProvider.initialize();
      }

      // Initialize legacy face detector for camera integration (still needed for camera overlay)
      await _faceDetectionProvider.initializeFaceDetector(
        performanceMode:
            FaceDetectorMode.fast, // Use fast mode for better performance
        enableTracking: false, // Disable tracking to save performance
        enableLandmarks: false, // Disable landmarks to save performance
        enableContours: false, // Disable contours to save performance
        enableClassification:
            false, // Disable classification to save performance
        config: camera_config.FaceDetectionConfig.performance, // Use performance config
      );

      // Set up listener for face detection changes
      _faceDetectionProvider.addListener(_onFaceDetectionChanged);

      // Set up face detection state callback for relay control
      _faceDetectionProvider.setFaceDetectionStateCallback(
        _onFaceDetectionStateChanged,
      );

      // Initialize configuration sync service
      await ConfigComponentSyncService.instance.initialize(
        faceDetectionProvider: _faceDetectionProvider,
      );

      // Set up image stream callback for face detection with enhanced safety checks
      _cameraProvider.setImageStreamCallback((image, camera) {
        // Safety checks to prevent processing when disposed
        if (!mounted) {
          return;
        }

        // Enhanced null safety checks for AndroidCamera compatibility
        if (image.planes.isEmpty) {
          if (kDebugMode) {
            print('⚠️ Invalid camera image (empty planes), skipping frame');
          }
          return;
        }

        // Additional safety checks for image data integrity
        try {
          // Verify image planes have valid data
          for (final plane in image.planes) {
            if (plane.bytes.isEmpty) {
              if (kDebugMode) {
                print(
                  '⚠️ Invalid camera image (empty plane bytes), skipping frame',
                );
              }
              return;
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Error checking image planes: $e');
          }
          return;
        }

        try {
          // Update camera health monitoring
          _lastImageProcessed = DateTime.now();

          // Always process face detection for wake-up capability
          // Even in extreme power saving mode, we need face detection to wake up
          _faceDetectionProvider.detectFacesFromImage(image, camera);

          // For face cropping, we'll need to capture image when face is detected
          // This will be handled in the face crop overlay widget
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Error processing image for face detection: $e');
            print('   Image format: ${image.format.group}');
            print('   Image size: ${image.width}x${image.height}');
            print('   Camera: ${camera.name}');
            print('   Planes count: ${image.planes.length}');
          }

          // If we get critical errors, try to restart the camera stream
          if (e.toString().contains('Null check operator') ||
              e.toString().contains('RangeError') ||
              e.toString().contains('null value')) {
            if (kDebugMode) {
              print(
                '🚨 Critical camera error detected, attempting recovery...',
              );
            }
            _handleCameraStreamError();
          }
        }
      });

      // Initialize camera with performance config
      await _cameraProvider.initializeCamera(CameraConfig.performance);

      // Add delay and force enable image stream after camera is ready
      await Future.delayed(const Duration(milliseconds: 500));

      if (_cameraProvider.isCameraReady &&
          !_cameraProvider.isStreamingEnabled) {
        await _cameraProvider.toggleImageStream(true);

        // Double check after another delay
        await Future.delayed(const Duration(milliseconds: 200));
        if (!_cameraProvider.isStreamingEnabled) {
          await _cameraProvider.toggleImageStream(true);
        }
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing camera and face detection: $e');
      }

      // Handle specific camera errors
      if (e.toString().contains('ImageReader') ||
          e.toString().contains('getSurface')) {
        if (kDebugMode) {
          print('🔧 ImageReader error detected - attempting recovery');
        }
        await _recoverFromCameraError();
      } else {
        // Show error to user for other types of errors
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi khởi tạo camera: ${e.toString()}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Thử lại',
                onPressed: () => _initializeCameraAndFaceDetection(),
              ),
            ),
          );
        }
      }
    }
  }

  /// Pause all services when app goes to background
  void _pauseAllServices() {
    try {
      if (kDebugMode) {
        print('⏸️ Pausing all services for background mode...');
      }

      // 1. Pause camera and face detection
      _cameraProvider.toggleImageStream(false);

      // 2. Cancel all timers
      _clockTimer.cancel();
      _cameraHealthTimer?.cancel();
      _recognitionThrottleTimer?.cancel();

      // 3. Pause face event triggers
      // Note: Don't dispose completely, just pause

      // 4. Emergency stop all relays for safety
      RelayThrottleService.instance.emergencyStop();

      // 5. Set power saving mode
      _isExtremePowerSaving = true;

      if (kDebugMode) {
        print('✅ All services paused successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error pausing services: $e');
      }
    }
  }

  /// Shutdown all services when app is terminated
  void _shutdownAllServices() {
    try {
      if (kDebugMode) {
        print('💀 Shutting down all services for app termination...');
      }

      // 1. Dispose camera completely
      _cameraProvider.disposeCamera();

      // 2. Cancel all timers
      _clockTimer.cancel();
      _cameraHealthTimer?.cancel();
      _recognitionThrottleTimer?.cancel();

      // 3. Emergency stop all relays
      RelayThrottleService.instance.emergencyStop();

      // 4. Dispose relay throttle service
      RelayThrottleService.instance.dispose();

      // 5. Stop face recognition processing
      _faceDetectionProvider.dispose();

      // 6. Dispose performance optimization services
      PerformanceOptimizationService.instance.dispose();
      MemoryPoolService.instance.dispose();

      if (kDebugMode) {
        print('✅ All services shutdown successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error shutting down services: $e');
      }
    }
  }

  /// Handle app resume with enhanced camera recovery
  void _handleAppResume() {
    if (kDebugMode) {
      print('🔄 Handling app resume - checking camera state');
    }

    // Use timer to avoid blocking lifecycle callback
    Timer(const Duration(milliseconds: 100), () async {
      try {
        // Resume from extreme power saving first
        if (_isExtremePowerSaving) {
          _resumeFromResourceOptimization();
          return; // _resumeFromResourceOptimization handles camera recovery
        }

        // Check if camera needs recovery
        bool needsRecovery = false;

        if (!_cameraProvider.isCameraReady) {
          needsRecovery = true;
          if (kDebugMode) {
            print('🔄 Camera not ready - needs full recovery');
          }
        } else if (!_cameraProvider.isStreamingEnabled) {
          if (kDebugMode) {
            print(
              '🔄 Camera ready but streaming disabled - attempting restart',
            );
          }

          // Try to restart image stream with enhanced error handling
          try {
            await _cameraProvider.toggleImageStream(true);

            // Wait and verify stream started
            await Future.delayed(const Duration(milliseconds: 500));

            if (!_cameraProvider.isStreamingEnabled) {
              if (kDebugMode) {
                print('⚠️ Image stream failed to start - needs full recovery');
              }
              needsRecovery = true;
            } else {
              if (kDebugMode) {
                print('✅ Image stream restarted successfully');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('❌ Failed to restart image stream: $e');
              // Check if this is the ImageReader null reference error
              if (e.toString().contains('ImageReader.getSurface()') ||
                  e.toString().contains('NullPointerException')) {
                print(
                  '🔍 Detected ImageReader null reference - forcing full recovery',
                );
              }
            }
            needsRecovery = true;
          }
        }

        // Perform full recovery if needed
        if (needsRecovery) {
          if (kDebugMode) {
            print('🔄 Performing full camera recovery after app resume');
          }
          await _performFullCameraRecovery();
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error during app resume recovery: $e');
        }
      }
    });
  }

  /// Perform full camera recovery with enhanced error handling
  Future<void> _performFullCameraRecovery() async {
    try {
      if (kDebugMode) {
        print('🔄 Starting full camera recovery...');
      }

      // Step 1: Stop image stream safely
      try {
        if (_cameraProvider.isStreamingEnabled) {
          await _cameraProvider.toggleImageStream(false);
          await Future.delayed(const Duration(milliseconds: 300));
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Error stopping image stream during recovery: $e');
        }
      }

      // Step 2: Dispose camera safely
      try {
        await _cameraProvider.disposeCamera();
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Error disposing camera during recovery: $e');
        }
      }

      // Step 3: Reinitialize camera with retry logic
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          if (kDebugMode) {
            print('🔄 Camera recovery attempt ${retryCount + 1}/$maxRetries');
          }

          await _cameraProvider.initializeCamera(CameraConfig.performance);

          // Wait for camera to be ready
          await Future.delayed(const Duration(milliseconds: 500));

          if (_cameraProvider.isCameraReady) {
            // Try to start image stream
            await _cameraProvider.toggleImageStream(true);
            await Future.delayed(const Duration(milliseconds: 300));

            if (_cameraProvider.isStreamingEnabled) {
              if (kDebugMode) {
                print('✅ Full camera recovery successful');
              }
              return;
            }
          }

          retryCount++;
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(milliseconds: 1000 * retryCount));
          }
        } catch (e) {
          retryCount++;
          if (kDebugMode) {
            print('❌ Camera recovery attempt ${retryCount} failed: $e');
          }

          if (retryCount < maxRetries) {
            await Future.delayed(Duration(milliseconds: 1000 * retryCount));
          }
        }
      }

      if (kDebugMode) {
        print('❌ Full camera recovery failed after $maxRetries attempts');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Critical error during camera recovery: $e');
      }
    }
  }

  /// Handle camera stream errors by attempting recovery
  void _handleCameraStreamError() {
    // Use a timer to avoid blocking the image stream callback
    Timer(const Duration(milliseconds: 100), () {
      _recoverFromCameraError();
    });
  }

  /// Recover from camera errors by reinitializing
  Future<void> _recoverFromCameraError() async {
    try {
      if (kDebugMode) {
        print('🔄 Attempting camera error recovery...');
      }

      // Dispose current camera
      await _cameraProvider.disposeCamera();

      // Wait longer for cleanup
      await Future.delayed(const Duration(milliseconds: 1000));

      // Retry initialization
      await _initializeCameraAndFaceDetection();

      if (kDebugMode) {
        print('✅ Camera error recovery successful');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Camera error recovery failed: $e');
      }

      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Không thể khôi phục camera. Vui lòng khởi động lại ứng dụng.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 10),
          ),
        );
      }
    }
  }

  void _ensureFaceDetectionActive() {
    if (!_cameraProvider.isStreamingEnabled) {
      _cameraProvider.toggleImageStream(true);
    }
  }

  /// Toggle hiển thị user info (sẽ được gọi từ camera detection logic)
  void toggleUserInfo(bool hasUser) {
    setState(() {
      _hasUserInFrame = hasUser;
    });
  }

  /// Callback khi có face detection thay đổi
  void _onFaceDetectionChanged() {
    // Throttle UI updates to improve performance
    final now = DateTime.now();
    if (now.difference(_lastUIUpdate) < _uiUpdateThrottle) {
      return; // Skip this update if too soon
    }
    _lastUIUpdate = now;

    // Ensure face detection is always enabled
    _ensureFaceDetectionActive();

    // Get current best face from provider
    final face = _faceDetectionProvider.bestFace;
    final newDetectedFacesCount = _faceDetectionProvider.faces.length;
    double newBestFaceQuality = 0.0;
    bool hasQualityFace = false;

    if (face != null) {
      newBestFaceQuality = _faceDetectionProvider.getFaceQuality(face);
      // Check if face quality is good enough for recognition (LOWERED for Telpo F8)
      final isInFrame = _isFaceInGuideFrame(face);
      hasQualityFace =
          newBestFaceQuality >= 0.3 && isInFrame; // Lowered from 0.4 to 0.3

      if (kDebugMode) {
        print('🔍 Face quality check:');
        print(
          '   Quality: ${(newBestFaceQuality * 100).toStringAsFixed(1)}% (min: 30%)',
        );
        print('   In frame: $isInFrame');
        print('   Has quality face: $hasQualityFace');
        print('   Power saving: $_isExtremePowerSaving');
      }

      // Update face detection time for resource optimization
      _lastFaceDetectedTime = DateTime.now();

      // Wake up from power saving mode with lower threshold for responsiveness
      if (_isExtremePowerSaving) {
        // Lower quality threshold for wake-up (any face detection)
        if (newBestFaceQuality >= 0.2) {
          // Lower threshold for wake-up
          if (kDebugMode) {
            print(
              '🌅 Waking up from extreme power saving - face quality: ${(newBestFaceQuality * 100).toStringAsFixed(1)}%',
            );
          }
          _resumeFromResourceOptimization();
        }
      } else {
        // Normal operation - resume from regular optimization
        _resumeFromResourceOptimization();
      }

      if (kDebugMode) {
        print(
          '👁️ Face detected - Quality: ${(newBestFaceQuality * 100).toStringAsFixed(1)}%, In frame: ${_isFaceInGuideFrame(face)}, Usable: $hasQualityFace, PowerSaving: $_isExtremePowerSaving',
        );
      }

      // Trigger recognition with throttle if quality is good enough
      if (hasQualityFace && !_isExtremePowerSaving) {
        final shouldTrigger =
            !_isRecognitionThrottleActive; // Check if throttle is not active
        final canRecognize = !_isRecognizing && shouldTrigger;

        if (kDebugMode) {
          print('🎯 Recognition trigger check:');
          print(
            '   Face quality: ${(newBestFaceQuality * 100).toStringAsFixed(1)}%',
          );
          print('   Is recognizing: $_isRecognizing');
          print('   Should trigger: $shouldTrigger');
          print('   Can recognize: $canRecognize');
        }

        if (canRecognize) {
          if (kDebugMode) {
            print('🚀 TRIGGERING FACE RECOGNITION TO SERVER');
          }
          _triggerFaceRecognition();
        } else {
          if (kDebugMode) {
            print('⏸️ Recognition blocked - already recognizing or throttled');
          }
        }
      }

      // Avatar will be updated when recognition request is sent to server
      // This ensures avatar shows the exact same image that was sent for recognition

      // If face is detected but not processed (e.g., stable face), keep current user
      _keepCurrentUserForStableFace();
    } else {
      // No face detected - check if we should clear current user
      _checkUserDisplayTimeout();
    }

    // Only call setState if there's a significant change
    bool shouldUpdate = false;

    if (_bestFace != face) {
      shouldUpdate = true;
    } else if (_detectedFacesCount != newDetectedFacesCount) {
      shouldUpdate = true;
    } else if ((_bestFaceQuality - newBestFaceQuality).abs() >
        FaceDetectionConstants.significantQualityChange) {
      shouldUpdate = true; // Only update if quality changed significantly
    } else if (hasQualityFace != _hasUserInFrame) {
      shouldUpdate = true;
    }

    if (shouldUpdate) {
      setState(() {
        _bestFace = face;
        _detectedFacesCount = newDetectedFacesCount;
        _bestFaceQuality = newBestFaceQuality;

        // Statistics removed - debug info moved to logs

        if (hasQualityFace != _hasUserInFrame) {
          toggleUserInfo(hasQualityFace);
        }
      });
    }
  }

  /// Check if face is in acceptable position (adapted from face_capture_screen)
  bool _isFaceInGuideFrame(Face face) {
    // Get screen size and camera preview dimensions
    final screenSize = MediaQuery.of(context).size;

    if (!_cameraProvider.isCameraReady) return false;

    final controller = _cameraProvider.cameraController!;
    final previewSize = controller.value.previewSize!;

    // Calculate actual preview dimensions (same as in _buildCameraPreview)
    final isPortrait = screenSize.height > screenSize.width;
    final cameraWidth = isPortrait ? previewSize.height : previewSize.width;
    final cameraHeight = isPortrait ? previewSize.width : previewSize.height;
    final cameraAspectRatio = cameraWidth / cameraHeight;

    // TELPO F8: Camera preview dimensions
    final actualPreviewWidth = screenSize.width; // Fill full width
    final actualPreviewHeight = actualPreviewWidth / cameraAspectRatio;

    // Use center area of camera preview as guide frame (not full screen)
    final previewCenter = Offset(
      actualPreviewWidth / 2,
      actualPreviewHeight / 2,
    );
    final radius = actualPreviewWidth * 0.3; // 30% of preview width

    // Calculate scale factors for camera preview area
    final scaleX = actualPreviewWidth / previewSize.height; // Note: swapped for camera
    final scaleY = actualPreviewHeight / previewSize.width;

    // Transform face bounding box to preview coordinates
    double faceLeft = face.boundingBox.left * scaleX;
    double faceTop = face.boundingBox.top * scaleY;
    double faceRight = face.boundingBox.right * scaleX;
    double faceBottom = face.boundingBox.bottom * scaleY;

    // Handle front camera mirroring
    if (controller.description.lensDirection == CameraLensDirection.front) {
      final temp = faceLeft;
      faceLeft = actualPreviewWidth - faceRight;
      faceRight = actualPreviewWidth - temp;
    }

    // Calculate face center within preview area
    final faceCenter = Offset(
      (faceLeft + faceRight) / 2,
      (faceTop + faceBottom) / 2,
    );

    // Check if face center is within acceptable area of preview
    final distance = (faceCenter - previewCenter).distance;
    final isInFrame = distance <= radius;

    if (kDebugMode) {
      print('🎯 Face in guide frame check:');
      print('   Preview size: ${actualPreviewWidth.toInt()}x${actualPreviewHeight.toInt()}');
      print('   Preview center: ${previewCenter.dx.toInt()}, ${previewCenter.dy.toInt()}');
      print('   Face center: ${faceCenter.dx.toInt()}, ${faceCenter.dy.toInt()}');
      print('   Distance: ${distance.toInt()}, Radius: ${radius.toInt()}');
      print('   In frame: $isInFrame');
    }

    return isInFrame;
  }

  /// Build camera preview với aspect ratio processing
  Widget _buildCameraPreview() {
    if (!_cameraProvider.isCameraReady) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: AppColors.primary),
              const SizedBox(height: 16),
              Text(
                _cameraProvider.status == CameraStatus.error
                    ? 'Lỗi camera: ${_cameraProvider.errorMessage ?? "Không xác định"}'
                    : _cameraProvider.status == CameraStatus.permissionDenied
                    ? 'Cần cấp quyền camera'
                    : 'Đang khởi tạo camera...',
                style: const TextStyle(color: Colors.white, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              if (_cameraProvider.status == CameraStatus.error ||
                  _cameraProvider.status == CameraStatus.permissionDenied) ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _initializeCameraAndFaceDetection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Thử lại'),
                ),
              ],
            ],
          ),
        ),
      );
    }

    final controller = _cameraProvider.cameraController!;
    final screenSize = MediaQuery.of(context).size;

    // Lấy tỷ lệ thực tế của camera và normalize theo orientation
    final previewSize = controller.value.previewSize!;
    final isPortrait = screenSize.height > screenSize.width;

    // Normalize camera size theo orientation
    final cameraWidth = isPortrait ? previewSize.height : previewSize.width;
    final cameraHeight = isPortrait ? previewSize.width : previewSize.height;
    final cameraAspectRatio = cameraWidth / cameraHeight;

    // TELPO F8: Ưu tiên chiều rộng, căn giữa chiều cao
    final actualPreviewWidth = screenSize.width; // Fill full width
    final actualPreviewHeight =
        actualPreviewWidth /
        cameraAspectRatio; // Calculate height to maintain ratio

    if (kDebugMode) {
      print('📱 TELPO F8 Camera Layout:');
      print(
        '   Screen: ${screenSize.width.toInt()}x${screenSize.height.toInt()}',
      );
      print(
        '   Camera: ${cameraWidth.toInt()}x${cameraHeight.toInt()} (ratio: ${cameraAspectRatio.toStringAsFixed(2)})',
      );
      print(
        '   Preview: ${actualPreviewWidth.toInt()}x${actualPreviewHeight.toInt()}',
      );
      print('   Fit mode: Fill width, center height');
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black, // Background cho khoảng trống
      child: ClipRect(
        // Clip để cắt phần tràn ra ngoài
        child: Stack(
          children: [
            // TELPO F8: Camera preview - fill width, center height
            Positioned(
              left: 0, // Fill full width
              top:
                  (screenSize.height - actualPreviewHeight) /
                  2, // Center height
              child: SizedBox(
                width: actualPreviewWidth, // = screenSize.width
                height: actualPreviewHeight, // Calculated to maintain ratio
                child: CameraPreview(controller),
              ),
            ),

            // Face detection overlay - positioned to match camera preview
            Positioned(
              left: 0, // Same as camera preview
              top: (screenSize.height - actualPreviewHeight) / 2, // Same as camera preview
              child: SizedBox(
                width: actualPreviewWidth, // Same as camera preview
                height: actualPreviewHeight, // Same as camera preview
                child: FaceDetectionOverlay(
                  faces: _faceDetectionProvider.faces,
                  bestFace: _bestFace,
                  imageSize: Size(
                    controller
                        .value
                        .previewSize!
                        .height, // Note: swapped for camera
                    controller.value.previewSize!.width,
                  ),
                  canvasSize: Size(actualPreviewWidth, actualPreviewHeight), // Match camera preview size
                  isFrontCamera:
                      controller.description.lensDirection ==
                      CameraLensDirection.front,
                  getFaceQuality: _faceDetectionProvider.getFaceQuality,
                  child: Container(), // Empty child since we want overlay on top
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (kDebugMode) {
      print('🔄 StreamScreen disposing - shutting down all services...');
    }

    // Shutdown all services completely when widget is disposed
    _shutdownAllServices();

    // Remove app lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    // Remove listener
    _faceDetectionProvider.removeListener(_onFaceDetectionChanged);

    // Dispose providers
    _cameraProvider.dispose();
    _faceDetectionProvider.dispose();
    _systemStatusProvider.dispose();
    _deviceRegistrationProvider.dispose();

    // Dispose device management service
    _deviceManagementService.dispose();

    // Khôi phục lại system UI khi thoát màn hình
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    if (kDebugMode) {
      print('✅ StreamScreen disposed successfully - app ready to exit');
    }

    super.dispose();
  }

  /// Handle app lifecycle changes for better camera management
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (kDebugMode) {
      print('📱 App lifecycle state changed to: $state');
    }

    switch (state) {
      case AppLifecycleState.resumed:
        // App is back in foreground - handle camera recovery
        _handleAppResume();
        break;

      case AppLifecycleState.paused:
        // App is going to background - pause all services to save resources
        if (kDebugMode) {
          print('⏸️ App paused - pausing all services');
        }
        _pauseAllServices();
        break;

      case AppLifecycleState.inactive:
        // App is inactive (e.g., phone call) - pause all services
        if (kDebugMode) {
          print('😴 App inactive - pausing all services');
        }
        _pauseAllServices();
        break;

      case AppLifecycleState.detached:
        // App is being terminated - shutdown everything completely
        if (kDebugMode) {
          print('💀 App detached - shutting down all services');
        }
        _shutdownAllServices();
        break;

      case AppLifecycleState.hidden:
        // App is hidden - pause all services (same as paused)
        if (kDebugMode) {
          print('🙈 App hidden - pausing all services');
        }
        _pauseAllServices();
        break;
    }
  }

  /// Start camera health monitoring
  void _startCameraHealthMonitoring() {
    _cameraHealthTimer?.cancel();
    _cameraHealthTimer = Timer.periodic(_cameraHealthCheckInterval, (timer) {
      _checkCameraHealth();
    });
  }

  /// Check camera health and recover if needed
  void _checkCameraHealth() {
    if (_lastImageProcessed == null) return;

    final now = DateTime.now();
    final timeSinceLastImage = now.difference(_lastImageProcessed!);

    if (timeSinceLastImage >= _cameraTimeoutThreshold) {
      if (kDebugMode) {
        print(
          '⚠️ Camera timeout detected - no images for ${timeSinceLastImage.inSeconds}s',
        );
      }

      // Attempt camera recovery
      _recoverFromCameraTimeout();
    }
  }

  /// Recover from camera timeout
  Future<void> _recoverFromCameraTimeout() async {
    try {
      if (kDebugMode) {
        print('🔄 Attempting camera timeout recovery...');
      }

      // Reset image processed time to prevent multiple recovery attempts
      _lastImageProcessed = DateTime.now();

      // Try to restart image stream first
      if (_cameraProvider.isCameraReady) {
        await _cameraProvider.toggleImageStream(false);
        await Future.delayed(const Duration(milliseconds: 500));
        await _cameraProvider.toggleImageStream(true);

        if (kDebugMode) {
          print('✅ Image stream restarted');
        }
      } else {
        // Camera not ready, full recovery
        await _recoverFromCameraError();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Camera timeout recovery failed: $e');
      }
    }
  }

  /// Check if we should clear current user based on timeout
  void _checkUserDisplayTimeout() {
    // If no user is displayed, nothing to do
    if (_currentUser == null || _lastRecognitionTime == null) return;

    final now = DateTime.now();
    final timeSinceRecognition = now.difference(_lastRecognitionTime!);

    // If no face has been detected for the timeout period, clear the user
    if (timeSinceRecognition > _userDisplayTimeout) {
      if (kDebugMode) {
        print(
          '⏱️ User display timeout reached (${_userDisplayTimeout.inSeconds}s) - clearing user',
        );
      }

      setState(() {
        _currentUser = null;
        _lastRecognitionTime = null;
        _allowAccess = false;
        _accessReason = null;
      });
    }
  }

  /// Keep current user when face is stable (but still send to server periodically)
  void _keepCurrentUserForStableFace() {
    if (_currentUser != null && _lastRecognitionTime != null) {
      // Update recognition time to keep user displayed
      setState(() {
        _lastRecognitionTime = DateTime.now();
      });

      if (kDebugMode) {
        print(
          '👤 Keeping current user for stable face: ${_currentUser!.name} (server still validates)',
        );
      }
    }
  }

  /// Update avatar with the exact same image being sent to server for recognition
  /// This ensures avatar shows the same image that was sent for recognition
  void _updateAvatarWithRecognitionImage(Uint8List imageBytes, double quality) {
    if (!mounted) return;

    try {
      // Skip if we already have a recent update (within 1 second)
      if (_lastDetectionImageTime != null) {
        final timeSinceLastUpdate = DateTime.now().difference(
          _lastDetectionImageTime!,
        );
        if (timeSinceLastUpdate.inSeconds < 1) return;
      }

      // Update avatar with the recognition image data
      setState(() {
        _detectionSourceImage = imageBytes;
        _detectionSourceQuality = quality;
        _lastDetectionImageTime = DateTime.now();
      });

      // Also update the CapturedFaceProvider for compatibility
      if (_bestFace != null) {
        final capturedFaceProvider = Provider.of<CapturedFaceProvider>(
          context,
          listen: false,
        );
        capturedFaceProvider.updateCapturedFace(
          imageBytes: imageBytes,
          face: _bestFace!,
          quality: quality,
        );
      }

      if (kDebugMode) {
        print(
          '📸 Avatar updated with recognition image: ${imageBytes.length} bytes',
        );
        print('   Quality: ${(quality * 100).toStringAsFixed(1)}%');
        print('   This is the EXACT same image sent to server for recognition');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating avatar with recognition image: $e');
      }
    }
  }

  /// Recognize face using v3.1 API with image bytes
  Future<dynamic> _recognizeFaceV3({
    required Uint8List imageBytes,
    required double quality,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Validate and convert image format if needed
      final conversionResult = await ImageFormatService.ensureSupportedFormat(
        imageBytes,
        preferredFormat: 'jpg', // Server prefers JPEG
        quality: 85,
      );

      if (!conversionResult.success) {
        throw Exception(
          'Image format validation failed: ${conversionResult.error}',
        );
      }

      // Create temporary file with validated image
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = ImageFormatService.getFileExtension(
        conversionResult.format!,
      );
      final tempFile = File(
        '${tempDir.path}/face_recognition_$timestamp$extension',
      );

      // Write validated image bytes to temporary file
      await tempFile.writeAsBytes(conversionResult.imageBytes!);

      if (kDebugMode) {
        print('🔍 Face recognition v3.1 request');
        print('   Temp file: ${tempFile.path}');
        print(
          '   Image format: ${conversionResult.format} (${conversionResult.converted ? 'converted' : 'original'})',
        );
        print('   Image size: ${conversionResult.imageBytes!.length} bytes');
        print('   Quality: ${(quality * 100).toStringAsFixed(1)}%');
        if (conversionResult.converted) {
          print('   Original format: ${conversionResult.originalFormat}');
          print('   Original size: ${conversionResult.originalSize} bytes');
        }
      }

      // Create request with metadata
      final deviceId =
          metadata?['device_id'] ??
          'terminal-${DateTime.now().millisecondsSinceEpoch}';
      final trackingId =
          metadata?['tracking_id'] ??
          'track-${DateTime.now().millisecondsSinceEpoch}';

      final request = FaceRecognitionV3Request(
        deviceId: deviceId,
        cameraId: 'camera-1',
        cameraIp: '127.0.0.1',
        trackingId: trackingId,
        detectedFace: tempFile,
      );

      // Send recognition request
      final v3Response = await FaceRecognitionV3Service.instance.recognizeFace(
        request,
      );

      // Clean up temporary file
      try {
        await tempFile.delete();
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Failed to delete temp file: $e');
        }
      }

      // Convert v3.1 response to legacy format for backward compatibility
      final legacyResult = FaceRecognitionMigrationHelper.convertToLegacyFormat(
        v3Response,
      );

      if (kDebugMode) {
        print('✅ Face recognition v3.1 completed');
        print('   Raw v3 response: ${v3Response.toString()}');
        print('   Success: ${legacyResult['success']}');
        print('   User: ${legacyResult['user']?['name'] ?? 'Unknown'}');
        print('   User ID: ${legacyResult['user']?['id'] ?? 'None'}');
        print(
          '   Confidence: ${((legacyResult['confidence'] ?? 0.0) * 100).toStringAsFixed(1)}%',
        );
        print('   Quality Pass: ${legacyResult['qualityPass']}');
        print('   Mask Detected: ${legacyResult['maskDetected']}');
        print('   Reason: ${legacyResult['reason']}');
        print('   Event ID: ${legacyResult['eventId']}');
        print('   Tracking ID: ${legacyResult['trackingId']}');
      }

      // Return in legacy format for compatibility
      return _convertV3ResponseToLegacyResult(legacyResult);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition v3.1 error: $e');
      }

      // Return error in legacy format
      return _createErrorResult('Face recognition failed: $e');
    }
  }

  /// Convert v3.1 response to legacy result format
  dynamic _convertV3ResponseToLegacyResult(Map<String, dynamic> legacyResult) {
    return _LegacyFaceRecognitionResult(
      success: legacyResult['success'] ?? false,
      recognized: legacyResult['user'] != null,
      user: legacyResult['user'] != null
          ? _LegacyFaceRecognitionUser(
              id: legacyResult['user']['id'] ?? '',
              name: legacyResult['user']['name'] ?? '',
            )
          : null,
      confidence: legacyResult['confidence'] ?? 0.0,
      reason: legacyResult['reason'] ?? '',
      allowAccess: legacyResult['user'] != null,
      qualityPass: legacyResult['qualityPass'] ?? false,
      maskDetected: legacyResult['maskDetected'] ?? false,
    );
  }

  /// Create error result in legacy format
  dynamic _createErrorResult(String message) {
    return _LegacyFaceRecognitionResult(
      success: false,
      recognized: false,
      user: null,
      confidence: 0.0,
      reason: message,
      allowAccess: false,
      qualityPass: false,
      maskDetected: false,
    );
  }

  /// Parse server URL and extract host and port intelligently
  Map<String, String> _parseServerUrl(String serverInput, String portInput) {
    String serverUrl = serverInput;
    String port = portInput;

    // If server input contains http:// or https://, extract port if available
    if (serverInput.startsWith('http://') ||
        serverInput.startsWith('https://')) {
      try {
        final uri = Uri.parse(serverInput);

        // If URL has explicit port, use it (override port input)
        if (uri.hasPort) {
          port = uri.port.toString();
        }
      } catch (e) {
        // If parsing fails, ignore
      }
    }

    // Return as-is, no automatic protocol or port addition
    return {'serverUrl': serverUrl, 'port': port};
  }

  /// Connect to custom server with provided settings
  Future<void> _connectToCustomServer(
    DeviceRegistrationProvider provider,
    String serverUrl,
    String port,
    String deviceId,
    String deviceName,
  ) async {
    try {
      // Parse server URL intelligently
      final parsed = _parseServerUrl(serverUrl, port);
      String actualServerUrl = parsed['serverUrl']!;
      final actualPort = parsed['port']!;

      // If serverUrl is 'localhost', replace with actual IP for mobile devices
      if (actualServerUrl == 'localhost') {
        actualServerUrl = '************';
      }

      // Build final URL - use exactly what user provided
      String finalUrl = actualServerUrl;

      // Only add protocol if not already present and it's a simple host
      if (!actualServerUrl.startsWith('http://') &&
          !actualServerUrl.startsWith('https://')) {
        finalUrl = 'http://$actualServerUrl';
      }

      // Add port only if provided and not already in URL
      if (actualPort.isNotEmpty && !finalUrl.contains(':$actualPort')) {
        if (finalUrl.startsWith('http://') || finalUrl.startsWith('https://')) {
          final uri = Uri.parse(finalUrl);
          finalUrl = '${uri.scheme}://${uri.host}:$actualPort${uri.path}';
        } else {
          finalUrl = '$finalUrl:$actualPort';
        }
      }

      if (kDebugMode) {
        print('Original input: $serverUrl:$port');
        print('Parsed to: $actualServerUrl:$actualPort');
        print('Final URL: $finalUrl');
        print('Device ID: $deviceId, Name: $deviceName');
      }

      // Register device with custom server settings using normal encryption
      final success = await provider.registerDevice(
        deviceId: deviceId,
        deviceName: deviceName,
        deviceType: 'terminal',
        location: 'Custom Location',
        serverUrl: finalUrl,
        capabilities: ['relay_control', 'face_auth', 'status_monitoring'],
        metadata: {
          'app_version': '1.0.0',
          'platform': 'flutter',
          'connection_type': 'custom',
        },
        usePlainText: false, // Use normal encryption by default
      );

      if (!success) {
        throw Exception('Failed to register device with custom server');
      }

      if (kDebugMode) {
        print('Successfully connected to custom server: $finalUrl');
      }

      // Initialize face recognition service
      if (provider.secureComm != null) {
        _faceRecognitionService = FaceRecognitionService(provider.secureComm!);
        if (kDebugMode) {
          print('✅ Face recognition service initialized');
        }
      }

      // Start server health check monitoring
      _startServerHealthCheck();

      // Start face recognition timer since overlay is hidden
      _startFaceRecognitionTimer();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to connect to custom server: $e');
      }
      rethrow;
    }
  }

  /// Start resource optimization monitoring
  void _startResourceOptimizationMonitoring() {
    _resourceOptimizationTimer?.cancel();
    _resourceOptimizationTimer = Timer.periodic(_optimizationCheckInterval, (
      timer,
    ) {
      _checkResourceOptimization();
    });
  }

  /// Start power saving watchdog to prevent getting stuck in power saving mode
  void _startPowerSavingWatchdog() {
    _powerSavingWatchdogTimer?.cancel();
    _powerSavingWatchdogTimer = Timer.periodic(const Duration(seconds: 10), (
      timer,
    ) {
      _checkPowerSavingWatchdog();
    });
  }

  /// Check if we're stuck in power saving mode and force recovery
  void _checkPowerSavingWatchdog() {
    if (!mounted) return;

    // If we're in extreme power saving mode for too long, force recovery
    if (_isExtremePowerSaving && _lastFaceDetectedTime != null) {
      final timeSinceLastFace = DateTime.now().difference(
        _lastFaceDetectedTime!,
      );

      // If we've been in power saving for more than 5 minutes, force recovery
      if (timeSinceLastFace > const Duration(minutes: 5)) {
        if (kDebugMode) {
          print('🚨 Power saving watchdog: Force recovering from stuck state');
        }

        // Force recovery
        setState(() {
          _isExtremePowerSaving = false;
          _isResourceOptimized = false;
        });

        // Restore system state
        try {
          _setScreenBrightness(1.0);
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
          _initializeClock();
        } catch (e) {
          if (kDebugMode) {
            print('❌ Watchdog recovery error: $e');
          }
        }
      }
    }

    // Also check for stuck resource optimization
    if (_isResourceOptimized &&
        !_isExtremePowerSaving &&
        _lastFaceDetectedTime != null) {
      final timeSinceLastFace = DateTime.now().difference(
        _lastFaceDetectedTime!,
      );

      // If we've been in resource optimization for more than 10 minutes, force recovery
      if (timeSinceLastFace > const Duration(minutes: 10)) {
        if (kDebugMode) {
          print(
            '🚨 Resource optimization watchdog: Force recovering from stuck state',
          );
        }

        setState(() {
          _isResourceOptimized = false;
        });
      }
    }
  }

  /// Check if resource optimization should be applied
  void _checkResourceOptimization() {
    // Initialize _lastFaceDetectedTime if null (app just started)
    if (_lastFaceDetectedTime == null) {
      _lastFaceDetectedTime = DateTime.now();
      return;
    }

    final now = DateTime.now();
    final timeSinceLastFace = now.difference(_lastFaceDetectedTime!);

    if (kDebugMode) {
      print(
        '🔋 Power saving check: ${timeSinceLastFace.inSeconds}s since last face (opt: ${_faceAbsenceOptimizationThreshold.inSeconds}s, extreme: ${_extremePowerSavingThreshold.inSeconds}s)',
      );
    }

    // Check for extreme power saving (screen off) first
    if (timeSinceLastFace >= _extremePowerSavingThreshold &&
        !_isExtremePowerSaving) {
      _applyExtremePowerSaving();
    }
    // Then check for regular resource optimization
    else if (timeSinceLastFace >= _faceAbsenceOptimizationThreshold &&
        !_isResourceOptimized) {
      _applyResourceOptimization();
    }
  }

  /// Apply resource optimization when no face detected for long time
  void _applyResourceOptimization() {
    if (_isResourceOptimized) return;

    setState(() {
      _isResourceOptimized = true;
    });

    if (kDebugMode) {
      print(
        '🔋 Applying resource optimization - no face detected for ${_faceAbsenceOptimizationThreshold.inSeconds}s',
      );
    }

    // Implement actual resource optimization:
    try {
      // 1. Reduce frame rate for face detection
      // Note: This would require modifications to the FaceDetectionProvider

      // 2. Pause non-essential timers to save CPU
      _faceRecognitionTimer?.cancel();

      // 3. Reduce UI update frequency
      // UI updates are already throttled, but we could make them even less frequent

      if (kDebugMode) {
        print(
          '🔋 Resource optimization applied: Reduced timers and processing',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error applying resource optimization: $e');
      }
    }
  }

  /// Apply extreme power saving mode (screen off)
  void _applyExtremePowerSaving() {
    if (_isExtremePowerSaving) return;

    setState(() {
      _isExtremePowerSaving = true;
      _isResourceOptimized = true; // Also apply regular optimization
    });

    if (kDebugMode) {
      final timeSinceLastFace = _lastFaceDetectedTime != null
          ? DateTime.now().difference(_lastFaceDetectedTime!).inSeconds
          : 0;
      print(
        '🌙 Applying extreme power saving - screen off mode after ${timeSinceLastFace}s without face',
      );
      print('🔍 Face detection will continue for wake-up capability');
    }

    // Reduce screen brightness to minimum for power saving
    _setScreenBrightness(0.0);

    // Reduce all UI updates
    // Pause non-essential timers
    _clockTimer.cancel();

    // Keep only camera and face detection running for wake-up
    // Face detection callback will still work to detect wake-up
  }

  /// Resume from resource optimization when face is detected
  void _resumeFromResourceOptimization() {
    bool wasExtremePowerSaving = _isExtremePowerSaving;

    if (!_isResourceOptimized && !_isExtremePowerSaving) return;

    if (kDebugMode) {
      if (wasExtremePowerSaving) {
        print('🌅 Resuming from extreme power saving - face detected');
      } else {
        print('⚡ Resuming from resource optimization - face detected');
      }
    }

    // Critical: Force state update first
    setState(() {
      _isResourceOptimized = false;
      _isExtremePowerSaving = false;
    });

    // Restore screen brightness to maximum when exiting power saving mode
    if (wasExtremePowerSaving) {
      try {
        _setScreenBrightness(1.0); // Restore to maximum brightness

        // Restart clock with safety check
        if (mounted) {
          _initializeClock();
        }

        // Force system UI restoration
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

        if (kDebugMode) {
          print('💡 Screen brightness and system UI restored');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error restoring from power saving: $e');
        }
        // Force another state update to ensure we're out of power saving mode
        if (mounted) {
          setState(() {
            _isExtremePowerSaving = false;
            _isResourceOptimized = false;
          });
        }
      }
    }

    // Restart timers that were paused during optimization
    try {
      if (wasExtremePowerSaving || _isResourceOptimized) {
        // Restart face recognition timer if it was paused
        _startFaceRecognitionTimer();

        if (kDebugMode) {
          print('🔄 Restarted paused timers and processing');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restarting timers: $e');
      }
    }

    // Additional safety: Schedule a delayed check to ensure we're fully restored
    if (mounted) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && (_isExtremePowerSaving || _isResourceOptimized)) {
          if (kDebugMode) {
            print('🔄 Force restoring stuck power saving state');
          }
          setState(() {
            _isExtremePowerSaving = false;
            _isResourceOptimized = false;
          });
        }
      });
    }
  }

  /// Set screen brightness (0.0 to 1.0)
  void _setScreenBrightness(double brightness) {
    try {
      // Clamp brightness value between 0.0 and 1.0
      final clampedBrightness = brightness.clamp(0.0, 1.0);

      // Set system brightness
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.black,
          statusBarColor: Colors.black,
        ),
      );

      // Note: For actual brightness control, you would need to use a plugin like
      // screen_brightness or implement platform-specific brightness control
      // For now, we'll just log the brightness change

      if (kDebugMode) {
        print(
          '💡 Setting screen brightness to ${(clampedBrightness * 100).toStringAsFixed(0)}%',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set screen brightness: $e');
      }
    }
  }

  /// Start server health check monitoring
  void _startServerHealthCheck() {
    _serverHealthCheckTimer?.cancel();
    _serverHealthCheckTimer = Timer.periodic(_healthCheckInterval, (timer) {
      _performServerHealthCheck();
    });
  }

  /// Start face recognition timer (since overlay is hidden)
  void _startFaceRecognitionTimer() {
    // Cancel existing timer if any
    _faceRecognitionTimer?.cancel();

    // Start new timer to process faces periodically
    _faceRecognitionTimer = Timer.periodic(
      _faceRecognitionInterval,
      (_) => _processFaceRecognition(),
    );

    if (kDebugMode) {
      print(
        '🔄 Started face recognition timer - processing every ${_faceRecognitionInterval.inSeconds}s',
      );
    }
  }

  /// Process face recognition periodically
  void _processFaceRecognition() {
    // Skip if no face detected or in power saving mode
    if (_bestFace == null || _isExtremePowerSaving) return;

    // Skip if already recognizing
    if (_isRecognizing) return;

    // Skip if processed too recently
    final now = DateTime.now();
    if (_lastFaceRecognitionTime != null) {
      final timeSinceLastRecognition = now.difference(
        _lastFaceRecognitionTime!,
      );
      if (timeSinceLastRecognition < _faceRecognitionInterval) return;
    }

    // Update last recognition time
    _lastFaceRecognitionTime = now;

    if (kDebugMode) {
      print('🔄 Face recognition timer triggered - face overlay is hidden');
      final quality = _faceDetectionProvider.getFaceQuality(_bestFace!);
      print('   Face quality: ${(quality * 100).toStringAsFixed(1)}%');
    }

    // Trigger face recognition with throttle
    _triggerFaceRecognition();
  }

  /// Start recognition throttle timer with visual progress
  void _startRecognitionThrottle() {
    // Cancel existing timer
    _recognitionThrottleTimer?.cancel();

    // Set initial state
    _lastRecognitionRequestTime = DateTime.now();
    _isRecognitionThrottleActive = true;
    _throttleProgress = 0.0;

    if (kDebugMode) {
      print(
        '⏱️ Starting recognition throttle - ${_recognitionThrottleDuration.inSeconds}s cooldown',
      );
    }

    // Start progress update timer
    _recognitionThrottleTimer = Timer.periodic(_throttleUpdateInterval, (
      timer,
    ) {
      _updateThrottleProgress();
    });

    // Update UI
    if (mounted) {
      setState(() {});
    }
  }

  /// Update throttle progress and check if completed
  void _updateThrottleProgress() {
    if (_lastRecognitionRequestTime == null) return;

    final now = DateTime.now();
    final elapsed = now.difference(_lastRecognitionRequestTime!);
    final progress =
        elapsed.inMilliseconds / _recognitionThrottleDuration.inMilliseconds;

    if (progress >= 1.0) {
      // Throttle completed
      _completeRecognitionThrottle();
    } else {
      // Update progress
      _throttleProgress = progress.clamp(0.0, 1.0);

      if (mounted) {
        setState(() {});
      }
    }
  }

  /// Complete recognition throttle and force new recognition request
  void _completeRecognitionThrottle() {
    _recognitionThrottleTimer?.cancel();
    _isRecognitionThrottleActive = false;
    _throttleProgress = 1.0;

    if (kDebugMode) {
      print('✅ Recognition throttle completed');
    }

    // ONLY force recognition if there is currently a face detected
    // No point in forcing recognition when no face is present
    if (_bestFace != null) {
      if (kDebugMode) {
        print(
          '🔄 FORCED recognition after throttle - face detected, forcing fresh server request',
        );
        print(
          '   This ensures fresh data from server and prevents client-side errors',
        );
      }
      _forcedRecognitionAfterThrottle();
    } else {
      if (kDebugMode) {
        print('⏸️ No face detected - skipping forced recognition');
        print(
          '   Throttle completed but no face present, waiting for face detection',
        );
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  /// Force recognition after throttle completion to refresh server data
  /// This bypasses normal quality and state checks to ensure fresh data
  /// ONLY runs when a face is currently detected
  Future<void> _forcedRecognitionAfterThrottle() async {
    // Double check: only proceed if face is currently detected
    if (_bestFace == null) {
      if (kDebugMode) {
        print('⏸️ FORCED recognition cancelled - no face detected');
      }
      return;
    }

    // Clear any stale client data first
    setState(() {
      _currentUser = null;
      _lastRecognitionTime = null;
      _allowAccess = false;
      _accessReason = null;
      _isRecognizing = true;
    });

    if (kDebugMode) {
      print('🔄 FORCED recognition - clearing stale client data');
      print('   Requesting fresh data from server...');
      print(
        '   This prevents users from standing too long without authentication',
      );
    }

    try {
      // CRITICAL: Re-check if face is still present before proceeding
      // Face might have disappeared between throttle completion and this execution
      if (_bestFace == null) {
        if (kDebugMode) {
          print(
            '❌ FORCED recognition cancelled - face disappeared during execution',
          );
          print('   Face was present at throttle completion but gone now');
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      // Check face quality again to ensure it's still valid
      final currentQuality = _faceDetectionProvider.getFaceQuality(_bestFace!);
      if (currentQuality < 0.3) {
        // Lower threshold for forced recognition
        if (kDebugMode) {
          print(
            '❌ FORCED recognition cancelled - face quality too low: ${(currentQuality * 100).toStringAsFixed(1)}%',
          );
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      if (kDebugMode) {
        print(
          '✅ FORCED recognition proceeding - face confirmed present with quality: ${(currentQuality * 100).toStringAsFixed(1)}%',
        );
      }

      // Start new throttle cycle for the forced request
      _startRecognitionThrottle();

      // Get current camera image for recognition
      final cameraImage = _faceDetectionProvider.latestCameraImage;
      final cameraDescription = _faceDetectionProvider.latestCameraDescription;

      if (cameraImage == null || cameraDescription == null) {
        if (kDebugMode) {
          print('❌ No camera image available for forced recognition');
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      // Convert camera image to bytes for recognition
      final imageBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
        quality: FaceDetectionConstants.recognitionImageQuality,
      );

      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ Failed to convert camera image to bytes');
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      // Force recognition with lower quality threshold to ensure server contact
      final quality = _faceDetectionProvider.getFaceQuality(_bestFace!);

      if (kDebugMode) {
        print('🔄 FORCED recognition - bypassing quality checks');
        print('   Face quality: ${(quality * 100).toStringAsFixed(1)}%');
        print('   Image size: ${imageBytes.length} bytes');
      }

      // Update avatar with the same image being sent to server
      _updateAvatarWithRecognitionImage(imageBytes, quality);

      // Send to server for recognition using v3.1 API (forced, bypasses normal quality checks)
      final result = await _recognizeFaceV3(
        imageBytes: imageBytes,
        quality: quality,
        metadata: {
          'source': 'forced_recognition_after_throttle',
          'timestamp': DateTime.now().toIso8601String(),
          'device_id': 'terminal_001',
          'forced': true,
          'reason': 'prevent_long_wait_without_auth',
        },
      );

      if (mounted) {
        if (result != null && result.recognized && result.user != null) {
          setState(() {
            _currentUser = result.user;
            _lastRecognitionTime = DateTime.now();
            _isRecognizing = false;
            _allowAccess = result.allowAccess;
            _accessReason = result.reason;
          });

          if (kDebugMode) {
            print('✅ FORCED recognition successful: ${result.user!.name}');
            print('   Server provided fresh data - client error prevented');
          }

          // Trigger side effects for successful recognition
          recognizedSideEffect(result);
        } else {
          // Clear current user if forced recognition failed
          setState(() {
            _currentUser = null;
            _lastRecognitionTime = null;
            _allowAccess = false;
            _accessReason = null;
            _isRecognizing = false;
          });

          if (kDebugMode) {
            print(
              '❌ FORCED recognition failed - server contacted but no match',
            );
            print('   Fresh data received from server');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ FORCED recognition error: $e');
      }
      if (mounted) {
        setState(() {
          _isRecognizing = false;
        });
      }
    }
  }

  /// Trigger face recognition if conditions are met
  void _triggerFaceRecognition() {
    // V3.1 do not using _faceRecognitionService, ignore it
    // Skip if throttle is active
    if (_isRecognitionThrottleActive) {
      if (kDebugMode) {
        print(
          '⏸️ Recognition skipped - throttle active (${(_throttleProgress * 100).toStringAsFixed(1)}%)',
        );
      }
      return;
    }

    if (kDebugMode) {
      print(
        '🚀 Triggering face recognition ${_bestFace != null ? 'with face' : 'without face'}, ${_isRecognizing ? 'already recognizing' : 'not recognizing'} (using V3 API)',
      );
    }

    // Skip if no face or already recognizing
    if (_bestFace == null || _isRecognizing) {
      return;
    }

    if (kDebugMode) {
      print('Face recognition triggered');
    }

    // Check face quality
    final quality = _faceDetectionProvider.getFaceQuality(_bestFace!);
    if (quality < FaceDetectionConstants.minFaceQualityForRecognition) {
      if (kDebugMode) {
        print(
          '⏸️ Recognition skipped - low quality: ${(quality * 100).toStringAsFixed(1)}%',
        );
      }
      return;
    }

    if (kDebugMode) {
      print(
        '🚀 Triggering face recognition - quality: ${(quality * 100).toStringAsFixed(1)}%',
      );
    }

    // Start throttle timer
    _startRecognitionThrottle();

    // Perform actual face recognition
    _performFaceRecognition(quality);
  }

  /// Perform the actual face recognition process
  Future<void> _performFaceRecognition(double quality) async {
    setState(() {
      _isRecognizing = true;
    });

    try {
      // Get current camera image for recognition
      final cameraImage = _faceDetectionProvider.latestCameraImage;
      final cameraDescription = _faceDetectionProvider.latestCameraDescription;

      if (cameraImage == null || cameraDescription == null) {
        if (kDebugMode) {
          print('❌ No camera image available for recognition');
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      if (kDebugMode) {
        print('📸 Capturing image for recognition...');
        print(
          '   Image size: ${cameraImage.planes.fold<int>(0, (sum, plane) => sum + plane.bytes.length)} bytes',
        );
      }

      // Convert camera image to bytes for recognition
      final imageBytes = await CameraImageConverter.convertToJpegBytes(
        cameraImage,
        cameraDescription,
        quality: FaceDetectionConstants.recognitionImageQuality,
      );

      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ Failed to convert camera image to bytes');
        }
        setState(() {
          _isRecognizing = false;
        });
        return;
      }

      if (kDebugMode) {
        print('🔍 Processing face recognition...');
        print('   Face quality: ${(quality * 100).toStringAsFixed(1)}%');
        print('   Image size: ${imageBytes.length} bytes');
      }

      // Update avatar with the same image being sent to server
      _updateAvatarWithRecognitionImage(imageBytes, quality);

      // Send to server for recognition using v3.1 API
      final result = await _recognizeFaceV3(
        imageBytes: imageBytes,
        quality: quality,
        metadata: {
          'source': 'terminal_camera_stream',
          'timestamp': DateTime.now().toIso8601String(),
          'device_id': 'terminal_001',
          'quality': quality,
        },
      );

      if (kDebugMode) {
        print('🔍 Face recognition result: ${result?.toString() ?? 'null'}');
      }

      if (mounted) {
        if (result != null && result.recognized && result.user != null) {
          setState(() {
            _currentUser = result.user;
            _lastRecognitionTime = DateTime.now();
            _isRecognizing = false;
            _allowAccess = result.allowAccess;
            _accessReason = result.reason;
          });

          if (kDebugMode) {
            print('✅ Face recognized: ${result.user!.name}');
          }

          // Trigger side effects for successful recognition
          recognizedSideEffect(result);
        } else {
          // Clear current user if recognition failed
          setState(() {
            _currentUser = null;
            _lastRecognitionTime = null;
            _allowAccess = false;
            _accessReason = null;
            _isRecognizing = false;
          });

          if (kDebugMode) {
            print('❌ Face not recognized');
            if (result != null) {
              print('   Status: ${result.status}');
              print('   Message: ${result.message}');
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Face recognition error: $e');
      }
      if (mounted) {
        setState(() {
          _isRecognizing = false;
        });
      }
    }
  }

  /// Handle side effects for face recognition result
  void recognizedSideEffect(dynamic result) {
    // Delegate to centralized face event trigger service
    _faceEventTriggerService.onFaceRecognitionResult(result);
  }

  /// Perform server health check via /health endpoint
  Future<void> _performServerHealthCheck() async {
    try {
      // Simple health check: GET /health should return 200
      final isHealthy = await FaceRecognitionV3Service.instance
          .testConnection();

      setState(() {
        _serverHealthStatus = isHealthy ? 'good' : 'error';
      });

      if (kDebugMode) {
        print(
          '🏥 Server health check result: ${isHealthy ? 'HEALTHY (200)' : 'UNHEALTHY (non-200)'}',
        );
        print(
          '   Endpoint: ${FaceRecognitionV3Service.instance.getServiceStatus()['baseUrl']}/health',
        );
      }
    } catch (e) {
      setState(() {
        _serverHealthStatus = 'error';
      });

      if (kDebugMode) {
        print('❌ Server health check failed: $e');
      }
    }
  }

  /// Auto-connect to server with default settings
  ///
  /// This method automatically connects to the default server after app startup.
  /// Configuration can be modified here or made configurable via settings.
  ///
  /// Default settings:
  /// - Server: http://************
  /// - Device ID: terminal_001
  /// - Device Name: Terminal App Auto
  /// - Delay: 2 seconds (to ensure other components are initialized)
  Future<void> _autoConnectToServer() async {
    // Add delay to ensure other components are fully initialized
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    try {
      if (kDebugMode) {
        print(
          '⏸️ Auto-connect to server disabled - new server doesn\'t support device registration',
        );
        print(
          '   Face recognition will work directly via v3.1 API without device registration',
        );
      }

      // Skip device registration - new server doesn't support it
      // Face recognition will work directly via FaceRecognitionV3Service
      return;

      // OLD CODE (disabled for new server):
      // const defaultServer = 'http://************';
      // const defaultPort = '';
      // const defaultDeviceId = 'terminal_001';
      // const defaultDeviceName = 'Terminal App Auto';
      //
      // if (_deviceRegistrationProvider.isConnected) {
      //   return;
      // }
      //
      // await _connectToCustomServer(...);
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Auto-connect failed: $e');
        print('   User can manually connect via server communication widget');
      }
      // Don't show error to user - they can manually connect if needed
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _cameraProvider),
        ChangeNotifierProvider.value(value: _faceDetectionProvider),
        ChangeNotifierProvider.value(value: _systemStatusProvider),
        ChangeNotifierProvider.value(value: _deviceRegistrationProvider),
      ],
      child: TelpoF8TouchDetector(
        showFPSOverlay: true,
        showPowerStatus: true,
        child: Scaffold(
          backgroundColor: Colors.black,
          body: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // Camera stream với aspect ratio processing
                _buildCameraPreview(),

                // Extreme power saving overlay (black screen)
                if (_isExtremePowerSaving)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.bedtime,
                              color: Colors.white24,
                              size: 48,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Chế độ tiết kiệm pin',
                              style: TextStyle(
                                color: Colors.white24,
                                fontSize: 16,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Đưa mặt vào để kích hoạt',
                              style: TextStyle(
                                color: Colors.white12,
                                fontSize: 12,
                              ),
                            ),
                            SizedBox(height: 16),
                            // Add pulsing indicator to show system is still active
                            _buildPowerSavingIndicator(),
                          ],
                        ),
                      ),
                    ),
                  ),

                // Overlay top bar với logo và status icons (hide in extreme power saving)
                if (!_isExtremePowerSaving)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: _buildTopOverlay(),
                  ),
                // Overlay bottom section với thông tin thời gian và user (hide in extreme power saving)
                if (!_isExtremePowerSaving)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: _buildBottomSection(),
                  ),
                // Face crop overlay widget - COMPLETELY HIDDEN (face display moved to avatar)
                // Face processing still runs in background for recognition without visual overlay
                // The TerminalFaceCropOverlay widget is no longer displayed but its functionality
                // is maintained through direct calls to the relevant methods
                // Face stats overlay - REMOVED for production (debug info moved to logs)

                // Recognition throttle indicator - now integrated into user info border

                // Server communication widget overlay
                if (_showServerCommunication)
                  _buildServerCommunicationOverlay(),

                // Relay testing widget overlay
                if (_showRelayTesting) _buildRelayTestingOverlay(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Get container background color based on access status
  Color _getContainerBackgroundColor() {
    if (_currentUser != null) {
      if (_allowAccess) {
        // Green tint for allowed access
        return const Color(
          0xFF2E4B3B,
        ).withValues(alpha: 0.8); // Darker green tint
      } else {
        // Red tint for denied access
        return const Color(
          0xFF4B2E2E,
        ).withValues(alpha: 0.8); // Darker red tint
      }
    }
    // Default color for no user
    return const Color(0xFF1E293B).withValues(alpha: 0.7);
  }

  /// Get container border color based on access status
  Color _getContainerBorderColor() {
    if (_currentUser != null) {
      if (_allowAccess) {
        return Colors.green.shade600; // Green border for allowed access
      } else {
        return Colors.red.shade600; // Red border for denied access
      }
    }
    // Default border color for no user
    return const Color(0xFF30415E);
  }

  /// Top overlay với logo và status icons
  Widget _buildTopOverlay() {
    return Container(
      height: 57,
      padding: EdgeInsets.only(right: AppDimensions.spacing24),
      decoration: BoxDecoration(
        color: const Color(0xFF373E47).withValues(alpha: 0.7),
      ),
      child: Row(
        children: [
          // Logo section
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.spacing24),
            child: Row(
              children: [
                // C-CAM Logo SVG
                SizedBox(
                  width: 36.36,
                  height: 24,
                  child: CustomPaint(painter: _CCAMLogoPainter()),
                ),
                SizedBox(width: AppDimensions.spacing8),
                Text(
                  'C-CAM',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          // Status icons
          _buildStatusIcons(),
        ],
      ),
    );
  }

  /// Status icons section
  Widget _buildStatusIcons() {
    return Consumer<SystemStatusProvider>(
      builder: (context, statusProvider, child) {
        // Get status states
        final databaseStatus = statusProvider.getStatus(
          SystemStatusType.database,
        );
        // Network status temporarily hidden to save space
        // final networkStatus = statusProvider.getStatus(SystemStatusType.network);
        // Server status is now handled by health check
        // final syncStatus = statusProvider.getStatus(SystemStatusType.sync);

        return Row(
          children: [
            _buildStatusIcon(
              _buildDatabaseIcon(_getStatusColor(databaseStatus?.state)),
              _getStatusColor(databaseStatus?.state),
              databaseStatus?.message ?? 'Đang kiểm tra...',
              SystemStatusType.database,
            ),
            SizedBox(
              width: AppDimensions.spacing8,
            ), // Reduced from spacing12 to spacing8
            // Network icon temporarily hidden to save space
            // _buildStatusIcon(
            //   _buildWifiIcon(_getStatusColor(networkStatus?.state)),
            //   _getStatusColor(networkStatus?.state),
            //   networkStatus?.message ?? 'Đang kiểm tra...',
            //   SystemStatusType.network,
            // ),
            // SizedBox(width: AppDimensions.spacing12),

            // Face detection engine indicator
            _buildFaceDetectionEngineIndicator(),
            SizedBox(
              width: AppDimensions.spacing8,
            ),
            // Configuration button
            _buildConfigurationButton(),
            SizedBox(
              width: AppDimensions.spacing8,
            ), // Reduced from spacing12 to spacing8
            // Server communication toggle button
            _buildServerCommToggle(),
            SizedBox(width: AppDimensions.spacing8),
            // Relay testing toggle button
            _buildRelayTestingToggle(),
          ],
        );
      },
    );
  }

  /// Build face detection engine indicator
  Widget _buildFaceDetectionEngineIndicator() {
    return Consumer<terminal.TerminalFaceDetectionProvider>(
      builder: (context, provider, child) {
        // Get current engine info
        String engineName = 'ML Kit'; // Default
        Color engineColor = Colors.blue.shade600;
        IconData engineIcon = Icons.psychology;

        // Try to get actual engine info if available
        try {
          if (provider.isInitialized) {
            // Check current engine type
            if (provider.useMediaPipe) {
              engineName = 'MediaPipe';
              engineColor = Colors.blue.shade600;
              engineIcon = Icons.smart_toy;
            } else {
              // Using ML Kit
              engineName = 'ML Kit';
              engineColor = Colors.green.shade600;
              engineIcon = Icons.psychology;
            }

            // Check if hybrid mode is available
            if (provider.currentEngine == FaceDetectionEngineType.hybrid) {
              engineName = 'Hybrid';
              engineColor = Colors.purple.shade600;
              engineIcon = Icons.merge_type;
            }
          } else {
            engineName = 'Chưa khởi tạo';
            engineColor = Colors.grey.shade600;
            engineIcon = Icons.psychology_outlined;
          }
        } catch (e) {
          engineName = 'Lỗi';
          engineColor = Colors.red.shade600;
          engineIcon = Icons.error_outline;
        }

        return GestureDetector(
          onTap: () => _showFaceDetectionEngineDetails(),
          child: Tooltip(
            message: 'Face Detection Engine: $engineName',
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.spacing8,
                vertical: AppDimensions.spacing8,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFF19313B),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: engineColor,
                  width: AppDimensions.borderNormal,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    engineIcon,
                    size: 14,
                    color: engineColor,
                  ),
                  SizedBox(width: 4),
                  Text(
                    engineName,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: engineColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build individual status icon
  Widget _buildStatusIcon(
    Widget icon,
    Color borderColor,
    String tooltip,
    SystemStatusType statusType,
  ) {
    return GestureDetector(
      onTap: () => _showStatusDetails(statusType),
      child: Tooltip(
        message: tooltip,
        child: Container(
          padding: EdgeInsets.all(AppDimensions.spacing12),
          decoration: BoxDecoration(
            color: const Color(0xFF19313B),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: borderColor,
              width: AppDimensions.borderNormal,
            ),
          ),
          child: SizedBox(width: 14, height: 14, child: icon),
        ),
      ),
    );
  }

  /// Build server communication toggle button
  Widget _buildServerCommToggle() {
    // Determine status color based on connection and modal state
    Color statusColor;
    IconData iconData;
    String tooltipMessage;

    if (_showServerCommunication) {
      statusColor = Colors.blue.shade600; // Modal is open
      iconData = Icons.router;
      tooltipMessage = 'Ẩn Server Communication';
    } else {
      // Show status based on face recognition service health
      switch (_serverHealthStatus) {
        case 'good':
          statusColor = Colors.green.shade600; // Service healthy
          iconData = Icons.router_outlined;
          tooltipMessage = 'Hiện Server Communication (Service OK)';
          break;
        case 'error':
          statusColor = Colors.red.shade600; // Service error
          iconData = Icons.router_outlined;
          tooltipMessage = 'Hiện Server Communication (Service Error)';
          break;
        default:
          statusColor = Colors.grey.shade600; // Unknown status
          iconData = Icons.router_outlined;
          tooltipMessage = 'Hiện Server Communication (Status Unknown)';
      }
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _showServerCommunication = !_showServerCommunication;
        });
      },
      child: Tooltip(
        message: tooltipMessage,
        child: Container(
          padding: EdgeInsets.all(AppDimensions.spacing12),
          decoration: BoxDecoration(
            color: const Color(0xFF19313B),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: statusColor,
              width: AppDimensions.borderNormal,
            ),
          ),
          child: SizedBox(
            width: 14,
            height: 14,
            child: Icon(iconData, color: statusColor, size: 14),
          ),
        ),
      ),
    );
  }

  /// Build configuration button
  Widget _buildConfigurationButton() {
    return GestureDetector(
      onTap: () {
        _openConfigurationMenu();
      },
      child: Tooltip(
        message: 'Cấu hình hệ thống',
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.blue.shade600, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(8),
          child: Icon(Icons.tune, color: Colors.blue.shade600, size: 20),
        ),
      ),
    );
  }

  /// Open configuration menu
  void _openConfigurationMenu() {
    showDialog(
      context: context,
      builder: (context) => _buildConfigurationDialog(),
    );
  }

  /// Build configuration dialog
  Widget _buildConfigurationDialog() {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF2A2D3A),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue.shade600, width: 2),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.tune, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Cấu hình hệ thống',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // Configuration options
            _buildConfigOption(
              'Cấu hình nhanh',
              'Điều chỉnh các thông số cơ bản',
              Icons.speed,
              () => _openQuickConfig(),
            ),
            const SizedBox(height: 12),
            _buildConfigOption(
              'Cấu hình chi tiết',
              'Truy cập tất cả thông số hệ thống',
              Icons.settings,
              () => _openDetailedConfig(),
            ),
            const SizedBox(height: 12),
            _buildConfigOption(
              'Trạng thái hệ thống',
              'Xem thông tin và trạng thái cấu hình',
              Icons.info_outline,
              () => _openConfigStatus(),
            ),
            const SizedBox(height: 12),
            _buildConfigOption(
              'Cấu hình Relay',
              'Thiết lập trigger relay cho face detection/recognition',
              Icons.electrical_services,
              () => _openRelayTriggerConfig(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build configuration option
  Widget _buildConfigOption(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF373E47),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade600, width: 1),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.blue.shade400, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey.shade400, fontSize: 14),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade500,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// Open quick configuration
  void _openQuickConfig() {
    Navigator.of(context).pop(); // Close dialog first
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => _buildQuickConfigScreen()));
  }

  /// Open detailed configuration
  void _openDetailedConfig() {
    Navigator.of(context).pop(); // Close dialog first
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => _buildDetailedConfigScreen()),
    );
  }

  /// Open configuration status
  void _openConfigStatus() {
    Navigator.of(context).pop(); // Close dialog first
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => _buildConfigStatusScreen()));
  }

  /// Build quick configuration screen
  Widget _buildQuickConfigScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D29),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2D3A),
        title: const Text(
          'Cấu hình nhanh',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            QuickConfigPresets.faceDetectionConfig(
              onChanged: () => setState(() {}),
            ),
            QuickConfigPresets.networkConfig(onChanged: () => setState(() {})),
            QuickConfigPresets.performanceConfig(
              onChanged: () => setState(() {}),
            ),
            QuickConfigPresets.uiConfig(onChanged: () => setState(() {})),
          ],
        ),
      ),
    );
  }

  /// Build detailed configuration screen
  Widget _buildDetailedConfigScreen() {
    return const AdminConfigScreen();
  }

  /// Build configuration status screen
  Widget _buildConfigStatusScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1D29),
      appBar: AppBar(
        backgroundColor: const Color(0xFF2A2D3A),
        title: const Text(
          'Trạng thái cấu hình',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16),
        child: ConfigStatusWidget(showDetails: true),
      ),
    );
  }

  /// Get color based on status state
  Color _getStatusColor(StatusState? state) {
    switch (state) {
      case StatusState.active:
        return const Color(0xFF34D38D); // Green
      case StatusState.warning:
        return const Color(0xFFFFA500); // Orange
      case StatusState.inactive:
        return const Color(0xFFFF4444); // Red
      case StatusState.unknown:
      case null:
        return const Color(0xFF666666); // Gray
    }
  }

  /// Convert color to hex string
  String _colorToHex(Color color) {
    // Using new color component accessors
    final red = (color.r * 255.0).round() & 0xff;
    final green = (color.g * 255.0).round() & 0xff;
    final blue = (color.b * 255.0).round() & 0xff;
    return '#${red.toRadixString(16).padLeft(2, '0')}'
        '${green.toRadixString(16).padLeft(2, '0')}'
        '${blue.toRadixString(16).padLeft(2, '0')}';
  }

  /// Build pulsing indicator for power saving mode
  Widget _buildPowerSavingIndicator() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(seconds: 2),
      tween: Tween(begin: 0.3, end: 1.0),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.green.shade400,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Đang chờ...',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: value * 0.5),
                  fontSize: 10,
                ),
              ),
            ],
          ),
        );
      },
      onEnd: () {
        // Restart animation if still in power saving mode
        if (mounted && _isExtremePowerSaving) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _isExtremePowerSaving) {
              setState(() {});
            }
          });
        }
      },
    );
  }

  /// Build avatar content with face from detection source or default icon
  Widget _buildAvatarContent(CapturedFaceProvider capturedFaceProvider) {
    // Priority 1: Show detection source image if available and recent
    if (_detectionSourceImage != null && _lastDetectionImageTime != null) {
      final timeSinceUpdate = DateTime.now().difference(
        _lastDetectionImageTime!,
      );
      if (timeSinceUpdate < FaceDetectionConstants.avatarDisplayDuration) {
        return Stack(
          children: [
            // Detection source face image
            Image.memory(
              _detectionSourceImage!,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
            // Quality badge
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(_detectionSourceQuality * 100).round()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        );
      }
    }

    // Priority 2: Fallback to CapturedFaceProvider if available (legacy support)
    if (capturedFaceProvider.hasCapturedFace &&
        capturedFaceProvider.isRecentCapture) {
      return Stack(
        children: [
          // Captured face image
          Image.memory(
            capturedFaceProvider.capturedFaceImage!,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
          // Quality badge
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${(capturedFaceProvider.capturedFaceQuality * 100).round()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      );
    }

    // Priority 3: Show face detection status if face is detected
    if (_bestFace != null && _bestFaceQuality > 0.6) {
      // High quality face detected - show green background with face icon
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.green.shade100, Colors.green.shade200],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.face, size: 32, color: Colors.green.shade700),
            const SizedBox(height: 4),
            Text(
              '${(_bestFaceQuality * 100).round()}%',
              style: TextStyle(
                color: Colors.green.shade700,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    } else if (_bestFace != null) {
      // Priority 4: Low quality face detected - show orange background
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.orange.shade100, Colors.orange.shade200],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.face_retouching_natural,
              size: 32,
              color: Colors.orange.shade700,
            ),
            const SizedBox(height: 4),
            Text(
              '${(_bestFaceQuality * 100).round()}%',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    } else {
      // Priority 5: No face detected - show default icon
      return const SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Icon(Icons.person, size: 40, color: Colors.grey),
      );
    }
  }

  /// Show status details dialog
  void _showStatusDetails(SystemStatusType statusType) {
    final status = _systemStatusProvider.getStatus(statusType);
    if (status == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF19313B),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: _getStatusColor(status.state),
                    width: 2,
                  ),
                ),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: _getStatusIcon(
                    statusType,
                    _getStatusColor(status.state),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _getStatusTitle(statusType),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusDetailRow(
                'Trạng thái',
                _getStatusStateText(status.state),
              ),
              const SizedBox(height: 12),
              _buildStatusDetailRow('Thông báo', status.message),
              const SizedBox(height: 12),
              _buildStatusDetailRow(
                'Cập nhật lần cuối',
                _formatDateTime(status.lastUpdated),
              ),
              const SizedBox(height: 16),
              _buildStatusActions(statusType),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Đóng',
                style: TextStyle(color: Colors.white70),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show face detection engine details dialog
  void _showFaceDetectionEngineDetails() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<terminal.TerminalFaceDetectionProvider>(
          builder: (context, provider, child) {
            // Get engine information
            String engineName = 'ML Kit';
            String engineDescription = 'Google ML Kit face detection - Good for mobile apps';
            Color engineColor = Colors.green.shade600;
            IconData engineIcon = Icons.psychology;

            if (provider.useMediaPipe) {
              engineName = 'MediaPipe BlazeFace';
              engineDescription = 'MediaPipe BlazeFace - Optimized for real-time detection';
              engineColor = Colors.blue.shade600;
              engineIcon = Icons.smart_toy;
            }

            if (provider.currentEngine == FaceDetectionEngineType.hybrid) {
              engineName = 'Hybrid Detector';
              engineDescription = 'Hybrid detector - Combines multiple engines for reliability';
              engineColor = Colors.purple.shade600;
              engineIcon = Icons.merge_type;
            }

            return AlertDialog(
              backgroundColor: const Color(0xFF1A1A1A),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              ),
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF19313B),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(
                        color: engineColor,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      engineIcon,
                      size: 20,
                      color: engineColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Face Detection Engine',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusDetailRow('Engine hiện tại', engineName),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow('Mô tả', engineDescription),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow(
                    'Trạng thái',
                    provider.isInitialized ? 'Đang hoạt động' : 'Chưa khởi tạo',
                  ),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow(
                    'Số khuôn mặt phát hiện',
                    provider.lastDetectedFaces.length.toString(),
                  ),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow(
                    'Tổng số lần phát hiện',
                    provider.totalDetections.toString(),
                  ),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow(
                    'Tỷ lệ thành công',
                    '${(provider.successRate * 100).toStringAsFixed(1)}%',
                  ),
                  const SizedBox(height: 12),
                  _buildStatusDetailRow(
                    'FPS trung bình',
                    provider.averageFPS.toStringAsFixed(1),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Đóng',
                    style: TextStyle(color: Colors.white70),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Get status icon widget
  Widget _getStatusIcon(SystemStatusType statusType, Color color) {
    switch (statusType) {
      case SystemStatusType.database:
        return _buildDatabaseIcon(color);
      case SystemStatusType.network:
        return _buildWifiIcon(color);
      case SystemStatusType.server:
        return _buildDocumentIcon(color);
      case SystemStatusType.sync:
        return _buildSyncIcon(color);
    }
  }

  /// Get status title
  String _getStatusTitle(SystemStatusType statusType) {
    switch (statusType) {
      case SystemStatusType.database:
        return 'Cơ sở dữ liệu';
      case SystemStatusType.network:
        return 'Kết nối mạng';
      case SystemStatusType.server:
        return 'Máy chủ';
      case SystemStatusType.sync:
        return 'Đồng bộ dữ liệu';
    }
  }

  /// Get status state text
  String _getStatusStateText(StatusState state) {
    switch (state) {
      case StatusState.active:
        return 'Hoạt động bình thường';
      case StatusState.warning:
        return 'Cảnh báo';
      case StatusState.inactive:
        return 'Không hoạt động';
      case StatusState.unknown:
        return 'Không xác định';
    }
  }

  /// Build status detail row
  Widget _buildStatusDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ],
    );
  }

  /// Format datetime
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Vừa xong';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} phút trước';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} giờ trước';
    } else {
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    }
  }

  /// Build status actions
  Widget _buildStatusActions(SystemStatusType statusType) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _systemStatusProvider.refreshStatus(statusType);
            },
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Làm mới'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF34D38D),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _systemStatusProvider.refreshAllStatuses();
            },
            icon: const Icon(Icons.refresh_outlined, size: 16),
            label: const Text('Làm mới tất cả'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white70,
              side: const BorderSide(color: Colors.white30),
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build server communication overlay
  Widget _buildServerCommunicationOverlay() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: () {
          // Dismiss when tapping outside
          setState(() {
            _showServerCommunication = false;
          });
        },
        child: Container(
          color: Colors.black.withValues(
            alpha: 0.3,
          ), // Semi-transparent backdrop
          child: Center(
            child: GestureDetector(
              onTap: () {}, // Prevent dismissal when tapping inside
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 80,
                ),
                constraints: const BoxConstraints(
                  maxHeight: 600,
                  maxWidth: 500,
                ),
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header with close button only
                        Row(
                          children: [
                            const Spacer(),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _showServerCommunication = false;
                                });
                              },
                              icon: Icon(
                                Icons.close,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              tooltip: 'Close',
                            ),
                          ],
                        ),

                        // Content
                        Flexible(
                          child: Consumer<DeviceRegistrationProvider>(
                            builder: (context, provider, child) {
                              return EnhancedServerCommunicationWidget(
                                secureComm: provider.secureComm,
                                provider: provider,
                                onConnect:
                                    (
                                      serverUrl,
                                      port,
                                      deviceId,
                                      deviceName,
                                    ) async {
                                      await _connectToCustomServer(
                                        provider,
                                        serverUrl,
                                        port,
                                        deviceId,
                                        deviceName,
                                      );
                                      setState(
                                        () {},
                                      ); // Refresh UI after connection
                                    },
                                onRefresh: () => provider.initialize(),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build relay testing toggle button
  Widget _buildRelayTestingToggle() {
    // Determine status color based on connection and modal state
    Color statusColor;
    IconData iconData;
    String tooltipMessage;

    if (_showRelayTesting) {
      statusColor = Colors.orange.shade600; // Modal is open
      iconData = Icons.electrical_services;
      tooltipMessage = 'Ẩn Relay Testing';
    } else {
      statusColor = Colors.orange.shade400; // Available for testing
      iconData = Icons.electrical_services_outlined;
      tooltipMessage = 'Hiện Relay Testing';
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _showRelayTesting = !_showRelayTesting;
        });
      },
      child: Tooltip(
        message: tooltipMessage,
        child: Container(
          padding: EdgeInsets.all(AppDimensions.spacing12),
          decoration: BoxDecoration(
            color: const Color(0xFF19313B),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: statusColor,
              width: AppDimensions.borderNormal,
            ),
          ),
          child: SizedBox(
            width: 14,
            height: 14,
            child: Icon(iconData, size: 14, color: statusColor),
          ),
        ),
      ),
    );
  }

  /// Build relay testing overlay
  Widget _buildRelayTestingOverlay() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: () {
          // Dismiss when tapping outside
          setState(() {
            _showRelayTesting = false;
          });
        },
        child: Container(
          color: Colors.black.withValues(
            alpha: 0.3,
          ), // Semi-transparent backdrop
          child: Center(
            child: GestureDetector(
              onTap: () {}, // Prevent dismissal when tapping inside
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 80,
                ),
                constraints: const BoxConstraints(
                  maxHeight: 600,
                  maxWidth: 500,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                    border: Border.all(color: Colors.orange.shade300, width: 2),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.electrical_services,
                              color: Colors.orange.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Relay Testing',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                            ),
                            const Spacer(),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _showRelayTesting = false;
                                });
                              },
                              icon: Icon(
                                Icons.close,
                                color: Colors.orange.shade700,
                                size: 20,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content
                      Flexible(
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Quick access buttons - compact layout
                              Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: () {
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const RelayTestingScreen(),
                                              ),
                                            );
                                          },
                                          icon: const Icon(
                                            Icons.settings,
                                            size: 16,
                                          ),
                                          label: const Text(
                                            'Full Testing',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.orange,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: _testQuickRelay,
                                          icon: const Icon(
                                            Icons.flash_on,
                                            size: 16,
                                          ),
                                          label: const Text(
                                            'Quick Test',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.blue,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 6),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: _checkUsbConnection,
                                          icon: const Icon(Icons.usb, size: 16),
                                          label: const Text(
                                            'Check USB',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.green,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: _testServerConnection,
                                          icon: const Icon(
                                            Icons.cloud,
                                            size: 16,
                                          ),
                                          label: const Text(
                                            'Test Server',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.purple,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 6),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: _openFaceRecognitionConfig,
                                          icon: const Icon(
                                            Icons.face,
                                            size: 16,
                                          ),
                                          label: const Text(
                                            'Face Config',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.teal,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Expanded(
                                        child: ElevatedButton.icon(
                                          onPressed: _openDeviceManagement,
                                          icon: const Icon(
                                            Icons.devices,
                                            size: 16,
                                          ),
                                          label: const Text(
                                            'Devices',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.brown,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 6,
                                            ),
                                            minimumSize: const Size(0, 32),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),

                              const SizedBox(height: 12),

                              // Quick relay control
                              QuickRelayControlWidget(
                                deviceId: 'T-A3B4-R01', // Default device ID
                                onCommandSent: () {
                                  // Handle command sent
                                },
                              ),

                              const SizedBox(height: 12),

                              // Enhanced relay manager
                              EnhancedRelayManagerWidget(
                                showDebugInfo: true,
                                showDeviceList: true,
                                showControls: true,
                                showEvents: true,
                                autoConnect: false,
                              ),

                              const SizedBox(height: 12),

                              // Relay status indicator
                              RelayStatusIndicator(
                                isConnected:
                                    false, // Will be updated with actual status
                                deviceId: 'T-A3B4-R01',
                                onTap: () {
                                  // Handle status tap
                                },
                              ),

                              const SizedBox(height: 12),

                              // Face recognition service status widget (replaces device registration)
                              const FaceRecognitionStatusWidget(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Quick relay test method
  Future<void> _testQuickRelay() async {
    if (!mounted) return;

    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Testing relay connection...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Simulate quick test for now
      // TODO: Integrate with actual relay testing service when available
      await Future.delayed(const Duration(seconds: 1));

      // Show result
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Quick relay test completed (simulated)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Quick test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check USB-TTL connection
  Future<void> _checkUsbConnection() async {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Checking USB-TTL connection...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Get available USB devices using real implementation
      final devices = await relay.UsbTtlRelayController.getAvailableDevices();

      // Filter for USB-TTL devices using vendor/product IDs and names
      final usbTtlDevices = devices.where((device) {
        final deviceName = device.deviceName.toLowerCase();
        final productName = device.productName?.toLowerCase() ?? '';
        final manufacturerName = device.manufacturerName?.toLowerCase() ?? '';
        final vid = device.vid;
        final pid = device.pid;

        // Check by vendor/product ID (most reliable method)
        if (vid != null && pid != null) {
          // Common USB-TTL chip vendor/product IDs
          final knownUsbTtlIds = {
            // Prolific PL2303
            0x067B: [0x2303],
            // FTDI chips
            0x0403: [0x6001, 0x6015, 0x6010, 0x6011],
            // CH340/CH341 chips
            0x1A86: [0x7523, 0x5523],
            // CP210x chips
            0x10C4: [0xEA60, 0xEA70],
            // Arduino boards
            0x2341: [0x0043, 0x0001, 0x0010],
          };

          if (knownUsbTtlIds.containsKey(vid)) {
            final productIds = knownUsbTtlIds[vid]!;
            if (productIds.contains(pid)) {
              return true;
            }
          }
        }

        // Fallback to name-based detection
        return deviceName.contains('pl2303') ||
            deviceName.contains('ch340') ||
            deviceName.contains('ch341') ||
            deviceName.contains('ftdi') ||
            deviceName.contains('cp210') ||
            productName.contains('usb-serial') ||
            productName.contains('uart') ||
            productName.contains('ttl') ||
            productName.contains('serial') ||
            manufacturerName.contains('prolific') ||
            manufacturerName.contains('ftdi') ||
            manufacturerName.contains('qinheng') ||
            manufacturerName.contains('wch');
      }).toList();

      if (mounted) {
        if (usbTtlDevices.isNotEmpty) {
          final deviceInfo = usbTtlDevices
              .map((d) {
                final vidPid = (d.vid != null && d.pid != null)
                    ? ' [${d.vid!.toRadixString(16).toUpperCase()}:${d.pid!.toRadixString(16).toUpperCase()}]'
                    : '';
                return '${d.deviceName} (${d.productName ?? 'N/A'})$vidPid';
              })
              .join('\n');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ USB-TTL device(s) detected:\n$deviceInfo'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 5),
            ),
          );
        } else {
          String message;
          if (devices.isEmpty) {
            message = '❌ No USB devices found';
          } else {
            // Show info about non-TTL devices for debugging
            final deviceList = devices
                .take(3)
                .map((d) {
                  final vidPid = (d.vid != null && d.pid != null)
                      ? ' [${d.vid!.toRadixString(16).toUpperCase()}:${d.pid!.toRadixString(16).toUpperCase()}]'
                      : '';
                  return '${d.deviceName}$vidPid';
                })
                .join(', ');
            final moreDevices = devices.length > 3
                ? ' (+${devices.length - 3} more)'
                : '';
            message = '⚠️ No USB-TTL detected. Found: $deviceList$moreDevices';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: devices.isEmpty ? Colors.red : Colors.orange,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ USB check failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Test server connection
  Future<void> _testServerConnection() async {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Testing server connection...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Test server health via /health endpoint (should return 200)
      final isServiceHealthy = await FaceRecognitionV3Service.instance
          .testConnection();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isServiceHealthy
                  ? '✅ Server health OK (GET /health = 200)'
                  : '❌ Server health failed (GET /health ≠ 200)',
            ),
            backgroundColor: isServiceHealthy ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Server test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Open face recognition configuration screen
  void _openFaceRecognitionConfig() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FaceRecognitionConfigScreen(),
      ),
    );
  }

  /// Open device management (navigate to devices tab in face config)
  void _openDeviceManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FaceRecognitionConfigScreen(),
      ),
    );
  }

  /// Open relay trigger configuration screen
  void _openRelayTriggerConfig() {
    Navigator.of(context).pop(); // Close dialog first
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const RelayTriggerConfigScreen()),
    );
  }

  /// Bottom section với thông tin user
  Widget _buildBottomSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.spacing24,
        vertical: AppDimensions.spacing24,
      ),
      child: Column(
        children: [
          // Date time section
          Container(
            width: double.infinity,
            height: 40, // Tăng height để đảm bảo hiển thị rõ
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.spacing16,
              vertical: AppDimensions.spacing4,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B), // Màu #1E293B đậm
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: const Color(0xFF30415E),
                width: AppDimensions.borderNormal,
              ),
              // Thêm shadow để nổi bật hơn
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                _currentTime.isNotEmpty
                    ? _currentTime
                    : 'Đang tải thời gian...',
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Colors.white, // Text màu trắng
                  letterSpacing: 0.5, // Thêm letter spacing để rõ hơn
                ),
              ),
            ),
          ),
          // User info section - chỉ hiển thị khi có user trong khung hình
          if (_hasUserInFrame) ...[
            SizedBox(height: AppDimensions.spacing8),
            AnimatedCircularProgressBorderWidget(
              progress: _throttleProgress,
              isVisible: _isRecognitionThrottleActive,
              progressColor: Colors.blue.shade400, // Fallback color
              strokeWidth: 3.0,
              borderRadius: 15.0,
              useGradient: false, // Use automatic color transition instead
              animationDuration: const Duration(milliseconds: 100),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(
                  18,
                ), // Tăng từ 15 lên 18 để có thêm không gian
                decoration: BoxDecoration(
                  color: _getContainerBackgroundColor(),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: _getContainerBorderColor(),
                    width: AppDimensions.borderNormal,
                  ),
                ),
                child: Row(
                  children: [
                    // Avatar with face captured and recognition status
                    Consumer<CapturedFaceProvider>(
                      builder: (context, capturedFaceProvider, child) {
                        // Determine border color based on recognition status
                        Color borderColor;
                        if (_currentUser != null) {
                          borderColor =
                              Colors.green.shade400; // Recognized user
                        } else if (_isRecognizing) {
                          borderColor =
                              Colors.blue.shade400; // Processing recognition
                        } else if (capturedFaceProvider.hasCapturedFace) {
                          borderColor = Colors
                              .orange
                              .shade400; // Face captured, waiting for recognition
                        } else if (_bestFace != null) {
                          borderColor = Colors
                              .green
                              .shade400; // Face detected, not captured yet
                        } else {
                          borderColor = const Color(0xFF30415E); // No face
                        }

                        return Container(
                          width: 100, // Tăng từ 86 lên 100
                          height: 100, // Tăng từ 86 lên 100
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: borderColor,
                              width: AppDimensions.borderNormal,
                            ),
                            color: Colors.grey[300],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(13),
                            child: Stack(
                              children: [
                                _buildAvatarContent(capturedFaceProvider),
                                // Recognition status indicator
                                if (_isRecognizing)
                                  Positioned(
                                    top: 4,
                                    left: 4,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: Colors.blue.shade600,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const SizedBox(
                                        width: 8,
                                        height: 8,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 1.5,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                // Recognition success indicator
                                if (_currentUser != null)
                                  Positioned(
                                    top: 4,
                                    left: 4,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade600,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 8,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(
                      width: 20,
                    ), // Tăng từ 18 lên 20 để cân bằng với avatar lớn hơn
                    // User details
                    Expanded(
                      child: SizedBox(
                        height:
                            120, // Tăng từ 88 lên 120 để có đủ không gian cho access status
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _currentUser?.name.toUpperCase() ??
                                  'CHƯA NHẬN DIỆN',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: _currentUser != null
                                    ? Colors.white
                                    : Colors.grey.shade400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8), // Giảm từ 10 xuống 8
                            Row(
                              children: [
                                Text(
                                  'Tổ chức:',
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFFBBBDC1),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _currentUser?.department ?? 'Đang xử lý...',
                                    style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w500,
                                      color: _currentUser != null
                                          ? const Color(0xFFBBBDC1)
                                          : Colors.grey.shade500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 6,
                            ), // Tăng từ 4 lên 6 để cân bằng
                            Row(
                              children: [
                                Text(
                                  'Chức vụ:',
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: const Color(0xFFBBBDC1),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _currentUser?.position ?? 'Vui lòng chờ...',
                                    style: TextStyle(
                                      fontSize: 15,
                                      color: _currentUser != null
                                          ? const Color(0xFFBBBDC1)
                                          : Colors.grey.shade500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            // Access status (integrated into user info)
                            if (_currentUser != null &&
                                _accessReason != null) ...[
                              const SizedBox(height: 6), // Giảm từ 8 xuống 6
                              Row(
                                children: [
                                  Icon(
                                    _allowAccess
                                        ? Icons.check_circle_outline
                                        : Icons.cancel_outlined,
                                    color: _allowAccess
                                        ? Colors.green.shade400
                                        : Colors.red.shade400,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _allowAccess
                                          ? 'Cho phép truy cập'
                                          : 'Từ chối truy cập',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: _allowAccess
                                            ? Colors.green.shade300
                                            : Colors.red.shade300,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ), // Close AnimatedCircularProgressBorderWidget
          ], // Đóng if statement
        ],
      ),
    );
  }

  /// Database icon
  Widget _buildDatabaseIcon(Color color) {
    final colorHex = _colorToHex(color);
    return SvgPicture.string(
      '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <ellipse cx="7.00065" cy="3.41699" rx="4.66667" ry="1.75" stroke="$colorHex"/>
        <path d="M11.6673 7.5C11.6673 8.4665 9.57798 9.25 7.00065 9.25C4.42332 9.25 2.33398 8.4665 2.33398 7.5" stroke="$colorHex"/>
        <path d="M11.6673 3.41699V11.5837C11.6673 12.5502 9.57798 13.3337 7.00065 13.3337C4.42332 13.3337 2.33398 12.5502 2.33398 11.5837V3.41699" stroke="$colorHex"/>
        <path d="M4.66602 5.16699V6.33366" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M4.66602 9.25V10.4167" stroke="$colorHex" stroke-linecap="round"/>
      </svg>''',
      width: 14,
      height: 14,
    );
  }

  /// WiFi icon
  Widget _buildWifiIcon(Color color) {
    final colorHex = _colorToHex(color);
    return SvgPicture.string(
      '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 11.292H7.00688" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M4.8125 9.54199C5.97917 8.37533 8.02083 8.37533 9.1875 9.54199" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10.7923 7.79134C8.59455 5.8469 5.54232 5.8469 3.20898 7.79134" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M1.16602 6.04134C4.85023 2.93024 9.14847 2.93024 12.8327 6.04128" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>''',
      width: 14,
      height: 14,
    );
  }

  /// Document icon
  Widget _buildDocumentIcon(Color color) {
    final colorHex = _colorToHex(color);
    return SvgPicture.string(
      '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7.30882 1.66699C9.63812 1.66699 10.8028 1.66699 11.5264 2.35041C12.25 3.03383 12.25 4.13377 12.25 6.33366V8.66699C12.25 10.8669 12.25 11.9668 11.5264 12.6502C10.8028 13.3337 9.63812 13.3337 7.30882 13.3337H6.69118C4.36188 13.3337 3.19724 13.3337 2.47362 12.6502C1.75 11.9668 1.75 10.8669 1.75 8.66699L1.75 6.33366C1.75 4.13377 1.75 3.03383 2.47362 2.35041C3.19724 1.66699 4.36188 1.66699 6.69118 1.66699L7.30882 1.66699Z" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M4.66602 4.58301H9.33268" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M4.66602 7.5H9.33268" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M4.66602 10.417L6.99935 10.417" stroke="$colorHex" stroke-linecap="round"/>
      </svg>''',
      width: 14,
      height: 14,
    );
  }

  /// Sync icon
  Widget _buildSyncIcon(Color color) {
    final colorHex = _colorToHex(color);
    return SvgPicture.string(
      '''<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <ellipse cx="5.83268" cy="3.41699" rx="4.66667" ry="1.75" stroke="$colorHex"/>
        <path d="M2.91602 6.8252C3.26694 6.93069 3.65938 7.01756 4.08268 7.08181" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M2.91602 10.9082C3.26694 11.0137 3.65938 11.1006 4.08268 11.1648" stroke="$colorHex" stroke-linecap="round"/>
        <path d="M7.00065 9.54177L7.66347 10.3966C7.997 9.15212 9.27646 8.41361 10.5212 8.74706C11.1584 8.91775 11.663 9.33623 11.959 9.87101M12.834 12.4577L12.1711 11.6041C11.8376 12.8485 10.5581 13.587 9.31339 13.2536C8.69101 13.0869 8.19514 12.6837 7.89657 12.1667" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M10.4993 3.41699V6.92178M1.16602 3.41699L1.16602 11.5948C1.16602 12.4885 2.94733 13.2259 5.24935 13.3337" stroke="$colorHex" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M1.16602 7.5C1.16602 8.39937 2.94733 9.14151 5.24935 9.25" stroke="$colorHex" stroke-linecap="round"/>
      </svg>''',
      width: 14,
      height: 14,
    );
  }
}

/// Custom painter cho C-CAM logo
class _CCAMLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    // Scale factor để điều chỉnh từ kích thước gốc (37x25) xuống kích thước hiện tại
    final scaleX = size.width / 37.0;
    final scaleY = size.height / 25.0;

    // Path 1
    final path1 = Path();
    path1.moveTo(10.4472 * scaleX, 20.4795 * scaleY);
    path1.cubicTo(
      10.4108 * scaleX,
      20.9021 * scaleY,
      10.1592 * scaleX,
      21.1487 * scaleY,
      9.94761 * scaleX,
      21.2809 * scaleY,
    );
    path1.cubicTo(
      9.27841 * scaleX,
      21.612 * scaleY,
      8.52812 * scaleX,
      21.8016 * scaleY,
      7.73216 * scaleX,
      21.8016 * scaleY,
    );
    path1.cubicTo(
      4.94864 * scaleX,
      21.8016 * scaleY,
      2.69265 * scaleX,
      19.5409 * scaleY,
      2.69265 * scaleX,
      16.7521 * scaleY,
    );
    path1.cubicTo(
      2.69265 * scaleX,
      13.9633 * scaleY,
      4.94864 * scaleX,
      11.7026 * scaleY,
      7.73216 * scaleX,
      11.7026 * scaleY,
    );
    path1.cubicTo(
      8.21029 * scaleX,
      11.7026 * scaleY,
      8.67258 * scaleX,
      11.7722 * scaleY,
      9.11111 * scaleX,
      11.8969 * scaleY,
    );
    path1.lineTo(9.11111 * scaleX, 9.1305 * scaleY);
    path1.cubicTo(
      8.6628 * scaleX,
      9.05018 * scaleY,
      8.20237 * scaleX,
      9.00488 * scaleY,
      7.73216 * scaleX,
      9.00488 * scaleY,
    );
    path1.cubicTo(
      3.46204 * scaleX,
      9.00488 * scaleY,
      0 * scaleX,
      12.4731 * scaleY,
      0 * scaleX,
      16.7521 * scaleY,
    );
    path1.cubicTo(
      0 * scaleX,
      21.031 * scaleY,
      3.46158 * scaleX,
      24.4993 * scaleY,
      7.73216 * scaleX,
      24.4993 * scaleY,
    );
    path1.cubicTo(
      9.65728 * scaleX,
      24.4993 * scaleY,
      11.457 * scaleX,
      23.7386 * scaleY,
      12.5345 * scaleX,
      22.7897 * scaleY,
    );
    path1.lineTo(10.4476 * scaleX, 14.6694 * scaleY);
    path1.lineTo(10.4472 * scaleX, 20.4795 * scaleY);
    path1.close();
    canvas.drawPath(path1, paint);

    // Path 2
    final path2 = Path();
    path2.moveTo(25.9102 * scaleX, 9.49023 * scaleY);
    path2.cubicTo(
      25.3701 * scaleX,
      9.70271 * scaleY,
      24.0992 * scaleX,
      10.3131 * scaleY,
      23.1015 * scaleX,
      11.658 * scaleY,
    );
    path2.lineTo(18.1785 * scaleX, 18.7397 * scaleY);
    path2.lineTo(13.2639 * scaleX, 11.6514 * scaleY);
    path2.cubicTo(
      12.2582 * scaleX,
      10.3131 * scaleY,
      10.9874 * scaleX,
      9.70318 * scaleY,
      10.4473 * scaleX,
      9.49023 * scaleY,
    );
    path2.lineTo(10.4473 * scaleX, 12.3215 * scaleY);
    path2.lineTo(18.1785 * scaleX, 23.4735 * scaleY);
    path2.lineTo(25.9107 * scaleX, 12.3215 * scaleY);
    path2.lineTo(25.9102 * scaleX, 9.49023 * scaleY);
    path2.close();
    canvas.drawPath(path2, paint);

    // Path 3
    final path3 = Path();
    path3.moveTo(28.6268 * scaleX, 9.00488 * scaleY);
    path3.cubicTo(
      28.1552 * scaleX,
      9.00488 * scaleY,
      27.6953 * scaleX,
      9.04971 * scaleY,
      27.2479 * scaleX,
      9.13097 * scaleY,
    );
    path3.lineTo(27.2479 * scaleX, 11.8969 * scaleY);
    path3.cubicTo(
      27.6864 * scaleX,
      11.7722 * scaleY,
      28.1487 * scaleX,
      11.7026 * scaleY,
      28.6268 * scaleX,
      11.7026 * scaleY,
    );
    path3.cubicTo(
      31.4099 * scaleX,
      11.7026 * scaleY,
      33.6659 * scaleX,
      13.9633 * scaleY,
      33.6659 * scaleX,
      16.7526 * scaleY,
    );
    path3.cubicTo(
      33.6659 * scaleX,
      19.5414 * scaleY,
      31.4099 * scaleX,
      21.8011 * scaleY,
      28.6268 * scaleX,
      21.8011 * scaleY,
    );
    path3.lineTo(25.9114 * scaleX, 20.4791 * scaleY);
    path3.lineTo(25.9114 * scaleX, 14.6689 * scaleY);
    path3.lineTo(23.2383 * scaleX, 18.5327 * scaleY);
    path3.lineTo(23.2383 * scaleX, 21.3761 * scaleY);
    path3.cubicTo(
      24.9015 * scaleX,
      23.7386 * scaleY,
      26.7013 * scaleX,
      24.5002 * scaleY,
      28.6273 * scaleX,
      24.5002 * scaleY,
    );
    path3.cubicTo(
      32.897 * scaleX,
      24.5002 * scaleY,
      36.3595 * scaleX,
      21.0315 * scaleY,
      36.3595 * scaleX,
      16.753 * scaleY,
    );
    path3.cubicTo(
      36.359 * scaleX,
      12.4731 * scaleY,
      32.8965 * scaleX,
      9.00488 * scaleY,
      28.6268 * scaleX,
      9.00488 * scaleY,
    );
    path3.close();
    canvas.drawPath(path3, paint);

    // Path 4
    final path4 = Path();
    path4.moveTo(13.266 * scaleX, 9.31986 * scaleY);
    path4.cubicTo(
      13.191 * scaleX,
      8.9743 * scaleY,
      13.1504 * scaleX,
      8.61612 * scaleY,
      13.1504 * scaleX,
      8.24814 * scaleY,
    );
    path4.cubicTo(
      13.1504 * scaleX,
      5.46541 * scaleY,
      15.4018 * scaleX,
      3.20989 * scaleY,
      18.1792 * scaleX,
      3.20989 * scaleY,
    );
    path4.cubicTo(
      20.9567 * scaleX,
      3.20989 * scaleY,
      23.208 * scaleX,
      5.46541 * scaleY,
      23.208 * scaleX,
      8.24814 * scaleY,
    );
    path4.cubicTo(
      23.208 * scaleX,
      8.61612 * scaleY,
      23.1675 * scaleX,
      8.9743 * scaleY,
      23.0929 * scaleX,
      9.31986 * scaleY,
    );
    path4.cubicTo(
      23.9206 * scaleX,
      8.71419 * scaleY,
      24.8736 * scaleX,
      8.27056 * scaleY,
      25.9053 * scaleX,
      8.0324 * scaleY,
    );
    path4.cubicTo(
      25.7912 * scaleX,
      3.85292 * scaleY,
      22.3771 * scaleX,
      0.5 * scaleY,
      18.1792 * scaleX,
      0.5 * scaleY,
    );
    path4.cubicTo(
      13.9813 * scaleX,
      0.5 * scaleY,
      10.5673 * scaleX,
      3.85292 * scaleY,
      10.4531 * scaleX,
      8.0324 * scaleY,
    );
    path4.cubicTo(
      11.4844 * scaleX,
      8.27056 * scaleY,
      12.4374 * scaleX,
      8.71419 * scaleY,
      13.266 * scaleX,
      9.31986 * scaleY,
    );
    path4.close();
    canvas.drawPath(path4, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Legacy face recognition result for backward compatibility
class _LegacyFaceRecognitionResult {
  final bool success;
  final bool recognized;
  final _LegacyFaceRecognitionUser? user;
  final double confidence;
  final String reason;
  final bool allowAccess;
  final bool qualityPass;
  final bool maskDetected;

  const _LegacyFaceRecognitionResult({
    required this.success,
    required this.recognized,
    this.user,
    required this.confidence,
    required this.reason,
    required this.allowAccess,
    required this.qualityPass,
    required this.maskDetected,
  });
}

/// Legacy face recognition user for backward compatibility
class _LegacyFaceRecognitionUser {
  final String id;
  final String name;

  const _LegacyFaceRecognitionUser({required this.id, required this.name});
}

# Secure Communication Library

A flexible, secure communication library for device-server communication that is **not tied to any specific data type or use case**. This library provides a unified, secure way to send any type of message between devices and servers with built-in authentication, signing, and transport abstraction.

## 🎯 Design Philosophy

- **Protocol Agnostic**: Works with HTTP, MQTT, WebSocket, or any custom transport
- **Data Type Agnostic**: Send face images, relay commands, logs, sensor data, or any custom payload
- **Security First**: JWT authentication, HMAC signing, replay attack prevention
- **Zero Trust**: Every message is signed and verified
- **Flexible**: Easy to extend for new message types and transport protocols

## ✨ Features

- 🔐 **JWT Authentication** with automatic token refresh
- 🔏 **HMAC Message Signing** for integrity verification
- 🛡️ **Replay Attack Prevention** with timestamp validation
- 📡 **Multiple Transport Protocols** (HTTP, MQTT, WebSocket)
- 🎛️ **Flexible Message Types** (face auth, relay control, logs, images, custom)
- 🔧 **Device Registration** and credential management
- 📊 **Built-in Message Types** for common use cases
- 🚀 **Easy Integration** with existing systems

## 🚀 Quick Start

### 1. Basic Usage

```dart
import 'package:secure_comm/secure_comm.dart';

// Create transport (HTTP, MQTT, or WebSocket)
final transport = HttpTransport('https://api.example.com');

// Create secure communication instance
final comm = SecureComm(
  deviceId: 'terminal-001',
  deviceType: 'face_terminal',
  transport: transport,
  deviceName: 'Main Entrance Terminal',
  capabilities: ['face_auth', 'relay_control'],
);

// Register device and get credentials
await comm.registerDevice();

// Send any type of message
await comm.sendMessage(
  type: 'face_auth',
  payload: {
    'face_image': base64Image,
    'user_id': 'user123',
    'confidence': 0.95,
  },
);
```

### 2. Built-in Message Types

```dart
// Face authentication
await comm.sendFaceAuth(
  faceImageBase64: base64Image,
  userId: 'user123',
  metadata: {'confidence': 0.95},
);

// Relay control
await comm.sendRelayControl(
  action: 'unlock',
  relayId: 'main_door',
  metadata: {'reason': 'face_auth_success'},
);

// Image upload
await comm.sendImageUpload(
  imageBase64: base64Image,
  imageType: 'face',
  filename: 'capture.jpg',
);

// Log messages
await comm.sendLog(
  level: 'info',
  message: 'User authenticated successfully',
  category: 'security',
  context: {'user_id': 'user123'},
);

// Status requests
await comm.sendStatusRequest(
  component: 'relay',
  filters: {'relay_id': 'main_door'},
);

// Custom messages
await comm.sendMessage(
  type: 'sensor_data',
  payload: {
    'temperature': 23.5,
    'humidity': 65.2,
    'motion_detected': false,
  },
);
```

### 3. Different Transport Protocols

```dart
// HTTP Transport
final httpTransport = HttpTransport('https://api.example.com');

// MQTT Transport
final mqttTransport = MqttTransport(
  brokerHost: 'mqtt.example.com',
  clientId: 'device-001',
  baseTopic: 'secure_comm',
  httpFallbackUrl: 'https://api.example.com', // For registration
);

// WebSocket Transport
final wsTransport = WebSocketTransport(
  'wss://api.example.com/ws',
  httpFallbackUrl: 'https://api.example.com', // For registration
);

// Custom Transport
class CustomTransport implements TransportInterface {
  // Implement your custom transport protocol
}
```

## 📋 Message Format

All messages follow a standard format:

```json
{
  "device_id": "terminal-001",
  "type": "face_auth",
  "payload": {
    "face_image": "base64_data",
    "user_id": "user123",
    "confidence": 0.95
  },
  "timestamp": 1723456789,
  "message_id": "msg-12345",
  "priority": 1,
  "signature": "hmac_sha256_signature"
}
```

## 🔧 Message Builder

For advanced use cases, use the MessageBuilder directly:

```dart
final builder = MessageBuilder(
  deviceId: 'device-001',
  secretKey: 'your-secret-key',
);

// Build and sign messages
final message = builder.buildFaceAuthMessage(
  faceImageBase64: base64Image,
  userId: 'user123',
);

// Verify received messages
final isValid = builder.verifyMessage(receivedMessage);
```

## 🔐 Security Features

### Device Registration Flow

1. **Device sends registration request** with device_id, device_type, capabilities
2. **Server responds with credentials**: JWT access token, HMAC secret key, scopes
3. **Device stores credentials** for subsequent communication

### Message Security

1. **JWT Authentication**: Every request includes Bearer token
2. **HMAC Signing**: Every message is signed with device secret key
3. **Timestamp Validation**: Prevents replay attacks (5-minute tolerance)
4. **Scope Verification**: Server checks device permissions

### Example Security Flow

```dart
// 1. Register device
await comm.registerDevice();

// 2. Send signed message (automatic)
final response = await comm.sendMessage(
  type: 'relay_control',
  payload: {'action': 'unlock'},
);

// 3. Server verifies:
//    - JWT token validity
//    - HMAC signature
//    - Timestamp freshness
//    - Device permissions
```

## 🌐 Transport Protocols

### HTTP Transport

Best for: Request-response patterns, REST APIs

```dart
final transport = HttpTransport(
  'https://api.example.com',
  timeout: Duration(seconds: 30),
  headers: {'Custom-Header': 'value'},
);
```

### MQTT Transport

Best for: Real-time communication, IoT devices

```dart
final transport = MqttTransport(
  brokerHost: 'mqtt.example.com',
  clientId: 'device-001',
  username: 'device_user',
  password: 'device_pass',
  useSecureConnection: true,
);
```

### WebSocket Transport

Best for: Bidirectional real-time communication

```dart
final transport = WebSocketTransport(
  'wss://api.example.com/ws',
  headers: {'Authorization': 'Bearer initial_token'},
);
```

## 📊 Built-in Message Types

| Type | Purpose | Payload Fields |
|------|---------|----------------|
| `face_auth` | Face authentication | `face_image`, `user_id`, `confidence` |
| `relay_control` | Relay/door control | `action`, `relay_id`, `reason` |
| `image_upload` | Image upload | `image_data`, `image_type`, `filename` |
| `log` | Logging | `level`, `message`, `category`, `context` |
| `status` | Status request | `component`, `filters` |
| `heartbeat` | Keep-alive ping | `system_info` |
| `config_update` | Configuration | `config`, `config_version` |
| Custom | Your own types | Any payload structure |

## 🔧 Crypto Utilities

```dart
// Generate secure device ID
final deviceId = CryptoUtils.generateDeviceId(prefix: 'terminal');

// Create HMAC signature
final signature = CryptoUtils.createHmacSignature(
  secretKey: 'secret',
  message: 'data to sign',
);

// Verify signature
final isValid = CryptoUtils.verifyHmacSignature(
  secretKey: 'secret',
  signature: signature,
  message: 'data to sign',
);

// Validate timestamp
final isRecent = CryptoUtils.isTimestampValid(timestamp);

// Create device fingerprint
final fingerprint = CryptoUtils.createDeviceFingerprint(
  deviceId: deviceId,
  deviceType: 'terminal',
  hardwareHash: 'hw_hash',
);
```

## 🧪 Testing

Run the example to test with the relay controller test server:

```bash
# Start test server
cd lib/packages/relay_controller/test_server
npm install
npm start

# Run example
cd lib/packages/secure_comm
dart run example/secure_comm_example.dart
```

## 🎯 Use Cases

- **Face Recognition Terminals**: Send face images for authentication
- **Access Control Systems**: Control relays, doors, gates
- **IoT Devices**: Send sensor data, receive commands
- **Security Systems**: Log events, upload images, send alerts
- **Industrial Control**: Send commands, receive status updates
- **Mobile Apps**: Any secure device-server communication

## 🔄 Integration Examples

### With Relay Controller

```dart
// Use SecureComm for relay control
final response = await comm.sendRelayControl(
  action: 'unlock',
  relayId: 'main_door',
);

if (response.success) {
  print('Door unlocked successfully');
}
```

### With Face Recognition

```dart
// Send face for authentication
final response = await comm.sendFaceAuth(
  faceImageBase64: capturedFaceImage,
  userId: detectedUserId,
  metadata: {
    'confidence': recognitionConfidence,
    'camera_id': 'cam_001',
  },
);

if (response.success && response.data?['authenticated'] == true) {
  // Proceed with access grant
  await comm.sendRelayControl(action: 'unlock');
}
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please read the contributing guidelines before submitting PRs.

## 📞 Support

For questions and support, please open an issue on the repository.

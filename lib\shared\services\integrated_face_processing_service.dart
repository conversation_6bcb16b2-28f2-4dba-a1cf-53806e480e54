import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:camera/camera.dart';
import '../providers/face_detection_provider.dart';
import '../providers/priority_face_processing_provider.dart';
import '../services/priority_face_processing_queue.dart';
import '../core/constants/face_cropping_constants.dart';

/// Service that integrates priority face processing with existing face detection workflow
class IntegratedFaceProcessingService {
  static const String _logTag = '🔗 IntegratedFaceProcessing';
  
  // Singleton pattern
  static final IntegratedFaceProcessingService _instance = IntegratedFaceProcessingService._internal();
  factory IntegratedFaceProcessingService() => _instance;
  IntegratedFaceProcessingService._internal();
  
  // Providers
  PriorityFaceProcessingProvider? _priorityProvider;
  FaceDetectionProvider? _faceDetectionProvider;
  
  // State
  bool _isInitialized = false;
  bool _isAutoProcessingEnabled = true;
  Timer? _processingTimer;
  
  // Configuration
  Duration _autoProcessingInterval = const Duration(milliseconds: 2000); // 2s interval
  double _minFaceQualityThreshold = 0.6;
  int _maxProcessingAttempts = 3;
  
  // Tracking
  String? _lastProcessedImagePath;
  Face? _lastProcessedFace;
  DateTime? _lastProcessingTime;
  int _processingAttempts = 0;
  
  /// Initialize the integrated service
  Future<void> initialize({
    required PriorityFaceProcessingProvider priorityProvider,
    required FaceDetectionProvider faceDetectionProvider,
    bool autoProcessingEnabled = true,
    Duration? autoProcessingInterval,
    double? minFaceQualityThreshold,
  }) async {
    if (_isInitialized) {
      debugPrint('$_logTag Service already initialized');
      return;
    }
    
    _priorityProvider = priorityProvider;
    _faceDetectionProvider = faceDetectionProvider;
    _isAutoProcessingEnabled = autoProcessingEnabled;
    
    if (autoProcessingInterval != null) {
      _autoProcessingInterval = autoProcessingInterval;
    }
    
    if (minFaceQualityThreshold != null) {
      _minFaceQualityThreshold = minFaceQualityThreshold;
      _priorityProvider?.setMinFaceQuality(minFaceQualityThreshold);
    }
    
    // Start auto-processing if enabled
    if (_isAutoProcessingEnabled) {
      _startAutoProcessing();
    }
    
    _isInitialized = true;
    debugPrint('$_logTag Service initialized successfully');
  }
  
  /// Start automatic face processing
  void _startAutoProcessing() {
    _processingTimer?.cancel();
    
    _processingTimer = Timer.periodic(_autoProcessingInterval, (timer) {
      _processCurrentFaces();
    });
    
    debugPrint('$_logTag Auto-processing started with ${_autoProcessingInterval.inMilliseconds}ms interval');
  }
  
  /// Stop automatic face processing
  void stopAutoProcessing() {
    _processingTimer?.cancel();
    _processingTimer = null;
    debugPrint('$_logTag Auto-processing stopped');
  }
  
  /// Process current detected faces
  Future<void> _processCurrentFaces() async {
    if (!_isInitialized || 
        _priorityProvider == null || 
        _faceDetectionProvider == null ||
        !_priorityProvider!.isEnabled) {
      return;
    }
    
    // Skip if already processing or no faces detected
    if (_priorityProvider!.isProcessing || _faceDetectionProvider!.faces.isEmpty) {
      return;
    }
    
    // Check processing attempts limit
    if (_processingAttempts >= _maxProcessingAttempts) {
      debugPrint('$_logTag Max processing attempts reached, skipping');
      return;
    }
    
    try {
      // Get current camera image path (this would be provided by camera service)
      final imagePath = await _getCurrentCameraImagePath();
      if (imagePath == null) {
        debugPrint('$_logTag No camera image available');
        return;
      }
      
      // Check if this is a new image or face
      if (_shouldSkipProcessing(imagePath, _faceDetectionProvider!.faces)) {
        return;
      }
      
      // Select best face for processing
      final bestFace = _selectBestFaceForProcessing(_faceDetectionProvider!.faces);
      if (bestFace == null) {
        debugPrint('$_logTag No suitable face found for processing');
        return;
      }
      
      // Determine face direction based on current capture mode
      final direction = _getCurrentFaceDirection();
      
      // Process the face
      final taskId = await _priorityProvider!.processDetectedFaces(
        imagePath: imagePath,
        detectedFaces: [bestFace], // Only process the best face
        direction: direction,
        context: {
          'integrated_service': true,
          'auto_processed': true,
          'face_count': _faceDetectionProvider!.faces.length,
          'selected_face_quality': _calculateFaceQuality(bestFace),
          'processing_attempt': _processingAttempts + 1,
          'timestamp': DateTime.now().toIso8601String(),
        },
        enabledSideEffects: [
          SideEffectType.auditLog,
          SideEffectType.databaseUpdate,
          // Add more side effects as needed
        ],
      );
      
      if (taskId != null) {
        _lastProcessedImagePath = imagePath;
        _lastProcessedFace = bestFace;
        _lastProcessingTime = DateTime.now();
        _processingAttempts++;
        
        debugPrint('$_logTag Started processing task: $taskId');
        
        // Monitor task completion
        _monitorTaskCompletion(taskId);
      }
      
    } catch (e) {
      debugPrint('$_logTag Error in auto-processing: $e');
      _processingAttempts++;
    }
  }
  
  /// Get current camera image path
  /// This is a placeholder - in real implementation, you would:
  /// 1. Capture current camera frame
  /// 2. Save to temporary file
  /// 3. Return the file path
  Future<String?> _getCurrentCameraImagePath() async {
    try {
      // Placeholder implementation
      // In real usage, you would integrate with camera provider to:
      // 1. Get current camera controller
      // 2. Take a picture or get current frame
      // 3. Save to temporary file
      // 4. Return file path
      
      // For now, return a mock path
      return '/tmp/current_camera_frame_${DateTime.now().millisecondsSinceEpoch}.jpg';
      
    } catch (e) {
      debugPrint('$_logTag Error getting camera image: $e');
      return null;
    }
  }
  
  /// Check if processing should be skipped
  bool _shouldSkipProcessing(String imagePath, List<Face> faces) {
    // Skip if same image path
    if (_lastProcessedImagePath == imagePath) {
      return true;
    }
    
    // Skip if no significant time has passed
    if (_lastProcessingTime != null) {
      final timeSinceLastProcessing = DateTime.now().difference(_lastProcessingTime!);
      if (timeSinceLastProcessing < const Duration(milliseconds: 1500)) {
        return true;
      }
    }
    
    // Skip if face is too similar to last processed face
    if (_lastProcessedFace != null && faces.isNotEmpty) {
      final bestFace = _selectBestFaceForProcessing(faces);
      if (bestFace != null && _isFaceSimilar(bestFace, _lastProcessedFace!)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// Select best face for processing (largest, highest quality)
  Face? _selectBestFaceForProcessing(List<Face> faces) {
    if (faces.isEmpty) return null;
    
    Face? bestFace;
    double bestScore = 0.0;
    
    for (final face in faces) {
      final quality = _calculateFaceQuality(face);
      
      // Skip faces below quality threshold
      if (quality < _minFaceQualityThreshold) continue;
      
      final area = face.boundingBox.width * face.boundingBox.height;
      
      // Combined score: 70% quality + 30% size
      final score = quality * 0.7 + (area / 100000).clamp(0.0, 1.0) * 0.3;
      
      if (score > bestScore) {
        bestScore = score;
        bestFace = face;
      }
    }
    
    return bestFace;
  }
  
  /// Calculate face quality score
  double _calculateFaceQuality(Face face) {
    double quality = 0.0;
    int factors = 0;
    
    // Size quality (larger faces are better)
    final area = face.boundingBox.width * face.boundingBox.height;
    final sizeQuality = (area / 100000).clamp(0.0, 1.0);
    quality += sizeQuality * 0.4;
    factors++;
    
    // Eye open probability
    if (face.leftEyeOpenProbability != null && face.rightEyeOpenProbability != null) {
      final eyeQuality = (face.leftEyeOpenProbability! + face.rightEyeOpenProbability!) / 2;
      quality += eyeQuality * 0.3;
      factors++;
    }
    
    // Head pose quality
    if (face.headEulerAngleY != null && face.headEulerAngleZ != null) {
      final yawAngle = face.headEulerAngleY!.abs();
      final rollAngle = face.headEulerAngleZ!.abs();
      final poseQuality = 1.0 - ((yawAngle + rollAngle) / 60.0).clamp(0.0, 1.0);
      quality += poseQuality * 0.2;
      factors++;
    }
    
    // Smiling probability (optional)
    if (face.smilingProbability != null) {
      quality += face.smilingProbability! * 0.1;
      factors++;
    }
    
    return factors > 0 ? quality : 0.0;
  }
  
  /// Check if two faces are similar
  bool _isFaceSimilar(Face face1, Face face2) {
    final center1 = face1.boundingBox.center;
    final center2 = face2.boundingBox.center;
    
    final distance = (center1 - center2).distance;
    
    final area1 = face1.boundingBox.width * face1.boundingBox.height;
    final area2 = face2.boundingBox.width * face2.boundingBox.height;
    final sizeDiff = (area1 - area2).abs() / area1;
    
    // Consider similar if center distance < 40 pixels and size difference < 15%
    return distance < 40 && sizeDiff < 0.15;
  }
  
  /// Get current face direction based on capture mode
  FaceDirection _getCurrentFaceDirection() {
    // This would be determined by the current face capture state
    // For now, return front as default
    return FaceDirection.front;
  }
  
  /// Monitor task completion
  void _monitorTaskCompletion(String taskId) {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_priorityProvider == null) {
        timer.cancel();
        return;
      }
      
      final queueStatus = _priorityProvider!.getQueueStatus();
      final currentTaskId = queueStatus['current_task_id'] as String?;
      
      // Check if task is no longer current (completed or failed)
      if (currentTaskId != taskId) {
        timer.cancel();
        
        // Reset processing attempts on completion
        _processingAttempts = 0;
        
        final statistics = _priorityProvider!.getStatistics();
        final wasSuccessful = statistics['total_processed'] > 0;
        
        debugPrint('$_logTag Task $taskId completed. Success: $wasSuccessful');
        
        // Callback for task completion if needed
        _onTaskCompleted(taskId, wasSuccessful);
      }
    });
  }
  
  /// Handle task completion
  void _onTaskCompleted(String taskId, bool success) {
    if (success) {
      debugPrint('$_logTag ✅ Face processing completed successfully: $taskId');
    } else {
      debugPrint('$_logTag ❌ Face processing failed: $taskId');
    }
    
    // Reset processing attempts on successful completion
    if (success) {
      _processingAttempts = 0;
    }
  }
  
  /// Manual face processing trigger
  Future<String?> processManually({
    required String imagePath,
    required List<Face> faces,
    required FaceDirection direction,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
  }) async {
    if (!_isInitialized || _priorityProvider == null) {
      throw Exception('Service not initialized');
    }
    
    final bestFace = _selectBestFaceForProcessing(faces);
    if (bestFace == null) {
      throw Exception('No suitable face found for processing');
    }
    
    return await _priorityProvider!.processDetectedFaces(
      imagePath: imagePath,
      detectedFaces: [bestFace],
      direction: direction,
      context: {
        'integrated_service': true,
        'manual_trigger': true,
        'face_quality': _calculateFaceQuality(bestFace),
        ...?context,
      },
      enabledSideEffects: enabledSideEffects,
    );
  }
  
  /// Get service status
  Map<String, dynamic> getStatus() {
    return {
      'is_initialized': _isInitialized,
      'is_auto_processing_enabled': _isAutoProcessingEnabled,
      'auto_processing_interval_ms': _autoProcessingInterval.inMilliseconds,
      'min_face_quality_threshold': _minFaceQualityThreshold,
      'max_processing_attempts': _maxProcessingAttempts,
      'current_processing_attempts': _processingAttempts,
      'last_processed_image_path': _lastProcessedImagePath,
      'last_processing_time': _lastProcessingTime?.toIso8601String(),
      'has_last_processed_face': _lastProcessedFace != null,
      'priority_provider_enabled': _priorityProvider?.isEnabled ?? false,
      'priority_provider_processing': _priorityProvider?.isProcessing ?? false,
    };
  }
  
  /// Configure service settings
  void configure({
    bool? autoProcessingEnabled,
    Duration? autoProcessingInterval,
    double? minFaceQualityThreshold,
    int? maxProcessingAttempts,
  }) {
    if (autoProcessingEnabled != null) {
      _isAutoProcessingEnabled = autoProcessingEnabled;
      if (autoProcessingEnabled) {
        _startAutoProcessing();
      } else {
        stopAutoProcessing();
      }
    }
    
    if (autoProcessingInterval != null) {
      _autoProcessingInterval = autoProcessingInterval;
      if (_isAutoProcessingEnabled) {
        _startAutoProcessing(); // Restart with new interval
      }
    }
    
    if (minFaceQualityThreshold != null) {
      _minFaceQualityThreshold = minFaceQualityThreshold;
      _priorityProvider?.setMinFaceQuality(minFaceQualityThreshold);
    }
    
    if (maxProcessingAttempts != null) {
      _maxProcessingAttempts = maxProcessingAttempts;
    }
    
    debugPrint('$_logTag Service configuration updated');
  }
  
  /// Dispose service
  void dispose() {
    stopAutoProcessing();
    _isInitialized = false;
    _priorityProvider = null;
    _faceDetectionProvider = null;
    debugPrint('$_logTag Service disposed');
  }
}

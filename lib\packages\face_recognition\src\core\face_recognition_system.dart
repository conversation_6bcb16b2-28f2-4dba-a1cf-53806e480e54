import 'dart:async';
import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';

// Import types from main package
import '../../face_recognition.dart';

import 'interfaces/face_detection_engine.dart';
import 'interfaces/face_recognition_service.dart';
import 'models/face_recognition_config.dart';
import 'models/face_recognition_result.dart';
import '../detection/detection_engine_factory.dart';
import '../recognition/recognition_service_factory.dart';
import '../services/network/network_detection_service.dart';
import '../services/monitoring/performance_monitor.dart';

/// Main face recognition system that orchestrates detection and recognition
class FaceRecognitionSystem extends ChangeNotifier {
  // Core components
  FaceDetectionEngine? _detectionEngine;
  FaceRecognitionService? _recognitionService;
  NetworkDetectionService? _networkService;
  PerformanceMonitor? _performanceMonitor;
  
  // Configuration
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  bool _isProcessing = false;
  
  // State
  List<FaceDetection> _detectedFaces = [];
  FaceRecognitionResult? _lastRecognitionResult;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isProcessing => _isProcessing;
  List<FaceDetection> get detectedFaces => List.unmodifiable(_detectedFaces);
  FaceRecognitionResult? get lastRecognitionResult => _lastRecognitionResult;
  FaceRecognitionConfig? get config => _config;
  
  /// Initialize the face recognition system
  Future<void> initialize({required FaceRecognitionConfig config}) async {
    if (_isInitialized) {
      throw StateError('FaceRecognitionSystem is already initialized');
    }
    
    try {
      _config = config;
      
      if (kDebugMode) {
        print('🚀 Initializing FaceRecognitionSystem');
        print('   Detection Engine: ${config.detectionEngine.name}');
        print('   Recognition Mode: ${config.recognitionMode.name}');
        print('   Performance Profile: ${config.performanceProfile.name}');
      }
      
      // Initialize network service
      _networkService = NetworkDetectionService();
      await _networkService!.initialize();
      
      // Initialize performance monitor
      _performanceMonitor = PerformanceMonitor(config: config);
      await _performanceMonitor!.initialize();
      
      // Initialize detection engine
      _detectionEngine = DetectionEngineFactory.create(
        engine: config.detectionEngine,
        config: config,
      );
      await _detectionEngine!.initialize();
      
      // Initialize recognition service
      _recognitionService = RecognitionServiceFactory.create(
        mode: config.recognitionMode,
        config: config,
        networkService: _networkService!,
      );
      await _recognitionService!.initialize();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ FaceRecognitionSystem initialized successfully');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize FaceRecognitionSystem: $e');
      }
      await dispose();
      rethrow;
    }
  }
  
  /// Process camera image for face detection and recognition
  Future<void> processCameraImage(CameraImage image) async {
    if (!_isInitialized || _isProcessing) return;
    
    _isProcessing = true;
    final stopwatch = Stopwatch()..start();
    
    try {
      // Detect faces
      final faces = await _detectionEngine!.processCameraImage(image);
      _detectedFaces = faces;
      
      // Trigger recognition for best quality face
      if (faces.isNotEmpty) {
        final bestFace = _selectBestFace(faces);
        if (bestFace != null && _shouldTriggerRecognition(bestFace)) {
          await _triggerRecognition(bestFace);
        }
      }
      
      // Update performance metrics
      stopwatch.stop();
      _performanceMonitor?.recordProcessingTime(stopwatch.elapsedMilliseconds);
      _performanceMonitor?.recordFaceCount(faces.length);
      
      notifyListeners();
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing camera image: $e');
      }
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Process image bytes for face detection and recognition
  Future<List<FaceDetection>> processImageBytes(Uint8List imageBytes) async {
    if (!_isInitialized) {
      throw StateError('FaceRecognitionSystem not initialized');
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      final faces = await _detectionEngine!.detectFaces(imageBytes);
      
      stopwatch.stop();
      _performanceMonitor?.recordProcessingTime(stopwatch.elapsedMilliseconds);
      _performanceMonitor?.recordFaceCount(faces.length);
      
      return faces;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing image bytes: $e');
      }
      rethrow;
    }
  }
  
  /// Select the best face for recognition
  FaceDetection? _selectBestFace(List<FaceDetection> faces) {
    if (faces.isEmpty) return null;
    
    // Sort by quality score and confidence
    faces.sort((a, b) {
      final scoreA = a.quality * 0.7 + a.confidence * 0.3;
      final scoreB = b.quality * 0.7 + b.confidence * 0.3;
      return scoreB.compareTo(scoreA);
    });
    
    final bestFace = faces.first;
    
    // Check minimum quality threshold
    if (bestFace.quality < _config!.minFaceQuality) {
      return null;
    }
    
    return bestFace;
  }
  
  /// Check if recognition should be triggered for this face
  bool _shouldTriggerRecognition(FaceDetection face) {
    // Check quality threshold
    if (face.quality < _config!.minFaceQuality) return false;
    
    // Check confidence threshold
    if (face.confidence < _config!.minConfidence) return false;
    
    // Check if face is frontal enough
    if (!face.pose.isFrontal && _config!.requireFrontalFace) return false;
    
    // Check throttling
    if (_recognitionService!.isThrottled) return false;
    
    return true;
  }
  
  /// Trigger face recognition
  Future<void> _triggerRecognition(FaceDetection face) async {
    if (face.croppedFace == null) return;
    
    try {
      final result = await _recognitionService!.recognizeFace(face.croppedFace!);
      
      if (result != null) {
        _lastRecognitionResult = result;
        
        if (kDebugMode) {
          print('✅ Face recognized: ${result.userName} (${(result.confidence * 100).toStringAsFixed(1)}%)');
        }
        
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Recognition failed: $e');
      }
    }
  }
  
  /// Get current performance metrics
  PerformanceMetrics? getPerformanceMetrics() {
    return _performanceMonitor?.getCurrentMetrics();
  }

  /// Get system statistics
  TerminalStats? getStats() {
    if (!_isInitialized) return null;

    return TerminalStats(
      averageFPS: 30.0, // Placeholder
      totalProcessed: 0, // Placeholder
      totalRecognized: 0, // Placeholder
      recognitionRate: 0.0, // Placeholder
      isOnline: true, // Placeholder
    );
  }

  /// Check if system is online
  bool get isOnline => true; // Placeholder

  /// Get last recognition result
  TerminalRecognitionResult? get lastRecognition => null; // Placeholder

  /// Start enrollment session
  Future<EnrollmentSession> startEnrollment({
    required String userId,
    required String userName,
    required String projectId,
    required AccessLevel accessLevel,
  }) async {
    return EnrollmentSession(
      userId: userId,
      userName: userName,
      projectId: projectId,
    );
  }

  /// Process enrollment image
  Future<EnrollmentProgress> processEnrollmentImage(Uint8List imageBytes) async {
    return EnrollmentProgress(
      progress: 0.5,
      status: 'Processing...',
    );
  }

  /// Complete enrollment
  Future<EnrollmentResult> completeEnrollment() async {
    return EnrollmentResult(success: true, message: 'Enrollment completed');
  }
  
  /// Update configuration
  Future<void> updateConfig(FaceRecognitionConfig newConfig) async {
    if (!_isInitialized) {
      throw StateError('FaceRecognitionSystem not initialized');
    }
    
    final oldConfig = _config!;
    _config = newConfig;
    
    // Reinitialize if engine changed
    if (oldConfig.detectionEngine != newConfig.detectionEngine) {
      await _detectionEngine?.dispose();
      _detectionEngine = DetectionEngineFactory.create(
        engine: newConfig.detectionEngine,
        config: newConfig,
      );
      await _detectionEngine!.initialize();
    }
    
    // Update recognition service if mode changed
    if (oldConfig.recognitionMode != newConfig.recognitionMode) {
      await _recognitionService?.dispose();
      _recognitionService = RecognitionServiceFactory.create(
        mode: newConfig.recognitionMode,
        config: newConfig,
        networkService: _networkService!,
      );
      await _recognitionService!.initialize();
    }
    
    if (kDebugMode) {
      print('⚙️ Configuration updated');
    }
    
    notifyListeners();
  }
  
  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isInitialized = false;
    
    await _detectionEngine?.dispose();
    await _recognitionService?.dispose();
    await _performanceMonitor?.dispose();
    _networkService?.dispose();
    
    _detectionEngine = null;
    _recognitionService = null;
    _performanceMonitor = null;
    _networkService = null;
    
    if (kDebugMode) {
      print('🗑️ FaceRecognitionSystem disposed');
    }
    
    super.dispose();
  }
}

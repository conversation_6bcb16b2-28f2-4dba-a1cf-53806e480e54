/// Shared Components Index - Export all shared component widgets
///
/// This file exports all shared component widgets that provide consistent
/// UI elements across mobile and terminal applications.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/components/index.dart';
///
/// // Use any shared component
/// AppButton(
///   text: 'Click me',
///   onPressed: () {},
/// )
/// ```
///
/// Migration from widgets/common:
/// - CTextField → AppInputField ✅
/// - CTabsBar → TabsBar ✅  
/// - ErrorScreen → ErrorScreen ✅ (moved)
/// - NotFoundScreen → NotFoundScreen ✅ (moved)
/// - EnhancedErrorMessage → AppNotification.auth() ✅
library;

// ============================================================================
// SHARED COMPONENT EXPORTS
// ============================================================================

/// App button widget with consistent styling
export 'app_button.dart';

/// App input field widget with validation and enhanced features
export 'app_input_field.dart';

/// App list controller for managing list state
export 'app_list_controller.dart';

/// App list view widget for displaying lists
export 'app_list_view.dart';

/// App loading widget with various display options
export 'app_loading.dart';

/// App logo widget (C-Faces logo)
export 'app_logo.dart';

/// App notification widget for displaying messages with retry support
export 'app_notification.dart';

/// App search field widget for search functionality
export 'app_search_field.dart';

/// App toggle button widget for switching between options
export 'app_toggle_button.dart';

/// Error screen for displaying error messages (migrated from widgets/common)
export 'error_screen.dart';

/// Flavor aware widget for environment-specific rendering
export 'flavor_aware_widget.dart';

/// Not found screen for 404 errors (migrated from widgets/common)
export 'not_found_screen.dart';

/// OTP input field widget for one-time password entry
export 'otp_input_field.dart';

/// Tabs bar widget for navigation with built-in routing (migrated from widgets/common)
export 'tabs_bar.dart';

/// Tenant list item widget for displaying tenant information
export 'tenant_list_item.dart';

/// Tenants empty state widget for no tenants scenario
export 'tenants_empty_state.dart';

/// Users filter popup widget for filtering user lists
export 'users_filter_popup.dart';

// ============================================================================
// MIGRATION NOTES
// ============================================================================

/// Components successfully migrated from lib/shared/presentation/widgets/common:
/// 
/// 1. CTextField → AppInputField
///    - API change: hintText → placeholder
///    - Enhanced: Built-in password visibility toggle
///    - Simplified: No need for external state management
/// 
/// 2. CTabsBar → TabsBar
///    - API change: currentIndex → selectedIndex, onTap → onTabSelected
///    - Enhanced: Built-in navigation with GoRouter integration
///    - Improved: Automatic route handling
/// 
/// 3. ErrorScreen (moved)
///    - Location: widgets/common → shared/components
///    - API: No changes
///    - Usage: Updated import paths in router files
/// 
/// 4. NotFoundScreen (moved)
///    - Location: widgets/common → shared/components
///    - API: No changes
///    - Usage: Updated import paths in router files
/// 
/// 5. EnhancedErrorMessage → AppNotification.auth()
///    - Enhanced: AppNotification now supports retry buttons and details
///    - Factory: AppNotification.auth() for authentication errors
///    - Improved: Better error handling and user experience
/// 
/// Migration completed: 2025-07-03
/// Files affected: 9 files, 15 component usages
/// Status: ✅ All migrations successful, zero compilation errors

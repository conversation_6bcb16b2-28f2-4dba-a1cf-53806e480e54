import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

/// Logo component của ứng dụng C-CAM
class AppLogo extends StatelessWidget {
  final bool isLarge;
  final Color? textColor;

  const AppLogo({
    super.key,
    this.isLarge = false,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLogoSvg(),
        SizedBox(width: AppDimensions.spacing8),
        Text(
          'C-CAM',
          style: isLarge 
            ? AppTextStyles.heading1.copyWith(color: textColor ?? AppColors.primary)
            : AppTextStyles.heading2.copyWith(color: textColor ?? AppColors.primary),
        ),
      ],
    );
  }

  Widget _buildLogoSvg() {
    final width = isLarge ? AppDimensions.logoLargeWidth : AppDimensions.logoSmallWidth;
    final height = isLarge ? AppDimensions.logoLargeHeight : AppDimensions.logoSmallHeight;
    
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: _LogoPainter(),
      ),
    );
  }
}

class _LogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    // Scale factor để điều chỉnh từ kích thước gốc (85x56) xuống kích thước hiện tại
    final scaleX = size.width / 85.0;
    final scaleY = size.height / 56.0;

    // Path 1 - Left C
    final path1 = Path();
    path1.moveTo(24.4588 * scaleX, 46.6179 * scaleY);
    path1.cubicTo(24.374 * scaleX, 47.604 * scaleY, 23.7868 * scaleX, 48.1793 * scaleY, 23.2931 * scaleX, 48.4877 * scaleY);
    path1.cubicTo(21.7317 * scaleX, 49.2603 * scaleY, 19.981 * scaleX, 49.7026 * scaleY, 18.1238 * scaleX, 49.7026 * scaleY);
    path1.cubicTo(11.6289 * scaleX, 49.7026 * scaleY, 6.36489 * scaleX, 44.4278 * scaleY, 6.36489 * scaleX, 37.9206 * scaleY);
    path1.cubicTo(6.36489 * scaleX, 31.4133 * scaleY, 11.6289 * scaleX, 26.1385 * scaleY, 18.1238 * scaleX, 26.1385 * scaleY);
    path1.cubicTo(19.2394 * scaleX, 26.1385 * scaleY, 20.3181 * scaleX, 26.3008 * scaleY, 21.3413 * scaleX, 26.5918 * scaleY);
    path1.lineTo(21.3413 * scaleX, 20.1369 * scaleY);
    path1.cubicTo(20.2953 * scaleX, 19.9494 * scaleY, 19.2209 * scaleX, 19.8438 * scaleY, 18.1238 * scaleX, 19.8438 * scaleY);
    path1.cubicTo(8.16014 * scaleX, 19.8438 * scaleY, 0.0820312 * scaleX, 27.9364 * scaleY, 0.0820312 * scaleX, 37.9206 * scaleY);
    path1.cubicTo(0.0820312 * scaleX, 47.9048 * scaleY, 8.15905 * scaleX, 55.9974 * scaleY, 18.1238 * scaleX, 55.9974 * scaleY);
    path1.cubicTo(22.6157 * scaleX, 55.9974 * scaleY, 26.8152 * scaleX, 54.2224 * scaleY, 29.3292 * scaleX, 52.0083 * scaleY);
    path1.cubicTo(29.4858 * scaleX, 51.8361 * scaleY, 29.6597 * scaleX, 51.677 * scaleY, 29.8816 * scaleX, 51.3185 * scaleY);
    path1.cubicTo(29.999 * scaleX, 51.1344 * scaleY, 30.0708 * scaleX, 51.0167 * scaleY, 30.2393 * scaleX, 50.7171 * scaleY);
    path1.cubicTo(30.36 * scaleX, 50.4534 * scaleY, 30.4677 * scaleX, 50.1592 * scaleY, 30.5525 * scaleX, 49.8192 * scaleY);
    path1.cubicTo(30.5666 * scaleX, 49.7582 * scaleY, 30.5786 * scaleX, 49.683 * scaleY, 30.6144 * scaleX, 49.4869 * scaleY);
    path1.cubicTo(30.6297 * scaleX, 49.3834 * scaleY, 30.659 * scaleX, 49.1698 * scaleY, 30.6971 * scaleX, 48.71 * scaleY);
    path1.lineTo(30.6971 * scaleX, 42.0764 * scaleY);
    path1.lineTo(24.4599 * scaleX, 33.0609 * scaleY);
    path1.close();

    // Path 2 - Center Mountain
    final path2 = Path();
    path2.moveTo(60.5402 * scaleX, 20.9766 * scaleY);
    path2.cubicTo(59.2799 * scaleX, 21.4723 * scaleY, 56.3146 * scaleX, 22.8965 * scaleY, 53.9865 * scaleX, 26.0346 * scaleY);
    path2.lineTo(42.4995 * scaleX, 42.5587 * scaleY);
    path2.lineTo(31.0321 * scaleX, 26.0193 * scaleY);
    path2.cubicTo(28.6855 * scaleX, 22.8965 * scaleY, 25.7202 * scaleX, 21.4734 * scaleY, 24.46 * scaleX, 20.9766 * scaleY);
    path2.lineTo(24.46 * scaleX, 27.5829 * scaleY);
    path2.lineTo(42.4995 * scaleX, 53.6042 * scaleY);
    path2.lineTo(60.5412 * scaleX, 27.5829 * scaleY);
    path2.close();

    // Path 3 - Right C
    final path3 = Path();
    path3.moveTo(66.8761 * scaleX, 19.8438 * scaleY);
    path3.cubicTo(65.7756 * scaleX, 19.8438 * scaleY, 64.7024 * scaleX, 19.9484 * scaleY, 63.6585 * scaleX, 20.1379 * scaleY);
    path3.lineTo(63.6585 * scaleX, 26.5918 * scaleY);
    path3.cubicTo(64.6817 * scaleX, 26.3008 * scaleY, 65.7604 * scaleX, 26.1385 * scaleY, 66.8761 * scaleX, 26.1385 * scaleY);
    path3.cubicTo(73.3699 * scaleX, 26.1385 * scaleY, 78.6338 * scaleX, 31.4133 * scaleY, 78.6338 * scaleX, 37.9217 * scaleY);
    path3.cubicTo(78.6338 * scaleX, 44.4289 * scaleY, 73.3699 * scaleX, 49.7015 * scaleY, 66.8761 * scaleX, 49.7015 * scaleY);
    path3.cubicTo(65.0166 * scaleX, 49.7015 * scaleY, 63.266 * scaleX, 49.2592 * scaleY, 61.7045 * scaleX, 48.4888 * scaleY);
    path3.cubicTo(61.213 * scaleX, 48.1783 * scaleY, 60.6247 * scaleX, 47.6051 * scaleY, 60.5399 * scaleX, 46.6168 * scaleY);
    path3.lineTo(60.5399 * scaleX, 33.0598 * scaleY);
    path3.lineTo(54.3027 * scaleX, 42.0753 * scaleY);
    path3.lineTo(54.3027 * scaleX, 48.71 * scaleY);
    path3.cubicTo(54.3114 * scaleX, 48.8048 * scaleY, 54.3408 * scaleX, 49.172 * scaleY, 54.3854 * scaleX, 49.4869 * scaleY);
    path3.cubicTo(54.4213 * scaleX, 49.683 * scaleY, 54.4419 * scaleX, 49.7931 * scaleY, 54.4474 * scaleX, 49.8192 * scaleY);
    path3.cubicTo(54.53 * scaleX, 50.1592 * scaleY, 54.6387 * scaleX, 50.4534 * scaleY, 54.7573 * scaleX, 50.7171 * scaleY);
    path3.cubicTo(54.9291 * scaleX, 51.0167 * scaleY, 55.0019 * scaleX, 51.1344 * scaleY, 55.1194 * scaleX, 51.3185 * scaleY);
    path3.cubicTo(55.339 * scaleX, 51.677 * scaleY, 55.5152 * scaleX, 51.835 * scaleY, 55.6696 * scaleX, 52.0083 * scaleY);
    path3.cubicTo(58.1836 * scaleX, 54.2224 * scaleY, 62.383 * scaleX, 55.9996 * scaleY, 66.8771 * scaleX, 55.9996 * scaleY);
    path3.cubicTo(76.8397 * scaleX, 55.9996 * scaleY, 84.9189 * scaleX, 47.9059 * scaleY, 84.9189 * scaleX, 37.9227 * scaleY);
    path3.cubicTo(84.9178 * scaleX, 27.9364 * scaleY, 76.8386 * scaleX, 19.8438 * scaleY, 66.8761 * scaleX, 19.8438 * scaleY);
    path3.close();

    // Path 4 - Top Circle
    final path4 = Path();
    path4.moveTo(31.0361 * scaleX, 20.5797 * scaleY);
    path4.cubicTo(30.861 * scaleX, 19.7734 * scaleY, 30.7664 * scaleX, 18.9376 * scaleY, 30.7664 * scaleX, 18.079 * scaleY);
    path4.cubicTo(30.7664 * scaleX, 11.5859 * scaleY, 36.0195 * scaleX, 6.32307 * scaleY, 42.5002 * scaleX, 6.32307 * scaleY);
    path4.cubicTo(48.981 * scaleX, 6.32307 * scaleY, 54.2341 * scaleX, 11.5859 * scaleY, 54.2341 * scaleX, 18.079 * scaleY);
    path4.cubicTo(54.2341 * scaleX, 18.9376 * scaleY, 54.1395 * scaleX, 19.7734 * scaleY, 53.9655 * scaleX, 20.5797 * scaleY);
    path4.cubicTo(55.8967 * scaleX, 19.1664 * scaleY, 58.1204 * scaleX, 18.1313 * scaleY, 60.5278 * scaleX, 17.5756 * scaleY);
    path4.cubicTo(60.2614 * scaleX, 7.82348 * scaleY, 52.2953 * scaleX, 0 * scaleY, 42.5002 * scaleX, 0 * scaleY);
    path4.cubicTo(32.7052 * scaleX, 0 * scaleY, 24.7391 * scaleX, 7.82348 * scaleY, 24.4727 * scaleX, 17.5756 * scaleY);
    path4.cubicTo(26.879 * scaleX, 18.1313 * scaleY, 29.1027 * scaleX, 19.1664 * scaleY, 31.0361 * scaleX, 20.5797 * scaleY);
    path4.close();

    canvas.drawPath(path1, paint);
    canvas.drawPath(path2, paint);
    canvas.drawPath(path3, paint);
    canvas.drawPath(path4, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

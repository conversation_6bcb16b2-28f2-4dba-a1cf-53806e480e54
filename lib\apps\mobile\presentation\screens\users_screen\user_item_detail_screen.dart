import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/data/services/users_api_service.dart';
import '../../../../../shared/services/api_endpoints.dart';
import '../../../../../shared/core/config/app_config.dart';
import '../../../../../shared/widgets/authenticated_image.dart';
import 'user_detail_screen.dart';
import '../users_screen.dart';

/// User item detail screen - hiển thị chi tiết thông tin user
class UserItemDetailScreen extends StatefulWidget {
  final UserUIModel user;

  const UserItemDetailScreen({super.key, required this.user});

  @override
  State<UserItemDetailScreen> createState() => _UserItemDetailScreenState();
}

class _UserItemDetailScreenState extends State<UserItemDetailScreen> {
  late final UsersApiService _usersApiService;
  OverlayEntry? _loadingOverlay;

  @override
  void initState() {
    super.initState();
    _usersApiService = GetIt.instance<UsersApiService>();
  }

  @override
  void dispose() {
    _hideLoadingOverlay();
    super.dispose();
  }

  String _getGenderDisplayText(String? genderValue) {
    switch (genderValue) {
      case 'male':
        return 'Nam';
      case 'female':
        return 'Nữ';
      case 'other':
        return 'Khác';
      default:
        return 'Nam'; // Default fallback
    }
  }

  void _showLoadingOverlay() {
    if (_loadingOverlay != null || !mounted) return;

    _loadingOverlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black54,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );

    Overlay.of(context).insert(_loadingOverlay!);

    if (kDebugMode) {
      print('Loading overlay shown');
    }
  }

  void _hideLoadingOverlay() {
    if (_loadingOverlay != null) {
      try {
        _loadingOverlay!.remove();
        _loadingOverlay = null;
        if (kDebugMode) {
          print('Loading overlay removed');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error removing loading overlay: $e');
        }
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).padding.bottom + 80, // Add bottom padding for navigation
                ),
                child: _buildContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Text(
            'Chi tiết thành viên',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildProfileSection(),
          _buildBasicInfoSection(),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Container(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          SizedBox(height: AppDimensions.spacing16),

          // Avatar
          _buildAvatar(),

          SizedBox(height: AppDimensions.spacing16),

          // User name and email
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.user.name,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF1F2329),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.spacing4),
              Text(
                widget.user.email,
                style: AppTextStyles.caption.copyWith(
                  color: const Color(0xFF85888C),
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacing12),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: const Color(0xFF008FD3),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  child: TextButton.icon(
                    onPressed: () => _editUser(),
                    icon: const Icon(
                      Icons.edit_outlined,
                      size: 16,
                      color: Colors.white,
                    ),
                    label: Text(
                      'Chỉnh sửa',
                      style: AppTextStyles.caption.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacing12),
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: const Color(0xFFE5E6E7)),
                ),
                child: IconButton(
                  onPressed: () => _showMoreOptions(),
                  icon: const Icon(
                    Icons.more_horiz,
                    size: 16,
                    color: Color(0xFF1F2329),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Stack(
      children: [
        Container(
          width: 92,
          height: 92,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            color: AppColors.border,
          ),
          child: ClipOval(
            child: _buildAvatarImage(),
          ),
        ),

        // Status indicator
        Positioned(
          right: 8,
          bottom: 5,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.user.status == UserStatus.active
                  ? const Color(0xFF40BF24)
                  : Colors.grey,
              border: Border.all(color: Colors.white, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAvatarImage() {
    if (widget.user.avatar.isNotEmpty) {
      return AuthenticatedImage(
        imageId: widget.user.avatar,
        width: 88,
        height: 88,
        fit: BoxFit.cover,
        placeholder: const Icon(
          Icons.person,
          size: 48,
          color: Colors.grey,
        ),
      );
    } else {
      return const Icon(
        Icons.person,
        size: 48,
        color: Colors.grey,
      );
    }
  }

  /// Tạo URL để lấy image từ server
  String _createImageStreamUrl(String imageId) {
    final appConfig = AppConfig();
    final baseUrl = appConfig.baseApiUrl;
    final endpoint = ApiEndpoints.imageStream(imageId);
    return '$baseUrl$endpoint';
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Color(0xFF008FD3), width: 1.5),
                  ),
                ),
                child: Text(
                  'Thông tin cơ bản',
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF008FD3),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 1.5,
                  margin: const EdgeInsets.only(
                    bottom: 0,
                  ), // Align với bottom của text
                  color: const Color(0xFF1F2329).withValues(alpha: 0.15),
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacing16),

          // Info card
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: const Color(0xFFE5E6E7)),
            ),
            child: Column(
              children: [
                _buildInfoRow('Mã nhân viên:', widget.user.id),
                _buildInfoRow('Email:', widget.user.email),
                _buildInfoRow('Số điện thoại:', widget.user.phone),
                _buildInfoRow('Tổ chức:', widget.user.originalModel.tenant?['name'] ?? 'CMC TS'),
                _buildInfoRow('Đơn vị:', widget.user.unit),
                _buildInfoRow('Vai trò:', widget.user.originalModel.currentRole?['name'] ?? 'Product Executive'),
                _buildInfoRow('Ngày sinh:', widget.user.originalModel.dob?.toString().split(' ')[0] ?? '19/10/2000'),
                _buildInfoRow('Giới tính:', _getGenderDisplayText(widget.user.originalModel.gender), isLast: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isLast = false}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: AppTextStyles.caption.copyWith(
                fontWeight: FontWeight.w500,
                color: const Color(0xFF73787E),
              ),
            ),
            Text(
              value,
              style: AppTextStyles.caption.copyWith(
                color: const Color(0xFF1F2329),
              ),
            ),
          ],
        ),
        if (!isLast) SizedBox(height: AppDimensions.spacing16),
      ],
    );
  }

  void _editUser() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(
          userId: widget.user.realId, // Use realId (_id) for API calls
          initialName: widget.user.name,
          initialEmail: widget.user.email,
          initialPhone: widget.user.phone,
          initialDepartment: widget.user.unit,
          initialRole: widget.user.originalModel.currentRole?['name'] ?? 'Product Executive',
        ),
      ),
    );

    if (result == true && mounted) {
      // User was updated, pop back to refresh the list
      Navigator.of(context).pop(true);
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusM),
            topRight: Radius.circular(AppDimensions.radiusM),
          ),
        ),
        child: SafeArea(
          child: Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: EdgeInsets.only(bottom: AppDimensions.spacing16),
                  decoration: BoxDecoration(
                    color: AppColors.border,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Xem ảnh khuôn mặt (luôn hiển thị)
                ListTile(
                  leading: const Icon(Icons.face, color: Colors.blue),
                  title: const Text('Xem ảnh khuôn mặt'),
                  subtitle: Text(_hasFaceImages()
                    ? 'Xem các góc chụp khuôn mặt'
                    : 'Chưa có ảnh khuôn mặt'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showFaceImagesModal();
                  },
                ),
                const Divider(height: 1),

                // Xóa thành viên
                ListTile(
                  leading: const Icon(Icons.delete_outline, color: Colors.red),
                  title: const Text('Xóa thành viên'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _confirmDelete();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text(
          'Bạn có chắc chắn muốn xóa thành viên ${widget.user.name}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteUser();
            },
            child: const Text('Xóa', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteUser() async {
    try {
      // Show loading overlay instead of dialog
      _showLoadingOverlay();

      // Call delete API using realId (_id) with proper service injection
      if (kDebugMode) {
        print('Deleting user with ID: ${widget.user.realId}');
      }
      await _usersApiService.deleteUser(widget.user.realId);
      if (kDebugMode) {
        print('User deleted successfully');
      }

      // Mark users list for refresh
      UserListRefreshNotifier.markForRefresh();

      // Close loading overlay
      _hideLoadingOverlay();

      // Small delay to ensure dialog is closed
      await Future.delayed(const Duration(milliseconds: 200));

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Đã xóa thành viên ${widget.user.name}')),
        );

        if (kDebugMode) {
          print('Navigating back to users list with refresh');
        }

        // Return to users screen and force refresh
        if (kDebugMode) {
          print('Popping back to users screen with refresh signal');
        }

        // Pop this screen and signal refresh
        Navigator.of(context).pop('refresh_needed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting user: $e');
      }

      if (mounted) {
        // Close loading overlay
        _hideLoadingOverlay();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi xóa thành viên: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Kiểm tra user có face images không (với mock logic)
  bool _hasFaceImages() {
    // Kiểm tra thực tế nếu user có faceId
    if (widget.user.originalModel.faceId != null &&
        widget.user.originalModel.faceId!.isNotEmpty) {
      return true;
    }

    // Mock logic: Random 60% chance có face images
    // Sử dụng user ID để tạo consistent random result
    final userId = widget.user.originalModel.id;
    final hash = userId.hashCode.abs();
    return (hash % 10) < 6; // 60% chance
  }

  /// Hiển thị modal xem ảnh khuôn mặt
  void _showFaceImagesModal() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  const Icon(Icons.face, color: Colors.blue),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Ảnh khuôn mặt đã upload',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Face images placeholder
              _buildFaceImagesFromServer(),

              const SizedBox(height: 20),

              // Actions
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Đóng'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Xây dựng grid hiển thị ảnh khuôn mặt từ server
  Widget _buildFaceImagesFromServer() {
    final hasFaceImages = _hasFaceImages();

    if (!hasFaceImages) {
      // Hiển thị thông báo chưa có ảnh
      return _buildNoFaceImagesView();
    }

    // Hiển thị 5 ảnh sample các góc mặt
    final faceDirections = [
      {'name': 'Chính giữa', 'icon': Icons.face},
      {'name': 'Trái', 'icon': Icons.keyboard_arrow_left},
      {'name': 'Phải', 'icon': Icons.keyboard_arrow_right},
      {'name': 'Lên', 'icon': Icons.keyboard_arrow_up},
      {'name': 'Xuống', 'icon': Icons.keyboard_arrow_down},
    ];

    return Column(
      children: [
        // Header info
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.green.shade700, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Face ID: ${widget.user.originalModel.faceId ?? _generateMockFaceId()}',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Grid hiển thị 5 góc mặt
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: faceDirections.length,
          itemBuilder: (context, index) {
            final direction = faceDirections[index];
            return _buildFaceImageSlot(
              direction['name'] as String,
              direction['icon'] as IconData,
              index,
            );
          },
        ),
      ],
    );
  }

  /// Hiển thị view khi chưa có ảnh khuôn mặt
  Widget _buildNoFaceImagesView() {
    return Column(
      children: [
        // Header info
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.warning_amber_outlined, color: Colors.orange.shade700, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Chưa có dữ liệu khuôn mặt',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Empty state illustration
        Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.face_retouching_off,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'Chưa có ảnh khuôn mặt',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Người dùng này chưa thực hiện đăng ký khuôn mặt.\nVui lòng thực hiện chụp ảnh khuôn mặt để sử dụng tính năng nhận diện.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Tạo mock Face ID
  String _generateMockFaceId() {
    final userId = widget.user.originalModel.id;
    final hash = userId.hashCode.abs();
    return 'face_${hash.toString().substring(0, 6)}';
  }

  /// Xây dựng slot cho từng góc mặt
  Widget _buildFaceImageSlot(String directionName, IconData icon, int index) {
    return GestureDetector(
      onTap: () => _showFaceImagePreview(directionName, index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.green.shade300,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Mock face image với gradient background
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _getMockImageColors(index),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.face,
                      color: Colors.white.withValues(alpha: 0.8),
                      size: 32,
                    ),
                    const SizedBox(height: 4),
                    Icon(
                      icon,
                      color: Colors.white.withValues(alpha: 0.6),
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),

            // Direction label
            Positioned(
              bottom: 4,
              left: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  directionName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

            // Success indicator
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Tạo màu gradient cho mock images
  List<Color> _getMockImageColors(int index) {
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600],
      [Colors.green.shade400, Colors.green.shade600],
      [Colors.purple.shade400, Colors.purple.shade600],
      [Colors.orange.shade400, Colors.orange.shade600],
      [Colors.teal.shade400, Colors.teal.shade600],
    ];
    return colors[index % colors.length];
  }

  /// Hiển thị preview ảnh khuôn mặt lớn
  void _showFaceImagePreview(String directionName, int index) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Góc: $directionName',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    constraints: const BoxConstraints(
                      maxWidth: 300,
                      maxHeight: 300,
                    ),
                    child: Container(
                      width: 250,
                      height: 250,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: _getMockImageColors(index),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.face,
                            color: Colors.white.withValues(alpha: 0.9),
                            size: 80,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            directionName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Mock Face Image',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 20,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

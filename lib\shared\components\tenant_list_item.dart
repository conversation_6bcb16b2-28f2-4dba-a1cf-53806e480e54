import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_dimensions.dart';

/// List item component cho danh sách tenant/tổ chức
class TenantListItem extends StatelessWidget {
  final String name;
  final String address;
  final String avatarText;
  final VoidCallback? onTap;
  final bool showDivider;

  const TenantListItem({
    super.key,
    required this.name,
    required this.address,
    required this.avatarText,
    this.onTap,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS + 4,
            ),
            child: Row(
              children: [
                _buildAvatar(),
                SizedBox(width: AppDimensions.spacing12),
                Expanded(
                  child: _buildContent(),
                ),
                _buildArrowIcon(),
              ],
            ),
          ),
        ),
        if (showDivider) _buildDivider(),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: AppDimensions.avatarM,
      height: AppDimensions.avatarM,
      decoration: BoxDecoration(
        color: AppColors.avatarBackground,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Center(
        child: Text(
          avatarText,
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name,
          style: AppTextStyles.labelSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacing2),
        Text(
          address,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildArrowIcon() {
    return Icon(
      Icons.chevron_right,
      size: AppDimensions.iconS,
      color: AppColors.textPrimary,
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      height: AppDimensions.dividerThickness,
      color: AppColors.border,
    );
  }
}

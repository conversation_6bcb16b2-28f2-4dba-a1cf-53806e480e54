import 'package:flutter/material.dart';

/// Simplified Face Recognition Configuration Screen for build compatibility
/// 
/// This is a simplified version that doesn't depend on providers
/// that may not be fully implemented yet.
class FaceRecognitionConfigScreen extends StatefulWidget {
  const FaceRecognitionConfigScreen({super.key});

  @override
  State<FaceRecognitionConfigScreen> createState() => _FaceRecognitionConfigScreenState();
}

class _FaceRecognitionConfigScreenState extends State<FaceRecognitionConfigScreen> 
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Simulated configuration state
  bool _sideEffectsEnabled = true;
  bool _triggerRelayOnAccess = true;
  bool _showSuccessNotification = true;
  bool _showDeniedNotification = false;
  bool _logAllAttempts = true;
  bool _saveRecognitionImages = true;
  
  String _defaultRelayDevice = 'T-A3B4-R01';
  int _relayTriggerDuration = 3;
  int _notificationDuration = 3;
  
  bool _requireServerApproval = true;
  double _minimumConfidenceThreshold = 0.7;
  List<String> _allowedAccessLevels = ['ADMIN', 'USER'];
  
  bool _autoRegisterEnabled = true;
  int _defaultRelayCount = 4;
  List<String> _registeredDevices = ['T-A3B4-R01', 'T-A3B4-R02', 'T-A3B4-R03', 'T-A3B4-R04'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Recognition Configuration'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.settings), text: 'Side Effects'),
            Tab(icon: Icon(Icons.devices), text: 'Devices'),
            Tab(icon: Icon(Icons.security), text: 'Access Control'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSideEffectsTab(),
          _buildDevicesTab(),
          _buildAccessControlTab(),
        ],
      ),
    );
  }

  Widget _buildSideEffectsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main toggle
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Side Effects Configuration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Configure what happens after face recognition results are received from server',
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Side Effects'),
                    subtitle: const Text('Master switch for all side effects'),
                    value: _sideEffectsEnabled,
                    onChanged: (value) {
                      setState(() => _sideEffectsEnabled = value);
                      _showConfigSavedMessage();
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Relay Control
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Relay Control',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Trigger Relay on Access Granted'),
                    subtitle: const Text('Automatically trigger relay when server grants access'),
                    value: _triggerRelayOnAccess,
                    onChanged: _sideEffectsEnabled ? (value) {
                      setState(() => _triggerRelayOnAccess = value);
                      _showConfigSavedMessage();
                    } : null,
                  ),
                  const SizedBox(height: 8),
                  ListTile(
                    title: const Text('Default Relay Device'),
                    subtitle: Text(_defaultRelayDevice),
                    trailing: const Icon(Icons.edit),
                    onTap: _sideEffectsEnabled ? _editRelayDevice : null,
                  ),
                  ListTile(
                    title: const Text('Trigger Duration'),
                    subtitle: Text('$_relayTriggerDuration seconds'),
                    trailing: const Icon(Icons.timer),
                    onTap: _sideEffectsEnabled ? _editTriggerDuration : null,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Notifications
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Notifications',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Show Success Notifications'),
                    subtitle: const Text('Display notification when access is granted'),
                    value: _showSuccessNotification,
                    onChanged: _sideEffectsEnabled ? (value) {
                      setState(() => _showSuccessNotification = value);
                      _showConfigSavedMessage();
                    } : null,
                  ),
                  SwitchListTile(
                    title: const Text('Show Denied Notifications'),
                    subtitle: const Text('Display notification when access is denied'),
                    value: _showDeniedNotification,
                    onChanged: _sideEffectsEnabled ? (value) {
                      setState(() => _showDeniedNotification = value);
                      _showConfigSavedMessage();
                    } : null,
                  ),
                  ListTile(
                    title: const Text('Notification Duration'),
                    subtitle: Text('$_notificationDuration seconds'),
                    trailing: const Icon(Icons.schedule),
                    onTap: _sideEffectsEnabled ? _editNotificationDuration : null,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Logging & Storage
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Logging & Storage',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Log All Attempts'),
                    subtitle: const Text('Keep logs of all face recognition attempts'),
                    value: _logAllAttempts,
                    onChanged: _sideEffectsEnabled ? (value) {
                      setState(() => _logAllAttempts = value);
                      _showConfigSavedMessage();
                    } : null,
                  ),
                  SwitchListTile(
                    title: const Text('Save Recognition Images'),
                    subtitle: const Text('Save face images for audit purposes'),
                    value: _saveRecognitionImages,
                    onChanged: _sideEffectsEnabled ? (value) {
                      setState(() => _saveRecognitionImages = value);
                      _showConfigSavedMessage();
                    } : null,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevicesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Auto Registration
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Auto Device Registration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Automatically register relay devices when terminal connects to server',
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Auto Registration'),
                    subtitle: const Text('Register relay devices automatically'),
                    value: _autoRegisterEnabled,
                    onChanged: (value) {
                      setState(() => _autoRegisterEnabled = value);
                      _showConfigSavedMessage();
                    },
                  ),
                  ListTile(
                    title: const Text('Default Relay Count'),
                    subtitle: Text('$_defaultRelayCount relays'),
                    trailing: const Icon(Icons.edit),
                    onTap: _editRelayCount,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Terminal Info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Terminal Device',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  const ListTile(
                    title: Text('Terminal ID'),
                    subtitle: Text('T-A3B4 (Simulated)'),
                    leading: Icon(Icons.check_circle, color: Colors.green),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Registered Devices
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Registered Relay Devices',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const Spacer(),
                      Text(
                        '${_registeredDevices.length} devices',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ..._registeredDevices.map((deviceId) {
                    final index = _registeredDevices.indexOf(deviceId);
                    final profiles = ['Main Door', 'Back Door', 'Garage', 'Emergency'];
                    final profile = index < profiles.length ? profiles[index] : 'Custom';
                    
                    return ListTile(
                      leading: const Icon(Icons.electrical_services),
                      title: Text(deviceId),
                      subtitle: Text('Terminal Relay ${index + 1} ($profile)'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _confirmUnregisterDevice(deviceId),
                      ),
                    );
                  }).toList(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _triggerAutoRegistration,
                          icon: const Icon(Icons.refresh),
                          label: const Text('Re-register All'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _addManualDevice,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Manual'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessControlTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Access Control Settings
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Access Control Settings',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Require Server Approval'),
                    subtitle: const Text('Always require server to approve access'),
                    value: _requireServerApproval,
                    onChanged: (value) {
                      setState(() => _requireServerApproval = value);
                      _showConfigSavedMessage();
                    },
                  ),
                  ListTile(
                    title: const Text('Minimum Confidence Threshold'),
                    subtitle: Text('${(_minimumConfidenceThreshold * 100).toInt()}%'),
                    trailing: const Icon(Icons.tune),
                    onTap: _editConfidenceThreshold,
                  ),
                  ListTile(
                    title: const Text('Allowed Access Levels'),
                    subtitle: Text(_allowedAccessLevels.join(', ')),
                    trailing: const Icon(Icons.edit),
                    onTap: _editAccessLevels,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Configuration Summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Configuration Summary',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  _buildSummaryRow('SIDE EFFECTS ENABLED', _sideEffectsEnabled.toString()),
                  _buildSummaryRow('RELAY TRIGGER ENABLED', _triggerRelayOnAccess.toString()),
                  _buildSummaryRow('NOTIFICATION ENABLED', (_showSuccessNotification || _showDeniedNotification).toString()),
                  _buildSummaryRow('LOGGING ENABLED', _logAllAttempts.toString()),
                  _buildSummaryRow('IMAGE SAVING ENABLED', _saveRecognitionImages.toString()),
                  _buildSummaryRow('REGISTERED RELAY COUNT', _registeredDevices.length.toString()),
                  _buildSummaryRow('AUTO REGISTER ENABLED', _autoRegisterEnabled.toString()),
                  _buildSummaryRow('MIN CONFIDENCE THRESHOLD', _minimumConfidenceThreshold.toString()),
                  _buildSummaryRow('ALLOWED ACCESS LEVELS', _allowedAccessLevels.toString()),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Reset Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _confirmResetToDefaults,
              icon: const Icon(Icons.restore),
              label: const Text('Reset to Defaults'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String key, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            child: Text(
              key,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  void _showConfigSavedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✅ Configuration saved'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _editRelayDevice() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Relay device selection will be implemented')),
    );
  }

  void _editTriggerDuration() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Duration picker will be implemented')),
    );
  }

  void _editNotificationDuration() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification duration picker will be implemented')),
    );
  }

  void _editRelayCount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Relay count picker will be implemented')),
    );
  }

  void _editConfidenceThreshold() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Confidence threshold slider will be implemented')),
    );
  }

  void _editAccessLevels() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Access levels selection will be implemented')),
    );
  }

  void _triggerAutoRegistration() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('✅ Auto-registration triggered (simulated)')),
    );
  }

  void _addManualDevice() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Manual device addition will be implemented')),
    );
  }

  void _confirmUnregisterDevice(String deviceId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unregister Device'),
        content: Text('Are you sure you want to unregister $deviceId?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() => _registeredDevices.remove(deviceId));
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('✅ $deviceId unregistered')),
              );
            },
            child: const Text('Unregister'),
          ),
        ],
      ),
    );
  }

  void _confirmResetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Defaults'),
        content: const Text('Are you sure you want to reset all configuration to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _sideEffectsEnabled = true;
                _triggerRelayOnAccess = true;
                _showSuccessNotification = true;
                _showDeniedNotification = false;
                _logAllAttempts = true;
                _saveRecognitionImages = true;
                _defaultRelayDevice = 'T-A3B4-R01';
                _relayTriggerDuration = 3;
                _notificationDuration = 3;
                _requireServerApproval = true;
                _minimumConfidenceThreshold = 0.7;
                _allowedAccessLevels = ['ADMIN', 'USER'];
                _autoRegisterEnabled = true;
                _defaultRelayCount = 4;
                _registeredDevices = ['T-A3B4-R01', 'T-A3B4-R02', 'T-A3B4-R03', 'T-A3B4-R04'];
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('✅ Configuration reset to defaults')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}

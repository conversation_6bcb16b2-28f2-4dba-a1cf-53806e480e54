import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';

import '../src/detection/detection_engine.dart';
import '../src/detection/engines/mediapipe_engine.dart';
import '../src/detection/engines/ultraface_engine.dart';
import '../src/detection/hybrid_detector.dart';

/// Test script for MediaPipe BlazeFace engine
/// This script validates the MediaPipe engine implementation
class MediaPipeEngineTest {
  
  /// Test MediaPipe engine initialization
  static Future<bool> testInitialization() async {
    print('🧪 Testing MediaPipe Engine Initialization...');
    
    try {
      final engine = MediaPipeEngine();
      
      // Test initialization
      await engine.initialize();
      
      if (!engine.isInitialized) {
        print('❌ Engine not initialized after initialize() call');
        return false;
      }
      
      // Test engine properties
      if (engine.name != 'MediaPipe-BlazeFace') {
        print('❌ Unexpected engine name: ${engine.name}');
        return false;
      }
      
      if (engine.version != '1.0.0') {
        print('❌ Unexpected engine version: ${engine.version}');
        return false;
      }
      
      // Test stats
      final stats = engine.getStats();
      if (stats.engineName != engine.name) {
        print('❌ Stats engine name mismatch');
        return false;
      }
      
      // Clean up
      await engine.dispose();
      
      print('✅ MediaPipe Engine initialization test passed');
      return true;
      
    } catch (e) {
      print('❌ MediaPipe Engine initialization test failed: $e');
      return false;
    }
  }
  
  /// Test MediaPipe engine with synthetic image data
  static Future<bool> testDetection() async {
    print('🧪 Testing MediaPipe Engine Detection...');
    
    try {
      final engine = MediaPipeEngine();
      await engine.initialize();
      
      // Create synthetic test image (128x128 RGB)
      final width = 128;
      final height = 128;
      final testImage = _createTestImage(width, height);
      
      // Test detection
      final faces = await engine.detectFacesFromBytes(testImage, width, height);
      
      // Validate results (synthetic image may not have faces, but should not crash)
      if (faces.isNotEmpty) {
        print('📊 Detected ${faces.length} faces in synthetic image');
        
        for (int i = 0; i < faces.length; i++) {
          final face = faces[i];
          print('  Face $i: confidence=${face.confidence.toStringAsFixed(3)}, '
                'box=(${face.boundingBox.left.toStringAsFixed(1)}, '
                '${face.boundingBox.top.toStringAsFixed(1)}, '
                '${face.boundingBox.width.toStringAsFixed(1)}, '
                '${face.boundingBox.height.toStringAsFixed(1)})');
        }
      } else {
        print('📊 No faces detected in synthetic image (expected for random data)');
      }
      
      // Test configuration with landmarks
      engine.configure(
        confidenceThreshold: 0.3,
        maxFaces: 10,
        enableLandmarks: true,
      );

      // Test detection again with new config and landmarks
      final faces2 = await engine.detectFacesFromBytes(testImage, width, height);
      print('📊 After config change: detected ${faces2.length} faces');

      // Check if any faces have landmarks
      for (int i = 0; i < faces2.length; i++) {
        final face = faces2[i];
        if (face.landmarks != null && face.landmarks!.isNotEmpty) {
          print('  Face $i has ${face.landmarks!.length} landmarks');

          // Print some key landmarks
          for (final landmark in face.landmarks!) {
            print('    ${landmark.type}: (${landmark.position.dx.toStringAsFixed(1)}, ${landmark.position.dy.toStringAsFixed(1)})');
          }
        } else {
          print('  Face $i has no landmarks');
        }
      }
      
      // Test stats after detection
      final stats = engine.getStats();
      if (stats.totalFramesProcessed < 2) {
        print('❌ Stats not updated correctly: ${stats.totalFramesProcessed} frames');
        return false;
      }
      
      print('📊 Engine stats: ${stats.totalFramesProcessed} frames, '
            '${stats.averageProcessingTime.toStringAsFixed(2)}ms avg, '
            '${stats.currentFPS.toStringAsFixed(1)} FPS');
      
      // Clean up
      await engine.dispose();
      
      print('✅ MediaPipe Engine detection test passed');
      return true;
      
    } catch (e) {
      print('❌ MediaPipe Engine detection test failed: $e');
      return false;
    }
  }
  
  /// Test hybrid detector with MediaPipe
  static Future<bool> testHybridDetector() async {
    print('🧪 Testing Hybrid Detector with MediaPipe...');
    
    try {
      // Create hybrid detector for Telpo F8
      final detector = await HybridDetector.createForTelpoF8(
        confidenceThreshold: 0.5,
        maxFaces: 5,
        enableFallback: true,
      );
      
      // Check engine info
      final engineInfo = detector.getEngineInfo();
      print('📊 Engine info: $engineInfo');
      
      if (engineInfo['primary'] != 'UltraFace') {
        print('❌ Expected UltraFace as primary engine, got: ${engineInfo['primary']}');
        return false;
      }
      
      if (engineInfo['fallback'] != 'MediaPipe-BlazeFace') {
        print('❌ Expected MediaPipe-BlazeFace as fallback engine, got: ${engineInfo['fallback']}');
        return false;
      }
      
      // Create test image
      final width = 320;
      final height = 240;
      final testImage = _createTestImage(width, height);
      
      // Test detection
      final faces = await detector.detectFacesFromBytes(testImage, width, height);
      print('📊 Hybrid detector found ${faces.length} faces');
      
      // Test stats
      final stats = detector.getStats();
      print('📊 Hybrid stats: ${stats.totalDetections} detections, '
            '${stats.primaryEngineUsagePercent.toStringAsFixed(1)}% primary usage');
      
      // Test configuration
      detector.configure(
        confidenceThreshold: 0.3,
        maxFaces: 10,
        enableFallback: true,
      );
      
      // Test engine switching
      detector.switchEngines();
      final newEngineInfo = detector.getEngineInfo();
      print('📊 After switch: ${newEngineInfo}');
      
      // Clean up
      await detector.dispose();
      
      print('✅ Hybrid Detector test passed');
      return true;
      
    } catch (e) {
      print('❌ Hybrid Detector test failed: $e');
      return false;
    }
  }
  
  /// Compare UltraFace vs MediaPipe performance
  static Future<bool> testEngineComparison() async {
    print('🧪 Testing Engine Performance Comparison...');
    
    try {
      final ultraFaceEngine = UltraFaceEngine();
      final mediaPipeEngine = MediaPipeEngine();
      
      await ultraFaceEngine.initialize();
      await mediaPipeEngine.initialize();
      
      // Create test image
      final width = 320;
      final height = 240;
      final testImage = _createTestImage(width, height);
      
      // Test UltraFace performance
      final ultraFaceStart = DateTime.now();
      final ultraFaceFaces = await ultraFaceEngine.detectFacesFromBytes(testImage, width, height);
      final ultraFaceTime = DateTime.now().difference(ultraFaceStart).inMicroseconds / 1000.0;
      
      // Test MediaPipe performance
      final mediaPipeStart = DateTime.now();
      final mediaPipeFaces = await mediaPipeEngine.detectFacesFromBytes(testImage, width, height);
      final mediaPipeTime = DateTime.now().difference(mediaPipeStart).inMicroseconds / 1000.0;
      
      // Compare results
      print('📊 Performance Comparison:');
      print('  UltraFace: ${ultraFaceFaces.length} faces, ${ultraFaceTime.toStringAsFixed(2)}ms');
      print('  MediaPipe: ${mediaPipeFaces.length} faces, ${mediaPipeTime.toStringAsFixed(2)}ms');
      
      final speedRatio = ultraFaceTime / mediaPipeTime;
      if (speedRatio > 1) {
        print('  MediaPipe is ${speedRatio.toStringAsFixed(2)}x faster');
      } else {
        print('  UltraFace is ${(1/speedRatio).toStringAsFixed(2)}x faster');
      }
      
      // Get detailed stats
      final ultraFaceStats = ultraFaceEngine.getStats();
      final mediaPipeStats = mediaPipeEngine.getStats();
      
      print('📊 Memory Usage:');
      print('  UltraFace: ${ultraFaceStats.memoryUsageMB} MB');
      print('  MediaPipe: ${mediaPipeStats.memoryUsageMB} MB');
      
      // Clean up
      await ultraFaceEngine.dispose();
      await mediaPipeEngine.dispose();
      
      print('✅ Engine comparison test passed');
      return true;
      
    } catch (e) {
      print('❌ Engine comparison test failed: $e');
      return false;
    }
  }
  
  /// Test landmark detection specifically
  static Future<bool> testLandmarkDetection() async {
    print('🧪 Testing MediaPipe Landmark Detection...');

    try {
      final engine = MediaPipeEngine();

      // Configure with landmarks enabled
      engine.configure(
        confidenceThreshold: 0.3,
        maxFaces: 5,
        enableLandmarks: true,
      );

      await engine.initialize();

      // Create test image with face-like pattern
      final width = 128;
      final height = 128;
      final testImage = _createTestImage(width, height);

      // Test detection with landmarks
      final faces = await engine.detectFacesFromBytes(testImage, width, height);

      print('📊 Detected ${faces.length} faces with landmark detection enabled');

      bool hasLandmarks = false;
      for (int i = 0; i < faces.length; i++) {
        final face = faces[i];
        if (face.landmarks != null && face.landmarks!.isNotEmpty) {
          hasLandmarks = true;
          print('  Face $i: ${face.landmarks!.length} landmarks detected');

          // Validate landmark types
          final landmarkTypes = face.landmarks!.map((l) => l.type).toSet();
          print('  Landmark types: ${landmarkTypes.map((t) => t.toString().split('.').last).join(', ')}');

          // Check if key landmarks are present
          final hasEyes = landmarkTypes.contains(FaceLandmarkType.leftEye) &&
                         landmarkTypes.contains(FaceLandmarkType.rightEye);
          final hasNose = landmarkTypes.contains(FaceLandmarkType.noseBase);
          final hasMouth = landmarkTypes.contains(FaceLandmarkType.leftMouth) ||
                          landmarkTypes.contains(FaceLandmarkType.rightMouth);

          print('  Key features: Eyes=$hasEyes, Nose=$hasNose, Mouth=$hasMouth');
        }
      }

      if (!hasLandmarks && faces.isNotEmpty) {
        print('⚠️ Faces detected but no landmarks extracted (may be expected for synthetic data)');
      }

      // Test with landmarks disabled
      engine.configure(enableLandmarks: false);
      final facesNoLandmarks = await engine.detectFacesFromBytes(testImage, width, height);

      bool allNoLandmarks = true;
      for (final face in facesNoLandmarks) {
        if (face.landmarks != null && face.landmarks!.isNotEmpty) {
          allNoLandmarks = false;
          break;
        }
      }

      if (allNoLandmarks) {
        print('✅ Landmark detection correctly disabled');
      } else {
        print('⚠️ Some faces still have landmarks when disabled');
      }

      await engine.dispose();

      print('✅ Landmark detection test completed');
      return true;

    } catch (e) {
      print('❌ Landmark detection test failed: $e');
      return false;
    }
  }

  /// Run all tests
  static Future<bool> runAllTests() async {
    print('🚀 Starting MediaPipe Engine Test Suite...');
    print('=' * 60);

    final tests = [
      testInitialization,
      testDetection,
      testLandmarkDetection,
      testHybridDetector,
      testEngineComparison,
    ];
    
    int passed = 0;
    int total = tests.length;
    
    for (int i = 0; i < tests.length; i++) {
      print('\n📋 Test ${i + 1}/${total}:');
      try {
        final result = await tests[i]();
        if (result) {
          passed++;
        }
      } catch (e) {
        print('❌ Test ${i + 1} crashed: $e');
      }
      print('-' * 40);
    }
    
    print('\n' + '=' * 60);
    print('📊 Test Results: $passed/$total tests passed');
    
    if (passed == total) {
      print('🎉 All tests passed! MediaPipe engine is ready for production.');
      return true;
    } else {
      print('⚠️ Some tests failed. Please review the issues above.');
      return false;
    }
  }
  
  // Helper method to create synthetic test image
  static Uint8List _createTestImage(int width, int height) {
    final random = math.Random();
    final bytes = Uint8List(width * height * 3);
    
    // Create a simple pattern with some structure
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final index = (y * width + x) * 3;
        
        // Create a simple gradient pattern
        final r = ((x / width) * 255).round();
        final g = ((y / height) * 255).round();
        final b = ((x + y) / (width + height) * 255).round();
        
        bytes[index] = r.clamp(0, 255);
        bytes[index + 1] = g.clamp(0, 255);
        bytes[index + 2] = b.clamp(0, 255);
      }
    }
    
    return bytes;
  }
}

/// Main function to run tests
Future<void> main() async {
  await MediaPipeEngineTest.runAllTests();
}

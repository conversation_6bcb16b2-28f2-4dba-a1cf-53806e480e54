<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.common.f8sdk">

    <uses-permission android:name="android.permission.NFC" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/sdk"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name="com.common.f8sdk.WiegandActivity" />
        <activity android:name="com.common.f8sdk.RelayActivity" />
        <activity android:name="com.common.f8sdk.RS485Activity" />
        <activity android:name="com.common.f8sdk.LedActivity" />
        <activity android:name="com.common.f8sdk.QrCodeActivity" />
        <activity android:name="com.common.f8sdk.NFCActivity"
            android:launchMode="singleTask"/>
        <activity android:name="com.common.f8sdk.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
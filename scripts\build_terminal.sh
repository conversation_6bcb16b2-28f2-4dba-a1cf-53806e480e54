#!/bin/bash

echo "🖥️  Building C-Face Terminal App..."
echo "===================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build terminal app
echo "🔨 Building terminal APK..."
flutter build apk --target lib/apps/terminal/main_terminal.dart --release

# Check build result
if [ $? -eq 0 ]; then
    echo "✅ Terminal app build completed successfully!"
    echo "🖥️  APK location: build/app/outputs/flutter-apk/app-release.apk"

    # Show APK size
    if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
        APK_SIZE=$(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)
        echo "📊 APK size: $APK_SIZE"
    fi

    echo "🎯 Terminal app optimized for:"
    echo "   • Kiosk mode operation"
    echo "   • Large touch-friendly interface"
    echo "   • Landscape orientation"
    echo "   • Fullscreen immersive mode"
else
    echo "❌ Terminal app build failed!"
    exit 1
fi

import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';

import '../core/interfaces/face_detection_engine.dart';
import '../core/models/face_recognition_config.dart';
import '../shared/engines/mediapipe_detection_engine.dart';
import 'enrollment/face_enrollment_service.dart';
import 'management/user_management_service.dart';
import 'analytics/attendance_analytics_service.dart';
import 'configuration/terminal_config_service.dart';

/// Mobile-specific face recognition system for management and enrollment
class MobileFaceSystem extends ChangeNotifier {
  // Core components
  FaceDetectionEngine? _detectionEngine;
  FaceEnrollmentService? _enrollmentService;
  UserManagementService? _userManagementService;
  AttendanceAnalyticsService? _analyticsService;
  TerminalConfigService? _terminalConfigService;
  
  // Configuration
  FaceRecognitionConfig? _config;
  bool _isInitialized = false;
  
  // State
  List<FaceDetection> _detectedFaces = [];
  EnrollmentSession? _currentEnrollmentSession;
  List<ManagedUser> _managedUsers = [];
  
  // Getters
  bool get isInitialized => _isInitialized;
  List<FaceDetection> get detectedFaces => List.unmodifiable(_detectedFaces);
  EnrollmentSession? get currentEnrollmentSession => _currentEnrollmentSession;
  List<ManagedUser> get managedUsers => List.unmodifiable(_managedUsers);
  bool get isEnrolling => _currentEnrollmentSession != null;
  
  /// Initialize mobile face system
  Future<void> initialize({
    required FaceRecognitionConfig config,
    required MobileDeviceType deviceType,
  }) async {
    if (_isInitialized) {
      throw StateError('MobileFaceSystem is already initialized');
    }
    
    try {
      _config = config;
      
      if (kDebugMode) {
        print('📱 Initializing MobileFaceSystem');
        print('   Device Type: ${deviceType.name}');
        print('   Detection Engine: ${config.detectionEngine.name}');
      }
      
      // Initialize detection engine (MediaPipe for mobile)
      _detectionEngine = MediaPipeDetectionEngine(config: config);
      await _detectionEngine!.initialize();
      
      // Initialize enrollment service
      _enrollmentService = FaceEnrollmentService(config: config);
      await _enrollmentService!.initialize();
      
      // Initialize user management service
      _userManagementService = UserManagementService(config: config);
      await _userManagementService!.initialize();
      
      // Initialize analytics service
      _analyticsService = AttendanceAnalyticsService(config: config);
      await _analyticsService!.initialize();
      
      // Initialize terminal configuration service
      _terminalConfigService = TerminalConfigService(config: config);
      await _terminalConfigService!.initialize();
      
      // Load managed users
      await _loadManagedUsers();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ MobileFaceSystem initialized successfully');
        print('   Managed users: ${_managedUsers.length}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize MobileFaceSystem: $e');
      }
      await dispose();
      rethrow;
    }
  }
  
  /// Start face enrollment session
  Future<EnrollmentSession> startEnrollment({
    required String userId,
    required String userName,
    required String projectId,
    AccessLevel accessLevel = AccessLevel.user,
  }) async {
    if (!_isInitialized) {
      throw StateError('MobileFaceSystem not initialized');
    }
    
    if (_currentEnrollmentSession != null) {
      throw StateError('Enrollment session already in progress');
    }
    
    try {
      _currentEnrollmentSession = await _enrollmentService!.startEnrollment(
        userId: userId,
        userName: userName,
        projectId: projectId,
        accessLevel: accessLevel,
      );
      
      if (kDebugMode) {
        print('📝 Started enrollment for: $userName');
        print('   Session ID: ${_currentEnrollmentSession!.sessionId}');
      }
      
      notifyListeners();
      return _currentEnrollmentSession!;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to start enrollment: $e');
      }
      rethrow;
    }
  }
  
  /// Process image for enrollment
  Future<EnrollmentProgress> processEnrollmentImage(Uint8List imageBytes) async {
    if (_currentEnrollmentSession == null) {
      throw StateError('No enrollment session in progress');
    }
    
    try {
      // Detect faces in image
      final faces = await _detectionEngine!.detectFaces(imageBytes);
      _detectedFaces = faces;
      
      // Process best face for enrollment
      if (faces.isNotEmpty) {
        final bestFace = _selectBestFaceForEnrollment(faces);
        if (bestFace != null) {
          final progress = await _enrollmentService!.processEnrollmentFace(
            _currentEnrollmentSession!,
            bestFace,
          );
          
          if (kDebugMode) {
            print('📸 Enrollment progress: ${progress.completedSamples}/${progress.requiredSamples}');
          }
          
          notifyListeners();
          return progress;
        }
      }
      
      throw Exception('No suitable face detected for enrollment');
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to process enrollment image: $e');
      }
      rethrow;
    }
  }
  
  /// Complete enrollment session
  Future<EnrollmentResult> completeEnrollment() async {
    if (_currentEnrollmentSession == null) {
      throw StateError('No enrollment session in progress');
    }
    
    try {
      final result = await _enrollmentService!.completeEnrollment(_currentEnrollmentSession!);
      
      if (result.success) {
        // Add user to managed users
        final newUser = ManagedUser(
          userId: _currentEnrollmentSession!.userId,
          userName: _currentEnrollmentSession!.userName,
          projectId: _currentEnrollmentSession!.projectId,
          accessLevel: _currentEnrollmentSession!.accessLevel,
          enrolledAt: DateTime.now(),
          isActive: true,
        );
        
        _managedUsers.add(newUser);
        
        // Sync to terminals
        await _syncUserToTerminals(newUser);
        
        if (kDebugMode) {
          print('✅ Enrollment completed for: ${_currentEnrollmentSession!.userName}');
        }
      }
      
      _currentEnrollmentSession = null;
      notifyListeners();
      
      return result;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to complete enrollment: $e');
      }
      rethrow;
    }
  }
  
  /// Cancel enrollment session
  Future<void> cancelEnrollment() async {
    if (_currentEnrollmentSession == null) return;
    
    try {
      await _enrollmentService!.cancelEnrollment(_currentEnrollmentSession!);
      _currentEnrollmentSession = null;
      
      if (kDebugMode) {
        print('❌ Enrollment cancelled');
      }
      
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error cancelling enrollment: $e');
      }
    }
  }
  
  /// Get user management operations
  UserManagementService get userManagement {
    if (!_isInitialized) {
      throw StateError('MobileFaceSystem not initialized');
    }
    return _userManagementService!;
  }
  
  /// Get analytics operations
  AttendanceAnalyticsService get analytics {
    if (!_isInitialized) {
      throw StateError('MobileFaceSystem not initialized');
    }
    return _analyticsService!;
  }
  
  /// Get terminal configuration operations
  TerminalConfigService get terminalConfig {
    if (!_isInitialized) {
      throw StateError('MobileFaceSystem not initialized');
    }
    return _terminalConfigService!;
  }
  
  /// Select best face for enrollment
  FaceDetection? _selectBestFaceForEnrollment(List<FaceDetection> faces) {
    if (faces.isEmpty) return null;
    
    // For enrollment, prioritize quality and frontal pose
    faces.sort((a, b) {
      final scoreA = a.quality * 0.5 + 
                    a.confidence * 0.3 + 
                    (a.pose.isFrontal ? 0.2 : 0.0);
      final scoreB = b.quality * 0.5 + 
                    b.confidence * 0.3 + 
                    (b.pose.isFrontal ? 0.2 : 0.0);
      return scoreB.compareTo(scoreA);
    });
    
    final bestFace = faces.first;
    
    // Higher quality requirements for enrollment
    if (bestFace.quality < 0.8 || bestFace.confidence < 0.9) {
      return null;
    }
    
    return bestFace;
  }
  
  /// Load managed users from storage
  Future<void> _loadManagedUsers() async {
    try {
      _managedUsers = await _userManagementService!.getAllUsers();
      
      if (kDebugMode) {
        print('📋 Loaded ${_managedUsers.length} managed users');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to load managed users: $e');
      }
    }
  }
  
  /// Sync user to all configured terminals
  Future<void> _syncUserToTerminals(ManagedUser user) async {
    try {
      await _terminalConfigService!.syncUserToAllTerminals(user);
      
      if (kDebugMode) {
        print('🔄 Synced user ${user.userName} to terminals');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to sync user to terminals: $e');
      }
    }
  }
  
  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isInitialized = false;
    
    // Cancel any ongoing enrollment
    if (_currentEnrollmentSession != null) {
      await cancelEnrollment();
    }
    
    await _detectionEngine?.dispose();
    await _enrollmentService?.dispose();
    await _userManagementService?.dispose();
    await _analyticsService?.dispose();
    await _terminalConfigService?.dispose();
    
    if (kDebugMode) {
      print('🗑️ MobileFaceSystem disposed');
    }
    
    super.dispose();
  }
}

/// Enrollment session information
class EnrollmentSession {
  final String sessionId;
  final String userId;
  final String userName;
  final String projectId;
  final AccessLevel accessLevel;
  final DateTime startedAt;
  final int requiredSamples;
  final List<Uint8List> capturedSamples;
  
  EnrollmentSession({
    required this.sessionId,
    required this.userId,
    required this.userName,
    required this.projectId,
    required this.accessLevel,
    required this.startedAt,
    this.requiredSamples = 10,
    this.capturedSamples = const [],
  });
  
  bool get isComplete => capturedSamples.length >= requiredSamples;
  double get progress => capturedSamples.length / requiredSamples;
}

/// Enrollment progress information
class EnrollmentProgress {
  final int completedSamples;
  final int requiredSamples;
  final double quality;
  final String feedback;
  final bool isComplete;
  
  EnrollmentProgress({
    required this.completedSamples,
    required this.requiredSamples,
    required this.quality,
    required this.feedback,
    required this.isComplete,
  });
  
  double get progress => completedSamples / requiredSamples;
}

/// Enrollment result
class EnrollmentResult {
  final bool success;
  final String? errorMessage;
  final String? userId;
  final double? averageQuality;
  final int totalSamples;
  
  EnrollmentResult({
    required this.success,
    this.errorMessage,
    this.userId,
    this.averageQuality,
    this.totalSamples = 0,
  });
}

/// Managed user information
class ManagedUser {
  final String userId;
  final String userName;
  final String projectId;
  final AccessLevel accessLevel;
  final DateTime enrolledAt;
  final bool isActive;
  final Map<String, dynamic> metadata;
  
  ManagedUser({
    required this.userId,
    required this.userName,
    required this.projectId,
    required this.accessLevel,
    required this.enrolledAt,
    required this.isActive,
    this.metadata = const {},
  });
}

/// Access levels (shared with terminal)
enum AccessLevel {
  admin,
  user,
  guest,
  denied,
}

import 'dart:typed_data';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:flutter/foundation.dart';

/// Optimized image processing utilities for Snapdragon 8 Gen 2
/// Focuses on efficient CameraImage to InputImage conversion and buffer management
class ImageProcessingOptimizer {
  
  // Buffer pool for reusing Uint8List objects
  static final List<Uint8List> _bufferPool = [];
  static const int _maxPoolSize = 5;
  static const int _bufferSize = 1920 * 1080 * 4; // Max buffer size for 1080p RGBA

  /// Get a buffer from the pool or create a new one
  static Uint8List _getBuffer(int size) {
    // Try to find a suitable buffer from the pool
    for (int i = 0; i < _bufferPool.length; i++) {
      if (_bufferPool[i].length >= size) {
        return _bufferPool.removeAt(i);
      }
    }
    
    // Create new buffer if none available
    return Uint8List(size);
  }

  /// Return a buffer to the pool for reuse
  static void _returnBuffer(Uint8List buffer) {
    if (_bufferPool.length < _maxPoolSize && buffer.length <= _bufferSize) {
      _bufferPool.add(buffer);
    }
  }

  /// Optimized conversion from CameraImage to InputImage for Snapdragon 8 Gen 2
  static InputImage? convertCameraImageToInputImage(
    CameraImage cameraImage,
    CameraDescription camera,
  ) {
    try {
      // Use optimized conversion based on image format
      switch (cameraImage.format.group) {
        case ImageFormatGroup.yuv420:
          return _convertYUV420ToInputImage(cameraImage, camera);
        case ImageFormatGroup.nv21:
          return _convertNV21ToInputImage(cameraImage, camera);
        case ImageFormatGroup.bgra8888:
          return _convertBGRA8888ToInputImage(cameraImage, camera);
        default:
          debugPrint('⚠️ Unsupported image format: ${cameraImage.format.group}');
          return null;
      }
    } catch (e) {
      debugPrint('❌ Error converting camera image: $e');
      return null;
    }
  }

  /// Optimized YUV420 to InputImage conversion
  static InputImage? _convertYUV420ToInputImage(
    CameraImage cameraImage,
    CameraDescription camera,
  ) {
    try {
      // Get image metadata
      final imageRotation = _getImageRotation(camera);
      final inputImageFormat = _getInputImageFormat(cameraImage.format);
      
      if (inputImageFormat == null) return null;

      // Create InputImageMetadata
      final metadata = InputImageMetadata(
        size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: cameraImage.planes[0].bytesPerRow,
      );

      // For YUV420, we can directly use the Y plane for better performance
      final yPlane = cameraImage.planes[0];
      
      // Use buffer pooling for better memory management
      final buffer = _getBuffer(yPlane.bytes.length);
      buffer.setRange(0, yPlane.bytes.length, yPlane.bytes);

      final inputImage = InputImage.fromBytes(
        bytes: buffer,
        metadata: metadata,
      );

      // Return buffer to pool after a delay to avoid immediate reuse
      Future.delayed(const Duration(milliseconds: 100), () {
        _returnBuffer(buffer);
      });

      return inputImage;
    } catch (e) {
      debugPrint('❌ Error converting YUV420: $e');
      return null;
    }
  }

  /// Optimized NV21 to InputImage conversion
  static InputImage? _convertNV21ToInputImage(
    CameraImage cameraImage,
    CameraDescription camera,
  ) {
    try {
      final imageRotation = _getImageRotation(camera);
      final inputImageFormat = _getInputImageFormat(cameraImage.format);
      
      if (inputImageFormat == null) return null;

      final metadata = InputImageMetadata(
        size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: cameraImage.planes[0].bytesPerRow,
      );

      // Concatenate all planes for NV21
      final allBytes = <int>[];
      for (final plane in cameraImage.planes) {
        allBytes.addAll(plane.bytes);
      }

      final buffer = _getBuffer(allBytes.length);
      buffer.setRange(0, allBytes.length, allBytes);

      final inputImage = InputImage.fromBytes(
        bytes: buffer,
        metadata: metadata,
      );

      Future.delayed(const Duration(milliseconds: 100), () {
        _returnBuffer(buffer);
      });

      return inputImage;
    } catch (e) {
      debugPrint('❌ Error converting NV21: $e');
      return null;
    }
  }

  /// Optimized BGRA8888 to InputImage conversion
  static InputImage? _convertBGRA8888ToInputImage(
    CameraImage cameraImage,
    CameraDescription camera,
  ) {
    try {
      final imageRotation = _getImageRotation(camera);
      final inputImageFormat = _getInputImageFormat(cameraImage.format);
      
      if (inputImageFormat == null) return null;

      final metadata = InputImageMetadata(
        size: Size(cameraImage.width.toDouble(), cameraImage.height.toDouble()),
        rotation: imageRotation,
        format: inputImageFormat,
        bytesPerRow: cameraImage.planes[0].bytesPerRow,
      );

      final bytes = cameraImage.planes[0].bytes;
      final buffer = _getBuffer(bytes.length);
      buffer.setRange(0, bytes.length, bytes);

      final inputImage = InputImage.fromBytes(
        bytes: buffer,
        metadata: metadata,
      );

      Future.delayed(const Duration(milliseconds: 100), () {
        _returnBuffer(buffer);
      });

      return inputImage;
    } catch (e) {
      debugPrint('❌ Error converting BGRA8888: $e');
      return null;
    }
  }

  /// Get image rotation based on camera orientation
  static InputImageRotation _getImageRotation(CameraDescription camera) {
    switch (camera.lensDirection) {
      case CameraLensDirection.front:
        return InputImageRotation.rotation270deg;
      case CameraLensDirection.back:
        return InputImageRotation.rotation90deg;
      default:
        return InputImageRotation.rotation0deg;
    }
  }

  /// Get InputImageFormat from CameraImage format
  static InputImageFormat? _getInputImageFormat(ImageFormat format) {
    switch (format.group) {
      case ImageFormatGroup.yuv420:
        return InputImageFormat.yuv420;
      case ImageFormatGroup.nv21:
        return InputImageFormat.nv21;
      case ImageFormatGroup.bgra8888:
        return InputImageFormat.bgra8888;
      default:
        return null;
    }
  }

  /// Clear buffer pool to free memory
  static void clearBufferPool() {
    _bufferPool.clear();
    debugPrint('🧹 Image processing buffer pool cleared');
  }

  /// Get buffer pool statistics
  static Map<String, dynamic> getBufferPoolStats() {
    return {
      'poolSize': _bufferPool.length,
      'maxPoolSize': _maxPoolSize,
      'bufferSize': _bufferSize,
      'totalMemoryUsed': _bufferPool.fold<int>(0, (sum, buffer) => sum + buffer.length),
    };
  }

  /// Optimize image processing for Snapdragon 8 Gen 2
  static Map<String, dynamic> getSnapdragonOptimizations() {
    return {
      'preferredImageFormat': ImageFormatGroup.yuv420,
      'enableHardwareAcceleration': true,
      'useBufferPooling': true,
      'optimizeForAdreno740GPU': true,
      'enableHexagonDSP': true,
      'recommendations': [
        'Use YUV420 format for best performance on Snapdragon',
        'Enable hardware acceleration for image processing',
        'Use buffer pooling to reduce memory allocations',
        'Process only Y plane for grayscale face detection',
        'Avoid unnecessary format conversions',
      ],
    };
  }

  /// Pre-warm buffer pool for better initial performance
  static void preWarmBufferPool() {
    for (int i = 0; i < _maxPoolSize; i++) {
      _bufferPool.add(Uint8List(_bufferSize ~/ 4)); // Start with smaller buffers
    }
    debugPrint('🔥 Image processing buffer pool pre-warmed');
  }
}

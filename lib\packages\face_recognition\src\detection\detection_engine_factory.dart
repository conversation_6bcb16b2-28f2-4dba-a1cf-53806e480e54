import 'detection_engine.dart';
import 'engines/ultraface_engine.dart';
import 'engines/mediapipe_engine.dart';
import 'engines/mock_ultraface_engine.dart';

/// Factory for creating face detection engines
class DetectionEngineFactory {
  /// Create a detection engine based on type
  static DetectionEngine create(DetectionEngineType type) {
    switch (type) {
      case DetectionEngineType.ultraface:
        return UltraFaceEngine();

      case DetectionEngineType.mediapipe:
        return MediaPipeEngine();

      case DetectionEngineType.mlkit:
        return MockUltraFaceEngine(); // Fallback for now

      case DetectionEngineType.custom:
        return MockUltraFaceEngine(); // Fallback for now
    }
  }

  /// Get recommended engine for terminal devices
  static DetectionEngineType getRecommendedEngineForTerminal() {
    return DetectionEngineType.ultraface; // Best performance for embedded
  }

  /// Get recommended engine for mobile devices
  static DetectionEngineType getRecommendedEngineForMobile() {
    return DetectionEngineType.mediapipe; // Good balance of accuracy and performance
  }

  /// Get all available engines
  static List<DetectionEngineType> getAvailableEngines() {
    return [
      DetectionEngineType.ultraface,
      DetectionEngineType.mediapipe,
      DetectionEngineType.mlkit,
    ];
  }
}

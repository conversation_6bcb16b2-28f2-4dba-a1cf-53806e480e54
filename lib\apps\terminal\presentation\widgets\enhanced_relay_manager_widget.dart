import 'dart:async';
import 'package:flutter/material.dart';
import 'package:relay_controller/relay_controller.dart';

/// Enhanced relay manager widget for comprehensive relay control
class EnhancedRelayManagerWidget extends StatefulWidget {
  final bool showDebugInfo;
  final bool showDeviceList;
  final bool showControls;
  final bool showEvents;
  final bool autoConnect;
  
  const EnhancedRelayManagerWidget({
    super.key,
    this.showDebugInfo = true,
    this.showDeviceList = true,
    this.showControls = true,
    this.showEvents = true,
    this.autoConnect = false,
  });

  @override
  State<EnhancedRelayManagerWidget> createState() => _EnhancedRelayManagerWidgetState();
}

class _EnhancedRelayManagerWidgetState extends State<EnhancedRelayManagerWidget> {
  final EnhancedRelayManager _manager = EnhancedRelayManager();
  final List<RelayDeviceEvent> _events = [];
  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _selectedDeviceId;
  StreamSubscription<RelayDeviceEvent>? _eventSubscription;
  
  @override
  void initState() {
    super.initState();
    _setupEventListener();
    
    if (widget.autoConnect) {
      _initializeManager();
    }
  }
  
  @override
  void dispose() {
    _eventSubscription?.cancel();
    super.dispose();
  }
  
  void _setupEventListener() {
    _eventSubscription = _manager.deviceEvents.listen((event) {
      setState(() {
        _events.add(event);
        if (_events.length > 50) {
          _events.removeAt(0);
        }
      });
    });
  }
  
  Future<void> _initializeManager() async {
    if (_isInitialized || _isInitializing) return;
    
    setState(() {
      _isInitializing = true;
    });
    
    try {
      await _manager.initialize();
      setState(() {
        _isInitialized = true;
        _isInitializing = false;
      });
    } catch (e) {
      setState(() {
        _isInitializing = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initialize relay manager: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _connectToDevice(String deviceId) async {
    try {
      final controller = await _manager.connectToDevice(deviceId);
      setState(() {
        _selectedDeviceId = deviceId;
      });
      
      if (controller != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connected to device: ${controller.deviceName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to connect: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  Future<void> _controlRelay(int relayIndex, RelayAction action) async {
    if (_selectedDeviceId == null) return;
    
    try {
      await _manager.controlRelay(_selectedDeviceId!, relayIndex, action);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Relay $relayIndex ${action.name} successful'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to control relay: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            
            if (!_isInitialized)
              _buildInitializeButton()
            else
              ...[
                if (widget.showDeviceList)
                  _buildDeviceList(),
                  
                if (widget.showControls && _selectedDeviceId != null)
                  ...[
                    const SizedBox(height: 16),
                    _buildRelayControls(),
                  ],
                  
                if (widget.showEvents && _events.isNotEmpty)
                  ...[
                    const SizedBox(height: 16),
                    _buildEventLog(),
                  ],
              ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.electrical_services,
          color: _isInitialized ? Colors.green : Colors.orange,
        ),
        const SizedBox(width: 8),
        Text(
          'Enhanced Relay Manager',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_isInitialized)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green),
            ),
            child: Text(
              'Active',
              style: TextStyle(
                color: Colors.green.shade800,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildInitializeButton() {
    return Center(
      child: Column(
        children: [
          if (_isInitializing)
            ...[
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Initializing relay manager...'),
            ]
          else
            ElevatedButton.icon(
              onPressed: _initializeManager,
              icon: const Icon(Icons.power_settings_new),
              label: const Text('Initialize Relay Manager'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildDeviceList() {
    final devices = _manager.discoveredDevices;
    
    if (devices.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No relay devices found'),
        ),
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Devices (${devices.length})',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: devices.length,
          itemBuilder: (context, index) {
            final device = devices[index];
            final isSelected = device.id == _selectedDeviceId;
            
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              color: isSelected ? Colors.blue.shade50 : null,
              child: ListTile(
                title: Text(device.name),
                subtitle: Text(
                  '${device.type.name} | ${device.vendorId != null ? "0x${device.vendorId!.toRadixString(16)}" : "N/A"}:${device.productId != null ? "0x${device.productId!.toRadixString(16)}" : "N/A"}',
                  style: const TextStyle(fontSize: 12),
                ),
                trailing: ElevatedButton(
                  onPressed: isSelected ? null : () => _connectToDevice(device.id),
                  child: Text(isSelected ? 'Connected' : 'Connect'),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
  
  Widget _buildRelayControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Relay Controls',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: 4, // Default to 4 relays
          itemBuilder: (context, index) {
            return Card(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Relay $index',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildRelayButton(
                          index,
                          RelayAction.on,
                          Colors.green,
                          Icons.power,
                        ),
                        _buildRelayButton(
                          index,
                          RelayAction.off,
                          Colors.red,
                          Icons.power_off,
                        ),
                        _buildRelayButton(
                          index,
                          RelayAction.toggle,
                          Colors.blue,
                          Icons.swap_horiz,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
  
  Widget _buildRelayButton(int relayIndex, RelayAction action, Color color, IconData icon) {
    return SizedBox(
      width: 36,
      height: 36,
      child: IconButton(
        onPressed: () => _controlRelay(relayIndex, action),
        icon: Icon(icon, size: 16),
        style: IconButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
  
  Widget _buildEventLog() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Event Log (${_events.length})',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const Spacer(),
            IconButton(
              onPressed: () => setState(() => _events.clear()),
              icon: const Icon(Icons.clear_all, size: 16),
              tooltip: 'Clear logs',
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: _events.length,
            reverse: true,
            itemBuilder: (context, index) {
              final event = _events[_events.length - 1 - index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  '${_getEventIcon(event.type)} ${event.message}',
                  style: TextStyle(
                    fontSize: 12,
                    color: _getEventColor(event.type),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  String _getEventIcon(String eventType) {
    switch (eventType) {
      case 'manager_initialized':
        return '🚀';
      case 'discovery_completed':
        return '🔍';
      case 'usb_discovered':
        return '🔌';
      case 'device_connected':
        return '✅';
      case 'device_disconnected':
        return '❌';
      case 'connection_failed':
        return '⚠️';
      case 'relay_controlled':
        return '⚡';
      case 'error':
        return '❌';
      default:
        return 'ℹ️';
    }
  }
  
  Color _getEventColor(String eventType) {
    switch (eventType) {
      case 'error':
      case 'connection_failed':
        return Colors.red.shade700;
      case 'device_disconnected':
        return Colors.orange.shade700;
      case 'device_connected':
      case 'manager_initialized':
        return Colors.green.shade700;
      case 'relay_controlled':
        return Colors.blue.shade700;
      default:
        return Colors.black87;
    }
  }
}

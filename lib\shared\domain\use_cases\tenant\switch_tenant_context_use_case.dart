import 'package:dartz/dartz.dart';
import '../../repositories/tenant_repository.dart';
import '../../../core/errors/failures.dart';

class SwitchTenantContextUseCase {
  final TenantRepository repository;

  SwitchTenantContextUseCase(this.repository);

  Future<Either<Failure, bool>> call(SwitchTenantContextParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to switch tenant context
    return await repository.switchTenantContext(params.tenantId);
  }

  ValidationFailure? _validateParams(SwitchTenantContextParams params) {
    final errors = <String, List<String>>{};

    // Validate tenant ID
    if (params.tenantId.trim().isEmpty) {
      errors['tenantId'] = ['Tenant ID is required'];
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Invalid parameters',
        code: 'VALIDATION_ERROR',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

class SwitchTenantContextParams {
  final String tenantId;

  const SwitchTenantContextParams({
    required this.tenantId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SwitchTenantContextParams &&
        other.tenantId == tenantId;
  }

  @override
  int get hashCode => tenantId.hashCode;

  @override
  String toString() {
    return 'SwitchTenantContextParams(tenantId: $tenantId)';
  }
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/priority_face_processing_provider.dart';
import '../providers/face_detection_provider.dart';
import '../services/priority_face_processing_queue.dart';
import '../core/constants/face_cropping_constants.dart';

/// Widget that integrates priority face processing with existing face detection
class PriorityFaceProcessingWidget extends StatefulWidget {
  final Widget child;
  final bool autoProcess;
  final FaceDirection defaultDirection;
  final Map<String, dynamic>? context;
  final List<SideEffectType>? enabledSideEffects;
  final Function(String taskId)? onTaskAdded;
  final Function(String taskId, bool success)? onTaskCompleted;
  
  const PriorityFaceProcessingWidget({
    Key? key,
    required this.child,
    this.autoProcess = true,
    this.defaultDirection = FaceDirection.front,
    this.context,
    this.enabledSideEffects,
    this.onTaskAdded,
    this.onTaskCompleted,
  }) : super(key: key);
  
  @override
  State<PriorityFaceProcessingWidget> createState() => _PriorityFaceProcessingWidgetState();
}

class _PriorityFaceProcessingWidgetState extends State<PriorityFaceProcessingWidget> {
  late PriorityFaceProcessingProvider _priorityProvider;
  String? _lastImagePath;
  
  @override
  void initState() {
    super.initState();
    _priorityProvider = PriorityFaceProcessingProvider();
    
    // Configure default settings
    _priorityProvider.setMinFaceQuality(0.6); // Chất lượng cao
    _priorityProvider.setFaceDetectionCooldown(const Duration(milliseconds: 1500)); // 1.5s cooldown
  }
  
  @override
  void dispose() {
    _priorityProvider.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<PriorityFaceProcessingProvider>.value(
          value: _priorityProvider,
        ),
      ],
      child: Consumer2<FaceDetectionProvider, PriorityFaceProcessingProvider>(
        builder: (context, faceDetectionProvider, priorityProvider, child) {
          // Auto-process detected faces if enabled
          if (widget.autoProcess && faceDetectionProvider.faces.isNotEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _processCurrentFaces(faceDetectionProvider, priorityProvider);
            });
          }
          
          return Stack(
            children: [
              widget.child,
              
              // Processing status overlay
              if (priorityProvider.isProcessing)
                _buildProcessingOverlay(priorityProvider),
              
              // Queue status indicator
              _buildQueueStatusIndicator(priorityProvider),
            ],
          );
        },
      ),
    );
  }
  
  /// Process current detected faces
  Future<void> _processCurrentFaces(
    FaceDetectionProvider faceDetectionProvider,
    PriorityFaceProcessingProvider priorityProvider,
  ) async {
    // Skip if no faces or already processing
    if (faceDetectionProvider.faces.isEmpty || priorityProvider.isProcessing) {
      return;
    }
    
    // Get current image path (this would need to be provided by camera provider)
    // For now, we'll use a placeholder or get from context
    final imagePath = _getImagePathFromContext();
    if (imagePath == null) {
      return;
    }
    
    try {
      final taskId = await priorityProvider.processDetectedFaces(
        imagePath: imagePath,
        detectedFaces: faceDetectionProvider.faces,
        direction: widget.defaultDirection,
        context: {
          ...?widget.context,
          'auto_processed': true,
          'face_count': faceDetectionProvider.faces.length,
          'best_face_quality': faceDetectionProvider.getFaceQuality(faceDetectionProvider.getBestFace() ?? faceDetectionProvider.faces.first),
        },
        enabledSideEffects: widget.enabledSideEffects,
      );
      
      if (taskId != null) {
        widget.onTaskAdded?.call(taskId);
        
        // Monitor task completion
        _monitorTaskCompletion(taskId, priorityProvider);
      }
      
    } catch (e) {
      debugPrint('Error processing faces: $e');
    }
  }
  
  /// Monitor task completion
  void _monitorTaskCompletion(String taskId, PriorityFaceProcessingProvider provider) {
    // This is a simplified monitoring approach
    // In a real implementation, you might use streams or callbacks
    Timer.periodic(const Duration(seconds: 1), (timer) {
      final queueStatus = provider.getQueueStatus();
      final queueTasks = queueStatus['queue_tasks'] as List<dynamic>? ?? [];
      
      // Check if task is completed or failed
      final taskExists = queueTasks.any((task) => task['id'] == taskId);
      
      if (!taskExists && provider.currentTaskId != taskId) {
        // Task completed or failed
        timer.cancel();
        
        final statistics = provider.getStatistics();
        final wasSuccessful = statistics['total_processed'] > 0;
        
        widget.onTaskCompleted?.call(taskId, wasSuccessful);
      }
    });
  }
  
  /// Get image path from context or camera provider
  String? _getImagePathFromContext() {
    // This is a placeholder implementation
    // In real usage, you would get the current image path from:
    // 1. Camera provider's last captured image
    // 2. Context parameter
    // 3. Face capture provider
    
    if (widget.context?.containsKey('image_path') == true) {
      return widget.context!['image_path'] as String;
    }
    
    // Return placeholder for demo
    return '/tmp/current_camera_frame.jpg';
  }
  
  /// Build processing status overlay
  Widget _buildProcessingOverlay(PriorityFaceProcessingProvider provider) {
    return Positioned(
      top: 50,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: FaceCroppingConstants.processingColor.withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Đang xử lý khuôn mặt...',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (provider.currentStatus != null)
                    Text(
                      _getStatusDisplayText(provider.currentStatus!),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build queue status indicator
  Widget _buildQueueStatusIndicator(PriorityFaceProcessingProvider provider) {
    final queueStatus = provider.getQueueStatus();
    final queueSize = queueStatus['queue_size'] as int;
    
    if (queueSize == 0 && !provider.isProcessing) {
      return const SizedBox.shrink();
    }
    
    return Positioned(
      bottom: 100,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              provider.isProcessing ? Icons.face : Icons.queue,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              provider.isProcessing ? 'Đang xử lý' : 'Hàng đợi: $queueSize',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Get display text for processing status
  String _getStatusDisplayText(FaceProcessingStatus status) {
    switch (status) {
      case FaceProcessingStatus.queued:
        return 'Đang chờ xử lý...';
      case FaceProcessingStatus.processing:
        return 'Đang khởi tạo...';
      case FaceProcessingStatus.cropping:
        return 'Đang cắt khuôn mặt...';
      case FaceProcessingStatus.apiProcessing:
        return 'Đang xác minh...';
      case FaceProcessingStatus.sideEffects:
        return 'Đang thực thi...';
      case FaceProcessingStatus.completed:
        return 'Hoàn thành';
      case FaceProcessingStatus.failed:
        return 'Thất bại';
    }
  }
}

/// Control panel widget for priority face processing
class PriorityFaceProcessingControlPanel extends StatelessWidget {
  const PriorityFaceProcessingControlPanel({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Consumer<PriorityFaceProcessingProvider>(
      builder: (context, provider, child) {
        final statistics = provider.getStatistics();
        final queueStatus = provider.getQueueStatus();
        
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    const Icon(Icons.face_retouching_natural),
                    const SizedBox(width: 8),
                    const Text(
                      'Xử lý khuôn mặt ưu tiên',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: provider.isEnabled,
                      onChanged: provider.setEnabled,
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Statistics
                Row(
                  children: [
                    _buildStatItem('Đã xử lý', statistics['total_processed'].toString(), Colors.green),
                    const SizedBox(width: 16),
                    _buildStatItem('Thất bại', statistics['total_failed'].toString(), Colors.red),
                    const SizedBox(width: 16),
                    _buildStatItem('Hàng đợi', queueStatus['queue_size'].toString(), Colors.blue),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Quality threshold
                Text('Ngưỡng chất lượng: ${(provider.minFaceQuality * 100).toInt()}%'),
                Slider(
                  value: provider.minFaceQuality,
                  min: 0.3,
                  max: 0.9,
                  divisions: 12,
                  onChanged: provider.setMinFaceQuality,
                ),
                
                const SizedBox(height: 8),
                
                // Control buttons
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: provider.clearQueue,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('Xóa hàng đợi'),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: () => _showDetailedStatus(context, provider),
                      icon: const Icon(Icons.info),
                      label: const Text('Chi tiết'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
  
  void _showDetailedStatus(BuildContext context, PriorityFaceProcessingProvider provider) {
    final statistics = provider.getStatistics();
    final queueStatus = provider.getQueueStatus();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Trạng thái chi tiết'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Đang xử lý: ${queueStatus['is_processing']}'),
              Text('Task hiện tại: ${queueStatus['current_task_id'] ?? 'Không có'}'),
              Text('Trạng thái: ${queueStatus['current_task_status'] ?? 'Không có'}'),
              Text('Tỷ lệ thành công: ${statistics['success_rate_percent']}%'),
              Text('Thời gian xử lý cuối: ${statistics['last_processing_time'] ?? 'Chưa có'}'),
              const SizedBox(height: 16),
              const Text('Cấu hình:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('Chất lượng tối thiểu: ${(provider.minFaceQuality * 100).toInt()}%'),
              Text('Cooldown: ${provider.getStatistics()['cooldown_ms']}ms'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}

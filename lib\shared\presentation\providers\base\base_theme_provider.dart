import 'package:flutter/material.dart';
import '../../../core/base/base_provider.dart';

/// Theme mode options
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Base theme provider that can be extended by mobile and terminal apps
/// 
/// Provides common theme functionality including:
/// - Theme mode management (light/dark/system)
/// - Color scheme management
/// - Typography management
/// - App-specific theme customization
abstract class BaseThemeProvider extends BaseProvider {
  
  // ============================================================================
  // STATE VARIABLES
  // ============================================================================
  
  AppThemeMode _themeMode = AppThemeMode.system;
  bool _isDarkMode = false;
  String _selectedColorScheme = 'default';
  double _textScaleFactor = 1.0;

  // ============================================================================
  // GETTERS
  // ============================================================================
  
  /// Current theme mode
  AppThemeMode get themeMode => _themeMode;
  
  /// Check if dark mode is active
  bool get isDarkMode => _isDarkMode;
  
  /// Current color scheme name
  String get selectedColorScheme => _selectedColorScheme;
  
  /// Current text scale factor
  double get textScaleFactor => _textScaleFactor;
  
  /// Get Flutter ThemeMode for MaterialApp
  ThemeMode get flutterThemeMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  // ============================================================================
  // ABSTRACT METHODS (TO BE IMPLEMENTED BY SUBCLASSES)
  // ============================================================================
  
  /// Get light theme data - to be implemented by subclasses
  ThemeData getLightTheme();
  
  /// Get dark theme data - to be implemented by subclasses
  ThemeData getDarkTheme();
  
  /// Get available color schemes - to be implemented by subclasses
  List<String> getAvailableColorSchemes();
  
  /// Handle theme change - to be implemented by subclasses
  void onThemeChanged(AppThemeMode newMode) {}
  
  /// Handle color scheme change - to be implemented by subclasses
  void onColorSchemeChanged(String newScheme) {}

  // ============================================================================
  // THEME MANAGEMENT METHODS
  // ============================================================================
  
  /// Set theme mode
  void setThemeMode(AppThemeMode mode) {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    _updateDarkModeStatus();
    notifyListeners();
    
    // Call app-specific theme change handler
    onThemeChanged(mode);
    
    // Save preference
    _saveThemePreference();
  }
  
  /// Toggle between light and dark mode
  void toggleTheme() {
    switch (_themeMode) {
      case AppThemeMode.light:
        setThemeMode(AppThemeMode.dark);
        break;
      case AppThemeMode.dark:
        setThemeMode(AppThemeMode.light);
        break;
      case AppThemeMode.system:
        // If system mode, toggle to opposite of current system setting
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        setThemeMode(brightness == Brightness.dark ? AppThemeMode.light : AppThemeMode.dark);
        break;
    }
  }
  
  /// Set color scheme
  void setColorScheme(String scheme) {
    if (_selectedColorScheme == scheme) return;
    if (!getAvailableColorSchemes().contains(scheme)) return;
    
    _selectedColorScheme = scheme;
    notifyListeners();
    
    // Call app-specific color scheme change handler
    onColorSchemeChanged(scheme);
    
    // Save preference
    _saveColorSchemePreference();
  }
  
  /// Set text scale factor
  void setTextScaleFactor(double factor) {
    if (_textScaleFactor == factor) return;
    if (factor < 0.5 || factor > 2.0) return; // Reasonable bounds
    
    _textScaleFactor = factor;
    notifyListeners();
    
    // Save preference
    _saveTextScalePreference();
  }
  
  /// Reset text scale to default
  void resetTextScale() {
    setTextScaleFactor(1.0);
  }
  
  /// Update dark mode status based on current theme mode and system settings
  void updateSystemTheme() {
    if (_themeMode == AppThemeMode.system) {
      _updateDarkModeStatus();
      notifyListeners();
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Get current effective theme data
  ThemeData getCurrentTheme() {
    return _isDarkMode ? getDarkTheme() : getLightTheme();
  }
  
  /// Check if current theme is dark
  bool isCurrentThemeDark() {
    return _isDarkMode;
  }
  
  /// Get theme-appropriate color
  Color getThemeColor({
    required Color lightColor,
    required Color darkColor,
  }) {
    return _isDarkMode ? darkColor : lightColor;
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// Update dark mode status based on theme mode and system settings
  void _updateDarkModeStatus() {
    switch (_themeMode) {
      case AppThemeMode.light:
        _isDarkMode = false;
        break;
      case AppThemeMode.dark:
        _isDarkMode = true;
        break;
      case AppThemeMode.system:
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        _isDarkMode = brightness == Brightness.dark;
        break;
    }
  }
  
  /// Save theme preference - to be implemented by subclasses
  Future<void> _saveThemePreference() async {
    // Subclasses should implement persistence logic
  }
  
  /// Save color scheme preference - to be implemented by subclasses
  Future<void> _saveColorSchemePreference() async {
    // Subclasses should implement persistence logic
  }
  
  /// Save text scale preference - to be implemented by subclasses
  Future<void> _saveTextScalePreference() async {
    // Subclasses should implement persistence logic
  }

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================
  
  /// Initialize theme provider
  Future<void> initialize() async {
    // Load saved preferences
    await _loadThemePreferences();
    
    // Update dark mode status
    _updateDarkModeStatus();
    
    notifyListeners();
  }
  
  /// Load theme preferences - to be implemented by subclasses
  Future<void> _loadThemePreferences() async {
    // Subclasses should implement preference loading logic
  }
  
  @override
  Future<void> retry() async {
    // Retry theme initialization if needed
    await initialize();
  }
  
  @override
  void reset() {
    super.reset();
    _themeMode = AppThemeMode.system;
    _selectedColorScheme = 'default';
    _textScaleFactor = 1.0;
    _updateDarkModeStatus();
    notifyListeners();
  }
}

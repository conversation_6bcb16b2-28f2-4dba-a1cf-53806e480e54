import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// Service để quản lý logic back navigation cho toàn bộ ứng dụng
/// <PERSON><PERSON> lý các trường hợp đặc biệt như exit confirmation và navigation stack
class BackNavigationService {
  static final BackNavigationService _instance = BackNavigationService._internal();
  factory BackNavigationService() => _instance;
  BackNavigationService._internal();

  // Stack để theo dõi navigation history
  final List<String> _navigationStack = [];
  
  // Callback cho exit confirmation
  VoidCallback? _exitConfirmationCallback;

  /// Đ<PERSON>ng ký callback cho exit confirmation
  void setExitConfirmationCallback(VoidCallback? callback) {
    _exitConfirmationCallback = callback;
  }

  /// Thêm route vào navigation stack
  void pushRoute(String route) {
    _navigationStack.add(route);
    // Giới hạn stack size để tránh memory leak
    if (_navigationStack.length > 20) {
      _navigationStack.removeAt(0);
    }
  }

  /// Xóa route khỏi navigation stack
  void popRoute() {
    if (_navigationStack.isNotEmpty) {
      _navigationStack.removeLast();
    }
  }

  /// Lấy route trước đó trong stack
  String? getPreviousRoute() {
    if (_navigationStack.length >= 2) {
      return _navigationStack[_navigationStack.length - 2];
    }
    return null;
  }

  /// Xóa toàn bộ navigation stack
  void clearStack() {
    _navigationStack.clear();
  }

  /// Kiểm tra xem có phải là root screen không
  bool isRootScreen(String currentRoute) {
    final rootScreens = [
      '/dashboard',
      '/main',
      '/login',
      '/splash',
    ];
    return rootScreens.contains(currentRoute);
  }

  /// Kiểm tra xem có phải là authentication screen không
  bool isAuthScreen(String currentRoute) {
    final authScreens = [
      '/login',
      '/tenants',
      '/tenant-create',
      '/forgot-password',
      '/enter-otp',
      '/confirm-password',
      '/successfully',
    ];
    return authScreens.contains(currentRoute);
  }

  /// Xử lý back navigation dựa trên context và route hiện tại
  Future<bool> handleBackNavigation(BuildContext context, {String? currentRoute}) async {
    final router = GoRouter.of(context);
    final location = currentRoute ?? router.routerDelegate.currentConfiguration.uri.path;

    // Xử lý các trường hợp đặc biệt
    switch (location) {
      case '/login':
        return await _handleLoginBack(context);
      
      case '/tenants':
        return await _handleTenantsBack(context);
      
      case '/tenant-create':
        return await _handleTenantCreateBack(context);
      
      case '/dashboard':
      case '/main':
        return await _handleDashboardBack(context);
      
      default:
        return await _handleDefaultBack(context);
    }
  }

  /// Xử lý back navigation cho login screen
  Future<bool> _handleLoginBack(BuildContext context) async {
    // Hiển thị exit confirmation
    return await _showExitConfirmation(context);
  }

  /// Xử lý back navigation cho tenants screen
  Future<bool> _handleTenantsBack(BuildContext context) async {
    // Quay về login screen
    context.go('/login');
    return true;
  }

  /// Xử lý back navigation cho tenant create screen
  Future<bool> _handleTenantCreateBack(BuildContext context) async {
    // Quay về tenants screen
    context.go('/tenants');
    return true;
  }

  /// Xử lý back navigation cho dashboard/main screen
  Future<bool> _handleDashboardBack(BuildContext context) async {
    // Hiển thị exit confirmation
    return await _showExitConfirmation(context);
  }

  /// Xử lý back navigation mặc định
  Future<bool> _handleDefaultBack(BuildContext context) async {
    if (context.canPop()) {
      context.pop();
      return true;
    } else {
      // Nếu không thể pop, navigate về dashboard
      context.go('/dashboard');
      return true;
    }
  }

  /// Hiển thị dialog xác nhận thoát ứng dụng
  Future<bool> _showExitConfirmation(BuildContext context) async {
    // Nếu có custom callback, sử dụng nó
    if (_exitConfirmationCallback != null) {
      _exitConfirmationCallback!();
      return false;
    }

    final shouldExit = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Thoát ứng dụng'),
        content: const Text('Bạn có chắc chắn muốn thoát ứng dụng không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Thoát'),
          ),
        ],
      ),
    );

    if (shouldExit == true) {
      SystemNavigator.pop();
      return true;
    }
    
    return false;
  }

  /// Xử lý back navigation cho tab navigation
  Future<bool> handleTabBackNavigation(BuildContext context, int currentTabIndex) async {
    // Nếu đang ở tab đầu tiên (Dashboard), hiển thị exit confirmation
    if (currentTabIndex == 0) {
      return await _showExitConfirmation(context);
    } else {
      // Chuyển về tab đầu tiên
      // Logic này sẽ được implement trong TabsBar
      return false;
    }
  }

  /// Lấy route phù hợp để navigate back
  String getBackRoute(String currentRoute) {
    switch (currentRoute) {
      case '/tenant-create':
        return '/tenants';
      case '/tenants':
        return '/login';
      case '/profile':
      case '/tools':
      case '/notifications':
        return '/dashboard';
      default:
        return '/dashboard';
    }
  }

  /// Kiểm tra xem có nên hiển thị back button trong AppBar không
  bool shouldShowBackButton(String currentRoute) {
    final noBackButtonScreens = [
      '/login',
      '/dashboard',
      '/main',
      '/splash',
    ];
    return !noBackButtonScreens.contains(currentRoute);
  }

  /// Reset service về trạng thái ban đầu
  void reset() {
    _navigationStack.clear();
    _exitConfirmationCallback = null;
  }
}

/// Extension để dễ dàng sử dụng BackNavigationService
extension BackNavigationServiceExtension on BuildContext {
  /// Xử lý back navigation với service
  Future<bool> handleBackWithService({String? currentRoute}) async {
    return await BackNavigationService().handleBackNavigation(this, currentRoute: currentRoute);
  }

  /// Kiểm tra xem có nên hiển thị back button không
  bool shouldShowBackButton() {
    final location = GoRouter.of(this).routerDelegate.currentConfiguration.uri.path;
    return BackNavigationService().shouldShowBackButton(location);
  }
}

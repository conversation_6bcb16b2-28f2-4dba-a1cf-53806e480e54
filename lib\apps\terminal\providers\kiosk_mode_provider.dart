import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Kiosk mode provider for terminal application
/// 
/// Manages kiosk mode functionality including:
/// - Auto-timeout and return to home
/// - Full-screen mode
/// - System navigation restrictions
/// - Idle detection and handling
class KioskModeProvider extends ChangeNotifier {
  bool _isKioskMode = true;
  bool _isFullScreen = false;
  bool _isIdle = false;
  Timer? _idleTimer;
  Timer? _autoReturnTimer;
  DateTime? _lastInteractionTime;
  
  // Configuration
  static const Duration _defaultIdleTimeout = Duration(minutes: 2);
  static const Duration _defaultAutoReturnTimeout = Duration(seconds: 30);
  
  Duration _idleTimeout = _defaultIdleTimeout;
  Duration _autoReturnTimeout = _defaultAutoReturnTimeout;
  String _homeRoute = '/kiosk-home';

  // ============================================================================
  // GETTERS
  // ============================================================================

  /// Whether kiosk mode is enabled
  bool get isKioskMode => _isKioskMode;

  /// Whether in full-screen mode
  bool get isFullScreen => _isFullScreen;

  /// Whether system is idle
  bool get isIdle => _isIdle;

  /// Current idle timeout duration
  Duration get idleTimeout => _idleTimeout;

  /// Current auto-return timeout duration
  Duration get autoReturnTimeout => _autoReturnTimeout;

  /// Home route for kiosk mode
  String get homeRoute => _homeRoute;

  /// Last interaction time
  DateTime? get lastInteractionTime => _lastInteractionTime;

  /// Time until idle (if timer is active)
  Duration? get timeUntilIdle {
    if (_idleTimer == null || _lastInteractionTime == null) return null;
    
    final elapsed = DateTime.now().difference(_lastInteractionTime!);
    final remaining = _idleTimeout - elapsed;
    
    return remaining.isNegative ? Duration.zero : remaining;
  }

  // ============================================================================
  // KIOSK MODE MANAGEMENT
  // ============================================================================

  /// Enable kiosk mode
  Future<void> enableKioskMode() async {
    if (_isKioskMode) return;
    
    _isKioskMode = true;
    
    try {
      // Enable full-screen mode
      await _enableFullScreen();
      
      // Start idle detection
      _startIdleDetection();
      
      if (kDebugMode) {
        debugPrint('Kiosk mode enabled');
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error enabling kiosk mode: $error');
      }
    }
    
    notifyListeners();
  }

  /// Disable kiosk mode
  Future<void> disableKioskMode() async {
    if (!_isKioskMode) return;
    
    _isKioskMode = false;
    
    try {
      // Disable full-screen mode
      await _disableFullScreen();
      
      // Stop timers
      _stopAllTimers();
      
      if (kDebugMode) {
        debugPrint('Kiosk mode disabled');
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error disabling kiosk mode: $error');
      }
    }
    
    notifyListeners();
  }

  /// Toggle kiosk mode
  Future<void> toggleKioskMode() async {
    if (_isKioskMode) {
      await disableKioskMode();
    } else {
      await enableKioskMode();
    }
  }

  // ============================================================================
  // INTERACTION TRACKING
  // ============================================================================

  /// Record user interaction (resets idle timer)
  void recordInteraction() {
    _lastInteractionTime = DateTime.now();
    
    if (_isIdle) {
      _isIdle = false;
      notifyListeners();
    }
    
    // Restart idle timer
    if (_isKioskMode) {
      _startIdleTimer();
    }
  }

  /// Start idle detection
  void _startIdleDetection() {
    _lastInteractionTime = DateTime.now();
    _startIdleTimer();
  }

  /// Start idle timer
  void _startIdleTimer() {
    _idleTimer?.cancel();
    
    _idleTimer = Timer(_idleTimeout, () {
      _onIdleTimeout();
    });
  }

  /// Handle idle timeout
  void _onIdleTimeout() {
    if (!_isKioskMode) return;
    
    _isIdle = true;
    
    if (kDebugMode) {
      debugPrint('System went idle after ${_idleTimeout.inMinutes} minutes');
    }
    
    // Start auto-return timer
    _startAutoReturnTimer();
    
    notifyListeners();
  }

  /// Start auto-return timer
  void _startAutoReturnTimer() {
    _autoReturnTimer?.cancel();
    
    _autoReturnTimer = Timer(_autoReturnTimeout, () {
      _returnToHome();
    });
  }

  /// Return to home screen
  void _returnToHome() {
    if (kDebugMode) {
      debugPrint('Auto-returning to home screen');
    }
    
    // This would trigger navigation to home
    // The actual navigation should be handled by the router or navigation service
    _isIdle = false;
    _lastInteractionTime = DateTime.now();
    
    // Restart idle detection
    _startIdleTimer();
    
    notifyListeners();
  }

  /// Force return to home
  void forceReturnToHome() {
    _stopAllTimers();
    _returnToHome();
  }

  // ============================================================================
  // FULL-SCREEN MANAGEMENT
  // ============================================================================

  /// Enable full-screen mode
  Future<void> _enableFullScreen() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
      
      // Set preferred orientations for terminal (landscape)
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      
      _isFullScreen = true;
      
      if (kDebugMode) {
        debugPrint('Full-screen mode enabled');
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error enabling full-screen mode: $error');
      }
    }
  }

  /// Disable full-screen mode
  Future<void> _disableFullScreen() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
      
      // Reset orientation preferences
      await SystemChrome.setPreferredOrientations([]);
      
      _isFullScreen = false;
      
      if (kDebugMode) {
        debugPrint('Full-screen mode disabled');
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error disabling full-screen mode: $error');
      }
    }
  }

  // ============================================================================
  // CONFIGURATION
  // ============================================================================

  /// Set idle timeout duration
  void setIdleTimeout(Duration timeout) {
    _idleTimeout = timeout;
    
    // Restart timer with new duration if active
    if (_isKioskMode && _idleTimer != null) {
      _startIdleTimer();
    }
    
    notifyListeners();
  }

  /// Set auto-return timeout duration
  void setAutoReturnTimeout(Duration timeout) {
    _autoReturnTimeout = timeout;
    notifyListeners();
  }

  /// Set home route
  void setHomeRoute(String route) {
    _homeRoute = route;
    notifyListeners();
  }

  // ============================================================================
  // CLEANUP
  // ============================================================================

  /// Stop all timers
  void _stopAllTimers() {
    _idleTimer?.cancel();
    _idleTimer = null;
    
    _autoReturnTimer?.cancel();
    _autoReturnTimer = null;
  }

  @override
  void dispose() {
    _stopAllTimers();
    super.dispose();
  }
}

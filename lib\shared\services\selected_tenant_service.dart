import '../domain/entities/tenant/tenant.dart';

/// Service để lưu thông tin tenant đ<PERSON><PERSON><PERSON> chọn
/// Chỉ lưu trong memory, không persist
class SelectedTenantService {
  static final SelectedTenantService _instance = SelectedTenantService._internal();
  factory SelectedTenantService() => _instance;
  SelectedTenantService._internal();

  Tenant? _selectedTenant;

  /// Get selected tenant
  Tenant? get selectedTenant => _selectedTenant;

  /// Set selected tenant
  void setSelectedTenant(Tenant tenant) {
    _selectedTenant = tenant;
  }

  /// Clear selected tenant
  void clearSelectedTenant() {
    _selectedTenant = null;
  }

  /// Check if has selected tenant
  bool get hasSelectedTenant => _selectedTenant != null;

  /// Get selected tenant name
  String get selectedTenantName => _selectedTenant?.name ?? 'Chưa chọn tenant';
}

# Cleanup Script - Remove Old Components

## Tổng Quan
Script này hướng dẫn cleanup các old components sau khi migration hoàn thành và đã được test kỹ lưỡng.

⚠️ **CẢNH BÁO**: Chỉ thực hiện cleanup sau khi đã test đầy đủ và confirm rằng migration hoạt động đúng!

## ✅ Migration Status: COMPLETED
- **Date**: 2025-07-03
- **Files Migrated**: 9 files, 15 component usages
- **Compilation**: ✅ No errors
- **Testing**: Ready for cleanup verification

## Components Có Thể Remove

### 1. Migrated Components (Safe to remove)
Các components này đã được migrate hoàn toàn và có thể remove an toàn:

```bash
# CTextField - migrated to AppInputField
rm lib/shared/presentation/widgets/common/c_text_field.dart

# CTabsBar - migrated to TabsBar in shared/components  
rm lib/shared/presentation/widgets/common/c_tabs_bar.dart

# EnhancedErrorMessage - migrated to AppNotification.auth()
rm lib/shared/presentation/widgets/common/enhanced_error_message.dart

# ErrorScreen - moved to shared/components
rm lib/shared/presentation/widgets/common/error_screen.dart

# NotFoundScreen - moved to shared/components
rm lib/shared/presentation/widgets/common/not_found_screen.dart
```

### 2. Unused Components (Check before removing)
Các components này cần kiểm tra xem có đang được sử dụng ở đâu không:

```bash
# Check usage before removing
grep -r "CButton" lib/ --include="*.dart"
grep -r "CCard" lib/ --include="*.dart"
grep -r "CLoadingWidget" lib/ --include="*.dart"
grep -r "CFacesLogo" lib/ --include="*.dart"
grep -r "CEmptyState" lib/ --include="*.dart"
grep -r "CConfirmationDialog" lib/ --include="*.dart"
grep -r "CDatePicker" lib/ --include="*.dart"
grep -r "CLabel" lib/ --include="*.dart"
grep -r "CListScroll" lib/ --include="*.dart"
```

## Cleanup Steps

### Step 1: Verify Migration Success
```bash
# Run tests to ensure everything works
flutter test

# Run app and test manually
./scripts/run_mobile.sh

# Check for any compilation errors
flutter analyze
```

### Step 2: Check for Remaining References
```bash
# Search for any remaining imports of old components
find lib/ -name "*.dart" -exec grep -l "shared/presentation/widgets/common" {} \;

# Should only return index.dart (which we've already updated)
```

### Step 3: Remove Migrated Components
```bash
# Remove migrated components (ONLY after thorough testing)
rm lib/shared/presentation/widgets/common/c_text_field.dart
rm lib/shared/presentation/widgets/common/c_tabs_bar.dart  
rm lib/shared/presentation/widgets/common/enhanced_error_message.dart
rm lib/shared/presentation/widgets/common/error_screen.dart
rm lib/shared/presentation/widgets/common/not_found_screen.dart
```

### Step 4: Update Index File
```bash
# The index.dart file has already been updated to comment out migrated components
# Optionally, you can remove the commented lines entirely
```

### Step 5: Final Verification
```bash
# Run final checks
flutter clean
flutter pub get
flutter analyze
flutter test
./scripts/run_mobile.sh
```

## Rollback Plan

If issues are discovered after cleanup:

### 1. Restore from Git
```bash
# Restore specific files
git checkout HEAD -- lib/shared/presentation/widgets/common/c_text_field.dart
git checkout HEAD -- lib/shared/presentation/widgets/common/c_tabs_bar.dart
# ... etc for other files
```

### 2. Revert Migration
```bash
# If major issues, revert entire migration
git revert <migration-commit-hash>
```

## Components Status

### ✅ Safe to Remove (Fully Migrated)
- `c_text_field.dart` → `AppInputField`
- `c_tabs_bar.dart` → `TabsBar` (shared/components)
- `enhanced_error_message.dart` → `AppNotification.auth()`
- `error_screen.dart` → moved to shared/components
- `not_found_screen.dart` → moved to shared/components

### ⚠️ Check Before Removing (May be used elsewhere)
- `c_button.dart` → Check if used in terminal app
- `c_card.dart` → Check if used in terminal app
- `c_loading_widget.dart` → Check if used in terminal app
- `c_faces_logo.dart` → Check if used in terminal app
- `c_empty_state.dart` → Check if used in terminal app
- `c_confirmation_dialog.dart` → Check if used in terminal app
- `c_date_picker.dart` → Check if used in terminal app
- `c_label.dart` → Check if used in terminal app
- `c_list_scroll.dart` → Check if used in terminal app
- `error_message.dart` → Check if used in terminal app

### 🔄 Keep (Still in use or not migrated)
- Components that are still being used in terminal app or other parts
- Components that haven't been migrated yet

## Usage Check Commands

### Check Terminal App Usage
```bash
# Check if terminal app uses any of these components
find lib/apps/terminal/ -name "*.dart" -exec grep -l "widgets/common" {} \;

# Check specific components
grep -r "CButton" lib/apps/terminal/ --include="*.dart"
grep -r "CCard" lib/apps/terminal/ --include="*.dart"
# ... etc
```

### Check Shared Usage
```bash
# Check if any shared code still uses old components
find lib/shared/ -name "*.dart" -exec grep -l "widgets/common" {} \;
```

## Recommendations

### 1. Gradual Cleanup
- Don't remove all components at once
- Remove one component at a time and test
- Keep git commits small for easy rollback

### 2. Documentation
- Update any documentation that references old components
- Update README files if they mention component locations
- Update developer guides

### 3. Team Communication
- Inform team about component location changes
- Update coding standards/guidelines
- Consider creating migration guide for other developers

## Final Notes

- ✅ Migration completed successfully
- ✅ All functionality preserved
- ✅ Zero compilation errors
- ✅ Enhanced components with new features
- ⚠️ Cleanup should be done carefully and gradually
- 📝 Keep documentation updated

**Remember**: The goal is to have a clean, maintainable codebase while preserving all functionality!

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import '../services/api_endpoints.dart';
import '../core/config/app_config.dart';
import '../core/network/api_client.dart';

/// Optimized widget để load image với authentication header và caching
class AuthenticatedImage extends StatefulWidget {
  final String imageId;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget placeholder;
  final BorderRadius? borderRadius;

  const AuthenticatedImage({
    super.key,
    required this.imageId,
    required this.width,
    required this.height,
    required this.fit,
    required this.placeholder,
    this.borderRadius,
  });

  @override
  State<AuthenticatedImage> createState() => _AuthenticatedImageState();
}

/// Static cache để lưu trữ image data
class _ImageCache {
  static final Map<String, Uint8List> _cache = {};
  static const int maxCacheSize = 100; // Tăng cache size cho better performance
  
  static Uint8List? get(String key) => _cache[key];
  
  static void put(String key, Uint8List data) {
    // Nếu cache đầy, xóa item cũ nhất (LRU-like)
    if (_cache.length >= maxCacheSize) {
      final firstKey = _cache.keys.first;
      _cache.remove(firstKey);
    }
    _cache[key] = data;
  }
  
  static void clear() => _cache.clear();
  
  static bool contains(String key) => _cache.containsKey(key);
  
  static int get size => _cache.length;
}

class _AuthenticatedImageState extends State<AuthenticatedImage> {
  late final ApiClient _apiClient;
  Uint8List? _cachedImageData;
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _apiClient = GetIt.instance<ApiClient>();
    _loadImageData();
  }

  @override
  void didUpdateWidget(AuthenticatedImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Nếu imageId thay đổi, load lại image
    if (oldWidget.imageId != widget.imageId) {
      _loadImageData();
    }
  }

  void _loadImageData() {
    // Kiểm tra cache trước
    final cachedData = _ImageCache.get(widget.imageId);
    if (cachedData != null) {
      if (mounted) {
        setState(() {
          _cachedImageData = cachedData;
          _isLoading = false;
          _hasError = false;
        });
      }
      return;
    }

    // Nếu không có trong cache, load từ server
    _fetchImageData();
  }

  Future<void> _fetchImageData() async {
    if (_isLoading || !mounted) return; // Tránh multiple requests
    
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final appConfig = AppConfig();
      final baseUrl = appConfig.baseApiUrl;
      final endpoint = ApiEndpoints.imageStream(widget.imageId);
      final url = '$baseUrl$endpoint';
      
      final uri = Uri.parse(url);
      final request = http.Request('GET', uri);
      
      // Add authentication header
      if (_apiClient.accessToken != null) {
        request.headers['Authorization'] = 'Bearer ${_apiClient.accessToken}';
      }
      
      final streamedResponse = await request.send();
      
      if (streamedResponse.statusCode >= 200 && streamedResponse.statusCode < 300) {
        final bytes = await streamedResponse.stream.toBytes();
        final imageData = Uint8List.fromList(bytes);
        
        // Lưu vào cache
        _ImageCache.put(widget.imageId, imageData);
        
        if (mounted) {
          setState(() {
            _cachedImageData = imageData;
            _isLoading = false;
            _hasError = false;
          });
        }
      } else {
        if (kDebugMode) {
          print('Failed to load image: HTTP ${streamedResponse.statusCode}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = true;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading image: $e');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;

    // Nếu có cached data, hiển thị ngay lập tức
    if (_cachedImageData != null) {
      imageWidget = Image.memory(
        _cachedImageData!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) => widget.placeholder,
      );
    }
    // Nếu đang loading, hiển thị loading với placeholder background
    else if (_isLoading) {
      imageWidget = SizedBox(
        width: widget.width,
        height: widget.height,
        child: Stack(
          children: [
            // Placeholder background để tránh flickering
            widget.placeholder,
            // Loading indicator overlay
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.1),
                child: const Center(
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
    // Nếu có lỗi hoặc chưa load, hiển thị placeholder
    else {
      imageWidget = widget.placeholder;
    }

    // Apply border radius if provided
    if (widget.borderRadius != null) {
      return ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

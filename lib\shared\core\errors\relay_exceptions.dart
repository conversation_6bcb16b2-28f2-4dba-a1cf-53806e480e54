/// Base exception class for relay management operations.
class RelayManagementException implements Exception {
  /// The error message.
  final String message;

  /// The underlying cause of the error, if any.
  final dynamic cause;

  /// Creates a new [RelayManagementException].
  const RelayManagementException(this.message, [this.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'RelayManagementException: $message\nCaused by: $cause';
    }
    return 'RelayManagementException: $message';
  }
}

/// Exception thrown when relay device connection fails.
class RelayConnectionException extends RelayManagementException {
  /// Creates a new [RelayConnectionException].
  const RelayConnectionException(super.message, [super.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'RelayConnectionException: $message\nCaused by: $cause';
    }
    return 'RelayConnectionException: $message';
  }
}

/// Exception thrown when relay configuration is invalid.
class RelayConfigurationException extends RelayManagementException {
  /// Creates a new [RelayConfigurationException].
  const RelayConfigurationException(super.message, [super.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'RelayConfigurationException: $message\nCaused by: $cause';
    }
    return 'RelayConfigurationException: $message';
  }
}

/// Exception thrown when relay command execution fails.
class RelayCommandException extends RelayManagementException {
  /// The command that failed.
  final String? command;

  /// The relay index that was targeted.
  final int? relayIndex;

  /// Creates a new [RelayCommandException].
  const RelayCommandException(
    String message, {
    this.command,
    this.relayIndex,
    dynamic cause,
  }) : super(message, cause);

  @override
  String toString() {
    final buffer = StringBuffer('RelayCommandException: $message');
    
    if (command != null) {
      buffer.write('\nCommand: $command');
    }
    
    if (relayIndex != null) {
      buffer.write('\nRelay Index: $relayIndex');
    }
    
    if (cause != null) {
      buffer.write('\nCaused by: $cause');
    }
    
    return buffer.toString();
  }
}

/// Exception thrown when relay server registration fails.
class RelayRegistrationException extends RelayManagementException {
  /// The server URL that was attempted.
  final String? serverUrl;

  /// Creates a new [RelayRegistrationException].
  const RelayRegistrationException(
    String message, {
    this.serverUrl,
    dynamic cause,
  }) : super(message, cause);

  @override
  String toString() {
    final buffer = StringBuffer('RelayRegistrationException: $message');
    
    if (serverUrl != null) {
      buffer.write('\nServer URL: $serverUrl');
    }
    
    if (cause != null) {
      buffer.write('\nCaused by: $cause');
    }
    
    return buffer.toString();
  }
} 
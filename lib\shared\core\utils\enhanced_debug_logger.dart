import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Enhanced debug logger with WebSocket message support
class EnhancedDebugLogger {
  static final EnhancedDebugLogger _instance = EnhancedDebugLogger._internal();
  factory EnhancedDebugLogger() => _instance;
  EnhancedDebugLogger._internal();

  /// Debug logs storage
  final List<DebugLogEntry> _logs = [];
  
  /// Maximum number of logs to keep
  static const int _maxLogs = 100;
  
  /// Stream controller for log updates
  final StreamController<DebugLogEntry> _logController = 
      StreamController<DebugLogEntry>.broadcast();
  
  /// WebSocket message listeners
  final List<StreamSubscription> _wsSubscriptions = [];

  /// Get stream of log entries
  Stream<DebugLogEntry> get logStream => _logController.stream;

  /// Get current logs
  List<DebugLogEntry> get logs => List.unmodifiable(_logs);

  /// Add a debug log entry
  void addLog(String message, {
    DebugLogLevel level = DebugLogLevel.info,
    String? tag,
    Map<String, dynamic>? data,
  }) {
    if (!kDebugMode) return;

    final entry = DebugLogEntry(
      timestamp: DateTime.now(),
      level: level,
      message: message,
      tag: tag,
      data: data,
    );

    _logs.add(entry);
    
    // Keep only the last _maxLogs entries
    if (_logs.length > _maxLogs) {
      _logs.removeAt(0);
    }

    // Emit to stream
    _logController.add(entry);

    // Also print to console
    _printToConsole(entry);
  }

  /// Listen to WebSocket messages and log them
  void listenToWebSocketMessages(Stream<Map<String, dynamic>> messageStream, {
    String? tag,
  }) {
    final subscription = messageStream.listen(
      (message) {
        final messageType = message['type'] as String? ?? 'unknown';
        final messageId = message['message_id'] as String?;
        
        String logMessage = '📨 WebSocket message received';
        if (messageId != null) {
          logMessage += ' (ID: $messageId)';
        }
        logMessage += ': $messageType';

        addLog(
          logMessage,
          level: DebugLogLevel.websocket,
          tag: tag ?? 'WebSocket',
          data: {
            'message_type': messageType,
            'message_id': messageId,
            'full_message': message,
          },
        );
      },
      onError: (error) {
        addLog(
          '❌ WebSocket error: $error',
          level: DebugLogLevel.error,
          tag: tag ?? 'WebSocket',
          data: {'error': error.toString()},
        );
      },
      onDone: () {
        addLog(
          '🔌 WebSocket connection closed',
          level: DebugLogLevel.warning,
          tag: tag ?? 'WebSocket',
        );
      },
    );

    _wsSubscriptions.add(subscription);
    
    addLog(
      '🎧 Started listening to WebSocket messages',
      level: DebugLogLevel.info,
      tag: tag ?? 'WebSocket',
    );
  }

  /// Log WebSocket response
  void logWebSocketResponse(Map<String, dynamic> response, {
    String? tag,
    String? requestId,
  }) {
    final success = response['success'] as bool? ?? false;
    final error = response['error'] as String?;
    
    String message = success ? '✅ WebSocket response: SUCCESS' : '❌ WebSocket response: FAILED';
    if (requestId != null) {
      message += ' (Request: $requestId)';
    }
    if (error != null) {
      message += ' - $error';
    }

    addLog(
      message,
      level: success ? DebugLogLevel.success : DebugLogLevel.error,
      tag: tag ?? 'WebSocket',
      data: {
        'success': success,
        'error': error,
        'request_id': requestId,
        'full_response': response,
      },
    );
  }

  /// Clear all logs
  void clearLogs() {
    _logs.clear();
    addLog('🧹 Debug logs cleared', level: DebugLogLevel.info);
  }

  /// Print log entry to console
  void _printToConsole(DebugLogEntry entry) {
    final timestamp = entry.timestamp.toLocal().toString().substring(11, 19);
    final levelIcon = entry.level.icon;
    final tag = entry.tag != null ? '[${entry.tag}] ' : '';
    
    print('$levelIcon $timestamp $tag${entry.message}');
    
    if (entry.data != null && entry.data!.isNotEmpty) {
      print('   Data: ${jsonEncode(entry.data)}');
    }
  }

  /// Dispose resources
  void dispose() {
    for (final subscription in _wsSubscriptions) {
      subscription.cancel();
    }
    _wsSubscriptions.clear();
    _logController.close();
  }
}

/// Debug log entry
class DebugLogEntry {
  final DateTime timestamp;
  final DebugLogLevel level;
  final String message;
  final String? tag;
  final Map<String, dynamic>? data;

  const DebugLogEntry({
    required this.timestamp,
    required this.level,
    required this.message,
    this.tag,
    this.data,
  });

  /// Get formatted timestamp
  String get formattedTime => timestamp.toLocal().toString().substring(11, 19);

  /// Get display message with level icon
  String get displayMessage => '${level.icon} $message';
}

/// Debug log levels
enum DebugLogLevel {
  debug('🐛'),
  info('ℹ️'),
  warning('⚠️'),
  error('❌'),
  success('✅'),
  websocket('📨'),
  relay('🔌'),
  server('🌐');

  const DebugLogLevel(this.icon);
  final String icon;
}

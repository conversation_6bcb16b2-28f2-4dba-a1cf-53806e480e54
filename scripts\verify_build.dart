#!/usr/bin/env dart

/// Simple verification script to check if terminal app builds successfully
/// This script verifies the build without running the full app

import 'dart:io';

void main() async {
  print('🔍 Verifying Terminal App Build');
  print('=' * 40);
  
  var testsPassed = 0;
  var totalTests = 0;
  
  // Test 1: Check if main_terminal.dart exists
  totalTests++;
  try {
    final mainFile = File('lib/apps/terminal/main_terminal.dart');
    if (await mainFile.exists()) {
      print('✅ Test 1: main_terminal.dart exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 1: main_terminal.dart missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 1: Error checking main_terminal.dart - FAILED: $e');
  }
  
  // Test 2: Check if face detection provider exists
  totalTests++;
  try {
    final providerFile = File('lib/apps/terminal/providers/face_detection_provider.dart');
    if (await providerFile.exists()) {
      print('✅ Test 2: face_detection_provider.dart exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 2: face_detection_provider.dart missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 2: Error checking face_detection_provider.dart - FAILED: $e');
  }
  
  // Test 3: Check if configuration files exist
  totalTests++;
  try {
    final configFile = File('lib/apps/terminal/config/face_detection_terminal_config.dart');
    if (await configFile.exists()) {
      print('✅ Test 3: face_detection_terminal_config.dart exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 3: face_detection_terminal_config.dart missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 3: Error checking face_detection_terminal_config.dart - FAILED: $e');
  }
  
  // Test 4: Check if unified provider exists
  totalTests++;
  try {
    final unifiedFile = File('lib/packages/face_recognition/src/providers/unified_face_detection_provider.dart');
    if (await unifiedFile.exists()) {
      print('✅ Test 4: unified_face_detection_provider.dart exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 4: unified_face_detection_provider.dart missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 4: Error checking unified_face_detection_provider.dart - FAILED: $e');
  }
  
  // Test 5: Check if engine files exist
  totalTests++;
  try {
    final mediapipeFile = File('lib/packages/face_recognition/src/detection/engines/mediapipe_engine.dart');
    final mlkitFile = File('lib/packages/face_recognition/src/detection/engines/mlkit_engine.dart');
    final ultrafaceFile = File('lib/packages/face_recognition/src/detection/engines/ultraface_engine.dart');
    
    if (await mediapipeFile.exists() && await mlkitFile.exists() && await ultrafaceFile.exists()) {
      print('✅ Test 5: All engine files exist - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 5: Some engine files missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 5: Error checking engine files - FAILED: $e');
  }
  
  // Test 6: Check if PowerShell script exists
  totalTests++;
  try {
    final scriptFile = File('scripts/run_terminal.ps1');
    if (await scriptFile.exists()) {
      print('✅ Test 6: run_terminal.ps1 exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 6: run_terminal.ps1 missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 6: Error checking run_terminal.ps1 - FAILED: $e');
  }
  
  // Test 7: Check pubspec.yaml for required dependencies
  totalTests++;
  try {
    final pubspecFile = File('pubspec.yaml');
    if (await pubspecFile.exists()) {
      final content = await pubspecFile.readAsString();
      if (content.contains('google_mlkit_face_detection') && 
          content.contains('camera') && 
          content.contains('provider')) {
        print('✅ Test 7: Required dependencies in pubspec.yaml - PASSED');
        testsPassed++;
      } else {
        print('❌ Test 7: Missing required dependencies - FAILED');
      }
    } else {
      print('❌ Test 7: pubspec.yaml missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 7: Error checking pubspec.yaml - FAILED: $e');
  }
  
  // Test 8: Check if demo widget exists
  totalTests++;
  try {
    final demoFile = File('lib/apps/terminal/widgets/face_detection_demo_widget.dart');
    if (await demoFile.exists()) {
      print('✅ Test 8: face_detection_demo_widget.dart exists - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 8: face_detection_demo_widget.dart missing - FAILED');
    }
  } catch (e) {
    print('❌ Test 8: Error checking face_detection_demo_widget.dart - FAILED: $e');
  }
  
  // Test 9: Run flutter analyze
  totalTests++;
  try {
    print('🔍 Running flutter analyze...');
    final result = await Process.run(
      'flutter',
      ['analyze', 'lib/apps/terminal/main_terminal.dart'],
      workingDirectory: Directory.current.path,
    );
    
    if (result.exitCode == 0) {
      print('✅ Test 9: Flutter analyze passed - PASSED');
      testsPassed++;
    } else {
      print('❌ Test 9: Flutter analyze failed - FAILED');
      print('Error output: ${result.stderr}');
    }
  } catch (e) {
    print('❌ Test 9: Error running flutter analyze - FAILED: $e');
  }
  
  // Summary
  print('');
  print('=' * 40);
  print('📊 Verification Results: $testsPassed/$totalTests tests passed');
  
  if (testsPassed == totalTests) {
    print('🎉 All verification tests passed!');
    print('✅ Terminal app structure is complete');
    print('✅ All required files are present');
    print('✅ Dependencies are configured');
    print('✅ Code analysis passed');
    print('✅ Ready for build and deployment');
    
    // Additional info
    print('');
    print('🚀 Next Steps:');
    print('1. Run: flutter run --target lib/apps/terminal/main_terminal.dart -d windows');
    print('2. Or use: .\\scripts\\run_terminal.ps1 windows');
    print('3. Test face detection with ML Kit engine');
    print('4. Deploy to Telpo F8 device when ready');
    
    exit(0);
  } else {
    print('⚠️ Some verification tests failed.');
    print('Please review the issues above before proceeding.');
    exit(1);
  }
}

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import '../../providers/device_registration_provider.dart';
import '../../../../shared/core/utils/enhanced_debug_logger.dart';

/// Simplified debug widget to check device registration status
class DeviceRegistrationDebugWidget extends StatefulWidget {
  const DeviceRegistrationDebugWidget({super.key});

  @override
  State<DeviceRegistrationDebugWidget> createState() => _DeviceRegistrationDebugWidgetState();
}

class _DeviceRegistrationDebugWidgetState extends State<DeviceRegistrationDebugWidget> {
  final EnhancedDebugLogger _logger = EnhancedDebugLogger();
  bool _isChecking = false;
  List<dynamic>? _serverDevices;
  StreamSubscription<DebugLogEntry>? _logSubscription;

  @override
  void initState() {
    super.initState();
    _addLog('Device Registration Debug Widget initialized');

    // Listen to log updates
    _logSubscription = _logger.logStream.listen((_) {
      if (mounted) {
        setState(() {});
      }
    });

    // Setup WebSocket listening
    _setupWebSocketListening();

    // Auto-check server on startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _addLog('Auto-checking server status...');
      _checkServerDevices();
    });
  }

  @override
  void dispose() {
    _logSubscription?.cancel();
    super.dispose();
  }

  void _addLog(String message, {DebugLogLevel level = DebugLogLevel.info}) {
    _logger.addLog(message, level: level, tag: 'DeviceDebug');
  }

  void _setupWebSocketListening() {
    try {
      final provider = Provider.of<DeviceRegistrationProvider>(context, listen: false);

      // Check if provider has secure communication with WebSocket transport
      if (provider.secureComm?.transport != null) {
        final transport = provider.secureComm!.transport;

        // Check if it's a WebSocket transport
        if (transport.transportType == 'websocket') {
          // Access the message stream using reflection or cast
          try {
            final wsTransport = transport as dynamic;
            if (wsTransport.messageStream != null) {
              _logger.listenToWebSocketMessages(
                wsTransport.messageStream,
                tag: 'DeviceWS',
              );
              _addLog('🎧 WebSocket message listening enabled', level: DebugLogLevel.websocket);
            }
          } catch (e) {
            _addLog('⚠️ Could not access WebSocket message stream: $e', level: DebugLogLevel.warning);
          }
        } else {
          _addLog('ℹ️ Transport is not WebSocket (${transport.transportType})', level: DebugLogLevel.info);
        }
      } else {
        _addLog('ℹ️ No secure communication transport available yet', level: DebugLogLevel.info);
      }
    } catch (e) {
      _addLog('❌ Failed to setup WebSocket listening: $e', level: DebugLogLevel.error);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceRegistrationProvider>(
      builder: (context, provider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    const Icon(Icons.bug_report, color: Colors.orange, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      'Registration Debug',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const Spacer(),
                    if (_isChecking)
                      const SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Current Status
                _buildStatusSection(provider),
                
                const SizedBox(height: 12),
                
                // Action Buttons
                _buildActionButtons(provider),
                
                const SizedBox(height: 12),
                
                // Server Devices Summary
                if (_serverDevices != null) _buildServerSummary(),
                
                const SizedBox(height: 12),
                
                // Debug Logs (compact)
                _buildDebugLogsSection(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusSection(DeviceRegistrationProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status',
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const SizedBox(height: 6),
        _buildStatusRow('Registered', provider.isRegistered),
        _buildStatusRow('Connected', provider.isConnected),
        _buildInfoRow('Device', provider.deviceInfo?.deviceName ?? 'Unknown'),
        if (provider.deviceInfo?.deviceId != null)
          _buildInfoRow('Device ID', provider.deviceInfo!.deviceId),
        if (provider.deviceInfo?.serverUrl != null)
          _buildInfoRow('Server', provider.deviceInfo!.serverUrl),
        if (provider.errorMessage != null)
          _buildErrorRow('Error', provider.errorMessage!),
      ],
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.error,
            size: 12,
            color: status ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 6),
          Text(label, style: const TextStyle(fontSize: 11)),
          const Spacer(),
          Text(
            status ? 'YES' : 'NO',
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: status ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        children: [
          const Icon(Icons.info, size: 12, color: Colors.blue),
          const SizedBox(width: 6),
          Text(label, style: const TextStyle(fontSize: 11)),
          const Spacer(),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorRow(String label, String error) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        children: [
          const Icon(Icons.warning, size: 12, color: Colors.orange),
          const SizedBox(width: 6),
          Text(label, style: const TextStyle(fontSize: 11)),
          const Spacer(),
          Flexible(
            child: Text(
              error,
              style: const TextStyle(fontSize: 11, color: Colors.orange),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(DeviceRegistrationProvider provider) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isChecking ? null : _checkServerDevices,
            icon: const Icon(Icons.cloud, size: 14),
            label: const Text('Check Server', style: TextStyle(fontSize: 11)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
              minimumSize: const Size(0, 28),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isChecking ? null : _testRelayRegistration,
            icon: const Icon(Icons.electrical_services, size: 14),
            label: const Text('Test Relay', style: TextStyle(fontSize: 11)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
              minimumSize: const Size(0, 28),
            ),
          ),
        ),
        const SizedBox(width: 6),
        IconButton(
          onPressed: _clearLogs,
          icon: const Icon(Icons.clear, size: 14),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildServerSummary() {
    final terminalCount = _serverDevices!.where((d) => 
        d['id']?.toString().startsWith('T-') == true && 
        !(d['id']?.toString().contains('-R') ?? false)).length;
    final relayCount = _serverDevices!.where((d) => 
        d['id']?.toString().contains('-R') == true).length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Server Devices',
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const SizedBox(height: 6),
        Row(
          children: [
            _buildCountChip('Total', _serverDevices!.length, Colors.blue),
            const SizedBox(width: 6),
            _buildCountChip('Terminals', terminalCount, Colors.green),
            const SizedBox(width: 6),
            _buildCountChip('Relays', relayCount, Colors.orange),
          ],
        ),
      ],
    );
  }

  Widget _buildCountChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildDebugLogsSection() {
    final logs = _logger.logs;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Enhanced Debug Logs (${logs.length})',
              style: Theme.of(context).textTheme.labelMedium,
            ),
            const Spacer(),
            if (logs.isNotEmpty)
              IconButton(
                onPressed: _clearLogs,
                icon: const Icon(Icons.clear_all, size: 16),
                tooltip: 'Clear logs',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 140,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(6),
          ),
          child: ListView.builder(
            padding: const EdgeInsets.all(6),
            itemCount: logs.length,
            reverse: true, // Show newest logs at top
            itemBuilder: (context, index) {
              final log = logs[logs.length - 1 - index];
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 1),
                child: RichText(
                  text: TextSpan(
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 9,
                      color: Colors.black87,
                    ),
                    children: [
                      TextSpan(
                        text: '${log.formattedTime} ',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      if (log.tag != null)
                        TextSpan(
                          text: '[${log.tag}] ',
                          style: const TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      TextSpan(
                        text: log.displayMessage,
                        style: TextStyle(
                          color: _getLogColor(log.level),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getLogColor(DebugLogLevel level) {
    switch (level) {
      case DebugLogLevel.error:
        return Colors.red.shade700;
      case DebugLogLevel.warning:
        return Colors.orange.shade700;
      case DebugLogLevel.success:
        return Colors.green.shade700;
      case DebugLogLevel.websocket:
        return Colors.purple.shade700;
      case DebugLogLevel.relay:
        return Colors.teal.shade700;
      case DebugLogLevel.server:
        return Colors.indigo.shade700;
      default:
        return Colors.black87;
    }
  }

  Future<void> _checkServerDevices() async {
    if (_isChecking) return;
    
    setState(() => _isChecking = true);
    _addLog('🔍 Checking server devices...');

    try {
      const serverUrl = 'http://10.161.80.12';
      _addLog('Server: $serverUrl');

      final uri = Uri.parse('$serverUrl/devices');
      _addLog('📡 GET /devices');
      
      final response = await http.get(uri).timeout(
        const Duration(seconds: 10),
      );
      
      _addLog('Response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final devices = json.decode(response.body) as List;
        _addLog('📊 Found ${devices.length} devices', level: DebugLogLevel.server);

        // Log WebSocket response if this was a WebSocket call
        _logger.logWebSocketResponse(
          {
            'success': true,
            'data': {'device_count': devices.length},
            'message': 'Server devices retrieved successfully'
          },
          tag: 'ServerAPI',
        );

        setState(() {
          _serverDevices = devices;
        });

        // Log device summary with group device support
        final terminalDevices = devices.where((d) =>
            d['id']?.toString().startsWith('T-') == true &&
            !(d['id']?.toString().contains('-R') ?? false)).toList();
        final relayDevices = devices.where((d) =>
            d['id']?.toString().contains('-R') == true).toList();
        final groupDevices = devices.where((d) =>
            d['type']?.toString() == 'terminal_group' ||
            d['groupType']?.toString() == 'terminal_with_relays').toList();

        _addLog('🖥️ Terminals: ${terminalDevices.length}', level: DebugLogLevel.server);
        _addLog('🔌 Relays: ${relayDevices.length}', level: DebugLogLevel.relay);
        _addLog('👥 Group Devices: ${groupDevices.length}', level: DebugLogLevel.server);

        // Log group devices first
        for (final device in groupDevices.take(3)) {
          final relayCount = device['relayCount'] ?? device['relay_count'] ?? 0;
          _addLog('  G: ${device['id']} ($relayCount relays)', level: DebugLogLevel.server);
        }

        // Log individual terminals
        for (final device in terminalDevices.take(3)) {
          _addLog('  T: ${device['id']}', level: DebugLogLevel.info);
        }

        // Log individual relays (legacy)
        for (final device in relayDevices.take(5)) {
          _addLog('  R: ${device['id']}', level: DebugLogLevel.relay);
        }

      } else {
        _addLog('❌ Server error: ${response.statusCode}', level: DebugLogLevel.error);

        // Log WebSocket error response
        _logger.logWebSocketResponse(
          {
            'success': false,
            'error': 'HTTP ${response.statusCode}',
            'message': 'Server request failed'
          },
          tag: 'ServerAPI',
        );
      }

    } catch (e) {
      _addLog('❌ Failed: $e');

      // Show error in UI
      setState(() {
        _serverDevices = null;
        _isChecking = false;
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Server check failed: ${e.toString().split('\n').first}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isChecking = false);
    }
  }

  Future<void> _testRelayRegistration() async {
    if (_isChecking) return;

    setState(() => _isChecking = true);
    _addLog('🔌 Testing relay registration using provider...');

    try {
      final provider = Provider.of<DeviceRegistrationProvider>(context, listen: false);

      if (provider.deviceInfo == null) {
        _addLog('❌ No terminal device registered');
        _addLog('Please register terminal first');
        return;
      }

      final terminalId = provider.deviceInfo!.deviceId;
      _addLog('Terminal: $terminalId');
      _addLog('Using provider auto-registration method...');

      // Use provider method instead of duplicate code
      final success = await provider.registerRelayDevices();

      if (success) {
        _addLog('✅ Provider relay registration completed');
      } else {
        _addLog('❌ Provider relay registration failed');
      }

      // Small delay then check server
      await Future.delayed(const Duration(milliseconds: 1000));
      _addLog('🔄 Checking server after registration...');
      await _checkServerDevices();

    } catch (e) {
      _addLog('❌ Test failed: $e');
    } finally {
      setState(() => _isChecking = false);
    }
  }

  void _clearLogs() {
    setState(() {
      _serverDevices = null;
    });
    _logger.clearLogs();
  }
}

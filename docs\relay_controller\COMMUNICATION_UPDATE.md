# Relay Controller Communication Update

## Overview

Đã cập nhật hệ thống communication của relay controller để tương thích với test server mới và hỗ trợ các tính năng bảo mật nâng cao.

## Key Changes

### 1. Updated API Endpoints

**New Secure Endpoints:**
- `POST /api/device/register` - Secure device registration với JWT + HMAC
- `POST /api/device/refresh` - Refresh access token
- `POST /api/device/revoke` - Revoke device credentials
- `POST /api/face/recognize` - Face recognition với secure authentication
- `POST /api/message` - Secure messaging với HMAC signature
- `POST /api/message/plain` - Plain text messaging (no auth required)

**Device Naming System:**
- `GET /api/naming/generate-terminal-id` - Generate terminal ID (T-XXXX format)
- `POST /api/naming/generate-relay-id` - Generate relay device ID
- `POST /api/naming/validate` - Validate device naming format
- `GET /api/naming/summary/:terminalId` - Get naming summary
- `GET /api/devices/grouped` - Get devices grouped by terminal

**Legacy Endpoints (Backward Compatible):**
- `POST /register` - Legacy device registration
- `GET /relay/on?deviceId=xxx` - Turn relay on
- `GET /relay/off?deviceId=xxx` - Turn relay off
- `POST /relay/control` - Control relay via JSON

### 2. Enhanced RelayApiService

**New Methods:**
```dart
// Device registration with secure/legacy API support
Future<RelayRegistrationResponse> registerDevice({
  required RelayDeviceConfig deviceConfig,
  Map<String, dynamic>? additionalInfo,
  bool useSecureApi = true,
});

// Face recognition
Future<FaceRecognitionResponse> recognizeFace({
  required String deviceId,
  required String imageData,
  double? confidenceScore,
  Map<String, dynamic>? metadata,
});

// Secure messaging
Future<SecureMessageResponse> sendSecureMessage({
  required String deviceId,
  required String messageType,
  required Map<String, dynamic> payload,
  String? messageId,
});

// Plain text messaging
Future<PlainMessageResponse> sendPlainMessage({
  required String deviceId,
  required String messageType,
  required Map<String, dynamic> payload,
  String? deviceType,
  String? messageId,
});
```

### 3. Device Profile Integration

Hệ thống device profile được tích hợp vào registration data:

```dart
// Device profile được lấy từ configuration
final profileType = ConfigHelper.getValue('relay.device_profile', 'esp32');

// Supported profiles:
- esp32: R0:1, R0:0, R1:TOGGLE, R2:500, ALL:1, ALL:0
- arduino: REL_0_ON, REL_0_OFF, REL_ALL_ON, etc.
- simple: 01, 00, 1T, 1P500, A1, A0
- custom: User-defined command templates
```

### 4. Enhanced Response Models

**New Response Types:**
- `FaceRecognitionResponse` - Face recognition results
- `SecureMessageResponse` - Secure message responses
- `PlainMessageResponse` - Plain text message responses

## Usage Examples

### 1. Device Registration

```dart
// Secure API registration
final apiService = RelayApiService.instance;
await apiService.initialize(httpClient);

final response = await apiService.registerDevice(
  deviceConfig: RelayDeviceConfig(
    deviceId: 'T-A3B4-R01',
    deviceName: 'Main Door Relay',
    relayCount: 4,
    baudRate: 115200,
  ),
  useSecureApi: true, // Use secure API
);

// Legacy API registration
final legacyResponse = await apiService.registerDevice(
  deviceConfig: deviceConfig,
  useSecureApi: false, // Use legacy API
);
```

### 2. Face Recognition

```dart
final faceResponse = await apiService.recognizeFace(
  deviceId: 'T-A3B4-R01',
  imageData: base64ImageData,
  confidenceScore: 0.85,
  metadata: {
    'source': 'camera_1',
    'timestamp': DateTime.now().toIso8601String(),
  },
);

if (faceResponse.recognized) {
  print('User: ${faceResponse.user?['name']}');
  print('Access: ${faceResponse.allowAccess}');
}
```

### 3. Secure Messaging

```dart
final messageResponse = await apiService.sendSecureMessage(
  deviceId: 'T-A3B4-R01',
  messageType: 'relay_control',
  payload: {
    'action': 'ON',
    'relay_index': 1,
  },
);

print('Message sent: ${messageResponse.success}');
```

### 4. Device Naming System

```dart
// Generate terminal ID
final terminalResponse = await http.get(
  Uri.parse('$baseUrl/api/naming/generate-terminal-id'),
);

// Generate relay ID
final relayResponse = await http.post(
  Uri.parse('$baseUrl/api/naming/generate-relay-id'),
  body: jsonEncode({
    'terminal_id': 'T-A3B4',
    'relay_index': 1,
  }),
);
```

## Configuration

### API Endpoints Configuration

```dart
// lib/shared/services/api_endpoints.dart
class ApiEndpoints {
  // Secure device registration
  static const String registerDevice = '/api/device/register';
  
  // Legacy device registration
  static const String registerDeviceLegacy = '/register';
  
  // Face recognition
  static const String faceRecognize = '/api/face/recognize';
  
  // Secure messaging
  static const String secureMessage = '/api/message';
  static const String plainMessage = '/api/message/plain';
  
  // Device naming system
  static const String generateTerminalId = '/api/naming/generate-terminal-id';
  static const String generateRelayId = '/api/naming/generate-relay-id';
}
```

### Device Profile Configuration

```dart
// Configuration parameters
relay.device_profile = 'esp32' // esp32, arduino, simple, custom
relay.custom_profile_name = 'Custom Device'
relay.relay_on_template = 'R{relay}:1'
relay.relay_off_template = 'R{relay}:0'
relay.relay_toggle_template = 'R{relay}:TOGGLE'
```

## Test Server Integration

### Starting Test Server

```bash
cd lib/packages/relay_controller/test_server
npm install
npm start
```

Server runs on `http://localhost:3000` with:
- Web dashboard at `/`
- Secure API endpoints at `/api/*`
- Legacy endpoints for backward compatibility
- WebSocket support for real-time updates

### Testing Communication

```bash
cd lib/packages/relay_controller/test_server
dart run test_communication.dart
```

## Security Features

### 1. JWT Authentication
- Access tokens với expiration time
- Refresh token mechanism
- Device scope management

### 2. HMAC Request Signing
- Message integrity verification
- Replay attack prevention
- Secret key rotation support

### 3. Device Registration Security
- Hardware hash verification
- Device capability validation
- Secure credential exchange

## Migration Guide

### From Old System

1. **Update imports:**
```dart
// Old
import 'old_relay_service.dart';

// New
import 'relay_api_service.dart';
```

2. **Update registration calls:**
```dart
// Old
await oldService.register(deviceId, deviceName);

// New
await apiService.registerDevice(
  deviceConfig: RelayDeviceConfig(...),
  useSecureApi: true,
);
```

3. **Update configuration:**
```dart
// Add device profile configuration
relay.device_profile = 'esp32'
```

## Troubleshooting

### Common Issues

1. **Connection refused:** Ensure test server is running on port 3000
2. **Authentication failed:** Check JWT token and HMAC signature
3. **Device naming invalid:** Use T-XXXX format for terminal IDs
4. **Profile not found:** Verify device profile configuration

### Debug Mode

Enable debug logging:
```dart
debugPrint('✅ RelayApiService initialized');
```

## Future Enhancements

- [ ] WebSocket real-time communication
- [ ] MQTT broker integration
- [ ] Enhanced security with certificate pinning
- [ ] Device health monitoring
- [ ] Batch operations support

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Home icon component for TabsBar
class HomeIcon extends StatelessWidget {
  final bool isActive;
  final double size;

  const HomeIcon({
    super.key,
    required this.isActive,
    this.size = 18.0,
  });

  @override
  Widget build(BuildContext context) {
    final color = isActive ? '#008FD3' : '#9CA5B3';
    
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        '''<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M7.124 16.5L6.93602 13.8683C6.83496 12.4535 7.95554 11.25 9.374 11.25C10.7925 11.25 11.913 12.4535 11.812 13.8683L11.624 16.5"
            stroke="$color"
            stroke-width="1.5"
          />
          <path
            d="M2.13854 9.91009C1.87378 8.18718 1.7414 7.32573 2.06712 6.56203C2.39285 5.79834 3.11552 5.27582 4.56085 4.2308L5.64074 3.45C7.43872 2.15 8.33771 1.5 9.375 1.5C10.4123 1.5 11.3113 2.15 13.1093 3.45L14.1891 4.2308C15.6345 5.27582 16.3571 5.79834 16.6829 6.56203C17.0086 7.32573 16.8762 8.18718 16.6115 9.91009L16.3857 11.3793C16.0103 13.8217 15.8227 15.0429 14.9468 15.7714C14.0708 16.5 12.7903 16.5 10.2291 16.5H8.52089C5.95975 16.5 4.67918 16.5 3.80325 15.7714C2.92732 15.0429 2.73965 13.8217 2.36432 11.3793L2.13854 9.91009Z"
            stroke="$color"
            stroke-width="1.5"
            stroke-linejoin="round"
          />
        </svg>''',
        width: size,
        height: size,
      ),
    );
  }
}

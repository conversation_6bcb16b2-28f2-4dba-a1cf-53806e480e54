package com.ccam.terminal.utils;

import android.util.Log;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Custom log filter to reduce spam from native libraries
 */
public class LogFilter {
    private static final String TAG = "LogFilter";
    private static final Set<String> SPAM_TAGS = new HashSet<>(Arrays.asList(
            "FaceDetectorV2Jni",
            "ML Kit",
            "GoogleApiAvailability",
            "DynamiteModule",
            "CameraX"
    ));

    private static final Set<String> SPAM_MESSAGES = new HashSet<>(Arrays.asList(
            "detectFacesImageByteArray.start()",
            "detectFacesImageByteArray.end()"
    ));

    private static boolean enabled = true;

    /**
     * Initialize log filter
     */
    public static void init() {
        Log.i(TAG, "Log filter initialized");
    }

    /**
     * Check if message should be filtered
     * @param tag Log tag
     * @param message Log message
     * @return true if message should be filtered out
     */
    public static boolean shouldFilter(String tag, String message) {
        if (!enabled) {
            return false;
        }

        // Check if tag is in spam list
        if (SPAM_TAGS.contains(tag)) {
            return true;
        }

        // Check if message contains spam patterns
        for (String spamMessage : SPAM_MESSAGES) {
            if (message != null && message.contains(spamMessage)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Enable or disable log filtering
     * @param enabled true to enable filtering, false to disable
     */
    public static void setEnabled(boolean enabled) {
        LogFilter.enabled = enabled;
        Log.i(TAG, "Log filter " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Add a tag to the spam list
     * @param tag Tag to add
     */
    public static void addSpamTag(String tag) {
        SPAM_TAGS.add(tag);
    }

    /**
     * Add a message pattern to the spam list
     * @param message Message pattern to add
     */
    public static void addSpamMessage(String message) {
        SPAM_MESSAGES.add(message);
    }

    /**
     * Remove a tag from the spam list
     * @param tag Tag to remove
     */
    public static void removeSpamTag(String tag) {
        SPAM_TAGS.remove(tag);
    }

    /**
     * Remove a message pattern from the spam list
     * @param message Message pattern to remove
     */
    public static void removeSpamMessage(String message) {
        SPAM_MESSAGES.remove(message);
    }

    /**
     * Clear all filters
     */
    public static void clearFilters() {
        SPAM_TAGS.clear();
        SPAM_MESSAGES.clear();
    }
}

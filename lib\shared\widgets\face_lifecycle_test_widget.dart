import 'package:flutter/material.dart';
import '../providers/face_providers_lifecycle_manager.dart';

/// Test widget để verify FaceProvidersLifecycleManager functionality
/// 
/// Sử dụng để manual testing:
/// 1. Initialize/dispose providers
/// 2. Test reference counting
/// 3. Test concurrent access
/// 4. Test callbacks
/// 5. Test error scenarios
class FaceLifecycleTestWidget extends StatefulWidget {
  const FaceLifecycleTestWidget({super.key});

  @override
  State<FaceLifecycleTestWidget> createState() => _FaceLifecycleTestWidgetState();
}

class _FaceLifecycleTestWidgetState extends State<FaceLifecycleTestWidget> {
  final FaceProvidersLifecycleManager _lifecycleManager = 
      FaceProvidersLifecycleManager.instance;
  
  String _widgetId = '';
  bool _isRegistered = false;
  String _statusMessage = '';
  List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _widgetId = 'test_widget_${DateTime.now().millisecondsSinceEpoch}';
    
    // Listen to lifecycle manager changes
    _lifecycleManager.addListener(_onLifecycleChanged);
    
    // Add callbacks
    _lifecycleManager.addOnReadyCallback(_onReady);
    _lifecycleManager.addOnDisposedCallback(_onDisposed);
    _lifecycleManager.addOnErrorCallback(_onError);
    
    _updateStatus();
  }

  @override
  void dispose() {
    // Cleanup
    if (_isRegistered) {
      _lifecycleManager.unregisterWidget(_widgetId);
    }
    
    _lifecycleManager.removeListener(_onLifecycleChanged);
    _lifecycleManager.removeOnReadyCallback(_onReady);
    _lifecycleManager.removeOnDisposedCallback(_onDisposed);
    _lifecycleManager.removeOnErrorCallback(_onError);
    
    super.dispose();
  }

  void _onLifecycleChanged() {
    if (mounted) {
      setState(() {
        _updateStatus();
      });
    }
  }

  void _onReady() {
    _addLog('✅ Providers ready callback triggered');
  }

  void _onDisposed() {
    _addLog('🧹 Providers disposed callback triggered');
  }

  void _onError(String error) {
    _addLog('❌ Error callback: $error');
  }

  void _addLog(String message) {
    if (mounted) {
      setState(() {
        _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
        if (_logs.length > 20) {
          _logs.removeAt(0);
        }
      });
    }
  }

  void _updateStatus() {
    try {
      final state = _lifecycleManager.state;
      final refCount = _lifecycleManager.referenceCount;
      final error = _lifecycleManager.errorMessage;

      _statusMessage = 'State: ${state.name}\n'
          'Reference Count: $refCount\n'
          'Is Initializing: ${_lifecycleManager.isInitializing}\n'
          'Is Disposing: ${_lifecycleManager.isDisposing}\n'
          'Is Ready: ${_lifecycleManager.isReady}\n'
          'Widget Registered: $_isRegistered\n'
          'Widget ID: $_widgetId';

      if (error != null) {
        _statusMessage += '\nError: $error';
      }
    } catch (e) {
      _statusMessage = 'Error updating status: $e';
      _addLog('❌ Error updating status: $e');
    }
  }

  Future<void> _registerWidget() async {
    try {
      await _lifecycleManager.registerWidget(_widgetId, autoInitialize: true);
      setState(() {
        _isRegistered = true;
      });
      _addLog('📝 Widget registered with auto-initialize');
    } catch (e) {
      _addLog('❌ Failed to register widget: $e');
    }
  }

  Future<void> _unregisterWidget() async {
    try {
      await _lifecycleManager.unregisterWidget(_widgetId, autoDispose: true);
      setState(() {
        _isRegistered = false;
      });
      _addLog('📝 Widget unregistered with auto-dispose');
    } catch (e) {
      _addLog('❌ Failed to unregister widget: $e');
    }
  }

  Future<void> _initializeProviders() async {
    try {
      _addLog('🚀 Initializing providers...');
      await _lifecycleManager.initializeProviders();
      _addLog('✅ Providers initialized');
    } catch (e) {
      _addLog('❌ Failed to initialize providers: $e');
    }
  }

  Future<void> _disposeProviders() async {
    try {
      _addLog('🧹 Disposing providers...');
      await _lifecycleManager.disposeProviders();
      _addLog('✅ Providers disposed');
    } catch (e) {
      _addLog('❌ Failed to dispose providers: $e');
    }
  }

  Future<void> _forceDispose() async {
    try {
      _addLog('💥 Force disposing providers...');
      await _lifecycleManager.forceDisposeProviders();
      _addLog('✅ Providers force disposed');
    } catch (e) {
      _addLog('❌ Failed to force dispose providers: $e');
    }
  }

  Future<void> _resetManager() async {
    try {
      _addLog('🔄 Resetting lifecycle manager...');
      await _lifecycleManager.reset();
      setState(() {
        _isRegistered = false;
      });
      _addLog('✅ Lifecycle manager reset');
    } catch (e) {
      _addLog('❌ Failed to reset manager: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Lifecycle Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Status section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Lifecycle Manager Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Control buttons
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRegistered ? null : _registerWidget,
                        child: const Text('Register Widget'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: !_isRegistered ? null : _unregisterWidget,
                        child: const Text('Unregister Widget'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _initializeProviders,
                        child: const Text('Initialize'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _disposeProviders,
                        child: const Text('Dispose'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _forceDispose,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                        child: const Text('Force Dispose'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _resetManager,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                        ),
                        child: const Text('Reset'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Logs section
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Expanded(
                            child: Text(
                              'Activity Logs',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 80,
                            child: TextButton(
                              onPressed: () {
                                setState(() {
                                  _logs.clear();
                                });
                              },
                              child: const Text('Clear'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: _logs.map((log) => Padding(
                                padding: const EdgeInsets.only(bottom: 2),
                                child: Text(
                                  log,
                                  style: const TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                  ),
                                ),
                              )).toList(),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }
}

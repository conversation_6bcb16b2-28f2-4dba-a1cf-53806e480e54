<div
  class="flex flex-col justify-start items-center w-[375px] h-[815px] overflow-hidden bg-[#f4fbff]"
>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3 py-4"
    >
      <div
        class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-4"
      >
        <div
          class="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 h-6"
        >
          <div
            class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-3"
          >
            <svg
              width="7"
              height="12"
              viewBox="0 0 7 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-[5px] h-2.5"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M6 1L1.70711 5.29289C1.37377 5.62623 1.20711 5.79289 1.20711 6C1.20711 6.20711 1.37377 6.37377 1.70711 6.70711L6 11"
                stroke="#85888C"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <div
              class="flex justify-start items-center flex-grow-0 flex-shrink-0 relative"
            >
              <p
                class="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#15171a]"
              >
                Danh sách thành viên
              </p>
            </div>
          </div>
          <div
            class="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1.5 rounded-[50px]"
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M6.99967 2.33301L6.99967 11.6663M11.6663 6.99967L2.33301 6.99967"
                stroke="#008FD3"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <p
              class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-[#008fd3]"
            >
              Thêm mới
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4"
  >
    <div
      class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3 px-4"
    >
      <div
        class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2"
      >
        <div
          class="flex flex-col justify-start items-start flex-grow h-9 gap-2.5 px-4 py-2 rounded-lg bg-white border border-[#e5e6e7]"
        >
          <div
            class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
              preserveAspectRatio="xMidYMid meet"
            >
              <g clip-path="url(#clip0_200_1661)">
                <path
                  d="M11.667 11.667L14.667 14.667"
                  stroke="#85888C"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></path>
                <path
                  d="M13.333 7.33301C13.333 4.0193 10.6467 1.33301 7.33301 1.33301C4.0193 1.33301 1.33301 4.0193 1.33301 7.33301C1.33301 10.6467 4.0193 13.333 7.33301 13.333C10.6467 13.333 13.333 10.6467 13.333 7.33301Z"
                  stroke="#85888C"
                  stroke-width="1.5"
                  stroke-linejoin="round"
                ></path>
              </g>
              <defs>
                <clipPath id="clip0_200_1661">
                  <rect width="16" height="16" fill="white"></rect>
                </clipPath>
              </defs>
            </svg>
            <p class="flex-grow w-[231px] text-xs text-left text-[#85888c]">
              Theo tên hoặc email
            </p>
          </div>
        </div>
        <div
          class="flex justify-center items-center flex-grow-0 flex-shrink-0 h-9 relative gap-2 px-4 rounded-lg bg-white border border-[#e5e6e7]"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
            preserveAspectRatio="xMidYMid meet"
          >
            <path
              d="M5.90497 8.3374C4.246 7.09708 3.06376 5.73277 2.41822 4.96578C2.2184 4.72835 2.15292 4.5546 2.11355 4.24854C1.97874 3.20054 1.91134 2.67654 2.21864 2.33827C2.52593 2 3.06936 2 4.15622 2H11.8438C12.9306 2 13.4741 2 13.7814 2.33827C14.0887 2.67654 14.0213 3.20054 13.8864 4.24854C13.8471 4.55461 13.7816 4.72836 13.5818 4.96578C12.9353 5.73375 11.7507 7.10047 10.0884 8.34236C9.93799 8.45472 9.83885 8.63782 9.82047 8.84093C9.65579 10.6613 9.50393 11.6584 9.40941 12.1628C9.25684 12.9771 8.10211 13.4671 7.48401 13.9042C7.11604 14.1644 6.66953 13.8546 6.62185 13.4519C6.53095 12.6841 6.35974 11.1242 6.17285 8.84093C6.15606 8.63596 6.05657 8.45072 5.90497 8.3374Z"
              stroke="#85888C"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </div>
      </div>
      <div
        class="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-1"
      >
        <div
          class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative"
        >
          <p
            class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#85888c]"
          >
            Tổng:
          </p>
        </div>
        <div
          class="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative"
        >
          <p
            class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-[#1f2329]"
          >
            10 người
          </p>
        </div>
      </div>
      <div
        class="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3"
      >
        <div
          class="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden gap-4 px-3 py-4 rounded-lg bg-white border border-[#e5e6e7]"
        >
          <div
            class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2"
          >
            <img
              src="img_7877-2.png"
              class="flex-grow-0 flex-shrink-0 w-[42px] h-[42px] rounded-[50px] object-none border border-[#e5e6e7]"
            />
            <div class="flex flex-col justify-center items-start flex-grow">
              <div
                class="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4"
              >
                <div
                  class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-[#081f41]"
                  >
                    Trần Thanh Long
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#67718e]"
                  >
                    -
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#85888c]"
                  >
                    E000001
                  </p>
                </div>
              </div>
              <div
                class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[153px] h-5 relative gap-1.5"
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                  preserveAspectRatio="xMidYMid meet"
                >
                  <path
                    d="M3.15209 8.24999H8.84809C8.97309 8.17499 9.10809 8.11499 9.25009 8.07249V6.99949C9.25009 6.86688 9.19741 6.73971 9.10365 6.64594C9.00988 6.55217 8.8827 6.49949 8.75009 6.49949H3.25009C3.11748 6.49949 2.99031 6.55217 2.89654 6.64594C2.80277 6.73971 2.75009 6.86688 2.75009 6.99949V8.07249C2.89259 8.11499 3.02759 8.17499 3.15209 8.24999ZM1.75009 8.07249V6.99949C1.75009 6.60167 1.90813 6.22014 2.18943 5.93883C2.47074 5.65753 2.85227 5.49949 3.25009 5.49949H5.50009V4.43649C5.02876 4.3148 4.618 4.02538 4.34479 3.62249C4.07159 3.2196 3.9547 2.7309 4.01603 2.24799C4.07737 1.76508 4.31272 1.32112 4.67798 0.999331C5.04323 0.677536 5.51331 0.5 6.00009 0.5C6.48688 0.5 6.95696 0.677536 7.32221 0.999331C7.68746 1.32112 7.92281 1.76508 7.98415 2.24799C8.04549 2.7309 7.9286 3.2196 7.65539 3.62249C7.38219 4.02538 6.97142 4.3148 6.50009 4.43649V5.49949H8.75009C9.14792 5.49949 9.52945 5.65753 9.81075 5.93883C10.0921 6.22014 10.2501 6.60167 10.2501 6.99949V8.07249C10.6348 8.18716 10.9684 8.43061 11.1949 8.76201C11.4214 9.09341 11.5272 9.49262 11.4943 9.8927C11.4615 10.2928 11.2921 10.6694 11.0146 10.9595C10.737 11.2495 10.3682 11.4353 9.96999 11.4857C9.57174 11.5361 9.16826 11.4481 8.82721 11.2364C8.48615 11.0247 8.22825 10.7021 8.09675 10.3229C7.96524 9.94357 7.96813 9.53062 8.10493 9.15322C8.24172 8.77582 8.50411 8.45692 8.84809 8.24999H3.15209C3.49608 8.45692 3.75846 8.77582 3.89526 9.15322C4.03205 9.53062 4.03494 9.94357 3.90344 10.3229C3.77193 10.7021 3.51403 11.0247 3.17298 11.2364C2.83192 11.4481 2.42845 11.5361 2.0302 11.4857C1.63195 11.4353 1.26314 11.2495 0.985611 10.9595C0.70808 10.6694 0.538698 10.2928 0.505865 9.8927C0.473033 9.49262 0.578745 9.09341 0.805278 8.76201C1.03181 8.43061 1.36539 8.18716 1.75009 8.07249ZM6.00009 3.49949C6.26531 3.49949 6.51966 3.39413 6.7072 3.2066C6.89473 3.01906 7.00009 2.76471 7.00009 2.49949C7.00009 2.23428 6.89473 1.97992 6.7072 1.79238C6.51966 1.60485 6.26531 1.49949 6.00009 1.49949C5.73488 1.49949 5.48052 1.60485 5.29299 1.79238C5.10545 1.97992 5.00009 2.23428 5.00009 2.49949C5.00009 2.76471 5.10545 3.01906 5.29299 3.2066C5.48052 3.39413 5.73488 3.49949 6.00009 3.49949ZM9.75009 10.5C9.949 10.5 10.1398 10.421 10.2804 10.2803C10.4211 10.1397 10.5001 9.9489 10.5001 9.74999C10.5001 9.55108 10.4211 9.36031 10.2804 9.21966C10.1398 9.07901 9.949 8.99999 9.75009 8.99999C9.55118 8.99999 9.36042 9.07901 9.21976 9.21966C9.07911 9.36031 9.00009 9.55108 9.00009 9.74999C9.00009 9.9489 9.07911 10.1397 9.21976 10.2803C9.36042 10.421 9.55118 10.5 9.75009 10.5ZM2.25009 10.5C2.449 10.5 2.63977 10.421 2.78042 10.2803C2.92107 10.1397 3.00009 9.9489 3.00009 9.74999C3.00009 9.55108 2.92107 9.36031 2.78042 9.21966C2.63977 9.07901 2.449 8.99999 2.25009 8.99999C2.05118 8.99999 1.86041 9.07901 1.71976 9.21966C1.57911 9.36031 1.50009 9.55108 1.50009 9.74999C1.50009 9.9489 1.57911 10.1397 1.71976 10.2803C1.86041 10.421 2.05118 10.5 2.25009 10.5Z"
                    fill="#85888C"
                  ></path>
                </svg>
                <p
                  class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#85888c]"
                >
                  Phòng PD
                </p>
              </div>
              <div
                class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[153px] h-5 relative gap-1.5"
              >
                <div
                  class="flex justify-center items-center flex-grow-0 flex-shrink-0 w-3 h-3"
                >
                  <div
                    class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#40bf24]"
                  ></div>
                </div>
                <p
                  class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#85888c]"
                >
                  Đang hoạt động
                </p>
              </div>
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M6 12L9.29289 8.70711C9.62623 8.37377 9.79289 8.20711 9.79289 8C9.79289 7.79289 9.62623 7.62623 9.29289 7.29289L6 4"
                stroke="#1F2329"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
          </div>
        </div>
        <div
          class="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden gap-4 px-3 py-4 rounded-lg bg-white border border-[#e5e6e7]"
        >
          <div
            class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-2"
          >
            <img
              src="img_7877-2.png"
              class="flex-grow-0 flex-shrink-0 w-[42px] h-[42px] rounded-[50px] object-none border border-[#e5e6e7]"
            />
            <div class="flex flex-col justify-center items-start flex-grow">
              <div
                class="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4"
              >
                <div
                  class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1"
                >
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-[#081f41]"
                  >
                    Trần Thanh Long
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#67718e]"
                  >
                    -
                  </p>
                  <p
                    class="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#85888c]"
                  >
                    E000002
                  </p>
                </div>
              </div>
              <div
                class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[153px] h-5 relative gap-1.5"
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                  preserveAspectRatio="xMidYMid meet"
                >
                  <path
                    d="M3.15209 8.24999H8.84809C8.97309 8.17499 9.10809 8.11499 9.25009 8.07249V6.99949C9.25009 6.86688 9.19741 6.73971 9.10365 6.64594C9.00988 6.55217 8.8827 6.49949 8.75009 6.49949H3.25009C3.11748 6.49949 2.99031 6.55217 2.89654 6.64594C2.80277 6.73971 2.75009 6.86688 2.75009 6.99949V8.07249C2.89259 8.11499 3.02759 8.17499 3.15209 8.24999ZM1.75009 8.07249V6.99949C1.75009 6.60167 1.90813 6.22014 2.18943 5.93883C2.47074 5.65753 2.85227 5.49949 3.25009 5.49949H5.50009V4.43649C5.02876 4.3148 4.618 4.02538 4.34479 3.62249C4.07159 3.2196 3.9547 2.7309 4.01603 2.24799C4.07737 1.76508 4.31272 1.32112 4.67798 0.999331C5.04323 0.677536 5.51331 0.5 6.00009 0.5C6.48688 0.5 6.95696 0.677536 7.32221 0.999331C7.68746 1.32112 7.92281 1.76508 7.98415 2.24799C8.04549 2.7309 7.9286 3.2196 7.65539 3.62249C7.38219 4.02538 6.97142 4.3148 6.50009 4.43649V5.49949H8.75009C9.14792 5.49949 9.52945 5.65753 9.81075 5.93883C10.0921 6.22014 10.2501 6.60167 10.2501 6.99949V8.07249C10.6348 8.18716 10.9684 8.43061 11.1949 8.76201C11.4214 9.09341 11.5272 9.49262 11.4943 9.8927C11.4615 10.2928 11.2921 10.6694 11.0146 10.9595C10.737 11.2495 10.3682 11.4353 9.96999 11.4857C9.57174 11.5361 9.16826 11.4481 8.82721 11.2364C8.48615 11.0247 8.22825 10.7021 8.09675 10.3229C7.96524 9.94357 7.96813 9.53062 8.10493 9.15322C8.24172 8.77582 8.50411 8.45692 8.84809 8.24999H3.15209C3.49608 8.45692 3.75846 8.77582 3.89526 9.15322C4.03205 9.53062 4.03494 9.94357 3.90344 10.3229C3.77193 10.7021 3.51403 11.0247 3.17298 11.2364C2.83192 11.4481 2.42845 11.5361 2.0302 11.4857C1.63195 11.4353 1.26314 11.2495 0.985611 10.9595C0.70808 10.6694 0.538698 10.2928 0.505865 9.8927C0.473033 9.49262 0.578745 9.09341 0.805278 8.76201C1.03181 8.43061 1.36539 8.18716 1.75009 8.07249ZM6.00009 3.49949C6.26531 3.49949 6.51966 3.39413 6.7072 3.2066C6.89473 3.01906 7.00009 2.76471 7.00009 2.49949C7.00009 2.23428 6.89473 1.97992 6.7072 1.79238C6.51966 1.60485 6.26531 1.49949 6.00009 1.49949C5.73488 1.49949 5.48052 1.60485 5.29299 1.79238C5.10545 1.97992 5.00009 2.23428 5.00009 2.49949C5.00009 2.76471 5.10545 3.01906 5.29299 3.2066C5.48052 3.39413 5.73488 3.49949 6.00009 3.49949ZM9.75009 10.5C9.949 10.5 10.1398 10.421 10.2804 10.2803C10.4211 10.1397 10.5001 9.9489 10.5001 9.74999C10.5001 9.55108 10.4211 9.36031 10.2804 9.21966C10.1398 9.07901 9.949 8.99999 9.75009 8.99999C9.55118 8.99999 9.36042 9.07901 9.21976 9.21966C9.07911 9.36031 9.00009 9.55108 9.00009 9.74999C9.00009 9.9489 9.07911 10.1397 9.21976 10.2803C9.36042 10.421 9.55118 10.5 9.75009 10.5ZM2.25009 10.5C2.449 10.5 2.63977 10.421 2.78042 10.2803C2.92107 10.1397 3.00009 9.9489 3.00009 9.74999C3.00009 9.55108 2.92107 9.36031 2.78042 9.21966C2.63977 9.07901 2.449 8.99999 2.25009 8.99999C2.05118 8.99999 1.86041 9.07901 1.71976 9.21966C1.57911 9.36031 1.50009 9.55108 1.50009 9.74999C1.50009 9.9489 1.57911 10.1397 1.71976 10.2803C1.86041 10.421 2.05118 10.5 2.25009 10.5Z"
                    fill="#85888C"
                  ></path>
                </svg>
                <p
                  class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#85888c]"
                >
                  Phòng PD
                </p>
              </div>
              <div
                class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[153px] h-5 relative gap-1.5"
              >
                <div
                  class="flex justify-center items-center flex-grow-0 flex-shrink-0 w-3 h-3"
                >
                  <div
                    class="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#40bf24]"
                  ></div>
                </div>
                <p
                  class="flex-grow-0 flex-shrink-0 text-xs text-left text-[#85888c]"
                >
                  Đang hoạt động
                </p>
              </div>
            </div>
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M6 12L9.29289 8.70711C9.62623 8.37377 9.79289 8.20711 9.79289 8C9.79289 7.79289 9.62623 7.62623 9.29289 7.29289L6 4"
                stroke="#1F2329"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-col justify-start items-start self-stretch flex-grow w-[375px] h-[484px] gap-4 py-4"
  ></div>
</div>

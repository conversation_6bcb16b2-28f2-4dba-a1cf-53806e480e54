import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for optimizing app performance and reducing resource usage
class PerformanceOptimizationService {
  static PerformanceOptimizationService? _instance;
  static PerformanceOptimizationService get instance {
    _instance ??= PerformanceOptimizationService._();
    return _instance!;
  }
  
  PerformanceOptimizationService._();

  // Performance monitoring
  Timer? _performanceMonitorTimer;
  DateTime? _lastFrameTime;
  int _frameCount = 0;
  double _currentFps = 0.0;
  
  // Thermal management
  bool _isThrottling = false;
  DateTime? _lastThrottleCheck;
  
  // Adaptive settings
  int _currentCameraFps = 30;
  int _currentDetectionInterval = 1; // Process every N frames
  bool _isLowPowerMode = false;

  /// Initialize performance optimization
  Future<void> initialize() async {
    _startPerformanceMonitoring();
    
    if (kDebugMode) {
      print('🚀 PerformanceOptimizationService initialized');
    }
  }

  /// Start monitoring performance metrics
  void _startPerformanceMonitoring() {
    _performanceMonitorTimer?.cancel();
    _performanceMonitorTimer = Timer.periodic(
      const Duration(seconds: 5), 
      (timer) => _checkPerformanceMetrics()
    );
  }

  /// Check performance metrics and adjust settings
  void _checkPerformanceMetrics() {
    _checkThermalState();
    _adjustPerformanceSettings();
    
    if (kDebugMode) {
      print('📊 Performance: FPS=${_currentFps.toStringAsFixed(1)}, '
            'Throttling=$_isThrottling, LowPower=$_isLowPowerMode');
    }
  }

  /// Check thermal state and enable throttling if needed
  void _checkThermalState() {
    // Simple thermal detection based on performance degradation
    // In production, you could use platform-specific thermal APIs
    
    final now = DateTime.now();
    if (_lastThrottleCheck != null) {
      final timeDiff = now.difference(_lastThrottleCheck!).inMilliseconds;
      
      // If FPS drops significantly, assume thermal throttling
      if (_currentFps < 15 && timeDiff > 10000) { // 10 seconds of low FPS
        _isThrottling = true;
        if (kDebugMode) {
          print('🔥 Thermal throttling detected - enabling power saving');
        }
      } else if (_currentFps > 25 && _isThrottling) {
        _isThrottling = false;
        if (kDebugMode) {
          print('❄️ Thermal state improved - disabling throttling');
        }
      }
    }
    
    _lastThrottleCheck = now;
  }

  /// Adjust performance settings based on current state
  void _adjustPerformanceSettings() {
    if (_isThrottling || _isLowPowerMode) {
      // Aggressive power saving
      _currentCameraFps = 15;
      _currentDetectionInterval = 3; // Process every 3rd frame
    } else if (_currentFps < 20) {
      // Moderate optimization
      _currentCameraFps = 20;
      _currentDetectionInterval = 2; // Process every 2nd frame
    } else {
      // Normal performance
      _currentCameraFps = 30;
      _currentDetectionInterval = 1; // Process every frame
    }
  }

  /// Update frame rate tracking
  void updateFrameRate() {
    final now = DateTime.now();
    
    if (_lastFrameTime != null) {
      _frameCount++;
      
      final timeDiff = now.difference(_lastFrameTime!).inMilliseconds;
      if (timeDiff >= 1000) { // Calculate FPS every second
        _currentFps = _frameCount * 1000.0 / timeDiff;
        _frameCount = 0;
        _lastFrameTime = now;
      }
    } else {
      _lastFrameTime = now;
    }
  }

  /// Get recommended camera FPS
  int get recommendedCameraFps => _currentCameraFps;

  /// Get recommended face detection interval
  int get recommendedDetectionInterval => _currentDetectionInterval;

  /// Check if should process current frame for face detection
  bool shouldProcessFrame(int frameIndex) {
    return frameIndex % _currentDetectionInterval == 0;
  }

  /// Enable low power mode
  void enableLowPowerMode() {
    _isLowPowerMode = true;
    _adjustPerformanceSettings();
    
    if (kDebugMode) {
      print('🔋 Low power mode enabled');
    }
  }

  /// Disable low power mode
  void disableLowPowerMode() {
    _isLowPowerMode = false;
    _adjustPerformanceSettings();
    
    if (kDebugMode) {
      print('⚡ Low power mode disabled');
    }
  }

  /// Get performance recommendations
  Map<String, dynamic> getPerformanceRecommendations() {
    return {
      'currentFps': _currentFps,
      'recommendedCameraFps': _currentCameraFps,
      'detectionInterval': _currentDetectionInterval,
      'isThrottling': _isThrottling,
      'isLowPowerMode': _isLowPowerMode,
      'recommendations': _getRecommendations(),
    };
  }

  List<String> _getRecommendations() {
    final recommendations = <String>[];
    
    if (_isThrottling) {
      recommendations.add('Device is thermal throttling - reduce camera quality');
      recommendations.add('Consider increasing face detection interval');
    }
    
    if (_currentFps < 15) {
      recommendations.add('Low FPS detected - enable power saving mode');
      recommendations.add('Reduce background processing');
    }
    
    if (_currentFps > 25 && _isLowPowerMode) {
      recommendations.add('Performance is good - can disable power saving');
    }
    
    return recommendations;
  }

  /// Dispose resources
  void dispose() {
    _performanceMonitorTimer?.cancel();
    _performanceMonitorTimer = null;
    
    if (kDebugMode) {
      print('🔄 PerformanceOptimizationService disposed');
    }
  }
}

/// Camera optimization configuration
class CameraOptimizationConfig {
  // Adaptive FPS based on performance
  static const int maxFps = 30;
  static const int normalFps = 20;
  static const int powerSavingFps = 15;
  static const int minFps = 10;
  
  // Face detection intervals
  static const int normalDetectionInterval = 1;  // Every frame
  static const int optimizedDetectionInterval = 2; // Every 2nd frame
  static const int powerSavingDetectionInterval = 3; // Every 3rd frame
  static const int extremePowerSavingDetectionInterval = 5; // Every 5th frame
  
  // Image quality settings
  static const int highQualityWidth = 1920;
  static const int highQualityHeight = 1080;
  static const int normalQualityWidth = 1280;
  static const int normalQualityHeight = 720;
  static const int powerSavingQualityWidth = 854;
  static const int powerSavingQualityHeight = 480;
  
  /// Get optimized camera config based on performance state
  static Map<String, dynamic> getOptimizedConfig({
    required bool isThrottling,
    required bool isLowPowerMode,
    required double currentFps,
  }) {
    if (isThrottling || isLowPowerMode) {
      return {
        'fps': powerSavingFps,
        'width': powerSavingQualityWidth,
        'height': powerSavingQualityHeight,
        'detectionInterval': powerSavingDetectionInterval,
        'quality': 'power_saving',
      };
    } else if (currentFps < 20) {
      return {
        'fps': normalFps,
        'width': normalQualityWidth,
        'height': normalQualityHeight,
        'detectionInterval': optimizedDetectionInterval,
        'quality': 'normal',
      };
    } else {
      return {
        'fps': maxFps,
        'width': highQualityWidth,
        'height': highQualityHeight,
        'detectionInterval': normalDetectionInterval,
        'quality': 'high',
      };
    }
  }
}

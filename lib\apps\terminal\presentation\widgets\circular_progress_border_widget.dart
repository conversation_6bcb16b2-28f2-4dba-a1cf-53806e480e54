import 'package:flutter/material.dart';
import 'circular_progress_border_painter.dart';

/// Widget that wraps a child with a circular progress border animation
/// 
/// This widget displays a progress animation around the border of its child,
/// typically used to indicate throttle or loading progress.
class CircularProgressBorderWidget extends StatelessWidget {
  /// The child widget to wrap with the progress border
  final Widget child;
  
  /// Progress value from 0.0 to 1.0
  final double progress;
  
  /// Whether the progress border should be visible
  final bool isVisible;
  
  /// Color of the progress arc
  final Color progressColor;
  
  /// Width of the progress arc stroke
  final double strokeWidth;
  
  /// Border radius of the container (should match child's border radius)
  final double borderRadius;
  
  /// Whether to use a gradient for the progress arc
  final bool useGradient;
  
  /// Start color for gradient (if useGradient is true)
  final Color? gradientStartColor;
  
  /// End color for gradient (if useGradient is true)
  final Color? gradientEndColor;
  
  /// Animation duration for smooth progress updates
  final Duration animationDuration;

  const CircularProgressBorderWidget({
    super.key,
    required this.child,
    required this.progress,
    this.isVisible = true,
    this.progressColor = Colors.blue,
    this.strokeWidth = 2.0,
    this.borderRadius = 15.0,
    this.useGradient = false,
    this.gradientStartColor,
    this.gradientEndColor,
    this.animationDuration = const Duration(milliseconds: 100),
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return child;
    }

    return Stack(
      children: [
        // The original child widget
        child,
        
        // The progress border overlay
        Positioned.fill(
          child: AnimatedBuilder(
            animation: AlwaysStoppedAnimation(progress),
            builder: (context, _) {
              return CustomPaint(
                painter: CircularProgressBorderPainter(
                  progress: progress,
                  progressColor: progressColor,
                  strokeWidth: strokeWidth,
                  borderRadius: borderRadius,
                  useGradient: useGradient,
                  gradientStartColor: gradientStartColor,
                  gradientEndColor: gradientEndColor,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Animated version of CircularProgressBorderWidget with smooth transitions
class AnimatedCircularProgressBorderWidget extends StatefulWidget {
  /// The child widget to wrap with the progress border
  final Widget child;
  
  /// Progress value from 0.0 to 1.0
  final double progress;
  
  /// Whether the progress border should be visible
  final bool isVisible;
  
  /// Color of the progress arc
  final Color progressColor;
  
  /// Width of the progress arc stroke
  final double strokeWidth;
  
  /// Border radius of the container (should match child's border radius)
  final double borderRadius;
  
  /// Whether to use a gradient for the progress arc
  final bool useGradient;
  
  /// Start color for gradient (if useGradient is true)
  final Color? gradientStartColor;
  
  /// End color for gradient (if useGradient is true)
  final Color? gradientEndColor;
  
  /// Animation duration for smooth progress updates
  final Duration animationDuration;

  const AnimatedCircularProgressBorderWidget({
    super.key,
    required this.child,
    required this.progress,
    this.isVisible = true,
    this.progressColor = Colors.blue,
    this.strokeWidth = 2.0,
    this.borderRadius = 15.0,
    this.useGradient = false,
    this.gradientStartColor,
    this.gradientEndColor,
    this.animationDuration = const Duration(milliseconds: 100),
  });

  @override
  State<AnimatedCircularProgressBorderWidget> createState() => 
      _AnimatedCircularProgressBorderWidgetState();
}

class _AnimatedCircularProgressBorderWidgetState 
    extends State<AnimatedCircularProgressBorderWidget>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularProgressBorderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return widget.child;
    }

    return Stack(
      children: [
        // The original child widget
        widget.child,
        
        // The animated progress border overlay
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, _) {
              return CustomPaint(
                painter: CircularProgressBorderPainter(
                  progress: _progressAnimation.value,
                  progressColor: widget.progressColor,
                  strokeWidth: widget.strokeWidth,
                  borderRadius: widget.borderRadius,
                  useGradient: widget.useGradient,
                  gradientStartColor: widget.gradientStartColor,
                  gradientEndColor: widget.gradientEndColor,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

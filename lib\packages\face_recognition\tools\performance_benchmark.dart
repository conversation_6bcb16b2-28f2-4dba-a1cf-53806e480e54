import 'dart:async';
import 'dart:typed_data';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

import '../face_recognition.dart';

/// Performance benchmarking tool for face recognition system
class PerformanceBenchmark {
  static const int _defaultTestDuration = 30; // seconds
  static const int _defaultWarmupFrames = 10;
  
  /// Run comprehensive benchmark on terminal device
  static Future<BenchmarkResult> benchmarkTerminal({
    TerminalDeviceType deviceType = TerminalDeviceType.telpoF8,
    int testDurationSeconds = _defaultTestDuration,
    int warmupFrames = _defaultWarmupFrames,
  }) async {
    print('🚀 Starting Terminal Benchmark');
    print('   Device: ${deviceType.name}');
    print('   Duration: ${testDurationSeconds}s');
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Initialize terminal system
      final faceSystem = await FaceRecognitionTerminal.initialize(
        deviceType: deviceType,
        performanceProfile: PerformanceProfile.maxPerformance,
      );
      
      // Generate test images
      final testImages = _generateTestImages(100);
      
      // Warmup phase
      print('🔥 Warming up...');
      for (int i = 0; i < warmupFrames; i++) {
        await faceSystem.processCameraImage(testImages[i % testImages.length]);
      }
      
      // Benchmark phase
      print('📊 Running benchmark...');
      final benchmarkStopwatch = Stopwatch()..start();
      int frameCount = 0;
      final List<int> processingTimes = [];
      final List<int> faceDetectionCounts = [];
      
      while (benchmarkStopwatch.elapsedMilliseconds < testDurationSeconds * 1000) {
        final frameStopwatch = Stopwatch()..start();
        
        final testImage = testImages[frameCount % testImages.length];
        await faceSystem.processCameraImage(testImage);
        
        frameStopwatch.stop();
        processingTimes.add(frameStopwatch.elapsedMilliseconds);
        faceDetectionCounts.add(faceSystem.detectedFaces.length);
        
        frameCount++;
        
        // Small delay to simulate real camera frame rate
        await Future.delayed(const Duration(milliseconds: 16)); // ~60 FPS
      }
      
      benchmarkStopwatch.stop();
      
      // Calculate metrics
      final totalTime = benchmarkStopwatch.elapsedMilliseconds;
      final averageFPS = (frameCount * 1000) / totalTime;
      final averageProcessingTime = processingTimes.reduce((a, b) => a + b) / processingTimes.length;
      final maxProcessingTime = processingTimes.reduce(math.max);
      final minProcessingTime = processingTimes.reduce(math.min);
      final averageFaceCount = faceDetectionCounts.reduce((a, b) => a + b) / faceDetectionCounts.length;
      
      // Get system stats
      final stats = faceSystem.getStats();
      
      // Cleanup
      await faceSystem.dispose();
      await FaceRecognitionTerminal.dispose();
      
      stopwatch.stop();
      
      final result = BenchmarkResult(
        deviceType: deviceType.name,
        testDuration: testDurationSeconds,
        totalFrames: frameCount,
        averageFPS: averageFPS,
        averageProcessingTime: averageProcessingTime,
        maxProcessingTime: maxProcessingTime,
        minProcessingTime: minProcessingTime,
        averageFaceCount: averageFaceCount,
        memoryUsage: stats.memoryUsage,
        recognitionRate: stats.recognitionRate,
        totalTestTime: stopwatch.elapsedMilliseconds,
      );
      
      print('✅ Benchmark completed');
      print(result.toString());
      
      return result;
      
    } catch (e) {
      print('❌ Benchmark failed: $e');
      rethrow;
    }
  }
  
  /// Run benchmark comparing different detection engines
  static Future<Map<DetectionEngine, BenchmarkResult>> compareEngines({
    List<DetectionEngine> engines = const [
      DetectionEngine.ultraface,
      DetectionEngine.mediapipe,
      DetectionEngine.mlKit,
    ],
    PlatformType platform = PlatformType.terminal,
    int testDurationSeconds = 15,
  }) async {
    print('🔍 Comparing Detection Engines');
    print('   Platform: ${platform.name}');
    print('   Engines: ${engines.map((e) => e.name).join(', ')}');
    
    final results = <DetectionEngine, BenchmarkResult>{};
    
    for (final engine in engines) {
      print('\n📊 Testing ${engine.name}...');
      
      try {
        // Create config for this engine
        final config = platform == PlatformType.terminal
            ? FaceRecognitionConfig.forTerminal().copyWith(detectionEngine: engine)
            : FaceRecognitionConfig.forMobile().copyWith(detectionEngine: engine);
        
        // Run engine-specific benchmark
        final result = await _benchmarkEngine(engine, config, testDurationSeconds);
        results[engine] = result;
        
        print('   ${engine.name}: ${result.averageFPS.toStringAsFixed(1)} FPS');
        
      } catch (e) {
        print('   ❌ ${engine.name} failed: $e');
      }
    }
    
    // Print comparison summary
    print('\n📈 Engine Comparison Summary:');
    print('Engine'.padRight(15) + 'FPS'.padRight(10) + 'Proc Time'.padRight(12) + 'Memory');
    print('-' * 50);
    
    for (final entry in results.entries) {
      final engine = entry.key.name.padRight(15);
      final fps = entry.value.averageFPS.toStringAsFixed(1).padRight(10);
      final procTime = '${entry.value.averageProcessingTime.toStringAsFixed(1)}ms'.padRight(12);
      final memory = '${entry.value.memoryUsage}MB';
      
      print('$engine$fps$procTime$memory');
    }
    
    return results;
  }
  
  /// Benchmark specific detection engine
  static Future<BenchmarkResult> _benchmarkEngine(
    DetectionEngine engine,
    FaceRecognitionConfig config,
    int testDurationSeconds,
  ) async {
    // This would initialize the specific engine and run tests
    // For now, return mock results based on engine capabilities
    final capabilities = DetectionEngineFactory.getCapabilities(engine);
    
    return BenchmarkResult(
      deviceType: config.platformType.name,
      testDuration: testDurationSeconds,
      totalFrames: capabilities.averageFPS * testDurationSeconds,
      averageFPS: capabilities.averageFPS.toDouble(),
      averageProcessingTime: 1000 / capabilities.averageFPS,
      maxProcessingTime: (1000 / capabilities.averageFPS * 1.5).round(),
      minProcessingTime: (1000 / capabilities.averageFPS * 0.5).round(),
      averageFaceCount: 1.2, // Mock average
      memoryUsage: capabilities.memoryUsage,
      recognitionRate: capabilities.accuracy,
      totalTestTime: testDurationSeconds * 1000,
    );
  }
  
  /// Generate test camera images for benchmarking
  static List<CameraImage> _generateTestImages(int count) {
    final images = <CameraImage>[];
    
    for (int i = 0; i < count; i++) {
      // Create mock camera image
      final mockImage = MockCameraImage(
        width: 640,
        height: 480,
        format: ImageFormat(ImageFormatGroup.yuv420, raw: 0),
      );
      
      images.add(mockImage);
    }
    
    return images;
  }
  
  /// Run stress test to find performance limits
  static Future<StressTestResult> stressTest({
    TerminalDeviceType deviceType = TerminalDeviceType.telpoF8,
    int maxConcurrentStreams = 5,
    int testDurationSeconds = 60,
  }) async {
    print('💪 Starting Stress Test');
    print('   Device: ${deviceType.name}');
    print('   Max Streams: $maxConcurrentStreams');
    print('   Duration: ${testDurationSeconds}s');
    
    final results = <int, BenchmarkResult>{};
    
    for (int streamCount = 1; streamCount <= maxConcurrentStreams; streamCount++) {
      print('\n📊 Testing $streamCount concurrent stream(s)...');
      
      try {
        final result = await _stressTestWithStreams(
          deviceType,
          streamCount,
          testDurationSeconds ~/ maxConcurrentStreams,
        );
        
        results[streamCount] = result;
        
        print('   $streamCount streams: ${result.averageFPS.toStringAsFixed(1)} FPS');
        
        // Stop if performance degrades significantly
        if (result.averageFPS < 10) {
          print('   ⚠️ Performance degraded, stopping stress test');
          break;
        }
        
      } catch (e) {
        print('   ❌ Failed with $streamCount streams: $e');
        break;
      }
    }
    
    return StressTestResult(
      deviceType: deviceType.name,
      maxStreams: results.length,
      results: results,
    );
  }
  
  /// Run stress test with specific number of concurrent streams
  static Future<BenchmarkResult> _stressTestWithStreams(
    TerminalDeviceType deviceType,
    int streamCount,
    int testDurationSeconds,
  ) async {
    // This would simulate multiple concurrent face detection streams
    // For now, return scaled results based on stream count
    final singleStreamResult = await benchmarkTerminal(
      deviceType: deviceType,
      testDurationSeconds: testDurationSeconds,
    );
    
    // Simulate performance degradation with multiple streams
    final scaleFactor = 1.0 / math.sqrt(streamCount);
    
    return BenchmarkResult(
      deviceType: singleStreamResult.deviceType,
      testDuration: testDurationSeconds,
      totalFrames: (singleStreamResult.totalFrames * scaleFactor).round(),
      averageFPS: singleStreamResult.averageFPS * scaleFactor,
      averageProcessingTime: singleStreamResult.averageProcessingTime / scaleFactor,
      maxProcessingTime: (singleStreamResult.maxProcessingTime / scaleFactor).round(),
      minProcessingTime: (singleStreamResult.minProcessingTime / scaleFactor).round(),
      averageFaceCount: singleStreamResult.averageFaceCount,
      memoryUsage: (singleStreamResult.memoryUsage * streamCount * 0.8).round(),
      recognitionRate: singleStreamResult.recognitionRate,
      totalTestTime: testDurationSeconds * 1000,
    );
  }
}

/// Mock camera image for testing
class MockCameraImage implements CameraImage {
  @override
  final int width;
  
  @override
  final int height;
  
  @override
  final ImageFormat format;
  
  @override
  final List<CameraImagePlane> planes;
  
  MockCameraImage({
    required this.width,
    required this.height,
    required this.format,
  }) : planes = [
    CameraImagePlane(
      bytes: Uint8List(width * height),
      bytesPerRow: width,
      bytesPerPixel: 1,
    ),
    CameraImagePlane(
      bytes: Uint8List((width * height) ~/ 2),
      bytesPerRow: width,
      bytesPerPixel: 2,
    ),
    CameraImagePlane(
      bytes: Uint8List((width * height) ~/ 2),
      bytesPerRow: width,
      bytesPerPixel: 2,
    ),
  ];
}

/// Benchmark result data class
class BenchmarkResult {
  final String deviceType;
  final int testDuration;
  final int totalFrames;
  final double averageFPS;
  final double averageProcessingTime;
  final int maxProcessingTime;
  final int minProcessingTime;
  final double averageFaceCount;
  final int memoryUsage;
  final double recognitionRate;
  final int totalTestTime;
  
  const BenchmarkResult({
    required this.deviceType,
    required this.testDuration,
    required this.totalFrames,
    required this.averageFPS,
    required this.averageProcessingTime,
    required this.maxProcessingTime,
    required this.minProcessingTime,
    required this.averageFaceCount,
    required this.memoryUsage,
    required this.recognitionRate,
    required this.totalTestTime,
  });
  
  @override
  String toString() {
    return '''
📊 Benchmark Results:
   Device: $deviceType
   Test Duration: ${testDuration}s
   Total Frames: $totalFrames
   Average FPS: ${averageFPS.toStringAsFixed(1)}
   Processing Time: ${averageProcessingTime.toStringAsFixed(1)}ms (avg), ${minProcessingTime}ms (min), ${maxProcessingTime}ms (max)
   Average Faces: ${averageFaceCount.toStringAsFixed(1)}
   Memory Usage: ${memoryUsage}MB
   Recognition Rate: ${(recognitionRate * 100).toStringAsFixed(1)}%
   Total Test Time: ${totalTestTime}ms
''';
  }
}

/// Stress test result data class
class StressTestResult {
  final String deviceType;
  final int maxStreams;
  final Map<int, BenchmarkResult> results;
  
  const StressTestResult({
    required this.deviceType,
    required this.maxStreams,
    required this.results,
  });
  
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.writeln('💪 Stress Test Results:');
    buffer.writeln('   Device: $deviceType');
    buffer.writeln('   Max Concurrent Streams: $maxStreams');
    buffer.writeln('');
    buffer.writeln('Streams'.padRight(10) + 'FPS'.padRight(10) + 'Memory'.padRight(10) + 'Status');
    buffer.writeln('-' * 40);
    
    for (final entry in results.entries) {
      final streams = entry.key.toString().padRight(10);
      final fps = entry.value.averageFPS.toStringAsFixed(1).padRight(10);
      final memory = '${entry.value.memoryUsage}MB'.padRight(10);
      final status = entry.value.averageFPS > 20 ? '✅ Good' : '⚠️ Degraded';
      
      buffer.writeln('$streams$fps$memory$status');
    }
    
    return buffer.toString();
  }
}

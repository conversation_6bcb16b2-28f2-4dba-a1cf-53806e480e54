import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// Service for validating and converting image formats for server compatibility
class ImageFormatService {
  static const List<String> supportedFormats = ['png', 'jpg', 'jpeg'];
  static const List<String> supportedMimeTypes = [
    'image/png',
    'image/jpeg',
    'image/jpg',
  ];

  /// Detect image format from bytes by checking magic numbers
  static String? detectImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return null;

    // PNG magic number: 89 50 4E 47
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return 'png';
    }

    // JPEG magic number: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return 'jpg';
    }

    // WebP magic number: 52 49 46 46 (RIFF) + WebP signature
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return 'webp';
    }

    // BMP magic number: 42 4D
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return 'bmp';
    }

    return null;
  }

  /// Check if image format is supported by server
  static bool isFormatSupported(String format) {
    return supportedFormats.contains(format.toLowerCase());
  }

  /// Get MIME type from format
  static String getMimeType(String format) {
    switch (format.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'application/octet-stream';
    }
  }

  /// Convert image bytes to supported format if needed
  static Future<ImageConversionResult> ensureSupportedFormat(
    Uint8List imageBytes, {
    String preferredFormat = 'jpg',
    int quality = 85,
  }) async {
    try {
      // Detect current format
      final currentFormat = detectImageFormat(imageBytes);
      
      if (kDebugMode) {
        print('🔍 Image format detection:');
        print('   Detected format: $currentFormat');
        print('   Image size: ${imageBytes.length} bytes');
        print('   Preferred format: $preferredFormat');
      }

      // If format is already supported, return as-is
      if (currentFormat != null && isFormatSupported(currentFormat)) {
        if (kDebugMode) {
          print('✅ Image format already supported: $currentFormat');
        }
        
        return ImageConversionResult(
          success: true,
          imageBytes: imageBytes,
          format: currentFormat,
          mimeType: getMimeType(currentFormat),
          converted: false,
          originalFormat: currentFormat,
        );
      }

      // Need to convert to supported format
      if (kDebugMode) {
        print('🔄 Converting image to supported format: $preferredFormat');
      }

      final convertedBytes = await _convertImageFormat(
        imageBytes,
        preferredFormat,
        quality,
      );

      if (convertedBytes == null) {
        return ImageConversionResult(
          success: false,
          error: 'Failed to convert image format',
          originalFormat: currentFormat,
        );
      }

      if (kDebugMode) {
        print('✅ Image converted successfully');
        print('   Original format: $currentFormat');
        print('   New format: $preferredFormat');
        print('   Original size: ${imageBytes.length} bytes');
        print('   New size: ${convertedBytes.length} bytes');
        print('   Size change: ${((convertedBytes.length - imageBytes.length) / imageBytes.length * 100).toStringAsFixed(1)}%');
      }

      return ImageConversionResult(
        success: true,
        imageBytes: convertedBytes,
        format: preferredFormat,
        mimeType: getMimeType(preferredFormat),
        converted: true,
        originalFormat: currentFormat,
        originalSize: imageBytes.length,
        newSize: convertedBytes.length,
      );

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error ensuring supported format: $e');
      }
      
      return ImageConversionResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Convert image bytes to specified format
  static Future<Uint8List?> _convertImageFormat(
    Uint8List imageBytes,
    String targetFormat,
    int quality,
  ) async {
    try {
      // Decode image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        if (kDebugMode) {
          print('❌ Failed to decode image for conversion');
        }
        return null;
      }

      // Encode to target format
      List<int> encodedBytes;
      switch (targetFormat.toLowerCase()) {
        case 'png':
          encodedBytes = img.encodePng(image);
          break;
        case 'jpg':
        case 'jpeg':
          encodedBytes = img.encodeJpg(image, quality: quality);
          break;
        default:
          if (kDebugMode) {
            print('❌ Unsupported target format: $targetFormat');
          }
          return null;
      }

      return Uint8List.fromList(encodedBytes);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting image format: $e');
      }
      return null;
    }
  }

  /// Validate image file format
  static Future<bool> validateImageFile(File imageFile) async {
    try {
      if (!await imageFile.exists()) {
        return false;
      }

      final bytes = await imageFile.readAsBytes();
      final format = detectImageFormat(bytes);
      
      return format != null && isFormatSupported(format);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image file: $e');
      }
      return false;
    }
  }

  /// Get recommended file extension for format
  static String getFileExtension(String format) {
    switch (format.toLowerCase()) {
      case 'png':
        return '.png';
      case 'jpg':
      case 'jpeg':
        return '.jpg';
      default:
        return '.jpg'; // Default to JPEG
    }
  }

  /// Create filename with proper extension
  static String createFilename({
    required String baseName,
    required String format,
    int? timestamp,
  }) {
    final ts = timestamp ?? DateTime.now().millisecondsSinceEpoch;
    final extension = getFileExtension(format);
    return '${baseName}_$ts$extension';
  }
}

/// Result of image format conversion
class ImageConversionResult {
  final bool success;
  final Uint8List? imageBytes;
  final String? format;
  final String? mimeType;
  final bool converted;
  final String? originalFormat;
  final int? originalSize;
  final int? newSize;
  final String? error;

  const ImageConversionResult({
    required this.success,
    this.imageBytes,
    this.format,
    this.mimeType,
    this.converted = false,
    this.originalFormat,
    this.originalSize,
    this.newSize,
    this.error,
  });

  /// Get compression ratio if converted
  double? get compressionRatio {
    if (originalSize != null && newSize != null && originalSize! > 0) {
      return (originalSize! - newSize!) / originalSize! * 100;
    }
    return null;
  }

  @override
  String toString() {
    if (!success) {
      return 'ImageConversionResult(success: false, error: $error)';
    }
    
    return 'ImageConversionResult('
        'success: true, '
        'format: $format, '
        'converted: $converted, '
        'originalFormat: $originalFormat, '
        'size: ${imageBytes?.length ?? 0} bytes'
        ')';
  }
}

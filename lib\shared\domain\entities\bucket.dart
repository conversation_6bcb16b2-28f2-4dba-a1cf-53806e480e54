class Bucket {
  final String id;
  final String name;
  final String? description;
  final bool isPublic;
  final String? region;
  final bool versioningEnabled;
  final bool encryptionEnabled;
  final Map<String, dynamic>? lifecyclePolicy;
  final Map<String, dynamic>? corsPolicy;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Bucket({
    required this.id,
    required this.name,
    this.description,
    required this.isPublic,
    this.region,
    required this.versioningEnabled,
    required this.encryptionEnabled,
    this.lifecyclePolicy,
    this.corsPolicy,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });
}

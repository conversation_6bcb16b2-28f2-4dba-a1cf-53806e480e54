import 'dart:convert';
import 'package:http/http.dart' as http;
import 'relay_controller_base.dart';
import 'exceptions.dart';

/// HTTP method types for relay control.
enum HttpMethod { get, post, put, patch }

/// A relay controller that uses HTTP requests.
/// 
/// This controller sends HTTP requests to specified URLs to control relay states.
/// Supports GET, POST, PUT, and PATCH methods.
/// 
/// Example usage:
/// ```dart
/// final controller = HttpRelayController(
///   urlOn: 'http://*************/relay/on',
///   urlOff: 'http://*************/relay/off',
/// );
/// 
/// await controller.triggerOn();
/// await controller.triggerOff();
/// ```
class HttpRelayController extends RelayController {
  /// URL to send request for turning relay ON.
  final String urlOn;

  /// URL to send request for turning relay OFF.
  final String urlOff;

  /// HTTP method to use for requests.
  final HttpMethod method;

  /// Request headers.
  final Map<String, String> headers;

  /// Request body for ON command (used with POST/PUT/PATCH).
  final String? bodyOn;

  /// Request body for OFF command (used with POST/PUT/PATCH).
  final String? bodyOff;

  /// Request timeout in seconds.
  final int timeoutSeconds;

  /// HTTP client instance.
  final http.Client _client = http.Client();

  /// Creates a new [HttpRelayController].
  ///
  /// [deviceId] is the unique device identifier.
  /// [urlOn] is the URL to send request for turning relay ON.
  /// [urlOff] is the URL to send request for turning relay OFF.
  /// [deviceName] is the device name/description.
  /// [method] is the HTTP method to use (default: GET).
  /// [headers] are additional headers to include in requests.
  /// [bodyOn] is the request body for ON command (for POST/PUT/PATCH).
  /// [bodyOff] is the request body for OFF command (for POST/PUT/PATCH).
  /// [timeoutSeconds] is the request timeout in seconds (default: 10).
  HttpRelayController({
    required super.deviceId,
    required this.urlOn,
    required this.urlOff,
    super.deviceName = 'HTTP Relay',
    this.method = HttpMethod.get,
    this.headers = const {'Content-Type': 'application/json'},
    this.bodyOn,
    this.bodyOff,
    this.timeoutSeconds = 10,
  });

  /// Sends an HTTP request to the specified URL.
  Future<http.Response> _sendRequest(String url, String? body) async {
    try {
      final uri = Uri.parse(url);
      final timeout = Duration(seconds: timeoutSeconds);

      http.Response response;

      switch (method) {
        case HttpMethod.get:
          response = await _client.get(uri, headers: headers).timeout(timeout);
          break;
        case HttpMethod.post:
          response = await _client.post(
            uri,
            headers: headers,
            body: body,
          ).timeout(timeout);
          break;
        case HttpMethod.put:
          response = await _client.put(
            uri,
            headers: headers,
            body: body,
          ).timeout(timeout);
          break;
        case HttpMethod.patch:
          response = await _client.patch(
            uri,
            headers: headers,
            body: body,
          ).timeout(timeout);
          break;
      }

      if (response.statusCode < 200 || response.statusCode >= 300) {
        throw HttpRelayException(
          'HTTP request failed with status ${response.statusCode}: ${response.body}',
        );
      }

      return response;
    } catch (e) {
      if (e is HttpRelayException) {
        rethrow;
      }
      throw HttpRelayException('Failed to send HTTP request to $url', e);
    }
  }

  @override
  Future<void> triggerOn() async {
    await _sendRequest(urlOn, bodyOn);
  }

  @override
  Future<void> triggerOff() async {
    await _sendRequest(urlOff, bodyOff);
  }

  @override
  Future<void> registerDevice() async {
    // Try to register device with server
    try {
      final registrationUrl = _getRegistrationUrl();
      if (registrationUrl != null) {
        final registrationBody = _getRegistrationBody();
        await _sendRequest(registrationUrl, registrationBody);
      }
    } catch (e) {
      throw HttpRelayException('Failed to register device', e);
    }
  }

  @override
  Future<bool?> getStatus() async {
    try {
      final statusUrl = _getStatusUrl();
      if (statusUrl != null) {
        final response = await _sendRequest(statusUrl, null);
        return _parseStatusResponse(response);
      }
      return null;
    } catch (e) {
      throw HttpRelayException('Failed to get device status', e);
    }
  }

  /// Gets the registration URL for the device.
  /// Override this method to customize registration endpoint.
  String? _getRegistrationUrl() {
    // Try to derive registration URL from base URL
    final baseUrl = _getBaseUrl();
    if (baseUrl != null) {
      return '$baseUrl/register';
    }
    return null;
  }

  /// Gets the status URL for the device.
  /// Override this method to customize status endpoint.
  String? _getStatusUrl() {
    // Try to derive status URL from base URL
    final baseUrl = _getBaseUrl();
    if (baseUrl != null) {
      return '$baseUrl/status';
    }
    return null;
  }

  /// Gets the base URL from urlOn or urlOff.
  String? _getBaseUrl() {
    try {
      final uri = Uri.parse(urlOn);
      return '${uri.scheme}://${uri.host}:${uri.port}';
    } catch (e) {
      return null;
    }
  }

  /// Gets the registration request body.
  String _getRegistrationBody() {
    return jsonEncode({
      'deviceId': deviceId,
      'deviceName': deviceName,
      'type': 'relay',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Parses the status response to determine relay state.
  bool? _parseStatusResponse(http.Response response) {
    try {
      final data = jsonDecode(response.body);
      if (data is Map<String, dynamic>) {
        final status = data['status'];
        if (status == 'on' || status == 'ON' || status == true) {
          return true;
        } else if (status == 'off' || status == 'OFF' || status == false) {
          return false;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> dispose() async {
    _client.close();
    await super.dispose();
  }
}

/// A simplified HTTP relay controller for basic GET requests.
/// 
/// Example usage:
/// ```dart
/// final controller = SimpleHttpRelayController(
///   baseUrl: 'http://*************',
///   onEndpoint: '/relay/on',
///   offEndpoint: '/relay/off',
/// );
/// 
/// await controller.triggerOn();
/// await controller.triggerOff();
/// ```
class SimpleHttpRelayController extends HttpRelayController {
  /// Creates a simplified HTTP relay controller.
  ///
  /// [deviceId] is the unique device identifier.
  /// [baseUrl] is the base URL of the relay device.
  /// [onEndpoint] is the endpoint for turning relay ON.
  /// [offEndpoint] is the endpoint for turning relay OFF.
  /// [deviceName] is the device name/description.
  /// [headers] are additional headers to include in requests.
  /// [timeoutSeconds] is the request timeout in seconds.
  SimpleHttpRelayController({
    required super.deviceId,
    required String baseUrl,
    String onEndpoint = '/on',
    String offEndpoint = '/off',
    super.deviceName = 'Simple HTTP Relay',
    super.headers = const {},
    super.timeoutSeconds = 10,
  }) : super(
          urlOn: '$baseUrl$onEndpoint',
          urlOff: '$baseUrl$offEndpoint',
          method: HttpMethod.get,
        );
}

/// HTTP relay controller with authentication support.
/// 
/// Example usage:
/// ```dart
/// final controller = AuthenticatedHttpRelayController(
///   urlOn: 'http://*************/relay/on',
///   urlOff: 'http://*************/relay/off',
///   username: 'admin',
///   password: 'password',
/// );
/// 
/// await controller.triggerOn();
/// await controller.triggerOff();
/// ```
class AuthenticatedHttpRelayController extends HttpRelayController {
  /// Creates an HTTP relay controller with basic authentication.
  ///
  /// [deviceId] is the unique device identifier.
  /// [urlOn] is the URL for turning relay ON.
  /// [urlOff] is the URL for turning relay OFF.
  /// [username] is the username for basic authentication.
  /// [password] is the password for basic authentication.
  /// [deviceName] is the device name/description.
  /// [method] is the HTTP method to use.
  /// [additionalHeaders] are additional headers to include.
  /// [timeoutSeconds] is the request timeout in seconds.
  AuthenticatedHttpRelayController({
    required super.deviceId,
    required super.urlOn,
    required super.urlOff,
    required String username,
    required String password,
    super.deviceName = 'Authenticated HTTP Relay',
    super.method = HttpMethod.get,
    Map<String, String> additionalHeaders = const {},
    super.timeoutSeconds = 10,
  }) : super(
          headers: {
            'Authorization': 'Basic ${base64Encode(utf8.encode('$username:$password'))}',
            'Content-Type': 'application/json',
            ...additionalHeaders,
          },
        );
}

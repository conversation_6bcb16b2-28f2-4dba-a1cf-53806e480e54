# 📋 Task Management Guide

## 🎯 **Quy Tr<PERSON><PERSON>uản Lý Tasks**

### **Bước 1: <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> Bắt Đầu Task**
1. **Cập nhật status thành "🔄 In Progress"** trong Implementation Plan
2. **<PERSON><PERSON> nhận thời gian bắt đầu**
3. **Xác nhận requirements và dependencies**

### **Bước 2: Trong Quá Trình Thực Hiện**
1. **Commit code thường xuyên** với message rõ ràng
2. **Update progress notes** nếu có thay đổi scope
3. **Document issues** gặp phải trong quá trình làm

### **Bước 3: Sau <PERSON>hi Hoàn Thành Task**
1. **Cập nhật status thành "✅ Complete"**
2. **Ghi nhận thời gian hoàn thành**
3. **Update estimated vs actual hours**
4. **Add completion notes**

---

## 🔄 **Template <PERSON><PERSON><PERSON>h<PERSON>t Status**

### **<PERSON><PERSON> B<PERSON>t Đầu Task:**
```markdown
| Task ID | Component     | Description      | Priority | Status        | Estimated Hours | Assigned  |
| ------- | ------------- | ---------------- | -------- | ------------- | --------------- | --------- |
| SB-XXX  | ComponentName | Task description | HIGH     | 🔄 In Progress | Xh              | Developer |
```

### **Khi Hoàn Thành Task:**
```markdown
| Task ID | Component     | Description      | Priority | Status     | Estimated Hours | Assigned  |
| ------- | ------------- | ---------------- | -------- | ---------- | --------------- | --------- |
| SB-XXX  | ComponentName | Task description | HIGH     | ✅ Complete | Xh (Actual: Yh) | Developer |
```

---

## 📊 **Status Icons Reference**

- ⏳ **Pending**: Chưa bắt đầu
- 🔄 **In Progress**: Đang thực hiện
- ✅ **Complete**: Hoàn thành
- ❌ **Blocked**: Bị chặn, cần giải quyết dependencies
- ⚠️ **Issue**: Có vấn đề cần attention
- 🔁 **Review**: Cần review code/testing
- ⏸️ **Paused**: Tạm dừng

---

## 🎯 **Ví Dụ Thực Tế**

### **Trước khi bắt đầu SB-004:**

```markdown
### **Day 1-2: Staff Dashboard Foundation**

| Task ID | Component      | Description                                                   | Priority | Status        | Estimated Hours | Assigned |
| ------- | -------------- | ------------------------------------------------------------- | -------- | ------------- | --------------- | -------- |
| SB-001  | StaffDashboard | Create main dashboard layout with navigation                  | HIGH     | ✅ Complete    | 8h (Actual: 6h) | -        |
| SB-002  | QuickActions   | Build quick action buttons (check-in, check-out, new booking) | HIGH     | ✅ Complete    | 4h (Actual: 3h) | -        |
| SB-003  | TodayOverview  | Today's arrivals, departures, occupancy display               | HIGH     | ✅ Complete    | 6h (Actual: 5h) | -        |
| SB-004  | BookingSearch  | Quick search for existing bookings                            | MEDIUM   | 🔄 In Progress | 4h              | -        |
| SB-005  | RoomStatus     | Real-time room availability grid                              | HIGH     | ⏳ Pending     | 8h              | -        |
```

### **Sau khi hoàn thành SB-004:**

```markdown
### **Day 1-2: Staff Dashboard Foundation**

| Task ID | Component      | Description                                                   | Priority | Status     | Estimated Hours | Assigned |
| ------- | -------------- | ------------------------------------------------------------- | -------- | ---------- | --------------- | -------- |
| SB-001  | StaffDashboard | Create main dashboard layout with navigation                  | HIGH     | ✅ Complete | 8h (Actual: 6h) | -        |
| SB-002  | QuickActions   | Build quick action buttons (check-in, check-out, new booking) | HIGH     | ✅ Complete | 4h (Actual: 3h) | -        |
| SB-003  | TodayOverview  | Today's arrivals, departures, occupancy display               | HIGH     | ✅ Complete | 6h (Actual: 5h) | -        |
| SB-004  | BookingSearch  | Quick search for existing bookings                            | MEDIUM   | ✅ Complete | 4h (Actual: 4h) | -        |
| SB-005  | RoomStatus     | Real-time room availability grid                              | HIGH     | ⏳ Pending  | 8h              | -        |
```

---

## 📈 **Progress Tracking**

### **Cập Nhật Progress Summary:**

```markdown
## 📊 **PROJECT SUMMARY**

| Week      | Tasks  | Estimated Hours | Focus Area                        | Progress      |
| --------- | ------ | --------------- | --------------------------------- | ------------- |
| Week 1    | 15     | 83h             | Core Staff Booking Interface      | 4/15 (27%)    |
| Week 2    | 14     | 82h             | Admin Management Tools            | 0/14 (0%)     |
| Week 3    | 14     | 82h             | Operational Features              | 0/14 (0%)     |
| Week 4    | 12     | 68h             | Advanced Features & Polish        | 0/12 (0%)     |
| **Total** | **55** | **315h**        | **Complete Staff Booking System** | **4/55 (7%)** |
```

### **Cập Nhật Weekly Progress:**

```markdown
### **Weekly Progress**
- **Week 1**: 4/15 tasks (27%) - 🔄 In Progress
- **Week 2**: 0/14 tasks (0%) - ⏳ Pending
- **Week 3**: 0/14 tasks (0%) - ⏳ Pending
- **Week 4**: 0/12 tasks (0%) - ⏳ Pending
```

---

## 🔧 **Commands để Cập Nhật**

### **1. Cập nhật một task thành In Progress:**
```bash
# Tìm task trong file và thay đổi status
# Từ: | SB-004 | BookingSearch | ... | MEDIUM | ⏳ Pending | 4h | - |
# Thành: | SB-004 | BookingSearch | ... | MEDIUM | 🔄 In Progress | 4h | - |
```

### **2. Cập nhật task thành Complete:**
```bash
# Thay đổi status và thêm actual hours
# Từ: | SB-004 | BookingSearch | ... | MEDIUM | 🔄 In Progress | 4h | - |
# Thành: | SB-004 | BookingSearch | ... | MEDIUM | ✅ Complete | 4h (Actual: 4h) | - |
```

### **3. Cập nhật Progress Summary:**
```bash
# Tính toán lại số tasks completed và percentage
# Update cả PROJECT SUMMARY và Weekly Progress sections
```

---

## 📝 **Commit Message Convention**

### **Khi bắt đầu task:**
```bash
git commit -m "🔄 Start SB-004: BookingSearch component

- Update task status to In Progress
- Set up component structure
- Add basic search functionality"
```

### **Khi hoàn thành task:**
```bash
git commit -m "✅ Complete SB-004: BookingSearch component

- Implemented quick search for existing bookings
- Added search filters and results display
- Updated task status to Complete
- Actual time: 4h (estimated: 4h)"
```

---

## 🎯 **Checklist cho Mỗi Task**

### **Trước khi bắt đầu:**
- [ ] Đọc kỹ task description và requirements
- [ ] Check dependencies đã hoàn thành chưa
- [ ] Cập nhật status thành "🔄 In Progress"
- [ ] Commit status update

### **Trong quá trình làm:**
- [ ] Code theo best practices
- [ ] Test functionality cơ bản
- [ ] Commit progress thường xuyên
- [ ] Document any issues hoặc changes

### **Khi hoàn thành:**
- [ ] Test component thoroughly
- [ ] Cập nhật status thành "✅ Complete"
- [ ] Ghi nhận actual hours
- [ ] Update progress summary
- [ ] Commit final changes với proper message
- [ ] Commit với từng task một, không gộp nhiều task vào một commit

---

## 🚀 **Next Steps**

Sau khi hoàn thành guide này, áp dụng quy trình cho:

1. **SB-004: BookingSearch** - Cập nhật status thành In Progress
2. **Implement BookingSearch component**
3. **Cập nhật status thành Complete**
4. **SB-005: RoomStatus** - Repeat process

Quy trình này sẽ giúp tracking progress một cách chính xác và professional!

class FaceRecognitionLogs {
  final String id;
  final String edgeDeviceId;
  final String userId;
  final String deviceId;
  final String cameraId;
  final DateTime recognizedAt;
  final String imageUrl;
  final double similarityPercent;
  final String status;
  final DateTime createdAt;
  final String createdBy;

  FaceRecognitionLogs({
    required this.id,
    required this.edgeDeviceId,
    required this.userId,
    required this.deviceId,
    required this.cameraId,
    required this.recognizedAt,
    required this.imageUrl,
    required this.similarityPercent,
    required this.status,
    required this.createdAt,
    required this.createdBy,
  });
}

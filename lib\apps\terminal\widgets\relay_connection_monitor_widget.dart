import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/relay_trigger_service.dart';
import '../../../shared/services/relay_management_service.dart';
import 'package:c_face_terminal/packages/relay_controller/lib/relay_controller.dart';

/// Widget to monitor and display relay connection status with controls
class RelayConnectionMonitorWidget extends StatefulWidget {
  const RelayConnectionMonitorWidget({super.key});

  @override
  State<RelayConnectionMonitorWidget> createState() => _RelayConnectionMonitorWidgetState();
}

class _RelayConnectionMonitorWidgetState extends State<RelayConnectionMonitorWidget> {
  final RelayTriggerService _relayTriggerService = RelayTriggerService();
  final RelayManagementService _relayService = RelayManagementService.instance;
  
  StreamSubscription<RelayStatusUpdate>? _statusSubscription;
  bool _isConnected = false;
  String _lastError = '';
  DateTime? _lastConnectionTime;
  DateTime? _lastDisconnectionTime;
  int _reconnectAttempts = 0;
  
  @override
  void initState() {
    super.initState();
    _setupConnectionMonitoring();
  }
  
  @override
  void dispose() {
    _statusSubscription?.cancel();
    super.dispose();
  }
  
  void _setupConnectionMonitoring() {
    _statusSubscription = _relayService.statusUpdates.listen((status) {
      if (mounted) {
        setState(() {
          switch (status.type) {
            case RelayStatusType.connected:
              _isConnected = true;
              _lastConnectionTime = status.timestamp;
              _lastError = '';
              _reconnectAttempts = 0;
              break;
              
            case RelayStatusType.disconnected:
              _isConnected = false;
              _lastDisconnectionTime = status.timestamp;
              break;
              
            default:
              break;
          }
        });
      }
    });
    
    // Check initial status
    _isConnected = _relayService.isConnected;
  }
  
  Future<void> _testRelayConnection() async {
    try {
      setState(() {
        _lastError = '';
      });
      
      // Test basic relay control
      await _relayService.controlRelay(0, RelayAction.on);
      await Future.delayed(const Duration(milliseconds: 500));
      await _relayService.controlRelay(0, RelayAction.off);
      
      if (kDebugMode) {
        print('✅ Relay connection test successful');
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
      
      if (kDebugMode) {
        print('❌ Relay connection test failed: $e');
      }
    }
  }
  
  Future<void> _triggerEmergencyStop() async {
    try {
      await _relayTriggerService.emergencyStop();
      
      if (kDebugMode) {
        print('🚨 Emergency stop executed');
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
    }
  }
  
  Future<void> _forceReconnect() async {
    try {
      setState(() {
        _reconnectAttempts++;
        _lastError = '';
      });
      
      await _relayService.disconnect();
      await Future.delayed(const Duration(milliseconds: 1000));
      
      if (_relayService.deviceConfig != null) {
        await _relayService.initialize(
          config: _relayService.deviceConfig!,
          autoConnect: true,
        );
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  _isConnected ? Icons.usb : Icons.usb_off,
                  color: _isConnected ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Relay Connection Monitor',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _isConnected ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _isConnected ? 'CONNECTED' : 'DISCONNECTED',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Connection Info
            _buildInfoRow('Device ID', _relayService.deviceConfig?.deviceId ?? 'N/A'),
            _buildInfoRow('Device Name', _relayService.deviceConfig?.deviceName ?? 'N/A'),
            _buildInfoRow('Baud Rate', '${_relayService.deviceConfig?.baudRate ?? 'N/A'}'),
            
            if (_lastConnectionTime != null)
              _buildInfoRow('Last Connected', _formatDateTime(_lastConnectionTime!)),
              
            if (_lastDisconnectionTime != null)
              _buildInfoRow('Last Disconnected', _formatDateTime(_lastDisconnectionTime!)),
              
            if (_reconnectAttempts > 0)
              _buildInfoRow('Reconnect Attempts', '$_reconnectAttempts'),
            
            if (_lastError.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _lastError,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Control Buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isConnected ? _testRelayConnection : null,
                  icon: const Icon(Icons.play_arrow, size: 16),
                  label: const Text('Test Connection'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _forceReconnect,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Force Reconnect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _triggerEmergencyStop,
                  icon: const Icon(Icons.stop, size: 16),
                  label: const Text('Emergency Stop'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
  
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }
}

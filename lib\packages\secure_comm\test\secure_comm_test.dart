import 'package:flutter_test/flutter_test.dart';
import 'package:secure_comm/secure_comm.dart';

void main() {
  group('SecureMessage Tests', () {
    test('SecureMessage can be created and serialized', () {
      final message = SecureMessage.create(
        deviceId: 'test-device',
        type: 'test_message',
        payload: {'key': 'value'},
      );

      expect(message.deviceId, 'test-device');
      expect(message.type, 'test_message');
      expect(message.payload['key'], 'value');
      expect(message.timestamp, isA<int>());
      expect(message.signature, isNull);
    });

    test('SecureMessage can be converted to/from JSON', () {
      final message = SecureMessage.create(
        deviceId: 'test-device',
        type: 'test_message',
        payload: {'key': 'value'},
        messageId: 'msg-123',
        priority: 1,
      );

      final json = message.toJson();
      final restored = SecureMessage.fromJson(json);

      expect(restored.deviceId, message.deviceId);
      expect(restored.type, message.type);
      expect(restored.payload, message.payload);
      expect(restored.messageId, message.messageId);
      expect(restored.priority, message.priority);
    });

    test('SecureMessage can be signed', () {
      final message = SecureMessage.create(
        deviceId: 'test-device',
        type: 'test_message',
        payload: {'key': 'value'},
      );

      final signedMessage = message.withSignature('test-signature');
      
      expect(signedMessage.signature, 'test-signature');
      expect(signedMessage.deviceId, message.deviceId);
      expect(signedMessage.type, message.type);
    });

    test('SecureMessage expiration check works', () {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      final recentMessage = SecureMessage(
        deviceId: 'test-device',
        type: 'test_message',
        payload: {'key': 'value'},
        timestamp: now - 60, // 1 minute ago
      );

      final oldMessage = SecureMessage(
        deviceId: 'test-device',
        type: 'test_message',
        payload: {'key': 'value'},
        timestamp: now - 600, // 10 minutes ago
      );

      expect(recentMessage.isExpired(), false);
      expect(oldMessage.isExpired(), true);
    });
  });

  group('CryptoUtils Tests', () {
    test('generateDeviceId creates valid device IDs', () {
      final deviceId1 = CryptoUtils.generateDeviceId();
      final deviceId2 = CryptoUtils.generateDeviceId(prefix: 'test');

      expect(deviceId1, isNotEmpty);
      expect(deviceId2, startsWith('test-'));
      expect(deviceId1, isNot(equals(deviceId2)));
      expect(CryptoUtils.isValidDeviceId(deviceId1), true);
      expect(CryptoUtils.isValidDeviceId(deviceId2), true);
    });

    test('HMAC signing and verification works', () {
      const secretKey = 'test-secret-key';
      const message = 'test message to sign';

      final signature = CryptoUtils.createHmacSignature(
        secretKey: secretKey,
        message: message,
      );

      expect(signature, isNotEmpty);

      final isValid = CryptoUtils.verifyHmacSignature(
        secretKey: secretKey,
        signature: signature,
        message: message,
      );

      expect(isValid, true);

      // Test with wrong signature
      final isInvalid = CryptoUtils.verifyHmacSignature(
        secretKey: secretKey,
        signature: 'wrong-signature',
        message: message,
      );

      expect(isInvalid, false);
    });

    test('timestamp validation works', () {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final recent = now - 60; // 1 minute ago
      final old = now - 600; // 10 minutes ago

      expect(CryptoUtils.isTimestampValid(now), true);
      expect(CryptoUtils.isTimestampValid(recent), true);
      expect(CryptoUtils.isTimestampValid(old), false);
    });

    test('device fingerprint generation works', () {
      final fingerprint1 = CryptoUtils.createDeviceFingerprint(
        deviceId: 'device-001',
        deviceType: 'terminal',
        hardwareHash: 'hw-hash-1',
      );

      final fingerprint2 = CryptoUtils.createDeviceFingerprint(
        deviceId: 'device-002',
        deviceType: 'terminal',
        hardwareHash: 'hw-hash-2',
      );

      expect(fingerprint1, isNotEmpty);
      expect(fingerprint2, isNotEmpty);
      expect(fingerprint1, isNot(equals(fingerprint2)));
    });

    test('message ID generation works', () {
      final id1 = CryptoUtils.generateMessageId();
      final id2 = CryptoUtils.generateMessageId();

      expect(id1, isNotEmpty);
      expect(id2, isNotEmpty);
      expect(id1, isNot(equals(id2)));
    });

    test('validation functions work', () {
      expect(CryptoUtils.isValidDeviceId('device-001'), true);
      expect(CryptoUtils.isValidDeviceId('dev_123'), true);
      expect(CryptoUtils.isValidDeviceId(''), false);
      expect(CryptoUtils.isValidDeviceId('a'), false);
      expect(CryptoUtils.isValidDeviceId('device with spaces'), false);

      expect(CryptoUtils.isValidMessageType('face_auth'), true);
      expect(CryptoUtils.isValidMessageType('relay_control'), true);
      expect(CryptoUtils.isValidMessageType(''), false);
      expect(CryptoUtils.isValidMessageType('type with spaces'), false);
    });
  });

  group('MessageBuilder Tests', () {
    late MessageBuilder builder;

    setUp(() {
      builder = MessageBuilder(
        deviceId: 'test-device',
        secretKey: 'test-secret-key',
      );
    });

    test('buildMessage creates signed messages', () {
      final message = builder.buildMessage(
        type: 'test_type',
        payload: {'key': 'value'},
      );

      expect(message.deviceId, 'test-device');
      expect(message.type, 'test_type');
      expect(message.payload['key'], 'value');
      expect(message.signature, isNotNull);
      expect(message.signature, isNotEmpty);
    });

    test('buildFaceAuthMessage creates correct message', () {
      final message = builder.buildFaceAuthMessage(
        faceImageBase64: 'base64-image',
        userId: 'user123',
        metadata: {'confidence': 0.95},
      );

      expect(message.type, 'face_auth');
      expect(message.payload['face_image'], 'base64-image');
      expect(message.payload['user_id'], 'user123');
      expect(message.payload['confidence'], 0.95);
      expect(message.priority, 1); // High priority
    });

    test('buildRelayControlMessage creates correct message', () {
      final message = builder.buildRelayControlMessage(
        action: 'unlock',
        relayId: 'door-001',
        metadata: {'reason': 'face_auth'},
      );

      expect(message.type, 'relay_control');
      expect(message.payload['action'], 'unlock');
      expect(message.payload['relay_id'], 'door-001');
      expect(message.payload['reason'], 'face_auth');
    });

    test('buildLogMessage creates correct message', () {
      final message = builder.buildLogMessage(
        level: 'error',
        message: 'Test error',
        category: 'security',
        context: {'user_id': 'user123'},
      );

      expect(message.type, 'log');
      expect(message.payload['level'], 'error');
      expect(message.payload['message'], 'Test error');
      expect(message.payload['category'], 'security');
      expect(message.payload['context']['user_id'], 'user123');
      expect(message.priority, 2); // Error priority
    });

    test('verifyMessage works correctly', () {
      final message = builder.buildMessage(
        type: 'test_type',
        payload: {'key': 'value'},
      );

      expect(builder.verifyMessage(message), true);

      // Test with tampered message
      final tamperedMessage = message.withPayload({'key': 'tampered'});
      expect(builder.verifyMessage(tamperedMessage), false);
    });

    test('builder can be configured with different defaults', () {
      final customBuilder = builder.withDefaults(
        priority: 5,
        encryption: true,
      );

      final message = customBuilder.buildMessage(
        type: 'test_type',
        payload: {'key': 'value'},
      );

      expect(message.priority, 5);
      expect(message.encrypted, true);
    });
  });

  group('DeviceCredentials Tests', () {
    test('DeviceCredentials can be created from registration', () {
      final response = DeviceRegistrationResponse(
        accessToken: 'test-token',
        expiresIn: 3600,
        secretKey: 'test-secret',
        scopes: ['relay_control', 'face_auth'],
        endpoints: {'message': '/api/message'},
      );

      final credentials = DeviceCredentials.fromRegistration(
        deviceId: 'test-device',
        response: response,
      );

      expect(credentials.deviceId, 'test-device');
      expect(credentials.accessToken, 'test-token');
      expect(credentials.secretKey, 'test-secret');
      expect(credentials.scopes, ['relay_control', 'face_auth']);
      expect(credentials.hasScope('relay_control'), true);
      expect(credentials.hasScope('invalid_scope'), false);
      expect(credentials.getEndpoint('message'), '/api/message');
    });

    test('DeviceCredentials expiration check works', () {
      final futureTime = DateTime.now().add(Duration(hours: 1));
      final pastTime = DateTime.now().subtract(Duration(hours: 1));

      final validCredentials = DeviceCredentials(
        deviceId: 'test-device',
        accessToken: 'token',
        secretKey: 'secret',
        expiresAt: futureTime,
        scopes: [],
      );

      final expiredCredentials = DeviceCredentials(
        deviceId: 'test-device',
        accessToken: 'token',
        secretKey: 'secret',
        expiresAt: pastTime,
        scopes: [],
      );

      expect(validCredentials.isExpired, false);
      expect(expiredCredentials.isExpired, true);
    });

    test('DeviceCredentials can be updated with new token', () {
      final credentials = DeviceCredentials(
        deviceId: 'test-device',
        accessToken: 'old-token',
        secretKey: 'secret',
        expiresAt: DateTime.now().add(Duration(hours: 1)),
        scopes: ['scope1'],
      );

      final updatedCredentials = credentials.withNewToken(
        accessToken: 'new-token',
        expiresIn: 7200,
      );

      expect(updatedCredentials.accessToken, 'new-token');
      expect(updatedCredentials.secretKey, 'secret'); // Unchanged
      expect(updatedCredentials.scopes, ['scope1']); // Unchanged
      expect(updatedCredentials.deviceId, 'test-device'); // Unchanged
    });
  });

  group('SecureResponse Tests', () {
    test('SecureResponse can be created and serialized', () {
      final response = SecureResponse(
        success: true,
        data: {'result': 'ok'},
        timestamp: 1234567890,
      );

      expect(response.success, true);
      expect(response.data?['result'], 'ok');
      expect(response.error, isNull);

      final json = response.toJson();
      final restored = SecureResponse.fromJson(json);

      expect(restored.success, response.success);
      expect(restored.data, response.data);
      expect(restored.timestamp, response.timestamp);
    });

    test('SecureResponse handles error cases', () {
      final response = SecureResponse(
        success: false,
        error: 'Something went wrong',
        timestamp: 1234567890,
      );

      expect(response.success, false);
      expect(response.error, 'Something went wrong');
      expect(response.data, isNull);
    });
  });
}

import 'package:flutter/material.dart';
import '../../../shared/services/relay_api_service.dart';
import '../../../shared/services/relay_management_service.dart';
import '../../../shared/services/http_client_service.dart';
import '../../../shared/core/config/config_helper.dart';

/// Example demonstrating relay server integration with the new communication system
class RelayServerIntegrationExample extends StatefulWidget {
  const RelayServerIntegrationExample({Key? key}) : super(key: key);

  @override
  State<RelayServerIntegrationExample> createState() => _RelayServerIntegrationExampleState();
}

class _RelayServerIntegrationExampleState extends State<RelayServerIntegrationExample> {
  final RelayApiService _apiService = RelayApiService.instance;
  final RelayManagementService _managementService = RelayManagementService.instance;
  
  bool _isInitialized = false;
  String _status = 'Not initialized';
  String _deviceId = 'T-A3B4-R01';
  String _serverUrl = 'http://localhost:3000';
  
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// Initialize relay services with server communication
  Future<void> _initializeServices() async {
    try {
      setState(() => _status = 'Initializing services...');
      
      // Initialize HTTP client
      final httpClient = HttpClientService();
      httpClient.initialize(HttpClientConfig(
        baseUrl: _serverUrl,
        connectTimeout: 30000,
        receiveTimeout: 30000,
        sendTimeout: 30000,
      ));
      
      // Initialize API service
      await _apiService.initialize(httpClient);
      
      // Initialize management service
      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceId,
        deviceName: 'Terminal Door Relay',
        relayCount: 4,
        baudRate: 115200,
      );
      
      await _managementService.initialize(
        config: deviceConfig,
        autoConnect: false, // Don't auto-connect to USB device
      );
      
      setState(() {
        _isInitialized = true;
        _status = 'Services initialized successfully';
      });
      
    } catch (e) {
      setState(() => _status = 'Initialization failed: $e');
    }
  }

  /// Register device with server using secure API
  Future<void> _registerDeviceSecure() async {
    try {
      setState(() => _status = 'Registering device (secure API)...');
      
      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceId,
        deviceName: 'Terminal Door Relay',
        relayCount: 4,
        baudRate: 115200,
      );
      
      final response = await _apiService.registerDevice(
        deviceConfig: deviceConfig,
        additionalInfo: {
          'location': 'Main Entrance',
          'installation_date': DateTime.now().toIso8601String(),
        },
        useSecureApi: true,
      );
      
      setState(() => _status = 'Device registered successfully: ${response.deviceId}');
      
    } catch (e) {
      setState(() => _status = 'Registration failed: $e');
    }
  }

  /// Register device with server using legacy API
  Future<void> _registerDeviceLegacy() async {
    try {
      setState(() => _status = 'Registering device (legacy API)...');
      
      final deviceConfig = RelayDeviceConfig(
        deviceId: _deviceId,
        deviceName: 'Terminal Door Relay',
        relayCount: 4,
        baudRate: 115200,
      );
      
      final response = await _apiService.registerDevice(
        deviceConfig: deviceConfig,
        useSecureApi: false, // Use legacy API
      );
      
      setState(() => _status = 'Device registered (legacy): ${response.deviceId}');
      
    } catch (e) {
      setState(() => _status = 'Legacy registration failed: $e');
    }
  }

  /// Send relay control command
  Future<void> _controlRelay(String action) async {
    try {
      setState(() => _status = 'Sending relay command: $action...');
      
      final command = RelayCommand(
        command: action,
        relayIndex: 1,
        action: action == 'ON' ? RelayAction.on : RelayAction.off,
      );
      
      final response = await _apiService.sendRelayCommand(_deviceId, command);
      
      setState(() => _status = 'Relay command sent: ${response.success}');
      
    } catch (e) {
      setState(() => _status = 'Relay control failed: $e');
    }
  }

  /// Test face recognition
  Future<void> _testFaceRecognition() async {
    try {
      setState(() => _status = 'Testing face recognition...');
      
      // Sample base64 image data (1x1 pixel PNG)
      const sampleImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
      
      final response = await _apiService.recognizeFace(
        deviceId: _deviceId,
        imageData: sampleImageData,
        confidenceScore: 0.85,
        metadata: {
          'source': 'terminal_camera',
          'test_mode': true,
        },
      );
      
      setState(() => _status = 'Face recognition: ${response.recognized ? "Recognized" : "Not recognized"}');
      
    } catch (e) {
      setState(() => _status = 'Face recognition failed: $e');
    }
  }

  /// Send secure message
  Future<void> _sendSecureMessage() async {
    try {
      setState(() => _status = 'Sending secure message...');
      
      final response = await _apiService.sendSecureMessage(
        deviceId: _deviceId,
        messageType: 'ping',
        payload: {
          'message': 'Hello from terminal app',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      
      setState(() => _status = 'Secure message sent: ${response.success}');
      
    } catch (e) {
      setState(() => _status = 'Secure message failed: $e');
    }
  }

  /// Send plain text message
  Future<void> _sendPlainMessage() async {
    try {
      setState(() => _status = 'Sending plain message...');
      
      final response = await _apiService.sendPlainMessage(
        deviceId: _deviceId,
        messageType: 'status_update',
        payload: {
          'status': 'online',
          'last_activity': DateTime.now().toIso8601String(),
        },
      );
      
      setState(() => _status = 'Plain message sent: ${response.success}');
      
    } catch (e) {
      setState(() => _status = 'Plain message failed: $e');
    }
  }

  /// Register with server using management service
  Future<void> _registerWithServer() async {
    try {
      setState(() => _status = 'Registering with server...');
      
      await _managementService.registerWithServer(
        serverUrl: _serverUrl,
        authToken: null, // No auth token for demo
      );
      
      setState(() => _status = 'Server registration successful');
      
    } catch (e) {
      setState(() => _status = 'Server registration failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relay Server Integration'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    const SizedBox(height: 8),
                    Text('Device ID: $_deviceId'),
                    Text('Server: $_serverUrl'),
                    Text('Initialized: ${_isInitialized ? "Yes" : "No"}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Device Registration Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Device Registration',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? _registerDeviceSecure : null,
                            child: const Text('Secure API'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? _registerDeviceLegacy : null,
                            child: const Text('Legacy API'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isInitialized ? _registerWithServer : null,
                        child: const Text('Management Service'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Relay Control Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Relay Control',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? () => _controlRelay('ON') : null,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                            child: const Text('Turn ON'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? () => _controlRelay('OFF') : null,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                            child: const Text('Turn OFF'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Advanced Features Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Advanced Features',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isInitialized ? _testFaceRecognition : null,
                        child: const Text('Test Face Recognition'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? _sendSecureMessage : null,
                            child: const Text('Secure Message'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isInitialized ? _sendPlainMessage : null,
                            child: const Text('Plain Message'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import '../providers/face_detection_provider.dart';
import '../models/face_capture_result.dart';

/// Modal gallery để xem tất cả ảnh đã capture
class FaceCaptureGalleryModal extends StatefulWidget {
  final FaceCaptureResult result;
  final VoidCallback? onRetakeAll;
  final VoidCallback? onClose;

  const FaceCaptureGalleryModal({
    super.key,
    required this.result,
    this.onRetakeAll,
    this.onClose,
  });

  @override
  State<FaceCaptureGalleryModal> createState() => _FaceCaptureGalleryModalState();

  /// Static method để hiển thị modal
  static Future<void> show({
    required BuildContext context,
    required FaceCaptureResult result,
    VoidCallback? onRetakeAll,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (BuildContext context) {
        return FaceCaptureGalleryModal(
          result: result,
          onRetakeAll: onRetakeAll,
          onClose: () => Navigator.of(context).pop(),
        );
      },
    );
  }
}

class _FaceCaptureGalleryModalState extends State<FaceCaptureGalleryModal> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final directions = widget.result.capturedDirections;
    
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.photo_library,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Ảnh khuôn mặt đã chụp (${widget.result.imageCount})',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: widget.onClose,
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            
            // Image viewer
            Expanded(
              child: Column(
                children: [
                  // Main image display
                  Expanded(
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                      itemCount: directions.length,
                      itemBuilder: (context, index) {
                        final direction = directions[index];
                        final imagePath = widget.result.getImagePath(direction)!;
                        
                        return Container(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              // Direction label
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(color: Colors.blue.shade200),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      _getDirectionIcon(direction),
                                      color: Colors.blue,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      _getDirectionName(direction),
                                      style: TextStyle(
                                        color: Colors.blue.shade700,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),
                              
                              // Image
                              Expanded(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.file(
                                    File(imagePath),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade200,
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: const Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.error_outline,
                                                size: 48,
                                                color: Colors.grey,
                                              ),
                                              SizedBox(height: 8),
                                              Text(
                                                'Không thể tải ảnh',
                                                style: TextStyle(color: Colors.grey),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  
                  // Page indicator
                  if (directions.length > 1) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          directions.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: index == _currentIndex
                                  ? Colors.blue
                                  : Colors.grey.shade300,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            // Action buttons
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: widget.onClose,
                      icon: const Icon(Icons.check),
                      label: const Text('Giữ tất cả ảnh'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: const BorderSide(color: Colors.green),
                        foregroundColor: Colors.green,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        widget.onClose?.call();
                        widget.onRetakeAll?.call();
                      },
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('Chụp lại tất cả'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDirectionIcon(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return Icons.face;
      case FaceDirection.top:
        return Icons.keyboard_arrow_up;
      case FaceDirection.bottom:
        return Icons.keyboard_arrow_down;
      case FaceDirection.left:
        return Icons.keyboard_arrow_left;
      case FaceDirection.right:
        return Icons.keyboard_arrow_right;
      case FaceDirection.unknown:
        return Icons.help_outline;
    }
  }

  String _getDirectionName(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return 'Chính giữa';
      case FaceDirection.top:
        return 'Nhìn xuống';
      case FaceDirection.bottom:
        return 'Nhìn lên';
      case FaceDirection.left:
        return 'Quay phải';
      case FaceDirection.right:
        return 'Quay trái';
      case FaceDirection.unknown:
        return 'Không xác định';
    }
  }

}

class DailyAttendanceSummaries {
  final String id;
  final String userId;
  final String shiftId;
  final String holidayId;
  final DateTime workDate;
  final bool isLate;
  final bool isEarlyLeave;
  final DateTime checkinTime;
  final DateTime checkoutTime;
  final int lateMinutes;
  final int earlyLeaveMinutes;
  final int expectedWorkMinutes;
  final int totalWorkMinutes;
  final String note;
  final String createdBy;
  final DateTime createdAt;

  DailyAttendanceSummaries({
    required this.id,
    required this.userId,
    required this.shiftId,
    required this.holidayId,
    required this.workDate,
    required this.isLate,
    required this.isEarlyLeave,
    required this.checkinTime,
    required this.checkoutTime,
    required this.lateMinutes,
    required this.earlyLeaveMinutes,
    required this.expectedWorkMinutes,
    required this.totalWorkMinutes,
    required this.note,
    required this.createdBy,
    required this.createdAt,
  });
}

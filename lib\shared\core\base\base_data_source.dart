import '../errors/exceptions.dart';

/// Base abstract class cho tất cả Data Source implementations
///
/// Cung cấp common functionality và error handling
abstract class BaseDataSource {
  /// Execute operation với error handling
  Future<T> executeOperation<T>(
    Future<T> Function() operation,
  ) async {
    try {
      return await operation();
    } catch (e) {
      throw _mapErrorToException(e);
    }
  }

  /// Execute synchronous operation với error handling
  T executeSyncOperation<T>(
    T Function() operation,
  ) {
    try {
      return operation();
    } catch (e) {
      throw _mapErrorToException(e);
    }
  }

  /// Map generic error to appropriate exception
  AppException _mapErrorToException(dynamic error) {
    if (error is AppException) {
      return error;
    } else if (error is Exception) {
      return ServerException(error.toString());
    } else {
      return ServerException('Unknown error occurred: ${error.toString()}');
    }
  }
}

/// Base class cho Remote Data Sources
abstract class BaseRemoteDataSource extends BaseDataSource {
  /// Execute HTTP operation với network error handling
  Future<T> executeHttpOperation<T>(
    Future<T> Function() operation,
  ) async {
    try {
      return await executeOperation(operation);
    } on NetworkException {
      rethrow;
    } on ServerException {
      rethrow;
    } on AuthException {
      rethrow;
    } on ValidationException {
      rethrow;
    } catch (e) {
      throw NetworkException('Network error: ${e.toString()}');
    }
  }

  /// Handle HTTP response và throw appropriate exceptions
  T handleHttpResponse<T>(
    dynamic response,
    T Function(Map<String, dynamic>) parser,
  ) {
    try {
      if (response == null) {
        throw ServerException('Empty response from server');
      }

      // Handle different response types
      Map<String, dynamic> jsonData;
      
      if (response is Map<String, dynamic>) {
        jsonData = response;
      } else if (response is String) {
        // Try to parse JSON string
        try {
          // For now, assume string responses are error messages
          throw ServerException('String response received: $response');
        } catch (e) {
          throw ServerException('Failed to parse response: ${e.toString()}');
        }
      } else {
        throw ServerException('Unsupported response type: ${response.runtimeType}');
      }

      // Check for API error response
      if (jsonData.containsKey('error')) {
        final error = jsonData['error'];
        if (error is Map<String, dynamic>) {
          final message = error['message'] ?? 'Unknown error';
          final code = error['code'];
          final statusCode = error['status_code'];
          
          if (statusCode == 401 || statusCode == 403) {
            throw AuthException(message, code: code);
          } else if (statusCode == 422) {
            final fieldErrorsRaw = error['field_errors'] as Map<String, dynamic>?;
            final fieldErrors = fieldErrorsRaw?.map((key, value) =>
                MapEntry(key, value is List ? value.cast<String>() : [value.toString()]));
            throw ValidationException(message, code: code, fieldErrors: fieldErrors);
          } else {
            throw ServerException(message, code: code, statusCode: statusCode);
          }
        } else {
          throw ServerException(error.toString());
        }
      }

      // Parse successful response
      return parser(jsonData);
    } catch (e) {
      if (e is AppException) {
        rethrow;
      } else {
        throw ServerException('Failed to handle response: ${e.toString()}');
      }
    }
  }

  /// Handle paginated response
  List<T> handlePaginatedResponse<T>(
    dynamic response,
    T Function(Map<String, dynamic>) itemParser,
  ) {
    return handleHttpResponse<List<T>>(
      response,
      (json) {
        final data = json['data'];
        if (data is List) {
          return data
              .map((item) => itemParser(item as Map<String, dynamic>))
              .toList();
        } else {
          throw ServerException('Invalid paginated response format');
        }
      },
    );
  }
}

/// Base class cho Local Data Sources
abstract class BaseLocalDataSource extends BaseDataSource {
  /// Execute storage operation với cache error handling
  Future<T> executeStorageOperation<T>(
    Future<T> Function() operation,
  ) async {
    try {
      return await executeOperation(operation);
    } on CacheException {
      rethrow;
    } catch (e) {
      throw CacheException('Storage error: ${e.toString()}');
    }
  }

  /// Execute synchronous storage operation
  T executeSyncStorageOperation<T>(
    T Function() operation,
  ) {
    try {
      return executeSyncOperation(operation);
    } on CacheException {
      rethrow;
    } catch (e) {
      throw CacheException('Storage error: ${e.toString()}');
    }
  }

  /// Safe JSON parsing với error handling
  Map<String, dynamic> safeJsonParse(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) {
      throw CacheException('Empty or null JSON string');
    }

    try {
      // Assume JSON is already parsed or use appropriate JSON parsing
      // This is a placeholder - actual implementation depends on your JSON library
      throw UnimplementedError('JSON parsing not implemented');
    } catch (e) {
      throw CacheException('Failed to parse JSON: ${e.toString()}');
    }
  }

  /// Safe JSON encoding với error handling
  String safeJsonEncode(Map<String, dynamic> data) {
    try {
      // Assume JSON encoding - actual implementation depends on your JSON library
      throw UnimplementedError('JSON encoding not implemented');
    } catch (e) {
      throw CacheException('Failed to encode JSON: ${e.toString()}');
    }
  }
}

/// Mixin để thêm caching support cho data sources
mixin CachingMixin on BaseDataSource {
  /// Cache duration
  Duration get cacheDuration => const Duration(minutes: 5);

  /// Generate cache key
  String generateCacheKey(String prefix, Map<String, dynamic> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );
    final paramString = sortedParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    return '$prefix:$paramString';
  }

  /// Check if cache is expired
  bool isCacheExpired(DateTime? cacheTime) {
    if (cacheTime == null) return true;
    return DateTime.now().difference(cacheTime) > cacheDuration;
  }
}

/// Mixin để thêm validation support cho data sources
mixin ValidationMixin on BaseDataSource {
  /// Validate required fields
  void validateRequiredFields(
    Map<String, dynamic> data,
    List<String> requiredFields,
  ) {
    final missingFields = <String>[];
    
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        missingFields.add(field);
      }
    }
    
    if (missingFields.isNotEmpty) {
      throw ValidationException(
        'Missing required fields: ${missingFields.join(', ')}',
        fieldErrors: {
          for (final field in missingFields) field: ['This field is required']
        },
      );
    }
  }

  /// Validate email format
  void validateEmail(String? email) {
    if (email == null || email.isEmpty) return;
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      throw ValidationException(
        'Invalid email format',
        fieldErrors: {'email': ['Invalid email format']},
      );
    }
  }

  /// Validate phone format
  void validatePhone(String? phone) {
    if (phone == null || phone.isEmpty) return;
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
    if (!phoneRegex.hasMatch(phone)) {
      throw ValidationException(
        'Invalid phone format',
        fieldErrors: {'phone': ['Invalid phone format']},
      );
    }
  }

  /// Validate password strength
  void validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      throw ValidationException(
        'Password is required',
        fieldErrors: {'password': ['Password is required']},
      );
    }

    if (password.length < 6) {
      throw ValidationException(
        'Password must be at least 6 characters',
        fieldErrors: {'password': ['Password must be at least 6 characters']},
      );
    }
  }
}

/// Data source utilities
class DataSourceUtils {
  /// Convert query parameters to URL query string
  static String buildQueryString(Map<String, dynamic> params) {
    final filteredParams = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');

    return filteredParams.isNotEmpty ? '?$filteredParams' : '';
  }

  /// Merge default parameters with provided parameters
  static Map<String, dynamic> mergeParams(
    Map<String, dynamic> defaultParams,
    Map<String, dynamic> providedParams,
  ) {
    return {...defaultParams, ...providedParams};
  }

  /// Clean null values from parameters
  static Map<String, dynamic> cleanParams(Map<String, dynamic> params) {
    return Map.fromEntries(
      params.entries.where((entry) => entry.value != null),
    );
  }
}

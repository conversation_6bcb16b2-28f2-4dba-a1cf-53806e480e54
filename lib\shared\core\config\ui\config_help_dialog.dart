import 'package:flutter/material.dart';
import 'config_title_helper.dart';

/// Dialog hiển thị hướng dẫn chi tiết cho configuration parameter
class ConfigHelpDialog extends StatelessWidget {
  final String parameterKey;
  final String currentValue;

  const ConfigHelpDialog({
    Key? key,
    required this.parameterKey,
    required this.currentValue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final title = ConfigTitleHelper.getTitle(parameterKey);
    final description = ConfigTitleHelper.getDescription(parameterKey);
    final example = ConfigTitleHelper.getUsageExample(parameterKey);
    final category = ConfigTitleHelper.getTitleWithCategory(parameterKey);
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.help_outline, color: theme.colorScheme.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  parameterKey,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Category
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                category.split(':')[0],
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Current Value
            _buildSection(
              context,
              'Giá trị hiện tại',
              currentValue.isEmpty ? 'Chưa được thiết lập' : currentValue,
              Icons.settings,
            ),

            const SizedBox(height: 16),

            // Description
            _buildSection(
              context,
              'Mô tả',
              description,
              Icons.description,
            ),

            const SizedBox(height: 16),

            // Usage Example
            _buildSection(
              context,
              'Ví dụ sử dụng',
              example,
              Icons.code,
            ),

            const SizedBox(height: 16),

            // Tips
            _buildTipsSection(context),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Đóng'),
        ),
      ],
    );
  }

  Widget _buildSection(BuildContext context, String title, String content, IconData icon) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Text(
            content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTipsSection(BuildContext context) {
    final tips = _getTipsForParameter(parameterKey);
    final theme = Theme.of(context);
    
    if (tips.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.lightbulb_outline, size: 16, color: theme.colorScheme.tertiary),
            const SizedBox(width: 8),
            Text(
              'Lưu ý và gợi ý',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.tertiaryContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.tertiary.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: tips.map((tip) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ', 
                    style: TextStyle(
                      color: theme.colorScheme.onTertiaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      tip,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onTertiaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  List<String> _getTipsForParameter(String key) {
    switch (key) {
      case 'face_detection.min_quality_detection':
        return [
          'Giá trị thấp (0.3-0.5) giúp phát hiện nhiều khuôn mặt hơn nhưng có thể có nhiều false positive',
          'Giá trị cao (0.7-0.9) giúp chính xác hơn nhưng có thể bỏ sót khuôn mặt có chất lượng thấp',
          'Nên bắt đầu với 0.5 và điều chỉnh theo môi trường sử dụng',
        ];
      case 'face_detection.min_quality_recognition':
        return [
          'Nên cao hơn min_quality_detection để đảm bảo chỉ nhận diện khuôn mặt chất lượng tốt',
          'Giá trị khuyến nghị: 0.6-0.8',
          'Giá trị quá cao có thể làm giảm tỷ lệ nhận diện thành công',
        ];
      case 'face_detection.recognition_throttle_duration':
        return [
          'Giá trị thấp (1-3 giây) cho phản hồi nhanh nhưng tốn tài nguyên',
          'Giá trị cao (5-10 giây) tiết kiệm tài nguyên nhưng phản hồi chậm',
          'Nên cân bằng giữa trải nghiệm người dùng và hiệu suất hệ thống',
        ];
      case 'network.request_timeout':
        return [
          'Giá trị thấp (5-10 giây) cho phản hồi nhanh nhưng có thể timeout với mạng chậm',
          'Giá trị cao (30-60 giây) phù hợp với mạng không ổn định',
          'Nên kiểm tra tốc độ mạng trước khi thiết lập',
        ];
      case 'performance.cache_size_mb':
        return [
          'Cache lớn hơn giúp tăng hiệu suất nhưng tốn bộ nhớ',
          'Nên để dưới 50% RAM khả dụng',
          'Theo dõi sử dụng bộ nhớ để điều chỉnh phù hợp',
        ];
      default:
        return [];
    }
  }

  /// Show help dialog for a configuration parameter
  static void show(BuildContext context, String parameterKey, String currentValue) {
    showDialog(
      context: context,
      builder: (context) => ConfigHelpDialog(
        parameterKey: parameterKey,
        currentValue: currentValue,
      ),
    );
  }
} 
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/enhanced_face_capture_provider.dart';
import '../providers/face_detection_provider.dart';
import '../core/constants/face_cropping_constants.dart';
import '../models/enhanced_face_capture_result.dart';

/// Demo widget showing face cropping functionality integration
class FaceCroppingDemoWidget extends StatefulWidget {
  final Map<FaceDirection, String?> capturedImages;
  final Map<FaceDirection, Face> detectedFaces;
  
  const FaceCroppingDemoWidget({
    Key? key,
    required this.capturedImages,
    required this.detectedFaces,
  }) : super(key: key);
  
  @override
  State<FaceCroppingDemoWidget> createState() => _FaceCroppingDemoWidgetState();
}

class _FaceCroppingDemoWidgetState extends State<FaceCroppingDemoWidget> {
  late EnhancedFaceCaptureProvider _provider;
  
  @override
  void initState() {
    super.initState();
    _provider = EnhancedFaceCaptureProvider();
  }
  
  @override
  void dispose() {
    _provider.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<EnhancedFaceCaptureProvider>.value(
      value: _provider,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Face Cropping Demo'),
          backgroundColor: FaceCroppingConstants.processingColor,
        ),
        body: Consumer<EnhancedFaceCaptureProvider>(
          builder: (context, provider, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildConfigurationSection(provider),
                  const SizedBox(height: 20),
                  _buildProcessingSection(provider),
                  const SizedBox(height: 20),
                  _buildResultsSection(provider),
                  const SizedBox(height: 20),
                  _buildQueueStatusSection(provider),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildConfigurationSection(EnhancedFaceCaptureProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // Processing Mode Selection
            const Text('Processing Mode:', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ProcessingMode.values.map((mode) {
                return ChoiceChip(
                  label: Text(mode.name),
                  selected: provider.processingMode == mode,
                  onSelected: (selected) {
                    if (selected) {
                      provider.setProcessingMode(mode);
                    }
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Crop Padding Slider
            Text('Crop Padding: ${(provider.cropPadding * 100).toInt()}%'),
            Slider(
              value: provider.cropPadding,
              min: FaceCroppingConstants.minPadding,
              max: FaceCroppingConstants.maxPadding,
              divisions: 20,
              onChanged: provider.setCropPadding,
            ),
            
            const SizedBox(height: 16),
            
            // Output Quality Slider
            Text('Output Quality: ${provider.outputQuality}%'),
            Slider(
              value: provider.outputQuality.toDouble(),
              min: 50,
              max: 100,
              divisions: 10,
              onChanged: (value) => provider.setOutputQuality(value.toInt()),
            ),
            
            const SizedBox(height: 16),
            
            // Side Effects Selection
            const Text('Enabled Side Effects:', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: SideEffectType.values.map((effect) {
                final isEnabled = provider.enabledSideEffects.contains(effect);
                return FilterChip(
                  label: Text(effect.name),
                  selected: isEnabled,
                  onSelected: (selected) {
                    final newEffects = List<SideEffectType>.from(provider.enabledSideEffects);
                    if (selected) {
                      newEffects.add(effect);
                    } else {
                      newEffects.remove(effect);
                    }
                    provider.setEnabledSideEffects(newEffects);
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildProcessingSection(EnhancedFaceCaptureProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Processing',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // Processing Status
            if (provider.isProcessing) ...[
              LinearProgressIndicator(value: provider.processingProgress),
              const SizedBox(height: 8),
              Text(provider.processingStatus ?? 'Processing...'),
              const SizedBox(height: 16),
            ],
            
            // Action Buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: provider.isProcessing ? null : () => _processSync(provider),
                  child: const Text('Process Sync'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: provider.isProcessing ? null : () => _processAsync(provider),
                  child: const Text('Process Async'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: provider.isProcessing ? null : () => _processQueue(provider),
                  child: const Text('Process Queue'),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                TextButton(
                  onPressed: provider.reset,
                  child: const Text('Reset'),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: provider.clearLastResult,
                  child: const Text('Clear Result'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildResultsSection(EnhancedFaceCaptureProvider provider) {
    final result = provider.lastResult;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Results',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            if (result == null) ...[
              const Text('No results yet. Process some images to see results.'),
            ] else ...[
              _buildResultSummary(result),
              const SizedBox(height: 16),
              _buildResultDetails(result),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildResultSummary(EnhancedFaceCaptureResult result) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: result.success ? FaceCroppingConstants.successColor.withOpacity(0.1) : FaceCroppingConstants.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: result.success ? FaceCroppingConstants.successColor : FaceCroppingConstants.errorColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.success ? Icons.check_circle : Icons.error,
                color: result.success ? FaceCroppingConstants.successColor : FaceCroppingConstants.errorColor,
              ),
              const SizedBox(width: 8),
              Text(
                result.success ? 'Processing Successful' : 'Processing Failed',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: result.success ? FaceCroppingConstants.successColor : FaceCroppingConstants.errorColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('Original Images: ${result.imageCount}'),
          Text('Cropped Images: ${result.croppedImageCount}'),
          Text('Completion: ${result.isComplete ? 'Complete' : 'Incomplete'}'),
          Text('Cropping: ${result.isCroppingComplete ? 'Complete' : 'Incomplete'}'),
          if (result.error != null) ...[
            const SizedBox(height: 8),
            Text('Error: ${result.error}', style: const TextStyle(color: Colors.red)),
          ],
        ],
      ),
    );
  }
  
  Widget _buildResultDetails(EnhancedFaceCaptureResult result) {
    return ExpansionTile(
      title: const Text('Detailed Results'),
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('API Results Summary:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              ...result.getApiResultsSummary().entries.map((entry) {
                return Text('${entry.key}: ${entry.value}');
              }).toList(),
              
              const SizedBox(height: 16),
              
              const Text('Captured Directions:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: result.capturedDirections.map((direction) {
                  return Chip(
                    label: Text(direction.name),
                    backgroundColor: FaceCroppingConstants.successColor.withOpacity(0.2),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 16),
              
              const Text('Cropped Directions:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: result.croppedDirections.map((direction) {
                  return Chip(
                    label: Text(direction.name),
                    backgroundColor: FaceCroppingConstants.processingColor.withOpacity(0.2),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildQueueStatusSection(EnhancedFaceCaptureProvider provider) {
    final queueStatus = provider.getQueueStatus();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Queue Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            ...queueStatus.entries.where((entry) => entry.key != 'queue_items').map((entry) {
              return Text('${entry.key}: ${entry.value}');
            }).toList(),
          ],
        ),
      ),
    );
  }
  
  Future<void> _processSync(EnhancedFaceCaptureProvider provider) async {
    await provider.processSynchronous(
      capturedImages: widget.capturedImages,
      detectedFaces: widget.detectedFaces,
      context: {'demo_mode': true, 'processing_type': 'synchronous'},
    );
  }
  
  Future<void> _processAsync(EnhancedFaceCaptureProvider provider) async {
    await provider.processAsynchronous(
      capturedImages: widget.capturedImages,
      detectedFaces: widget.detectedFaces,
      context: {'demo_mode': true, 'processing_type': 'asynchronous'},
    );
  }
  
  Future<void> _processQueue(EnhancedFaceCaptureProvider provider) async {
    await provider.processQueueBased(
      capturedImages: widget.capturedImages,
      detectedFaces: widget.detectedFaces,
      context: {'demo_mode': true, 'processing_type': 'queue_based'},
    );
  }
}

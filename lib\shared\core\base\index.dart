/// Base Classes Index - Export all base classes
///
/// This file exports all base classes that provide common functionality
/// for data sources, providers, repositories, and use cases.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/shared/core/base/index.dart';
///
/// // Extend base classes
/// class UserRepository extends BaseRepository {
///   UserRepository({required super.networkInfo});
/// }
///
/// class UserProvider extends BaseProvider {
///   // Inherits loading, error, and success state management
/// }
///
/// class GetUserUseCase extends BaseUseCase<User, IdParams> {
///   @override
///   Future<Either<Failure, User>> call(IdParams params) async {
///     // Implementation
///   }
/// }
/// ```
library;

// ============================================================================
// BASE CLASSES EXPORTS
// ============================================================================

/// Base data source classes with error handling and validation
export 'base_data_source.dart' hide ValidationMixin, CachingMixin;

/// Base provider classes with state management
export 'base_provider.dart';

/// Base repository classes with network handling
export 'base_repository.dart';

/// Base use case classes with parameters and validation
export 'base_use_case.dart' hide ValidationMixin, CachingMixin;

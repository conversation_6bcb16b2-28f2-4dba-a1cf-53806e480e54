import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../shared/components/error_screen.dart';
import '../../../shared/presentation/widgets/navigation/back_navigation_wrapper.dart';

import '../../../shared/presentation/providers/base/base_auth_provider.dart';
import '../presentation/providers/auth_provider.dart';
import '../presentation/screens/splash_screen.dart';
import '../presentation/screens/login_screen.dart';
import '../presentation/screens/tenants_screen.dart';
import '../presentation/screens/tenant_create_screen.dart';
import '../presentation/screens/main_screen.dart';
import '../presentation/screens/users_screen/user_face_register_screen.dart';
import '../presentation/screens/change_password_screen.dart';
import '../presentation/screens/update_account_screen.dart';
import '../../../shared/widgets/provider_test_widget.dart';
import '../../../shared/widgets/face_lifecycle_test_widget.dart';
import '../../../shared/widgets/face_wrapper_test_widget.dart';
import '../../../shared/widgets/face_providers_wrapper.dart';

import 'mobile_route_names.dart';

/// Mobile app router configuration
class MobileRouter {
  // Private constructor to prevent instantiation
  MobileRouter._();

  // Navigator keys for different navigation contexts
  static final GlobalKey<NavigatorState> _rootNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'root');
  static final GlobalKey<NavigatorState> _shellNavigatorKey =
      GlobalKey<NavigatorState>(debugLabel: 'shell');

  /// Get the root navigator key
  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;

  /// Get the shell navigator key
  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;

  /// Create the mobile app router
  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: MobileRouteNames.splash,
      debugLogDiagnostics: kDebugMode,

      // Redirect logic based on authentication state
      redirect: (BuildContext context, GoRouterState state) {
        // Try to get AuthProvider, but handle case where it's not available yet
        AuthProvider? authProvider;
        try {
          authProvider = context.read<AuthProvider>();
        } catch (e) {
          // AuthProvider not available yet, stay on splash
          return MobileRouteNames.splash;
        }

        final currentLocation = state.uri.path;

        // Debug logging
        if (kDebugMode) {
          debugPrint(
            'Mobile Router redirect - Current: $currentLocation, Auth Status: ${authProvider.authStatus}, Is Authenticated: ${authProvider.isAuthenticated}',
          );
        }

        // Handle initial loading state - stay on splash
        if (authProvider.authStatus == AuthStatus.initial ||
            authProvider.authStatus == AuthStatus.loading) {
          if (currentLocation != MobileRouteNames.splash) {
            return MobileRouteNames.splash;
          }
          return null; // Already on splash, don't redirect
        }

        // If user is authenticated and trying to access auth routes, redirect to dashboard
        if (authProvider.isAuthenticated &&
            MobileRouteNames.isAuthRoute(currentLocation)) {
          if (kDebugMode) {
            debugPrint(
              'Redirecting authenticated user from auth route to dashboard',
            );
          }
          return MobileRouteNames.dashboard;
        }

        // If user is not authenticated and trying to access protected routes, redirect to login
        if (!authProvider.isAuthenticated &&
            MobileRouteNames.isProtectedRoute(currentLocation)) {
          if (kDebugMode) {
            debugPrint('Redirecting unauthenticated user to login');
          }
          return MobileRouteNames.login;
        }

        // If on splash and auth check is complete, redirect appropriately
        if (currentLocation == MobileRouteNames.splash &&
            authProvider.authStatus != AuthStatus.initial &&
            authProvider.authStatus != AuthStatus.loading) {
          final destination = authProvider.isAuthenticated
              ? MobileRouteNames.dashboard
              : MobileRouteNames.login;
          if (kDebugMode) {
            debugPrint(
              '🔄 Redirecting from splash to: $destination (Auth Status: ${authProvider.authStatus}, Is Authenticated: ${authProvider.isAuthenticated})',
            );
          }
          return destination;
        }

        // Additional check for authenticated users on splash
        if (currentLocation == MobileRouteNames.splash &&
            authProvider.authStatus == AuthStatus.authenticated &&
            authProvider.isAuthenticated) {
          if (kDebugMode) {
            debugPrint(
              '🚀 Force redirect authenticated user from splash to dashboard',
            );
          }
          return MobileRouteNames.dashboard;
        }

        // Allow the navigation
        return null;
      },

      // Error handling
      errorBuilder: (context, state) => ErrorScreen(
        error: state.error?.toString() ?? 'Unknown error occurred',
      ),

      routes: [
        // ======================================================================
        // SPLASH SCREEN
        // ======================================================================
        GoRoute(
          path: MobileRouteNames.splash,
          name: 'splash',
          builder: (context, state) => const SplashScreen().withBackNavigation(
            enableEdgeSwipe: false,
            enableHardwareBack: false,
          ),
        ),

        // ======================================================================
        // AUTHENTICATION ROUTES
        // ======================================================================
        GoRoute(
          path: MobileRouteNames.login,
          name: 'login',
          builder: (context, state) =>
              const LoginScreen().withRootBackNavigation(),
        ),

        GoRoute(
          path: MobileRouteNames.tenants,
          name: 'tenants',
          builder: (context, state) =>
              const TenantsScreen().withCustomBackNavigation('/login'),
        ),

        GoRoute(
          path: MobileRouteNames.tenantCreate,
          name: 'tenant-create',
          builder: (context, state) =>
              const TenantCreateScreen().withCustomBackNavigation('/tenants'),
        ),

        // ======================================================================
        // MAIN APP ROUTES WITH BOTTOM NAVIGATION
        // ======================================================================
        GoRoute(
          path: MobileRouteNames.dashboard,
          name: 'dashboard',
          builder: (context, state) => const MainScreen(),
        ),

        GoRoute(
          path: MobileRouteNames.profile,
          name: 'profile',
          builder: (context, state) => const MainScreen(),
        ),

        GoRoute(
          path: MobileRouteNames.tools,
          name: 'tools',
          builder: (context, state) => const MainScreen(),
        ),

        GoRoute(
          path: MobileRouteNames.notifications,
          name: 'notifications',
          builder: (context, state) => const MainScreen(),
        ),

        GoRoute(
          path: MobileRouteNames.changePassword,
          name: 'change-password',
          builder: (context, state) =>
              const ChangePasswordScreen().withBackNavigation(),
        ),

        GoRoute(
          path: MobileRouteNames.updateAccount,
          name: 'update-account',
          builder: (context, state) =>
              const UpdateAccountScreen().withBackNavigation(),
        ),

        // ======================================================================
        // USER MANAGEMENT ROUTES
        // ======================================================================
        GoRoute(
          path: MobileRouteNames.userFaceRegister,
          name: 'user-face-register',
          pageBuilder: (context, state) => MaterialPage<void>(
            key: state.pageKey,
            fullscreenDialog: true, // Ensure full-screen presentation
            child: const FaceProvidersWrapper(
              autoInitialize: true,
              child: UserFaceRegisterScreen(),
            ),
          ),
        ),
        // ======================================================================
        // DEVELOPMENT ROUTES
        // ======================================================================
        GoRoute(
          path: '/provider-test',
          name: 'provider-test',
          builder: (context, state) => const ProviderTestWidget(),
        ),

        GoRoute(
          path: MobileRouteNames.faceLifecycleTest,
          name: 'face-lifecycle-test',
          builder: (context, state) => const FaceLifecycleTestWidget(),
        ),

        GoRoute(
          path: MobileRouteNames.faceWrapperTest,
          name: 'face-wrapper-test',
          builder: (context, state) => const FaceWrapperTestWidget(),
        ),
 
        // ======================================================================
        // ROOT ROUTE - REDIRECT BASED ON AUTH STATE
        // ======================================================================
        GoRoute(
          path: MobileRouteNames.root,
          name: 'root',
          redirect: (context, state) {
            try {
              final authProvider = context.read<AuthProvider>();
              if (authProvider.isAuthenticated) {
                return MobileRouteNames.dashboard;
              } else {
                return MobileRouteNames.login;
              }
            } catch (e) {
              return MobileRouteNames.splash;
            }
          },
        ),
      ],
    );
  }
}

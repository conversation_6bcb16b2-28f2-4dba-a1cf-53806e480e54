import 'package:flutter/material.dart';
import '../../../core/constants/app_text_styles.dart';

class CLabel extends StatelessWidget {
  final String text;
  final bool isRequired;
  final TextStyle? style;

  const CLabel({
    super.key,
    required this.text,
    this.isRequired = false,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          text,
          style: style ?? AppTextStyles.label,
        ),
        if (isRequired)
          const Text(
            " *",
            style: AppTextStyles.requiredAsterisk,
          ),
      ],
    );
  }
}

import 'package:flutter/foundation.dart';
import '../errors/failures.dart';

/// Base class cho tất cả các Provider trong ứng dụng
///
/// Cung cấp các tính năng chung như loading state, error handling
abstract class BaseProvider extends ChangeNotifier {
  bool _isLoading = false;
  bool _isDisposed = false;
  Failure? _failure;
  String? _successMessage;

  /// Getter cho loading state
  bool get isLoading => _isLoading;

  /// Getter cho error state
  bool get hasError => _failure != null;

  /// Getter cho failure object
  Failure? get failure => _failure;

  /// Getter cho success message
  String? get successMessage => _successMessage;

  /// Getter để check nếu provider đã disposed
  bool get isDisposed => _isDisposed;

  /// Set loading state
  void setLoading(bool loading) {
    if (_isDisposed) return;
    
    _isLoading = loading;
    if (loading) {
      _failure = null; // Clear error when loading
      _successMessage = null; // Clear success message when loading
    }
    notifyListeners();
  }

  /// Set error state
  void setError(Failure failure) {
    if (_isDisposed) return;
    
    _failure = failure;
    _isLoading = false;
    _successMessage = null;
    notifyListeners();
  }

  /// Set success state
  void setSuccess([String? message]) {
    if (_isDisposed) return;
    
    _failure = null;
    _isLoading = false;
    _successMessage = message;
    notifyListeners();
  }

  /// Clear all states
  void clearStates() {
    if (_isDisposed) return;
    
    _failure = null;
    _isLoading = false;
    _successMessage = null;
    notifyListeners();
  }

  /// Clear error state
  void clearError() {
    if (_isDisposed) return;
    
    _failure = null;
    notifyListeners();
  }

  /// Clear success message
  void clearSuccessMessage() {
    if (_isDisposed) return;
    
    _successMessage = null;
    notifyListeners();
  }

  /// Execute async operation với automatic loading và error handling
  Future<T?> executeAsync<T>(
    Future<T> Function() operation, {
    String? successMessage,
    bool showLoading = true,
    Function(T result)? onSuccess,
    Function(Failure failure)? onError,
  }) async {
    try {
      if (showLoading) setLoading(true);

      final result = await operation();

      if (onSuccess != null) {
        onSuccess(result);
      } else {
        setSuccess(successMessage);
      }

      return result;
    } catch (error) {
      final failure = _mapErrorToFailure(error);
      
      if (onError != null) {
        onError(failure);
      } else {
        setError(failure);
      }
      
      return null;
    }
  }

  /// Map generic error to Failure
  Failure _mapErrorToFailure(dynamic error) {
    if (error is Failure) {
      return error;
    } else if (error is Exception) {
      return ServerFailure(error.toString());
    } else {
      return ServerFailure('Unknown error occurred: ${error.toString()}');
    }
  }

  /// Retry last failed operation
  /// Subclasses should override this if they want to support retry
  Future<void> retry() async {
    // Default implementation does nothing
    // Subclasses can override to implement retry logic
  }

  /// Reset provider to initial state
  /// Subclasses should override this to reset their specific state
  void reset() {
    clearStates();
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  @override
  void notifyListeners() {
    if (!_isDisposed) {
      super.notifyListeners();
    }
  }
}

/// Mixin để thêm pagination support cho providers
mixin PaginationMixin<T> on BaseProvider {
  List<T> _items = [];
  int _currentPage = 1;
  int _totalPages = 1;
  int _totalItems = 0;
  bool _hasMore = true;
  bool _isLoadingMore = false;

  /// Getter cho items list
  List<T> get items => List.unmodifiable(_items);

  /// Getter cho current page
  int get currentPage => _currentPage;

  /// Getter cho total pages
  int get totalPages => _totalPages;

  /// Getter cho total items
  int get totalItems => _totalItems;

  /// Getter cho has more items
  bool get hasMore => _hasMore;

  /// Getter cho loading more state
  bool get isLoadingMore => _isLoadingMore;

  /// Set loading more state
  void setLoadingMore(bool loading) {
    if (isDisposed) return;
    
    _isLoadingMore = loading;
    notifyListeners();
  }

  /// Update pagination data
  void updatePaginationData({
    required List<T> newItems,
    required int currentPage,
    required int totalPages,
    required int totalItems,
    bool append = false,
  }) {
    if (isDisposed) return;
    
    if (append) {
      _items.addAll(newItems);
    } else {
      _items = newItems;
    }
    
    _currentPage = currentPage;
    _totalPages = totalPages;
    _totalItems = totalItems;
    _hasMore = currentPage < totalPages;
    _isLoadingMore = false;
    
    notifyListeners();
  }

  /// Load next page
  Future<void> loadNextPage() async {
    if (!_hasMore || _isLoadingMore) return;
    
    setLoadingMore(true);
    // Subclasses should override this to implement actual loading
  }

  /// Refresh items (load first page)
  Future<void> refresh() async {
    _currentPage = 1;
    _hasMore = true;
    clearStates();
    // Subclasses should override this to implement actual refresh
  }

  /// Reset pagination state
  void resetPagination() {
    _items.clear();
    _currentPage = 1;
    _totalPages = 1;
    _totalItems = 0;
    _hasMore = true;
    _isLoadingMore = false;
    notifyListeners();
  }
}

/// Mixin để thêm search support cho providers
mixin SearchMixin on BaseProvider {
  String _searchQuery = '';
  bool _isSearching = false;

  /// Getter cho search query
  String get searchQuery => _searchQuery;

  /// Getter cho searching state
  bool get isSearching => _isSearching;

  /// Set search query
  void setSearchQuery(String query) {
    if (isDisposed) return;
    
    _searchQuery = query;
    notifyListeners();
  }

  /// Set searching state
  void setSearching(bool searching) {
    if (isDisposed) return;
    
    _isSearching = searching;
    notifyListeners();
  }

  /// Clear search
  void clearSearch() {
    if (isDisposed) return;
    
    _searchQuery = '';
    _isSearching = false;
    notifyListeners();
  }

  /// Perform search
  /// Subclasses should override this to implement actual search
  Future<void> search(String query) async {
    setSearchQuery(query);
    setSearching(true);
    // Subclasses should override this to implement actual search
  }
}

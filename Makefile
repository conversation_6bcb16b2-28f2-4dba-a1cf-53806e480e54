# C-Face Terminal Multi-App Makefile
# ===================================

.PHONY: help clean deps test build-mobile build-terminal build-all run-mobile run-terminal

# Default target
help:
	@echo "🏗️  C-Face Terminal Multi-App Build System"
	@echo "=========================================="
	@echo ""
	@echo "Available commands:"
	@echo "  make help          - Show this help message"
	@echo "  make clean         - Clean build artifacts"
	@echo "  make deps          - Get Flutter dependencies"
	@echo "  make test          - Test build configurations"
	@echo "  make build-mobile  - Build mobile app APK"
	@echo "  make build-terminal- Build terminal app APK"
	@echo "  make build-all     - Build both apps"
	@echo "  make run-mobile    - Run mobile app in debug mode (Android)"
	@echo "  make run-terminal  - Run terminal app in debug mode (Android)"
	@echo "  make run-mobile-web- Run mobile app on web browser"
	@echo "  make run-terminal-web- Run terminal app on web browser"
	@echo ""
	@echo "📱 Mobile App Target:   lib/apps/mobile/main_mobile.dart"
	@echo "🖥️  Terminal App Target: lib/apps/terminal/main_terminal.dart"

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	flutter clean
	@echo "✅ Clean completed!"

# Get dependencies
deps:
	@echo "📦 Getting Flutter dependencies..."
	flutter pub get
	@echo "✅ Dependencies updated!"

# Test build configurations
test:
	@echo "🧪 Testing build configurations..."
	@chmod +x scripts/test_builds.sh
	@./scripts/test_builds.sh

# Build mobile app
build-mobile:
	@echo "📱 Building mobile app..."
	@chmod +x scripts/build_mobile.sh
	@./scripts/build_mobile.sh

# Build terminal app
build-terminal:
	@echo "🖥️  Building terminal app..."
	@chmod +x scripts/build_terminal.sh
	@./scripts/build_terminal.sh

# Build all apps
build-all:
	@echo "🏗️  Building all apps..."
	@chmod +x scripts/build_all.sh
	@./scripts/build_all.sh

# Run mobile app
run-mobile:
	@echo "🚀 Running mobile app..."
	@chmod +x scripts/run_mobile.sh
	@./scripts/run_mobile.sh

# Run terminal app
run-terminal:
	@echo "🚀 Running terminal app..."
	@chmod +x scripts/run_terminal.sh
	@./scripts/run_terminal.sh

# Run mobile app on web
run-mobile-web:
	@echo "🌐 Running mobile app on web..."
	@chmod +x scripts/run_mobile_web.sh
	@./scripts/run_mobile_web.sh

# Run terminal app on web
run-terminal-web:
	@echo "🌐 Running terminal app on web..."
	@chmod +x scripts/run_terminal_web.sh
	@./scripts/run_terminal_web.sh

# Development shortcuts
dev-mobile: deps run-mobile
dev-terminal: deps run-terminal
dev-mobile-web: deps run-mobile-web
dev-terminal-web: deps run-terminal-web

# Production build shortcuts
prod-mobile: clean deps build-mobile
prod-terminal: clean deps build-terminal
prod-all: clean deps build-all

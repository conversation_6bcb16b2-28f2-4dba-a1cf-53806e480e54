import 'package:flutter/material.dart';
import 'app_theme.dart';
import 'color_schemes.dart';
import 'text_themes.dart';

/// Terminal-specific theme configuration
/// 
/// Extends the base app theme with terminal-specific customizations
/// Optimized for desktop/kiosk interactions and terminal UI patterns
class TerminalTheme {
  
  // ============================================================================
  // TERMINAL THEME CONSTANTS
  // ============================================================================
  
  static const double terminalAppBarHeight = 64.0;
  static const double terminalPadding = 24.0;
  static const double terminalCardMargin = 16.0;
  static const double terminalBorderRadius = 16.0;
  static const double terminalElevation = 4.0;
  
  // ============================================================================
  // TERMINAL LIGHT THEME
  // ============================================================================
  
  static ThemeData get lightTheme {
    final baseTheme = AppTheme.lightTheme;
    
    return baseTheme.copyWith(
      // Terminal-specific app bar
      appBarTheme: baseTheme.appBarTheme.copyWith(
        toolbarHeight: terminalAppBarHeight,
        titleSpacing: terminalPadding,
        backgroundColor: AppColorSchemes.lightColorScheme.surface,
        foregroundColor: AppColorSchemes.lightColorScheme.onSurface,
        elevation: 2.0,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
        titleTextStyle: AppTextThemes.lightTextTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w700,
        ),
      ),
      
      // Terminal-optimized cards with larger spacing
      cardTheme: baseTheme.cardTheme.copyWith(
        margin: const EdgeInsets.all(terminalCardMargin),
        elevation: terminalElevation,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
      ),
      
      // Terminal-optimized buttons with larger touch targets
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: baseTheme.elevatedButtonTheme.style?.copyWith(
          minimumSize: MaterialStateProperty.all(
            const Size(double.infinity, 56.0),
          ),
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(
              horizontal: terminalPadding,
              vertical: 16.0,
            ),
          ),
          textStyle: MaterialStateProperty.all(
            AppTextThemes.lightTextTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      
      // Terminal-optimized list tiles with larger spacing
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: terminalPadding,
          vertical: 12.0,
        ),
        minVerticalPadding: 12.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
        tileColor: AppColorSchemes.lightColorScheme.surface,
        selectedTileColor: AppColorSchemes.lightColorScheme.primaryContainer,
        iconColor: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        textColor: AppColorSchemes.lightColorScheme.onSurface,
        titleTextStyle: AppTextThemes.lightTextTheme.titleMedium,
        subtitleTextStyle: AppTextThemes.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        ),
        leadingAndTrailingTextStyle: AppTextThemes.lightTextTheme.labelLarge,
      ),
      
      // Terminal-optimized input fields
      inputDecorationTheme: baseTheme.inputDecorationTheme.copyWith(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: terminalPadding,
          vertical: 20.0,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.lightColorScheme.outline,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.lightColorScheme.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.lightColorScheme.primary,
            width: 2.0,
          ),
        ),
        labelStyle: AppTextThemes.lightTextTheme.bodyLarge,
        hintStyle: AppTextThemes.lightTextTheme.bodyLarge?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        ),
      ),
      
      // Terminal-optimized dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColorSchemes.lightColorScheme.surface,
        surfaceTintColor: AppColorSchemes.lightColorScheme.surfaceTint,
        elevation: 8.0,
        shadowColor: AppColorSchemes.lightColorScheme.shadow.withOpacity(0.2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
        titleTextStyle: AppTextThemes.lightTextTheme.headlineSmall?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurface,
        ),
        contentTextStyle: AppTextThemes.lightTextTheme.bodyLarge?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurface,
        ),
      ),
      
      // Terminal-optimized data table theme
      dataTableTheme: DataTableThemeData(
        headingRowColor: MaterialStateProperty.all(
          AppColorSchemes.lightColorScheme.surfaceVariant.withOpacity(0.5),
        ),
        dataRowColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColorSchemes.lightColorScheme.primaryContainer.withOpacity(0.3);
          }
          return null;
        }),
        headingTextStyle: AppTextThemes.lightTextTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColorSchemes.lightColorScheme.onSurfaceVariant,
        ),
        dataTextStyle: AppTextThemes.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.lightColorScheme.onSurface,
        ),
        horizontalMargin: terminalPadding,
        columnSpacing: terminalPadding,
        dividerThickness: 1.0,
      ),
    );
  }
  
  // ============================================================================
  // TERMINAL DARK THEME
  // ============================================================================
  
  static ThemeData get darkTheme {
    final baseTheme = AppTheme.darkTheme;
    
    return baseTheme.copyWith(
      // Terminal-specific app bar
      appBarTheme: baseTheme.appBarTheme.copyWith(
        toolbarHeight: terminalAppBarHeight,
        titleSpacing: terminalPadding,
        backgroundColor: AppColorSchemes.darkColorScheme.surface,
        foregroundColor: AppColorSchemes.darkColorScheme.onSurface,
        elevation: 2.0,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.3),
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
        titleTextStyle: AppTextThemes.darkTextTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w700,
        ),
      ),
      
      // Terminal-optimized cards with larger spacing
      cardTheme: baseTheme.cardTheme.copyWith(
        margin: const EdgeInsets.all(terminalCardMargin),
        elevation: terminalElevation,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.3),
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
      ),
      
      // Terminal-optimized buttons with larger touch targets
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: baseTheme.elevatedButtonTheme.style?.copyWith(
          minimumSize: MaterialStateProperty.all(
            const Size(double.infinity, 56.0),
          ),
          padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(
              horizontal: terminalPadding,
              vertical: 16.0,
            ),
          ),
          textStyle: MaterialStateProperty.all(
            AppTextThemes.darkTextTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      
      // Terminal-optimized list tiles with larger spacing
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: terminalPadding,
          vertical: 12.0,
        ),
        minVerticalPadding: 12.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
        tileColor: AppColorSchemes.darkColorScheme.surface,
        selectedTileColor: AppColorSchemes.darkColorScheme.primaryContainer,
        iconColor: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        textColor: AppColorSchemes.darkColorScheme.onSurface,
        titleTextStyle: AppTextThemes.darkTextTheme.titleMedium,
        subtitleTextStyle: AppTextThemes.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        ),
        leadingAndTrailingTextStyle: AppTextThemes.darkTextTheme.labelLarge,
      ),
      
      // Terminal-optimized input fields
      inputDecorationTheme: baseTheme.inputDecorationTheme.copyWith(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: terminalPadding,
          vertical: 20.0,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.darkColorScheme.outline,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.darkColorScheme.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
          borderSide: BorderSide(
            color: AppColorSchemes.darkColorScheme.primary,
            width: 2.0,
          ),
        ),
        labelStyle: AppTextThemes.darkTextTheme.bodyLarge,
        hintStyle: AppTextThemes.darkTextTheme.bodyLarge?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        ),
      ),
      
      // Terminal-optimized dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColorSchemes.darkColorScheme.surface,
        surfaceTintColor: AppColorSchemes.darkColorScheme.surfaceTint,
        elevation: 8.0,
        shadowColor: AppColorSchemes.darkColorScheme.shadow.withOpacity(0.4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(terminalBorderRadius),
        ),
        titleTextStyle: AppTextThemes.darkTextTheme.headlineSmall?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurface,
        ),
        contentTextStyle: AppTextThemes.darkTextTheme.bodyLarge?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurface,
        ),
      ),
      
      // Terminal-optimized data table theme
      dataTableTheme: DataTableThemeData(
        headingRowColor: MaterialStateProperty.all(
          AppColorSchemes.darkColorScheme.surfaceVariant.withOpacity(0.5),
        ),
        dataRowColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColorSchemes.darkColorScheme.primaryContainer.withOpacity(0.3);
          }
          return null;
        }),
        headingTextStyle: AppTextThemes.darkTextTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColorSchemes.darkColorScheme.onSurfaceVariant,
        ),
        dataTextStyle: AppTextThemes.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorSchemes.darkColorScheme.onSurface,
        ),
        horizontalMargin: terminalPadding,
        columnSpacing: terminalPadding,
        dividerThickness: 1.0,
      ),
    );
  }
}

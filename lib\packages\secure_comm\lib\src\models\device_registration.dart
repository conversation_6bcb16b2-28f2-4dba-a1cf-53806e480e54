/// Device registration models for secure communication
class DeviceRegistrationRequest {
  /// Unique device identifier
  final String deviceId;

  /// Device type (e.g., 'face_terminal', 'relay_controller', 'mobile_app')
  final String deviceType;

  /// Human-readable device name
  final String? deviceName;

  /// Hardware fingerprint for device verification
  final String? hardwareHash;

  /// Application version
  final String? appVersion;

  /// Device capabilities
  final List<String> capabilities;

  /// Additional device metadata
  final Map<String, dynamic> metadata;

  /// Public key for RSA encryption (optional)
  final String? publicKey;

  const DeviceRegistrationRequest({
    required this.deviceId,
    required this.deviceType,
    this.deviceName,
    this.hardwareHash,
    this.appVersion,
    this.capabilities = const [],
    this.metadata = const {},
    this.publicKey,
  });

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'device_type': deviceType,
      if (deviceName != null) 'device_name': deviceName,
      if (hardwareHash != null) 'hardware_hash': hardwareHash,
      if (appVersion != null) 'app_version': appVersion,
      'capabilities': capabilities,
      'metadata': metadata,
      if (publicKey != null) 'public_key': publicKey,
      'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
    };
  }

  factory DeviceRegistrationRequest.fromJson(Map<String, dynamic> json) {
    return DeviceRegistrationRequest(
      deviceId: json['device_id'] as String,
      deviceType: json['device_type'] as String,
      deviceName: json['device_name'] as String?,
      hardwareHash: json['hardware_hash'] as String?,
      appVersion: json['app_version'] as String?,
      capabilities: List<String>.from(json['capabilities'] as List? ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      publicKey: json['public_key'] as String?,
    );
  }
}

/// Device registration response from server
class DeviceRegistrationResponse {
  /// JWT access token
  final String accessToken;

  /// Token expiration time in seconds
  final int expiresIn;

  /// Refresh token for token renewal
  final String? refreshToken;

  /// HMAC secret key for message signing
  final String secretKey;

  /// Device permissions/scopes
  final List<String> scopes;

  /// Server endpoints for different operations
  final Map<String, String> endpoints;

  /// Additional configuration from server
  final Map<String, dynamic> config;

  const DeviceRegistrationResponse({
    required this.accessToken,
    required this.expiresIn,
    this.refreshToken,
    required this.secretKey,
    required this.scopes,
    this.endpoints = const {},
    this.config = const {},
  });

  factory DeviceRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return DeviceRegistrationResponse(
      accessToken: json['access_token'] as String,
      expiresIn: json['expires_in'] as int,
      refreshToken: json['refresh_token'] as String?,
      secretKey: json['secret_key'] as String,
      scopes: List<String>.from(json['scopes'] as List? ?? []),
      endpoints: Map<String, String>.from(json['endpoints'] as Map? ?? {}),
      config: Map<String, dynamic>.from(json['config'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'expires_in': expiresIn,
      if (refreshToken != null) 'refresh_token': refreshToken,
      'secret_key': secretKey,
      'scopes': scopes,
      'endpoints': endpoints,
      'config': config,
    };
  }
}

/// Device credentials for secure communication
class DeviceCredentials {
  /// Device ID
  final String deviceId;

  /// JWT access token
  final String accessToken;

  /// HMAC secret key
  final String secretKey;

  /// Token expiration time
  final DateTime expiresAt;

  /// Refresh token
  final String? refreshToken;

  /// Device scopes/permissions
  final List<String> scopes;

  /// Server endpoints
  final Map<String, String> endpoints;

  const DeviceCredentials({
    required this.deviceId,
    required this.accessToken,
    required this.secretKey,
    required this.expiresAt,
    this.refreshToken,
    required this.scopes,
    this.endpoints = const {},
  });

  /// Check if token is expired or about to expire
  bool get isExpired {
    final buffer = Duration(minutes: 5); // 5-minute buffer
    return DateTime.now().add(buffer).isAfter(expiresAt);
  }

  /// Check if device has specific scope
  bool hasScope(String scope) {
    return scopes.contains(scope);
  }

  /// Get endpoint URL for specific operation
  String? getEndpoint(String operation) {
    return endpoints[operation];
  }

  /// Create credentials from registration response
  factory DeviceCredentials.fromRegistration({
    required String deviceId,
    required DeviceRegistrationResponse response,
  }) {
    final expiresAt = DateTime.now().add(Duration(seconds: response.expiresIn));
    
    return DeviceCredentials(
      deviceId: deviceId,
      accessToken: response.accessToken,
      secretKey: response.secretKey,
      expiresAt: expiresAt,
      refreshToken: response.refreshToken,
      scopes: response.scopes,
      endpoints: response.endpoints,
    );
  }

  /// Create updated credentials with new token
  DeviceCredentials withNewToken({
    required String accessToken,
    required int expiresIn,
  }) {
    final expiresAt = DateTime.now().add(Duration(seconds: expiresIn));
    
    return DeviceCredentials(
      deviceId: deviceId,
      accessToken: accessToken,
      secretKey: secretKey,
      expiresAt: expiresAt,
      refreshToken: refreshToken,
      scopes: scopes,
      endpoints: endpoints,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'access_token': accessToken,
      'secret_key': secretKey,
      'expires_at': expiresAt.toIso8601String(),
      if (refreshToken != null) 'refresh_token': refreshToken,
      'scopes': scopes,
      'endpoints': endpoints,
    };
  }

  factory DeviceCredentials.fromJson(Map<String, dynamic> json) {
    return DeviceCredentials(
      deviceId: json['device_id'] as String,
      accessToken: json['access_token'] as String,
      secretKey: json['secret_key'] as String,
      expiresAt: DateTime.parse(json['expires_at'] as String),
      refreshToken: json['refresh_token'] as String?,
      scopes: List<String>.from(json['scopes'] as List),
      endpoints: Map<String, String>.from(json['endpoints'] as Map? ?? {}),
    );
  }
}

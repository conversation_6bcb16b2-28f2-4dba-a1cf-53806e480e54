<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextureView
        android:id="@+id/texture_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <com.arcsoft.arcfacedemo.widget.FaceRectView
        android:id="@+id/face_rect_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <LinearLayout
        android:layout_margin="@dimen/common_margin"
        android:orientation="vertical"
        android:layout_gravity="end|bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_margin="@dimen/common_margin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="drawRectHorizontalMirror"
            app:srcCompat="@drawable/ic_swap_horizontal" />

        <ImageView
            android:layout_margin="@dimen/common_margin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="drawRectVerticalMirror"
            app:srcCompat="@drawable/ic_swap_vertical"/>

        <ImageView
            android:layout_margin="@dimen/common_margin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="switchCamera"
            app:srcCompat="@drawable/ic_switch_camera"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/color_black_shadow"
            android:text="@string/original_preview_data"
            android:textColor="@android:color/white" />

        <com.arcsoft.arcfacedemo.util.camera.glsurface.CameraGLSurfaceView
            android:id="@+id/camera_gl_surface_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/color_black_shadow"
        android:text="@string/adapted_preview_data"
        android:textColor="@android:color/white" />

</FrameLayout>
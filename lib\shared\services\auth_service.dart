import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'http_client_service.dart';
import 'flutter_secure_storage.dart';
import 'secure_storage_keys.dart';

/// Model class cho Login Request
class LoginRequest {
  final String email;
  final String password;
  final String? deviceId;
  final String? deviceName;
  final bool rememberMe;

  const LoginRequest({
    required this.email,
    required this.password,
    this.deviceId,
    this.deviceName,
    this.rememberMe = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'password': password,
      'device_id': deviceId,
      'device_name': deviceName,
      'remember_me': rememberMe,
    };
  }

  String toJson() => jsonEncode(toMap());
}

/// Model class cho Register Request
class RegisterRequest {
  final String email;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? department;
  final Map<String, dynamic>? additionalData;

  const RegisterRequest({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.department,
    this.additionalData,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'password': password,
      'confirm_password': confirmPassword,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'department': department,
      ...?additionalData,
    };
  }

  String toJson() => jsonEncode(toMap());
}

/// Model class cho User
class User {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? department;
  final String? avatar;
  final bool isEmailVerified;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  const User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.department,
    this.avatar,
    this.isEmailVerified = false,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  String get fullName => '$firstName $lastName';

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toString() ?? '',
      email: map['email'] ?? '',
      firstName: map['first_name'] ?? '',
      lastName: map['last_name'] ?? '',
      phone: map['phone'],
      department: map['department'],
      avatar: map['avatar'],
      isEmailVerified: map['is_email_verified'] ?? false,
      isActive: map['is_active'] ?? true,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'department': department,
      'avatar': avatar,
      'is_email_verified': isEmailVerified,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  String toJson() => jsonEncode(toMap());

  @override
  String toString() {
    return 'User(id: $id, email: $email, fullName: $fullName)';
  }
}

/// Model class cho Auth Response
class AuthResponse {
  final String accessToken;
  final String? refreshToken;
  final User user;
  final int? expiresIn;
  final String tokenType;

  const AuthResponse({
    required this.accessToken,
    this.refreshToken,
    required this.user,
    this.expiresIn,
    this.tokenType = 'Bearer',
  });

  factory AuthResponse.fromMap(Map<String, dynamic> map) {
    return AuthResponse(
      accessToken: map['access_token'] ?? '',
      refreshToken: map['refresh_token'],
      user: User.fromMap(map['user'] ?? {}),
      expiresIn: map['expires_in'],
      tokenType: map['token_type'] ?? 'Bearer',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'user': user.toMap(),
      'expires_in': expiresIn,
      'token_type': tokenType,
    };
  }

  String toJson() => jsonEncode(toMap());
}

/// Enum cho trạng thái authentication
enum AuthStatus {
  unknown,
  authenticated,
  unauthenticated,
  loading,
}

/// Service class để xử lý authentication APIs
class AuthService {
  static AuthService? _instance;
  final HttpClientService _httpClient = HttpClientService();
  final SecureStorageService _secureStorage = SecureStorageService();

  User? _currentUser;
  AuthStatus _authStatus = AuthStatus.unknown;

  AuthService._internal();

  /// Singleton factory
  factory AuthService() {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  /// Getters
  User? get currentUser => _currentUser;
  AuthStatus get authStatus => _authStatus;
  bool get isAuthenticated => _authStatus == AuthStatus.authenticated;
  bool get isLoading => _authStatus == AuthStatus.loading;

  /// Khởi tạo auth service và kiểm tra trạng thái đăng nhập
  Future<void> initialize() async {
    try {
      _authStatus = AuthStatus.loading;

      final hasToken = await _httpClient.hasAuthToken();
      if (hasToken) {
        // Thử lấy thông tin user hiện tại để verify token
        final userResponse = await getCurrentUser();
        if (userResponse.success && userResponse.data != null) {
          _currentUser = userResponse.data;
          _authStatus = AuthStatus.authenticated;
        } else {
          // Token không hợp lệ, xóa và set unauthenticated
          await _clearAuthData();
          _authStatus = AuthStatus.unauthenticated;
        }
      } else {
        _authStatus = AuthStatus.unauthenticated;
      }
    } catch (e) {
      debugPrint('Lỗi khi khởi tạo auth service: $e');
      _authStatus = AuthStatus.unauthenticated;
    }
  }

  /// Đăng nhập
  Future<ApiResponse<AuthResponse>> login(LoginRequest request) async {
    try {
      _authStatus = AuthStatus.loading;

      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/login',
        data: request.toMap(),
      );

      if (response.success && response.data != null) {
        final authResponse = AuthResponse.fromMap(response.data!);

        // Lưu tokens
        await _httpClient.saveAuthTokens(
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
        );

        // Lưu user info
        await _saveUserInfo(authResponse.user);

        _currentUser = authResponse.user;
        _authStatus = AuthStatus.authenticated;

        return ApiResponse.success(
          data: authResponse,
          message: response.message ?? 'Đăng nhập thành công',
          statusCode: response.statusCode,
        );
      } else {
        _authStatus = AuthStatus.unauthenticated;
        return ApiResponse.error(
          message: response.message ?? 'Đăng nhập thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      _authStatus = AuthStatus.unauthenticated;
      return ApiResponse.error(
        message: 'Lỗi khi đăng nhập: $e',
        statusCode: 0,
      );
    }
  }

  /// Đăng ký
  Future<ApiResponse<AuthResponse>> register(RegisterRequest request) async {
    try {
      _authStatus = AuthStatus.loading;

      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/register',
        data: request.toMap(),
      );

      if (response.success && response.data != null) {
        final authResponse = AuthResponse.fromMap(response.data!);

        // Lưu tokens
        await _httpClient.saveAuthTokens(
          accessToken: authResponse.accessToken,
          refreshToken: authResponse.refreshToken,
        );

        // Lưu user info
        await _saveUserInfo(authResponse.user);

        _currentUser = authResponse.user;
        _authStatus = AuthStatus.authenticated;

        return ApiResponse.success(
          data: authResponse,
          message: response.message ?? 'Đăng ký thành công',
          statusCode: response.statusCode,
        );
      } else {
        _authStatus = AuthStatus.unauthenticated;
        return ApiResponse.error(
          message: response.message ?? 'Đăng ký thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      _authStatus = AuthStatus.unauthenticated;
      return ApiResponse.error(
        message: 'Lỗi khi đăng ký: $e',
        statusCode: 0,
      );
    }
  }

  /// Đăng xuất
  Future<ApiResponse<bool>> logout() async {
    try {
      // Gọi API logout (optional)
      await _httpClient.post('/auth/logout');
    } catch (e) {
      debugPrint('Lỗi khi gọi API logout: $e');
    } finally {
      // Luôn clear local data dù API có lỗi
      await _clearAuthData();
      _currentUser = null;
      _authStatus = AuthStatus.unauthenticated;
    }

    return ApiResponse.success(
      data: true,
      message: 'Đăng xuất thành công',
    );
  }

  /// Lấy thông tin user hiện tại
  Future<ApiResponse<User>> getCurrentUser() async {
    try {
      final response = await _httpClient.get<Map<String, dynamic>>('/auth/me');

      if (response.success && response.data != null) {
        final user = User.fromMap(response.data!);
        _currentUser = user;
        await _saveUserInfo(user);

        return ApiResponse.success(
          data: user,
          message: response.message,
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Không thể lấy thông tin user',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi lấy thông tin user: $e',
        statusCode: 0,
      );
    }
  }

  /// Cập nhật thông tin user
  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await _httpClient.put<Map<String, dynamic>>(
        '/auth/profile',
        data: data,
      );

      if (response.success && response.data != null) {
        final user = User.fromMap(response.data!);
        _currentUser = user;
        await _saveUserInfo(user);

        return ApiResponse.success(
          data: user,
          message: response.message ?? 'Cập nhật thông tin thành công',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Cập nhật thông tin thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi cập nhật thông tin: $e',
        statusCode: 0,
      );
    }
  }

  /// Đổi mật khẩu
  Future<ApiResponse<bool>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/change-password',
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
          'confirm_password': confirmPassword,
        },
      );

      if (response.success) {
        return ApiResponse.success(
          data: true,
          message: response.message ?? 'Đổi mật khẩu thành công',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Đổi mật khẩu thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi đổi mật khẩu: $e',
        statusCode: 0,
      );
    }
  }

  /// Quên mật khẩu
  Future<ApiResponse<bool>> forgotPassword(String email) async {
    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/forgot-password',
        data: {'email': email},
      );

      if (response.success) {
        return ApiResponse.success(
          data: true,
          message: response.message ?? 'Email reset password đã được gửi',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Gửi email reset password thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi gửi email reset password: $e',
        statusCode: 0,
      );
    }
  }

  /// Reset mật khẩu
  Future<ApiResponse<bool>> resetPassword({
    required String token,
    required String email,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/reset-password',
        data: {
          'token': token,
          'email': email,
          'password': newPassword,
          'confirm_password': confirmPassword,
        },
      );

      if (response.success) {
        return ApiResponse.success(
          data: true,
          message: response.message ?? 'Reset mật khẩu thành công',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Reset mật khẩu thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi reset mật khẩu: $e',
        statusCode: 0,
      );
    }
  }

  /// Xác thực email
  Future<ApiResponse<bool>> verifyEmail(String token) async {
    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/verify-email',
        data: {'token': token},
      );

      if (response.success) {
        // Cập nhật trạng thái email verified cho user hiện tại
        if (_currentUser != null) {
          _currentUser = User(
            id: _currentUser!.id,
            email: _currentUser!.email,
            firstName: _currentUser!.firstName,
            lastName: _currentUser!.lastName,
            phone: _currentUser!.phone,
            department: _currentUser!.department,
            avatar: _currentUser!.avatar,
            isEmailVerified: true,
            isActive: _currentUser!.isActive,
            createdAt: _currentUser!.createdAt,
            updatedAt: _currentUser!.updatedAt,
            metadata: _currentUser!.metadata,
          );
          await _saveUserInfo(_currentUser!);
        }

        return ApiResponse.success(
          data: true,
          message: response.message ?? 'Xác thực email thành công',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Xác thực email thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi xác thực email: $e',
        statusCode: 0,
      );
    }
  }

  /// Gửi lại email xác thực
  Future<ApiResponse<bool>> resendVerificationEmail() async {
    try {
      final response = await _httpClient.post<Map<String, dynamic>>(
        '/auth/resend-verification',
      );

      if (response.success) {
        return ApiResponse.success(
          data: true,
          message: response.message ?? 'Email xác thực đã được gửi lại',
          statusCode: response.statusCode,
        );
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Gửi lại email xác thực thất bại',
          statusCode: response.statusCode,
          errors: response.errors,
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Lỗi khi gửi lại email xác thực: $e',
        statusCode: 0,
      );
    }
  }

  // ========== PRIVATE HELPER METHODS ==========

  /// Lưu thông tin user vào secure storage
  Future<void> _saveUserInfo(User user) async {
    try {
      await _secureStorage.write(
        key: SecureStorageKeys.userId,
        value: user.id,
      );
      await _secureStorage.write(
        key: SecureStorageKeys.username,
        value: user.fullName,
      );
      await _secureStorage.write(
        key: SecureStorageKeys.userEmail,
        value: user.email,
      );

      // Lưu toàn bộ user data dưới dạng JSON
      await _secureStorage.write(
        key: 'user_data',
        value: user.toJson(),
      );
    } catch (e) {
      debugPrint('Lỗi khi lưu user info: $e');
    }
  }

  /// Xóa tất cả dữ liệu authentication
  Future<void> _clearAuthData() async {
    try {
      await _httpClient.clearAuthTokens();
      await _secureStorage.delete(key: SecureStorageKeys.userId);
      await _secureStorage.delete(key: SecureStorageKeys.username);
      await _secureStorage.delete(key: SecureStorageKeys.userEmail);
      await _secureStorage.delete(key: 'user_data');
    } catch (e) {
      debugPrint('Lỗi khi xóa auth data: $e');
    }
  }

  // ========== UTILITY METHODS ==========

  /// Kiểm tra xem user có cần xác thực email không
  bool get needsEmailVerification =>
      _currentUser != null && !_currentUser!.isEmailVerified;

  /// Kiểm tra xem user có active không
  bool get isUserActive => _currentUser?.isActive ?? false;

  /// Lấy access token hiện tại
  Future<String?> getAccessToken() => _httpClient.getAccessToken();

  /// Refresh user data từ server
  Future<void> refreshUserData() async {
    if (isAuthenticated) {
      await getCurrentUser();
    }
  }

  /// Clear instance (để testing)
  static void clearInstance() {
    _instance = null;
  }
}
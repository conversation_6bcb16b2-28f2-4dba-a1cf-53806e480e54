<?xml version="1.0" encoding="utf-8"?>
<!--
    Network Security Configuration for C-CAM Terminal App

    This configuration allows HTTP cleartext traffic to specific endpoints:
    - Primary API Server: http://************
    - Local development servers
    - Private network ranges

    IMPORTANT: This is required for terminal deployment where the API server
    uses HTTP instead of HTTPS for local network communication.
-->
<network-security-config>
    <!-- Allow cleartext traffic for local development and terminal communication -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Local development servers -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>

        <!-- Terminal API server - SPECIFIC IP:PORT -->
        <domain includeSubdomains="false">************</domain>

        <!-- Local network ranges for terminal deployment -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">10.0.0.0</domain>
        <domain includeSubdomains="true">**********</domain>

        <!-- Development tunnels -->
        <domain includeSubdomains="true">ngrok.io</domain>
        <domain includeSubdomains="true">ngrok-free.app</domain>
    </domain-config>
    
    <!-- Base configuration for HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust system CAs -->
            <certificates src="system"/>
            <!-- Trust user-added CAs for development -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug overrides for development builds -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>

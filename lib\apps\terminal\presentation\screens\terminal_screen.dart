import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/device_registration_provider.dart';
import '../../services/device_command_handler.dart';
import '../widgets/device_status_widget.dart';
import '../widgets/command_history_widget.dart';
import '../widgets/manual_command_widget.dart';
import '../widgets/server_communication_widget.dart';
import 'device_registration_screen.dart';

class TerminalScreen extends StatefulWidget {
  const TerminalScreen({super.key});

  @override
  State<TerminalScreen> createState() => _TerminalScreenState();
}

class _TerminalScreenState extends State<TerminalScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DeviceCommandHandler? _commandHandler;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeProviders();
  }

  Future<void> _initializeProviders() async {
    final registrationProvider = Provider.of<DeviceRegistrationProvider>(
      context,
      listen: false,
    );
    
    await registrationProvider.initialize();
    
    // Initialize command handler if device is registered
    if (registrationProvider.isRegistered && registrationProvider.secureComm != null) {
      _commandHandler = DeviceCommandHandler();
      await _commandHandler!.initialize(registrationProvider.secureComm!);
      
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commandHandler?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Terminal'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<DeviceRegistrationProvider>(
            builder: (context, provider, child) {
              return PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, provider),
                itemBuilder: (context) => [
                  if (!provider.isRegistered)
                    const PopupMenuItem(
                      value: 'register',
                      child: ListTile(
                        leading: Icon(Icons.app_registration),
                        title: Text('Register Device'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  if (provider.isRegistered)
                    const PopupMenuItem(
                      value: 'unregister',
                      child: ListTile(
                        leading: Icon(Icons.logout),
                        title: Text('Unregister Device'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'refresh',
                    child: ListTile(
                      leading: Icon(Icons.refresh),
                      title: Text('Refresh Status'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings),
                      title: Text('Settings'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'Dashboard',
            ),
            Tab(
              icon: Icon(Icons.terminal),
              text: 'Commands',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'History',
            ),
            Tab(
              icon: Icon(Icons.router),
              text: 'Server',
            ),
          ],
        ),
      ),
      body: Consumer<DeviceRegistrationProvider>(
        builder: (context, provider, child) {
          if (provider.status == DeviceRegistrationStatus.unregistered) {
            return _buildUnregisteredView(provider);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildDashboardTab(provider),
              _buildCommandsTab(),
              _buildHistoryTab(),
              _buildServerTab(provider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildServerTab(DeviceRegistrationProvider provider) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_off,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'Server Tab',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Server functionality disabled for new API',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnregisteredView(DeviceRegistrationProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.security,
              size: 120,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 32),
            Text(
              'Device Not Registered',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'This terminal needs to be registered with the security server before it can be used for secure communication and access control.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 200,
              height: 48,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToRegistration(),
                icon: const Icon(Icons.app_registration),
                label: const Text('Register Device'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            DeviceStatusWidget(
              showDetails: false,
              onTap: () => _navigateToRegistration(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardTab(DeviceRegistrationProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Device Status
          DeviceStatusWidget(
            onTap: () => _showDeviceDetails(provider),
          ),
          const SizedBox(height: 16),

          // Quick Actions
          _buildQuickActionsCard(),
          const SizedBox(height: 16),

          // System Information
          _buildSystemInfoCard(),
          const SizedBox(height: 16),

          // Recent Activity
          if (_commandHandler != null)
            _buildRecentActivityCard(),
        ],
      ),
    );
  }

  Widget _buildCommandsTab() {
    if (_commandHandler == null) {
      return const Center(
        child: Text('Command handler not initialized'),
      );
    }

    return ChangeNotifierProvider.value(
      value: _commandHandler!,
      child: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: ManualCommandWidget(),
      ),
    );
  }

  Widget _buildHistoryTab() {
    if (_commandHandler == null) {
      return const Center(
        child: Text('Command handler not initialized'),
      );
    }

    return ChangeNotifierProvider.value(
      value: _commandHandler!,
      child: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: CommandHistoryWidget(),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_commandHandler != null)
              ChangeNotifierProvider.value(
                value: _commandHandler!,
                child: Consumer<DeviceCommandHandler>(
                  builder: (context, commandHandler, child) {
                    return Wrap(
                      spacing: 12,
                      runSpacing: 12,
                      children: [
                        _buildQuickActionButton(
                          'Unlock Door',
                          Icons.lock_open,
                          Colors.green,
                          () => commandHandler.unlockDoor(),
                        ),
                        _buildQuickActionButton(
                          'Lock Door',
                          Icons.lock,
                          Colors.red,
                          () => commandHandler.lockDoor(),
                        ),
                        _buildQuickActionButton(
                          'Check Status',
                          Icons.info,
                          Colors.blue,
                          () => commandHandler.getRelayStatus(),
                        ),
                        _buildQuickActionButton(
                          'Send Heartbeat',
                          Icons.favorite,
                          Colors.pink,
                          () => commandHandler.sendHeartbeat(),
                        ),
                      ],
                    );
                  },
                ),
              )
            else
              Text(
                'Command handler not available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    Color color,
    Future<CommandResult> Function() action,
  ) {
    return ElevatedButton.icon(
      onPressed: () => _executeQuickAction(action, label),
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildSystemInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.computer,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'System Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Platform', 'Flutter Terminal'),
            _buildInfoRow('Version', '1.0.0'),
            _buildInfoRow('Build', 'Debug'),
            _buildInfoRow('Uptime', _getUptime()),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _tabController.animateTo(2),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ChangeNotifierProvider.value(
              value: _commandHandler!,
              child: Consumer<DeviceCommandHandler>(
                builder: (context, commandHandler, child) {
                  final recentCommands = commandHandler.commandHistory.take(3).toList();
                  
                  if (recentCommands.isEmpty) {
                    return Text(
                      'No recent activity',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    );
                  }

                  return Column(
                    children: recentCommands.map((entry) {
                      return ListTile(
                        leading: Icon(
                          entry.result.success ? Icons.check_circle : Icons.error,
                          color: entry.result.success ? Colors.green : Colors.red,
                        ),
                        title: Text(entry.commandType.replaceAll('_', ' ').toUpperCase()),
                        subtitle: Text(entry.result.message),
                        trailing: Text(
                          _formatTime(entry.timestamp),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        contentPadding: EdgeInsets.zero,
                      );
                    }).toList(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Future<void> _executeQuickAction(
    Future<CommandResult> Function() action,
    String actionName,
  ) async {
    try {
      final result = await action();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$actionName: ${result.message}'),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$actionName failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action, DeviceRegistrationProvider provider) {
    switch (action) {
      case 'register':
        _navigateToRegistration();
        break;
      case 'unregister':
        _showUnregisterConfirmation(provider);
        break;
      case 'refresh':
        provider.sendHeartbeat();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _navigateToRegistration() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DeviceRegistrationScreen(),
      ),
    ).then((_) {
      // Reinitialize after registration
      _initializeProviders();
    });
  }

  void _showUnregisterConfirmation(DeviceRegistrationProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unregister Device'),
        content: const Text(
          'Are you sure you want to unregister this device? This will revoke all credentials and disable secure communication.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await provider.unregisterDevice();
              _commandHandler?.dispose();
              _commandHandler = null;
              setState(() {});
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unregister'),
          ),
        ],
      ),
    );
  }

  void _showDeviceDetails(DeviceRegistrationProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Details'),
        content: SingleChildScrollView(
          child: DeviceStatusWidget(showDetails: true),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings screen not implemented yet'),
      ),
    );
  }

  String _getUptime() {
    // Simple uptime calculation - in real app, track actual start time
    return '${DateTime.now().hour}:${DateTime.now().minute.toString().padLeft(2, '0')}';
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}

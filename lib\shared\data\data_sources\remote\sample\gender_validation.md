# Gender Field Validation

## API Requirements

The `gender` field in Create User and Update User APIs must be one of the following values:
- `"male"`
- `"female"`
- `"other"`

## Implementation Details

### Frontend Display vs API Values

**Frontend Display (Vietnamese):**
- "Nam" → API value: `"male"`
- "Nữ" → API value: `"female"`
- "Khác" → API value: `"other"`

### Code Implementation

#### 1. User Selection (UserDetailScreen)
```dart
void _selectGender() {
  // Show Vietnamese options to user
  ListTile(
    title: const Text('Nam'),
    onTap: () {
      setState(() {
        _selectedGender = 'male'; // Store API value
      });
    },
  ),
  // ... other options
}
```

#### 2. Display Conversion
```dart
String? _getGenderDisplayText(String? genderValue) {
  switch (genderValue) {
    case 'male': return 'Nam';
    case 'female': return 'Nữ';
    case 'other': return 'Khác';
    default: return null;
  }
}
```

#### 3. API Request
```dart
final requestBody = {
  // ... other fields
  if (_selectedGender != null)
    'gender': _selectedGender!, // Already in API format
};
```

## Error Handling

If an invalid gender value is sent, the API will return:
```json
{
  "success": false,
  "error": {
    "message": "Gender must be one of: male, female, other",
    "code": "VALIDATION_ERROR"
  }
}
```

## Testing

### Valid Test Cases
```json
{ "gender": "male" }    // ✅ Valid
{ "gender": "female" }  // ✅ Valid
{ "gender": "other" }   // ✅ Valid
{ "gender": null }      // ✅ Valid (optional field)
```

### Invalid Test Cases
```json
{ "gender": "nam" }     // ❌ Invalid
{ "gender": "nữ" }      // ❌ Invalid
{ "gender": "Male" }    // ❌ Invalid (case sensitive)
{ "gender": "" }        // ❌ Invalid (empty string)
```

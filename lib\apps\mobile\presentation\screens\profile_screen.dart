import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/widgets/authenticated_image.dart';
import '../providers/auth_provider.dart';
import '../../routes/mobile_route_names.dart';

/// Profile screen - màn hình tài khoản người dùng
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4FBFF), // bg-[#f4fbff]
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Text(
            'Tài khoản',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: const Color(0xFF15171A),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          _buildUserCard(),
          SizedBox(height: AppDimensions.spacing16),
          _buildAccountSection(),
          SizedBox(height: AppDimensions.spacing16),
          _buildLogoutButton(),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildUserCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Handle loading state
        if (authProvider.isLoading) {
          return Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              border: Border.all(color: const Color(0xFFE5E6E7)),
            ),
            child: Row(
              children: [
                // Avatar skeleton
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[300],
                  ),
                ),
                SizedBox(width: AppDimensions.spacing8),
                // Text skeleton
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 16,
                        width: 120,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacing4),
                      Container(
                        height: 14,
                        width: 160,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        // Handle error state
        if (authProvider.hasError) {
          return Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: const Color(0xFFFFEBEE),
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              border: Border.all(color: const Color(0xFFE03E59)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Color(0xFFE03E59),
                  size: 24,
                ),
                SizedBox(width: AppDimensions.spacing8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lỗi tải thông tin',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFE03E59),
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacing4),
                      Text(
                        'Không thể tải thông tin người dùng',
                        style: AppTextStyles.caption.copyWith(
                          color: const Color(0xFFE03E59),
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Retry loading user data
                    authProvider.retry();
                  },
                  child: Text(
                    'Thử lại',
                    style: AppTextStyles.caption.copyWith(
                      color: const Color(0xFFE03E59),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        final user = authProvider.currentUser;
        final userName = user?.name ?? 'Người dùng';
        final userEmail = user?.email ?? 'Chưa có email';

        return Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            border: Border.all(color: const Color(0xFFE5E6E7)),
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                  color: AppColors.border,
                ),
                child: user?.avatar != null && user!.avatar!.isNotEmpty
                    ? AuthenticatedImage(
                        imageId: user.avatar!,
                        width: 56,
                        height: 56,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(28), // Circular border
                        placeholder: const Icon(
                          Icons.person,
                          size: 32,
                          color: Colors.grey,
                        ),
                      )
                    : const Icon(
                        Icons.person,
                        size: 32,
                        color: Colors.grey,
                      ),
              ),
              SizedBox(width: AppDimensions.spacing8),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: AppTextStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2329),
                      ),
                    ),
                    SizedBox(height: AppDimensions.spacing4),
                    Text(
                      userEmail,
                      style: AppTextStyles.caption.copyWith(
                        color: const Color(0xFF8F959E),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Tài khoản',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: const Color(0xFF8F959E),
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),

        // Account options
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            border: Border.all(color: const Color(0xFFE5E6E7)),
          ),
          child: Column(
            children: [
              _buildAccountItem(
                title: 'Cập nhật tài khoản',
                onTap: () => _updateAccount(),
              ),
              _buildDivider(),
              _buildAccountItem(
                title: 'Đổi mật khẩu',
                onTap: () => _changePassword(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2329),
                ),
              ),
            ),
            const Icon(
              Icons.chevron_right,
              size: 16,
              color: Color(0xFF1F2329),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      height: 0.5,
      color: const Color(0xFFE5E6E7),
    );
  }

  Widget _buildLogoutButton() {
    return GestureDetector(
      onTap: () => _logout(),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          border: Border.all(color: const Color(0xFFE5E6E7)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                'Đăng xuất',
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFFE03E59),
                ),
              ),
            ),
            const Icon(
              Icons.chevron_right,
              size: 16,
              color: Color(0xFFE03E59),
            ),
          ],
        ),
      ),
    );
  }

  void _updateAccount() {
    context.push(MobileRouteNames.updateAccount);
  }

  void _changePassword() {
    context.push(MobileRouteNames.changePassword);
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đăng xuất'),
        content: const Text('Bạn có chắc chắn muốn đăng xuất?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              final goRouter = GoRouter.of(context);
              final authProvider = context.read<AuthProvider>();

              navigator.pop();

              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              try {
                // Perform logout
                await authProvider.logout();

                // Close loading dialog
                if (mounted) navigator.pop();

                // Navigate to login screen and clear all routes
                if (mounted) {
                  goRouter.go('/login');
                }
              } catch (e) {
                // Close loading dialog
                if (mounted) navigator.pop();

                // Show error message
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Lỗi đăng xuất: $e'),
                      backgroundColor: const Color(0xFFE03E59),
                    ),
                  );
                }
              }
            },
            child: const Text(
              'Đăng xuất',
              style: TextStyle(color: Color(0xFFE03E59)),
            ),
          ),
        ],
      ),
    );
  }
}
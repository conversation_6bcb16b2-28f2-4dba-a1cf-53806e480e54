import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

final GetIt getIt = GetIt.instance;

/// Mobile Navigation Module
/// 
/// This module registers mobile-specific navigation dependencies
/// including router configuration, navigation services, and
/// mobile-specific route handling.
/// 
/// Note: This module will be fully implemented when navigation
/// layer is migrated and mobile routes are defined.
void registerMobileNavigationDependencies() {
  // TODO: Implement when mobile navigation is migrated
  // This is a placeholder to prevent import errors
  
  // Example of what will be registered:
  // getIt.registerLazySingleton<GoRouter>(
  //   () => createMobileRouter(),
  // );
  // 
  // getIt.registerLazySingleton<MobileNavigationService>(
  //   () => MobileNavigationServiceImpl(getIt<GoRouter>()),
  // );
}

/// Unregister mobile navigation dependencies (for testing)
void unregisterMobileNavigationDependencies() {
  // TODO: Implement when navigation dependencies are added
  // if (getIt.isRegistered<GoRouter>()) {
  //   getIt.unregister<GoRouter>();
  // }
  // if (getIt.isRegistered<MobileNavigationService>()) {
  //   getIt.unregister<MobileNavigationService>();
  // }
}

/// Reset mobile navigation module (clear và re-register)
void resetMobileNavigationModule() {
  unregisterMobileNavigationDependencies();
  registerMobileNavigationDependencies();
}

/// Check if mobile navigation dependencies are registered
bool areMobileNavigationDependenciesRegistered() {
  // TODO: Implement proper checks when dependencies are added
  // return getIt.isRegistered<GoRouter>() &&
  //        getIt.isRegistered<MobileNavigationService>();
  return true; // Placeholder
}

/// Get mobile navigation dependencies for debugging
Map<String, bool> getMobileNavigationDependenciesStatus() {
  return {
    // TODO: Add actual dependencies when implemented
    'placeholder': true,
    // 'GoRouter': getIt.isRegistered<GoRouter>(),
    // 'MobileNavigationService': getIt.isRegistered<MobileNavigationService>(),
  };
}

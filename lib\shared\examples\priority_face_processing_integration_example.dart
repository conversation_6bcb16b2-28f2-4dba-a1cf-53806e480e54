import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../providers/priority_face_processing_provider.dart';
import '../services/integrated_face_processing_service.dart';
import '../services/priority_face_processing_queue.dart';
import '../widgets/priority_face_processing_widget.dart';
import '../core/constants/face_cropping_constants.dart';

/// Ví dụ tích hợp hoàn chỉnh cho hệ thống xử lý khuôn mặt ưu tiên
/// 
/// Hệ thống này:
/// 1. Chỉ xử lý 1 khuôn mặt tốt nhất tại 1 thời điểm
/// 2. Ưu tiên khuôn mặt lớn/gần nhất với chất lượng cao
/// 3. Xử lý tuần tự: Detect → Crop → API → Side Effects
/// 4. Giảm tải thiết bị bằng cách tr<PERSON>h xử lý đồng thời
class PriorityFaceProcessingIntegrationExample extends StatefulWidget {
  const PriorityFaceProcessingIntegrationExample({Key? key}) : super(key: key);
  
  @override
  State<PriorityFaceProcessingIntegrationExample> createState() => 
      _PriorityFaceProcessingIntegrationExampleState();
}

class _PriorityFaceProcessingIntegrationExampleState 
    extends State<PriorityFaceProcessingIntegrationExample> {
  
  late PriorityFaceProcessingProvider _priorityProvider;
  late IntegratedFaceProcessingService _integratedService;
  
  // Demo state
  bool _isDemoMode = true;
  List<String> _processedTasks = [];
  Map<String, bool> _taskResults = {};
  
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }
  
  @override
  void dispose() {
    _integratedService.dispose();
    _priorityProvider.dispose();
    super.dispose();
  }
  
  /// Khởi tạo các service
  Future<void> _initializeServices() async {
    _priorityProvider = PriorityFaceProcessingProvider();
    _integratedService = IntegratedFaceProcessingService();
    
    // Cấu hình cho hiệu suất tối ưu
    _priorityProvider.setMinFaceQuality(0.7); // Chất lượng cao (70%)
    _priorityProvider.setFaceDetectionCooldown(const Duration(milliseconds: 1500)); // 1.5s cooldown
    
    // Cấu hình queue để giảm tải
    _priorityProvider.configureQueue(
      maxQueueSize: 5, // Chỉ 5 task tối đa
      processingTimeout: const Duration(seconds: 20), // 20s timeout
    );
    
    // Khởi tạo integrated service
    await _integratedService.initialize(
      priorityProvider: _priorityProvider,
      faceDetectionProvider: context.read<FaceDetectionProvider>(),
      autoProcessingEnabled: true,
      autoProcessingInterval: const Duration(milliseconds: 2000), // 2s interval
      minFaceQualityThreshold: 0.7,
    );
    
    debugPrint('🎯 Priority face processing services initialized');
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Xử lý khuôn mặt ưu tiên'),
        backgroundColor: FaceCroppingConstants.processingColor,
        actions: [
          IconButton(
            icon: Icon(_isDemoMode ? Icons.play_arrow : Icons.stop),
            onPressed: () {
              setState(() {
                _isDemoMode = !_isDemoMode;
              });
              
              if (_isDemoMode) {
                _startDemoMode();
              } else {
                _stopDemoMode();
              }
            },
          ),
        ],
      ),
      body: MultiProvider(
        providers: [
          ChangeNotifierProvider<PriorityFaceProcessingProvider>.value(
            value: _priorityProvider,
          ),
        ],
        child: PriorityFaceProcessingWidget(
          autoProcess: true,
          defaultDirection: FaceDirection.front,
          context: {
            'demo_mode': _isDemoMode,
            'integration_example': true,
          },
          enabledSideEffects: const [
            SideEffectType.auditLog,
            SideEffectType.databaseUpdate,
            SideEffectType.pushNotification,
          ],
          onTaskAdded: _onTaskAdded,
          onTaskCompleted: _onTaskCompleted,
          child: Column(
            children: [
              // Camera preview area (placeholder)
              Expanded(
                flex: 3,
                child: _buildCameraPreviewArea(),
              ),
              
              // Control panel
              Expanded(
                flex: 1,
                child: _buildControlPanel(),
              ),
              
              // Task history
              Expanded(
                flex: 1,
                child: _buildTaskHistory(),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// Xây dựng khu vực camera preview
  Widget _buildCameraPreviewArea() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey),
      ),
      child: Consumer<PriorityFaceProcessingProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              // Camera preview placeholder
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      size: 64,
                      color: Colors.white54,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Camera Preview',
                      style: TextStyle(
                        color: Colors.white54,
                        fontSize: 18,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Hệ thống sẽ tự động detect và xử lý\nkhuôn mặt tốt nhất',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white38,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Face detection overlay
              if (_isDemoMode)
                _buildFaceDetectionOverlay(),
              
              // Processing status
              if (provider.isProcessing)
                _buildProcessingStatusOverlay(provider),
            ],
          );
        },
      ),
    );
  }
  
  /// Xây dựng overlay hiển thị face detection
  Widget _buildFaceDetectionOverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: FaceDetectionOverlayPainter(),
      ),
    );
  }
  
  /// Xây dựng overlay trạng thái xử lý
  Widget _buildProcessingStatusOverlay(PriorityFaceProcessingProvider provider) {
    return Positioned(
      top: 16,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: FaceCroppingConstants.processingColor.withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Đang xử lý khuôn mặt ưu tiên...',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (provider.currentStatus != null)
                    Text(
                      _getStatusText(provider.currentStatus!),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Xây dựng panel điều khiển
  Widget _buildControlPanel() {
    return Consumer<PriorityFaceProcessingProvider>(
      builder: (context, provider, child) {
        final statistics = provider.getStatistics();
        
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.settings),
                    const SizedBox(width: 8),
                    const Text(
                      'Điều khiển xử lý ưu tiên',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: provider.isEnabled,
                      onChanged: provider.setEnabled,
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Statistics
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatCard('Đã xử lý', statistics['total_processed'].toString(), Colors.green),
                    _buildStatCard('Thất bại', statistics['total_failed'].toString(), Colors.red),
                    _buildStatCard('Tỷ lệ thành công', '${statistics['success_rate_percent']}%', Colors.blue),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Quality threshold
                Row(
                  children: [
                    const Text('Chất lượng tối thiểu: '),
                    Text(
                      '${(provider.minFaceQuality * 100).toInt()}%',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Slider(
                  value: provider.minFaceQuality,
                  min: 0.3,
                  max: 0.9,
                  divisions: 12,
                  onChanged: provider.setMinFaceQuality,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  
  /// Xây dựng lịch sử task
  Widget _buildTaskHistory() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Lịch sử xử lý',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: _processedTasks.isEmpty
                  ? const Center(
                      child: Text(
                        'Chưa có task nào được xử lý',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _processedTasks.length,
                      itemBuilder: (context, index) {
                        final taskId = _processedTasks[index];
                        final success = _taskResults[taskId] ?? false;
                        
                        return ListTile(
                          dense: true,
                          leading: Icon(
                            success ? Icons.check_circle : Icons.error,
                            color: success ? Colors.green : Colors.red,
                            size: 20,
                          ),
                          title: Text(
                            'Task ${index + 1}',
                            style: const TextStyle(fontSize: 14),
                          ),
                          subtitle: Text(
                            taskId,
                            style: const TextStyle(fontSize: 12),
                          ),
                          trailing: Text(
                            success ? 'Thành công' : 'Thất bại',
                            style: TextStyle(
                              fontSize: 12,
                              color: success ? Colors.green : Colors.red,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Xây dựng card thống kê
  Widget _buildStatCard(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
  
  /// Lấy text hiển thị cho trạng thái
  String _getStatusText(FaceProcessingStatus status) {
    switch (status) {
      case FaceProcessingStatus.queued:
        return 'Đang chờ trong hàng đợi...';
      case FaceProcessingStatus.processing:
        return 'Đang khởi tạo xử lý...';
      case FaceProcessingStatus.cropping:
        return 'Đang cắt khuôn mặt...';
      case FaceProcessingStatus.apiProcessing:
        return 'Đang gửi API xác minh...';
      case FaceProcessingStatus.sideEffects:
        return 'Đang thực thi side effects...';
      case FaceProcessingStatus.completed:
        return 'Hoàn thành xử lý';
      case FaceProcessingStatus.failed:
        return 'Xử lý thất bại';
    }
  }
  
  /// Callback khi task được thêm
  void _onTaskAdded(String taskId) {
    debugPrint('🎯 Task added: $taskId');
    
    setState(() {
      if (!_processedTasks.contains(taskId)) {
        _processedTasks.insert(0, taskId);
        // Giữ tối đa 10 task trong lịch sử
        if (_processedTasks.length > 10) {
          final removedTask = _processedTasks.removeLast();
          _taskResults.remove(removedTask);
        }
      }
    });
  }
  
  /// Callback khi task hoàn thành
  void _onTaskCompleted(String taskId, bool success) {
    debugPrint('🎯 Task completed: $taskId, Success: $success');
    
    setState(() {
      _taskResults[taskId] = success;
    });
    
    // Hiển thị snackbar
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'Xử lý khuôn mặt thành công!' 
                : 'Xử lý khuôn mặt thất bại',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
  
  /// Bắt đầu demo mode
  void _startDemoMode() {
    debugPrint('🎯 Demo mode started');
    _integratedService.configure(autoProcessingEnabled: true);
  }
  
  /// Dừng demo mode
  void _stopDemoMode() {
    debugPrint('🎯 Demo mode stopped');
    _integratedService.configure(autoProcessingEnabled: false);
  }
}

/// Custom painter cho face detection overlay
class FaceDetectionOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    // Vẽ một hình chữ nhật giả lập face detection
    final rect = Rect.fromCenter(
      center: Offset(size.width * 0.5, size.height * 0.4),
      width: size.width * 0.4,
      height: size.height * 0.5,
    );
    
    canvas.drawRect(rect, paint);
    
    // Vẽ text "Best Face"
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'Best Face',
        style: TextStyle(
          color: Colors.green,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(rect.left, rect.top - 20),
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ActivityLog {
  final String id;
  final String userId;
  final String action;
  final String resourceType;
  final String? resourceId;
  final String description;
  final Map<String, dynamic>? metadata;
  final String? ipAddress;
  final String? userAgent;
  final String createdBy;
  final DateTime createdAt;

  ActivityLog({
    required this.id,
    required this.userId,
    required this.action,
    required this.resourceType,
    this.resourceId,
    required this.description,
    this.metadata,
    this.ipAddress,
    this.userAgent,
    required this.createdBy,
    required this.createdAt,
  });
}

import 'dart:typed_data';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
// import 'package:tflite_flutter/tflite_flutter.dart'; // Temporarily disabled
// import 'package:image/image.dart' as img;

import '../detection_engine.dart';

// REAL TensorFlow Lite imports (NO MORE MOCK)
import 'package:tflite_flutter/tflite_flutter.dart';

/// UltraFace TensorFlow Lite detection engine
/// Optimized for Telpo F8 RK3399 hardware
class UltraFaceEngine implements DetectionEngine {
  static const String _modelAssetPath = 'lib/packages/face_recognition/assets/models/ultraface_320.tflite';
  static const int _inputWidth = 320;
  static const int _inputHeight = 240;
  static const int _inputChannels = 3;
  static const double _defaultConfidenceThreshold = 0.7;
  
  Interpreter? _interpreter;
  bool _isInitialized = false;
  
  // Performance tracking
  int _totalFramesProcessed = 0;
  double _totalProcessingTime = 0.0;
  DateTime _lastFrameTime = DateTime.now();
  double _currentFPS = 0.0;
  
  // Configuration
  double _confidenceThreshold = _defaultConfidenceThreshold;
  int _maxFaces = 3;
  
  @override
  String get name => 'UltraFace';
  
  @override
  String get version => '1.0.0';
  
  @override
  bool get isInitialized => _isInitialized;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🚀 Initializing UltraFace Engine...');
      }
      
      // Load model from assets
      final interpreterOptions = InterpreterOptions();
      
      // Optimize for Telpo F8 RK3399
      if (await _isGPUAvailable()) {
        interpreterOptions.addDelegate(GpuDelegateV2());
        if (kDebugMode) {
          print('✅ GPU acceleration enabled');
        }
      }
      
      // Use NNAPI if available on RK3399 (commented out for now due to compatibility)
      // if (await _isNNAPIAvailable()) {
      //   interpreterOptions.addDelegate(NnApiDelegate());
      //   if (kDebugMode) {
      //     print('✅ NNAPI acceleration enabled');
      //   }
      // }
      
      // Set thread count for RK3399 (dual-core optimization)
      interpreterOptions.threads = 2;
      
      // Load model (mock implementation)
      try {
        _interpreter = await Interpreter.fromAsset(
          _modelAssetPath,
          options: interpreterOptions,
        );
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ TensorFlow Lite disabled - using mock implementation');
        }
        // Continue with mock implementation
      }
      
      // Verify model input/output shapes
      final inputShape = _interpreter!.getInputTensor(0).shape;
      final outputShape = _interpreter!.getOutputTensor(0).shape;
      
      if (kDebugMode) {
        print('📊 Model input shape: $inputShape');
        print('📊 Model output shape: $outputShape');
      }
      
      // Validate expected shapes
      if (inputShape[1] != _inputHeight || 
          inputShape[2] != _inputWidth || 
          inputShape[3] != _inputChannels) {
        throw Exception('Invalid model input shape: expected [1, $_inputHeight, $_inputWidth, $_inputChannels], got $inputShape');
      }
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ UltraFace Engine initialized successfully');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize UltraFace Engine: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<List<DetectedFace>> detectFaces(CameraImage image) async {
    if (!_isInitialized || _interpreter == null) {
      throw Exception('UltraFace Engine not initialized');
    }
    
    final startTime = DateTime.now();
    
    try {
      // Convert CameraImage to RGB bytes
      final rgbBytes = _convertCameraImageToRGB(image);
      
      // Detect faces
      final faces = await detectFacesFromBytes(
        rgbBytes,
        image.width,
        image.height,
      );
      
      // Update performance stats
      _updatePerformanceStats(startTime);
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ UltraFace detection failed: $e');
      }
      return [];
    }
  }
  
  @override
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  ) async {
    if (!_isInitialized || _interpreter == null) {
      throw Exception('UltraFace Engine not initialized');
    }
    
    try {
      // Preprocess image
      final inputTensor = _preprocessImage(bytes, width, height);
      
      // Run inference
      final outputTensor = List.filled(
        _interpreter!.getOutputTensor(0).shape.reduce((a, b) => a * b),
        0.0,
      ); // Mock implementation - reshape not needed
      
      _interpreter!.run(inputTensor, outputTensor);
      
      // Post-process results
      final faces = _postprocessResults(outputTensor, width, height);
      
      return faces;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ UltraFace inference failed: $e');
      }
      return [];
    }
  }
  
  @override
  Future<void> dispose() async {
    if (_interpreter != null) {
      _interpreter!.close();
      _interpreter = null;
    }
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ UltraFace Engine disposed');
    }
  }
  
  @override
  EngineStats getStats() {
    final avgProcessingTime = _totalFramesProcessed > 0 
        ? _totalProcessingTime / _totalFramesProcessed 
        : 0.0;
        
    return EngineStats(
      engineName: name,
      totalFramesProcessed: _totalFramesProcessed,
      averageProcessingTime: avgProcessingTime,
      currentFPS: _currentFPS,
      memoryUsageMB: _getMemoryUsage(),
      lastUpdated: DateTime.now(),
    );
  }
  
  /// Configure detection parameters
  void configure({
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    if (confidenceThreshold != null) {
      _confidenceThreshold = confidenceThreshold;
    }
    if (maxFaces != null) {
      _maxFaces = maxFaces;
    }
  }
  
  // Private helper methods
  
  Future<bool> _isGPUAvailable() async {
    try {
      // Check if GPU delegate is available
      // final delegate = GpuDelegateV2(); // Mock implementation
      return true; // Assume available for now
    } catch (e) {
      return false;
    }
  }
  
  Future<bool> _isNNAPIAvailable() async {
    try {
      // Check if NNAPI is available (Android 8.1+ on RK3399)
      return defaultTargetPlatform == TargetPlatform.android;
    } catch (e) {
      return false;
    }
  }
  
  Uint8List _convertCameraImageToRGB(CameraImage image) {
    // Convert YUV420 to RGB
    final int width = image.width;
    final int height = image.height;
    final int uvRowStride = image.planes[1].bytesPerRow;
    final int uvPixelStride = image.planes[1].bytesPerPixel!;
    
    final rgbBytes = Uint8List(width * height * 3);
    
    // YUV to RGB conversion (simplified)
    // This is a performance-critical section for Telpo F8
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final int yIndex = y * width + x;
        final int uvIndex = (y ~/ 2) * uvRowStride + (x ~/ 2) * uvPixelStride;
        
        final int yValue = image.planes[0].bytes[yIndex];
        final int uValue = image.planes[1].bytes[uvIndex];
        final int vValue = image.planes[2].bytes[uvIndex];
        
        // YUV to RGB conversion
        final int r = (yValue + 1.402 * (vValue - 128)).round().clamp(0, 255);
        final int g = (yValue - 0.344 * (uValue - 128) - 0.714 * (vValue - 128)).round().clamp(0, 255);
        final int b = (yValue + 1.772 * (uValue - 128)).round().clamp(0, 255);
        
        final int rgbIndex = yIndex * 3;
        rgbBytes[rgbIndex] = r;
        rgbBytes[rgbIndex + 1] = g;
        rgbBytes[rgbIndex + 2] = b;
      }
    }
    
    return rgbBytes;
  }
  
  List<List<List<List<double>>>> _preprocessImage(
    Uint8List bytes,
    int width,
    int height,
  ) {
    // Resize to model input size and normalize
    final resizedBytes = _resizeImage(bytes, width, height, _inputWidth, _inputHeight);
    
    // Convert to tensor format [1, height, width, channels]
    final input = List.generate(
      1,
      (b) => List.generate(
        _inputHeight,
        (y) => List.generate(
          _inputWidth,
          (x) => List.generate(
            _inputChannels,
            (c) {
              final index = (y * _inputWidth + x) * _inputChannels + c;
              // Normalize to [-1, 1] range
              return (resizedBytes[index] / 127.5) - 1.0;
            },
          ),
        ),
      ),
    );
    
    return input;
  }
  
  Uint8List _resizeImage(Uint8List bytes, int width, int height, int newWidth, int newHeight) {
    // Simple bilinear interpolation resize
    final resized = Uint8List(newWidth * newHeight * 3);
    
    final xRatio = width / newWidth;
    final yRatio = height / newHeight;
    
    for (int y = 0; y < newHeight; y++) {
      for (int x = 0; x < newWidth; x++) {
        final srcX = (x * xRatio).floor();
        final srcY = (y * yRatio).floor();
        
        final srcIndex = (srcY * width + srcX) * 3;
        final dstIndex = (y * newWidth + x) * 3;
        
        if (srcIndex + 2 < bytes.length && dstIndex + 2 < resized.length) {
          resized[dstIndex] = bytes[srcIndex];
          resized[dstIndex + 1] = bytes[srcIndex + 1];
          resized[dstIndex + 2] = bytes[srcIndex + 2];
        }
      }
    }
    
    return resized;
  }
  
  List<DetectedFace> _postprocessResults(
    List<dynamic> outputTensor,
    int originalWidth,
    int originalHeight,
  ) {
    final faces = <DetectedFace>[];
    
    // UltraFace output format: [batch, num_detections, 4 + 1 + num_classes]
    // [x1, y1, x2, y2, confidence, ...]
    
    try {
      final detections = outputTensor[0] as List;
      
      for (final detection in detections) {
        final det = detection as List<double>;
        
        if (det.length >= 5) {
          final confidence = det[4];
          
          if (confidence >= _confidenceThreshold) {
            // Convert normalized coordinates to pixel coordinates
            final x1 = (det[0] * originalWidth).clamp(0.0, originalWidth.toDouble());
            final y1 = (det[1] * originalHeight).clamp(0.0, originalHeight.toDouble());
            final x2 = (det[2] * originalWidth).clamp(0.0, originalWidth.toDouble());
            final y2 = (det[3] * originalHeight).clamp(0.0, originalHeight.toDouble());
            
            final width = x2 - x1;
            final height = y2 - y1;
            
            if (width > 0 && height > 0) {
              faces.add(DetectedFace(
                boundingBox: Rect(
                  left: x1,
                  top: y1,
                  width: width,
                  height: height,
                ),
                confidence: confidence,
              ));
            }
          }
        }
      }
      
      // Sort by confidence and limit to maxFaces
      faces.sort((a, b) => b.confidence.compareTo(a.confidence));
      return faces.take(_maxFaces).toList();
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to postprocess UltraFace results: $e');
      }
      return [];
    }
  }
  
  void _updatePerformanceStats(DateTime startTime) {
    final processingTime = DateTime.now().difference(startTime).inMicroseconds / 1000.0;
    
    _totalFramesProcessed++;
    _totalProcessingTime += processingTime;
    
    // Calculate FPS
    final now = DateTime.now();
    final timeDiff = now.difference(_lastFrameTime).inMicroseconds / 1000000.0;
    if (timeDiff > 0) {
      _currentFPS = 1.0 / timeDiff;
    }
    _lastFrameTime = now;
  }
  
  int _getMemoryUsage() {
    // Estimate memory usage (simplified)
    return 50; // MB - placeholder
  }
}

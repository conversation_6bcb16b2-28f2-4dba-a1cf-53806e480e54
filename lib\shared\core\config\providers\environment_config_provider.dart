/// Environment Variables Configuration Provider
/// 
/// Loads configuration from environment variables with support for
/// different data types and validation.

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../flexible_config_system.dart';
import '../config_parameters_registry.dart';

/// Environment variables configuration provider
class EnvironmentConfigProvider implements ConfigProvider {
  final String prefix;
  final bool enableAutoRefresh;
  
  Map<String, dynamic> _currentConfig = {};
  final StreamController<Map<String, dynamic>> _configController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  Timer? _refreshTimer;

  EnvironmentConfigProvider({
    this.prefix = 'CCAM_',
    this.enableAutoRefresh = false,
  });

  @override
  String get name => 'EnvironmentConfigProvider';

  @override
  ConfigSource get source => ConfigSource.environment;

  @override
  Stream<Map<String, dynamic>> get configStream => _configController.stream;

  @override
  Future<void> initialize() async {
    await _loadConfiguration();
    
    if (enableAutoRefresh) {
      _startAutoRefresh();
    }
  }

  @override
  Future<Map<String, dynamic>> loadConfiguration() async {
    await _loadConfiguration();
    return Map<String, dynamic>.from(_currentConfig);
  }

  @override
  Future<void> saveConfiguration(Map<String, dynamic> config) async {
    throw UnsupportedError('Cannot save configuration to environment variables');
  }

  @override
  Future<void> dispose() async {
    _refreshTimer?.cancel();
    await _configController.close();
  }

  /// Load configuration from environment variables
  Future<void> _loadConfiguration() async {
    final newConfig = <String, dynamic>{};
    
    try {
      // Get all registered parameters
      final parameters = ConfigParametersRegistry.getAllParameters();
      
      for (final parameter in parameters.values) {
        final envKey = parameter.environmentKey;
        if (envKey == null) continue;
        
        // Try with prefix first, then without prefix
        String? envValue = Platform.environment['$prefix$envKey'] ?? 
                          Platform.environment[envKey];
        
        if (envValue != null && envValue.isNotEmpty) {
          try {
            final parsedValue = _parseEnvironmentValue(envValue, parameter);
            if (parameter.isValid(parsedValue)) {
              newConfig[parameter.key] = parsedValue;
            } else {
              debugPrint('Invalid environment value for ${parameter.key}: $envValue');
            }
          } catch (e) {
            debugPrint('Error parsing environment value for ${parameter.key}: $e');
          }
        }
      }
      
      // Check if configuration changed
      if (!_mapsEqual(_currentConfig, newConfig)) {
        _currentConfig = newConfig;
        _configController.add(_currentConfig);
        debugPrint('Environment configuration loaded: ${newConfig.keys.length} parameters');
      }
      
    } catch (e) {
      debugPrint('Error loading environment configuration: $e');
    }
  }

  /// Parse environment value based on parameter type
  dynamic _parseEnvironmentValue(String value, ConfigParameter parameter) {
    try {
      return parameter.parseValue(value);
    } catch (e) {
      // Fallback parsing for common types
      switch (parameter.type) {
        case ConfigValueType.string:
          return value;
        case ConfigValueType.integer:
          return int.parse(value);
        case ConfigValueType.double:
          return double.parse(value);
        case ConfigValueType.boolean:
          return value.toLowerCase() == 'true' || value == '1';
        case ConfigValueType.duration:
          return Duration(milliseconds: int.parse(value));
        case ConfigValueType.color:
          return int.parse(value.replaceFirst('#', ''), radix: 16);
        case ConfigValueType.list:
          return value.split(',').map((e) => e.trim()).toList();
        case ConfigValueType.map:
          // Simple key=value pairs separated by semicolons
          final map = <String, String>{};
          for (final pair in value.split(';')) {
            final parts = pair.split('=');
            if (parts.length == 2) {
              map[parts[0].trim()] = parts[1].trim();
            }
          }
          return map;
      }
    }
  }

  /// Start auto refresh timer
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(Duration(minutes: 1), (timer) async {
      await _loadConfiguration();
    });
  }

  /// Check if two maps are equal
  bool _mapsEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.length != map2.length) return false;
    
    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }
    
    return true;
  }

  /// Get all available environment variables with prefix
  Map<String, String> getAllEnvironmentVariables() {
    final env = Platform.environment;
    final filtered = <String, String>{};
    
    for (final entry in env.entries) {
      if (entry.key.startsWith(prefix)) {
        filtered[entry.key] = entry.value;
      }
    }
    
    return filtered;
  }

  /// Get environment variable value
  String? getEnvironmentVariable(String key) {
    return Platform.environment['$prefix$key'] ?? Platform.environment[key];
  }

  /// Check if environment variable exists
  bool hasEnvironmentVariable(String key) {
    return Platform.environment.containsKey('$prefix$key') || 
           Platform.environment.containsKey(key);
  }

  /// Get environment variables for a specific category
  Map<String, String> getEnvironmentVariablesByCategory(String category) {
    final parameters = ConfigParametersRegistry.getParametersByCategory(category);
    final envVars = <String, String>{};
    
    for (final parameter in parameters.values) {
      final envKey = parameter.environmentKey;
      if (envKey != null) {
        final value = getEnvironmentVariable(envKey);
        if (value != null) {
          envVars[parameter.key] = value;
        }
      }
    }
    
    return envVars;
  }

  /// Validate all environment variables
  List<String> validateEnvironmentVariables() {
    final errors = <String>[];
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final parameter in parameters.values) {
      final envKey = parameter.environmentKey;
      if (envKey == null) continue;
      
      final envValue = getEnvironmentVariable(envKey);
      if (envValue == null) {
        if (parameter.isRequired) {
          errors.add('Required environment variable missing: $envKey');
        }
        continue;
      }
      
      try {
        final parsedValue = _parseEnvironmentValue(envValue, parameter);
        if (!parameter.isValid(parsedValue)) {
          errors.add('Invalid environment variable value for $envKey: $envValue');
        }
      } catch (e) {
        errors.add('Error parsing environment variable $envKey: $e');
      }
    }
    
    return errors;
  }

  /// Generate environment variables template
  String generateEnvironmentTemplate() {
    final buffer = StringBuffer();
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    // Group by category
    final categories = <String, List<ConfigParameter>>{};
    for (final parameter in parameters.values) {
      if (parameter.environmentKey != null) {
        categories.putIfAbsent(parameter.category, () => []).add(parameter);
      }
    }
    
    buffer.writeln('# Configuration Environment Variables Template');
    buffer.writeln('# Generated on ${DateTime.now().toIso8601String()}');
    buffer.writeln();
    
    for (final category in categories.keys) {
      buffer.writeln('# ${category.toUpperCase()} CONFIGURATION');
      buffer.writeln('# ${'=' * 50}');
      
      for (final parameter in categories[category]!) {
        buffer.writeln();
        buffer.writeln('# ${parameter.description}');
        buffer.writeln('# Type: ${parameter.type.name}');
        buffer.writeln('# Default: ${parameter.defaultValue}');
        
        if (parameter.minValue != null) {
          buffer.writeln('# Min: ${parameter.minValue}');
        }
        if (parameter.maxValue != null) {
          buffer.writeln('# Max: ${parameter.maxValue}');
        }
        if (parameter.allowedValues != null) {
          buffer.writeln('# Allowed: ${parameter.allowedValues!.join(', ')}');
        }
        if (parameter.isRequired) {
          buffer.writeln('# Required: true');
        }
        
        buffer.writeln('$prefix${parameter.environmentKey}=${parameter.defaultValue}');
      }
      
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  /// Export current environment configuration
  Map<String, dynamic> exportEnvironmentConfiguration() {
    final config = <String, dynamic>{};
    final parameters = ConfigParametersRegistry.getAllParameters();
    
    for (final parameter in parameters.values) {
      final envKey = parameter.environmentKey;
      if (envKey != null) {
        final value = getEnvironmentVariable(envKey);
        if (value != null) {
          config[parameter.key] = value;
        }
      }
    }
    
    return config;
  }
}

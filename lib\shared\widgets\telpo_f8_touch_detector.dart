import 'package:flutter/material.dart';
import '../utils/telpo_f8_optimizer.dart';

/// Touch detector widget for Telpo F8 terminal power management
class TelpoF8TouchDetector extends StatefulWidget {
  final Widget child;
  final bool showFPSOverlay;
  final bool showPowerStatus;

  const TelpoF8TouchDetector({
    super.key,
    required this.child,
    this.showFPSOverlay = true,
    this.showPowerStatus = true,
  });

  @override
  State<TelpoF8TouchDetector> createState() => _TelpoF8TouchDetectorState();
}

class _TelpoF8TouchDetectorState extends State<TelpoF8TouchDetector> {
  Map<String, dynamic> _fpsMetrics = {};

  @override
  void initState() {
    super.initState();
    TelpoF8Optimizer.initialize();
    
    // Update FPS metrics every second
    Stream.periodic(const Duration(seconds: 1)).listen((_) {
      if (mounted) {
        setState(() {
          _fpsMetrics = TelpoF8Optimizer.getFPSMetrics();
        });
      }
    });
  }

  @override
  void dispose() {
    TelpoF8Optimizer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Basic touch detection
      onTap: () {
        TelpoF8Optimizer.onTouchDetected();
      },
      onTapDown: (TapDownDetails details) {
        TelpoF8Optimizer.onTouchDetected();
      },

      // Scale gesture (includes pan/drag functionality)
      onScaleStart: (ScaleStartDetails details) {
        TelpoF8Optimizer.onTouchDetected();
      },
      onScaleUpdate: (ScaleUpdateDetails details) {
        TelpoF8Optimizer.onTouchDetected();
      },
      onScaleEnd: (ScaleEndDetails details) {
        TelpoF8Optimizer.onTouchDetected();
      },

      // Long press detection
      onLongPress: () {
        TelpoF8Optimizer.onTouchDetected();
      },
      child: Stack(
        children: [
          widget.child,
          
          // FPS Overlay
          if (widget.showFPSOverlay)
            Positioned(
              top: 50,
              right: 16,
              child: _buildFPSOverlay(),
            ),
          
          // Power Status Overlay
          if (widget.showPowerStatus)
            Positioned(
              top: 50,
              left: 16,
              child: _buildPowerStatusOverlay(),
            ),
        ],
      ),
    );
  }

  Widget _buildFPSOverlay() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '📊 TELPO F8 FPS',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Camera: ${(_fpsMetrics['camera_fps'] ?? 0.0).toStringAsFixed(1)} FPS',
            style: TextStyle(color: Colors.white, fontSize: 10),
          ),
          Text(
            'Processing: ${(_fpsMetrics['processing_fps'] ?? 0.0).toStringAsFixed(1)} FPS',
            style: TextStyle(color: Colors.white, fontSize: 10),
          ),
          Text(
            'Skip: ${(_fpsMetrics['skip_ratio'] ?? 0.0).toStringAsFixed(1)}%',
            style: TextStyle(color: Colors.white, fontSize: 10),
          ),
        ],
      ),
    );
  }

  Widget _buildPowerStatusOverlay() {
    final isPowerSaving = _fpsMetrics['power_saving_mode'] ?? false;
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isPowerSaving
            ? Colors.orange.withValues(alpha: 0.8)
            : Colors.green.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isPowerSaving ? Icons.battery_saver : Icons.flash_on,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                isPowerSaving ? 'POWER SAVING' : 'NORMAL MODE',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (isPowerSaving) ...[
            const SizedBox(height: 4),
            Text(
              'Touch to exit',
              style: TextStyle(
                color: Colors.white,
                fontSize: 9,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// FPS Display Widget for debugging
class TelpoF8FPSDisplay extends StatefulWidget {
  const TelpoF8FPSDisplay({super.key});

  @override
  State<TelpoF8FPSDisplay> createState() => _TelpoF8FPSDisplayState();
}

class _TelpoF8FPSDisplayState extends State<TelpoF8FPSDisplay> {
  String _optimizationSummary = '';

  @override
  void initState() {
    super.initState();
    
    // Update summary every 2 seconds
    Stream.periodic(const Duration(seconds: 2)).listen((_) {
      if (mounted) {
        setState(() {
          _optimizationSummary = TelpoF8Optimizer.getOptimizationSummary();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.5)),
      ),
      child: SingleChildScrollView(
        child: Text(
          _optimizationSummary,
          style: TextStyle(
            color: Colors.white,
            fontSize: 11,
            fontFamily: 'monospace',
          ),
        ),
      ),
    );
  }
}

/// Power Saving Mode Indicator
class PowerSavingIndicator extends StatelessWidget {
  final bool isPowerSaving;
  
  const PowerSavingIndicator({
    super.key,
    required this.isPowerSaving,
  });

  @override
  Widget build(BuildContext context) {
    if (!isPowerSaving) return const SizedBox.shrink();
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.orange.withValues(alpha: 0.9),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.battery_saver, color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Text(
            'POWER SAVING MODE - Touch screen to exit',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

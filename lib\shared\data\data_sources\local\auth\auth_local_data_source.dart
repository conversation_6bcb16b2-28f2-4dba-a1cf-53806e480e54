import '../../../models/auth/auth_result_model.dart';
import '../../../models/user/user_model.dart';

/// Abstract interface for authentication local data source
abstract class AuthLocalDataSource {
  /// Save authentication result to local storage
  Future<void> saveAuthResult(AuthResultModel authResult);
  
  /// Get authentication result from local storage
  Future<AuthResultModel?> getAuthResult();
  
  /// Save user data to local storage
  Future<void> saveUser(UserModel user);
  
  /// Get user data from local storage
  Future<UserModel?> getUser();
  
  /// Save access token to secure storage
  Future<void> saveAccessToken(String token);
  
  /// Get access token from secure storage
  Future<String?> getAccessToken();
  
  /// Save refresh token to secure storage
  Future<void> saveRefreshToken(String token);
  
  /// Get refresh token from secure storage
  Future<String?> getRefreshToken();
  
  /// Check if user is authenticated (has valid tokens)
  Future<bool> isAuthenticated();
  
  /// Clear all authentication data
  Future<void> clearAuthData();
  
  /// Save session data
  Future<void> saveSessionData(Map<String, dynamic> sessionData);
  
  /// Get session data
  Future<Map<String, dynamic>?> getSessionData();
  
  /// Save login timestamp
  Future<void> saveLoginTimestamp(DateTime timestamp);
  
  /// Get login timestamp
  Future<DateTime?> getLoginTimestamp();
  
  /// Check if session is expired
  Future<bool> isSessionExpired();
}

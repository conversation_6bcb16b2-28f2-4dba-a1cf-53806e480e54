#!/usr/bin/env python3
"""
Test script to verify downloaded face recognition models.
This script loads and tests the TensorFlow Lite models to ensure they work correctly.
"""

import os
import sys
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import argparse

try:
    import tensorflow as tf
except ImportError:
    print("Error: TensorFlow not installed. Run: pip install tensorflow")
    sys.exit(1)

class ModelTester:
    def __init__(self, models_dir: str):
        self.models_dir = Path(models_dir)
        
    def load_model(self, model_path: Path) -> Optional[tf.lite.Interpreter]:
        """Load a TensorFlow Lite model."""
        try:
            interpreter = tf.lite.Interpreter(model_path=str(model_path))
            interpreter.allocate_tensors()
            return interpreter
        except Exception as e:
            print(f"✗ Error loading {model_path.name}: {e}")
            return None
    
    def get_model_info(self, interpreter: tf.lite.Interpreter) -> Dict:
        """Get model input/output information."""
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        return {
            'input_shape': input_details[0]['shape'],
            'input_dtype': input_details[0]['dtype'],
            'output_shapes': [output['shape'] for output in output_details],
            'output_dtypes': [output['dtype'] for output in output_details],
            'input_name': input_details[0]['name'],
            'output_names': [output['name'] for output in output_details]
        }
    
    def create_test_input(self, shape: List[int], dtype) -> np.ndarray:
        """Create test input data for the model."""
        if dtype == np.float32:
            # Create random float data in [0, 1] range
            return np.random.random(shape).astype(np.float32)
        elif dtype == np.uint8:
            # Create random uint8 data in [0, 255] range
            return np.random.randint(0, 256, shape, dtype=np.uint8)
        else:
            # Default to float32
            return np.random.random(shape).astype(dtype)
    
    def test_model_inference(self, interpreter: tf.lite.Interpreter, model_name: str) -> bool:
        """Test model inference with dummy data."""
        try:
            input_details = interpreter.get_input_details()
            output_details = interpreter.get_output_details()
            
            # Create test input
            input_shape = input_details[0]['shape']
            input_dtype = input_details[0]['dtype']
            test_input = self.create_test_input(input_shape, input_dtype)
            
            # Set input tensor
            interpreter.set_tensor(input_details[0]['index'], test_input)
            
            # Run inference
            interpreter.invoke()
            
            # Get output
            outputs = []
            for output_detail in output_details:
                output_data = interpreter.get_tensor(output_detail['index'])
                outputs.append(output_data)
            
            print(f"✓ {model_name}: Inference successful")
            print(f"  Input shape: {input_shape}")
            print(f"  Output shapes: {[output.shape for output in outputs]}")
            
            return True
            
        except Exception as e:
            print(f"✗ {model_name}: Inference failed - {e}")
            return False
    
    def analyze_model_performance(self, interpreter: tf.lite.Interpreter, model_name: str, num_runs: int = 10) -> Dict:
        """Analyze model performance."""
        try:
            import time
            
            input_details = interpreter.get_input_details()
            input_shape = input_details[0]['shape']
            input_dtype = input_details[0]['dtype']
            test_input = self.create_test_input(input_shape, input_dtype)
            
            # Warm up
            interpreter.set_tensor(input_details[0]['index'], test_input)
            interpreter.invoke()
            
            # Time multiple runs
            times = []
            for _ in range(num_runs):
                start_time = time.time()
                interpreter.set_tensor(input_details[0]['index'], test_input)
                interpreter.invoke()
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # Convert to ms
            
            avg_time = np.mean(times)
            min_time = np.min(times)
            max_time = np.max(times)
            
            print(f"  Performance ({num_runs} runs):")
            print(f"    Average: {avg_time:.2f} ms")
            print(f"    Min: {min_time:.2f} ms")
            print(f"    Max: {max_time:.2f} ms")
            
            return {
                'avg_time_ms': avg_time,
                'min_time_ms': min_time,
                'max_time_ms': max_time
            }
            
        except Exception as e:
            print(f"  Performance test failed: {e}")
            return {}
    
    def test_model(self, model_name: str, run_performance: bool = False) -> bool:
        """Test a single model."""
        model_path = self.models_dir / model_name
        
        print(f"\n{'='*60}")
        print(f"Testing {model_name}")
        print(f"{'='*60}")
        
        # Check if file exists
        if not model_path.exists():
            print(f"✗ Model file not found: {model_path}")
            return False
        
        # Check file size
        size_mb = model_path.stat().st_size / 1024 / 1024
        print(f"File size: {size_mb:.2f} MB")
        
        if size_mb < 0.01:  # Less than 10KB
            print(f"✗ File too small, likely a placeholder")
            return False
        
        # Load model
        interpreter = self.load_model(model_path)
        if not interpreter:
            return False
        
        # Get model info
        info = self.get_model_info(interpreter)
        print(f"✓ Model loaded successfully")
        print(f"  Input: {info['input_name']} {info['input_shape']} ({info['input_dtype'].__name__})")
        for i, (name, shape, dtype) in enumerate(zip(info['output_names'], info['output_shapes'], info['output_dtypes'])):
            print(f"  Output {i}: {name} {shape} ({dtype.__name__})")
        
        # Test inference
        if not self.test_model_inference(interpreter, model_name):
            return False
        
        # Performance test
        if run_performance:
            self.analyze_model_performance(interpreter, model_name)
        
        return True
    
    def test_all_models(self, run_performance: bool = False) -> bool:
        """Test all models in the directory."""
        model_files = [
            "ultraface_320.tflite",
            "mobilefacenet.tflite", 
            "mediapipe_face.tflite"
        ]
        
        print(f"Testing models in: {self.models_dir}")
        
        success_count = 0
        total_count = len(model_files)
        
        for model_file in model_files:
            if self.test_model(model_file, run_performance):
                success_count += 1
        
        print(f"\n{'='*60}")
        print(f"Test Summary: {success_count}/{total_count} models passed")
        
        if success_count == total_count:
            print("✓ All models are working correctly!")
        else:
            print(f"✗ {total_count - success_count} models failed tests")
        
        return success_count == total_count
    
    def compare_models(self) -> None:
        """Compare model characteristics."""
        model_files = [
            "ultraface_320.tflite",
            "mobilefacenet.tflite",
            "mediapipe_face.tflite"
        ]
        
        print(f"\n{'='*60}")
        print("Model Comparison")
        print(f"{'='*60}")
        
        print(f"{'Model':<25} {'Size (MB)':<12} {'Input Shape':<20} {'Status'}")
        print("-" * 70)
        
        for model_file in model_files:
            model_path = self.models_dir / model_file
            
            if not model_path.exists():
                print(f"{model_file:<25} {'N/A':<12} {'N/A':<20} Not found")
                continue
            
            size_mb = model_path.stat().st_size / 1024 / 1024
            
            interpreter = self.load_model(model_path)
            if interpreter:
                info = self.get_model_info(interpreter)
                input_shape_str = str(list(info['input_shape']))
                status = "✓ OK"
            else:
                input_shape_str = "Error"
                status = "✗ Failed"
            
            print(f"{model_file:<25} {size_mb:<12.2f} {input_shape_str:<20} {status}")

def main():
    parser = argparse.ArgumentParser(description="Test face recognition models")
    parser.add_argument(
        "--models-dir",
        default="lib/packages/face_recognition/assets/models",
        help="Directory containing models to test"
    )
    parser.add_argument(
        "--model",
        choices=["ultraface_320.tflite", "mobilefacenet.tflite", "mediapipe_face.tflite"],
        help="Test specific model only"
    )
    parser.add_argument(
        "--performance",
        action="store_true",
        help="Run performance benchmarks"
    )
    parser.add_argument(
        "--compare",
        action="store_true",
        help="Compare all models"
    )
    
    args = parser.parse_args()
    
    # Resolve models directory
    script_dir = Path(__file__).parent
    models_dir = script_dir.parent / args.models_dir
    
    tester = ModelTester(str(models_dir))
    
    if args.compare:
        tester.compare_models()
        return
    
    if args.model:
        # Test specific model
        success = tester.test_model(args.model, args.performance)
        sys.exit(0 if success else 1)
    else:
        # Test all models
        success = tester.test_all_models(args.performance)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

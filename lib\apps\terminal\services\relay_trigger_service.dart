import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/relay_trigger_config.dart';
import '../../../shared/services/relay_management_service.dart';
import 'package:relay_controller/relay_controller.dart' as relay;

/// Service to handle relay triggers based on face detection/recognition events
class RelayTriggerService {
  static final RelayTriggerService _instance = RelayTriggerService._internal();
  factory RelayTriggerService() => _instance;
  RelayTriggerService._internal();

  RelayTriggerConfig _config = RelayTriggerConfig.defaultConfig;
  final RelayManagementService _relayService = RelayManagementService.instance;

  // Track active triggers to prevent conflicts
  final Map<int, Timer> _activeTriggers = {};
  final Map<int, String> _activeScenarios = {};

  // Connection monitoring
  StreamSubscription<RelayStatusUpdate>? _connectionSubscription;
  bool _isRelayConnected = false;
  int _consecutiveFailures = 0;
  static const int _maxConsecutiveFailures = 3;
  Timer? _reconnectTimer;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  bool get isRelayConnected => _isRelayConnected;
  
  RelayTriggerConfig get config => _config;

  /// Initialize the service with configuration
  Future<void> initialize({RelayTriggerConfig? config}) async {
    if (config != null) {
      _config = config;
    }

    // Set up connection monitoring
    await _setupConnectionMonitoring();

    if (kDebugMode) {
      print('🔧 RelayTriggerService initialized');
      print('   Device ID: ${_config.deviceId}');
      print('   Max concurrent triggers: ${_config.maxConcurrentTriggers}');
      print('   Scenarios configured: ${_config.scenarios.length}');
      print('   Connection monitoring: enabled');
    }

    _isInitialized = true;
  }

  /// Update configuration
  void updateConfig(RelayTriggerConfig newConfig) {
    _config = newConfig;
    if (kDebugMode) {
      print('🔧 RelayTriggerService configuration updated');
    }
  }

  /// Trigger relays for a specific scenario
  Future<void> triggerScenario(RelayTriggerScenario scenario, {
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isInitialized || !_config.isEnabled) {
      if (kDebugMode) {
        print('⏸️ RelayTriggerService not initialized or disabled');
      }
      return;
    }

    final actions = _config.getActionsForScenario(scenario);
    if (actions.isEmpty) {
      if (kDebugMode) {
        print('⏸️ No actions configured for scenario: ${scenario.name}');
      }
      return;
    }

    if (kDebugMode) {
      print('🚀 Triggering scenario: ${scenario.name}');
      print('   Actions to execute: ${actions.length}');
      if (metadata != null) {
        print('   Metadata: $metadata');
      }
    }

    // Execute actions with proper delays and priorities
    for (final action in actions) {
      _executeAction(scenario, action, metadata);
    }
  }

  /// Execute a simple relay trigger (ON then immediate OFF)
  Future<void> _executeAction(
    RelayTriggerScenario scenario,
    RelayTriggerAction action,
    Map<String, dynamic>? metadata,
  ) async {
    // Apply delay if specified
    if (action.delayMs > 0) {
      await Future.delayed(Duration(milliseconds: action.delayMs));
    }

    // Cancel any existing trigger for this relay
    if (_activeTriggers.containsKey(action.relayIndex)) {
      _activeTriggers[action.relayIndex]?.cancel();
      _activeTriggers.remove(action.relayIndex);
      _activeScenarios.remove(action.relayIndex);
    }

    try {
      if (kDebugMode) {
        print('🔌 Triggered: ${scenario.name} -> R${action.relayIndex} (${action.relayName})');
      }

      // Simple ON/OFF sequence without delay
      try {
        // Turn ON relay
        await _relayService.controlRelay(action.relayIndex, relay.RelayAction.on);

        // Small delay to ensure relay activates (100ms)
        await Future.delayed(const Duration(milliseconds: 100));

        // Turn OFF relay immediately
        await _relayService.controlRelay(action.relayIndex, relay.RelayAction.off);

        // Reset failure counter on success
        _consecutiveFailures = 0;

        if (kDebugMode) {
          print('✅ Relay R${action.relayIndex} triggered successfully');
        }
      } catch (e) {
        // If relay control fails, just log and continue
        if (kDebugMode) {
          print('⚠️ Relay control failed: $e');
        }
        rethrow; // Re-throw to be caught by outer catch
      }

    } catch (e) {
      _consecutiveFailures++;

      if (kDebugMode) {
        print('❌ Failed to trigger relay R${action.relayIndex}: $e');
        print('   Consecutive failures: $_consecutiveFailures');
      }

      // Only handle connection loss if too many consecutive failures
      if (_consecutiveFailures >= _maxConsecutiveFailures) {
        if (kDebugMode) {
          print('🚨 Too many consecutive failures, checking connection...');
        }
        await _handleConnectionLoss();
      }
    }
  }

  // Note: _turnOffRelay method removed since we use immediate ON/OFF triggers

  /// Get status of all relays
  Map<int, String> getActiveRelayStatus() {
    return Map.from(_activeScenarios);
  }

  /// Check if a specific relay is active
  bool isRelayActive(int relayIndex) {
    return _activeTriggers.containsKey(relayIndex);
  }

  /// Cancel all active triggers
  Future<void> cancelAllTriggers() async {
    if (kDebugMode) {
      print('🛑 Cancelling all active relay triggers');
    }

    for (final entry in _activeTriggers.entries) {
      entry.value.cancel();
      // No need to turn off relays since we use immediate ON/OFF triggers
    }

    _activeTriggers.clear();
    _activeScenarios.clear();
  }

  /// Cancel triggers for a specific scenario
  Future<void> cancelScenarioTriggers(RelayTriggerScenario scenario) async {
    final relaysToCancel = <int>[];
    
    for (final entry in _activeScenarios.entries) {
      if (entry.value == scenario.name) {
        relaysToCancel.add(entry.key);
      }
    }

    for (final relayIndex in relaysToCancel) {
      _activeTriggers[relayIndex]?.cancel();
      // No need to turn off relays since we use immediate ON/OFF triggers
    }

    if (kDebugMode && relaysToCancel.isNotEmpty) {
      print('🛑 Cancelled ${relaysToCancel.length} triggers for scenario: ${scenario.name}');
    }
  }

  /// Emergency stop - immediately turn off all relays
  Future<void> emergencyStop() async {
    if (kDebugMode) {
      print('🚨 EMERGENCY STOP - Turning off all relays immediately');
    }

    // Cancel all timers
    for (final timer in _activeTriggers.values) {
      timer.cancel();
    }

    // Simple turn off all relays (best effort)
    for (int i = 0; i < 4; i++) {
      try {
        await _relayService.controlRelay(i, relay.RelayAction.off);
        if (kDebugMode) {
          print('✅ Emergency stop: R$i turned off');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Emergency stop failed for R$i: $e');
        }
        // Continue trying other relays even if one fails
      }
    }

    // Clear tracking
    _activeTriggers.clear();
    _activeScenarios.clear();

    if (kDebugMode) {
      print('✅ Emergency stop completed');
    }
  }

  /// Set up connection monitoring for USB-TTL relay device
  Future<void> _setupConnectionMonitoring() async {
    try {
      // Listen to relay service connection status
      _connectionSubscription = _relayService.statusUpdates.listen((status) {
        switch (status.type) {
          case RelayStatusType.connected:
            _isRelayConnected = true;
            _consecutiveFailures = 0;
            _reconnectTimer?.cancel();
            if (kDebugMode) {
              print('🔌 Relay device connected: ${status.deviceId}');
            }
            break;

          case RelayStatusType.disconnected:
            _isRelayConnected = false;
            if (kDebugMode) {
              print('🔌 Relay device disconnected: ${status.deviceId}');
            }
            _handleConnectionLoss();
            break;

          default:
            break;
        }
      });

      // Check initial connection status
      _isRelayConnected = _relayService.isConnected;

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to setup connection monitoring: $e');
      }
    }
  }

  /// Check if an error is related to connection issues
  bool _isConnectionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('not connected') ||
           errorString.contains('disconnected') ||
           errorString.contains('device not found') ||
           errorString.contains('timeout') ||
           errorString.contains('usb') ||
           errorString.contains('serial');
  }

  /// Handle connection loss by stopping all active triggers
  Future<void> _handleConnectionLoss() async {
    _isRelayConnected = false;

    if (kDebugMode) {
      print('🚨 Relay connection lost - stopping all active triggers');
    }

    // Cancel all active triggers since we can't turn them off
    for (final timer in _activeTriggers.values) {
      timer.cancel();
    }
    _activeTriggers.clear();
    _activeScenarios.clear();

    // Start reconnection attempts
    _startReconnectionAttempts();
  }

  /// Start automatic reconnection attempts
  void _startReconnectionAttempts() {
    _reconnectTimer?.cancel();

    _reconnectTimer = Timer.periodic(const Duration(seconds: 10), (_) async {
      if (_isRelayConnected) {
        _reconnectTimer?.cancel();
        return;
      }

      if (kDebugMode) {
        print('🔄 Attempting relay reconnection...');
      }

      await _attemptReconnection();
    });
  }

  /// Attempt to reconnect to relay device
  Future<void> _attemptReconnection() async {
    try {
      // Disconnect first
      await _relayService.disconnect();

      // Wait a bit for cleanup
      await Future.delayed(const Duration(milliseconds: 500));

      // Reinitialize with current config
      if (_relayService.deviceConfig != null) {
        await _relayService.initialize(
          config: _relayService.deviceConfig!,
          autoConnect: true,
        );
      }

      if (kDebugMode) {
        print('✅ Relay reconnection successful');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Relay reconnection failed: $e');
      }
    }
  }

  /// Dispose service and cleanup
  void dispose() {
    // Cancel connection monitoring
    _connectionSubscription?.cancel();
    _reconnectTimer?.cancel();

    cancelAllTriggers();
    _isInitialized = false;

    if (kDebugMode) {
      print('🔧 RelayTriggerService disposed');
    }
  }
}

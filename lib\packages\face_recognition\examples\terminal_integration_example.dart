/// Example integration of Face Recognition Package in Terminal App
/// 
/// This example shows how to integrate the face recognition system
/// into the existing terminal app for Telpo F8 devices.

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

// Import the face recognition package
import '../face_recognition.dart';

/// Example terminal screen with face recognition integration
class TerminalFaceRecognitionScreen extends StatefulWidget {
  const TerminalFaceRecognitionScreen({super.key});

  @override
  State<TerminalFaceRecognitionScreen> createState() => _TerminalFaceRecognitionScreenState();
}

class _TerminalFaceRecognitionScreenState extends State<TerminalFaceRecognitionScreen> {
  // Face recognition system
  TerminalFaceSystem? _faceSystem;
  
  // Camera
  CameraController? _cameraController;
  
  // State
  bool _isInitialized = false;
  bool _isProcessing = false;
  String _statusMessage = 'Initializing...';
  
  @override
  void initState() {
    super.initState();
    _initializeSystem();
  }
  
  /// Initialize the terminal face recognition system
  Future<void> _initializeSystem() async {
    try {
      // Initialize face recognition for Telpo F8
      _faceSystem = await FaceRecognitionTerminal.initialize(
        deviceType: TerminalDeviceType.telpoF8,
        performanceProfile: PerformanceProfile.maxPerformance,
        serverEndpoint: 'https://api.yourcompany.com/face',
        apiKey: 'your-api-key',
      );
      
      // Initialize camera
      final cameras = await availableCameras();
      if (cameras.isNotEmpty) {
        _cameraController = CameraController(
          cameras.first,
          ResolutionPreset.medium,
          enableAudio: false,
        );
        
        await _cameraController!.initialize();
        
        // Start image stream processing
        _cameraController!.startImageStream(_processCameraImage);
      }
      
      // Listen to face system changes
      _faceSystem!.addListener(_onFaceSystemChanged);
      
      setState(() {
        _isInitialized = true;
        _statusMessage = 'Ready for face recognition';
      });
      
    } catch (e) {
      setState(() {
        _statusMessage = 'Initialization failed: $e';
      });
    }
  }
  
  /// Process camera image through face recognition system
  Future<void> _processCameraImage(CameraImage image) async {
    if (_isProcessing || _faceSystem == null) return;
    
    _isProcessing = true;
    
    try {
      await _faceSystem!.processCameraImage(image);
    } catch (e) {
      print('Error processing camera image: $e');
    } finally {
      _isProcessing = false;
    }
  }
  
  /// Handle face system state changes
  void _onFaceSystemChanged() {
    if (!mounted) return;
    
    final stats = _faceSystem!.getStats();
    final lastRecognition = _faceSystem!.lastRecognition;
    
    setState(() {
      if (lastRecognition != null) {
        if (lastRecognition.hasAccess) {
          _statusMessage = '✅ Welcome, ${lastRecognition.userName}!';
        } else {
          _statusMessage = '❌ Access denied for ${lastRecognition.userName}';
        }
      } else if (_faceSystem!.detectedFaces.isNotEmpty) {
        _statusMessage = 'Face detected, recognizing...';
      } else {
        _statusMessage = 'Looking for faces...';
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera preview
          if (_isInitialized && _cameraController != null)
            Positioned.fill(
              child: CameraPreview(_cameraController!),
            ),
          
          // Face detection overlay
          if (_isInitialized && _faceSystem != null)
            Positioned.fill(
              child: CustomPaint(
                painter: FaceDetectionPainter(
                  faces: _faceSystem!.detectedFaces,
                  imageSize: _cameraController?.value.previewSize ?? Size.zero,
                ),
              ),
            ),
          
          // Status overlay
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _statusMessage,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_isInitialized && _faceSystem != null) ...[
                    const SizedBox(height: 8),
                    _buildStatsWidget(),
                  ],
                ],
              ),
            ),
          ),
          
          // Hardware status indicator
          if (_isInitialized && _faceSystem != null)
            Positioned(
              bottom: 20,
              right: 20,
              child: _buildHardwareStatusWidget(),
            ),
        ],
      ),
    );
  }
  
  /// Build performance stats widget
  Widget _buildStatsWidget() {
    final stats = _faceSystem!.getStats();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'FPS: ${stats.averageFPS.toStringAsFixed(1)}',
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        Text(
          'Processed: ${stats.totalProcessed}',
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        Text(
          'Recognized: ${stats.totalRecognized}',
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        Text(
          'Success Rate: ${(stats.recognitionRate * 100).toStringAsFixed(1)}%',
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
        Text(
          'Network: ${stats.isOnline ? "Online" : "Offline"}',
          style: TextStyle(
            color: stats.isOnline ? Colors.green : Colors.orange,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
  
  /// Build hardware status widget
  Widget _buildHardwareStatusWidget() {
    // This would show the status of connected hardware (LEDs, relays, etc.)
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lightbulb, color: Colors.green, size: 16),
              SizedBox(width: 4),
              Text('LED', style: TextStyle(color: Colors.white, fontSize: 12)),
            ],
          ),
          SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.power, color: Colors.green, size: 16),
              SizedBox(width: 4),
              Text('Relay', style: TextStyle(color: Colors.white, fontSize: 12)),
            ],
          ),
          SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.volume_up, color: Colors.green, size: 16),
              SizedBox(width: 4),
              Text('Buzzer', style: TextStyle(color: Colors.white, fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _cameraController?.dispose();
    _faceSystem?.removeListener(_onFaceSystemChanged);
    FaceRecognitionTerminal.dispose();
    super.dispose();
  }
}

/// Custom painter for face detection overlay
class FaceDetectionPainter extends CustomPainter {
  final List<FaceDetection> faces;
  final Size imageSize;
  
  FaceDetectionPainter({
    required this.faces,
    required this.imageSize,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (faces.isEmpty || imageSize == Size.zero) return;
    
    final paint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    for (final face in faces) {
      // Transform face bounding box to canvas coordinates
      final rect = _transformRect(face.boundingBox, imageSize, size);
      
      // Draw bounding box
      canvas.drawRect(rect, paint);
      
      // Draw landmarks if available
      if (face.landmarks.isNotEmpty) {
        final landmarkPaint = Paint()
          ..color = Colors.red
          ..style = PaintingStyle.fill;
        
        for (final landmark in face.landmarks) {
          final point = _transformPoint(landmark, imageSize, size);
          canvas.drawCircle(point, 3, landmarkPaint);
        }
      }
      
      // Draw confidence text
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${(face.confidence * 100).toStringAsFixed(0)}%',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(rect.left, rect.top - 20),
      );
    }
  }
  
  Rect _transformRect(Rect rect, Size imageSize, Size canvasSize) {
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;
    
    return Rect.fromLTRB(
      rect.left * scaleX,
      rect.top * scaleY,
      rect.right * scaleX,
      rect.bottom * scaleY,
    );
  }
  
  Offset _transformPoint(Point point, Size imageSize, Size canvasSize) {
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;
    
    return Offset(
      point.x * scaleX,
      point.y * scaleY,
    );
  }
  
  @override
  bool shouldRepaint(FaceDetectionPainter oldDelegate) {
    return faces != oldDelegate.faces || imageSize != oldDelegate.imageSize;
  }
}

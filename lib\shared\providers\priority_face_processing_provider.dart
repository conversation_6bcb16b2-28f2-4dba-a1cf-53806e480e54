import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../services/priority_face_processing_queue.dart';
import '../core/constants/face_cropping_constants.dart';
import '../models/enhanced_face_capture_result.dart';

/// Provider for priority-based face processing
/// Manages single-face processing with quality prioritization
class PriorityFaceProcessingProvider extends ChangeNotifier {
  static const String _logTag = '🎯 PriorityFaceProvider';
  
  final PriorityFaceProcessingQueue _queue = PriorityFaceProcessingQueue();
  
  // State management
  bool _isEnabled = true;
  bool _isProcessing = false;
  String? _currentTaskId;
  FaceProcessingStatus? _currentStatus;
  
  // Configuration
  double _minFaceQuality = 0.5;
  int _maxConcurrentTasks = 1; // Chỉ xử lý 1 face tại 1 thời điểm
  Duration _faceDetectionCooldown = const Duration(milliseconds: 2000); // 2s giữa các lần detect
  
  // Face tracking
  Face? _lastProcessedFace;
  DateTime? _lastProcessingTime;
  String? _lastTaskId;
  EnhancedFaceCaptureResult? _lastProcessedResult;

  // Statistics
  int _totalProcessed = 0;
  int _totalFailed = 0;
  
  // Timer for periodic status updates
  Timer? _statusUpdateTimer;
  
  PriorityFaceProcessingProvider() {
    _startStatusMonitoring();
  }
  
  // Getters
  bool get isEnabled => _isEnabled;
  bool get isProcessing => _isProcessing;
  String? get currentTaskId => _currentTaskId;
  FaceProcessingStatus? get currentStatus => _currentStatus;
  double get minFaceQuality => _minFaceQuality;
  int get totalProcessed => _totalProcessed;
  int get totalFailed => _totalFailed;
  EnhancedFaceCaptureResult? get lastProcessedResult => _lastProcessedResult;
  Face? get lastProcessedFace => _lastProcessedFace;
  
  /// Enable/disable face processing
  void setEnabled(bool enabled) {
    if (_isEnabled != enabled) {
      _isEnabled = enabled;
      debugPrint('$_logTag Face processing ${enabled ? 'enabled' : 'disabled'}');
      
      if (!enabled) {
        _queue.clearQueue();
        _isProcessing = false;
        _currentTaskId = null;
        _currentStatus = null;
      }
      
      notifyListeners();
    }
  }
  
  /// Set minimum face quality threshold
  void setMinFaceQuality(double quality) {
    if (quality >= 0.0 && quality <= 1.0) {
      _minFaceQuality = quality;
      debugPrint('$_logTag Min face quality set to: $quality');
      notifyListeners();
    }
  }
  
  /// Set face detection cooldown period
  void setFaceDetectionCooldown(Duration cooldown) {
    _faceDetectionCooldown = cooldown;
    debugPrint('$_logTag Face detection cooldown set to: ${cooldown.inMilliseconds}ms');
    notifyListeners();
  }
  
  /// Process detected faces with priority queue
  /// Only processes the best face to reduce device load
  Future<String?> processDetectedFaces({
    required String imagePath,
    required List<Face> detectedFaces,
    required FaceDirection direction,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
  }) async {
    if (!_isEnabled) {
      debugPrint('$_logTag Face processing is disabled');
      return null;
    }
    
    // Check cooldown period
    if (_lastProcessingTime != null) {
      final timeSinceLastProcessing = DateTime.now().difference(_lastProcessingTime!);
      if (timeSinceLastProcessing < _faceDetectionCooldown) {
        debugPrint('$_logTag Cooldown period active, skipping processing');
        return null;
      }
    }
    
    // Filter faces by quality
    final qualityFaces = _filterFacesByQuality(detectedFaces);
    if (qualityFaces.isEmpty) {
      debugPrint('$_logTag No faces meet quality threshold ($minFaceQuality)');
      return null;
    }
    
    // Check if we should process (avoid duplicate processing)
    final bestFace = _selectBestFace(qualityFaces);
    if (bestFace != null && _isSimilarToLastProcessed(bestFace)) {
      debugPrint('$_logTag Skipping similar face to avoid duplicate processing');
      return null;
    }
    
    try {
      // Calculate priority based on face quality and size
      final priority = _calculateFacePriority(bestFace!);
      
      // Add to priority queue
      final taskId = await _queue.addFaceProcessingTask(
        imagePath: imagePath,
        detectedFaces: qualityFaces,
        direction: direction,
        context: {
          ...?context,
          'provider_session': DateTime.now().millisecondsSinceEpoch.toString(),
          'face_count': detectedFaces.length,
          'quality_faces_count': qualityFaces.length,
          'calculated_priority': priority,
        },
        enabledSideEffects: enabledSideEffects,
        priority: priority,
      );
      
      _lastProcessedFace = bestFace;
      _lastProcessingTime = DateTime.now();
      _lastTaskId = taskId;
      
      debugPrint('$_logTag Added face processing task: $taskId (Priority: $priority)');
      
      notifyListeners();
      return taskId;
      
    } catch (e) {
      debugPrint('$_logTag ❌ Failed to add face processing task: $e');
      _totalFailed++;
      notifyListeners();
      return null;
    }
  }
  
  /// Filter faces by quality threshold
  List<Face> _filterFacesByQuality(List<Face> faces) {
    return faces.where((face) {
      final quality = _calculateFaceQuality(face);
      return quality >= _minFaceQuality;
    }).toList();
  }
  
  /// Calculate face quality score (0.0 to 1.0)
  double _calculateFaceQuality(Face face) {
    double quality = 0.0;
    int factors = 0;
    
    // Face size quality (larger faces are better)
    final area = face.boundingBox.width * face.boundingBox.height;
    final sizeQuality = (area / 100000).clamp(0.0, 1.0); // Normalize to 100k pixels
    quality += sizeQuality * 0.4; // 40% weight
    factors++;
    
    // Eye open probability
    if (face.leftEyeOpenProbability != null && face.rightEyeOpenProbability != null) {
      final eyeQuality = (face.leftEyeOpenProbability! + face.rightEyeOpenProbability!) / 2;
      quality += eyeQuality * 0.3; // 30% weight
      factors++;
    }
    
    // Head pose quality (frontal faces are better)
    if (face.headEulerAngleY != null && face.headEulerAngleZ != null) {
      final yawAngle = face.headEulerAngleY!.abs();
      final rollAngle = face.headEulerAngleZ!.abs();
      final poseQuality = 1.0 - ((yawAngle + rollAngle) / 60.0).clamp(0.0, 1.0);
      quality += poseQuality * 0.2; // 20% weight
      factors++;
    }
    
    // Smiling probability (optional, lower weight)
    if (face.smilingProbability != null) {
      quality += face.smilingProbability! * 0.1; // 10% weight
      factors++;
    }
    
    return factors > 0 ? quality : 0.0;
  }
  
  /// Select best face from quality faces
  Face? _selectBestFace(List<Face> faces) {
    if (faces.isEmpty) return null;
    
    Face? bestFace;
    double bestScore = 0.0;
    
    for (final face in faces) {
      final quality = _calculateFaceQuality(face);
      final area = face.boundingBox.width * face.boundingBox.height;
      
      // Combined score: quality + size
      final score = quality * 0.7 + (area / 100000).clamp(0.0, 1.0) * 0.3;
      
      if (score > bestScore) {
        bestScore = score;
        bestFace = face;
      }
    }
    
    return bestFace;
  }
  
  /// Calculate priority for face processing (higher = more important)
  int _calculateFacePriority(Face face) {
    final quality = _calculateFaceQuality(face);
    final area = face.boundingBox.width * face.boundingBox.height;
    
    // Base priority from quality (0-50)
    int priority = (quality * 50).round();
    
    // Bonus for larger faces (0-30)
    final sizeBonus = ((area / 100000).clamp(0.0, 1.0) * 30).round();
    priority += sizeBonus;
    
    // Bonus for frontal faces (0-20)
    if (face.headEulerAngleY != null) {
      final frontalBonus = (20 * (1.0 - face.headEulerAngleY!.abs() / 30.0)).clamp(0.0, 20.0).round();
      priority += frontalBonus;
    }
    
    return priority.clamp(0, 100);
  }
  
  /// Check if face is similar to last processed face (avoid duplicates)
  bool _isSimilarToLastProcessed(Face face) {
    if (_lastProcessedFace == null) return false;
    
    final lastBox = _lastProcessedFace!.boundingBox;
    final currentBox = face.boundingBox;
    
    // Calculate center distance
    final lastCenter = lastBox.center;
    final currentCenter = currentBox.center;
    final distance = (lastCenter - currentCenter).distance;
    
    // Calculate size difference
    final lastArea = lastBox.width * lastBox.height;
    final currentArea = currentBox.width * currentBox.height;
    final sizeDiff = (lastArea - currentArea).abs() / lastArea;
    
    // Consider similar if center distance < 50 pixels and size difference < 20%
    return distance < 50 && sizeDiff < 0.2;
  }
  
  /// Start monitoring queue status
  void _startStatusMonitoring() {
    _statusUpdateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateStatus();
    });
  }
  
  /// Update current status from queue
  void _updateStatus() {
    final queueStatus = _queue.getQueueStatus();
    final statistics = _queue.getStatistics();
    
    final wasProcessing = _isProcessing;
    final wasCurrentTaskId = _currentTaskId;
    
    _isProcessing = queueStatus['is_processing'] as bool;
    _currentTaskId = queueStatus['current_task_id'] as String?;
    
    // Parse current status
    final statusString = queueStatus['current_task_status'] as String?;
    if (statusString != null) {
      try {
        _currentStatus = FaceProcessingStatus.values.firstWhere(
          (status) => status.name == statusString,
        );
      } catch (e) {
        _currentStatus = null;
      }
    } else {
      _currentStatus = null;
    }
    
    // Update statistics
    _totalProcessed = statistics['total_processed'] as int;
    _totalFailed = statistics['total_failed'] as int;
    
    // Notify if status changed
    if (wasProcessing != _isProcessing || wasCurrentTaskId != _currentTaskId) {
      notifyListeners();
    }
  }
  
  /// Get current queue status
  Map<String, dynamic> getQueueStatus() {
    return _queue.getQueueStatus();
  }
  
  /// Get processing statistics
  Map<String, dynamic> getStatistics() {
    final queueStats = _queue.getStatistics();
    
    return {
      ...queueStats,
      'is_enabled': _isEnabled,
      'min_face_quality': _minFaceQuality,
      'cooldown_ms': _faceDetectionCooldown.inMilliseconds,
      'last_task_id': _lastTaskId,
      'last_processing_time': _lastProcessingTime?.toIso8601String(),
      'has_last_processed_face': _lastProcessedFace != null,
    };
  }
  
  /// Clear queue and reset state
  void clearQueue() {
    _queue.clearQueue();
    _isProcessing = false;
    _currentTaskId = null;
    _currentStatus = null;
    _lastProcessedFace = null;
    _lastProcessingTime = null;
    _lastTaskId = null;
    
    debugPrint('$_logTag Queue cleared and state reset');
    notifyListeners();
  }
  
  /// Configure queue settings
  void configureQueue({
    int? maxQueueSize,
    Duration? processingTimeout,
  }) {
    _queue.configure(
      maxQueueSize: maxQueueSize,
      processingTimeout: processingTimeout,
    );
    notifyListeners();
  }

  /// Simulate test face crop for demo purposes
  Future<void> simulateTestFaceCrop() async {
    try {
      debugPrint('$_logTag Starting test face crop simulation...');

      // Simulate processing delay
      await Future.delayed(const Duration(seconds: 2));

      // Create mock result with a test image path
      final mockResult = EnhancedFaceCaptureResult(
        success: true,
        capturedImages: {FaceDirection.front: '/mock/original/path.jpg'},
        croppedImages: {FaceDirection.front: '/mock/cropped/face.jpg'},
        detectedFaces: {},
        timestamp: DateTime.now(),
      );

      // Update state
      _lastProcessedResult = mockResult;

      debugPrint('$_logTag Test face crop simulation completed successfully');
      notifyListeners();

    } catch (e) {
      debugPrint('$_logTag ❌ Test simulation failed: $e');
      rethrow;
    }
  }

  @override
  void dispose() {
    _statusUpdateTimer?.cancel();
    _queue.stopProcessing();
    debugPrint('$_logTag Provider disposed');
    super.dispose();
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/face_event_trigger_service.dart';
import '../../models/relay_trigger_config.dart';

/// Relay Trigger Configuration Screen
/// 
/// Provides interface for configuring relay triggers based on face detection
/// and recognition events. Allows customization of:
/// - Individual relay assignments (R0-R3)
/// - Trigger durations and delays
/// - Enable/disable specific scenarios
/// - Auto USB-TTL connection settings
class RelayTriggerConfigScreen extends StatefulWidget {
  const RelayTriggerConfigScreen({super.key});

  @override
  State<RelayTriggerConfigScreen> createState() => _RelayTriggerConfigScreenState();
}

class _RelayTriggerConfigScreenState extends State<RelayTriggerConfigScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final FaceEventTriggerService _triggerService = FaceEventTriggerService();
  
  // Configuration state
  late FaceEventTriggerConfig _config;
  bool _hasUnsavedChanges = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCurrentConfiguration();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentConfiguration() async {
    setState(() => _isLoading = true);
    
    try {
      // Load current configuration from service
      if (_triggerService.isInitialized) {
        _config = _triggerService.config;
      } else {
        _config = FaceEventTriggerConfig.defaultConfig;
      }
    } catch (e) {
      _config = FaceEventTriggerConfig.defaultConfig;
      _showErrorSnackBar('Error loading configuration: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _saveConfiguration() async {
    try {
      // Update service configuration
      _triggerService.updateConfig(_config);
      
      setState(() => _hasUnsavedChanges = false);
      _showSuccessSnackBar('Configuration saved successfully');
    } catch (e) {
      _showErrorSnackBar('Error saving configuration: $e');
    }
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Defaults'),
        content: const Text('Are you sure you want to reset all relay trigger settings to default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _config = FaceEventTriggerConfig.defaultConfig;
                _hasUnsavedChanges = true;
              });
              Navigator.pop(context);
              _showSuccessSnackBar('Configuration reset to defaults');
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relay Trigger Configuration'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.electrical_services), text: 'Relay Setup'),
            Tab(icon: Icon(Icons.face), text: 'Face Events'),
            Tab(icon: Icon(Icons.usb), text: 'USB-TTL'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetToDefaults,
            tooltip: 'Reset to Defaults',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildRelaySetupTab(),
                _buildFaceEventsTab(),
                _buildUsbTtlTab(),
              ],
            ),
      floatingActionButton: _hasUnsavedChanges
          ? FloatingActionButton.extended(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
              label: const Text('Save Changes'),
              backgroundColor: Colors.indigo,
            )
          : null,
    );
  }

  Widget _buildRelaySetupTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Relay Schema Overview
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Relay Schema (Default)',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  _buildRelaySchemaItem('R0', 'LED Lighting', 'Controls LED for face detection', Colors.amber),
                  _buildRelaySchemaItem('R1', 'Terminal Door', 'Opens terminal door for authorized access', Colors.green),
                  _buildRelaySchemaItem('R2', 'Backup Sensor 1', 'Additional sensor for face recognition', Colors.blue),
                  _buildRelaySchemaItem('R3', 'Backup Sensor 2', 'Secondary backup sensor', Colors.purple),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Relay Configuration
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Relay Configuration',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  
                  // Device ID
                  TextFormField(
                    initialValue: _config.relayTriggerConfig.deviceId,
                    decoration: const InputDecoration(
                      labelText: 'Device ID',
                      hintText: 'Enter relay device ID',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _config = _config.copyWith(
                          relayTriggerConfig: _config.relayTriggerConfig.copyWith(deviceId: value),
                        );
                        _hasUnsavedChanges = true;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Max Concurrent Triggers
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Max Concurrent Triggers: ${_config.relayTriggerConfig.maxConcurrentTriggers}',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ),
                      SizedBox(
                        width: 200,
                        child: Slider(
                          value: _config.relayTriggerConfig.maxConcurrentTriggers.toDouble(),
                          min: 1,
                          max: 4,
                          divisions: 3,
                          label: _config.relayTriggerConfig.maxConcurrentTriggers.toString(),
                          onChanged: (value) {
                            setState(() {
                              _config = _config.copyWith(
                                relayTriggerConfig: _config.relayTriggerConfig.copyWith(
                                  maxConcurrentTriggers: value.round(),
                                ),
                              );
                              _hasUnsavedChanges = true;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelaySchemaItem(String relay, String name, String description, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: color, width: 2),
            ),
            child: Center(
              child: Text(
                relay,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFaceEventsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Face Detection Triggers
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Face Detection Triggers',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  SwitchListTile(
                    title: const Text('Enable Face Detection Triggers'),
                    subtitle: const Text('Automatically control R0 (LED) based on face detection'),
                    value: _config.enableFaceDetectionTriggers,
                    onChanged: (value) {
                      setState(() {
                        _config = _config.copyWith(enableFaceDetectionTriggers: value);
                        _hasUnsavedChanges = true;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Face Recognition Triggers
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Face Recognition Triggers',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  SwitchListTile(
                    title: const Text('Enable Face Recognition Triggers'),
                    subtitle: const Text('Automatically control R1 (Terminal) based on recognition results'),
                    value: _config.enableFaceRecognitionTriggers,
                    onChanged: (value) {
                      setState(() {
                        _config = _config.copyWith(enableFaceRecognitionTriggers: value);
                        _hasUnsavedChanges = true;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsbTtlTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'USB-TTL Auto Connection',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 12),
                  SwitchListTile(
                    title: const Text('Enable Auto USB-TTL Connect'),
                    subtitle: const Text('Automatically detect and connect to USB-TTL relay devices'),
                    value: _config.enableAutoUsbTtlConnect,
                    onChanged: (value) {
                      setState(() {
                        _config = _config.copyWith(enableAutoUsbTtlConnect: value);
                        _hasUnsavedChanges = true;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Supported USB-TTL Devices:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  _buildUsbDeviceItem('FTDI FT232', 'Common USB-Serial converter'),
                  _buildUsbDeviceItem('Prolific PL2303', 'Popular USB-TTL adapter'),
                  _buildUsbDeviceItem('Silicon Labs CP210x', 'High-quality USB bridge'),
                  _buildUsbDeviceItem('CH340/CH341', 'Low-cost Chinese USB-Serial'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsbDeviceItem(String name, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const Icon(Icons.usb, size: 20, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

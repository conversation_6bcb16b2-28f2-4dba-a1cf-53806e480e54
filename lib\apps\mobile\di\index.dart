/// Mobile Dependency Injection Index
///
/// This file exports all mobile-specific DI components for easy importing.
/// The mobile DI system builds upon shared dependencies and adds
/// mobile-specific services and configurations.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/mobile/di/index.dart';
///
/// // Setup mobile dependencies
/// await MobileServiceLocator.setupMobileDependencies();
///
/// // Use mobile dependencies
/// final mobileRouter = getIt<GoRouter>();
/// final cameraController = getIt<MobileCameraController>();
///
/// // Check status
/// final isReady = MobileServiceLocator.isInitialized;
/// ```
library;

// ============================================================================
// MOBILE SERVICE LOCATOR
// ============================================================================
export 'mobile_service_locator.dart';

// ============================================================================
// MOBILE DI MODULES
// ============================================================================
export 'modules/mobile_navigation_module.dart' hide getIt;
export 'modules/mobile_ui_module.dart' hide getIt;
export 'modules/mobile_camera_module.dart' hide getIt;

// ============================================================================
// SHARED DEPENDENCIES (RE-EXPORT FOR CONVENIENCE)
// ============================================================================
export '../../../shared/core/di/shared_service_locator.dart';

// ============================================================================
// MOBILE DI UTILITIES
// ============================================================================

// Import required classes for utility functions
import 'mobile_service_locator.dart';

/// Quick setup function for mobile dependencies
Future<void> setupMobile() async {
  await MobileServiceLocator.setupMobileDependencies();
}

/// Quick validation function for mobile dependencies
bool validateMobile() {
  return MobileServiceLocator.validateMobileDependencies();
}

/// Quick status check for mobile dependencies
Map<String, dynamic> getMobileStatus() {
  return MobileServiceLocator.getMobileDependencyStatus();
}

/// Quick cleanup function for mobile dependencies
Future<void> disposeMobile() async {
  await MobileServiceLocator.disposeMobileDependencies();
}

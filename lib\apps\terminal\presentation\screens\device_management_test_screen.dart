import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/services/device_management_service.dart';
import '../../../../shared/widgets/device_management_widget.dart';

/// Test screen for device management functionality
class DeviceManagementTestScreen extends StatefulWidget {
  const DeviceManagementTestScreen({super.key});

  @override
  State<DeviceManagementTestScreen> createState() => _DeviceManagementTestScreenState();
}

class _DeviceManagementTestScreenState extends State<DeviceManagementTestScreen> {
  final DeviceManagementService _deviceService = DeviceManagementService.instance;
  
  bool _isServiceInitialized = false;
  String _lastError = '';
  Timer? _statusTimer;
  
  @override
  void initState() {
    super.initState();
    _initializeService();
    _startStatusUpdates();
  }
  
  @override
  void dispose() {
    _statusTimer?.cancel();
    super.dispose();
  }
  
  Future<void> _initializeService() async {
    try {
      if (!_deviceService.isInitialized) {
        await _deviceService.initialize();
      }
      
      setState(() {
        _isServiceInitialized = true;
        _lastError = '';
      });
      
      if (kDebugMode) {
        print('✅ Device management service initialized for test screen');
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
      
      if (kDebugMode) {
        print('❌ Failed to initialize device management service: $e');
      }
    }
  }
  
  void _startStatusUpdates() {
    _statusTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      if (mounted) {
        setState(() {
          // Trigger UI refresh to show current status
        });
      }
    });
  }
  
  Future<void> _testBrightnessSequence() async {
    try {
      setState(() {
        _lastError = '';
      });
      
      // Test brightness sequence: 20% -> 50% -> 80% -> back to original
      final originalBrightness = await _deviceService.getBrightness();
      
      final testSequence = [0.2, 0.5, 0.8, originalBrightness];
      
      for (final brightness in testSequence) {
        await _deviceService.setBrightness(brightness);
        await Future.delayed(const Duration(seconds: 1));
        
        if (kDebugMode) {
          print('🔆 Brightness set to ${(brightness * 100).toInt()}%');
        }
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Brightness test sequence completed'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
    }
  }
  
  Future<void> _testWakeLockToggle() async {
    try {
      setState(() {
        _lastError = '';
      });
      
      final wasEnabled = _deviceService.isWakeLockEnabled;
      
      // Toggle wake lock
      if (wasEnabled) {
        await _deviceService.disableWakeLock();
        await Future.delayed(const Duration(seconds: 2));
        await _deviceService.enableWakeLock();
      } else {
        await _deviceService.enableWakeLock();
        await Future.delayed(const Duration(seconds: 2));
        await _deviceService.disableWakeLock();
      }
      
      setState(() {});
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Wake lock toggled: ${_deviceService.isWakeLockEnabled ? "ON" : "OFF"}'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Device Management Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _initializeService,
            icon: const Icon(Icons.refresh),
            tooltip: 'Reinitialize Service',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service Status
            _buildServiceStatus(),
            
            const SizedBox(height: 16),
            
            // Device Management Widget
            const DeviceManagementWidget(),
            
            const SizedBox(height: 16),
            
            // Additional monitoring can be added here
            const SizedBox(height: 8),
            
            // Test Controls
            _buildTestControls(),
            
            if (_lastError.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildErrorDisplay(),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildServiceStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isServiceInitialized ? Icons.check_circle : Icons.error,
                  color: _isServiceInitialized ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Device Management Service',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _isServiceInitialized ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _isServiceInitialized ? 'READY' : 'NOT READY',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            if (_isServiceInitialized) ...[
              _buildStatusRow('Wake Lock', _deviceService.isWakeLockEnabled ? 'Enabled' : 'Disabled'),
              _buildStatusRow('Brightness', '${(_deviceService.currentBrightness * 100).toInt()}%'),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTestControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Controls',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isServiceInitialized ? _testBrightnessSequence : null,
                  icon: const Icon(Icons.brightness_6, size: 16),
                  label: const Text('Test Brightness'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _isServiceInitialized ? _testWakeLockToggle : null,
                  icon: const Icon(Icons.screen_lock_portrait, size: 16),
                  label: const Text('Test Wake Lock'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: () {
                    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
                  },
                  icon: const Icon(Icons.fullscreen, size: 16),
                  label: const Text('Full Screen'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: () {
                    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
                  },
                  icon: const Icon(Icons.fullscreen_exit, size: 16),
                  label: const Text('Exit Full Screen'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildErrorDisplay() {
    return Card(
      color: Colors.red.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Error',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _lastError,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}

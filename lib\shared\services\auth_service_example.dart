// import 'package:face_terminal/shared/services/auth_service.dart';
// import 'package:face_terminal/shared/services/http_client_service.dart';

// /// File example minh họa cách sử dụng AuthService
// /// <PERSON><PERSON><PERSON> là file demo, có thể xóa sau khi đã hiểu cách sử dụng
// class AuthServiceExample {
//   final AuthService _authService = AuthService();
//   final HttpClientService _httpClient = HttpClientService();

//   /// Khởi tạo services
//   Future<void> initializeServices() async {
//     // Khởi tạo HTTP client với config
//     _httpClient.initialize(
//       HttpClientConfig(
//         baseUrl: 'https://api.example.com',
//         enableLogging: true,
//         enableRetry: true,
//         maxRetries: 3,
//       ),
//     );

//     // Khởi tạo auth service
//     await _authService.initialize();
    
//     print('Auth Status: ${_authService.authStatus}');
//     print('Is Authenticated: ${_authService.isAuthenticated}');
//     if (_authService.currentUser != null) {
//       print('Current User: ${_authService.currentUser!.fullName}');
//     }
//   }

//   /// Example: Đăng nhập
//   Future<void> exampleLogin() async {
//     try {
//       final loginRequest = LoginRequest(
//         email: '<EMAIL>',
//         password: 'password123',
//         deviceId: 'device_123',
//         deviceName: 'iPhone 15 Pro',
//         rememberMe: true,
//       );

//       print('Đang đăng nhập...');
//       final response = await _authService.login(loginRequest);

//       if (response.success) {
//         print('✅ Đăng nhập thành công!');
//         print('User: ${response.data!.user.fullName}');
//         print('Access Token: ${response.data!.accessToken}');
//         print('Token Type: ${response.data!.tokenType}');
        
//         if (response.data!.expiresIn != null) {
//           print('Expires In: ${response.data!.expiresIn} seconds');
//         }
//       } else {
//         print('❌ Đăng nhập thất bại: ${response.message}');
//         if (response.errors != null) {
//           print('Errors: ${response.errors}');
//         }
//       }
//     } catch (e) {
//       print('❌ Lỗi khi đăng nhập: $e');
//     }
//   }

//   /// Example: Đăng ký
//   Future<void> exampleRegister() async {
//     try {
//       final registerRequest = RegisterRequest(
//         email: '<EMAIL>',
//         password: 'password123',
//         confirmPassword: 'password123',
//         firstName: 'John',
//         lastName: 'Doe',
//         phone: '+84123456789',
//         department: 'IT Department',
//         additionalData: {
//           'company': 'ABC Corp',
//           'position': 'Developer',
//         },
//       );

//       print('Đang đăng ký...');
//       final response = await _authService.register(registerRequest);

//       if (response.success) {
//         print('✅ Đăng ký thành công!');
//         print('User: ${response.data!.user.fullName}');
//         print('Email: ${response.data!.user.email}');
//         print('Email Verified: ${response.data!.user.isEmailVerified}');
//       } else {
//         print('❌ Đăng ký thất bại: ${response.message}');
//         if (response.errors != null) {
//           print('Errors: ${response.errors}');
//         }
//       }
//     } catch (e) {
//       print('❌ Lỗi khi đăng ký: $e');
//     }
//   }

//   /// Example: Lấy thông tin user hiện tại
//   Future<void> exampleGetCurrentUser() async {
//     try {
//       print('Đang lấy thông tin user...');
//       final response = await _authService.getCurrentUser();

//       if (response.success) {
//         final user = response.data!;
//         print('✅ Lấy thông tin user thành công!');
//         print('ID: ${user.id}');
//         print('Name: ${user.fullName}');
//         print('Email: ${user.email}');
//         print('Phone: ${user.phone ?? 'N/A'}');
//         print('Department: ${user.department ?? 'N/A'}');
//         print('Email Verified: ${user.isEmailVerified}');
//         print('Active: ${user.isActive}');
//         print('Created At: ${user.createdAt}');
//       } else {
//         print('❌ Lấy thông tin user thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi lấy thông tin user: $e');
//     }
//   }

//   /// Example: Cập nhật thông tin user
//   Future<void> exampleUpdateProfile() async {
//     try {
//       final updateData = {
//         'first_name': 'John Updated',
//         'last_name': 'Doe Updated',
//         'phone': '+84987654321',
//         'department': 'Engineering Department',
//       };

//       print('Đang cập nhật thông tin...');
//       final response = await _authService.updateProfile(updateData);

//       if (response.success) {
//         print('✅ Cập nhật thông tin thành công!');
//         print('Updated User: ${response.data!.fullName}');
//         print('New Phone: ${response.data!.phone}');
//         print('New Department: ${response.data!.department}');
//       } else {
//         print('❌ Cập nhật thông tin thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi cập nhật thông tin: $e');
//     }
//   }

//   /// Example: Đổi mật khẩu
//   Future<void> exampleChangePassword() async {
//     try {
//       print('Đang đổi mật khẩu...');
//       final response = await _authService.changePassword(
//         currentPassword: 'oldpassword123',
//         newPassword: 'newpassword123',
//         confirmPassword: 'newpassword123',
//       );

//       if (response.success) {
//         print('✅ Đổi mật khẩu thành công!');
//       } else {
//         print('❌ Đổi mật khẩu thất bại: ${response.message}');
//         if (response.errors != null) {
//           print('Errors: ${response.errors}');
//         }
//       }
//     } catch (e) {
//       print('❌ Lỗi khi đổi mật khẩu: $e');
//     }
//   }

//   /// Example: Quên mật khẩu
//   Future<void> exampleForgotPassword() async {
//     try {
//       print('Đang gửi email reset password...');
//       final response = await _authService.forgotPassword('<EMAIL>');

//       if (response.success) {
//         print('✅ Email reset password đã được gửi!');
//       } else {
//         print('❌ Gửi email reset password thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi gửi email reset password: $e');
//     }
//   }

//   /// Example: Reset mật khẩu
//   Future<void> exampleResetPassword() async {
//     try {
//       print('Đang reset mật khẩu...');
//       final response = await _authService.resetPassword(
//         token: 'reset_token_from_email',
//         email: '<EMAIL>',
//         newPassword: 'newpassword123',
//         confirmPassword: 'newpassword123',
//       );

//       if (response.success) {
//         print('✅ Reset mật khẩu thành công!');
//       } else {
//         print('❌ Reset mật khẩu thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi reset mật khẩu: $e');
//     }
//   }

//   /// Example: Xác thực email
//   Future<void> exampleVerifyEmail() async {
//     try {
//       print('Đang xác thực email...');
//       final response = await _authService.verifyEmail('verification_token');

//       if (response.success) {
//         print('✅ Xác thực email thành công!');
//         print('Current user email verified: ${_authService.currentUser?.isEmailVerified}');
//       } else {
//         print('❌ Xác thực email thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi xác thực email: $e');
//     }
//   }

//   /// Example: Gửi lại email xác thực
//   Future<void> exampleResendVerificationEmail() async {
//     try {
//       print('Đang gửi lại email xác thực...');
//       final response = await _authService.resendVerificationEmail();

//       if (response.success) {
//         print('✅ Email xác thực đã được gửi lại!');
//       } else {
//         print('❌ Gửi lại email xác thực thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi gửi lại email xác thực: $e');
//     }
//   }

//   /// Example: Đăng xuất
//   Future<void> exampleLogout() async {
//     try {
//       print('Đang đăng xuất...');
//       final response = await _authService.logout();

//       if (response.success) {
//         print('✅ Đăng xuất thành công!');
//         print('Auth Status: ${_authService.authStatus}');
//         print('Is Authenticated: ${_authService.isAuthenticated}');
//       } else {
//         print('❌ Đăng xuất thất bại: ${response.message}');
//       }
//     } catch (e) {
//       print('❌ Lỗi khi đăng xuất: $e');
//     }
//   }

//   /// Example: Kiểm tra trạng thái authentication
//   void exampleCheckAuthStatus() {
//     print('=== Auth Status Check ===');
//     print('Auth Status: ${_authService.authStatus}');
//     print('Is Authenticated: ${_authService.isAuthenticated}');
//     print('Is Loading: ${_authService.isLoading}');
//     print('Needs Email Verification: ${_authService.needsEmailVerification}');
//     print('Is User Active: ${_authService.isUserActive}');
    
//     if (_authService.currentUser != null) {
//       final user = _authService.currentUser!;
//       print('Current User:');
//       print('  - ID: ${user.id}');
//       print('  - Name: ${user.fullName}');
//       print('  - Email: ${user.email}');
//       print('  - Department: ${user.department ?? 'N/A'}');
//       print('  - Email Verified: ${user.isEmailVerified}');
//       print('  - Active: ${user.isActive}');
//     } else {
//       print('No current user');
//     }
//   }

//   /// Chạy tất cả examples
//   Future<void> runAllExamples() async {
//     print('=== AuthService Examples ===\n');

//     print('1. Initialize Services:');
//     await initializeServices();
//     print('');

//     print('2. Check Auth Status:');
//     exampleCheckAuthStatus();
//     print('');

//     print('3. Login Example:');
//     await exampleLogin();
//     print('');

//     print('4. Get Current User Example:');
//     await exampleGetCurrentUser();
//     print('');

//     print('5. Update Profile Example:');
//     await exampleUpdateProfile();
//     print('');

//     print('6. Change Password Example:');
//     await exampleChangePassword();
//     print('');

//     print('7. Forgot Password Example:');
//     await exampleForgotPassword();
//     print('');

//     print('8. Verify Email Example:');
//     await exampleVerifyEmail();
//     print('');

//     print('9. Resend Verification Email Example:');
//     await exampleResendVerificationEmail();
//     print('');

//     print('10. Logout Example:');
//     await exampleLogout();
//     print('');

//     print('=== Examples completed ===');
//   }
// }

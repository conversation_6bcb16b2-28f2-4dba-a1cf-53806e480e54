/// Remote Configuration Server Implementation
/// 
/// Provides server-side functionality for managing remote configurations
/// including device registration, configuration distribution, and real-time updates.

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// Simple HTTP server for remote configuration management
class RemoteConfigServer {
  HttpServer? _server;
  final int port;
  final Map<String, Map<String, dynamic>> _deviceConfigs = {};
  final Map<String, DateTime> _lastUpdated = {};
  final List<WebSocket> _connectedClients = [];
  
  bool _isRunning = false;

  RemoteConfigServer({this.port = 3000});

  /// Start the configuration server
  Future<void> start() async {
    if (_isRunning) return;

    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, port);
      _isRunning = true;
      
      debugPrint('🚀 Remote Config Server started on port $port');
      
      await for (HttpRequest request in _server!) {
        _handleRequest(request);
      }
    } catch (e) {
      debugPrint('❌ Failed to start config server: $e');
      rethrow;
    }
  }

  /// Stop the configuration server
  Future<void> stop() async {
    if (!_isRunning) return;

    await _server?.close();
    _server = null;
    _isRunning = false;
    
    // Close all WebSocket connections
    for (final client in _connectedClients) {
      await client.close();
    }
    _connectedClients.clear();
    
    debugPrint('🛑 Remote Config Server stopped');
  }

  /// Handle incoming HTTP requests
  void _handleRequest(HttpRequest request) async {
    try {
      // Enable CORS
      request.response.headers.add('Access-Control-Allow-Origin', '*');
      request.response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      request.response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (request.method == 'OPTIONS') {
        request.response.statusCode = 200;
        await request.response.close();
        return;
      }

      final path = request.uri.path;
      final method = request.method;

      debugPrint('📡 ${method} ${path}');

      switch (path) {
        case '/health':
          await _handleHealthCheck(request);
          break;
        case '/status':
          await _handleStatus(request);
          break;
        case '/config':
          if (method == 'POST') {
            await _handleCreateConfig(request);
          } else {
            await _handleNotFound(request);
          }
          break;
        case '/ws':
          await _handleWebSocket(request);
          break;
        default:
          if (path.startsWith('/config/')) {
            final deviceId = path.substring(8);
            if (deviceId.contains('/')) {
              final parts = deviceId.split('/');
              final id = parts[0];
              final action = parts[1];
              
              switch (action) {
                case 'info':
                  await _handleConfigInfo(request, id);
                  break;
                case 'history':
                  await _handleConfigHistory(request, id);
                  break;
                default:
                  await _handleNotFound(request);
              }
            } else {
              switch (method) {
                case 'GET':
                  await _handleGetConfig(request, deviceId);
                  break;
                case 'PUT':
                  await _handleUpdateConfig(request, deviceId);
                  break;
                case 'DELETE':
                  await _handleDeleteConfig(request, deviceId);
                  break;
                default:
                  await _handleNotFound(request);
              }
            }
          } else {
            await _handleNotFound(request);
          }
      }
    } catch (e) {
      debugPrint('❌ Error handling request: $e');
      await _handleError(request, e);
    }
  }

  /// Handle health check
  Future<void> _handleHealthCheck(HttpRequest request) async {
    final response = {
      'status': 'healthy',
      'timestamp': DateTime.now().toIso8601String(),
      'uptime': _isRunning ? 'running' : 'stopped',
    };
    
    await _sendJsonResponse(request, response);
  }

  /// Handle server status
  Future<void> _handleStatus(HttpRequest request) async {
    final response = {
      'server': 'Remote Config Server',
      'version': '1.0.0',
      'port': port,
      'devices': _deviceConfigs.length,
      'connected_clients': _connectedClients.length,
      'last_updated': _lastUpdated.isNotEmpty 
          ? _lastUpdated.values.reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
          : null,
    };
    
    await _sendJsonResponse(request, response);
  }

  /// Handle get configuration for device
  Future<void> _handleGetConfig(HttpRequest request, String deviceId) async {
    if (!_deviceConfigs.containsKey(deviceId)) {
      request.response.statusCode = 404;
      await _sendJsonResponse(request, {
        'error': 'Device configuration not found',
        'device_id': deviceId,
      });
      return;
    }

    final config = _deviceConfigs[deviceId]!;
    await _sendJsonResponse(request, {
      'device_id': deviceId,
      'config': config,
      'last_updated': _lastUpdated[deviceId]?.toIso8601String(),
    });
  }

  /// Handle create configuration
  Future<void> _handleCreateConfig(HttpRequest request) async {
    final body = await _readRequestBody(request);
    final data = jsonDecode(body) as Map<String, dynamic>;
    
    final deviceId = data['device_id'] as String?;
    final config = data['config'] as Map<String, dynamic>? ?? {};
    
    if (deviceId == null || deviceId.isEmpty) {
      request.response.statusCode = 400;
      await _sendJsonResponse(request, {
        'error': 'device_id is required',
      });
      return;
    }

    _deviceConfigs[deviceId] = Map<String, dynamic>.from(config);
    _lastUpdated[deviceId] = DateTime.now();
    
    request.response.statusCode = 201;
    await _sendJsonResponse(request, {
      'message': 'Configuration created',
      'device_id': deviceId,
    });

    // Notify connected clients
    _notifyClients(deviceId, 'config_created', config);
  }

  /// Handle update configuration
  Future<void> _handleUpdateConfig(HttpRequest request, String deviceId) async {
    final body = await _readRequestBody(request);
    final data = jsonDecode(body) as Map<String, dynamic>;
    
    final config = data['config'] as Map<String, dynamic>? ?? {};
    
    _deviceConfigs[deviceId] = Map<String, dynamic>.from(config);
    _lastUpdated[deviceId] = DateTime.now();
    
    await _sendJsonResponse(request, {
      'message': 'Configuration updated',
      'device_id': deviceId,
    });

    // Notify connected clients
    _notifyClients(deviceId, 'config_updated', config);
  }

  /// Handle delete configuration
  Future<void> _handleDeleteConfig(HttpRequest request, String deviceId) async {
    if (!_deviceConfigs.containsKey(deviceId)) {
      request.response.statusCode = 404;
      await _sendJsonResponse(request, {
        'error': 'Device configuration not found',
        'device_id': deviceId,
      });
      return;
    }

    _deviceConfigs.remove(deviceId);
    _lastUpdated.remove(deviceId);
    
    await _sendJsonResponse(request, {
      'message': 'Configuration deleted',
      'device_id': deviceId,
    });

    // Notify connected clients
    _notifyClients(deviceId, 'config_deleted', {});
  }

  /// Handle configuration info
  Future<void> _handleConfigInfo(HttpRequest request, String deviceId) async {
    if (!_deviceConfigs.containsKey(deviceId)) {
      request.response.statusCode = 404;
      await _sendJsonResponse(request, {
        'error': 'Device configuration not found',
        'device_id': deviceId,
      });
      return;
    }

    final config = _deviceConfigs[deviceId]!;
    await _sendJsonResponse(request, {
      'device_id': deviceId,
      'parameter_count': config.length,
      'last_updated': _lastUpdated[deviceId]?.toIso8601String(),
      'categories': _getConfigCategories(config),
    });
  }

  /// Handle configuration history
  Future<void> _handleConfigHistory(HttpRequest request, String deviceId) async {
    // For simplicity, return mock history data
    // In a real implementation, you would store and retrieve actual history
    await _sendJsonResponse(request, {
      'device_id': deviceId,
      'history': [
        {
          'timestamp': DateTime.now().subtract(Duration(hours: 1)).toIso8601String(),
          'action': 'updated',
          'changes': ['face_detection.min_quality_detection'],
        },
        {
          'timestamp': DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
          'action': 'created',
          'changes': ['initial_configuration'],
        },
      ],
    });
  }

  /// Handle WebSocket connections for real-time updates
  Future<void> _handleWebSocket(HttpRequest request) async {
    try {
      final socket = await WebSocketTransformer.upgrade(request);
      _connectedClients.add(socket);
      
      debugPrint('📱 WebSocket client connected (${_connectedClients.length} total)');
      
      socket.listen(
        (message) {
          debugPrint('📨 WebSocket message: $message');
          // Handle incoming WebSocket messages if needed
        },
        onDone: () {
          _connectedClients.remove(socket);
          debugPrint('📱 WebSocket client disconnected (${_connectedClients.length} total)');
        },
        onError: (error) {
          debugPrint('❌ WebSocket error: $error');
          _connectedClients.remove(socket);
        },
      );
      
      // Send welcome message
      socket.add(jsonEncode({
        'type': 'welcome',
        'message': 'Connected to Remote Config Server',
        'timestamp': DateTime.now().toIso8601String(),
      }));
      
    } catch (e) {
      debugPrint('❌ WebSocket upgrade failed: $e');
    }
  }

  /// Handle 404 Not Found
  Future<void> _handleNotFound(HttpRequest request) async {
    request.response.statusCode = 404;
    await _sendJsonResponse(request, {
      'error': 'Not Found',
      'path': request.uri.path,
    });
  }

  /// Handle errors
  Future<void> _handleError(HttpRequest request, dynamic error) async {
    request.response.statusCode = 500;
    await _sendJsonResponse(request, {
      'error': 'Internal Server Error',
      'message': error.toString(),
    });
  }

  /// Send JSON response
  Future<void> _sendJsonResponse(HttpRequest request, Map<String, dynamic> data) async {
    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(data));
    await request.response.close();
  }

  /// Read request body
  Future<String> _readRequestBody(HttpRequest request) async {
    final completer = Completer<String>();
    final contents = StringBuffer();
    
    request.transform(utf8.decoder).listen(
      (data) => contents.write(data),
      onDone: () => completer.complete(contents.toString()),
      onError: (error) => completer.completeError(error),
    );
    
    return completer.future;
  }

  /// Notify connected WebSocket clients
  void _notifyClients(String deviceId, String action, Map<String, dynamic> config) {
    final message = jsonEncode({
      'type': 'config_change',
      'device_id': deviceId,
      'action': action,
      'config': config,
      'timestamp': DateTime.now().toIso8601String(),
    });

    for (final client in _connectedClients.toList()) {
      try {
        client.add(message);
      } catch (e) {
        debugPrint('❌ Failed to send message to client: $e');
        _connectedClients.remove(client);
      }
    }
  }

  /// Get configuration categories
  List<String> _getConfigCategories(Map<String, dynamic> config) {
    final categories = <String>{};
    for (final key in config.keys) {
      if (key.contains('.')) {
        categories.add(key.split('.').first);
      }
    }
    return categories.toList()..sort();
  }

  /// Get server info
  Map<String, dynamic> get serverInfo => {
    'running': _isRunning,
    'port': port,
    'devices': _deviceConfigs.length,
    'connected_clients': _connectedClients.length,
  };
}

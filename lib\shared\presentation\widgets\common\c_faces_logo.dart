import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CFacesLogo extends StatelessWidget {
  final double? width;
  final double? height;
  final Color? color;
  final BoxFit fit;

  const CFacesLogo({
    super.key,
    this.width,
    this.height,
    this.color,
    this.fit = BoxFit.contain,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.string(
      _logoSvg,
      width: width,
      height: height,
      fit: fit,
      colorFilter: color != null 
        ? ColorFilter.mode(color!, BlendMode.srcIn)
        : null,
    );
  }

  static const String _logoSvg = '''
<svg width="146" height="34" viewBox="0 0 146 34" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.8002 28.3041C14.7487 28.9028 14.3922 29.2521 14.0924 29.4393C13.1444 29.9084 12.0815 30.177 10.9539 30.177C7.01057 30.177 3.81458 26.9744 3.81458 23.0235C3.81458 19.0727 7.01057 15.8701 10.9539 15.8701C11.6312 15.8701 12.2862 15.9687 12.9074 16.1454V12.2263C12.2723 12.1125 11.62 12.0483 10.9539 12.0483C4.90456 12.0483 0 16.9617 0 23.0235C0 29.0854 4.9039 33.9988 10.9539 33.9988C13.6811 33.9988 16.2308 32.9211 17.7572 31.5768C17.8522 31.4723 17.9579 31.3757 18.0925 31.158C18.0925 31.158 18.1625 31.0469 18.1638 31.0462C18.2074 30.9748 18.3005 30.814 18.3097 30.7929C18.383 30.6328 18.4484 30.4541 18.4999 30.2477C18.4999 30.2424 18.5019 30.2372 18.5032 30.2319C18.5085 30.2107 18.5104 30.1856 18.5157 30.165C18.523 30.126 18.5296 30.0843 18.5375 30.046C18.5468 29.9831 18.556 29.9203 18.5646 29.8535C18.5712 29.7979 18.5824 29.6318 18.5877 29.5743V25.5467L14.8008 20.073V28.3041H14.8002Z" fill="#008FD3"/>
<path d="M36.7068 12.7356C35.9417 13.0366 34.1413 13.9013 32.7279 15.8065C32.7239 15.8032 32.7193 15.7993 32.7166 15.7973L25.7536 25.839L18.7912 15.7973C18.7873 15.7993 18.784 15.8032 18.78 15.8065C17.3665 13.9013 15.5662 13.0373 14.801 12.7356V16.7466L25.7536 32.5452L36.7075 16.7466V12.7356H36.7068Z" fill="#008FD3"/>
<path d="M40.5533 12.0483C39.8852 12.0483 39.2336 12.1119 38.5998 12.227V16.1454C39.2211 15.9687 39.876 15.8701 40.5533 15.8701C44.496 15.8701 47.692 19.0727 47.692 23.0242C47.692 26.975 44.496 30.1763 40.5533 30.1763C39.4244 30.1763 38.3615 29.9077 37.4135 29.44C37.1151 29.2514 36.7579 28.9035 36.7064 28.3034V20.0724L32.9196 25.5461V29.5743C32.9248 29.6318 32.9354 29.7979 32.9427 29.8548C32.9499 29.9209 32.9592 29.9838 32.9697 30.046C32.977 30.085 32.9836 30.1267 32.9915 30.165C32.9955 30.1862 32.9988 30.2114 33.0041 30.2319C33.0054 30.2372 33.006 30.2424 33.0074 30.2477C33.0575 30.4541 33.1236 30.6328 33.1955 30.7929C33.2061 30.814 33.2998 30.9748 33.3441 31.0462C33.3441 31.0476 33.4154 31.158 33.4154 31.158C33.5487 31.3757 33.6557 31.4716 33.7494 31.5768C35.2758 32.9211 37.8254 34.0001 40.554 34.0001C46.6027 34.0001 51.5079 29.0861 51.5079 23.0249C51.5072 16.9617 46.602 12.0483 40.5533 12.0483Z" fill="#008FD3"/>
<path d="M18.7935 12.4948C18.6872 12.0053 18.6298 11.4978 18.6298 10.9765C18.6298 7.03432 21.8192 3.83901 25.7539 3.83901C29.6886 3.83901 32.878 7.03432 32.878 10.9765C32.878 11.4978 32.8206 12.0053 32.715 12.4948C33.8875 11.6368 35.2376 11.0083 36.6992 10.6709C36.5375 4.74997 31.7009 0 25.7539 0C19.8069 0 14.9703 4.74997 14.8086 10.6709C16.2696 11.0083 17.6197 11.6368 18.7935 12.4948Z" fill="#008FD3"/>
<path d="M69.4678 24.288C64.5718 24.288 60.9238 20.472 60.9238 15.648V15.6C60.9238 10.824 64.4998 6.912 69.5878 6.912C72.6838 6.912 74.5558 7.992 76.1638 9.528L74.2678 11.712C72.9238 10.464 71.4838 9.624 69.5638 9.624C66.3478 9.624 64.0198 12.264 64.0198 15.552V15.6C64.0198 18.888 66.3478 21.576 69.5638 21.576C71.6278 21.576 72.9478 20.736 74.3878 19.392L76.2838 21.312C74.5318 23.136 72.6118 24.288 69.4678 24.288ZM78.5706 18.072V15.216H85.6746V18.072H78.5706ZM96.9834 24.288C92.0874 24.288 88.4394 20.472 88.4394 15.648V15.6C88.4394 10.824 92.0154 6.912 97.1034 6.912C100.199 6.912 102.071 7.992 103.679 9.528L101.783 11.712C100.439 10.464 98.9994 9.624 97.0794 9.624C93.8634 9.624 91.5354 12.264 91.5354 15.552V15.6C91.5354 18.888 93.8634 21.576 97.0794 21.576C99.1434 21.576 100.463 20.736 101.903 19.392L103.799 21.312C102.047 23.136 100.127 24.288 96.9834 24.288ZM105.462 24L112.854 7.08H115.59L122.982 24H119.862L118.158 19.944H110.214L108.486 24H105.462ZM111.294 17.328H117.078L114.174 10.608L111.294 17.328ZM125.863 24V7.2H129.007L134.119 15.144L139.231 7.2H142.375V24H139.423V11.952L134.119 19.872H134.023L128.767 12V24H125.863Z" fill="#008FD3"/>
</svg>''';
}

// Predefined logo variants for common use cases
class CFacesLogoVariants {
  // Small logo for app bars
  static Widget small({Color? color}) => CFacesLogo(
    height: 24,
    color: color,
  );

  // Medium logo for headers
  static Widget medium({Color? color}) => CFacesLogo(
    height: 34,
    color: color,
  );

  // Large logo for splash screens
  static Widget large({Color? color}) => CFacesLogo(
    height: 48,
    color: color,
  );

  // Extra large for hero sections
  static Widget extraLarge({Color? color}) => CFacesLogo(
    height: 64,
    color: color,
  );

  // White variant for dark backgrounds
  static Widget white({double? height}) => CFacesLogo(
    height: height ?? 34,
    color: Colors.white,
  );

  // Brand color variant
  static Widget brandColor({double? height}) => CFacesLogo(
    height: height ?? 34,
    color: const Color(0xFF008FD3),
  );
}

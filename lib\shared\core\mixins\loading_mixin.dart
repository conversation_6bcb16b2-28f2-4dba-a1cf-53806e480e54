import 'package:flutter/foundation.dart';

/// Mixin for managing loading states
/// 
/// Provides standardized loading state management for any class
mixin LoadingMixin on ChangeNotifier {
  bool _isLoading = false;
  final Map<String, bool> _loadingStates = {};

  /// Get overall loading state
  bool get isLoading => _isLoading;

  /// Get loading state for specific operation
  bool isLoadingFor(String operation) => _loadingStates[operation] ?? false;

  /// Get all loading operations
  List<String> get loadingOperations => 
      _loadingStates.entries.where((e) => e.value).map((e) => e.key).toList();

  /// Check if any operation is loading
  bool get hasAnyLoading => _loadingStates.values.any((loading) => loading);

  /// Set overall loading state
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Set loading state for specific operation
  void setLoadingFor(String operation, bool loading) {
    final wasLoading = _loadingStates[operation] ?? false;
    if (wasLoading != loading) {
      _loadingStates[operation] = loading;
      _updateOverallLoading();
      notifyListeners();
    }
  }

  /// Execute operation with loading state management
  Future<T> executeWithLoading<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    final opName = operationName ?? 'default';
    
    try {
      setLoadingFor(opName, true);
      return await operation();
    } finally {
      setLoadingFor(opName, false);
    }
  }

  /// Clear all loading states
  void clearAllLoading() {
    _isLoading = false;
    _loadingStates.clear();
    notifyListeners();
  }

  /// Clear loading state for specific operation
  void clearLoadingFor(String operation) {
    if (_loadingStates.containsKey(operation)) {
      _loadingStates.remove(operation);
      _updateOverallLoading();
      notifyListeners();
    }
  }

  /// Update overall loading state based on individual operations
  void _updateOverallLoading() {
    final newOverallLoading = hasAnyLoading;
    if (_isLoading != newOverallLoading) {
      _isLoading = newOverallLoading;
    }
  }
}

/// Mixin for managing error states
/// 
/// Provides standardized error state management
mixin ErrorMixin on ChangeNotifier {
  String? _errorMessage;
  final Map<String, String> _errorStates = {};

  /// Get overall error message
  String? get errorMessage => _errorMessage;

  /// Get error message for specific operation
  String? getErrorFor(String operation) => _errorStates[operation];

  /// Check if there's any error
  bool get hasError => _errorMessage != null;

  /// Check if there's error for specific operation
  bool hasErrorFor(String operation) => _errorStates.containsKey(operation);

  /// Get all operations with errors
  List<String> get errorOperations => _errorStates.keys.toList();

  /// Set overall error message
  void setError(String? error) {
    if (_errorMessage != error) {
      _errorMessage = error;
      notifyListeners();
    }
  }

  /// Set error for specific operation
  void setErrorFor(String operation, String? error) {
    if (error != null) {
      _errorStates[operation] = error;
    } else {
      _errorStates.remove(operation);
    }
    _updateOverallError();
    notifyListeners();
  }

  /// Clear all errors
  void clearAllErrors() {
    _errorMessage = null;
    _errorStates.clear();
    notifyListeners();
  }

  /// Clear error for specific operation
  void clearErrorFor(String operation) {
    if (_errorStates.containsKey(operation)) {
      _errorStates.remove(operation);
      _updateOverallError();
      notifyListeners();
    }
  }

  /// Update overall error based on individual operation errors
  void _updateOverallError() {
    if (_errorStates.isNotEmpty) {
      _errorMessage = _errorStates.values.first;
    } else {
      _errorMessage = null;
    }
  }
}

/// Mixin for managing success states
/// 
/// Provides standardized success state management
mixin SuccessMixin on ChangeNotifier {
  String? _successMessage;
  final Map<String, String> _successStates = {};

  /// Get overall success message
  String? get successMessage => _successMessage;

  /// Get success message for specific operation
  String? getSuccessFor(String operation) => _successStates[operation];

  /// Check if there's any success
  bool get hasSuccess => _successMessage != null;

  /// Check if there's success for specific operation
  bool hasSuccessFor(String operation) => _successStates.containsKey(operation);

  /// Set overall success message
  void setSuccess(String? success) {
    if (_successMessage != success) {
      _successMessage = success;
      notifyListeners();
    }
  }

  /// Set success for specific operation
  void setSuccessFor(String operation, String? success) {
    if (success != null) {
      _successStates[operation] = success;
    } else {
      _successStates.remove(operation);
    }
    _updateOverallSuccess();
    notifyListeners();
  }

  /// Clear all success messages
  void clearAllSuccess() {
    _successMessage = null;
    _successStates.clear();
    notifyListeners();
  }

  /// Clear success for specific operation
  void clearSuccessFor(String operation) {
    if (_successStates.containsKey(operation)) {
      _successStates.remove(operation);
      _updateOverallSuccess();
      notifyListeners();
    }
  }

  /// Update overall success based on individual operation success
  void _updateOverallSuccess() {
    if (_successStates.isNotEmpty) {
      _successMessage = _successStates.values.first;
    } else {
      _successMessage = null;
    }
  }
}

/// Mixin for validation functionality
mixin ValidationMixin on ChangeNotifier {
  final Map<String, String?> _validationErrors = {};

  /// Get validation error for field
  String? getValidationError(String field) => _validationErrors[field];

  /// Check if field has validation error
  bool hasValidationError(String field) => _validationErrors.containsKey(field) && _validationErrors[field] != null;

  /// Check if any field has validation error
  bool get hasAnyValidationError => _validationErrors.values.any((error) => error != null);

  /// Set validation error for field
  void setValidationError(String field, String? error) {
    _validationErrors[field] = error;
    notifyListeners();
  }

  /// Clear validation error for field
  void clearValidationError(String field) {
    _validationErrors.remove(field);
    notifyListeners();
  }

  /// Clear all validation errors
  void clearAllValidationErrors() {
    _validationErrors.clear();
    notifyListeners();
  }

  /// Validate field with custom validator
  bool validateField(String field, String? value, String? Function(String?) validator) {
    final error = validator(value);
    setValidationError(field, error);
    return error == null;
  }
}

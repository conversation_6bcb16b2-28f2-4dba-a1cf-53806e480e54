# 🎛️ Admin Config Toggle Feature Demo

## ✨ Tính năng mới: Toggle Manual/Default Mode cho từng Tab

Mỗi tab trong Admin Configuration Screen giờ đây có toggle để chuyển đổi giữa:
- **🔧 Cấu hình thủ công**: <PERSON> phép chỉnh sửa tất cả tham số
- **⚡ Giá trị mặc định**: Sử dụng giá trị được khuyến nghị

## 🎯 Cách hoạt động:

### 1. **Toggle ở đầu mỗi tab**
```
┌─────────────────────────────────────────────────────────┐
│ 🔧 Cấu hình thủ công                          [ON/OFF] │
│ Cho phép chỉnh sửa các tham số trong danh mục này      │
└─────────────────────────────────────────────────────────┘
```

### 2. **Ch<PERSON> độ Default (OFF)**
- ✅ Tất cả tham số sử dụng giá trị mặc định
- 🔒 Các field bị disable, không thể chỉnh sửa
- 💡 Hiển thị icon khóa và text "Bật chế độ thủ công để chỉnh sửa"
- 🎨 <PERSON><PERSON>u xanh lá cây cho toggle

### 3. **Chế độ Manual (ON)**
- ✏️ Cho phép chỉnh sửa tất cả tham số
- 🔓 Tất cả field có thể chỉnh sửa
- 🔄 Button "Reset to default" cho từng parameter
- 🎨 Màu cam cho toggle

## 🚀 Ưu điểm:

### **Cho người dùng thông thường:**
- 🛡️ **An toàn**: Không thể vô tình thay đổi cấu hình quan trọng
- ⚡ **Nhanh chóng**: Sử dụng giá trị mặc định được tối ưu
- 🎯 **Đơn giản**: Giao diện rõ ràng, dễ hiểu

### **Cho admin chuyên nghiệp:**
- 🔧 **Linh hoạt**: Có thể fine-tune từng parameter khi cần
- 📊 **Kiểm soát**: Biết chính xác parameter nào đang custom
- 🔄 **Dễ rollback**: Có thể nhanh chóng quay về default

## 📱 Giao diện:

### **Tab Header với Toggle:**
```
┌─────────────────────────────────────────────────────────┐
│ ⚡ Sử dụng giá trị mặc định                    [●○○○○] │
│ Tất cả tham số sử dụng giá trị mặc định được khuyến nghị│
└─────────────────────────────────────────────────────────┘
```

### **Parameter Card (Default Mode):**
```
┌─────────────────────────────────────────────────────────┐
│ 📊 Minimum Face Quality                                │
│ face_detection.min_quality_detection                   │
│ Minimum face quality threshold for detection (0.0-1.0) │
│                                                         │
│ ┌─────────────────────────────────────────────────┐ 🔒 │
│ │ 0.4                                             │    │
│ │ Bật chế độ thủ công để chỉnh sửa                │    │
│ └─────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
```

### **Parameter Card (Manual Mode):**
```
┌─────────────────────────────────────────────────────────┐
│ 📊 Minimum Face Quality                                │
│ face_detection.min_quality_detection                   │
│ Minimum face quality threshold for detection (0.0-1.0) │
│                                                         │
│ ┌─────────────────────────────────────────────────┐ 🔄 │
│ │ 0.6                                             │    │
│ └─────────────────────────────────────────────────┘    │
│ Range: 0.0 - 1.0                                       │
└─────────────────────────────────────────────────────────┘
```

## 🎛️ Trạng thái được lưu:

- ✅ Trạng thái toggle được lưu cho từng category
- ✅ Khi tắt manual mode → tự động reset về default
- ✅ Khi bật manual mode → giữ nguyên giá trị hiện tại
- ✅ Trạng thái được persist qua các lần mở app

## 🔧 Technical Implementation:

1. **State Management**: `Map<String, bool> _categoryManualMode`
2. **Persistence**: Lưu với key `${category}_manual_mode`
3. **Auto Reset**: Khi tắt manual mode → reset về default values
4. **UI Updates**: Real-time enable/disable fields
5. **Validation**: Chỉ validate khi ở manual mode

## 🎯 Kết quả:

Người dùng giờ có thể:
- ✅ Sử dụng cấu hình an toàn với default values
- ✅ Chuyển sang manual mode khi cần fine-tuning
- ✅ Dễ dàng rollback về default bất cứ lúc nào
- ✅ Hiểu rõ đang ở chế độ nào qua visual indicators

**Perfect balance giữa simplicity và flexibility!** 🎉

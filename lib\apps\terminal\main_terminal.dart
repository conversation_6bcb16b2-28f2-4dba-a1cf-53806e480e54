import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/config/flavor_config.dart';
import '../../core/config/app_config.dart';
import '../../shared/presentation/themes/index.dart';
import 'routes/index.dart';
import 'providers/terminal_auth_provider.dart';
import 'providers/kiosk_mode_provider.dart';
import 'providers/face_detection_provider.dart';
import '../../shared/providers/captured_face_provider.dart';
import 'di/index.dart';
import '../../shared/core/config/config_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize configuration system first
  await ConfigInitializer.initializeForTerminal(
    deviceId: 'terminal_001',
    enableRemoteConfig: false, // Disable remote config for now
  );

  // Khởi tạo flavor config cho terminal app
  FlavorConfig.initialize(
    flavor: Flavor.terminal,
    appName: 'C-CAM Terminal',
    appId: 'com.ccam.terminal',
    appVersion: '1.0.0',
    isDebug: true,
    values: AppConfig.terminalConfig,
  );

  // Setup terminal dependencies
  await setupTerminal();

  runApp(const TerminalApp());
}

class TerminalApp extends StatelessWidget {
  const TerminalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<TerminalAuthProvider>(
          create: (_) => TerminalAuthProvider()..initialize(),
        ),
        ChangeNotifierProvider<KioskModeProvider>(
          create: (_) => KioskModeProvider()..enableKioskMode(),
        ),
        ChangeNotifierProvider<CapturedFaceProvider>(
          create: (_) => CapturedFaceProvider(),
        ),
        ChangeNotifierProvider<TerminalFaceDetectionProvider>(
          create: (_) => TerminalFaceDetectionProvider(
            useMediaPipe: true, // Use MediaPipe by default for terminal (mock for now)
            enableLandmarks: true,
            enableFallback: true,
            confidenceThreshold: 0.6,
            maxFaces: 3,
          )..initialize(),
        ),
      ],
      child: MaterialApp.router(
        title: FlavorConfig.instance.appName,
        debugShowCheckedModeBanner: FlavorConfig.instance.isDebug,
        theme: TerminalTheme.lightTheme,
        darkTheme: TerminalTheme.darkTheme,
        themeMode: ThemeMode.system,
        routerConfig: TerminalRouter.createRouter(),
      ),
    );
  }
}



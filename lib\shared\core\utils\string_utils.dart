/// Comprehensive string utilities for the application
class StringUtils {
  // Private constructor to prevent instantiation
  StringUtils._();

  /// Check if string is null or empty
  static bool isNullOrEmpty(String? value) {
    return value == null || value.isEmpty;
  }

  /// Check if string is null, empty, or whitespace only
  static bool isNullOrWhitespace(String? value) {
    return value == null || value.trim().isEmpty;
  }

  /// Get string or default value if null/empty
  static String orDefault(String? value, [String defaultValue = '']) {
    return isNullOrEmpty(value) ? defaultValue : value!;
  }

  /// Capitalize first letter
  static String capitalize(String value) {
    if (value.isEmpty) return value;
    return value[0].toUpperCase() + value.substring(1).toLowerCase();
  }

  /// Capitalize each word
  static String capitalizeWords(String value) {
    if (value.isEmpty) return value;
    return value.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// Convert to title case
  static String toTitleCase(String value) {
    return capitalizeWords(value);
  }

  /// Convert to camelCase
  static String toCamelCase(String value) {
    if (value.isEmpty) return value;
    final words = value.split(RegExp(r'[\s_-]+'));
    if (words.isEmpty) return value;
    
    final first = words.first.toLowerCase();
    final rest = words.skip(1).map((word) => capitalize(word));
    return first + rest.join('');
  }

  /// Convert to snake_case
  static String toSnakeCase(String value) {
    if (value.isEmpty) return value;
    return value
        .replaceAllMapped(RegExp(r'[A-Z]'), (match) => '_${match.group(0)!.toLowerCase()}')
        .replaceAll(RegExp(r'[\s-]+'), '_')
        .replaceAll(RegExp(r'^_'), '');
  }

  /// Convert to kebab-case
  static String toKebabCase(String value) {
    if (value.isEmpty) return value;
    return value
        .replaceAllMapped(RegExp(r'[A-Z]'), (match) => '-${match.group(0)!.toLowerCase()}')
        .replaceAll(RegExp(r'[\s_]+'), '-')
        .replaceAll(RegExp(r'^-'), '');
  }

  /// Truncate string with ellipsis
  static String truncate(String value, int maxLength, [String suffix = '...']) {
    if (value.length <= maxLength) return value;
    return value.substring(0, maxLength - suffix.length) + suffix;
  }

  /// Truncate string at word boundary
  static String truncateAtWord(String value, int maxLength, [String suffix = '...']) {
    if (value.length <= maxLength) return value;
    
    final truncated = value.substring(0, maxLength - suffix.length);
    final lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > 0) {
      return truncated.substring(0, lastSpace) + suffix;
    } else {
      return truncated + suffix;
    }
  }

  /// Remove all whitespace
  static String removeWhitespace(String value) {
    return value.replaceAll(RegExp(r'\s+'), '');
  }

  /// Normalize whitespace (replace multiple spaces with single space)
  static String normalizeWhitespace(String value) {
    return value.replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  /// Extract numbers from string
  static String extractNumbers(String value) {
    return value.replaceAll(RegExp(r'[^0-9]'), '');
  }

  /// Extract letters from string
  static String extractLetters(String value) {
    return value.replaceAll(RegExp(r'[^a-zA-Z]'), '');
  }

  /// Extract alphanumeric characters
  static String extractAlphanumeric(String value) {
    return value.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
  }

  /// Check if string contains only numbers
  static bool isNumeric(String value) {
    return RegExp(r'^[0-9]+$').hasMatch(value);
  }

  /// Check if string contains only letters
  static bool isAlpha(String value) {
    return RegExp(r'^[a-zA-Z]+$').hasMatch(value);
  }

  /// Check if string contains only alphanumeric characters
  static bool isAlphanumeric(String value) {
    return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value);
  }

  /// Check if string is a valid email
  static bool isValidEmail(String value) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value);
  }

  /// Check if string is a valid phone number (basic check)
  static bool isValidPhone(String value) {
    final cleaned = extractNumbers(value);
    return cleaned.length >= 10 && cleaned.length <= 15;
  }

  /// Check if string is a valid URL
  static bool isValidUrl(String value) {
    try {
      final uri = Uri.parse(value);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Mask string (e.g., for passwords, credit cards)
  static String mask(String value, {String maskChar = '*', int visibleStart = 0, int visibleEnd = 0}) {
    if (value.length <= visibleStart + visibleEnd) return value;
    
    final start = value.substring(0, visibleStart);
    final end = value.substring(value.length - visibleEnd);
    final masked = maskChar * (value.length - visibleStart - visibleEnd);
    
    return start + masked + end;
  }

  /// Mask email (show first char and domain)
  static String maskEmail(String email) {
    if (!isValidEmail(email)) return email;
    
    final parts = email.split('@');
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 1) return email;
    
    final maskedUsername = username[0] + '*' * (username.length - 1);
    return '$maskedUsername@$domain';
  }

  /// Mask phone number (show last 4 digits)
  static String maskPhone(String phone) {
    final cleaned = extractNumbers(phone);
    if (cleaned.length < 4) return phone;
    
    final masked = '*' * (cleaned.length - 4) + cleaned.substring(cleaned.length - 4);
    return masked;
  }

  /// Count words in string
  static int countWords(String value) {
    if (value.trim().isEmpty) return 0;
    return value.trim().split(RegExp(r'\s+')).length;
  }

  /// Count characters excluding whitespace
  static int countCharacters(String value) {
    return removeWhitespace(value).length;
  }

  /// Reverse string
  static String reverse(String value) {
    return value.split('').reversed.join('');
  }

  /// Check if string is palindrome
  static bool isPalindrome(String value) {
    final cleaned = removeWhitespace(value.toLowerCase());
    return cleaned == reverse(cleaned);
  }

  /// Slugify string (URL-friendly)
  static String slugify(String value) {
    return value
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .replaceAll(RegExp(r'-+'), '-')
        .replaceAll(RegExp(r'^-|-$'), '');
  }

  /// Format file size
  static String formatFileSize(int bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double size = bytes.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(unitIndex == 0 ? 0 : 1)} ${units[unitIndex]}';
  }

  /// Format number with thousand separators
  static String formatNumber(num number, {String separator = ','}) {
    final parts = number.toString().split('.');
    final integerPart = parts[0];
    final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';
    
    final formatted = integerPart.replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
      (match) => '${match.group(1)}$separator',
    );
    
    return formatted + decimalPart;
  }

  /// Parse number from formatted string
  static double? parseNumber(String value, {String separator = ','}) {
    final cleaned = value.replaceAll(separator, '');
    return double.tryParse(cleaned);
  }

  /// Get initials from name
  static String getInitials(String name, {int maxLength = 2}) {
    final words = name.trim().split(RegExp(r'\s+'));
    final initials = words.take(maxLength).map((word) => word.isNotEmpty ? word[0].toUpperCase() : '').join();
    return initials;
  }

  /// Highlight search terms in text
  static String highlightSearch(String text, String searchTerm, {String startTag = '<mark>', String endTag = '</mark>'}) {
    if (searchTerm.isEmpty) return text;
    
    final regex = RegExp(RegExp.escape(searchTerm), caseSensitive: false);
    return text.replaceAllMapped(regex, (match) => '$startTag${match.group(0)}$endTag');
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/components/app_logo.dart';
import '../../../../shared/services/selected_tenant_service.dart';

/// Dashboard screen với overview và recent activities
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Trigger rebuild to update tenant name when coming back from tenants screen
    setState(() {});
  }

  String _getTruncatedTenantName() {
    final tenantName = SelectedTenantService().selectedTenantName;
    if (tenantName.length > 17) {
      return '${tenantName.substring(0, 17)}...';
    }
    return tenantName;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.spacing24),
                    _buildOverviewSection(),
                    SizedBox(height: AppDimensions.spacing24),
                    _buildRecentActivitiesSection(),
                    SizedBox(height: AppDimensions.spacing16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(children: [const AppLogo()]),
          _buildTenantSelector(),
        ],
      ),
    );
  }

  Widget _buildTenantSelector() {
    return GestureDetector(
      onTap: () {
        // Navigate back to tenants screen
        context.go('/tenants');
      },
      child: Container(
        padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.business, size: 12, color: AppColors.primary),
            SizedBox(width: AppDimensions.spacing4),
            Text(
              _getTruncatedTenantName(),
              style: AppTextStyles.caption.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: AppDimensions.spacing4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 12,
              color: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewSection() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tổng quan hôm nay',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacing16),
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildOverviewCard(
                      title: 'Chấm công',
                      value: '1,000/1,234 lượt',
                      icon: Icons.person,
                      iconColor: AppColors.primary,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacing12),
                  Expanded(
                    child: _buildOverviewCard(
                      title: 'Ra vào',
                      value: '1,000 lượt',
                      icon: Icons.key,
                      iconColor: AppColors.primary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.spacing12),
              Row(
                children: [
                  Expanded(
                    child: _buildOverviewCard(
                      title: 'An ninh',
                      value: '1,000 cảnh báo',
                      icon: Icons.security,
                      iconColor: AppColors.primary,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacing12),
                  Expanded(
                    child: _buildOverviewCard(
                      title: 'Hệ thống',
                      value: '13/13 hoạt động',
                      icon: Icons.computer,
                      iconColor: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard({
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
  }) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: AppDimensions.spacing8),
                    Text(
                      value,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusRound,
                  ),
                ),
                child: Icon(icon, size: 12, color: iconColor),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivitiesSection() {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Hoạt động gần đây',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Row(
                children: [
                  Text(
                    'Xem thêm',
                    style: AppTextStyles.caption.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacing2),
                  Icon(Icons.chevron_right, size: 14, color: AppColors.primary),
                ],
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacing16),
          Column(
            children: [
              _buildActivityItem(
                title: 'Camera đã kết nối',
                description: 'Camera 1 đã kết nối thành công với AI Box 1',
                time: '2 phút trước',
                icon: Icons.security,
                iconBgColor: const Color(0xFFE5F4FB),
                iconColor: AppColors.primary,
              ),
              SizedBox(height: AppDimensions.spacing12),
              _buildActivityItem(
                title: 'Thiết bị biên đã kết nối',
                description: 'AI Box 1 đã kết nối tới hệ thống thành công',
                time: '2 phút trước',
                icon: Icons.computer,
                iconBgColor: const Color(0xFFE5F4FB),
                iconColor: AppColors.primary,
              ),
              SizedBox(height: AppDimensions.spacing12),
              _buildActivityItem(
                title: 'Phát hiện lửa',
                description: 'Phát hiện khói lửa ở CTS',
                time: '2 phút trước',
                icon: Icons.local_fire_department,
                iconBgColor: const Color(0xFFFFF5E5),
                iconColor: const Color(0xFFFF9800),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required String title,
    required String description,
    required String time,
    required IconData icon,
    required Color iconBgColor,
    required Color iconColor,
  }) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: iconBgColor,
              borderRadius: BorderRadius.circular(AppDimensions.radiusRound),
            ),
            child: Icon(icon, size: 14, color: iconColor),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: AppDimensions.spacing4),
                Text(
                  description,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: AppDimensions.spacing8),
                Text(
                  time,
                  style: AppTextStyles.caption.copyWith(
                    fontSize: 10,
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


}

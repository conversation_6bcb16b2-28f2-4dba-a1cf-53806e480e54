/// Terminal Dependency Injection Index
///
/// This file exports all terminal-specific DI components for easy importing.
/// The terminal DI system builds upon shared dependencies and adds
/// terminal-specific services like kiosk mode, hardware integration,
/// and terminal UI components.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/terminal/di/index.dart';
///
/// // Setup terminal dependencies
/// await TerminalServiceLocator.setupTerminalDependencies();
///
/// // Use terminal dependencies
/// final kioskService = getIt<KioskModeService>();
/// final hardwareManager = getIt<TerminalHardwareManager>();
///
/// // Check status
/// final isReady = TerminalServiceLocator.isInitialized;
/// ```
library;

// ============================================================================
// TERMINAL SERVICE LOCATOR
// ============================================================================
export 'terminal_service_locator.dart';

// ============================================================================
// TERMINAL DI MODULES
// ============================================================================
export 'modules/terminal_kiosk_module.dart' hide getIt;
export 'modules/terminal_ui_module.dart' hide getIt;
export 'modules/terminal_hardware_module.dart' hide getIt;

// ============================================================================
// SHARED DEPENDENCIES (RE-EXPORT FOR CONVENIENCE)
// ============================================================================
export '../../../shared/core/di/shared_service_locator.dart';

// ============================================================================
// TERMINAL DI UTILITIES
// ============================================================================

// Import required classes for utility functions
import 'terminal_service_locator.dart';

/// Quick setup function for terminal dependencies
Future<void> setupTerminal() async {
  await TerminalServiceLocator.setupTerminalDependencies();
}

/// Quick validation function for terminal dependencies
bool validateTerminal() {
  return TerminalServiceLocator.validateTerminalDependencies();
}

/// Quick status check for terminal dependencies
Map<String, dynamic> getTerminalStatus() {
  return TerminalServiceLocator.getTerminalDependencyStatus();
}

/// Quick cleanup function for terminal dependencies
Future<void> disposeTerminal() async {
  await TerminalServiceLocator.disposeTerminalDependencies();
}

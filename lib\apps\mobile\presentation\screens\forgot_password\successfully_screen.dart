import 'package:flutter/material.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/components/app_logo.dart';

/// Màn hình thông báo thay đổi mật khẩu thành công
class SuccessfullyScreen extends StatelessWidget {
  const SuccessfullyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLogoSection(),
              SizedBox(height: AppDimensions.spacing24),
              Expanded(child: _buildSuccessContent(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Row(children: [const AppLogo()]);
  }

  Widget _buildSuccessContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingM),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildSuccessSection(),
          SizedBox(height: AppDimensions.spacing32),
          _buildBackToLoginButton(context),
        ],
      ),
    );
  }

  Widget _buildSuccessSection() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSuccessIcon(),
            SizedBox(width: AppDimensions.spacing8),
            Text(
              'Thay đổi mật khẩu thành công',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
          ],
        ),
        SizedBox(height: AppDimensions.spacing16),
        Text(
          'Bạn đã cập nhật mật khẩu mới thành công.',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textPrimary.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 24,
      height: 24,
      decoration: const BoxDecoration(
        color: Color(0xFF40BF24),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.check, color: Colors.white, size: 16),
    );
  }

  Widget _buildBackToLoginButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/login', (route) => false);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.chevron_left, color: Colors.white, size: 13),
            SizedBox(width: AppDimensions.spacing8),
            Text(
              'Quay lại đăng nhập',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

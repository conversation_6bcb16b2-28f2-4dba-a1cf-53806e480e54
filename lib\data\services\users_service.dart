import 'dart:math';

/// User status enum
enum UserStatus { active, inactive }

/// User model
class UserModel {
  final String id;
  final String name;
  final String department;
  final UserStatus status;
  final String avatar;
  final String email;
  final String phone;

  UserModel({
    required this.id,
    required this.name,
    required this.department,
    required this.status,
    required this.avatar,
    required this.email,
    required this.phone,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Service để quản lý users data
class UsersService {
  static final UsersService _instance = UsersService._internal();
  factory UsersService() => _instance;
  UsersService._internal();

  // Mock data templates
  final List<String> _firstNames = [
    'Trần', 'Nguyễn', '<PERSON><PERSON>', '<PERSON>ạ<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Đặng',
    '<PERSON><PERSON><PERSON>', 'Đỗ', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>ơ<PERSON>',
    '<PERSON>rịnh', 'Đào', 'Lâm', 'Hà', 'Tăng', 'Chu', 'Mai', 'Tạ', 'Thái', 'Ông'
  ];

  final List<String> _middleNames = [
    'Văn', 'Thị', 'Minh', 'Thanh', 'Hữu', 'Đức', 'Quang', 'Anh', 'Tuấn', 'Hùng',
    'Công', 'Xuân', 'Thu', 'Hạ', 'Đông', 'Bảo', 'Kim', 'Ngọc', 'Phương', 'Lan'
  ];

  final List<String> _lastNames = [
    'An', 'Bình', 'Cường', 'Dũng', 'Em', 'Giang', 'Hoa', 'Linh', 'Mai', 'Nam',
    'Oanh', 'Phúc', 'Quý', 'Sơn', 'Tâm', 'Uyên', 'Vân', 'Xuân', 'Yến', 'Zung',
    'Long', 'Hương', 'Thảo', 'Đạt', 'Khoa', 'Tú', 'Hiền', 'Nhung', 'Trang', 'Hải'
  ];

  final List<String> _departments = [
    'Phòng PD',
    'Phòng IT', 
    'Phòng HR',
    'Phòng Marketing',
    'Phòng Sales',
    'Phòng Finance',
    'Phòng Operations',
    'Phòng QA'
  ];

  final Random _random = Random();

  /// Generate mock user
  UserModel _generateUser(int index) {
    final firstName = _firstNames[_random.nextInt(_firstNames.length)];
    final middleName = _middleNames[_random.nextInt(_middleNames.length)];
    final lastName = _lastNames[_random.nextInt(_lastNames.length)];
    final name = '$firstName $middleName $lastName';
    
    final department = _departments[_random.nextInt(_departments.length)];
    final status = _random.nextBool() ? UserStatus.active : UserStatus.inactive;
    final id = 'E${(index + 1).toString().padLeft(6, '0')}';
    
    // Generate email from name
    final emailName = '${firstName.toLowerCase()}.${lastName.toLowerCase()}';
    final email = '$<EMAIL>';
    
    // Generate phone
    final phone = '0${_random.nextInt(9) + 1}${_random.nextInt(100000000).toString().padLeft(8, '0')}';

    return UserModel(
      id: id,
      name: name,
      department: department,
      status: status,
      avatar: 'avatar_$index.png',
      email: email,
      phone: phone,
    );
  }

  /// Simulate API call to get users with pagination
  Future<List<UserModel>> getUsers({
    required int page,
    required int pageSize,
    String? searchQuery,
    String? departmentFilter,
    UserStatus? statusFilter,
  }) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));

    // Simulate occasional error (5% chance)
    if (_random.nextInt(100) < 5) {
      throw Exception('Network error occurred');
    }

    // Generate users for this page
    final startIndex = (page - 1) * pageSize;
    final users = <UserModel>[];

    for (int i = 0; i < pageSize; i++) {
      final userIndex = startIndex + i;
      
      // Simulate finite data (max 200 users)
      if (userIndex >= 200) break;
      
      final user = _generateUser(userIndex);
      
      // Apply filters
      bool shouldInclude = true;
      
      if (searchQuery != null && searchQuery.isNotEmpty) {
        shouldInclude = user.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                      user.email.toLowerCase().contains(searchQuery.toLowerCase()) ||
                      user.id.toLowerCase().contains(searchQuery.toLowerCase());
      }
      
      if (shouldInclude && departmentFilter != null && departmentFilter.isNotEmpty) {
        shouldInclude = user.department == departmentFilter;
      }
      
      if (shouldInclude && statusFilter != null) {
        shouldInclude = user.status == statusFilter;
      }
      
      if (shouldInclude) {
        users.add(user);
      }
    }

    return users;
  }

  /// Get user by ID
  Future<UserModel?> getUserById(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Extract index from ID
    final indexStr = id.substring(1); // Remove 'E' prefix
    final index = int.tryParse(indexStr);
    
    if (index == null || index <= 0 || index > 200) {
      return null;
    }
    
    return _generateUser(index - 1);
  }

  /// Create new user
  Future<UserModel> createUser(UserModel user) async {
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Simulate validation error (10% chance)
    if (_random.nextInt(100) < 10) {
      throw Exception('Validation error: Email already exists');
    }
    
    return user;
  }

  /// Update user
  Future<UserModel> updateUser(UserModel user) async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    // Simulate not found error (5% chance)
    if (_random.nextInt(100) < 5) {
      throw Exception('User not found');
    }
    
    return user;
  }

  /// Delete user
  Future<void> deleteUser(String id) async {
    await Future.delayed(const Duration(milliseconds: 400));
    
    // Simulate permission error (5% chance)
    if (_random.nextInt(100) < 5) {
      throw Exception('Permission denied');
    }
  }

  /// Get departments list
  List<String> getDepartments() {
    return List.from(_departments);
  }

  /// Get user statistics
  Future<Map<String, int>> getUserStats() async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return {
      'total': 200,
      'active': 170,
      'inactive': 30,
      'departments': _departments.length,
    };
  }
}

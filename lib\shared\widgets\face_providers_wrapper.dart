import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_capture_provider.dart';
import '../providers/face_detection_provider.dart';
import '../providers/face_providers_lifecycle_manager.dart';
import '../models/camera_config.dart';

/// Enhanced wrapper widget that provides face detection functionality
/// using FaceProvidersLifecycleManager for proper lifecycle management.
///
/// Features:
/// - Lazy initialization: providers only created when needed
/// - Automatic cleanup: providers disposed when widget unmounts
/// - Reference counting: multiple widgets can share same providers
/// - Error handling: graceful error recovery
class FaceProvidersWrapper extends StatefulWidget {
  final Widget child;
  final bool autoInitialize;
  final FaceDetectorMode performanceMode;
  final bool enableTracking;
  final bool enableClassification;
  final CameraConfig? cameraConfig;
  final VoidCallback? onReady;
  final VoidCallback? onDisposed;
  final Function(String)? onError;

  const FaceProvidersWrapper({
    super.key,
    required this.child,
    this.autoInitialize = true,
    this.performanceMode = FaceDetectorMode.fast,
    this.enableTracking = true,
    this.enableClassification = true,
    this.cameraConfig,
    this.onReady,
    this.onDisposed,
    this.onError,
  });

  @override
  State<FaceProvidersWrapper> createState() => _FaceProvidersWrapperState();
}

class _FaceProvidersWrapperState extends State<FaceProvidersWrapper> {
  final FaceProvidersLifecycleManager _lifecycleManager =
      FaceProvidersLifecycleManager.instance;

  String? _widgetId;
  bool _isRegistered = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _widgetId = 'face_wrapper_${DateTime.now().millisecondsSinceEpoch}';

    // Listen to lifecycle manager changes
    _lifecycleManager.addListener(_onLifecycleChanged);

    // Add callbacks if provided
    if (widget.onReady != null) {
      _lifecycleManager.addOnReadyCallback(widget.onReady!);
    }
    if (widget.onDisposed != null) {
      _lifecycleManager.addOnDisposedCallback(widget.onDisposed!);
    }
    if (widget.onError != null) {
      _lifecycleManager.addOnErrorCallback(widget.onError!);
    }

    // Register widget and auto-initialize if requested
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _registerWidget();
    });
  }

  @override
  void dispose() {
    _disposeAsync();
    super.dispose();
  }

  void _disposeAsync() async {
    try {
      // Cleanup callbacks
      if (widget.onReady != null) {
        _lifecycleManager.removeOnReadyCallback(widget.onReady!);
      }
      if (widget.onDisposed != null) {
        _lifecycleManager.removeOnDisposedCallback(widget.onDisposed!);
      }
      if (widget.onError != null) {
        _lifecycleManager.removeOnErrorCallback(widget.onError!);
      }

      // Remove listener
      _lifecycleManager.removeListener(_onLifecycleChanged);

      // Unregister widget with extended cleanup
      if (_isRegistered && _widgetId != null) {
        await _lifecycleManager.unregisterWidget(_widgetId!, autoDispose: true);
        // Wait for cleanup to complete
        await Future.delayed(const Duration(milliseconds: 300));
      }
    } catch (e) {
      debugPrint('Error during FaceProvidersWrapper dispose: $e');
    }
  }

  void _onLifecycleChanged() {
    if (mounted) {
      setState(() {
        _isInitialized = _lifecycleManager.isReady;
      });
    }
  }

  Future<void> _registerWidget() async {
    if (_widgetId == null || _isRegistered) return;

    try {
      await _lifecycleManager.registerWidget(
        _widgetId!,
        autoInitialize: widget.autoInitialize,
      );

      if (mounted) {
        setState(() {
          _isRegistered = true;
        });
      }

      // If auto-initialize is enabled, initialize with custom config
      if (widget.autoInitialize && !_lifecycleManager.isReady) {
        await _lifecycleManager.initializeProviders(
          performanceMode: widget.performanceMode,
          enableTracking: widget.enableTracking,
          enableClassification: widget.enableClassification,
          cameraConfig: widget.cameraConfig,
        );
      }
    } catch (e) {
      debugPrint('Error registering face providers widget: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    // If providers are not ready and auto-initialize is enabled, show loading
    if (widget.autoInitialize && !_isInitialized) {
      child = _buildLoadingWidget();
    }
    // If providers are ready, provide them to child widgets
    else if (_isInitialized &&
        _lifecycleManager.faceCaptureProvider != null &&
        _lifecycleManager.faceDetectionProvider != null) {
      child = MultiProvider(
        providers: [
          ChangeNotifierProvider<FaceCaptureProvider>.value(
            value: _lifecycleManager.faceCaptureProvider!,
          ),
          ChangeNotifierProvider<FaceDetectionProvider>.value(
            value: _lifecycleManager.faceDetectionProvider!,
          ),
        ],
        child: widget.child,
      );
    }
    // If auto-initialize is disabled, just return child
    else {
      child = widget.child;
    }

    // Return child directly - back button handling is done by individual screens
    return child;
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing Face Detection...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/foundation.dart';
import 'dart:async';

/// Error-safe handler for side effects to prevent crashes in main flow
class ErrorSafeHandler {
  static int _errorCount = 0;
  static DateTime _lastErrorTime = DateTime.now();
  static const int _maxErrorsPerMinute = 10;
  
  /// Execute a function safely without affecting main flow
  static Future<T?> executeSafely<T>(
    Future<T> Function() function, {
    String? operationName,
    T? fallbackValue,
    bool logErrors = true,
  }) async {
    try {
      return await function();
    } catch (e, stackTrace) {
      if (logErrors) {
        _logError(e, stackTrace, operationName);
      }
      return fallbackValue;
    }
  }

  /// Execute a synchronous function safely
  static T? executeSafelySync<T>(
    T Function() function, {
    String? operationName,
    T? fallbackValue,
    bool logErrors = true,
  }) {
    try {
      return function();
    } catch (e, stackTrace) {
      if (logErrors) {
        _logError(e, stackTrace, operationName);
      }
      return fallbackValue;
    }
  }

  /// Execute with timeout to prevent hanging
  static Future<T?> executeWithTimeout<T>(
    Future<T> Function() function, {
    Duration timeout = const Duration(seconds: 5),
    String? operationName,
    T? fallbackValue,
  }) async {
    try {
      return await function().timeout(timeout);
    } catch (e, stackTrace) {
      _logError(e, stackTrace, operationName);
      return fallbackValue;
    }
  }

  /// Execute side effect that should never block main flow
  static void executeSideEffect(
    void Function() sideEffect, {
    String? operationName,
  }) {
    // Run in next tick to avoid blocking current execution
    Future.microtask(() {
      executeSafelySync(
        sideEffect,
        operationName: operationName,
        logErrors: true,
      );
    });
  }

  /// Execute async side effect safely
  static void executeAsyncSideEffect(
    Future<void> Function() sideEffect, {
    String? operationName,
  }) {
    // Run without awaiting to avoid blocking
    executeSafely(
      sideEffect,
      operationName: operationName,
      logErrors: true,
    );
  }

  /// Log error with rate limiting
  static void _logError(dynamic error, StackTrace stackTrace, String? operationName) {
    final now = DateTime.now();
    
    // Reset error count if more than a minute has passed
    if (now.difference(_lastErrorTime).inMinutes >= 1) {
      _errorCount = 0;
    }
    
    _errorCount++;
    _lastErrorTime = now;
    
    // Rate limit error logging to prevent spam
    if (_errorCount <= _maxErrorsPerMinute) {
      if (kDebugMode) {
        debugPrint('🚨 ERROR-SAFE HANDLER: ${operationName ?? 'Unknown operation'}');
        debugPrint('   Error: $error');
        debugPrint('   Error count this minute: $_errorCount');
        
        // Only show stack trace for first few errors
        if (_errorCount <= 3) {
          debugPrint('   Stack trace: $stackTrace');
        }
      }
    } else if (_errorCount == _maxErrorsPerMinute + 1) {
      if (kDebugMode) {
        debugPrint('🚨 ERROR-SAFE HANDLER: Too many errors, suppressing further logs for this minute');
      }
    }
  }

  /// Get error statistics
  static Map<String, dynamic> getErrorStats() {
    return {
      'errorCount': _errorCount,
      'lastErrorTime': _lastErrorTime.toIso8601String(),
      'errorsThisMinute': _errorCount,
      'maxErrorsPerMinute': _maxErrorsPerMinute,
    };
  }

  /// Reset error count (for testing)
  static void resetErrorCount() {
    _errorCount = 0;
    _lastErrorTime = DateTime.now();
  }
}

/// Mixin for classes that need error-safe operations
mixin ErrorSafeMixin {
  /// Execute operation safely without affecting main flow
  Future<T?> safeExecute<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? fallbackValue,
  }) {
    return ErrorSafeHandler.executeSafely(
      operation,
      operationName: operationName ?? runtimeType.toString(),
      fallbackValue: fallbackValue,
    );
  }

  /// Execute synchronous operation safely
  T? safeExecuteSync<T>(
    T Function() operation, {
    String? operationName,
    T? fallbackValue,
  }) {
    return ErrorSafeHandler.executeSafelySync(
      operation,
      operationName: operationName ?? runtimeType.toString(),
      fallbackValue: fallbackValue,
    );
  }

  /// Execute side effect safely
  void safeSideEffect(
    void Function() sideEffect, {
    String? operationName,
  }) {
    ErrorSafeHandler.executeSideEffect(
      sideEffect,
      operationName: operationName ?? runtimeType.toString(),
    );
  }

  /// Execute async side effect safely
  void safeAsyncSideEffect(
    Future<void> Function() sideEffect, {
    String? operationName,
  }) {
    ErrorSafeHandler.executeAsyncSideEffect(
      sideEffect,
      operationName: operationName ?? runtimeType.toString(),
    );
  }
}

/// Extension for Future to add error safety
extension ErrorSafeFuture<T> on Future<T> {
  /// Make any Future error-safe
  Future<T?> errorSafe({
    String? operationName,
    T? fallbackValue,
  }) {
    return ErrorSafeHandler.executeSafely(
      () => this,
      operationName: operationName,
      fallbackValue: fallbackValue,
    );
  }

  /// Make Future error-safe with timeout
  Future<T?> errorSafeWithTimeout({
    Duration timeout = const Duration(seconds: 5),
    String? operationName,
    T? fallbackValue,
  }) {
    return ErrorSafeHandler.executeWithTimeout(
      () => this,
      timeout: timeout,
      operationName: operationName,
      fallbackValue: fallbackValue,
    );
  }
}

/// Terminal Routes Index - Export all route-related classes
///
/// This file exports all route configuration components for the terminal app
/// including route names, router configuration, navigation utilities optimized for kiosk mode.
///
/// Usage:
/// ```dart
/// import 'package:c_face_terminal/apps/terminal/routes/index.dart';
///
/// // Use in MaterialApp.router
/// MaterialApp.router(
///   routerConfig: TerminalRouter.createRouter(),
/// )
///
/// // Use navigation extensions
/// context.goToKioskHome();
/// context.goToStream();
/// context.goToAdminSettings();
///
/// // Check route information
/// if (context.isOnKioskPage) {
///   // Handle kiosk mode logic
/// }
/// ```
library;

// ============================================================================
// ROUTE EXPORTS
// ============================================================================

/// Route name constants for terminal app
export 'terminal_route_names.dart';

/// Router configuration for terminal app using GoRouter with kiosk mode support
export 'terminal_router.dart';

/// Navigation extension methods for BuildContext with kiosk mode features
export 'terminal_navigation_extensions.dart';

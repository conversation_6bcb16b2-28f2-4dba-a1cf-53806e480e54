package com.common.f8sdk;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.common.pos.api.util.PosUtil;
import com.common.pos.api.util.ShellUtils;

public class QrCodeActivity extends AppCompatActivity {

    TextView title_tv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_qrcode);

        initUI();
    }

    private void initUI() {
        title_tv = findViewById(R.id.title_tv);
        title_tv.setText("QrCode Enable Test");
    }

    @SuppressLint("NonConstantResourceId")
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.qrcode_open:
                PosUtil.setColorLed(100101,1, 255);
                ShellUtils.execCommand("echo 1 > /sys/class/gpio-ctrl/usb2_power/enable", false);
                Toast.makeText(QrCodeActivity.this, "turn on qrcode success", Toast.LENGTH_SHORT).show();
                break;
            case R.id.qrcode_close:
                PosUtil.setColorLed(100101,1, 0);
                ShellUtils.execCommand("echo 0 > /sys/class/gpio-ctrl/usb2_power/enable", false);
                Toast.makeText(QrCodeActivity.this, "turn off qrcode success", Toast.LENGTH_SHORT).show();
                break;
            default:
                break;
        }
    }

}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'terminal_route_names.dart';
import '../providers/kiosk_mode_provider.dart';

/// Extension methods for easier navigation in terminal app
/// 
/// Optimized for kiosk mode with auto-timeout and simplified navigation
extension TerminalNavigationExtension on BuildContext {
  // ============================================================================
  // KIOSK MODE NAVIGATION
  // ============================================================================
  
  /// Navigate to kiosk home screen
  void goToKioskHome() {
    _recordInteraction();
    go(TerminalRouteNames.kioskHome);
  }
  
  /// Navigate to stream screen
  void goToStream() {
    _recordInteraction();
    go(TerminalRouteNames.stream);
  }
  
  /// Navigate to face capture screen
  void goToFaceCapture() {
    _recordInteraction();
    go(TerminalRouteNames.faceCapture);
  }
  
  /// Navigate to face result screen
  void goToFaceResult() {
    _recordInteraction();
    go(TerminalRouteNames.faceResult);
  }
  
  /// Navigate to idle screen
  void goToKioskIdle() {
    _recordInteraction();
    go(TerminalRouteNames.kioskIdle);
  }
  
  // ============================================================================
  // ADMIN NAVIGATION
  // ============================================================================
  
  /// Navigate to admin login screen
  void goToAdminLogin() {
    _recordInteraction();
    go(TerminalRouteNames.adminLogin);
  }
  
  /// Navigate to admin settings screen
  void goToAdminSettings() {
    _recordInteraction();
    go(TerminalRouteNames.adminSettings);
  }
  
  /// Navigate to admin users screen
  void goToAdminUsers() {
    _recordInteraction();
    go(TerminalRouteNames.adminUsers);
  }
  
  /// Navigate to admin system screen
  void goToAdminSystem() {
    _recordInteraction();
    go(TerminalRouteNames.adminSystem);
  }
  
  // ============================================================================
  // BASIC NAVIGATION
  // ============================================================================
  
  /// Navigate to splash screen
  void goToSplash() => go(TerminalRouteNames.splash);
  
  /// Navigate to home (kiosk home)
  void goToHome() => goToKioskHome();
  
  // ============================================================================
  // ERROR NAVIGATION
  // ============================================================================
  
  /// Navigate to error screen with message
  void goToError(String message) {
    _recordInteraction();
    go(TerminalRouteNames.errorWithMessage(message));
  }
  
  /// Navigate to not found screen
  void goToNotFound() {
    _recordInteraction();
    go(TerminalRouteNames.notFound);
  }
  
  // ============================================================================
  // NAVIGATION WITH PARAMETERS
  // ============================================================================
  
  /// Navigate to face result with face ID
  void goToFaceResultWithId(String faceId) {
    _recordInteraction();
    go(TerminalRouteNames.faceResultWithId(faceId));
  }
  
  /// Navigate to admin settings with token
  void goToAdminSettingsWithToken(String token) {
    _recordInteraction();
    go(TerminalRouteNames.adminSettingsWithToken(token));
  }
  
  // ============================================================================
  // KIOSK-SPECIFIC NAVIGATION METHODS
  // ============================================================================
  
  /// Return to kiosk home (used by auto-timeout)
  void returnToKioskHome() {
    _recordInteraction();
    go(TerminalRouteNames.kioskHome);
  }
  
  /// Force return to home (bypass any navigation guards)
  void forceReturnToHome() {
    final kioskProvider = read<KioskModeProvider>();
    kioskProvider.forceReturnToHome();
    go(TerminalRouteNames.kioskHome);
  }
  
  /// Navigate with timeout awareness
  void navigateWithTimeout(String route, {Duration? customTimeout}) {
    _recordInteraction();
    go(route);
    
    // Set custom timeout if provided
    if (customTimeout != null) {
      final kioskProvider = read<KioskModeProvider>();
      final originalTimeout = kioskProvider.idleTimeout;
      kioskProvider.setIdleTimeout(customTimeout);
      
      // Reset to original timeout after navigation
      Future.delayed(const Duration(milliseconds: 100), () {
        kioskProvider.setIdleTimeout(originalTimeout);
      });
    }
  }
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Check if can go back
  bool get canGoBack => canPop();
  
  /// Go back or navigate to kiosk home if can't go back
  void goBackOrHome() {
    _recordInteraction();
    if (canPop()) {
      pop();
    } else {
      go(TerminalRouteNames.kioskHome);
    }
  }
  
  /// Go back with interaction recording
  void goBackWithInteraction() {
    _recordInteraction();
    if (canPop()) {
      pop();
    }
  }
  
  // ============================================================================
  // CURRENT ROUTE HELPERS
  // ============================================================================
  
  /// Get current route path
  String get currentRoute => GoRouterState.of(this).uri.path;
  
  /// Check if currently on kiosk home page
  bool get isOnKioskHomePage => currentRoute == TerminalRouteNames.kioskHome;
  
  /// Check if currently on stream page
  bool get isOnStreamPage => currentRoute == TerminalRouteNames.stream;
  
  /// Check if currently on kiosk page
  bool get isOnKioskPage => TerminalRouteNames.isKioskRoute(currentRoute);
  
  /// Check if currently on admin page
  bool get isOnAdminPage => TerminalRouteNames.isAdminRoute(currentRoute);
  
  /// Check if currently on face-related page
  bool get isOnFacePage => TerminalRouteNames.isFaceRoute(currentRoute);
  
  /// Check if current route allows auto-timeout
  bool get currentRouteAllowsTimeout => TerminalRouteNames.allowsAutoTimeout(currentRoute);
  
  // ============================================================================
  // ROUTE INFORMATION
  // ============================================================================
  
  /// Get current route display name
  String get currentRouteDisplayName => TerminalRouteNames.getDisplayName(currentRoute);
  
  /// Get current route description
  String get currentRouteDescription => TerminalRouteNames.getDescription(currentRoute);
  
  /// Get route parameters
  Map<String, String> get routeParameters => GoRouterState.of(this).pathParameters;
  
  /// Get query parameters
  Map<String, String> get queryParameters => GoRouterState.of(this).uri.queryParameters;
  
  /// Get specific route parameter
  String? getRouteParameter(String key) => routeParameters[key];
  
  /// Get specific query parameter
  String? getQueryParameter(String key) => queryParameters[key];
  
  /// Get timeout duration for current route
  int get currentRouteTimeoutDuration => TerminalRouteNames.getTimeoutDuration(currentRoute);
  
  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================
  
  /// Record user interaction for kiosk mode
  void _recordInteraction() {
    try {
      final kioskProvider = read<KioskModeProvider>();
      kioskProvider.recordInteraction();
    } catch (e) {
      // Ignore if provider is not available
    }
  }
}

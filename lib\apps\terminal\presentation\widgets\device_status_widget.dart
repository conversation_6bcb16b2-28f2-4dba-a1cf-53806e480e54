import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/device_registration_provider.dart';

class DeviceStatusWidget extends StatelessWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const DeviceStatusWidget({
    super.key,
    this.showDetails = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceRegistrationProvider>(
      builder: (context, provider, child) {
        return Card(
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusHeader(context, provider),
                  if (showDetails) ...[
                    const SizedBox(height: 16),
                    _buildStatusDetails(context, provider),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusHeader(BuildContext context, DeviceRegistrationProvider provider) {
    return Row(
      children: [
        _buildStatusIndicator(provider.status),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Device Status',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                provider.connectionStatus,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _getStatusColor(provider.status),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        if (provider.status == DeviceRegistrationStatus.registered)
          Icon(
            Icons.check_circle,
            color: Colors.green.shade600,
            size: 24,
          ),
        if (provider.status == DeviceRegistrationStatus.error)
          Icon(
            Icons.error,
            color: Colors.red.shade600,
            size: 24,
          ),
      ],
    );
  }

  Widget _buildStatusIndicator(DeviceRegistrationStatus status) {
    Color color;
    IconData icon;

    switch (status) {
      case DeviceRegistrationStatus.unregistered:
        color = Colors.grey;
        icon = Icons.radio_button_unchecked;
        break;
      case DeviceRegistrationStatus.registering:
        color = Colors.orange;
        icon = Icons.sync;
        break;
      case DeviceRegistrationStatus.registered:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case DeviceRegistrationStatus.error:
        color = Colors.red;
        icon = Icons.error;
        break;
      case DeviceRegistrationStatus.disconnected:
        color = Colors.amber;
        icon = Icons.cloud_off;
        break;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: color, width: 2),
      ),
      child: status == DeviceRegistrationStatus.registering
          ? Padding(
              padding: const EdgeInsets.all(12.0),
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            )
          : Icon(
              icon,
              color: color,
              size: 24,
            ),
    );
  }

  Widget _buildStatusDetails(BuildContext context, DeviceRegistrationProvider provider) {
    if (provider.status == DeviceRegistrationStatus.unregistered) {
      return _buildUnregisteredDetails(context);
    }

    if (provider.status == DeviceRegistrationStatus.registering) {
      return _buildRegisteringDetails(context, provider);
    }

    if (provider.status == DeviceRegistrationStatus.error) {
      return _buildErrorDetails(context, provider);
    }

    if (provider.status == DeviceRegistrationStatus.registered) {
      return _buildRegisteredDetails(context, provider);
    }

    return const SizedBox.shrink();
  }

  Widget _buildUnregisteredDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.blue.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Device is not registered with the server. Registration is required for secure communication.',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRegisteringDetails(BuildContext context, DeviceRegistrationProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        if (provider.registrationProgress.isNotEmpty) ...[
          Row(
            children: [
              Icon(
                Icons.sync,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  provider.registrationProgress,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        const LinearProgressIndicator(),
      ],
    );
  }

  Widget _buildErrorDetails(BuildContext context, DeviceRegistrationProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                provider.errorMessage ?? 'Unknown error occurred',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red.shade700,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRegisteredDetails(BuildContext context, DeviceRegistrationProvider provider) {
    final deviceInfo = provider.deviceInfo;
    if (deviceInfo == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        
        // Device Information
        _buildDetailRow(
          context,
          'Device ID',
          deviceInfo.deviceId,
          Icons.fingerprint,
        ),
        const SizedBox(height: 8),
        
        _buildDetailRow(
          context,
          'Device Name',
          deviceInfo.deviceName,
          Icons.label,
        ),
        const SizedBox(height: 8),
        
        _buildDetailRow(
          context,
          'Location',
          deviceInfo.location,
          Icons.location_on,
        ),
        const SizedBox(height: 8),
        
        if (deviceInfo.registrationTime != null)
          _buildDetailRow(
            context,
            'Registered',
            DateFormat('MMM dd, yyyy HH:mm').format(deviceInfo.registrationTime!),
            Icons.schedule,
          ),
        
        if (provider.lastHeartbeat != null) ...[
          const SizedBox(height: 8),
          _buildDetailRow(
            context,
            'Last Heartbeat',
            _formatLastHeartbeat(provider.lastHeartbeat!),
            Icons.favorite,
          ),
        ],
        
        // Capabilities
        if (provider.availableScopes.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Capabilities',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: provider.availableScopes.map((scope) {
              return Chip(
                label: Text(
                  scope,
                  style: const TextStyle(fontSize: 10),
                ),
                backgroundColor: Colors.blue.shade50,
                side: BorderSide(color: Colors.blue.shade200),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodySmall,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(DeviceRegistrationStatus status) {
    switch (status) {
      case DeviceRegistrationStatus.unregistered:
        return Colors.grey.shade600;
      case DeviceRegistrationStatus.registering:
        return Colors.orange.shade600;
      case DeviceRegistrationStatus.registered:
        return Colors.green.shade600;
      case DeviceRegistrationStatus.error:
        return Colors.red.shade600;
      case DeviceRegistrationStatus.disconnected:
        return Colors.amber.shade600;
    }
  }

  String _formatLastHeartbeat(DateTime lastHeartbeat) {
    final now = DateTime.now();
    final difference = now.difference(lastHeartbeat);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return DateFormat('MMM dd, HH:mm').format(lastHeartbeat);
    }
  }
}

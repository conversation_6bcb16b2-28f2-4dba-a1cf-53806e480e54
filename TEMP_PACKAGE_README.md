# Hướng dẫn sử dụng Package Name tạm thời

## Tổng quan
Để tránh việc build đè lên nhau khi dùng chung device, package name đã được thay đổi tạm thời:

- **Package gốc**: `com.ccam.mobile`
- **Package tạm thời**: `com.ccam.mobile.temp`
- **App name**: `C-CAM Mobile Temp`

## Các thay đổi đã thực hiện

### 1. Android Configuration
- File: `android/app/build.gradle.kts`
- Thay đổi `applicationId` từ `com.ccam.mobile` thành `com.ccam.mobile.temp`
- Thay đổi app name thành `C-CAM Mobile Temp`

### 2. Flutter Configuration
- File: `lib/apps/mobile/main_mobile.dart`
- File: `lib/apps/mobile/main.dart`
- File: `lib/apps/mobile/config/mobile_app_config.dart`
- Cập nhật `appId` và `appName` để đồng bộ

## Cách sử dụng

### 1. Chạy app với package tạm thời
```bash
# Sử dụng script tạm thời
./scripts/run_mobile_temp.sh

# Hoặc chạy trực tiếp
flutter run --target lib/apps/mobile/main_mobile.dart --debug --flavor mobile
```

### 2. Build APK với package tạm thời
```bash
# Sử dụng script build tạm thời
./scripts/build_mobile_temp.sh

# Hoặc build trực tiếp
flutter build apk --target lib/apps/mobile/main_mobile.dart --flavor mobile --debug
```

### 3. Khôi phục package gốc
```bash
# Khi hoàn thành, khôi phục lại package gốc
./scripts/restore_original_package.sh
```

## Lưu ý quan trọng

1. **Hai app có thể cài đặt cùng lúc**: App gốc (`com.ccam.mobile`) và app tạm thời (`com.ccam.mobile.temp`) có thể tồn tại cùng lúc trên device.

2. **Dữ liệu riêng biệt**: Mỗi app sẽ có dữ liệu riêng biệt (SharedPreferences, database, files).

3. **Khôi phục khi hoàn thành**: Nhớ chạy script `restore_original_package.sh` để khôi phục lại package gốc khi hoàn thành việc test.

4. **Commit cẩn thận**: Không commit các thay đổi package name tạm thời lên repository.

## Troubleshooting

### Nếu build lỗi:
```bash
flutter clean
flutter pub get
flutter doctor
```

### Nếu cần gỡ app cũ:
```bash
# Gỡ app tạm thời
adb uninstall com.ccam.mobile.temp.debug

# Gỡ app gốc
adb uninstall com.ccam.mobile.debug
```

### Kiểm tra app đã cài:
```bash
adb shell pm list packages | grep ccam
```

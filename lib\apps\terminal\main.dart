import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/config/flavor_config.dart';
import '../../core/config/app_config.dart';
import '../../core/constants/app_colors.dart';
import 'providers/device_registration_provider.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/stream_screen.dart';
import 'presentation/screens/terminal_screen.dart';

void main() {
  // Ensure Flutter binding is initialized first
  WidgetsFlutterBinding.ensureInitialized();

  // Khởi tạo flavor config cho terminal app
  FlavorConfig.initialize(
    flavor: Flavor.terminal,
    appName: 'C-CAM Terminal',
    appId: 'com.ccam.terminal',
    appVersion: '1.0.0',
    isDebug: true,
    values: AppConfig.terminalConfig,
  );

  // Cấu hình cho terminal mode
  _configureTerminalMode();

  runApp(const TerminalApp());
}

void _configureTerminalMode() {
  // Ẩn system UI cho terminal mode
  if (AppConfig.hideSystemUI) {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );
  }

  // Cho phép tất cả orientations cho terminal
  SystemChrome.setPreferredOrientations([
    // Cho phép app chạy ở chế độ portrait (dọc)
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    // Cho phép app chạy ở chế độ landscape (ngang)
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Giữ màn hình luôn sáng
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.transparent,
    ),
  );
}

class TerminalApp extends StatelessWidget {
  const TerminalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => DeviceRegistrationProvider(),
        ),
      ],
      child: MaterialApp(
        title: FlavorConfig.instance.appName,
        debugShowCheckedModeBanner: FlavorConfig.instance.isDebug,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.primary,
            primary: AppColors.primary,
            surface: AppColors.background,
          ),
          useMaterial3: true,
          fontFamily: 'System',
          bottomSheetTheme: const BottomSheetThemeData(
            dragHandleColor: Colors.white,
          ),
        ),
        initialRoute: '/',
        routes: _buildRoutes(),
        onGenerateRoute: _onGenerateRoute,
      ),
    );
  }

  Map<String, WidgetBuilder> _buildRoutes() {
    return {
      '/': (context) => const SplashScreen(),
      '/terminal': (context) => const TerminalScreen(),
      '/stream': (context) => const StreamScreen(),
    };
  }

  Route<dynamic>? _onGenerateRoute(RouteSettings settings) {
    // Handle any routes not defined in the routes map
    switch (settings.name) {
      case '/stream':
        return MaterialPageRoute(
          builder: (context) => const StreamScreen(),
          settings: settings,
        );
      default:
        // Return null to let the routes map handle it
        return null;
    }
  }
}

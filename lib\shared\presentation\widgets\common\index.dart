/// Common Widgets Index - Export all common widget components
///
/// ⚠️ DEPRECATED: Most components have been migrated to @lib/shared/components
/// Please use the new components from shared/components instead.
///
/// This file exports remaining common widget components that haven't been migrated yet.
/// For new components, use: import 'package:c_face_terminal/shared/components/[component].dart';
///
/// Migration Status:
/// - CTextField → AppInputField (migrated)
/// - CTabsBar → TabsBar (migrated)
/// - ErrorScreen → shared/components/error_screen.dart (moved)
/// - NotFoundScreen → shared/components/not_found_screen.dart (moved)
/// - EnhancedErrorMessage → AppNotification.auth() (migrated)
library;

// ============================================================================
// COMMON WIDGET EXPORTS
// ============================================================================

/// C-Faces logo widget
export 'c_faces_logo.dart';

/// Custom button widget with consistent styling
export 'c_button.dart';

/// Custom text field widget with validation
/// ⚠️ MIGRATED: Use AppInputField from shared/components instead
// export 'c_text_field.dart';

/// Custom date picker widget
export 'c_date_picker.dart';

/// Custom label widget for form fields
export 'c_label.dart';

/// Custom list scroll widget
export 'c_list_scroll.dart';

/// Custom tabs bar widget for navigation
/// ⚠️ MIGRATED: Use TabsBar from shared/components instead
// export 'c_tabs_bar.dart';

/// Error message widget for displaying errors
export 'error_message.dart';

/// Custom loading widget with various display options
export 'c_loading_widget.dart';

/// Custom empty state widget for no data scenarios
export 'c_empty_state.dart';

/// Custom confirmation dialog for user actions
export 'c_confirmation_dialog.dart';

/// Custom card widget with consistent styling
export 'c_card.dart';

/// Error screen for displaying error messages
/// ⚠️ MOVED: Now available in shared/components/error_screen.dart
// export 'error_screen.dart';

/// Not found screen for 404 errors
/// ⚠️ MOVED: Now available in shared/components/not_found_screen.dart
// export 'not_found_screen.dart';

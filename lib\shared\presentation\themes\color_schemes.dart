import 'package:flutter/material.dart';

/// App color schemes for light and dark themes
/// 
/// Provides consistent color palettes across mobile and terminal applications
/// Based on Material Design 3 color system
class AppColorSchemes {
  
  // ============================================================================
  // BRAND COLORS
  // ============================================================================
  
  static const Color primaryBlue = Color(0xFF008FD3);
  static const Color primaryBlueDark = Color(0xFF0066A3);
  static const Color primaryBlueLight = Color(0xFF33A3DC);
  
  static const Color secondaryTeal = Color(0xFF00BCD4);
  static const Color secondaryTealDark = Color(0xFF0097A7);
  static const Color secondaryTealLight = Color(0xFF4DD0E1);
  
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color accentOrangeLight = Color(0xFFFFB74D);
  static const Color accentOrangeDark = Color(0xFFF57C00);
  
  // ============================================================================
  // SEMANTIC COLORS
  // ============================================================================
  
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningAmber = Color(0xFFFFC107);
  static const Color errorRed = Color(0xFFF44336);
  static const Color infoBlue = Color(0xFF2196F3);
  
  // ============================================================================
  // NEUTRAL COLORS
  // ============================================================================
  
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFEEEEEE);
  static const Color neutral300 = Color(0xFFE0E0E0);
  static const Color neutral400 = Color(0xFFBDBDBD);
  static const Color neutral500 = Color(0xFF9E9E9E);
  static const Color neutral600 = Color(0xFF757575);
  static const Color neutral700 = Color(0xFF616161);
  static const Color neutral800 = Color(0xFF424242);
  static const Color neutral900 = Color(0xFF212121);
  
  // ============================================================================
  // LIGHT COLOR SCHEME
  // ============================================================================
  
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    
    // Primary colors
    primary: primaryBlue,
    onPrimary: Colors.white,
    primaryContainer: Color(0xFFE3F2FD),
    onPrimaryContainer: Color(0xFF001D36),
    
    // Secondary colors
    secondary: secondaryTeal,
    onSecondary: Colors.white,
    secondaryContainer: Color(0xFFE0F2F1),
    onSecondaryContainer: Color(0xFF002020),
    
    // Tertiary colors
    tertiary: accentOrange,
    onTertiary: Colors.white,
    tertiaryContainer: Color(0xFFFFE0B2),
    onTertiaryContainer: Color(0xFF2D1600),
    
    // Error colors
    error: errorRed,
    onError: Colors.white,
    errorContainer: Color(0xFFFFEBEE),
    onErrorContainer: Color(0xFF410E0B),
    
    // Background colors
    background: neutral50,
    onBackground: neutral900,
    
    // Surface colors
    surface: Colors.white,
    onSurface: neutral900,
    surfaceVariant: neutral100,
    onSurfaceVariant: neutral700,
    
    // Outline colors
    outline: neutral400,
    outlineVariant: neutral300,
    
    // Other colors
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: neutral800,
    onInverseSurface: neutral100,
    inversePrimary: Color(0xFF9CCFFF),
  );
  
  // ============================================================================
  // DARK COLOR SCHEME
  // ============================================================================
  
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    
    // Primary colors
    primary: Color(0xFF9CCFFF),
    onPrimary: Color(0xFF003258),
    primaryContainer: Color(0xFF004A77),
    onPrimaryContainer: Color(0xFFD1E4FF),
    
    // Secondary colors
    secondary: Color(0xFF4DD0E1),
    onSecondary: Color(0xFF003738),
    secondaryContainer: Color(0xFF004F50),
    onSecondaryContainer: Color(0xFF6FF6FF),
    
    // Tertiary colors
    tertiary: Color(0xFFFFB74D),
    onTertiary: Color(0xFF452B00),
    tertiaryContainer: Color(0xFF633F00),
    onTertiaryContainer: Color(0xFFFFDCC1),
    
    // Error colors
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    
    // Background colors
    background: Color(0xFF0F1419),
    onBackground: Color(0xFFE1E2E8),
    
    // Surface colors
    surface: Color(0xFF1A1C1E),
    onSurface: Color(0xFFE1E2E8),
    surfaceVariant: Color(0xFF42474E),
    onSurfaceVariant: Color(0xFFC2C7CE),
    
    // Outline colors
    outline: Color(0xFF8C9198),
    outlineVariant: Color(0xFF42474E),
    
    // Other colors
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: Color(0xFFE1E2E8),
    onInverseSurface: Color(0xFF2E3036),
    inversePrimary: primaryBlue,
  );
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Get color scheme based on brightness
  static ColorScheme getColorScheme(Brightness brightness) {
    return brightness == Brightness.light ? lightColorScheme : darkColorScheme;
  }
  
  /// Get semantic color for success state
  static Color getSuccessColor(Brightness brightness) {
    return brightness == Brightness.light ? successGreen : Color(0xFF81C784);
  }
  
  /// Get semantic color for warning state
  static Color getWarningColor(Brightness brightness) {
    return brightness == Brightness.light ? warningAmber : Color(0xFFFFD54F);
  }
  
  /// Get semantic color for info state
  static Color getInfoColor(Brightness brightness) {
    return brightness == Brightness.light ? infoBlue : Color(0xFF64B5F6);
  }
  
  /// Get neutral color by level (50-900)
  static Color getNeutralColor(int level) {
    switch (level) {
      case 50: return neutral50;
      case 100: return neutral100;
      case 200: return neutral200;
      case 300: return neutral300;
      case 400: return neutral400;
      case 500: return neutral500;
      case 600: return neutral600;
      case 700: return neutral700;
      case 800: return neutral800;
      case 900: return neutral900;
      default: return neutral500;
    }
  }
}

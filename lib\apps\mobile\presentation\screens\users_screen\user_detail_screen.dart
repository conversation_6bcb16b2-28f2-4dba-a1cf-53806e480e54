import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/components/app_button.dart';
import '../../../../../shared/icons/app_icons.dart';
import '../../../../../shared/widgets/full_screen_face_capture_modal.dart';
import '../../../../../shared/widgets/face_capture_gallery_modal.dart';
import '../../../../../shared/data/services/units_api_service.dart';
import '../../../../../shared/data/services/roles_api_service.dart';
import '../../../../../shared/data/services/users_api_service.dart';
import '../../../../../shared/services/face_api_service.dart';
import '../../../../../shared/data/models/role/member_role_model.dart';
import '../../../../../shared/models/face_capture_result.dart';
import '../../../../../shared/providers/face_detection_provider.dart';

/// Màn hình chi tiết user (tạo mới hoặc chỉnh sửa)
class UserDetailScreen extends StatefulWidget {
  final String? userId; // null = tạo mới, có giá trị = chỉnh sửa
  final String? initialName;
  final String? initialEmail;
  final String? initialPhone;
  final String? initialDepartment;
  final String? initialRole;

  const UserDetailScreen({
    super.key,
    this.userId,
    this.initialName,
    this.initialEmail,
    this.initialPhone,
    this.initialDepartment,
    this.initialRole,
  });

  @override
  State<UserDetailScreen> createState() => _UserDetailScreenState();
}

class _UserDetailScreenState extends State<UserDetailScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  // API Services
  late final UnitsApiService _unitsApiService;
  late final RolesApiService _rolesApiService;
  late final UsersApiService _usersApiService;

  // Form data
  String? _selectedGender;
  String? _selectedDepartment;
  String? _selectedDepartmentId;
  String? _selectedRole;
  String? _selectedRoleId;
  DateTime? _selectedBirthDate;

  // Loading states
  bool _isLoading = false;
  bool _isLoadingUnits = false;
  bool _isLoadingRoles = false;

  // Password visibility
  bool _isPasswordVisible = false;

  // Face capture data
  FaceCaptureResult? _faceCaptureResult;

  // API data
  List<UnitOption> _availableUnits = [];
  List<RoleOption> _availableRoles = [];

  // Face API Service
  late FaceApiService _faceApiService;

  // Form validation
  bool get _isFormValid {
    final basicValidation = _nameController.text.trim().isNotEmpty &&
           _emailController.text.trim().isNotEmpty &&
           _emailController.text.contains('@') &&
           _phoneController.text.trim().isNotEmpty &&
           _selectedDepartmentId != null &&
           _selectedRoleId != null;

    // For create mode, also require username and password
    if (!_isEditMode) {
      return basicValidation &&
             _usernameController.text.trim().isNotEmpty &&
             _passwordController.text.trim().isNotEmpty;
    }

    return basicValidation;
  }

  bool get _isEditMode => widget.userId != null;
  String get _screenTitle => _isEditMode ? 'Chỉnh sửa thành viên' : 'Thêm mới thành viên';

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeData();
    _setupListeners();
    _loadApiData();
  }

  void _initializeServices() {
    _unitsApiService = GetIt.instance<UnitsApiService>();
    _rolesApiService = GetIt.instance<RolesApiService>();
    _usersApiService = GetIt.instance<UsersApiService>();
    _faceApiService = FaceApiService();
  }

  Future<void> _loadApiData() async {
    // Load units and roles in parallel
    await Future.wait([
      _loadUnits(),
      _loadRoles(),
    ]);

    // After loading data, try to find matching IDs for edit mode
    if (_isEditMode) {
      _findMatchingIds();
    }
  }

  Future<void> _loadUnits() async {
    setState(() {
      _isLoadingUnits = true;
    });

    try {
      final units = await _unitsApiService.getUnitsForFilter();
      if (mounted) {
        setState(() {
          _availableUnits = units;
          _isLoadingUnits = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _availableUnits = _unitsApiService.getDefaultUnits();
          _isLoadingUnits = false;
        });
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Không thể tải danh sách đơn vị: $e')),
        );
      }
    }
  }

  Future<void> _loadRoles() async {
    setState(() {
      _isLoadingRoles = true;
    });

    try {
      final roles = await _rolesApiService.getRolesForFilter();
      if (mounted) {
        setState(() {
          _availableRoles = roles;
          _isLoadingRoles = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _availableRoles = _rolesApiService.getDefaultRoles();
          _isLoadingRoles = false;
        });
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Không thể tải danh sách vai trò: $e')),
        );
      }
    }
  }

  void _setupListeners() {
    _nameController.addListener(_updateFormState);
    _emailController.addListener(_updateFormState);
    _phoneController.addListener(_updateFormState);
    _usernameController.addListener(_updateFormState);
    _passwordController.addListener(_updateFormState);
  }

  void _generateUsernameFromEmail() {
    final email = _emailController.text.trim();
    if (email.isNotEmpty && email.contains('@')) {
      final username = email.split('@').first;
      _usernameController.text = username;
    }
  }

  void _updateFormState() {
    setState(() {
      // Trigger rebuild to update button state
    });
  }

  void _initializeData() {
    if (_isEditMode) {
      _nameController.text = widget.initialName ?? '';
      _emailController.text = widget.initialEmail ?? '';
      _phoneController.text = widget.initialPhone ?? '';
      _selectedDepartment = widget.initialDepartment;
      _selectedRole = widget.initialRole;

      // Load full user data from API
      _loadUserData();
    } else {
      // For create mode, generate default username from email
      _emailController.addListener(_generateUsernameFromEmail);
    }
  }

  Future<void> _loadUserData() async {
    if (widget.userId == null) return;

    try {
      // Load full user data from API
      final userUIModel = await _usersApiService.getUserById(widget.userId!);

      if (userUIModel != null && mounted) {
        final userModel = userUIModel.originalModel;

        // Populate form fields with loaded data
        setState(() {
          _nameController.text = userModel.name;
          _emailController.text = userModel.email ?? '';
          _phoneController.text = userModel.phone ?? '';
          _usernameController.text = userModel.username;
          _selectedBirthDate = userModel.dob;
          _selectedGender = userModel.gender;
          _selectedDepartmentId = userModel.currentUnitId;

          // Try to find matching unit name
          if (_selectedDepartmentId != null) {
            final matchingUnit = _availableUnits.firstWhere(
              (unit) => unit.id == _selectedDepartmentId,
              orElse: () => const UnitOption(id: '', name: ''),
            );
            if (matchingUnit.id.isNotEmpty) {
              _selectedDepartment = matchingUnit.name;
            }
          }
        });

        // Try to find matching role from memberRoleId if available
        // Note: This requires additional API call or data structure
        _findMatchingIds();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Không thể tải thông tin user: $e')),
        );

        // Fallback to finding matching IDs from initial data
        _findMatchingIds();
      }
    }
  }

  void _findMatchingIds() {
    // Try to find department ID from name
    if (_selectedDepartment != null) {
      final matchingUnit = _availableUnits.firstWhere(
        (unit) => unit.name == _selectedDepartment,
        orElse: () => const UnitOption(id: '', name: ''),
      );
      if (matchingUnit.id.isNotEmpty) {
        _selectedDepartmentId = matchingUnit.id;
      }
    }

    // Try to find role ID from name
    if (_selectedRole != null) {
      final matchingRole = _availableRoles.firstWhere(
        (role) => role.name == _selectedRole,
        orElse: () => const RoleOption(id: '', name: ''),
      );
      if (matchingRole.id.isNotEmpty) {
        _selectedRoleId = matchingRole.id;
      }
    }
  }

  @override
  void dispose() {
    _nameController.removeListener(_updateFormState);
    _emailController.removeListener(_updateFormState);
    _phoneController.removeListener(_updateFormState);
    _usernameController.removeListener(_updateFormState);
    _passwordController.removeListener(_updateFormState);

    // Remove email listener for username generation (only in create mode)
    if (!_isEditMode) {
      _emailController.removeListener(_generateUsernameFromEmail);
    }

    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    AppDimensions.paddingM,
                    AppDimensions.spacing2,
                    AppDimensions.paddingM,
                    AppDimensions.paddingM,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionHeader('Thông tin thành viên'),
                        SizedBox(height: AppDimensions.spacing12),
                        _buildPersonalInfoSection(),
                        SizedBox(height: AppDimensions.spacing16),
                        _buildOrganizationInfoSection(),
                        if (!_isEditMode) ...[
                          SizedBox(height: AppDimensions.spacing16),
                          _buildLoginInfoSection(),
                        ],
                        SizedBox(height: AppDimensions.spacing24),
                        _buildFaceDataSection(),
                        SizedBox(height: AppDimensions.spacing24),
                        _buildSubmitButton(),
                        SizedBox(height: AppDimensions.spacing24),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              child: Icon(
                Icons.chevron_left,
                size: 20,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacing12),
          Text(
            _screenTitle,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Row(
      children: [
        Container(
          width: 2,
          height: 16,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(1),
          ),
        ),
        SizedBox(width: AppDimensions.spacing8),
        Text(
          title,
          style: AppTextStyles.bodySmall.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin chung',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Column(
            children: [
              _buildFormField(
                label: 'Họ và tên',
                placeholder: 'Nhập họ và tên',
                controller: _nameController,
                isRequired: true,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return 'Vui lòng nhập họ và tên';
                  }
                  return null;
                },
              ),
              _buildDivider(),
              _buildFormField(
                label: 'Email',
                placeholder: 'Nhập thông tin email',
                controller: _emailController,
                isRequired: true,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return 'Vui lòng nhập email';
                  }
                  if (!value!.contains('@')) {
                    return 'Email không hợp lệ';
                  }
                  return null;
                },
              ),
              _buildDivider(),
              _buildFormField(
                label: 'Số điện thoại',
                placeholder: 'Nhập số điện thoại',
                controller: _phoneController,
                isRequired: true,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return 'Vui lòng nhập số điện thoại';
                  }
                  return null;
                },
              ),
              _buildDivider(),
              _buildDropdownField(
                label: 'Ngày sinh',
                placeholder: 'Chọn ngày sinh',
                value: _selectedBirthDate != null
                    ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                    : null,
                onTap: _selectBirthDate,
                icon: Icons.calendar_today,
              ),
              _buildDivider(),
              _buildDropdownField(
                label: 'Giới tính',
                placeholder: 'Chọn giới tính',
                value: _getGenderDisplayText(_selectedGender),
                onTap: _selectGender,
                icon: Icons.keyboard_arrow_down,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrganizationInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin tổ chức',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Column(
            children: [
              _buildDropdownField(
                label: 'Đơn vị',
                placeholder: 'Chọn đơn vị',
                value: _selectedDepartment,
                onTap: _selectDepartment,
                icon: _isLoadingUnits ? Icons.hourglass_empty : Icons.keyboard_arrow_down,
                isRequired: true,
                isLoading: _isLoadingUnits,
              ),
              _buildDivider(),
              _buildDropdownField(
                label: 'Vai trò',
                placeholder: 'Chọn vai trò',
                value: _selectedRole,
                onTap: _selectRole,
                icon: _isLoadingRoles ? Icons.hourglass_empty : Icons.keyboard_arrow_down,
                isRequired: true,
                isLoading: _isLoadingRoles,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoginInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin đăng nhập',
          style: AppTextStyles.caption.copyWith(
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: AppDimensions.spacing8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Column(
            children: [
              _buildFormField(
                label: 'Tên đăng nhập',
                placeholder: 'Nhập tên đăng nhập',
                controller: _usernameController,
                isRequired: true,
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return 'Vui lòng nhập tên đăng nhập';
                  }
                  if (value!.length < 3) {
                    return 'Tên đăng nhập phải có ít nhất 3 ký tự';
                  }
                  return null;
                },
              ),
              _buildDivider(),
              _buildPasswordField(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Container(
      height: 48, // Fixed height giống các form fields khác
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  'Mật khẩu',
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(width: 2),
                Text(
                  '*',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: TextFormField(
              controller: _passwordController,
              obscureText: !_isPasswordVisible,
              style: AppTextStyles.caption,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: 'Nhập mật khẩu',
                hintStyle: AppTextStyles.caption.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                isDense: true,
                suffixIcon: IconButton(
                  icon: Icon(
                    _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  onPressed: () {
                    setState(() {
                      _isPasswordVisible = !_isPasswordVisible;
                    });
                  },
                ),
              ),
              textAlign: TextAlign.right,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'Vui lòng nhập mật khẩu';
                }
                if (value!.length < 6) {
                  return 'Mật khẩu phải có ít nhất 6 ký tự';
                }
                return null;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFaceDataSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFaceDataSectionHeader(),
        SizedBox(height: AppDimensions.spacing16),
        GestureDetector(
          onTap: _handleFaceDataSectionTap,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: _faceCaptureResult?.success == true
                    ? Colors.green
                    : AppColors.primary,
                width: 1.5,
                strokeAlign: BorderSide.strokeAlignInside,
              ),
            ),
            child: _faceCaptureResult?.success == true
                ? _buildCapturedImagesContent()
                : _buildEmptyFaceDataContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildFaceDataSectionHeader() {
    return Row(
      children: [
        Expanded(
          child: _buildSectionHeader('Đăng ký dữ liệu khuôn mặt'),
        ),
        if (_faceCaptureResult?.success == true) ...[
          GestureDetector(
            onTap: _handleUpdateFaceData,
            child: Text(
              'Cập nhật',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.underline,
                decorationColor: AppColors.primary,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyFaceDataContent() {
    return SizedBox(
      height: 102,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppIcons.faceRegistration(
            size: 20,
            color: AppColors.primary,
          ),
          SizedBox(height: AppDimensions.spacing8),
          Text(
            'Đăng ký dữ liệu khuôn mặt',
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Nhấn để bắt đầu chụp ảnh khuôn mặt',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCapturedImagesContent() {
    final result = _faceCaptureResult!;
    final isComplete = result.isComplete;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isComplete ? Icons.check_circle : Icons.photo_camera,
                color: isComplete ? Colors.green : Colors.blue,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  isComplete
                      ? 'Hoàn thành! Đã chụp ${result.imageCount}/5 hình ảnh'
                      : 'Đã chụp ${result.imageCount}/5 hình ảnh',
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isComplete ? Colors.green : Colors.blue,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildImageGridRow(result),
        ],
      ),
    );
  }

  Widget _buildImageGridRow(FaceCaptureResult result) {
    // Tạo danh sách 5 slots cho tất cả hướng
    const allDirections = [
      FaceDirection.front,
      FaceDirection.left,
      FaceDirection.right,
      FaceDirection.top,
      FaceDirection.bottom,
    ];

    return Row(
      children: allDirections.map((direction) {
        final imagePath = result.getImagePath(direction);
        final hasImage = imagePath != null;

        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: _buildImageSlot(direction, imagePath, hasImage),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildImageSlot(FaceDirection direction, String? imagePath, bool hasImage) {
    return GestureDetector(
      onTap: hasImage ? () => _onImageTap(direction, imagePath!) : null,
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: hasImage ? Colors.green.shade300 : Colors.grey.shade300,
            width: hasImage ? 2 : 1,
          ),
          color: hasImage ? null : Colors.grey.shade50,
        ),
        child: Stack(
          children: [
            // Image or placeholder
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: hasImage
                  ? Image.file(
                      File(imagePath!),
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade200,
                          child: Icon(
                            Icons.error,
                            color: Colors.grey,
                            size: 20,
                          ),
                        );
                      },
                    )
                  : Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.grey.shade100,
                      child: Icon(
                        _getDirectionIcon(direction),
                        color: Colors.grey.shade400,
                        size: 20,
                      ),
                    ),
            ),

            // Quality badge (mock - always show tick for now)
            if (hasImage) ...[
              Positioned(
                top: 4,
                left: 4,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _onImageTap(FaceDirection direction, String imagePath) {
    // Hiển thị gallery với ảnh được chọn
    _showFaceCaptureGallery();
  }

  IconData _getDirectionIcon(FaceDirection direction) {
    switch (direction) {
      case FaceDirection.front:
        return Icons.face;
      case FaceDirection.top:
        return Icons.keyboard_arrow_up;
      case FaceDirection.bottom:
        return Icons.keyboard_arrow_down;
      case FaceDirection.left:
        return Icons.keyboard_arrow_left;
      case FaceDirection.right:
        return Icons.keyboard_arrow_right;
      case FaceDirection.unknown:
        return Icons.help_outline;
    }
  }

  Widget _buildSubmitButton() {
    return AppButton(
      text: _isEditMode ? 'Cập nhật' : 'Thêm mới',
      onPressed: (_isLoading || !_isFormValid) ? null : _handleSubmit,
      isLoading: _isLoading,
    );
  }

  Widget _buildFormField({
    required String label,
    required String placeholder,
    required TextEditingController controller,
    bool isRequired = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      height: 48, // Fixed height cho tất cả form fields
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  label,
                  style: AppTextStyles.caption.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                if (isRequired) ...[
                  const SizedBox(width: 2),
                  Text(
                    '*',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.error,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Expanded(
            child: TextFormField(
              controller: controller,
              keyboardType: keyboardType,
              validator: validator,
              style: AppTextStyles.caption,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: placeholder,
                hintStyle: AppTextStyles.caption.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                isDense: true,
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String placeholder,
    String? value,
    required VoidCallback onTap,
    required IconData icon,
    bool isRequired = false,
    bool isLoading = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 48, // Fixed height giống form fields
        padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Text(
                    label,
                    style: AppTextStyles.caption.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (isRequired) ...[
                    const SizedBox(width: 2),
                    Text(
                      '*',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Row(
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacing8),
                  Text(
                    'Đang tải...',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ] else ...[
                  Text(
                    value ?? placeholder,
                    style: AppTextStyles.caption.copyWith(
                      color: value != null ? AppColors.textPrimary : AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacing8),
                  Icon(
                    icon,
                    size: 14,
                    color: AppColors.textSecondary,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      height: 0.5,
      color: AppColors.border,
    );
  }

  // Helper methods
  String? _getGenderDisplayText(String? genderValue) {
    switch (genderValue) {
      case 'male':
        return 'Nam';
      case 'female':
        return 'Nữ';
      case 'other':
        return 'Khác';
      default:
        return null;
    }
  }

  // Action methods
  void _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedBirthDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  void _selectGender() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusM),
            topRight: Radius.circular(AppDimensions.radiusM),
          ),
        ),
        child: SafeArea(
          child: Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text('Nam'),
                  onTap: () {
                    setState(() {
                      _selectedGender = 'male'; // Store API value
                    });
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: const Text('Nữ'),
                  onTap: () {
                    setState(() {
                      _selectedGender = 'female'; // Store API value
                    });
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: const Text('Khác'),
                  onTap: () {
                    setState(() {
                      _selectedGender = 'other'; // Store API value
                    });
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectDepartment() {
    if (_isLoadingUnits) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Đang tải danh sách đơn vị...')),
      );
      return;
    }

    if (_availableUnits.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không có đơn vị nào khả dụng')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusM),
            topRight: Radius.circular(AppDimensions.radiusM),
          ),
        ),
        child: SafeArea(
          child: Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingS),
                  child: Text(
                    'Chọn đơn vị',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Divider(),
                ..._availableUnits.map((unit) => ListTile(
                  title: Text(unit.name),
                  onTap: () {
                    setState(() {
                      _selectedDepartment = unit.name;
                      _selectedDepartmentId = unit.id;
                    });
                    Navigator.pop(context);
                    _updateFormState(); // Trigger validation
                  },
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectRole() {
    if (_isLoadingRoles) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Đang tải danh sách vai trò...')),
      );
      return;
    }

    if (_availableRoles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không có vai trò nào khả dụng')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusM),
            topRight: Radius.circular(AppDimensions.radiusM),
          ),
        ),
        child: SafeArea(
          child: Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
              vertical: AppDimensions.paddingS),
                  child: Text(
                    'Chọn vai trò',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Divider(),
                ..._availableRoles.map((role) => ListTile(
                  title: Text(role.name),
                  subtitle: role.description != null ? Text(role.description!) : null,
                  onTap: () {
                    setState(() {
                      _selectedRole = role.name;
                      _selectedRoleId = role.id;
                    });
                    Navigator.pop(context);
                    _updateFormState(); // Trigger validation
                  },
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleFaceDataSectionTap() {
    if (_faceCaptureResult?.success == true) {
      // Nếu đã có dữ liệu, hiển thị gallery để xem ảnh
      _showFaceCaptureGallery();
    } else {
      // Nếu chưa có dữ liệu, bắt đầu capture
      _registerFaceData();
    }
  }

  void _handleUpdateFaceData() {
    // Xóa dữ liệu hiện tại và bắt đầu capture lại
    setState(() {
      _faceCaptureResult = null;
    });

    // Bắt đầu capture lại
    _registerFaceData();
  }

  void _showFaceCaptureGallery() {
    if (_faceCaptureResult == null) return;

    FaceCaptureGalleryModal.show(
      context: context,
      result: _faceCaptureResult!,
      onRetakeAll: _retakeAllFaceCapture,
    );
  }

  void _retakeAllFaceCapture() {
    // Xóa dữ liệu hiện tại và bắt đầu capture lại
    setState(() {
      _faceCaptureResult = null;
    });

    // Bắt đầu capture lại
    _registerFaceData();
  }

  void _registerFaceData() async {
    // Use full-screen modal instead of navigation
    final result = await FullScreenFaceCaptureModal.show(context);

    if (result is FaceCaptureResult && result.success && mounted) {
      setState(() {
        _faceCaptureResult = result;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đăng ký dữ liệu khuôn mặt thành công! Đã chụp ${result.imageCount} hình ảnh.'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      // Handle failure or cancellation
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đăng ký dữ liệu khuôn mặt đã bị hủy.'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }



  void _handleSubmit() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        String? userId;

        if (_isEditMode) {
          await _updateUser();
          userId = widget.userId; // Use existing user ID
        } else {
          userId = await _createUser(); // Get new user ID
        }

        // Nếu có face capture data và user ID, thực hiện face registration
        if (_faceCaptureResult?.success == true && userId != null) {
          await _registerUserFace(userId);
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          final successMessage = _isEditMode
              ? 'Cập nhật thành viên thành công!'
              : 'Thêm mới thành viên thành công!';

          final faceMessage = _faceCaptureResult?.success == true
              ? ' Dữ liệu khuôn mặt đã được đăng ký.'
              : '';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage + faceMessage),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditMode ? 'Lỗi cập nhật thành viên: $e' : 'Lỗi tạo thành viên: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  Future<String?> _createUser() async {
    // Validate required fields
    if (_selectedDepartmentId == null || _selectedRoleId == null) {
      throw Exception('Vui lòng chọn đơn vị và vai trò');
    }

    // Prepare request body according to API sample
    final requestBody = {
      'name': _nameController.text.trim(),
      'email': _emailController.text.trim(),
      'phone': _phoneController.text.trim(),
      'username': _usernameController.text.trim(),
      'password': _passwordController.text.trim(),
      'current_unit_id': _selectedDepartmentId!,
      'roleId': _selectedRoleId!,
      // Optional fields
      if (_selectedBirthDate != null)
        'dob': _selectedBirthDate!.toIso8601String().split('T')[0], // YYYY-MM-DD format
      if (_selectedGender != null)
        'gender': _selectedGender!, // Already in API format (male/female/other)
    };

    // Call API directly with request body
    final createdUser = await _usersApiService.createUserWithBody(requestBody);
    return createdUser.id;
  }

  Future<void> _updateUser() async {
    if (widget.userId == null) {
      throw Exception('User ID is required for update');
    }

    // Validate required fields
    if (_selectedDepartmentId == null || _selectedRoleId == null) {
      throw Exception('Vui lòng chọn đơn vị và vai trò');
    }

    // Prepare request body according to API sample
    final requestBody = {
      'name': _nameController.text.trim(),
      'email': _emailController.text.trim(),
      'phone': _phoneController.text.trim(),
      'current_unit_id': _selectedDepartmentId!,
      'roleId': _selectedRoleId!,
      // Optional fields
      if (_selectedBirthDate != null)
        'dob': _selectedBirthDate!.toIso8601String().split('T')[0], // YYYY-MM-DD format
      if (_selectedGender != null)
        'gender': _selectedGender!, // Already in API format (male/female/other)
    };

    // Call API directly with request body
    await _usersApiService.updateUserWithBody(widget.userId!, requestBody);
  }

  /// Đăng ký khuôn mặt cho user sau khi create/update thành công
  Future<void> _registerUserFace(String userId) async {
    if (_faceCaptureResult == null || !_faceCaptureResult!.success) {
      return;
    }

    try {
      // Validate face capture result trước khi gửi
      if (!_faceApiService.validateFaceCaptureResult(_faceCaptureResult!)) {
        throw Exception('Dữ liệu khuôn mặt không hợp lệ');
      }

      // Gọi API đăng ký khuôn mặt
      final response = await _faceApiService.registerUserFace(
        userId: userId,
        faceCaptureResult: _faceCaptureResult!,
      );

      // Log thành công (có thể thêm xử lý response nếu cần)
      debugPrint('✅ Face registration successful for user $userId: $response');

    } catch (e) {
      // Log lỗi nhưng không throw để không ảnh hưởng đến luồng chính
      debugPrint('❌ Face registration failed for user $userId: $e');

      // Hiển thị warning cho user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Cảnh báo: Không thể đăng ký dữ liệu khuôn mặt. Lỗi: $e'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}

/// Custom painter for dashed border
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;
  final double borderRadius;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(borderRadius),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashLength;
        final extractPath = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(extractPath, paint);
        distance = nextDistance + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
import 'dart:async';

import 'package:flutter/foundation.dart';

import 'network_detection_service.dart';

/// Manages switching between online and offline face recognition modes
class RecognitionModeManager extends ChangeNotifier {
  final NetworkDetectionService _networkService;
  
  RecognitionMode _currentMode = RecognitionMode.online;
  RecognitionMode _preferredMode = RecognitionMode.online;
  bool _isInitialized = false;
  Timer? _modeStabilityTimer;
  
  /// Current recognition mode
  RecognitionMode get currentMode => _currentMode;
  
  /// Preferred mode (user setting)
  RecognitionMode get preferredMode => _preferredMode;
  
  /// Whether the manager is initialized
  bool get isInitialized => _isInitialized;
  
  /// Whether currently in online mode
  bool get isOnlineMode => _currentMode == RecognitionMode.online;
  
  /// Whether currently in offline mode
  bool get isOfflineMode => _currentMode == RecognitionMode.offline;
  
  /// Whether currently in hybrid mode
  bool get isHybridMode => _currentMode == RecognitionMode.hybrid;
  
  RecognitionModeManager(this._networkService);
  
  /// Initialize the recognition mode manager
  Future<void> initialize({RecognitionMode preferredMode = RecognitionMode.hybrid}) async {
    if (_isInitialized) return;
    
    _preferredMode = preferredMode;
    
    // Initialize network service if not already done
    if (!_networkService.isInitialized) {
      await _networkService.initialize();
    }
    
    // Set initial mode based on network status and preference
    _updateModeBasedOnNetwork();
    
    // Listen to network changes
    _networkService.addListener(_onNetworkChanged);
    
    _isInitialized = true;
    
    if (kDebugMode) {
      print('🎯 RecognitionModeManager initialized');
      print('   Preferred mode: ${_preferredMode.name}');
      print('   Current mode: ${_currentMode.name}');
      print('   Network status: ${_networkService.statusString}');
    }
  }
  
  /// Handle network status changes
  void _onNetworkChanged() {
    if (kDebugMode) {
      print('🔄 Network status changed: ${_networkService.statusString}');
    }
    
    // Cancel any pending mode change
    _modeStabilityTimer?.cancel();
    
    // Wait for network stability before switching modes
    _modeStabilityTimer = Timer(const Duration(seconds: 2), () {
      _updateModeBasedOnNetwork();
    });
  }
  
  /// Update recognition mode based on network status and preferences
  void _updateModeBasedOnNetwork() {
    final wasOnline = _currentMode == RecognitionMode.online;
    RecognitionMode newMode;
    
    switch (_preferredMode) {
      case RecognitionMode.online:
        // Online only - fallback to offline if no network
        newMode = _networkService.isOnline 
            ? RecognitionMode.online 
            : RecognitionMode.offline;
        break;
        
      case RecognitionMode.offline:
        // Always offline
        newMode = RecognitionMode.offline;
        break;
        
      case RecognitionMode.hybrid:
        // Hybrid - prefer online when available
        newMode = _networkService.isOnline 
            ? RecognitionMode.online 
            : RecognitionMode.offline;
        break;
    }
    
    if (newMode != _currentMode) {
      final previousMode = _currentMode;
      _currentMode = newMode;
      
      if (kDebugMode) {
        print('🔄 Recognition mode switched: ${previousMode.name} → ${_currentMode.name}');
        print('   Reason: ${_getModeChangeReason(previousMode, newMode)}');
      }
      
      notifyListeners();
    }
  }
  
  /// Get human-readable reason for mode change
  String _getModeChangeReason(RecognitionMode from, RecognitionMode to) {
    if (from == RecognitionMode.offline && to == RecognitionMode.online) {
      return 'Network connection restored';
    } else if (from == RecognitionMode.online && to == RecognitionMode.offline) {
      return 'Network connection lost';
    } else {
      return 'Mode preference changed';
    }
  }
  
  /// Manually set preferred mode
  void setPreferredMode(RecognitionMode mode) {
    if (_preferredMode != mode) {
      _preferredMode = mode;
      
      if (kDebugMode) {
        print('⚙️ Preferred recognition mode changed to: ${mode.name}');
      }
      
      _updateModeBasedOnNetwork();
    }
  }
  
  /// Force a specific mode (temporary override)
  void forceMode(RecognitionMode mode, {Duration? duration}) {
    final previousMode = _currentMode;
    _currentMode = mode;
    
    if (kDebugMode) {
      print('🔧 Recognition mode forced to: ${mode.name}');
      if (duration != null) {
        print('   Duration: ${duration.inSeconds}s');
      }
    }
    
    notifyListeners();
    
    // Revert to automatic mode after duration
    if (duration != null) {
      Timer(duration, () {
        if (kDebugMode) {
          print('⏰ Forced mode expired, reverting to automatic');
        }
        _updateModeBasedOnNetwork();
      });
    }
  }
  
  /// Get mode statistics
  RecognitionModeStats getStats() {
    return RecognitionModeStats(
      currentMode: _currentMode,
      preferredMode: _preferredMode,
      networkStatus: _networkService.isOnline,
      isStable: _modeStabilityTimer == null,
    );
  }
  
  @override
  void dispose() {
    _modeStabilityTimer?.cancel();
    _networkService.removeListener(_onNetworkChanged);
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ RecognitionModeManager disposed');
    }
    
    super.dispose();
  }
}

/// Recognition mode enumeration
enum RecognitionMode {
  /// Online recognition only (server-based)
  online,
  
  /// Offline recognition only (local)
  offline,
  
  /// Hybrid mode (online preferred, offline fallback)
  hybrid,
}

/// Extension for recognition mode utilities
extension RecognitionModeExtension on RecognitionMode {
  /// Get display name
  String get displayName {
    switch (this) {
      case RecognitionMode.online:
        return 'Online';
      case RecognitionMode.offline:
        return 'Offline';
      case RecognitionMode.hybrid:
        return 'Hybrid';
    }
  }
  
  /// Get emoji representation
  String get emoji {
    switch (this) {
      case RecognitionMode.online:
        return '🌐';
      case RecognitionMode.offline:
        return '📱';
      case RecognitionMode.hybrid:
        return '🔄';
    }
  }
  
  /// Get description
  String get description {
    switch (this) {
      case RecognitionMode.online:
        return 'Server-based recognition with high accuracy';
      case RecognitionMode.offline:
        return 'Local recognition for privacy and speed';
      case RecognitionMode.hybrid:
        return 'Automatic switching based on network availability';
    }
  }
}

/// Recognition mode statistics
class RecognitionModeStats {
  final RecognitionMode currentMode;
  final RecognitionMode preferredMode;
  final bool networkStatus;
  final bool isStable;
  
  const RecognitionModeStats({
    required this.currentMode,
    required this.preferredMode,
    required this.networkStatus,
    required this.isStable,
  });
  
  @override
  String toString() {
    return 'RecognitionModeStats(current: ${currentMode.name}, preferred: ${preferredMode.name}, network: $networkStatus, stable: $isStable)';
  }
}

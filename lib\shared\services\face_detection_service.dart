import 'dart:async';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// Data class for passing face detection parameters
class FaceDetectionParams {
  final Uint8List imageBytes;
  final int width;
  final int height;
  final InputImageFormat format;
  final InputImageRotation rotation;
  final int bytesPerRow;

  FaceDetectionParams({
    required this.imageBytes,
    required this.width,
    required this.height,
    required this.format,
    required this.rotation,
    required this.bytesPerRow,
  });
}

/// Result class for face detection
class FaceDetectionResult {
  final List<Face> faces;
  final String? error;

  FaceDetectionResult({
    required this.faces,
    this.error,
  });
}

/// Service for handling face detection using compute function
class FaceDetectionService {
  static FaceDetector? _faceDetector;
  static bool _isInitialized = false;

  /// Initialize the face detection service
  static Future<void> initialize() async {
    if (kDebugMode) {
      print('🔧 Attempting to initialize face detection service...');
      print('🔧 Current state: _isInitialized=$_isInitialized');
    }

    if (_isInitialized) {
      if (kDebugMode) {
        print('🔧 Service already initialized, skipping...');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🔧 Creating FaceDetector...');
      }

      // Initialize face detector
      final options = FaceDetectorOptions(
        performanceMode: FaceDetectorMode.fast,
        enableTracking: true,
        enableLandmarks: false,
        enableContours: false,
        enableClassification: true,
        minFaceSize: 0.1,
      );

      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Face detection service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize face detection service: $e');
      }
    }
  }

  /// Process image for face detection
  static Future<FaceDetectionResult> processImage(FaceDetectionParams params) async {
    if (!_isInitialized || _faceDetector == null) {
      return FaceDetectionResult(
        faces: [],
        error: 'Face detection service not initialized',
      );
    }

    try {
      // Create InputImage from parameters
      final inputImage = InputImage.fromBytes(
        bytes: params.imageBytes,
        metadata: InputImageMetadata(
          size: Size(params.width.toDouble(), params.height.toDouble()),
          rotation: params.rotation,
          format: params.format,
          bytesPerRow: params.bytesPerRow,
        ),
      );

      // Process image using existing face detector
      final faces = await _faceDetector!.processImage(inputImage);

      return FaceDetectionResult(faces: faces);
    } catch (e) {
      return FaceDetectionResult(
        faces: [],
        error: e.toString(),
      );
    }
  }

  /// Dispose the service
  static Future<void> dispose() async {
    _faceDetector?.close();
    _faceDetector = null;
    _isInitialized = false;

    if (kDebugMode) {
      print('🧵 Face detection service disposed');
    }
  }
}

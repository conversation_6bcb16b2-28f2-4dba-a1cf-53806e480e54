<div
  class="flex flex-col justify-start items-center w-[343px] gap-1 py-6 rounded-xl bg-white border border-[#e5e6e7]"
>
  <div
    class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2 px-4 py-3"
  >
    <div class="flex flex-col justify-center items-center flex-grow gap-1 px-4">
      <div
        class="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[9px]"
      >
        <svg
          width="82"
          height="68"
          viewBox="0 0 82 68"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="flex-grow-0 flex-shrink-0 w-[81px] h-[67px] relative"
          preserveAspectRatio="none"
        >
          <path
            d="M40.5693 67.3324C57.0597 67.3324 70.4277 64.8071 70.4277 61.6921C70.4277 58.577 57.0597 56.0518 40.5693 56.0518C24.079 56.0518 10.7109 58.577 10.7109 61.6921C10.7109 64.8071 24.079 67.3324 40.5693 67.3324Z"
            fill="#F5F5F7"
            fill-opacity="0.8"
          ></path>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M64.4555 45.1223L53.9187 32.0121C53.4132 31.395 52.6741 31.0215 51.8959 31.0215H29.2413C28.4636 31.0215 27.7245 31.395 27.219 32.0121L16.6826 45.1223V51.9714H64.456V45.1223H64.4555Z"
            fill="#AEB8C2"
          ></path>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M25.6103 14.1006H55.5286C55.9958 14.1006 56.4439 14.2882 56.7742 14.6222C57.1046 14.9562 57.2902 15.4092 57.2902 15.8816V57.4422C57.2902 57.9146 57.1046 58.3675 56.7742 58.7015C56.4439 59.0355 55.9958 59.2232 55.5286 59.2232H25.6103C25.1431 59.2232 24.695 59.0355 24.3646 58.7015C24.0342 58.3675 23.8486 57.9146 23.8486 57.4422V15.8816C23.8486 15.4092 24.0342 14.9562 24.3646 14.6222C24.695 14.2882 25.1431 14.1006 25.6103 14.1006V14.1006Z"
            fill="#F5F5F7"
          ></path>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M29.5069 18.5322H51.6317C51.8653 18.5322 52.0894 18.626 52.2545 18.793C52.4197 18.96 52.5125 19.1865 52.5125 19.4227V30.5346C52.5125 30.7708 52.4197 30.9973 52.2545 31.1643C52.0894 31.3313 51.8653 31.4251 51.6317 31.4251H29.5069C29.2733 31.4251 29.0492 31.3313 28.8841 31.1643C28.7189 30.9973 28.6261 30.7708 28.6261 30.5346V19.4227C28.6261 19.1865 28.7189 18.96 28.8841 18.793C29.0492 18.626 29.2733 18.5322 29.5069 18.5322V18.5322ZM29.6223 36.2591H51.5163C51.7805 36.2591 52.0339 36.3652 52.2207 36.5541C52.4076 36.7429 52.5125 36.9991 52.5125 37.2662C52.5125 37.5333 52.4076 37.7895 52.2207 37.9784C52.0339 38.1673 51.7805 38.2734 51.5163 38.2734H29.6223C29.3581 38.2734 29.1047 38.1673 28.9178 37.9784C28.731 37.7895 28.6261 37.5333 28.6261 37.2662C28.6261 36.9991 28.731 36.7429 28.9178 36.5541C29.1047 36.3652 29.3581 36.2591 29.6223 36.2591V36.2591ZM29.6223 41.4965H51.5163C51.7806 41.4965 52.034 41.6026 52.2209 41.7915C52.4078 41.9804 52.5127 42.2367 52.5127 42.5038C52.5127 42.771 52.4078 43.0272 52.2209 43.2161C52.034 43.4051 51.7806 43.5112 51.5163 43.5112H29.6223C29.358 43.5112 29.1046 43.4051 28.9177 43.2161C28.7308 43.0272 28.6258 42.771 28.6258 42.5038C28.6258 42.2367 28.7308 41.9804 28.9177 41.7915C29.1046 41.6026 29.358 41.4965 29.6223 41.4965V41.4965ZM64.3586 60.8654C64.0173 62.2327 62.8185 63.2519 61.3925 63.2519H19.7461C18.3201 63.2519 17.1213 62.2323 16.7804 60.8654C16.7154 60.6048 16.6825 60.3371 16.6826 60.0684V45.123H28.2733C29.5536 45.123 30.5854 46.2129 30.5854 47.5362V47.554C30.5854 48.8768 31.6292 49.9449 32.9095 49.9449H48.2291C49.5094 49.9449 50.5531 48.867 50.5531 47.5437V47.5384C50.5531 46.2151 51.585 45.1225 52.8653 45.1225H64.456V60.0688C64.456 60.3436 64.4221 60.6103 64.3586 60.8654V60.8654Z"
            fill="#DCE0E6"
          ></path>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M65.8151 14.823L62.8071 16.0029C62.7298 16.0333 62.6456 16.041 62.5642 16.0251C62.4828 16.0093 62.4075 15.9705 62.3469 15.9133C62.2863 15.8561 62.2429 15.7828 62.2217 15.7017C62.2005 15.6207 62.2024 15.5352 62.2271 15.4552L63.0801 12.6916C61.9399 11.3808 61.2705 9.78241 61.2705 8.05754C61.2705 3.60735 65.7266 0 71.2237 0C76.7196 0 81.1761 3.60735 81.1761 8.05754C81.1761 12.5077 76.72 16.1151 71.2233 16.1151C69.2291 16.1151 67.3724 15.6405 65.8151 14.823Z"
            fill="#DCE0E6"
          ></path>
          <path
            d="M75.1444 9.51257C75.8373 9.51257 76.3991 8.95142 76.3991 8.25921C76.3991 7.56701 75.8373 7.00586 75.1444 7.00586C74.4514 7.00586 73.8896 7.56701 73.8896 8.25921C73.8896 8.95142 74.4514 9.51257 75.1444 9.51257Z"
            fill="white"
          ></path>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M68.5573 9.35537H66.0479L67.3242 7.16211L68.5573 9.35537ZM70.1256 7.16211H72.321V9.35537H70.1256V7.16211Z"
            fill="white"
          ></path>
        </svg>
        <p
          class="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#1f2329]"
        >
          Chưa có tổ chức nào.
        </p>
      </div>
      <div
        class="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative"
      >
        <p class="flex-grow w-[279px] text-xs text-center text-[#85888c]">
          Thiết lập tổ chức của bạn để bắt đầu sử dụng hệ thống nhận diện thông
          minh bằng Camera AI!
        </p>
      </div>
    </div>
  </div>
  <div
    class="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2.5 px-3 py-1.5 rounded-lg bg-[#008fd3]"
  >
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      class="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
      preserveAspectRatio="xMidYMid meet"
    >
      <path
        d="M6 2L6 10M10 6L2 6"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></path>
    </svg>
    <p
      class="flex-grow-0 flex-shrink-0 text-xs font-semibold text-center text-white"
    >
      Thêm tổ chức
    </p>
  </div>
</div>

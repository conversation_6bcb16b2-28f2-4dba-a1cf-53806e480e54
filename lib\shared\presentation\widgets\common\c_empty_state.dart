import 'package:flutter/material.dart';

/// Custom empty state widget for displaying when no data is available
/// 
/// Provides consistent empty state UI across mobile and terminal apps
class CEmptyState extends StatelessWidget {
  final IconData? icon;
  final String? iconAsset;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onAction;
  final double? iconSize;
  final Color? iconColor;
  final EdgeInsets? padding;
  final bool showBackground;

  const CEmptyState({
    super.key,
    this.icon,
    this.iconAsset,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : assert(icon != null || iconAsset != null, 'Either icon or iconAsset must be provided');

  /// Create an empty state for no data
  const CEmptyState.noData({
    super.key,
    this.title = 'No Data Available',
    this.subtitle = 'There is no data to display at the moment.',
    this.actionText,
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : icon = Icons.inbox_outlined,
       iconAsset = null;

  /// Create an empty state for no results
  const CEmptyState.noResults({
    super.key,
    this.title = 'No Results Found',
    this.subtitle = 'Try adjusting your search criteria.',
    this.actionText,
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : icon = Icons.search_off_outlined,
       iconAsset = null;

  /// Create an empty state for no internet connection
  const CEmptyState.noConnection({
    super.key,
    this.title = 'No Internet Connection',
    this.subtitle = 'Please check your internet connection and try again.',
    this.actionText = 'Retry',
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : icon = Icons.wifi_off_outlined,
       iconAsset = null;

  /// Create an empty state for errors
  const CEmptyState.error({
    super.key,
    this.title = 'Something Went Wrong',
    this.subtitle = 'An error occurred while loading data.',
    this.actionText = 'Try Again',
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : icon = Icons.error_outline,
       iconAsset = null;

  /// Create an empty state for maintenance
  const CEmptyState.maintenance({
    super.key,
    this.title = 'Under Maintenance',
    this.subtitle = 'This feature is currently under maintenance. Please try again later.',
    this.actionText,
    this.onAction,
    this.iconSize,
    this.iconColor,
    this.padding,
    this.showBackground = false,
  }) : icon = Icons.build_outlined,
       iconAsset = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultPadding = padding ?? const EdgeInsets.all(32.0);
    
    Widget content = Padding(
      padding: defaultPadding,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          _buildIcon(theme),
          
          const SizedBox(height: 24),
          
          // Title
          Text(
            title,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 12),
            Text(
              subtitle!,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          // Action button
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: onAction,
              child: Text(actionText!),
            ),
          ],
        ],
      ),
    );

    if (showBackground) {
      content = Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: content,
      );
    }

    return Center(child: content);
  }

  Widget _buildIcon(ThemeData theme) {
    final defaultIconSize = iconSize ?? 64.0;
    final defaultIconColor = iconColor ?? theme.colorScheme.onSurfaceVariant;

    if (iconAsset != null) {
      return Image.asset(
        iconAsset!,
        width: defaultIconSize,
        height: defaultIconSize,
        color: defaultIconColor,
      );
    }

    return Icon(
      icon!,
      size: defaultIconSize,
      color: defaultIconColor,
    );
  }
}

/// Empty state builder for lists and grids
class CEmptyStateBuilder extends StatelessWidget {
  final bool isEmpty;
  final bool isLoading;
  final bool hasError;
  final Widget child;
  final CEmptyState? emptyState;
  final CEmptyState? errorState;
  final Widget? loadingWidget;

  const CEmptyStateBuilder({
    super.key,
    required this.isEmpty,
    required this.isLoading,
    required this.hasError,
    required this.child,
    this.emptyState,
    this.errorState,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return loadingWidget ?? const Center(child: CircularProgressIndicator());
    }

    if (hasError) {
      return errorState ?? const CEmptyState.error();
    }

    if (isEmpty) {
      return emptyState ?? const CEmptyState.noData();
    }

    return child;
  }
}

/// Sliver version of empty state for use in CustomScrollView
class CSliverEmptyState extends StatelessWidget {
  final CEmptyState emptyState;

  const CSliverEmptyState({
    super.key,
    required this.emptyState,
  });

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      hasScrollBody: false,
      child: emptyState,
    );
  }
}

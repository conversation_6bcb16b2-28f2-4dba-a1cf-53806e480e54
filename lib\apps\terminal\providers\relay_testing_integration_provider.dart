import 'package:flutter/foundation.dart';
import '../../../shared/services/relay_management_service.dart';
import '../../../shared/services/relay_api_service.dart' as api;
import '../../../shared/services/http_client_service.dart';
import '../../../shared/core/config/configuration_manager.dart';
import '../../../shared/core/config/relay_config_parameters.dart';
import '../../../packages/relay_controller/lib/relay_controller.dart';

/// Provider for integrating relay testing with terminal app services
/// 
/// This provider bridges the relay testing functionality with existing
/// terminal app services like device registration, face recognition, etc.
class RelayTestingIntegrationProvider extends ChangeNotifier {
  static RelayTestingIntegrationProvider? _instance;
  static RelayTestingIntegrationProvider get instance => 
      _instance ??= RelayTestingIntegrationProvider._();

  RelayTestingIntegrationProvider._();

  final RelayManagementService _managementService = RelayManagementService.instance;
  final api.RelayApiService _apiService = api.RelayApiService.instance;
  
  // State
  bool _isInitialized = false;
  bool _isConnected = false;
  String? _currentDeviceId;
  String? _lastError;
  List<String> _testLogs = [];
  
  // Configuration
  String _serverUrl = 'http://localhost:3000';
  String _deviceProfile = 'esp32';

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  String? get currentDeviceId => _currentDeviceId;
  String? get lastError => _lastError;
  List<String> get testLogs => List.unmodifiable(_testLogs);
  String get serverUrl => _serverUrl;
  String get deviceProfile => _deviceProfile;

  /// Initialize the relay testing integration
  Future<void> initialize() async {
    try {
      _addLog('Initializing relay testing integration...');
      
      // Load configuration
      await _loadConfiguration();
      
      // Initialize HTTP client for API service
      final httpClient = HttpClientService();
      httpClient.initialize(HttpClientConfig(
        baseUrl: _serverUrl,
        connectTimeout: 30000,
        receiveTimeout: 30000,
        sendTimeout: 30000,
      ));
      
      await _apiService.initialize(httpClient);
      
      _isInitialized = true;
      _lastError = null;
      _addLog('✅ Relay testing integration initialized');
      notifyListeners();
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Initialization failed: $e');
      notifyListeners();
      rethrow;
    }
  }

  /// Load configuration from ConfigurationManager
  Future<void> _loadConfiguration() async {
    try {
      final config = ConfigurationManager.instance;
      
      _currentDeviceId = config.getValue<String>(RelayConfigKeys.relayDeviceId);
      _deviceProfile = config.getValue<String>(RelayConfigKeys.relayDeviceProfile);
      
      // Load server URL from configuration if available
      // _serverUrl = config.getValue<String>('server.base_url', _serverUrl);
      
      _addLog('Configuration loaded: Device ID: $_currentDeviceId, Profile: $_deviceProfile');
      
    } catch (e) {
      _addLog('⚠️ Configuration load failed, using defaults: $e');
    }
  }

  /// Test USB-TTL connection
  Future<bool> testUsbConnection() async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Testing USB-TTL connection...');
      
      final deviceConfig = RelayDeviceConfig(
        deviceId: _currentDeviceId ?? 'T-TEST-R01',
        deviceName: 'Test Relay Device',
        relayCount: 4,
        baudRate: 115200,
      );
      
      await _managementService.initialize(
        config: deviceConfig,
        autoConnect: true,
      );
      
      _isConnected = _managementService.isConnected;
      
      if (_isConnected) {
        _addLog('✅ USB-TTL connection successful');
      } else {
        _addLog('❌ USB-TTL connection failed');
      }
      
      _lastError = null;
      notifyListeners();
      return _isConnected;
      
    } catch (e) {
      _lastError = e.toString();
      _isConnected = false;
      _addLog('❌ USB-TTL connection error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Test server connection
  Future<bool> testServerConnection() async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Testing server connection...');
      
      // Test basic connectivity
      final devices = await _apiService.getDevices();
      
      _addLog('✅ Server connection successful');
      _addLog('Found ${devices.devices.length} registered devices');
      
      _lastError = null;
      notifyListeners();
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Server connection failed: $e');
      notifyListeners();
      return false;
    }
  }

  /// Register device with server
  Future<bool> registerDevice({bool useSecureApi = true}) async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Registering device with server...');
      
      final deviceConfig = RelayDeviceConfig(
        deviceId: _currentDeviceId ?? 'T-TEST-R01',
        deviceName: 'Terminal Relay Device',
        relayCount: 4,
        baudRate: 115200,
      );
      
      final response = await _apiService.registerDevice(
        deviceConfig: deviceConfig,
        additionalInfo: {
          'test_mode': true,
          'profile': _deviceProfile,
          'integration': 'terminal_app',
        },
        useSecureApi: useSecureApi,
      );
      
      _addLog('✅ Device registered successfully');
      _addLog('Device ID: ${response.deviceId}');
      _addLog('Registration time: ${response.registeredAt}');
      
      _lastError = null;
      notifyListeners();
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Device registration failed: $e');
      notifyListeners();
      return false;
    }
  }

  /// Send raw command to relay
  Future<bool> sendRawCommand(String command) async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Sending raw command: $command');
      
      await _managementService.sendRawCommand(command);
      
      _addLog('✅ Raw command sent successfully');
      _lastError = null;
      notifyListeners();
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Raw command failed: $e');
      notifyListeners();
      return false;
    }
  }

  /// Control relay via management service
  Future<bool> controlRelay(int relayIndex, RelayAction action) async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Controlling relay $relayIndex: ${action.name}');
      
      await _managementService.controlRelay(relayIndex, action);
      
      _addLog('✅ Relay control successful');
      _lastError = null;
      notifyListeners();
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Relay control failed: $e');
      notifyListeners();
      return false;
    }
  }

  /// Test face recognition integration
  Future<bool> testFaceRecognition() async {
    if (!_isInitialized) {
      throw StateError('Provider not initialized');
    }

    try {
      _addLog('Testing face recognition integration...');
      
      // Sample base64 image data for testing
      const sampleImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
      
      final response = await _apiService.recognizeFace(
        deviceId: _currentDeviceId ?? 'T-TEST-R01',
        imageData: sampleImageData,
        confidenceScore: 0.85,
        metadata: {
          'test_mode': true,
          'source': 'relay_testing_integration',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      
      _addLog('✅ Face recognition test successful');
      _addLog('Recognized: ${response.recognized}');
      _addLog('Recognition ID: ${response.recognitionId}');
      
      if (response.user != null) {
        _addLog('User: ${response.user!['name'] ?? 'Unknown'}');
      }
      
      if (response.allowAccess != null) {
        _addLog('Access: ${response.allowAccess! ? 'GRANTED' : 'DENIED'}');
        
        // If access is granted, trigger relay action
        if (response.allowAccess!) {
          await controlRelay(0, RelayAction.on);
          await Future.delayed(const Duration(seconds: 2));
          await controlRelay(0, RelayAction.off);
          _addLog('✅ Access granted - relay triggered');
        }
      }
      
      _lastError = null;
      notifyListeners();
      return true;
      
    } catch (e) {
      _lastError = e.toString();
      _addLog('❌ Face recognition test failed: $e');
      notifyListeners();
      return false;
    }
  }

  /// Run comprehensive test suite
  Future<bool> runComprehensiveTest() async {
    _addLog('🚀 Starting comprehensive test suite...');
    
    bool allTestsPassed = true;
    
    // Test 1: Server connection
    if (!await testServerConnection()) {
      allTestsPassed = false;
    }
    
    // Test 2: Device registration
    if (!await registerDevice()) {
      allTestsPassed = false;
    }
    
    // Test 3: USB connection
    if (!await testUsbConnection()) {
      allTestsPassed = false;
    }
    
    // Test 4: Raw command
    if (!await sendRawCommand('R0:1')) {
      allTestsPassed = false;
    }
    
    await Future.delayed(const Duration(seconds: 1));
    
    if (!await sendRawCommand('R0:0')) {
      allTestsPassed = false;
    }
    
    // Test 5: Face recognition integration
    if (!await testFaceRecognition()) {
      allTestsPassed = false;
    }
    
    if (allTestsPassed) {
      _addLog('✅ All tests passed successfully!');
    } else {
      _addLog('❌ Some tests failed. Check logs for details.');
    }
    
    notifyListeners();
    return allTestsPassed;
  }

  /// Update server URL
  void updateServerUrl(String newUrl) {
    _serverUrl = newUrl;
    _addLog('Server URL updated: $newUrl');
    notifyListeners();
  }

  /// Update device profile
  void updateDeviceProfile(String newProfile) {
    _deviceProfile = newProfile;
    _addLog('Device profile updated: $newProfile');
    notifyListeners();
  }

  /// Clear test logs
  void clearLogs() {
    _testLogs.clear();
    notifyListeners();
  }

  /// Add log entry
  void _addLog(String message) {
    final timestamp = DateTime.now().toLocal().toString().substring(11, 19);
    _testLogs.add('$timestamp - $message');
    
    // Keep only last 100 log entries
    if (_testLogs.length > 100) {
      _testLogs.removeAt(0);
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    _testLogs.clear();
    super.dispose();
  }
}

/// Face Capture UI Configuration
///
/// This file contains UI-specific configurations that can be easily modified
/// to change the appearance and behavior of the face capture interface.
/// Different overlay modes for camera display
enum OverlayMode {
  /// Full overlay with dark background, only circle visible (most focused)
  full,

  /// Light overlay with semi-transparent background (balanced)
  light,

  /// No overlay, only guide border (full camera view)
  none,
}

/// Indicator style for direction indicators
enum IndicatorStyle {
  /// Simple colored dots
  dots,
  /// Arrow shapes pointing inward
  arrows,
  /// Small icons (face emojis)
  icons,
  /// iOS-style dash marks around circle (like Face ID)
  iosDashes,
}

class FaceCaptureUIConfig {
  // ============================================================================
  // CAMERA OVERLAY OPTIONS
  // ============================================================================

  /// Current overlay mode - change this to switch between modes
  static const OverlayMode overlayMode = OverlayMode.light;

  /// Overlay opacity for light mode (0.0 = transparent, 1.0 = opaque)
  static const double lightOverlayOpacity = 0.3;

  /// Overlay opacity for full mode
  static const double fullOverlayOpacity = 0.8;

  /// Overlay color
  static const int overlayColorValue = 0xFF000000; // Black

  // ============================================================================
  // GUIDE CIRCLE CUSTOMIZATION
  // ============================================================================

  /// Guide circle border width
  static const double guideBorderWidth = 3.0;

  /// Enable/disable center crosshair
  static const bool showCrosshair = true;

  /// Crosshair length
  static const double crosshairLength = 20.0;

  /// Crosshair opacity
  static const double crosshairOpacity = 0.5;

  // ============================================================================
  // DIRECTION INDICATORS ON CIRCLE
  // ============================================================================

  /// Enable/disable direction indicators on circle border
  static const bool showDirectionIndicators = true;

  /// Size of direction indicator dots/arrows
  static const double indicatorSize = 12.0;

  /// Distance of indicators from circle border (outward)
  static const double indicatorOffset = 8.0;

  /// Current indicator style
  static const IndicatorStyle indicatorStyle = IndicatorStyle.icons;

  /// Indicator colors
  static const int activeIndicatorColor = 0xFF4CAF50; // Green for current direction
  static const int inactiveIndicatorColor = 0xFF9E9E9E; // Gray for other directions
  static const int capturedIndicatorColor = 0xFF2196F3; // Blue for captured directions

  /// iOS dash style configuration (optimized for performance)
  static const double iosDashLength = 8.0;
  static const double iosDashWidth = 2.5;
  static const double iosDashSpacing = 1.0;
  static const int iosDashCount = 60; // Reduced for better performance

  /// iOS dash colors
  static const int iosDashActiveColor = 0xFF007AFF; // iOS blue
  static const int iosDashCapturedColor = 0xFF34C759; // iOS green
  static const int iosDashInactiveColor = 0xFF8E8E93; // iOS gray
  static const int iosDashBackgroundColor = 0xFF2C2C2E; // iOS dark background

  /// Camera configuration
  /// Whether to use front camera by default
  static const bool useFrontCamera = true;

  /// Whether to mirror directions for front camera
  /// (left/right are mirrored when using front camera)
  static const bool mirrorDirectionsForFrontCamera = true;

  /// Circle position offset from center (negative = up, positive = down)
  static const double circleVerticalOffset = -60.0;

  /// Circle size multiplier (1.0 = normal, 1.35 = 35% larger)
  static const double circleSizeMultiplier = 1.35;

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /// Get overlay opacity based on current mode
  static double getOverlayOpacity() {
    switch (overlayMode) {
      case OverlayMode.full:
        return fullOverlayOpacity;
      case OverlayMode.light:
        return lightOverlayOpacity;
      case OverlayMode.none:
        return 0.0;
    }
  }

  /// Check if overlay should be drawn
  static bool shouldDrawOverlay() {
    return overlayMode != OverlayMode.none;
  }

  /// Get overlay color with appropriate opacity
  static int getOverlayColor() {
    return overlayColorValue;
  }

  // ============================================================================
  // PRESET CONFIGURATIONS
  // ============================================================================

  /// Apply Instagram-like configuration (focused with dark overlay)
  static void applyInstagramStyle() {
    // This would require making the constants non-const and adding setters
    // For now, manually change overlayMode to OverlayMode.full
  }

  /// Apply minimal configuration (just border, no overlay)
  static void applyMinimalStyle() {
    // This would require making the constants non-const and adding setters
    // For now, manually change overlayMode to OverlayMode.none
  }

  /// Apply balanced configuration (light overlay)
  static void applyBalancedStyle() {
    // This would require making the constants non-const and adding setters
    // For now, manually change overlayMode to OverlayMode.light (default)
  }
}

/// Quick configuration presets - uncomment the one you want to use:

// For Instagram/TikTok-like focused experience:
// static const OverlayMode overlayMode = OverlayMode.full;

// For balanced experience (recommended):
// static const OverlayMode overlayMode = OverlayMode.light;

// For full camera view without distraction:
// static const OverlayMode overlayMode = OverlayMode.none;

import 'dart:io';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../services/face_cropping_api_service.dart';
import '../core/constants/face_cropping_constants.dart';

/// Enhanced face capture result that includes both original and cropped face data
class EnhancedFaceCaptureResult {
  final bool success;
  final Map<FaceDirection, String> capturedImages;
  final Map<FaceDirection, String> croppedImages;
  final Map<FaceDirection, Face> detectedFaces;
  final DateTime timestamp;
  final String? error;
  
  // Face cropping API results
  final Map<FaceDirection, FaceCroppingSyncResult>? syncResults;
  final Map<FaceDirection, FaceCroppingAsyncResult>? asyncResults;
  final List<String>? queueOperationIds;
  
  EnhancedFaceCaptureResult({
    required this.success,
    required this.capturedImages,
    required this.croppedImages,
    required this.detectedFaces,
    required this.timestamp,
    this.error,
    this.syncResults,
    this.asyncResults,
    this.queueOperationIds,
  });
  
  /// Create successful result with original and cropped images
  factory EnhancedFaceCaptureResult.success({
    required Map<FaceDirection, String?> originalImages,
    required Map<FaceDirection, String?> croppedImages,
    required Map<FaceDirection, Face> detectedFaces,
    Map<FaceDirection, FaceCroppingSyncResult>? syncResults,
    Map<FaceDirection, FaceCroppingAsyncResult>? asyncResults,
    List<String>? queueOperationIds,
  }) {
    // Filter out null images
    final validOriginalImages = <FaceDirection, String>{};
    originalImages.forEach((direction, path) {
      if (path != null) {
        validOriginalImages[direction] = path;
      }
    });
    
    final validCroppedImages = <FaceDirection, String>{};
    croppedImages.forEach((direction, path) {
      if (path != null) {
        validCroppedImages[direction] = path;
      }
    });
    
    return EnhancedFaceCaptureResult(
      success: true,
      capturedImages: validOriginalImages,
      croppedImages: validCroppedImages,
      detectedFaces: detectedFaces,
      timestamp: DateTime.now(),
      syncResults: syncResults,
      asyncResults: asyncResults,
      queueOperationIds: queueOperationIds,
    );
  }
  
  /// Create failure result
  factory EnhancedFaceCaptureResult.failure({String? error}) {
    return EnhancedFaceCaptureResult(
      success: false,
      capturedImages: {},
      croppedImages: {},
      detectedFaces: {},
      timestamp: DateTime.now(),
      error: error,
    );
  }
  
  /// Check if has any images
  bool get hasImages => capturedImages.isNotEmpty;
  
  /// Check if has any cropped images
  bool get hasCroppedImages => croppedImages.isNotEmpty;
  
  /// Number of captured images
  int get imageCount => capturedImages.length;
  
  /// Number of cropped images
  int get croppedImageCount => croppedImages.length;
  
  /// List of captured directions
  List<FaceDirection> get capturedDirections => capturedImages.keys.toList();
  
  /// List of cropped directions
  List<FaceDirection> get croppedDirections => croppedImages.keys.toList();
  
  /// Get original image path for direction
  String? getOriginalImagePath(FaceDirection direction) {
    return capturedImages[direction];
  }
  
  /// Get cropped image path for direction
  String? getCroppedImagePath(FaceDirection direction) {
    return croppedImages[direction];
  }
  
  /// Get detected face for direction
  Face? getDetectedFace(FaceDirection direction) {
    return detectedFaces[direction];
  }
  
  /// Check if direction has been captured
  bool hasDirection(FaceDirection direction) {
    return capturedImages.containsKey(direction);
  }
  
  /// Check if direction has been cropped
  bool hasCroppedDirection(FaceDirection direction) {
    return croppedImages.containsKey(direction);
  }
  
  /// Validate that all image files exist
  bool validateOriginalImages() {
    return capturedImages.values.every((path) => File(path).existsSync());
  }
  
  /// Validate that all cropped image files exist
  bool validateCroppedImages() {
    return croppedImages.values.every((path) => File(path).existsSync());
  }
  
  /// Check if capture is complete (all 5 directions)
  bool get isComplete {
    const requiredDirections = [
      FaceDirection.front,
      FaceDirection.top,
      FaceDirection.bottom,
      FaceDirection.left,
      FaceDirection.right,
    ];
    
    return requiredDirections.every((direction) => capturedImages.containsKey(direction));
  }
  
  /// Check if cropping is complete for all captured directions
  bool get isCroppingComplete {
    return capturedDirections.every((direction) => croppedImages.containsKey(direction));
  }
  
  /// Get API processing results summary
  Map<String, dynamic> getApiResultsSummary() {
    final summary = <String, dynamic>{
      'sync_results_count': syncResults?.length ?? 0,
      'async_results_count': asyncResults?.length ?? 0,
      'queue_operations_count': queueOperationIds?.length ?? 0,
      'successful_sync_results': 0,
      'failed_sync_results': 0,
      'successful_async_results': 0,
      'failed_async_results': 0,
    };
    
    // Count sync results
    if (syncResults != null) {
      for (final result in syncResults!.values) {
        if (result.success) {
          summary['successful_sync_results']++;
        } else {
          summary['failed_sync_results']++;
        }
      }
    }
    
    // Count async results
    if (asyncResults != null) {
      for (final result in asyncResults!.values) {
        if (result.success) {
          summary['successful_async_results']++;
        } else {
          summary['failed_async_results']++;
        }
      }
    }
    
    return summary;
  }
  
  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'captured_images': capturedImages.map((key, value) => MapEntry(key.name, value)),
      'cropped_images': croppedImages.map((key, value) => MapEntry(key.name, value)),
      'detected_faces_count': detectedFaces.length,
      'timestamp': timestamp.toIso8601String(),
      'error': error,
      'image_count': imageCount,
      'cropped_image_count': croppedImageCount,
      'is_complete': isComplete,
      'is_cropping_complete': isCroppingComplete,
      'api_results_summary': getApiResultsSummary(),
    };
  }
  
  /// Create from original FaceCaptureResult (for backward compatibility)
  factory EnhancedFaceCaptureResult.fromOriginal(
    dynamic originalResult, {
    Map<FaceDirection, String>? croppedImages,
    Map<FaceDirection, Face>? detectedFaces,
  }) {
    if (originalResult is Map<String, dynamic>) {
      // Handle JSON format
      final success = originalResult['success'] as bool? ?? false;
      final capturedImagesMap = originalResult['captured_images'] as Map<String, dynamic>? ?? {};
      
      final capturedImages = <FaceDirection, String>{};
      capturedImagesMap.forEach((key, value) {
        final direction = FaceDirection.values.firstWhere(
          (d) => d.name == key,
          orElse: () => FaceDirection.unknown,
        );
        if (direction != FaceDirection.unknown && value is String) {
          capturedImages[direction] = value;
        }
      });
      
      return EnhancedFaceCaptureResult(
        success: success,
        capturedImages: capturedImages,
        croppedImages: croppedImages ?? {},
        detectedFaces: detectedFaces ?? {},
        timestamp: DateTime.now(),
        error: originalResult['error'] as String?,
      );
    }
    
    // Handle other formats or return failure
    return EnhancedFaceCaptureResult.failure(error: 'Invalid original result format');
  }
  
  /// Clean up temporary files (both original and cropped)
  Future<void> cleanupTempFiles() async {
    final allPaths = <String>[];
    allPaths.addAll(capturedImages.values);
    allPaths.addAll(croppedImages.values);
    
    for (final path in allPaths) {
      try {
        final file = File(path);
        if (file.existsSync()) {
          await file.delete();
        }
      } catch (e) {
        // Ignore cleanup errors
      }
    }
  }
  
  @override
  String toString() {
    return 'EnhancedFaceCaptureResult('
        'success: $success, '
        'imageCount: $imageCount, '
        'croppedImageCount: $croppedImageCount, '
        'directions: ${capturedDirections.map((d) => d.name).join(', ')}, '
        'croppedDirections: ${croppedDirections.map((d) => d.name).join(', ')}'
        ')';
  }
}

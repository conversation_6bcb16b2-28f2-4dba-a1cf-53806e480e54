import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// View/Eye icon component for password visibility toggle
class ViewIcon extends StatelessWidget {
  final bool isVisible;
  final double size;
  final Color? color;

  const ViewIcon({
    super.key,
    required this.isVisible,
    this.size = 16.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor = color?.toARGB32().toRadixString(16).padLeft(8, '0').substring(2) ??
                     (isVisible ? '008FD3' : '9CA5B3');
    
    return SizedBox(
      width: size,
      height: size,
      child: SvgPicture.string(
        isVisible ? _visibleEyeSvg(iconColor) : _hiddenEyeSvg(iconColor),
        width: size,
        height: size,
      ),
    );
  }

  String _visibleEyeSvg(String color) {
    return '''<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M1.33333 8C1.33333 8 3.66667 3.33333 8 3.33333C12.3333 3.33333 14.6667 8 14.6667 8C14.6667 8 12.3333 12.6667 8 12.6667C3.66667 12.6667 1.33333 8 1.33333 8Z"
        stroke="#$color"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z"
        stroke="#$color"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>''';
  }

  String _hiddenEyeSvg(String color) {
    return '''<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.41 9.41C9.2044 9.68484 8.94102 9.90811 8.64013 10.0629C8.33924 10.2177 8.00738 10.2986 7.67 10.3C7.11772 10.3 6.58829 10.0809 6.20322 9.69578C5.81814 9.31071 5.59 8.78128 5.59 8.229C5.59138 7.89162 5.67226 7.55976 5.82706 7.25887C5.98187 6.95798 6.20514 6.6946 6.48 6.489M11.93 11.93C10.8407 12.7425 9.53823 13.2045 8.19 13.26C3.86 13.26 1.53 8.59 1.53 8.59C2.42031 6.9019 3.70832 5.45586 5.28 4.38L11.93 11.93ZM6.73 3.13C7.14667 3.04333 7.57 3 8 3C12.33 3 14.66 7.67 14.66 7.67C14.2618 8.4212 13.7831 9.12425 13.23 9.77L6.73 3.13Z"
        stroke="#$color"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.33 1.33L14.67 14.67"
        stroke="#$color"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>''';
  }
}

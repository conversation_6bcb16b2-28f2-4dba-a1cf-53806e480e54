/// Mobile app route names constants
/// 
/// Centralized route name constants for the mobile application
/// Migrated from c-faces project and adapted for multi-app architecture
class MobileRouteNames {
  // Private constructor to prevent instantiation
  MobileRouteNames._();

  // ============================================================================
  // ROOT ROUTES
  // ============================================================================
  
  static const String root = '/';
  static const String splash = '/splash';

  // ============================================================================
  // AUTHENTICATION ROUTES
  // ============================================================================
  
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String enterOtp = '/enter-otp';
  static const String confirmPassword = '/confirm-password';
  static const String successfully = '/successfully';

  // ============================================================================
  // MAIN APP ROUTES
  // ============================================================================
  
  static const String home = '/home';
  static const String dashboard = '/dashboard';
  static const String main = '/main';
  
  // ============================================================================
  // TENANT MANAGEMENT ROUTES
  // ============================================================================
  
  static const String tenants = '/tenants';
  static const String tenantCreate = '/tenant-create';
  
  // ============================================================================
  // USER MANAGEMENT ROUTES
  // ============================================================================
  
  static const String users = '/users';
  static const String userDetail = '/user-detail';
  static const String userFaceRegister = '/user-face-register';
  static const String userItemDetail = '/user-item-detail';
  
  // ============================================================================
  // TOOLS ROUTES
  // ============================================================================
  
  static const String tools = '/tools';
  static const String accessControl = '/access-control';
  static const String attendance = '/attendance';
  static const String securityMonitoring = '/security-monitoring';
  static const String systemManagement = '/system-management';
  
  // ============================================================================
  // PROFILE & SETTINGS ROUTES
  // ============================================================================
  
  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String changePassword = '/change-password';
  static const String updateAccount = '/update-account';
  static const String settings = '/settings';
  static const String settingsAccount = '/settings/account';
  static const String settingsPrivacy = '/settings/privacy';
  static const String settingsNotifications = '/settings/notifications';
  
  // ============================================================================
  // NOTIFICATION ROUTES
  // ============================================================================
  
  static const String notifications = '/notifications';
  
  // ============================================================================
  // FACE CAPTURE ROUTES
  // ============================================================================
  
  static const String faceCapture = '/face-capture';
  static const String faceGuide = '/face-guide';
  static const String faceResult = '/face-result';
  static const String faceValidation = '/face-validation';
  
  // ============================================================================
  // ERROR ROUTES
  // ============================================================================
  
  static const String notFound = '/not-found';
  static const String error = '/error';
  
  // ============================================================================
  // DEVELOPMENT ROUTES (remove in production)
  // ============================================================================

  static const String iconDemo = '/icon-demo';
  static const String faceLifecycleTest = '/face-lifecycle-test';
  static const String faceWrapperTest = '/face-wrapper-test';
  
  // ============================================================================
  // ROUTE PARAMETERS
  // ============================================================================
  
  static const String userIdParam = 'userId';
  static const String tenantIdParam = 'tenantId';
  static const String faceIdParam = 'faceId';
  static const String errorMessageParam = 'errorMessage';
  
  // ============================================================================
  // ROUTE WITH PARAMETERS HELPERS
  // ============================================================================
  
  static String profileWithId(String userId) => '/profile/$userId';
  static String userDetailWithId(String userId) => '/user-detail/$userId';
  static String tenantDetailWithId(String tenantId) => '/tenant-detail/$tenantId';
  static String faceResultWithId(String faceId) => '/face-result/$faceId';
  static String errorWithMessage(String message) => '/error?message=$message';
  
  // ============================================================================
  // ROUTE VALIDATION HELPERS
  // ============================================================================
  
  /// Check if a route is an authentication route
  static bool isAuthRoute(String route) {
    return [
      login,
      register,
      forgotPassword,
      enterOtp,
      confirmPassword,
      successfully,
    ].contains(route);
  }
  
  /// Check if a route requires authentication
  static bool isProtectedRoute(String route) {
    return ![
      login,
      register,
      forgotPassword,
      enterOtp,
      confirmPassword,
      successfully,
      splash,
      notFound,
      error,
    ].contains(route);
  }
  
  /// Check if a route is a face-related route
  static bool isFaceRoute(String route) {
    return [
      faceCapture,
      faceGuide,
      faceResult,
      faceValidation,
      userFaceRegister,
    ].contains(route);
  }
  
  /// Check if a route is a user management route
  static bool isUserRoute(String route) {
    return [
      users,
      userDetail,
      userFaceRegister,
      userItemDetail,
    ].contains(route);
  }
  
  /// Check if a route is a tenant management route
  static bool isTenantRoute(String route) {
    return [
      tenants,
      tenantCreate,
    ].contains(route);
  }
  
  /// Check if a route is a tools route
  static bool isToolsRoute(String route) {
    return [
      tools,
      accessControl,
      attendance,
      securityMonitoring,
      systemManagement,
    ].contains(route);
  }
  
  /// Get all route names as a list
  static List<String> get allRoutes => [
    root,
    splash,
    login,
    register,
    forgotPassword,
    enterOtp,
    confirmPassword,
    successfully,
    home,
    dashboard,
    main,
    tenants,
    tenantCreate,
    users,
    userDetail,
    userFaceRegister,
    userItemDetail,
    tools,
    accessControl,
    attendance,
    securityMonitoring,
    systemManagement,
    profile,
    profileEdit,
    changePassword,
    updateAccount,
    settings,
    settingsAccount,
    settingsPrivacy,
    settingsNotifications,
    notifications,
    faceCapture,
    faceGuide,
    faceResult,
    faceValidation,
    notFound,
    error,
    iconDemo,
    faceLifecycleTest,
    faceWrapperTest,
  ];
  
  /// Get route display name for UI
  static String getDisplayName(String route) {
    switch (route) {
      case root:
        return 'Home';
      case splash:
        return 'Splash';
      case login:
        return 'Login';
      case register:
        return 'Register';
      case forgotPassword:
        return 'Forgot Password';
      case enterOtp:
        return 'Enter OTP';
      case confirmPassword:
        return 'Confirm Password';
      case successfully:
        return 'Success';
      case home:
      case dashboard:
      case main:
        return 'Dashboard';
      case tenants:
        return 'Tenants';
      case tenantCreate:
        return 'Create Tenant';
      case users:
        return 'Users';
      case userDetail:
        return 'User Detail';
      case userFaceRegister:
        return 'Face Registration';
      case userItemDetail:
        return 'User Item Detail';
      case tools:
        return 'Tools';
      case accessControl:
        return 'Access Control';
      case attendance:
        return 'Attendance';
      case securityMonitoring:
        return 'Security Monitoring';
      case systemManagement:
        return 'System Management';
      case profile:
        return 'Profile';
      case profileEdit:
        return 'Edit Profile';
      case changePassword:
        return 'Change Password';
      case updateAccount:
        return 'Update Account';
      case settings:
        return 'Settings';
      case notifications:
        return 'Notifications';
      case faceCapture:
        return 'Face Capture';
      case faceGuide:
        return 'Face Guide';
      case faceResult:
        return 'Face Result';
      case faceValidation:
        return 'Face Validation';
      case notFound:
        return 'Not Found';
      case error:
        return 'Error';
      case iconDemo:
        return 'Icon Demo';
      case faceLifecycleTest:
        return 'Face Lifecycle Test';
      case faceWrapperTest:
        return 'Face Wrapper Test';
      default:
        return route.replaceAll('/', '').replaceAll('-', ' ').toUpperCase();
    }
  }
}

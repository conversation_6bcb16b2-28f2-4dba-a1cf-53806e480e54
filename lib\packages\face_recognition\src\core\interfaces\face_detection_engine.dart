import 'dart:typed_data';
import 'dart:ui';

import 'package:camera/camera.dart';

/// Abstract interface for face detection engines
abstract class FaceDetectionEngine {
  /// Get engine name
  String get engineName;
  
  /// Check if engine is initialized
  bool get isInitialized;
  
  /// Initialize the detection engine
  Future<void> initialize();
  
  /// Detect faces in image bytes
  Future<List<FaceDetection>> detectFaces(Uint8List imageBytes);
  
  /// Process camera image for face detection
  Future<List<FaceDetection>> processCameraImage(CameraImage image);
  
  /// Dispose of resources
  void dispose();
}

/// Face detection result
class FaceDetection {
  final Rect boundingBox;
  final List<Point> landmarks;
  final double confidence;
  final double quality;
  final FacePose pose;
  final Uint8List? croppedFace;
  final Map<String, dynamic> metadata;
  
  const FaceDetection({
    required this.boundingBox,
    required this.landmarks,
    required this.confidence,
    required this.quality,
    required this.pose,
    this.croppedFace,
    this.metadata = const {},
  });
  
  @override
  String toString() {
    return 'FaceDetection(confidence: ${(confidence * 100).toStringAsFixed(1)}%, quality: ${(quality * 100).toStringAsFixed(1)}%)';
  }
}

/// Face pose information
class FacePose {
  final double yaw;
  final double pitch;
  final double roll;
  
  const FacePose({
    this.yaw = 0.0,
    this.pitch = 0.0,
    this.roll = 0.0,
  });
  
  bool get isFrontal {
    return yaw.abs() < 15 && pitch.abs() < 15 && roll.abs() < 15;
  }
  
  @override
  String toString() {
    return 'FacePose(yaw: ${yaw.toStringAsFixed(1)}°, pitch: ${pitch.toStringAsFixed(1)}°, roll: ${roll.toStringAsFixed(1)}°)';
  }
}

/// Point class for landmarks
class Point {
  final double x;
  final double y;
  
  const Point(this.x, this.y);
  
  @override
  String toString() {
    return 'Point(${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})';
  }
}

/// Face detection exception
class FaceDetectionException implements Exception {
  final String message;
  final String? engineName;
  final dynamic originalError;
  
  const FaceDetectionException(
    this.message, {
    this.engineName,
    this.originalError,
  });
  
  @override
  String toString() {
    final engine = engineName != null ? ' [$engineName]' : '';
    return 'FaceDetectionException$engine: $message';
  }
}

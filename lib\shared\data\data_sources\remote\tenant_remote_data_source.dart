import '../../models/tenant/tenant_model.dart';
import '../../../core/network/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../../../services/api_endpoints.dart';
import '../../../services/token_refresh_service.dart';

/// Remote data source for tenant operations
abstract class TenantRemoteDataSource {
  /// Get user tenants with pagination
  Future<List<TenantModel>> getUserTenants({
    int page = 1,
    int size = 10,
  });

  /// Get tenant by ID
  Future<TenantModel> getTenantById(String tenantId);

  /// Create new tenant
  Future<TenantModel> createTenant({
    required String name,
    String? address,
    String? unitId,
    List<String>? mappings,
  });

  /// Update tenant
  Future<TenantModel> updateTenant({
    required String tenantId,
    String? name,
    String? address,
    String? unitId,
    List<String>? mappings,
  });

  /// Delete tenant
  Future<void> deleteTenant(String tenantId);

  /// Switch tenant context
  Future<bool> switchTenantContext(String tenantId);

  // ========== UNITS MANAGEMENT ==========
  /// Get all units with pagination and optional filtering
  Future<List<Map<String, dynamic>>> getUnits({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  });

  /// Get units response with pagination info
  Future<Map<String, dynamic>> getUnitsWithPagination({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  });

  /// Get unit by ID
  Future<Map<String, dynamic>> getUnitById(String unitId);

  /// Get units by tenant ID
  Future<List<Map<String, dynamic>>> getUnitsByTenant(String tenantId);

  /// Create a new unit
  Future<Map<String, dynamic>> createUnit({
    required String name,
    required String tenantId,
    String? parentUnitId,
    List<String>? mappings,
  });

  /// Update unit
  Future<Map<String, dynamic>> updateUnit({
    required String unitId,
    String? name,
    String? parentUnitId,
    List<String>? mappings,
  });

  /// Delete unit
  Future<void> deleteUnit(String unitId);
}

/// Implementation of TenantRemoteDataSource
class TenantRemoteDataSourceImpl implements TenantRemoteDataSource {
  final ApiClient apiClient;

  TenantRemoteDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<TenantModel>> getUserTenants({
    int page = 1,
    int size = 10,
  }) async {
    try {
      final response = await apiClient.get(
        '${ApiEndpoints.userTenants}?page=$page&size=$size',
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        final items = response['data']['items'] as List<dynamic>;
        return items.map((item) => TenantModel.fromJson(item as Map<String, dynamic>)).toList();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get user tenants';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get user tenants: $e');
    }
  }

  @override
  Future<TenantModel> getTenantById(String tenantId) async {
    try {
      final response = await apiClient.get('${ApiEndpoints.tenants}/$tenantId');

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        return TenantModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get tenant: $e');
    }
  }

  @override
  Future<TenantModel> createTenant({
    required String name,
    String? address,
    String? unitId,
    List<String>? mappings,
  }) async {
    try {
      final response = await apiClient.post(
        ApiEndpoints.tenants,
        body: {
          'name': name,
          if (address != null) 'address': address,
          if (unitId != null) 'unit_id': unitId,
          if (mappings != null) 'mappings': mappings,
        },
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        return TenantModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to create tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to create tenant: $e');
    }
  }

  @override
  Future<TenantModel> updateTenant({
    required String tenantId,
    String? name,
    String? address,
    String? unitId,
    List<String>? mappings,
  }) async {
    try {
      final response = await apiClient.put(
        '${ApiEndpoints.tenants}/$tenantId',
        body: {
          if (name != null) 'name': name,
          if (address != null) 'address': address,
          if (unitId != null) 'unit_id': unitId,
          if (mappings != null) 'mappings': mappings,
        },
      );

      // Handle the new API response format
      if (response['success'] == true && response['data'] != null) {
        return TenantModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      if (e is ValidationException) rethrow;
      throw ServerException('Failed to update tenant: $e');
    }
  }

  @override
  Future<void> deleteTenant(String tenantId) async {
    try {
      final response = await apiClient.delete('${ApiEndpoints.tenants}/$tenantId');

      // Handle the new API response format
      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Failed to delete tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to delete tenant: $e');
    }
  }

  @override
  Future<bool> switchTenantContext(String tenantId) async {
    try {
      print('🔄 Switching tenant context to: $tenantId');
      print('🔄 API endpoint: ${ApiEndpoints.switchContext}');

      final response = await apiClient.post(
        ApiEndpoints.switchContext,
        body: {
          'current_tenant_id': tenantId,
        },
      );

      print('🔄 Switch context response: $response');

      // Handle the API response format and refresh token after switch success
      if (response['success'] == true) {
        final data = response['data'];
        if (data != null && data['success'] == true) {
          print('✅ Switch context successful');

          // Refresh token to get new access token for the new tenant context
          print('🔄 Refreshing token for new tenant context...');
          final tokenRefreshSuccess = await TokenRefreshService.instance.refreshToken();

          if (tokenRefreshSuccess) {
            print('✅ Token refreshed successfully for new tenant context');
          } else {
            print('⚠️ Token refresh failed after context switch, but context switch was successful');
            // Note: We still return true because the context switch itself was successful
            // The token refresh failure will be handled by the auth system
          }

          return true;
        }
      }

      print('❌ Switch context failed - success not true');
      return false;
    } catch (e) {
      print('🔴 Switch context error: $e');
      print('🔴 Error type: ${e.runtimeType}');

      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to switch tenant context: $e');
    }
  }

  // ========== UNITS IMPLEMENTATION ==========

  @override
  Future<List<Map<String, dynamic>>> getUnits({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    try {
      // API uses pageIndex and pageSize instead of skip/limit
      final queryParameters = <String, dynamic>{
        'pageSize': limit.toString(),
        'pageIndex': page.toString(),
        if (sortBy != null) 'sortBy': sortBy,
        if (sortDirection != null) 'sortDirection': sortDirection,
        if (search != null) 'search': search,
        if (tenantId != null) 'tenant_id': tenantId,
        if (parentUnitId != null) 'parent_unit_id': parentUnitId,
      };

      final response = await apiClient.get(
        ApiEndpoints.units,
        queryParameters: queryParameters,
      );

      if (response['success'] == true && response['data'] != null) {
        final items = response['data']['items'] as List<dynamic>;
        return items.cast<Map<String, dynamic>>();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get units';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get units: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getUnitsWithPagination({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'pageSize': limit.toString(),
        'pageIndex': page.toString(),
        if (sortBy != null) 'sortBy': sortBy,
        if (sortDirection != null) 'sortDirection': sortDirection,
        if (search != null) 'search': search,
        if (tenantId != null) 'tenant_id': tenantId,
        if (parentUnitId != null) 'parent_unit_id': parentUnitId,
      };

      final response = await apiClient.get(
        ApiEndpoints.units,
        queryParameters: queryParameters,
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'] as Map<String, dynamic>;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get units';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get units with pagination: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getUnitById(String unitId) async {
    try {
      final response = await apiClient.get(ApiEndpoints.unitDetail(unitId));

      if (response['success'] == true && response['data'] != null) {
        final unitData = response['data']['unit'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return unitData;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get unit';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get unit: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getUnitsByTenant(String tenantId) async {
    try {
      final response = await apiClient.get(
        ApiEndpoints.units,
        queryParameters: {'tenant_id': tenantId},
      );

      if (response['success'] == true && response['data'] != null) {
        final items = response['data']['items'] as List<dynamic>? ??
                     response['data'] as List<dynamic>? ?? [];

        return items.cast<Map<String, dynamic>>();
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to get units by tenant';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to get units by tenant: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> createUnit({
    required String name,
    required String tenantId,
    String? parentUnitId,
    List<String>? mappings,
  }) async {
    try {
      final response = await apiClient.post(
        ApiEndpoints.createUnit,
        body: {
          'name': name,
          'tenant_id': tenantId,
          if (parentUnitId != null) 'parent_unit_id': parentUnitId,
          if (mappings != null) 'mappings': mappings,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final unitData = response['data']['unit'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return unitData;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to create unit';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to create unit: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> updateUnit({
    required String unitId,
    String? name,
    String? parentUnitId,
    List<String>? mappings,
  }) async {
    try {
      final body = <String, dynamic>{};
      if (name != null) body['name'] = name;
      if (parentUnitId != null) body['parent_unit_id'] = parentUnitId;
      if (mappings != null) body['mappings'] = mappings;

      final response = await apiClient.put(
        ApiEndpoints.updateUnit(unitId),
        body: body,
      );

      if (response['success'] == true && response['data'] != null) {
        final unitData = response['data']['unit'] as Map<String, dynamic>? ??
                        response['data'] as Map<String, dynamic>;
        return unitData;
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update unit';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to update unit: $e');
    }
  }

  @override
  Future<void> deleteUnit(String unitId) async {
    try {
      final response = await apiClient.delete(ApiEndpoints.deleteUnit(unitId));

      if (response['success'] != true) {
        final errorMessage = response['error']?['message'] ?? 'Failed to delete unit';
        final errorCode = response['error']?['code'];
        throw ServerException(errorMessage, code: errorCode, statusCode: response['statusCode']);
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      if (e is NetworkException) rethrow;
      if (e is AuthException) rethrow;
      throw ServerException('Failed to delete unit: $e');
    }
  }
}

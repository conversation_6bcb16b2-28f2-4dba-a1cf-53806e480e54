import 'package:flutter/material.dart';
import '../../../core/base/base_provider.dart';
import '../../../core/config/app_config.dart';

/// Global app state provider
/// Manages app-wide state like theme, locale, connectivity, etc.
class AppStateProvider extends BaseProvider {
  final AppConfig _appConfig = AppConfig();

  // ============================================================================
  // THEME STATE
  // ============================================================================
  
  ThemeMode _themeMode = ThemeMode.system;
  bool _isDarkMode = false;

  /// Current theme mode
  ThemeMode get themeMode => _themeMode;
  
  /// Check if dark mode is enabled
  bool get isDarkMode => _isDarkMode;

  /// Toggle theme mode
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    _isDarkMode = _themeMode == ThemeMode.dark;
    notifyListeners();
  }

  /// Set theme mode
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _isDarkMode = mode == ThemeMode.dark;
    notifyListeners();
  }

  /// Update dark mode based on system brightness
  void updateDarkMode(bool isDark) {
    if (_themeMode == ThemeMode.system) {
      _isDarkMode = isDark;
      notifyListeners();
    }
  }

  // ============================================================================
  // LOCALE STATE
  // ============================================================================
  
  Locale _locale = const Locale('vi', 'VN'); // Default to Vietnamese

  /// Current locale
  Locale get locale => _locale;

  /// Set locale
  void setLocale(Locale newLocale) {
    _locale = newLocale;
    notifyListeners();
  }

  /// Check if current locale is Vietnamese
  bool get isVietnamese => _locale.languageCode == 'vi';

  /// Check if current locale is English
  bool get isEnglish => _locale.languageCode == 'en';

  // ============================================================================
  // CONNECTIVITY STATE
  // ============================================================================
  
  bool _isOnline = true;
  String _connectionType = 'unknown';

  /// Check if device is online
  bool get isOnline => _isOnline;
  
  /// Get connection type
  String get connectionType => _connectionType;
  
  /// Check if device is offline
  bool get isOffline => !_isOnline;

  /// Update connectivity status
  void updateConnectivity(bool isConnected, String type) {
    _isOnline = isConnected;
    _connectionType = type;
    notifyListeners();
  }

  // ============================================================================
  // APP LIFECYCLE STATE
  // ============================================================================
  
  AppLifecycleState _lifecycleState = AppLifecycleState.resumed;
  bool _isAppInForeground = true;

  /// Current app lifecycle state
  AppLifecycleState get lifecycleState => _lifecycleState;
  
  /// Check if app is in foreground
  bool get isAppInForeground => _isAppInForeground;
  
  /// Check if app is in background
  bool get isAppInBackground => !_isAppInForeground;

  /// Update app lifecycle state
  void updateLifecycleState(AppLifecycleState state) {
    _lifecycleState = state;
    _isAppInForeground = state == AppLifecycleState.resumed;
    notifyListeners();
  }

  // ============================================================================
  // FEATURE FLAGS STATE
  // ============================================================================
  
  Map<String, bool> _featureFlags = {};

  /// Get feature flag value
  bool getFeatureFlag(String flagName, {bool defaultValue = false}) {
    return _featureFlags[flagName] ?? defaultValue;
  }

  /// Set feature flag value
  void setFeatureFlag(String flagName, bool value) {
    _featureFlags[flagName] = value;
    notifyListeners();
  }

  /// Update multiple feature flags
  void updateFeatureFlags(Map<String, bool> flags) {
    _featureFlags.addAll(flags);
    notifyListeners();
  }

  /// Initialize default feature flags from config
  void initializeFeatureFlags() {
    _featureFlags = {
      'face_capture': _appConfig.enableFaceCapture,
      'user_management': _appConfig.enableUserManagement,
      'offline_mode': _appConfig.enableOfflineMode,
      'biometric_auth': _appConfig.enableBiometricAuth,
      'push_notifications': _appConfig.enablePushNotifications,
    };
    notifyListeners();
  }

  // ============================================================================
  // PERFORMANCE STATE
  // ============================================================================
  
  bool _isLowPerformanceMode = false;
  int _memoryUsage = 0;

  /// Check if low performance mode is enabled
  bool get isLowPerformanceMode => _isLowPerformanceMode;
  
  /// Get current memory usage (in MB)
  int get memoryUsage => _memoryUsage;

  /// Enable/disable low performance mode
  void setLowPerformanceMode(bool enabled) {
    _isLowPerformanceMode = enabled;
    notifyListeners();
  }

  /// Update memory usage
  void updateMemoryUsage(int usage) {
    _memoryUsage = usage;
    
    // Auto-enable low performance mode if memory usage is high
    if (usage > 200 && !_isLowPerformanceMode) {
      setLowPerformanceMode(true);
    } else if (usage < 100 && _isLowPerformanceMode) {
      setLowPerformanceMode(false);
    }
  }

  // ============================================================================
  // NOTIFICATION STATE
  // ============================================================================
  
  bool _notificationsEnabled = true;
  final List<AppNotification> _notifications = [];

  /// Check if notifications are enabled
  bool get notificationsEnabled => _notificationsEnabled;
  
  /// Get current notifications
  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  
  /// Get unread notifications count
  int get unreadNotificationsCount => 
      _notifications.where((n) => !n.isRead).length;

  /// Enable/disable notifications
  void setNotificationsEnabled(bool enabled) {
    _notificationsEnabled = enabled;
    notifyListeners();
  }

  /// Add notification
  void addNotification(AppNotification notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  /// Mark notification as read
  void markNotificationAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();
    }
  }

  /// Clear all notifications
  void clearNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  /// Remove notification
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  // ============================================================================
  // INITIALIZATION
  // ============================================================================
  
  /// Initialize app state
  void initialize() {
    initializeFeatureFlags();
    // Add other initialization logic here
  }

  // ============================================================================
  // DEBUG METHODS
  // ============================================================================
  
  /// Get app state as map for debugging
  Map<String, dynamic> getDebugInfo() {
    return {
      'themeMode': _themeMode.name,
      'isDarkMode': _isDarkMode,
      'locale': '${_locale.languageCode}_${_locale.countryCode}',
      'isOnline': _isOnline,
      'connectionType': _connectionType,
      'lifecycleState': _lifecycleState.name,
      'isAppInForeground': _isAppInForeground,
      'featureFlags': _featureFlags,
      'isLowPerformanceMode': _isLowPerformanceMode,
      'memoryUsage': _memoryUsage,
      'notificationsEnabled': _notificationsEnabled,
      'notificationsCount': _notifications.length,
      'unreadNotificationsCount': unreadNotificationsCount,
    };
  }
}

/// App notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final AppNotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    AppNotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }
}

/// App notification types
enum AppNotificationType {
  info,
  success,
  warning,
  error,
  system,
}

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../models/secure_comm_models.dart';

/// Simplified server communication widget that avoids layout assertion errors
class SimpleServerCommunicationWidget extends StatefulWidget {
  final SecureComm? secureComm;
  final VoidCallback? onRefresh;
  final Function(String serverUrl, String port, String deviceId, String deviceName)? onConnect;

  const SimpleServerCommunicationWidget({
    super.key,
    this.secureComm,
    this.onRefresh,
    this.onConnect,
  });

  @override
  State<SimpleServerCommunicationWidget> createState() => _SimpleServerCommunicationWidgetState();
}

enum CommunicationMode { http, mqtt }

class _SimpleServerCommunicationWidgetState extends State<SimpleServerCommunicationWidget> {
  // Connection state
  bool _isConnected = false;
  bool _isConnecting = false;
  String _connectionStatus = 'Disconnected';
  String _lastPingTime = 'Never';
  String _relayStatus = 'Unknown';
  String _lastStatusUpdate = 'Never';
  Map<String, dynamic>? _lastStatusData;
  List<String> _commandHistory = [];
  bool _isLoading = false;
  String? _lastError;

  // Communication mode
  CommunicationMode _communicationMode = CommunicationMode.http;

  // Controllers
  final _serverController = TextEditingController(text: 'http://10.161.80.12');
  final _portController = TextEditingController(text: '');
  final _deviceIdController = TextEditingController(text: 'terminal_001');
  final _deviceNameController = TextEditingController(text: 'Terminal Device');

  // Timers
  Timer? _pingTimer;
  Timer? _statusTimer;

  @override
  void initState() {
    super.initState();
    _updateConnectionStatus();
    _startPeriodicTasks();
  }

  @override
  void dispose() {
    _pingTimer?.cancel();
    _statusTimer?.cancel();
    _serverController.dispose();
    _portController.dispose();
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    super.dispose();
  }

  void _updateConnectionStatus() {
    if (mounted) {
      setState(() {
        _isConnected = widget.secureComm?.isAuthenticated ?? false;
        _connectionStatus = _isConnected ? 'Connected' : 'Disconnected';
      });
    }
  }

  void _startPeriodicTasks() {
    _pingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected && mounted) {
        _sendPing();
      }
    });

    _statusTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isConnected && mounted) {
        _getRelayStatus();
      }
    });
  }

  /// Parse server URL and extract host and port intelligently
  Map<String, String> _parseServerUrl(String serverInput, String portInput) {
    String serverUrl = serverInput;
    String port = portInput;

    // If server input contains http:// or https://, extract port if available
    if (serverInput.startsWith('http://') || serverInput.startsWith('https://')) {
      try {
        final uri = Uri.parse(serverInput);

        // If URL has explicit port, use it (override port input)
        if (uri.hasPort) {
          port = uri.port.toString();
        }
      } catch (e) {
        // If parsing fails, ignore
      }
    }

    // Return as-is, no automatic protocol or port addition
    return {'serverUrl': serverUrl, 'port': port};
  }

  Future<void> _connectToServer() async {
    if (_isConnecting) return;

    if (mounted) {
      setState(() {
        _isConnecting = true;
        _connectionStatus = 'Connecting...';
      });
    }

    try {
      final serverInput = _serverController.text.trim();
      final portInput = _portController.text.trim();
      final deviceId = _deviceIdController.text.trim();
      final deviceName = _deviceNameController.text.trim();

      // Parse server URL intelligently
      final parsed = _parseServerUrl(serverInput, portInput);
      final serverUrl = parsed['serverUrl']!;
      final port = parsed['port']!;

      if (widget.onConnect != null) {
        if (kDebugMode) {
          print('Original input: $serverInput:$portInput');
          print('Parsed to: $serverUrl:$port');
          print('Attempting to connect with device $deviceId');
        }

        await widget.onConnect!(serverUrl, port, deviceId, deviceName);

        // Wait a bit for connection to establish
        await Future.delayed(const Duration(milliseconds: 1000));

        if (widget.secureComm != null && widget.secureComm!.isAuthenticated) {
          if (mounted) {
            setState(() {
              _isConnected = true;
              _connectionStatus = 'Connected to $serverUrl:$port';
              _lastError = null; // Clear any previous errors
            });
          }
          _addToHistory('CONNECTION', 'Success', 'Connected to server successfully');
          _startPeriodicTasks();
          // Get initial status
          _getRelayStatus();
        } else {
          final errorMsg = widget.secureComm == null
              ? 'SecureComm is null - connection not initialized'
              : 'SecureComm not authenticated - connection failed';
          if (kDebugMode) {
            print('Connection failed: $errorMsg');
            print('SecureComm: ${widget.secureComm}');
            print('IsAuthenticated: ${widget.secureComm?.isAuthenticated}');
          }
          throw Exception(errorMsg);
        }
      } else {
        // Fallback: simulate connection for testing
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          setState(() {
            _isConnected = true;
            _connectionStatus = 'Connected to $serverUrl:$port (simulated)';
          });
        }
        _addToHistory('CONNECTION', 'Success', 'Connected to server successfully (simulated)');
        _startPeriodicTasks();
        // Get initial status
        _getRelayStatus();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectionStatus = 'Connection failed: ${e.toString()}';
          _lastError = e.toString();
        });
      }
      _addToHistory('CONNECTION', 'Failed', e.toString());
      if (kDebugMode) {
        print('Connection error: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  Future<void> _disconnectFromServer() async {
    if (mounted) {
      setState(() {
        _isConnecting = true;
        _connectionStatus = 'Disconnecting...';
      });
    }

    try {
      _pingTimer?.cancel();
      _statusTimer?.cancel();

      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {
          _isConnected = false;
          _connectionStatus = 'Disconnected';
          _lastPingTime = 'Never';
          _relayStatus = 'Unknown';
          _lastStatusUpdate = 'Never';
          _lastStatusData = null;
        });
      }

      _addToHistory('CONNECTION', 'Disconnected', 'Disconnected from server');
    } catch (e) {
      _addToHistory('CONNECTION', 'Error', 'Disconnect error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
      }
    }
  }

  Future<void> _sendPing() async {
    if (widget.secureComm == null || !_isConnected) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'ping',
        payload: {
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        if (mounted) {
          setState(() {
            _lastPingTime = DateTime.now().toString().substring(11, 19);
          });
        }
        _addToHistory('PING', 'Success', 'Ping sent successfully');
      } else {
        _addToHistory('PING', 'Failed', 'Ping failed');
      }
    } catch (e) {
      _addToHistory('PING', 'Error', e.toString());
    }
  }

  Future<void> _getRelayStatus() async {
    if (widget.secureComm == null || !_isConnected) return;

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'status_request',
        payload: {
          'component': 'relay',
        },
      );

      if (response.success && response.data != null) {
        final data = response.data!;
        String status = 'Unknown';

        // Try different possible status fields from server response
        if (data.containsKey('device_status')) {
          status = data['device_status'].toString();
        } else if (data.containsKey('relay_status')) {
          status = data['relay_status'].toString();
        } else if (data.containsKey('status')) {
          status = data['status'].toString();
        }

        if (mounted) {
          setState(() {
            _relayStatus = status;
            _lastStatusUpdate = DateTime.now().toString().substring(11, 19);
            _lastStatusData = data;
          });
        }

        _addToHistory('STATUS', 'Success', 'Status: $status');
      } else {
        _addToHistory('STATUS', 'Failed', 'Failed to get status');
      }
    } catch (e) {
      _addToHistory('STATUS', 'Error', e.toString());
      if (kDebugMode) {
        print('Error getting relay status: $e');
      }
    }
  }

  Future<void> _sendRelayCommand(String action) async {
    if (widget.secureComm == null || !_isConnected) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final response = await widget.secureComm!.sendMessage(
        type: 'relay_control',
        payload: {
          'action': action,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        _addToHistory('RELAY_CONTROL', 'Success', 'Relay $action command sent');
        // Update relay status after command
        await _getRelayStatus();
      } else {
        _addToHistory('RELAY_CONTROL', 'Failed', 'Relay command failed');
      }
    } catch (e) {
      _addToHistory('RELAY_CONTROL', 'Error', e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addToHistory(String command, String status, String details) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    final entry = '[$timestamp] $command: $status - $details';

    if (mounted) {
      setState(() {
        _commandHistory.insert(0, entry);
        if (_commandHistory.length > 20) {
          _commandHistory.removeLast();
        }
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'on':
      case 'active':
      case 'enabled':
        return Colors.green;
      case 'off':
      case 'inactive':
      case 'disabled':
        return Colors.red;
      case 'unknown':
      case 'error':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('SimpleServerCommunicationWidget.build() called');
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(8),
              color: kDebugMode ? Colors.blue.withValues(alpha: 0.1) : null,
              child: Row(
                children: [
                  Icon(
                    Icons.router,
                    color: _isConnected ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Server Communication',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  if (kDebugMode)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      color: Colors.orange,
                      child: const Text(
                        'DEBUG',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Connection Status
            Text(
              'Status: $_connectionStatus',
              style: TextStyle(
                color: _isConnected ? const Color.fromARGB(255, 109, 133, 111) : Colors.red.shade700,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Last Ping: $_lastPingTime',
              style: const TextStyle(color: Colors.black87, fontSize: 14),
            ),
            Row(
              children: [
                Text(
                  'Relay Status: ',
                  style: const TextStyle(color: Colors.black87, fontSize: 14),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_relayStatus),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _relayStatus.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            Text(
              'Last Status Update: $_lastStatusUpdate',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
            if (_lastStatusData != null && _lastStatusData!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Device Details:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 4),
                    ..._lastStatusData!.entries.map((entry) => Text(
                      '${entry.key}: ${entry.value}',
                      style: const TextStyle(fontSize: 11, color: Colors.black87),
                    )),
                  ],
                ),
              ),
            ],
            if (_lastError != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade200),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Error: $_lastError',
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),

            // Server Configuration (when not connected)
            if (!_isConnected) ...[
              const Text(
                'Server Configuration',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              _buildCommunicationModeSelector(),
              const SizedBox(height: 8),
              _buildSimpleInput('Server Address', _serverController, 'localhost or https://api.example.com'),
              const SizedBox(height: 8),
              _buildSimpleInput('Port', _portController, '3000 (leave empty for domains)'),
              const SizedBox(height: 8),
              _buildSimpleInput('Device ID', _deviceIdController, 'terminal_001'),
              const SizedBox(height: 8),
              _buildSimpleInput('Device Name', _deviceNameController, 'Terminal Device'),
              const SizedBox(height: 12),
              _buildSimpleButton(
                _isConnecting ? 'Connecting...' : 'Connect',
                _isConnecting ? null : _connectToServer,
                Colors.blue,
              ),
              const SizedBox(height: 16),
            ],

            // Connection Controls (when connected)
            if (_isConnected) ...[
              _buildSimpleButton('Disconnect', _disconnectFromServer, Colors.red),
              const SizedBox(height: 16),
            ],

            // Command Buttons
            if (_isConnected) ...[
              const Text(
                'Quick Commands',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              // Control buttons row
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleButton('Relay ON', _isLoading ? null : () => _sendRelayCommand('on'), Colors.green),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSimpleButton('Relay OFF', _isLoading ? null : () => _sendRelayCommand('off'), Colors.red),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Utility buttons row
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleButton('Ping', _isLoading ? null : _sendPing, Colors.blue),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSimpleButton('Toggle', _isLoading ? null : () => _sendRelayCommand('toggle'), Colors.orange),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSimpleButton('Refresh Status', _isLoading ? null : _getRelayStatus, Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Command History
            const Text(
              'Command History',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
              ),
              child: _commandHistory.isEmpty
                  ? const Center(
                      child: Text(
                        'No commands sent yet',
                        style: TextStyle(color: Colors.black54),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _commandHistory.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _commandHistory[index],
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                              color: Colors.black87,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build communication mode selector
  Widget _buildCommunicationModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Communication Mode',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _communicationMode = CommunicationMode.http;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: _communicationMode == CommunicationMode.http
                          ? Colors.blue.shade50
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                      border: _communicationMode == CommunicationMode.http
                          ? Border.all(color: Colors.blue.shade300)
                          : null,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.http,
                          color: _communicationMode == CommunicationMode.http
                              ? Colors.blue.shade700
                              : Colors.grey.shade600,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'HTTP',
                          style: TextStyle(
                            color: _communicationMode == CommunicationMode.http
                                ? Colors.blue.shade700
                                : Colors.grey.shade600,
                            fontWeight: _communicationMode == CommunicationMode.http
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _communicationMode = CommunicationMode.mqtt;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: _communicationMode == CommunicationMode.mqtt
                          ? Colors.orange.shade50
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                      border: _communicationMode == CommunicationMode.mqtt
                          ? Border.all(color: Colors.orange.shade300)
                          : null,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.router,
                          color: _communicationMode == CommunicationMode.mqtt
                              ? Colors.orange.shade700
                              : Colors.grey.shade600,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'MQTT',
                          style: TextStyle(
                            color: _communicationMode == CommunicationMode.mqtt
                                ? Colors.orange.shade700
                                : Colors.grey.shade600,
                            fontWeight: _communicationMode == CommunicationMode.mqtt
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        if (_communicationMode == CommunicationMode.mqtt)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'MQTT mode coming soon',
              style: TextStyle(
                color: Colors.orange.shade600,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSimpleInput(String label, TextEditingController controller, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.black87,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            color: Colors.grey.withValues(alpha: 0.1),
          ),
          child: TextField(
            controller: controller,
            style: const TextStyle(color: Colors.black87),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: const TextStyle(color: Colors.black54),
              border: InputBorder.none,
              isDense: true,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleButton(String text, VoidCallback? onPressed, Color color) {
    final isDisabled = onPressed == null;
    final buttonColor = isDisabled ? Colors.grey : color;
    final textColor = _getTextColorForBackground(buttonColor);

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Color _getTextColorForBackground(Color backgroundColor) {
    // Calculate luminance to determine if we need dark or light text
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
}

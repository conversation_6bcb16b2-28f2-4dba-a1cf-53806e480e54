import 'package:dartz/dartz.dart';
import '../../entities/user/user.dart';
import '../../repositories/user_repository.dart';
import '../../../core/errors/failures.dart';

/// Use case for getting users with pagination and filtering
class GetUsersUseCase {
  final UserRepository repository;

  GetUsersUseCase(this.repository);

  Future<Either<Failure, List<User>>> call(GetUsersParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    // Call repository to get users
    return await repository.getUsers(
      page: params.page,
      limit: params.limit,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection,
      search: params.search,
      unitId: params.unitId,
      memberRoleId: params.memberRoleId,
    );
  }

  ValidationFailure? _validateParams(GetUsersParams params) {
    final errors = <String, List<String>>{};

    // Validate page
    if (params.page < 1) {
      errors['page'] = ['Page must be greater than 0'];
    }

    // Validate limit
    if (params.limit < 1) {
      errors['limit'] = ['Limit must be greater than 0'];
    }

    if (params.limit > 100) {
      errors['limit'] = ['Limit cannot exceed 100'];
    }

    // Validate sort direction
    if (params.sortDirection != null) {
      final validDirections = ['asc', 'desc'];
      if (!validDirections.contains(params.sortDirection!.toLowerCase())) {
        errors['sortDirection'] = ['Sort direction must be either "asc" or "desc"'];
      }
    }

    // Validate sort by
    if (params.sortBy != null) {
      final validSortFields = [
        'name',
        'username',
        'email',
        'created_at',
        'updated_at',
      ];
      if (!validSortFields.contains(params.sortBy!.toLowerCase())) {
        errors['sortBy'] = [
          'Sort field must be one of: ${validSortFields.join(', ')}'
        ];
      }
    }

    if (errors.isNotEmpty) {
      return ValidationFailure(
        'Invalid parameters',
        fieldErrors: errors,
      );
    }

    return null;
  }
}

/// Parameters for GetUsersUseCase
class GetUsersParams {
  final int page;
  final int limit;
  final String? sortBy;
  final String? sortDirection;
  final String? search;
  final String? unitId;
  final String? memberRoleId;

  const GetUsersParams({
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortDirection,
    this.search,
    this.unitId,
    this.memberRoleId,
  });

  GetUsersParams copyWith({
    int? page,
    int? limit,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? unitId,
    String? memberRoleId,
  }) {
    return GetUsersParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortDirection: sortDirection ?? this.sortDirection,
      search: search ?? this.search,
      unitId: unitId ?? this.unitId,
      memberRoleId: memberRoleId ?? this.memberRoleId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GetUsersParams &&
        other.page == page &&
        other.limit == limit &&
        other.sortBy == sortBy &&
        other.sortDirection == sortDirection &&
        other.search == search &&
        other.unitId == unitId &&
        other.memberRoleId == memberRoleId;
  }

  @override
  int get hashCode {
    return page.hashCode ^
        limit.hashCode ^
        sortBy.hashCode ^
        sortDirection.hashCode ^
        search.hashCode ^
        unitId.hashCode ^
        memberRoleId.hashCode;
  }

  @override
  String toString() {
    return 'GetUsersParams(page: $page, limit: $limit, sortBy: $sortBy, sortDirection: $sortDirection, search: $search, unitId: $unitId, memberRoleId: $memberRoleId)';
  }
}

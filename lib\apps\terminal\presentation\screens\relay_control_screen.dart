import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/relay_control_provider.dart';
import '../../../../shared/core/config/configuration_manager.dart';
import '../../../../shared/core/config/relay_config_parameters.dart';
import '../../../../packages/relay_controller/lib/relay_controller.dart';

/// Relay Control Screen for Terminal App
/// 
/// This screen provides a comprehensive interface for controlling and monitoring
/// USB-TTL relay devices in the terminal application.
class RelayControlScreen extends StatefulWidget {
  const RelayControlScreen({super.key});

  @override
  State<RelayControlScreen> createState() => _RelayControlScreenState();
}

class _RelayControlScreenState extends State<RelayControlScreen> {
  late RelayControlProvider _relayProvider;
  final TextEditingController _rawCommandController = TextEditingController();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _relayProvider = RelayControlProvider.instance;
    _initializeRelayProvider();
  }

  Future<void> _initializeRelayProvider() async {
    try {
      await _relayProvider.initialize();
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khởi tạo relay: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _rawCommandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Điều khiển Relay'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _handleReconnect,
            tooltip: 'Kết nối lại',
          ),
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _handleServerRegistration,
            tooltip: 'Đăng ký với server',
          ),
        ],
      ),
      body: !_isInitialized
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Đang khởi tạo điều khiển relay...'),
                ],
              ),
            )
          : ChangeNotifierProvider.value(
              value: _relayProvider,
              child: Consumer<RelayControlProvider>(
                builder: (context, provider, child) {
                  if (!provider.isEnabled) {
                    return const _RelayDisabledView();
                  }

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildStatusSection(provider),
                        const SizedBox(height: 20),
                        _buildRelayControlSection(provider),
                        const SizedBox(height: 20),
                        _buildQuickActionsSection(provider),
                        const SizedBox(height: 20),
                        _buildRawCommandSection(provider),
                        const SizedBox(height: 20),
                        _buildConfigurationSection(),
                      ],
                    ),
                  );
                },
              ),
            ),
    );
  }

  Widget _buildStatusSection(RelayControlProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Trạng thái thiết bị',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  provider.isConnected ? Icons.check_circle : Icons.error,
                  color: provider.isConnected ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  provider.isConnected ? 'Đã kết nối' : 'Chưa kết nối',
                  style: TextStyle(
                    color: provider.isConnected ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (provider.deviceConfig != null) ...[
              const SizedBox(height: 8),
              Text('Thiết bị: ${provider.deviceConfig!.deviceName}'),
              Text('ID: ${provider.deviceConfig!.deviceId}'),
              Text('Số relay: ${provider.deviceConfig!.relayCount}'),
            ],
            if (provider.lastError != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade700, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Lỗi: ${provider.lastError}',
                        style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRelayControlSection(RelayControlProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Điều khiển Relay',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...provider.relayStatuses.map((status) => _buildRelayControl(status, provider)),
          ],
        ),
      ),
    );
  }

  Widget _buildRelayControl(RelayStatus status, RelayControlProvider provider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: status.isEnabled ? null : Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  '${status.name} (Relay ${status.index})',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: status.isEnabled ? null : Colors.grey,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getRelayStateColor(status.state),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getRelayStateText(status.state),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (status.isEnabled) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _controlRelay(provider, status.index, RelayAction.on),
                    icon: const Icon(Icons.power, size: 16),
                    label: const Text('BẬT'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _controlRelay(provider, status.index, RelayAction.off),
                    icon: const Icon(Icons.power_off, size: 16),
                    label: const Text('TẮT'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _controlRelay(provider, status.index, RelayAction.toggle),
                    icon: const Icon(Icons.sync, size: 16),
                    label: const Text('ĐẢO'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _controlRelayTimed(provider, status.index),
                  icon: const Icon(Icons.timer, size: 16),
                  label: const Text('TIMED'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ] else ...[
            const SizedBox(height: 8),
            const Text(
              'Relay này đã bị tắt trong cấu hình',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(RelayControlProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thao tác nhanh',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _controlAllRelays(provider, RelayAction.on),
                    icon: const Icon(Icons.power),
                    label: const Text('BẬT TẤT CẢ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _controlAllRelays(provider, RelayAction.off),
                    icon: const Icon(Icons.power_off),
                    label: const Text('TẮT TẤT CẢ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _triggerDoorUnlock(provider),
                    icon: const Icon(Icons.lock_open),
                    label: const Text('MỞ CỬA'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _triggerAccessGranted(provider),
                    icon: const Icon(Icons.check_circle),
                    label: const Text('THÔNG BÁO THÀNH CÔNG'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRawCommandSection(RelayControlProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Lệnh tùy chỉnh',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
                                    'Gửi lệnh trực tiếp đến thiết bị relay (ví dụ: R0:1, R1:TOGGLE, ALL:0)',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _rawCommandController,
                    decoration: const InputDecoration(
                      labelText: 'Lệnh Relay',
                      hintText: 'R0:1',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () => _sendRawCommand(provider),
                  icon: const Icon(Icons.send),
                  label: const Text('GỬI'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cấu hình hiện tại',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildConfigItem('Relay được bật', ConfigurationManager.instance.getValue<bool>(RelayConfigKeys.relayEnabled).toString()),
            _buildConfigItem('ID thiết bị', ConfigurationManager.instance.getValue<String>(RelayConfigKeys.relayDeviceId)),
            _buildConfigItem('Tên thiết bị', ConfigurationManager.instance.getValue<String>(RelayConfigKeys.relayDeviceName)),
            _buildConfigItem('Số relay', ConfigurationManager.instance.getValue<int>(RelayConfigKeys.relayCount).toString()),
            _buildConfigItem('Baud rate', ConfigurationManager.instance.getValue<int>(RelayConfigKeys.relayBaudRate).toString()),
            _buildConfigItem('Tự động kết nối', ConfigurationManager.instance.getValue<bool>(RelayConfigKeys.relayAutoConnect).toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Color _getRelayStateColor(RelayState state) {
    switch (state) {
      case RelayState.on:
        return Colors.green;
      case RelayState.off:
        return Colors.grey;
      case RelayState.unknown:
        return Colors.orange;
    }
  }

  String _getRelayStateText(RelayState state) {
    switch (state) {
      case RelayState.on:
        return 'BẬT';
      case RelayState.off:
        return 'TẮT';
      case RelayState.unknown:
        return 'KHÔNG RÕ';
    }
  }

  Future<void> _controlRelay(RelayControlProvider provider, int relayIndex, RelayAction action) async {
    try {
      await provider.controlRelay(relayIndex, action);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Relay $relayIndex ${action.name} thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi điều khiển relay: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _controlRelayTimed(RelayControlProvider provider, int relayIndex) async {
    try {
      await provider.controlRelayTimed(relayIndex);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Relay $relayIndex bật theo thời gian thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi điều khiển relay theo thời gian: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _controlAllRelays(RelayControlProvider provider, RelayAction action) async {
    try {
      await provider.controlAllRelays(action);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Tất cả relay ${action.name} thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi điều khiển tất cả relay: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _triggerDoorUnlock(RelayControlProvider provider) async {
    try {
      await provider.triggerDoorUnlock();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Mở cửa thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi mở cửa: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _triggerAccessGranted(RelayControlProvider provider) async {
    try {
      await provider.triggerAccessGranted();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Thông báo thành công đã được kích hoạt'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi thông báo thành công: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendRawCommand(RelayControlProvider provider) async {
    final command = _rawCommandController.text.trim();
    if (command.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng nhập lệnh'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      await provider.sendRawCommand(command);
      _rawCommandController.clear();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Gửi lệnh "$command" thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi gửi lệnh: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _handleReconnect() async {
    try {
      await _relayProvider.reconnect();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Kết nối lại thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi kết nối lại: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _handleServerRegistration() async {
    try {
      await _relayProvider.registerWithServer();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đăng ký với server thành công'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi đăng ký với server: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

/// Widget displayed when relay control is disabled
class _RelayDisabledView extends StatelessWidget {
  const _RelayDisabledView();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.power_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Điều khiển Relay đã bị tắt',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vui lòng bật tính năng relay trong cài đặt',
            style: TextStyle(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to settings or show settings dialog
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Vui lòng liên hệ quản trị viên để bật tính năng relay'),
                ),
              );
            },
            icon: const Icon(Icons.settings),
            label: const Text('Cài đặt'),
          ),
        ],
      ),
    );
  }
} 
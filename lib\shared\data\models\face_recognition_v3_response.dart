import 'dart:convert';

/// Face recognition response model for API v3.1
class FaceRecognitionV3Response {
  final String code;
  final bool success;
  final int statusCode;
  final String message;
  final FaceRecognitionV3Data? data;

  const FaceRecognitionV3Response({
    required this.code,
    required this.success,
    required this.statusCode,
    required this.message,
    this.data,
  });

  factory FaceRecognitionV3Response.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionV3Response(
      code: json['code'] ?? '',
      success: json['success'] ?? false,
      statusCode: json['statusCode'] ?? 0,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? FaceRecognitionV3Data.fromJson(json['data']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'success': success,
      'statusCode': statusCode,
      'message': message,
      'data': data?.toJson(),
    };
  }

  /// Check if recognition was successful
  bool get isSuccess => success && code == 'SUCCESS';

  /// Check if user was recognized (not unknown)
  bool get isUserRecognized => 
      data?.recognition.recognizeId != 'unknown' && 
      data?.recognition.recognizeId.isNotEmpty == true;

  /// Get confidence score
  double get confidence => data?.recognition.confidence ?? 0.0;

  /// Get recognized user name
  String get recognizedUserName => 
      isUserRecognized ? (data?.recognition.recognizeName ?? 'Unknown') : 'Unknown';

  /// Get recognized user ID
  String get recognizedUserId => 
      isUserRecognized ? (data?.recognition.recognizeId ?? '') : '';

  @override
  String toString() {
    return 'FaceRecognitionV3Response('
        'code: $code, '
        'success: $success, '
        'message: $message, '
        'recognized: $isUserRecognized, '
        'confidence: ${(confidence * 100).toStringAsFixed(1)}%'
        ')';
  }
}

/// Face recognition data model
class FaceRecognitionV3Data {
  final String deviceId;
  final String cameraId;
  final String eventId;
  final String trackingId;
  final FaceRecognitionResult recognition;
  final String faceReid;
  final String messageId;
  final String edgeDeviceType;
  final String srcId;
  final String updatedAt;
  final String debugInfo;
  final String processingTimestamp;
  final FaceRecognitionUser user;

  const FaceRecognitionV3Data({
    required this.deviceId,
    required this.cameraId,
    required this.eventId,
    required this.trackingId,
    required this.recognition,
    required this.faceReid,
    required this.messageId,
    required this.edgeDeviceType,
    required this.srcId,
    required this.updatedAt,
    required this.debugInfo,
    required this.processingTimestamp,
    required this.user,
  });

  factory FaceRecognitionV3Data.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionV3Data(
      deviceId: json['device_id'] ?? '',
      cameraId: json['camera_id'] ?? '',
      eventId: json['event_id'] ?? '',
      trackingId: json['tracking_id'] ?? '',
      recognition: FaceRecognitionResult.fromJson(json['recognition'] ?? {}),
      faceReid: json['face_reid'] ?? '',
      messageId: json['message_id'] ?? '',
      edgeDeviceType: json['edge_device_type'] ?? '',
      srcId: json['src_id'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      debugInfo: json['debug_info'] ?? '',
      processingTimestamp: json['processing_timestamp'] ?? '',
      user: FaceRecognitionUser.fromJson(json['user'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'camera_id': cameraId,
      'event_id': eventId,
      'tracking_id': trackingId,
      'recognition': recognition.toJson(),
      'face_reid': faceReid,
      'message_id': messageId,
      'edge_device_type': edgeDeviceType,
      'src_id': srcId,
      'updated_at': updatedAt,
      'debug_info': debugInfo,
      'processing_timestamp': processingTimestamp,
      'user': user.toJson(),
    };
  }
}

/// Face recognition result model
class FaceRecognitionResult {
  final String recognizeId;
  final String recognizeName;
  final bool qualityPass;
  final bool maskDetected;
  final double confidence;

  const FaceRecognitionResult({
    required this.recognizeId,
    required this.recognizeName,
    required this.qualityPass,
    required this.maskDetected,
    required this.confidence,
  });

  factory FaceRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionResult(
      recognizeId: json['recognize_id'] ?? '',
      recognizeName: json['recognize_name'] ?? '',
      qualityPass: json['quality_pass'] ?? false,
      maskDetected: json['mask_detected'] ?? false,
      confidence: (json['confidence'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recognize_id': recognizeId,
      'recognize_name': recognizeName,
      'quality_pass': qualityPass,
      'mask_detected': maskDetected,
      'confidence': confidence,
    };
  }

  /// Check if this is a recognized user (not unknown)
  bool get isRecognized => recognizeId != 'unknown' && recognizeId.isNotEmpty;

  /// Get confidence as percentage
  double get confidencePercentage => confidence * 100;
}

/// Face recognition user model
class FaceRecognitionUser {
  final String id;
  final String name;

  const FaceRecognitionUser({
    required this.id,
    required this.name,
  });

  factory FaceRecognitionUser.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionUser(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
    };
  }

  bool get isEmpty => id.isEmpty && name.isEmpty;
}

/// Debug information parser for detailed analysis
class FaceRecognitionDebugInfo {
  final double detectConf;
  final int detectDuration;
  final int detectTimepoint;
  final int featureExDuration;
  final String fvHash;
  final int headposeDuration;
  final double headposePitch;
  final double headposeRoll;
  final double headposeYaw;
  final double maskConf;
  final int maskDuration;
  final double quality;
  final int qualityDuration;
  final String recogTimepoint;
  final double recognizeConf;
  final double reidConf;
  final int reqExDuration;
  final int searchDuration;

  const FaceRecognitionDebugInfo({
    required this.detectConf,
    required this.detectDuration,
    required this.detectTimepoint,
    required this.featureExDuration,
    required this.fvHash,
    required this.headposeDuration,
    required this.headposePitch,
    required this.headposeRoll,
    required this.headposeYaw,
    required this.maskConf,
    required this.maskDuration,
    required this.quality,
    required this.qualityDuration,
    required this.recogTimepoint,
    required this.recognizeConf,
    required this.reidConf,
    required this.reqExDuration,
    required this.searchDuration,
  });

  factory FaceRecognitionDebugInfo.fromJson(String debugInfoJson) {
    try {
      final Map<String, dynamic> json = jsonDecode(debugInfoJson);
      return FaceRecognitionDebugInfo(
        detectConf: (json['detect_conf'] ?? 0.0).toDouble(),
        detectDuration: json['detect_duration'] ?? 0,
        detectTimepoint: json['detect_timepoint'] ?? 0,
        featureExDuration: json['feature_ex_duration'] ?? 0,
        fvHash: json['fv_hash'] ?? '',
        headposeDuration: json['headpose_duration'] ?? 0,
        headposePitch: (json['headpose_pitch'] ?? 0.0).toDouble(),
        headposeRoll: (json['headpose_roll'] ?? 0.0).toDouble(),
        headposeYaw: (json['headpose_yaw'] ?? 0.0).toDouble(),
        maskConf: (json['mask_conf'] ?? 0.0).toDouble(),
        maskDuration: json['mask_duration'] ?? 0,
        quality: (json['quality'] ?? 0.0).toDouble(),
        qualityDuration: json['quality_duration'] ?? 0,
        recogTimepoint: json['recog_timepoint'] ?? '',
        recognizeConf: (json['recognize_conf'] ?? 0.0).toDouble(),
        reidConf: (json['reid_conf'] ?? 0.0).toDouble(),
        reqExDuration: json['req_ex_duration'] ?? 0,
        searchDuration: json['search_duration'] ?? 0,
      );
    } catch (e) {
      // Return default values if parsing fails
      return const FaceRecognitionDebugInfo(
        detectConf: 0.0,
        detectDuration: 0,
        detectTimepoint: 0,
        featureExDuration: 0,
        fvHash: '',
        headposeDuration: 0,
        headposePitch: 0.0,
        headposeRoll: 0.0,
        headposeYaw: 0.0,
        maskConf: 0.0,
        maskDuration: 0,
        quality: 0.0,
        qualityDuration: 0,
        recogTimepoint: '',
        recognizeConf: 0.0,
        reidConf: 0.0,
        reqExDuration: 0,
        searchDuration: 0,
      );
    }
  }

  /// Get total processing time in milliseconds
  int get totalProcessingTime => 
      detectDuration + featureExDuration + headposeDuration + 
      maskDuration + qualityDuration + reqExDuration + searchDuration;
}

#!/bin/bash

# Script to run or build the terminal app
# Usage: ./scripts/run_terminal.sh [debug|release|build]

# Default action
ACTION="${1:-debug}"

echo "🖥️  C-Face Terminal App - $ACTION"
echo "=================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

case "$ACTION" in
    "build")
        echo "🔨 Building terminal app APK..."
        echo "🖥️  Target: lib/apps/terminal/main_terminal.dart"
        echo "🎯 Optimized for: Kiosk/Terminal devices"
        echo ""

        flutter build apk --target lib/apps/terminal/main_terminal.dart --flavor terminal

        if [ $? -eq 0 ]; then
            echo ""
            echo "✅ Build successful!"
            echo "📦 APK location: build/app/outputs/flutter-apk/app-terminal-release.apk"
        else
            echo ""
            echo "❌ Build failed!"
            exit 1
        fi
        ;;
    "debug")
        # Check for connected devices
        echo "📱 Checking for connected devices..."
        flutter devices

        echo "🏃 Starting terminal app in debug mode..."
        echo "🖥️  Target: lib/apps/terminal/main_terminal.dart"
        echo "🔧 Mode: Debug (hot reload enabled)"
        echo "🎯 Optimized for: Kiosk/Terminal devices"
        echo ""

        # Try Android first (default)
        echo "🤖 Attempting to run on Android device..."
        echo "Press 'r' to hot reload, 'R' to hot restart, 'q' to quit"
        echo ""

        flutter run --target lib/apps/terminal/main_terminal.dart --debug --flavor terminal


        # If Android fails, suggest web alternative
        if [ $? -ne 0 ]; then
            echo ""
            echo "❌ Android run failed. You can try running on web instead:"
            echo "   flutter run --target lib/apps/terminal/main_terminal.dart -d chrome --flavor terminal"
            echo ""
            echo "💡 To fix Android issues:"
            echo "   1. Check connected devices: flutter devices"
            echo "   2. Check Android setup: flutter doctor"
            echo "   3. Try cleaning: flutter clean && flutter pub get"
        fi
        ;;
    "release")
        # Check for connected devices
        echo "📱 Checking for connected devices..."
        flutter devices

        echo "🏃 Starting terminal app in release mode..."
        echo "🖥️  Target: lib/apps/terminal/main_terminal.dart"
        echo "🔧 Mode: Release (optimized)"
        echo "🎯 Optimized for: Kiosk/Terminal devices"
        echo ""

        flutter run --target lib/apps/terminal/main_terminal.dart --release --flavor terminal
        ;;
    *)
        echo "❌ Invalid action: $ACTION"
        echo "Usage: $0 [debug|release|build]"
        echo ""
        echo "Actions:"
        echo "  debug    - Run in debug mode (default)"
        echo "  release  - Run in release mode"
        echo "  build    - Build APK"
        exit 1
        ;;
esac

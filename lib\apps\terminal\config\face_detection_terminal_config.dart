import 'package:flutter/foundation.dart';

import '../../../packages/face_recognition/src/config/face_detection_config.dart';

/// Terminal-specific face detection configuration
/// Optimized for Telpo F8 and similar terminal devices
class FaceDetectionTerminalConfig {
  
  /// Get the recommended face detection configuration for terminal
  static FaceDetectionConfig getTerminalConfig({
    bool useMediaPipe = true,
    bool enableLandmarks = true,
    bool enableFallback = true,
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    if (useMediaPipe) {
      return FaceDetectionConfig.forTerminal(
        useMediaPipe: true,
        confidenceThreshold: confidenceThreshold ?? 0.6,
        maxFaces: maxFaces ?? 3,
        enableLandmarks: enableLandmarks,
      );
    } else {
      // Fallback to ML Kit for terminal (not recommended but supported)
      return FaceDetectionConfig(
        primaryEngine: FaceDetectionEngineType.mlKit,
        fallbackEngine: enableFallback ? FaceDetectionEngineType.mediaPipe : null,
        confidenceThreshold: confidenceThreshold ?? 0.5,
        maxFaces: maxFaces ?? 5,
        enableLandmarks: enableLandmarks,
        enableTracking: true,
        performanceMode: PerformanceMode.balanced,
        enableGPUAcceleration: true,
        enableFallback: enableFallback,
        detectionTimeoutMs: 4000,
      );
    }
  }
  
  /// Get hybrid configuration with MediaPipe as primary and ML Kit as fallback
  static FaceDetectionConfig getHybridConfig({
    double? confidenceThreshold,
    int? maxFaces,
    bool? enableLandmarks,
  }) {
    return FaceDetectionConfig.hybridMediaPipe(
      confidenceThreshold: confidenceThreshold ?? 0.5,
      maxFaces: maxFaces ?? 3,
      enableLandmarks: enableLandmarks ?? true,
    );
  }
  
  /// Get performance-optimized configuration for high-throughput scenarios
  static FaceDetectionConfig getPerformanceConfig({
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    return FaceDetectionConfig(
      primaryEngine: FaceDetectionEngineType.mediaPipe,
      fallbackEngine: FaceDetectionEngineType.ultraFace,
      confidenceThreshold: confidenceThreshold ?? 0.7, // Higher threshold for speed
      maxFaces: maxFaces ?? 2, // Fewer faces for speed
      enableLandmarks: false, // Disable landmarks for speed
      enableTracking: false, // Disable tracking for speed
      performanceMode: PerformanceMode.maxPerformance,
      enableGPUAcceleration: true,
      enableFallback: true,
      detectionTimeoutMs: 2000, // Shorter timeout
    );
  }
  
  /// Get accuracy-optimized configuration for enrollment scenarios
  static FaceDetectionConfig getAccuracyConfig({
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    return FaceDetectionConfig(
      primaryEngine: FaceDetectionEngineType.hybrid,
      fallbackEngine: FaceDetectionEngineType.mlKit,
      confidenceThreshold: confidenceThreshold ?? 0.8, // Higher threshold for accuracy
      maxFaces: maxFaces ?? 1, // Single face for enrollment
      enableLandmarks: true, // Enable landmarks for better quality
      enableTracking: true, // Enable tracking for stability
      performanceMode: PerformanceMode.maxAccuracy,
      enableGPUAcceleration: true,
      enableFallback: true,
      detectionTimeoutMs: 8000, // Longer timeout for accuracy
    );
  }
  
  /// Get configuration based on terminal hardware capabilities
  static FaceDetectionConfig getConfigForHardware({
    required TerminalHardwareType hardwareType,
    bool? useMediaPipe,
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    switch (hardwareType) {
      case TerminalHardwareType.telpoF8:
        return getTerminalConfig(
          useMediaPipe: useMediaPipe ?? true,
          confidenceThreshold: confidenceThreshold ?? 0.6,
          maxFaces: maxFaces ?? 3,
          enableLandmarks: true,
          enableFallback: true,
        );
        
      case TerminalHardwareType.highEnd:
        return getAccuracyConfig(
          confidenceThreshold: confidenceThreshold ?? 0.7,
          maxFaces: maxFaces ?? 5,
        );
        
      case TerminalHardwareType.lowEnd:
        return getPerformanceConfig(
          confidenceThreshold: confidenceThreshold ?? 0.8,
          maxFaces: maxFaces ?? 1,
        );
        
      case TerminalHardwareType.embedded:
        return FaceDetectionConfig(
          primaryEngine: FaceDetectionEngineType.ultraFace,
          fallbackEngine: FaceDetectionEngineType.mediaPipe,
          confidenceThreshold: confidenceThreshold ?? 0.7,
          maxFaces: maxFaces ?? 2,
          enableLandmarks: false,
          enableTracking: false,
          performanceMode: PerformanceMode.powerSaver,
          enableGPUAcceleration: false, // May not be available
          enableFallback: true,
          detectionTimeoutMs: 3000,
        );
    }
  }
  
  /// Get configuration based on use case
  static FaceDetectionConfig getConfigForUseCase({
    required TerminalUseCase useCase,
    bool? useMediaPipe,
    double? confidenceThreshold,
    int? maxFaces,
  }) {
    switch (useCase) {
      case TerminalUseCase.attendance:
        return getTerminalConfig(
          useMediaPipe: useMediaPipe ?? true,
          confidenceThreshold: confidenceThreshold ?? 0.6,
          maxFaces: maxFaces ?? 3,
          enableLandmarks: true,
          enableFallback: true,
        );
        
      case TerminalUseCase.accessControl:
        return getAccuracyConfig(
          confidenceThreshold: confidenceThreshold ?? 0.8,
          maxFaces: maxFaces ?? 1,
        );
        
      case TerminalUseCase.enrollment:
        return getAccuracyConfig(
          confidenceThreshold: confidenceThreshold ?? 0.9,
          maxFaces: maxFaces ?? 1,
        );
        
      case TerminalUseCase.monitoring:
        return getPerformanceConfig(
          confidenceThreshold: confidenceThreshold ?? 0.5,
          maxFaces: maxFaces ?? 5,
        );
        
      case TerminalUseCase.kiosk:
        return getTerminalConfig(
          useMediaPipe: useMediaPipe ?? true,
          confidenceThreshold: confidenceThreshold ?? 0.5,
          maxFaces: maxFaces ?? 3,
          enableLandmarks: false, // Not needed for kiosk
          enableFallback: true,
        );
    }
  }
  
  /// Validate configuration for terminal environment
  static bool validateTerminalConfig(FaceDetectionConfig config) {
    // Check if configuration is suitable for terminal
    final issues = <String>[];
    
    // Check confidence threshold
    if (config.confidenceThreshold < 0.3 || config.confidenceThreshold > 0.95) {
      issues.add('Confidence threshold should be between 0.3 and 0.95 for terminals');
    }
    
    // Check max faces
    if (config.maxFaces > 10) {
      issues.add('Max faces should not exceed 10 for terminal performance');
    }
    
    // Check timeout
    if (config.detectionTimeoutMs > 10000) {
      issues.add('Detection timeout should not exceed 10 seconds for terminals');
    }
    
    // Check engine compatibility
    if (config.primaryEngine == FaceDetectionEngineType.mlKit && 
        !config.enableFallback) {
      issues.add('ML Kit without fallback may not be optimal for terminals');
    }
    
    if (issues.isNotEmpty && kDebugMode) {
      print('⚠️ Terminal config validation issues:');
      for (final issue in issues) {
        print('  - $issue');
      }
    }
    
    return issues.isEmpty;
  }
  
  /// Get debug information about the configuration
  static Map<String, dynamic> getConfigDebugInfo(FaceDetectionConfig config) {
    return {
      'primaryEngine': config.primaryEngine.displayName,
      'fallbackEngine': config.fallbackEngine?.displayName,
      'confidenceThreshold': config.confidenceThreshold,
      'maxFaces': config.maxFaces,
      'enableLandmarks': config.enableLandmarks,
      'enableTracking': config.enableTracking,
      'performanceMode': config.performanceMode.description,
      'enableGPUAcceleration': config.enableGPUAcceleration,
      'enableFallback': config.enableFallback,
      'detectionTimeoutMs': config.detectionTimeoutMs,
      'isValidForTerminal': validateTerminalConfig(config),
      'recommendedForTerminal': config.primaryEngine == FaceDetectionEngineType.mediaPipe ||
                                config.primaryEngine == FaceDetectionEngineType.hybrid,
    };
  }
}

/// Terminal hardware types
enum TerminalHardwareType {
  /// Telpo F8 with RK3399 processor
  telpoF8,
  
  /// High-end terminal with powerful processor
  highEnd,
  
  /// Low-end terminal with limited resources
  lowEnd,
  
  /// Embedded device with minimal resources
  embedded,
}

/// Terminal use cases
enum TerminalUseCase {
  /// Employee attendance tracking
  attendance,
  
  /// Access control and security
  accessControl,
  
  /// Face enrollment and registration
  enrollment,
  
  /// Continuous monitoring
  monitoring,
  
  /// Interactive kiosk
  kiosk,
}

/// Extension for hardware type descriptions
extension TerminalHardwareTypeExtension on TerminalHardwareType {
  String get description {
    switch (this) {
      case TerminalHardwareType.telpoF8:
        return 'Telpo F8 with RK3399 processor and Mali-T860 GPU';
      case TerminalHardwareType.highEnd:
        return 'High-end terminal with powerful processor and dedicated GPU';
      case TerminalHardwareType.lowEnd:
        return 'Low-end terminal with limited CPU and memory resources';
      case TerminalHardwareType.embedded:
        return 'Embedded device with minimal resources and no GPU';
    }
  }
  
  bool get supportsGPU {
    switch (this) {
      case TerminalHardwareType.telpoF8:
      case TerminalHardwareType.highEnd:
        return true;
      case TerminalHardwareType.lowEnd:
      case TerminalHardwareType.embedded:
        return false;
    }
  }
  
  FaceDetectionEngineType get recommendedEngine {
    switch (this) {
      case TerminalHardwareType.telpoF8:
      case TerminalHardwareType.highEnd:
        return FaceDetectionEngineType.mediaPipe;
      case TerminalHardwareType.lowEnd:
        return FaceDetectionEngineType.ultraFace;
      case TerminalHardwareType.embedded:
        return FaceDetectionEngineType.ultraFace;
    }
  }
}

/// Extension for use case descriptions
extension TerminalUseCaseExtension on TerminalUseCase {
  String get description {
    switch (this) {
      case TerminalUseCase.attendance:
        return 'Employee attendance tracking with moderate accuracy requirements';
      case TerminalUseCase.accessControl:
        return 'Access control with high accuracy and security requirements';
      case TerminalUseCase.enrollment:
        return 'Face enrollment with maximum accuracy requirements';
      case TerminalUseCase.monitoring:
        return 'Continuous monitoring with performance priority';
      case TerminalUseCase.kiosk:
        return 'Interactive kiosk with balanced requirements';
    }
  }
  
  double get recommendedConfidence {
    switch (this) {
      case TerminalUseCase.attendance:
        return 0.6;
      case TerminalUseCase.accessControl:
        return 0.8;
      case TerminalUseCase.enrollment:
        return 0.9;
      case TerminalUseCase.monitoring:
        return 0.5;
      case TerminalUseCase.kiosk:
        return 0.5;
    }
  }
  
  int get recommendedMaxFaces {
    switch (this) {
      case TerminalUseCase.attendance:
        return 3;
      case TerminalUseCase.accessControl:
        return 1;
      case TerminalUseCase.enrollment:
        return 1;
      case TerminalUseCase.monitoring:
        return 5;
      case TerminalUseCase.kiosk:
        return 3;
    }
  }
}

# 🧪 Telpo F8 Integration Testing Plan

**Target Device**: Telpo F8 RK3399  
**Test Date**: 2025-01-25  
**APK Version**: app-terminal-debug.apk  
**Test Status**: 🔄 IN PROGRESS

## 📋 Pre-Test Checklist

### ✅ Build Verification
- [x] Terminal APK builds successfully
- [x] No compilation errors
- [x] APK size reasonable (~300MB)
- [x] All dependencies resolved

### 📱 Device Preparation
- [ ] Telpo F8 device available
- [ ] ADB connection established
- [ ] Previous app version uninstalled
- [ ] Device storage sufficient (>1GB free)
- [ ] Camera permissions granted

### 🔧 Test Environment
- [ ] Good lighting conditions
- [ ] Multiple test faces available
- [ ] Performance monitoring tools ready
- [ ] Log collection enabled

## 🧪 Test Scenarios

### 1. 📦 Installation & Launch Test
**Objective**: Verify app installs and launches correctly on Telpo F8

**Steps**:
```bash
# 1. Install APK
adb install -r build/app/outputs/flutter-apk/app-terminal-debug.apk

# 2. Launch app
adb shell am start -n com.ccam.terminal/com.ccam.terminal.MainActivity

# 3. Check logs
adb logcat | grep -E "(FaceDetection|Telpo|Hybrid|UltraFace)"
```

**Expected Results**:
- ✅ App installs without errors
- ✅ App launches successfully
- ✅ No crash on startup
- ✅ Face detection provider initializes

### 2. 🔍 Device Detection Test
**Objective**: Verify Telpo F8 device detection works correctly

**Steps**:
1. Launch app and navigate to face detection screen
2. Check logs for device detection messages
3. Verify hybrid system status

**Expected Results**:
```
🔍 Device detection: telpo f8
📱 Is Telpo F8: true
🚀 Initializing Hybrid Face Detection System for Telpo F8...
✅ Hybrid detection system initialized successfully
```

### 3. 📸 Camera Integration Test
**Objective**: Verify camera works with face detection

**Steps**:
1. Open camera/face detection screen
2. Point camera at face
3. Observe face detection overlay
4. Check detection accuracy

**Expected Results**:
- ✅ Camera preview displays correctly
- ✅ Face detection overlay appears
- ✅ Bounding boxes drawn around faces
- ✅ Confidence scores displayed

### 4. ⚡ Performance Benchmark Test
**Objective**: Measure performance improvements vs ML Kit

**Test Metrics**:
| Metric | ML Kit (Baseline) | Hybrid System (Target) | Actual | Status |
|--------|-------------------|------------------------|--------|--------|
| **FPS** | 15 | 45+ | ___ | ⏳ |
| **Memory** | 200MB+ | <150MB | ___MB | ⏳ |
| **CPU Usage** | 80%+ | <60% | __% | ⏳ |
| **Detection Latency** | 100ms+ | <50ms | ___ms | ⏳ |
| **Startup Time** | 5s | <3s | ___s | ⏳ |

**Performance Test Commands**:
```bash
# Monitor FPS
adb logcat | grep "FPS\|fps\|frame"

# Monitor memory usage
adb shell dumpsys meminfo com.ccam.terminal

# Monitor CPU usage
adb shell top | grep com.ccam.terminal

# Monitor temperature
adb shell cat /sys/class/thermal/thermal_zone*/temp
```

### 5. 🔄 Fallback Mechanism Test
**Objective**: Verify graceful fallback to ML Kit when hybrid system fails

**Steps**:
1. Simulate hybrid system failure (remove model files)
2. Restart app
3. Verify fallback to ML Kit
4. Restore model files and test recovery

**Expected Results**:
```
⚠️ Hybrid system failed, falling back to ML Kit: [error]
🔄 Using ML Kit Face Detection System
✅ Bộ phát hiện khuôn mặt đã được khởi tạo thành công
```

### 6. 🌡️ Thermal & Stability Test
**Objective**: Verify system stability under continuous operation

**Duration**: 30 minutes continuous operation

**Monitoring**:
- CPU temperature
- Memory leaks
- Frame rate consistency
- App crashes

**Expected Results**:
- ✅ Temperature stays below 85°C
- ✅ No memory leaks detected
- ✅ Consistent frame rate
- ✅ No crashes or freezes

## 📊 Test Results Template

### Test Execution Summary
**Date**: ___________  
**Tester**: ___________  
**Device**: Telpo F8 RK3399  
**App Version**: app-terminal-debug.apk

### Results Overview
| Test Category | Status | Notes |
|---------------|--------|-------|
| Installation | ✅/❌ | |
| Device Detection | ✅/❌ | |
| Camera Integration | ✅/❌ | |
| Performance | ✅/❌ | |
| Fallback Mechanism | ✅/❌ | |
| Thermal Stability | ✅/❌ | |

### Performance Metrics
| Metric | Target | Actual | Status |
|--------|--------|--------|--------|
| FPS | 45+ | ___ | ✅/❌ |
| Memory | <150MB | ___MB | ✅/❌ |
| CPU | <60% | ___% | ✅/❌ |
| Latency | <50ms | ___ms | ✅/❌ |
| Startup | <3s | ___s | ✅/❌ |

### Issues Found
| Issue | Severity | Description | Status |
|-------|----------|-------------|--------|
| | Critical/High/Medium/Low | | Open/Fixed |

### Recommendations
- [ ] Performance optimizations needed
- [ ] Bug fixes required
- [ ] Feature enhancements
- [ ] Ready for production

## 🚀 Quick Test Commands

### Device Info
```bash
# Check device model
adb shell getprop ro.product.model

# Check Android version
adb shell getprop ro.build.version.release

# Check available storage
adb shell df /data

# Check RAM
adb shell cat /proc/meminfo | grep MemTotal
```

### App Testing
```bash
# Install and launch
adb install -r build/app/outputs/flutter-apk/app-terminal-debug.apk
adb shell am start -n com.ccam.terminal/com.ccam.terminal.MainActivity

# Monitor logs
adb logcat -c && adb logcat | grep -E "(FaceDetection|Telpo|Hybrid|UltraFace|Error|Exception)"

# Performance monitoring
adb shell dumpsys meminfo com.ccam.terminal | grep TOTAL
adb shell top -n 1 | grep com.ccam.terminal
```

### Cleanup
```bash
# Uninstall app
adb uninstall com.ccam.terminal

# Clear logs
adb logcat -c
```

---

**Test Status**: 🔄 Ready to Execute  
**Next Step**: Connect Telpo F8 device and run installation test

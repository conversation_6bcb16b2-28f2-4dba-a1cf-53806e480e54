import 'package:equatable/equatable.dart';

/// Base failure class for the application
abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure(this.message, {this.code});

  @override
  List<Object?> get props => [message, code];

  @override
  String toString() => 'Failure: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server-related failures
class ServerFailure extends Failure {
  final int? statusCode;

  const ServerFailure(
    super.message, {
    super.code,
    this.statusCode,
  });

  @override
  List<Object?> get props => [message, code, statusCode];

  @override
  String toString() => 'ServerFailure: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure(super.message, {super.code});

  @override
  String toString() => 'NetworkFailure: $message';
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure(super.message, {super.code});

  @override
  String toString() => 'AuthFailure: $message';
}

/// Validation-related failures
class ValidationFailure extends Failure {
  final Map<String, List<String>>? fieldErrors;

  const ValidationFailure(
    super.message, {
    super.code,
    this.fieldErrors,
  });

  @override
  List<Object?> get props => [message, code, fieldErrors];

  @override
  String toString() {
    String baseMessage = 'ValidationFailure: $message';
    if (fieldErrors != null && fieldErrors!.isNotEmpty) {
      String fieldErrorsString = fieldErrors!.entries
          .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
          .join('; ');
      baseMessage += ' (Field Errors: $fieldErrorsString)';
    }
    return baseMessage;
  }
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure(super.message, {super.code});

  @override
  String toString() => 'CacheFailure: $message';
}

/// File-related failures
class FileFailure extends Failure {
  const FileFailure(super.message, {super.code});

  @override
  String toString() => 'FileFailure: $message';
}

/// Permission-related failures
class PermissionFailure extends Failure {
  const PermissionFailure(super.message, {super.code});

  @override
  String toString() => 'PermissionFailure: $message';
}

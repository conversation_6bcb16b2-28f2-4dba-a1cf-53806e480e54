#!/bin/bash

echo "========================================"
echo "   C-CAM Terminal Test Server"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js version:"
node --version
echo

# Check if we have the simple test server
if [ -f "test_server_simple.js" ]; then
    echo "Starting Simple Test Server..."
    echo "Server will be available at:"
    echo "  - Dashboard: http://localhost:3000"
    echo "  - WebSocket: ws://localhost:3000"
    echo "  - API: http://localhost:3000/api"
    echo
    echo "Press Ctrl+C to stop the server"
    echo
    node test_server_simple.js
elif [ -f "lib/packages/relay_controller/test_server/server.js" ]; then
    echo "Starting Enhanced Test Server..."
    cd lib/packages/relay_controller/test_server
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "Installing dependencies..."
        npm install
        echo
    fi
    
    echo "Server will be available at:"
    echo "  - Dashboard: http://localhost:3000"
    echo "  - WebSocket: ws://localhost:3000"
    echo "  - API: http://localhost:3000/api"
    echo
    echo "Press Ctrl+C to stop the server"
    echo
    node server.js
else
    echo "ERROR: No test server found!"
    echo "Please make sure you have either:"
    echo "  - test_server_simple.js in the root directory"
    echo "  - lib/packages/relay_controller/test_server/server.js"
    exit 1
fi

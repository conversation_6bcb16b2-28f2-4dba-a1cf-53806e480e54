import 'package:flutter/material.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/constants/app_text_styles.dart';
import '../../../../../core/constants/app_dimensions.dart';
import '../../../../../shared/icons/app_icons.dart';

/// Màn hình công cụ với danh sách các tools
class ToolsScreen extends StatefulWidget {
  const ToolsScreen({super.key});

  @override
  State<ToolsScreen> createState() => _ToolsScreenState();
}

class _ToolsScreenState extends State<ToolsScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.spacing2),
                    _buildToolsList(context),
                    <PERSON>zed<PERSON><PERSON>(height: AppDimensions.spacing24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Text(
            'Công cụ',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          _buildToolItem(
            icon: AppIcons.toolAttendance(),
            title: 'Chấm công',
            description: 'Quản lý thời gian làm việc tự động',
            onTap: () {
              // Navigate to attendance screen
            },
          ),
          SizedBox(height: AppDimensions.spacing16),
          _buildToolItem(
            icon: AppIcons.toolAccessControl(),
            title: 'Kiểm soát ra vào',
            description: 'Quản lý truy cập, hoạt động ra vào',
            onTap: () {
              // Navigate to access control screen
            },
          ),
          SizedBox(height: AppDimensions.spacing16),
          _buildToolItem(
            icon: AppIcons.toolSecurity(),
            title: 'Giám sát an ninh',
            description: 'Giám sát doanh nghiệp thông minh',
            onTap: () {
              // Navigate to security screen
            },
          ),
          SizedBox(height: AppDimensions.spacing16),
          _buildToolItem(
            icon: AppIcons.toolSystemAdmin(),
            title: 'Quản trị hệ thống',
            description: 'Quản lý, cấu hình và theo dõi hệ thống',
            onTap: () {
              Navigator.of(context).pushNamed('/system-management');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToolItem({
    required Widget icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingS),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(AppDimensions.radiusRound),
              ),
              child: icon,
            ),
            SizedBox(width: AppDimensions.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacing4),
                  Text(
                    description,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: AppDimensions.spacing8),
            Icon(Icons.chevron_right, size: 16, color: AppColors.textPrimary),
          ],
        ),
      ),
    );
  }
}

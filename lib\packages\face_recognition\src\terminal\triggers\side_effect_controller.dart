import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../../core/models/face_recognition_config.dart';
import '../terminal_face_system.dart';

/// Controls side effects triggered by face recognition in terminal devices
abstract class SideEffectController {
  /// Initialize the controller
  Future<void> initialize();
  
  /// Trigger effects when access is granted
  Future<void> triggerAccessGranted(TerminalRecognitionResult result);
  
  /// Trigger effects when access is denied
  Future<void> triggerAccessDenied(TerminalRecognitionResult result);
  
  /// Test all connected devices
  Future<void> testAllDevices();
  
  /// Get controller status
  SideEffectStatus get status;
  
  /// Dispose resources
  Future<void> dispose();
  
  /// Factory method to create device-specific controller
  static SideEffectController create(TerminalDeviceType deviceType) {
    switch (deviceType) {
      case TerminalDeviceType.telpoF8:
        return TelpoF8SideEffectController();
      case TerminalDeviceType.androidTerminal:
        return AndroidTerminalSideEffectController();
      case TerminalDeviceType.generic:
        return GenericSideEffectController();
    }
  }
}

/// Telpo F8 specific side effect controller
class TelpoF8SideEffectController extends SideEffectController {
  static const MethodChannel _channel = MethodChannel('telpo_f8_controller');
  
  bool _isInitialized = false;
  SideEffectStatus _status = SideEffectStatus.disconnected;
  Timer? _statusCheckTimer;
  
  @override
  SideEffectStatus get status => _status;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize Telpo F8 hardware interfaces
      await _channel.invokeMethod('initialize');
      
      // Test hardware connections
      final relayStatus = await _channel.invokeMethod('testRelay');
      final ledStatus = await _channel.invokeMethod('testLED');
      final buzzerStatus = await _channel.invokeMethod('testBuzzer');
      
      if (relayStatus && ledStatus && buzzerStatus) {
        _status = SideEffectStatus.connected;
      } else {
        _status = SideEffectStatus.partiallyConnected;
      }
      
      // Start periodic status checks
      _statusCheckTimer = Timer.periodic(
        const Duration(seconds: 30),
        (_) => _checkHardwareStatus(),
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('🔌 TelpoF8SideEffectController initialized');
        print('   Status: ${_status.name}');
      }
    } catch (e) {
      _status = SideEffectStatus.error;
      if (kDebugMode) {
        print('❌ Failed to initialize Telpo F8 controller: $e');
      }
      rethrow;
    }
  }
  
  @override
  Future<void> triggerAccessGranted(TerminalRecognitionResult result) async {
    if (!_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('✅ Telpo F8: Access granted for ${result.userName}');
      }
      
      // Sequence of actions for access granted
      await Future.wait([
        _triggerGreenLED(),
        _triggerSuccessBuzzer(),
        _triggerDoorRelay(),
      ]);
      
      // Log access event
      await _logAccessEvent(result, true);
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to trigger access granted effects: $e');
      }
    }
  }
  
  @override
  Future<void> triggerAccessDenied(TerminalRecognitionResult result) async {
    if (!_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('❌ Telpo F8: Access denied for ${result.userName}');
      }
      
      // Sequence of actions for access denied
      await Future.wait([
        _triggerRedLED(),
        _triggerErrorBuzzer(),
      ]);
      
      // Log access event
      await _logAccessEvent(result, false);
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to trigger access denied effects: $e');
      }
    }
  }
  
  /// Trigger green LED for success
  Future<void> _triggerGreenLED() async {
    await _channel.invokeMethod('setLED', {
      'color': 'green',
      'duration': 2000, // 2 seconds
      'pattern': 'solid',
    });
  }
  
  /// Trigger red LED for error
  Future<void> _triggerRedLED() async {
    await _channel.invokeMethod('setLED', {
      'color': 'red',
      'duration': 3000, // 3 seconds
      'pattern': 'blink',
    });
  }
  
  /// Trigger success buzzer
  Future<void> _triggerSuccessBuzzer() async {
    await _channel.invokeMethod('setBuzzer', {
      'frequency': 1000, // Hz
      'duration': 200, // ms
      'pattern': 'single',
    });
  }
  
  /// Trigger error buzzer
  Future<void> _triggerErrorBuzzer() async {
    await _channel.invokeMethod('setBuzzer', {
      'frequency': 500, // Hz
      'duration': 500, // ms
      'pattern': 'double',
    });
  }
  
  /// Trigger door relay
  Future<void> _triggerDoorRelay() async {
    await _channel.invokeMethod('setRelay', {
      'channel': 1,
      'state': true,
      'duration': 3000, // 3 seconds
    });
  }
  
  /// Log access event to local storage
  Future<void> _logAccessEvent(TerminalRecognitionResult result, bool granted) async {
    await _channel.invokeMethod('logEvent', {
      'userId': result.userId,
      'userName': result.userName,
      'timestamp': result.timestamp.toIso8601String(),
      'granted': granted,
      'confidence': result.confidence,
      'source': result.source,
    });
  }
  
  /// Check hardware status periodically
  Future<void> _checkHardwareStatus() async {
    try {
      final status = await _channel.invokeMethod('getHardwareStatus');
      
      if (status['relay'] && status['led'] && status['buzzer']) {
        _status = SideEffectStatus.connected;
      } else {
        _status = SideEffectStatus.partiallyConnected;
      }
    } catch (e) {
      _status = SideEffectStatus.error;
      if (kDebugMode) {
        print('⚠️ Hardware status check failed: $e');
      }
    }
  }
  
  @override
  Future<void> testAllDevices() async {
    if (!_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🧪 Testing all Telpo F8 devices...');
      }
      
      // Test sequence
      await _triggerGreenLED();
      await Future.delayed(const Duration(milliseconds: 500));
      await _triggerRedLED();
      await Future.delayed(const Duration(milliseconds: 500));
      await _triggerSuccessBuzzer();
      await Future.delayed(const Duration(milliseconds: 500));
      await _triggerDoorRelay();
      
      if (kDebugMode) {
        print('✅ Device test completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Device test failed: $e');
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    _statusCheckTimer?.cancel();
    _isInitialized = false;
    
    try {
      await _channel.invokeMethod('dispose');
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error disposing Telpo F8 controller: $e');
      }
    }
    
    if (kDebugMode) {
      print('🗑️ TelpoF8SideEffectController disposed');
    }
  }
}

/// Generic Android terminal controller
class AndroidTerminalSideEffectController extends SideEffectController {
  static const MethodChannel _channel = MethodChannel('android_terminal_controller');
  
  bool _isInitialized = false;
  SideEffectStatus _status = SideEffectStatus.disconnected;
  
  @override
  SideEffectStatus get status => _status;
  
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize Android terminal interfaces
      await _channel.invokeMethod('initialize');
      _status = SideEffectStatus.connected;
      _isInitialized = true;
      
      if (kDebugMode) {
        print('🔌 AndroidTerminalSideEffectController initialized');
      }
    } catch (e) {
      _status = SideEffectStatus.error;
      if (kDebugMode) {
        print('❌ Failed to initialize Android terminal controller: $e');
      }
    }
  }
  
  @override
  Future<void> triggerAccessGranted(TerminalRecognitionResult result) async {
    if (!_isInitialized) return;
    
    try {
      // Android terminal specific actions
      await _channel.invokeMethod('accessGranted', {
        'userId': result.userId,
        'userName': result.userName,
        'timestamp': result.timestamp.toIso8601String(),
      });
      
      if (kDebugMode) {
        print('✅ Android Terminal: Access granted for ${result.userName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to trigger Android terminal access granted: $e');
      }
    }
  }
  
  @override
  Future<void> triggerAccessDenied(TerminalRecognitionResult result) async {
    if (!_isInitialized) return;
    
    try {
      await _channel.invokeMethod('accessDenied', {
        'userId': result.userId,
        'userName': result.userName,
        'timestamp': result.timestamp.toIso8601String(),
      });
      
      if (kDebugMode) {
        print('❌ Android Terminal: Access denied for ${result.userName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to trigger Android terminal access denied: $e');
      }
    }
  }
  
  @override
  Future<void> testAllDevices() async {
    if (!_isInitialized) return;
    
    try {
      await _channel.invokeMethod('testDevices');
      if (kDebugMode) {
        print('🧪 Android terminal device test completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Android terminal device test failed: $e');
      }
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
    
    try {
      await _channel.invokeMethod('dispose');
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error disposing Android terminal controller: $e');
      }
    }
    
    if (kDebugMode) {
      print('🗑️ AndroidTerminalSideEffectController disposed');
    }
  }
}

/// Generic side effect controller (software only)
class GenericSideEffectController extends SideEffectController {
  bool _isInitialized = false;
  SideEffectStatus _status = SideEffectStatus.connected;
  
  @override
  SideEffectStatus get status => _status;
  
  @override
  Future<void> initialize() async {
    _isInitialized = true;
    if (kDebugMode) {
      print('🔌 GenericSideEffectController initialized (software only)');
    }
  }
  
  @override
  Future<void> triggerAccessGranted(TerminalRecognitionResult result) async {
    if (kDebugMode) {
      print('✅ Generic: Access granted for ${result.userName}');
      print('   [Software trigger - no hardware actions]');
    }
  }
  
  @override
  Future<void> triggerAccessDenied(TerminalRecognitionResult result) async {
    if (kDebugMode) {
      print('❌ Generic: Access denied for ${result.userName}');
      print('   [Software trigger - no hardware actions]');
    }
  }
  
  @override
  Future<void> testAllDevices() async {
    if (kDebugMode) {
      print('🧪 Generic: Device test (no hardware to test)');
    }
  }
  
  @override
  Future<void> dispose() async {
    _isInitialized = false;
    if (kDebugMode) {
      print('🗑️ GenericSideEffectController disposed');
    }
  }
}

/// Side effect controller status
enum SideEffectStatus {
  connected,
  partiallyConnected,
  disconnected,
  error,
}

/// Extension for status utilities
extension SideEffectStatusExtension on SideEffectStatus {
  String get displayName {
    switch (this) {
      case SideEffectStatus.connected:
        return 'Connected';
      case SideEffectStatus.partiallyConnected:
        return 'Partially Connected';
      case SideEffectStatus.disconnected:
        return 'Disconnected';
      case SideEffectStatus.error:
        return 'Error';
    }
  }
  
  String get emoji {
    switch (this) {
      case SideEffectStatus.connected:
        return '🟢';
      case SideEffectStatus.partiallyConnected:
        return '🟡';
      case SideEffectStatus.disconnected:
        return '🔴';
      case SideEffectStatus.error:
        return '❌';
    }
  }
}

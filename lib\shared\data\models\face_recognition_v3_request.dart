import 'dart:io';

/// Face recognition request model for API v3.1
class FaceRecognitionV3Request {
  final String deviceId;
  final String cameraId;
  final String cameraIp;
  final String trackingId;
  final File detectedFace;

  const FaceRecognitionV3Request({
    required this.deviceId,
    required this.cameraId,
    required this.cameraIp,
    required this.trackingId,
    required this.detectedFace,
  });

  /// Convert to form data for multipart request
  Map<String, dynamic> toFormData() {
    return {
      'device_id': deviceId,
      'camera_id': cameraId,
      'camera_ip': cameraIp,
      'tracking_id': trackingId,
      'detected_face': detectedFace,
    };
  }

  /// Create from face image file with default values
  factory FaceRecognitionV3Request.fromFaceImage({
    required File faceImage,
    String? deviceId,
    String? cameraId,
    String? cameraIp,
    String? trackingId,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    
    return FaceRecognitionV3Request(
      deviceId: deviceId ?? 'terminal-device-$timestamp',
      cameraId: cameraId ?? 'camera-1',
      cameraIp: cameraIp ?? '127.0.0.1',
      trackingId: trackingId ?? 'track-$timestamp',
      detectedFace: faceImage,
    );
  }

  @override
  String toString() {
    return 'FaceRecognitionV3Request('
        'deviceId: $deviceId, '
        'cameraId: $cameraId, '
        'cameraIp: $cameraIp, '
        'trackingId: $trackingId, '
        'detectedFace: ${detectedFace.path}'
        ')';
  }
}

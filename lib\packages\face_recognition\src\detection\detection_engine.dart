import 'dart:typed_data';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';

/// Abstract interface for face detection engines
abstract class DetectionEngine {
  /// Engine name for identification
  String get name;
  
  /// Engine version
  String get version;
  
  /// Whether the engine is initialized
  bool get isInitialized;
  
  /// Initialize the detection engine
  Future<void> initialize();
  
  /// Detect faces in camera image
  Future<List<DetectedFace>> detectFaces(CameraImage image);
  
  /// Detect faces in raw image bytes
  Future<List<DetectedFace>> detectFacesFromBytes(
    Uint8List bytes,
    int width,
    int height,
  );
  
  /// Dispose resources
  Future<void> dispose();
  
  /// Get engine performance stats
  EngineStats getStats();
}

/// Detected face result
class DetectedFace {
  final Rect boundingBox;
  final double confidence;
  final List<FaceLandmark>? landmarks;
  final double? headEulerAngleY; // Yaw
  final double? headEulerAngleZ; // Roll
  final double? leftEyeOpenProbability;
  final double? rightEyeOpenProbability;
  final double? smilingProbability;
  final int? trackingId;
  
  const DetectedFace({
    required this.boundingBox,
    required this.confidence,
    this.landmarks,
    this.headEulerAngleY,
    this.headEulerAngleZ,
    this.leftEyeOpenProbability,
    this.rightEyeOpenProbability,
    this.smilingProbability,
    this.trackingId,
  });
  
  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'boundingBox': {
        'left': boundingBox.left,
        'top': boundingBox.top,
        'width': boundingBox.width,
        'height': boundingBox.height,
      },
      'confidence': confidence,
      'landmarks': landmarks?.map((l) => l.toMap()).toList(),
      'headEulerAngleY': headEulerAngleY,
      'headEulerAngleZ': headEulerAngleZ,
      'leftEyeOpenProbability': leftEyeOpenProbability,
      'rightEyeOpenProbability': rightEyeOpenProbability,
      'smilingProbability': smilingProbability,
      'trackingId': trackingId,
    };
  }
}

/// Face landmark point
class FaceLandmark {
  final FaceLandmarkType type;
  final Offset position;
  
  const FaceLandmark({
    required this.type,
    required this.position,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'x': position.dx,
      'y': position.dy,
    };
  }
}

/// Face landmark types
enum FaceLandmarkType {
  leftEye,
  rightEye,
  noseBase,
  leftEar,
  rightEar,
  leftMouth,
  rightMouth,
  leftCheek,
  rightCheek,
}

/// Bounding box representation
class Rect {
  final double left;
  final double top;
  final double width;
  final double height;
  
  const Rect({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });
  
  double get right => left + width;
  double get bottom => top + height;
  double get centerX => left + width / 2;
  double get centerY => top + height / 2;
  
  /// Check if point is inside rectangle
  bool contains(Offset point) {
    return point.dx >= left && 
           point.dx <= right && 
           point.dy >= top && 
           point.dy <= bottom;
  }
  
  /// Calculate intersection over union with another rectangle
  double iou(Rect other) {
    final intersectionLeft = math.max(left, other.left);
    final intersectionTop = math.max(top, other.top);
    final intersectionRight = math.min(right, other.right);
    final intersectionBottom = math.min(bottom, other.bottom);
    
    if (intersectionLeft >= intersectionRight || intersectionTop >= intersectionBottom) {
      return 0.0;
    }
    
    final intersectionArea = (intersectionRight - intersectionLeft) * 
                           (intersectionBottom - intersectionTop);
    final unionArea = (width * height) + (other.width * other.height) - intersectionArea;
    
    return intersectionArea / unionArea;
  }
}

/// Engine performance statistics
class EngineStats {
  final String engineName;
  final int totalFramesProcessed;
  final double averageProcessingTime; // milliseconds
  final double currentFPS;
  final int memoryUsageMB;
  final DateTime lastUpdated;
  
  const EngineStats({
    required this.engineName,
    required this.totalFramesProcessed,
    required this.averageProcessingTime,
    required this.currentFPS,
    required this.memoryUsageMB,
    required this.lastUpdated,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'engineName': engineName,
      'totalFramesProcessed': totalFramesProcessed,
      'averageProcessingTime': averageProcessingTime,
      'currentFPS': currentFPS,
      'memoryUsageMB': memoryUsageMB,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Detection engine types
enum DetectionEngineType {
  ultraface,
  mediapipe,
  mlkit,
  custom,
}

/// Engine configuration
class EngineConfig {
  final DetectionEngineType type;
  final String modelPath;
  final double confidenceThreshold;
  final int maxFaces;
  final bool enableLandmarks;
  final bool enableTracking;
  final Map<String, dynamic> customSettings;
  
  const EngineConfig({
    required this.type,
    required this.modelPath,
    this.confidenceThreshold = 0.7,
    this.maxFaces = 3,
    this.enableLandmarks = false,
    this.enableTracking = true,
    this.customSettings = const {},
  });
}

/// Point representation
class Offset {
  final double dx;
  final double dy;

  const Offset(this.dx, this.dy);
}

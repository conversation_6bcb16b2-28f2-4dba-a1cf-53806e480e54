import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import '../core/constants/face_capture_constants.dart';

/// Performance monitoring utility for face detection and camera operations
/// Optimized for Snapdragon 8 Gen 2 performance analysis
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // Performance metrics storage
  final Queue<double> _fpsHistory = Queue<double>();
  final Queue<int> _detectionTimeHistory = Queue<int>();
  final Queue<int> _frameProcessingTimeHistory = Queue<int>();
  
  // Timing variables
  DateTime? _lastFrameTime;
  DateTime? _detectionStartTime;
  DateTime? _frameProcessingStartTime;
  
  // Current metrics
  double _currentFPS = 0.0;
  int _averageDetectionTime = 0;
  int _averageFrameProcessingTime = 0;
  int _totalFramesProcessed = 0;
  int _totalDetections = 0;
  int _skippedFrames = 0;
  
  // Configuration - optimized for mobile performance
  static const int _historySize = 15; // Reduced history for better performance
  static const Duration _metricsUpdateInterval = Duration(milliseconds: 1000); // Less frequent updates
  
  Timer? _metricsTimer;
  Timer? _fpsTimer;
  
  // Getters for current metrics
  double get currentFPS => _currentFPS;
  int get averageDetectionTime => _averageDetectionTime;
  int get averageFrameProcessingTime => _averageFrameProcessingTime;
  int get totalFramesProcessed => _totalFramesProcessed;
  int get totalDetections => _totalDetections;
  int get skippedFrames => _skippedFrames;
  double get detectionEfficiency => _totalFramesProcessed > 0 
      ? (_totalDetections / _totalFramesProcessed) * 100 
      : 0.0;

  /// Start monitoring performance
  void startMonitoring() {
    _resetMetrics();
    _metricsTimer = Timer.periodic(_metricsUpdateInterval, (_) {
      _updateMetrics();
    });

    // Start FPS timer with reduced frequency for mobile optimization
    _fpsTimer = Timer.periodic(const Duration(milliseconds: 100), (_) { // ~10 FPS for monitoring
      recordFrame();
    });

    // Initialize with first frame
    _lastFrameTime = DateTime.now();

    debugPrint('📊 Performance monitoring started for Snapdragon 8 Gen 2');
  }

  /// Stop monitoring performance
  void stopMonitoring() {
    _metricsTimer?.cancel();
    _metricsTimer = null;
    _fpsTimer?.cancel();
    _fpsTimer = null;
    debugPrint('📊 Performance monitoring stopped');
  }

  /// Record frame processing start
  void startFrameProcessing() {
    _frameProcessingStartTime = DateTime.now();
  }

  /// Record frame processing end
  void endFrameProcessing() {
    if (_frameProcessingStartTime != null) {
      final processingTime = DateTime.now().difference(_frameProcessingStartTime!).inMicroseconds;
      _frameProcessingTimeHistory.add(processingTime);
      if (_frameProcessingTimeHistory.length > _historySize) {
        _frameProcessingTimeHistory.removeFirst();
      }
      _totalFramesProcessed++;
    }
    _frameProcessingStartTime = null;
  }

  /// Record face detection start
  void startFaceDetection() {
    _detectionStartTime = DateTime.now();
  }

  /// Record face detection end
  void endFaceDetection() {
    if (_detectionStartTime != null) {
      final detectionTime = DateTime.now().difference(_detectionStartTime!).inMicroseconds;
      _detectionTimeHistory.add(detectionTime);
      if (_detectionTimeHistory.length > _historySize) {
        _detectionTimeHistory.removeFirst();
      }
      _totalDetections++;
    }
    _detectionStartTime = null;
  }

  /// Record frame skip
  void recordFrameSkip() {
    _skippedFrames++;
  }

  /// Record FPS measurement
  void recordFrame() {
    final now = DateTime.now();
    if (_lastFrameTime != null) {
      final frameDuration = now.difference(_lastFrameTime!).inMicroseconds;
      if (frameDuration > 0 && frameDuration < 1000000) { // Ignore frames > 1 second apart
        final fps = 1000000 / frameDuration; // Convert microseconds to FPS
        if (fps > 0 && fps <= 120) { // Reasonable FPS range
          _fpsHistory.add(fps);
          if (_fpsHistory.length > _historySize) {
            _fpsHistory.removeFirst();
          }
        }
      }
    }
    _lastFrameTime = now;
  }

  /// Update calculated metrics
  void _updateMetrics() {
    // Calculate average FPS
    if (_fpsHistory.isNotEmpty) {
      _currentFPS = _fpsHistory.reduce((a, b) => a + b) / _fpsHistory.length;
    }

    // Calculate average detection time
    if (_detectionTimeHistory.isNotEmpty) {
      final totalTime = _detectionTimeHistory.reduce((a, b) => a + b);
      _averageDetectionTime = (totalTime / _detectionTimeHistory.length).round();
    }

    // Calculate average frame processing time
    if (_frameProcessingTimeHistory.isNotEmpty) {
      final totalTime = _frameProcessingTimeHistory.reduce((a, b) => a + b);
      _averageFrameProcessingTime = (totalTime / _frameProcessingTimeHistory.length).round();
    }
  }

  /// Reset all metrics
  void _resetMetrics() {
    _fpsHistory.clear();
    _detectionTimeHistory.clear();
    _frameProcessingTimeHistory.clear();
    _lastFrameTime = null;
    _detectionStartTime = null;
    _frameProcessingStartTime = null;
    _currentFPS = 0.0;
    _averageDetectionTime = 0;
    _averageFrameProcessingTime = 0;
    _totalFramesProcessed = 0;
    _totalDetections = 0;
    _skippedFrames = 0;
  }

  /// Get comprehensive performance report
  Map<String, dynamic> getPerformanceReport() {
    return {
      'fps': {
        'current': _currentFPS.toStringAsFixed(1),
        'target': '30.0',
        'status': _currentFPS >= 25 ? 'Good' : _currentFPS >= 15 ? 'Fair' : 'Poor',
      },
      'detection': {
        'averageTime': '${(_averageDetectionTime / 1000).toStringAsFixed(1)}ms',
        'totalDetections': _totalDetections,
        'efficiency': '${detectionEfficiency.toStringAsFixed(1)}%',
      },
      'frameProcessing': {
        'averageTime': '${(_averageFrameProcessingTime / 1000).toStringAsFixed(1)}ms',
        'totalFrames': _totalFramesProcessed,
        'skippedFrames': _skippedFrames,
      },
      'recommendations': _getRecommendations(),
    };
  }

  /// Get performance-based recommendations for Snapdragon 8 Gen 2
  List<String> _getRecommendations() {
    final recommendations = <String>[];

    // FPS recommendations
    if (_currentFPS < 15) {
      recommendations.add('Increase frameSkip to 8-10 for better FPS');
      recommendations.add('Consider reducing camera resolution to medium');
      recommendations.add('Disable ML Kit tracking and classification');
    } else if (_currentFPS < 25) {
      recommendations.add('Increase frameSkip to 5-7');
      recommendations.add('Increase minDetectionInterval to 300ms');
    }

    // Detection time recommendations
    if (_averageDetectionTime > 100000) { // > 100ms
      recommendations.add('Detection time too high - use FaceDetectorMode.fast');
      recommendations.add('Disable landmarks and contours in ML Kit');
      recommendations.add('Reduce minFaceSize to 0.05');
    }

    // Frame processing recommendations
    if (_averageFrameProcessingTime > 50000) { // > 50ms
      recommendations.add('Frame processing slow - optimize image conversion');
      recommendations.add('Consider using lower camera resolution');
    }

    // Efficiency recommendations
    if (detectionEfficiency < 30) {
      recommendations.add('Too many frames skipped - adjust frameSkip value');
    }

    if (recommendations.isEmpty) {
      recommendations.add('Performance is optimal for Snapdragon 8 Gen 2');
    }

    return recommendations;
  }

  /// Print performance summary to debug console
  void printPerformanceSummary() {
    final report = getPerformanceReport();
    debugPrint('📊 === Snapdragon 8 Gen 2 Performance Report ===');
    debugPrint('🎯 FPS: ${report['fps']['current']} (${report['fps']['status']})');
    debugPrint('⚡ Detection: ${report['detection']['averageTime']} avg, ${report['detection']['efficiency']} efficiency');
    debugPrint('🖼️ Frame Processing: ${report['frameProcessing']['averageTime']} avg');
    debugPrint('📈 Total: ${report['frameProcessing']['totalFrames']} frames, ${report['frameProcessing']['skippedFrames']} skipped');
    
    final recommendations = report['recommendations'] as List<String>;
    if (recommendations.isNotEmpty) {
      debugPrint('💡 Recommendations:');
      for (final rec in recommendations) {
        debugPrint('   • $rec');
      }
    }
    debugPrint('================================================');
  }
}

/// Performance overlay widget for debugging
class PerformanceOverlay extends StatefulWidget {
  final Widget child;
  final bool showOverlay;
  final PerformanceMonitor? monitor; // Allow passing external monitor

  const PerformanceOverlay({
    super.key,
    required this.child,
    this.showOverlay = FaceCaptureConstants.showPerformanceProfiling,
    this.monitor,
  });

  @override
  State<PerformanceOverlay> createState() => _PerformanceOverlayState();
}

class _PerformanceOverlayState extends State<PerformanceOverlay> {
  Timer? _updateTimer;
  late PerformanceMonitor _monitor;

  @override
  void initState() {
    super.initState();
    _monitor = widget.monitor ?? PerformanceMonitor();

    if (widget.showOverlay) {
      // Start monitoring if not already started
      if (widget.monitor == null) {
        _monitor.startMonitoring();
      }

      // Less frequent UI updates for better performance
      _updateTimer = Timer.periodic(const Duration(milliseconds: 1000), (_) {
        if (mounted) setState(() {});
      });
    }
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    // Only stop monitoring if we created our own instance
    if (widget.monitor == null) {
      _monitor.stopMonitoring();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showOverlay) return widget.child;

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 50,
          left: 10, // Move to left side to avoid camera switch button
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Profiling',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'FPS: ${_monitor.currentFPS.toStringAsFixed(1)}',
                  style: TextStyle(
                    color: _getFPSColor(_monitor.currentFPS),
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Detection: ${(_monitor.averageDetectionTime / 1000).toStringAsFixed(1)}ms',
                  style: TextStyle(
                    color: _getDetectionColor(_monitor.averageDetectionTime),
                    fontSize: 11,
                  ),
                ),
                Text(
                  'Frames: ${_monitor.totalFramesProcessed}',
                  style: const TextStyle(color: Colors.white70, fontSize: 10),
                ),
                Text(
                  'Efficiency: ${_monitor.detectionEfficiency.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: _getEfficiencyColor(_monitor.detectionEfficiency),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getFPSColor(double fps) {
    if (fps >= 25) return Colors.green;
    if (fps >= 15) return Colors.orange;
    return Colors.red;
  }

  Color _getDetectionColor(int detectionTime) {
    final timeMs = detectionTime / 1000;
    if (timeMs <= 80) return Colors.green;
    if (timeMs <= 120) return Colors.orange;
    return Colors.red;
  }

  Color _getEfficiencyColor(double efficiency) {
    if (efficiency >= 70) return Colors.green;
    if (efficiency >= 50) return Colors.orange;
    return Colors.red;
  }
}

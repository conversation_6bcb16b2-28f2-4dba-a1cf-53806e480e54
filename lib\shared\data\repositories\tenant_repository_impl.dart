import 'package:dartz/dartz.dart';
import '../../domain/entities/tenant/tenant.dart';
import '../../domain/repositories/tenant_repository.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../data_sources/remote/tenant_remote_data_source.dart';

class TenantRepositoryImpl implements TenantRepository {
  final TenantRemoteDataSource remoteDataSource;

  TenantRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<Tenant>>> getUserTenants({
    int page = 1,
    int size = 10,
  }) async {
    try {
      final tenantModels = await remoteDataSource.getUserTenants(
        page: page,
        size: size,
      );

      final tenants = tenantModels.map((model) => model.toEntity()).toList();
      return Right(tenants);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Tenant>> getTenantById(String tenantId) async {
    try {
      final tenantModel = await remoteDataSource.getTenantById(tenantId);
      return Right(tenantModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Tenant>> createTenant({
    required String name,
    String? address,
    String? unitId,
    List<String>? mappings,
  }) async {
    try {
      final tenantModel = await remoteDataSource.createTenant(
        name: name,
        address: address,
        unitId: unitId,
        mappings: mappings,
      );
      return Right(tenantModel.toEntity());
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> switchTenantContext(String tenantId) async {
    try {
      final success = await remoteDataSource.switchTenantContext(tenantId);
      return Right(success);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  // ========== UNITS IMPLEMENTATION ==========

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getUnits({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    try {
      final units = await remoteDataSource.getUnits(
        page: page,
        limit: limit,
        sortBy: sortBy,
        sortDirection: sortDirection,
        search: search,
        tenantId: tenantId,
        parentUnitId: parentUnitId,
      );
      return Right(units);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUnitsWithPagination({
    int page = 1,
    int limit = 20,
    String? sortBy,
    String? sortDirection,
    String? search,
    String? tenantId,
    String? parentUnitId,
  }) async {
    try {
      final unitsResponse = await remoteDataSource.getUnitsWithPagination(
        page: page,
        limit: limit,
        sortBy: sortBy,
        sortDirection: sortDirection,
        search: search,
        tenantId: tenantId,
        parentUnitId: parentUnitId,
      );
      return Right(unitsResponse);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getUnitById(String unitId) async {
    try {
      final unit = await remoteDataSource.getUnitById(unitId);
      return Right(unit);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getUnitsByTenant(String tenantId) async {
    try {
      final units = await remoteDataSource.getUnitsByTenant(tenantId);
      return Right(units);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> createUnit({
    required String name,
    required String tenantId,
    String? parentUnitId,
    List<String>? mappings,
  }) async {
    try {
      final unit = await remoteDataSource.createUnit(
        name: name,
        tenantId: tenantId,
        parentUnitId: parentUnitId,
        mappings: mappings,
      );
      return Right(unit);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> updateUnit({
    required String unitId,
    String? name,
    String? parentUnitId,
    List<String>? mappings,
  }) async {
    try {
      final unit = await remoteDataSource.updateUnit(
        unitId: unitId,
        name: name,
        parentUnitId: parentUnitId,
        mappings: mappings,
      );
      return Right(unit);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteUnit(String unitId) async {
    try {
      await remoteDataSource.deleteUnit(unitId);
      return const Right(null);
    } on AuthException catch (e) {
      return Left(AuthFailure(e.message, code: e.code));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(
        e.message,
        code: e.code,
        fieldErrors: e.fieldErrors,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
        e.message,
        code: e.code,
        statusCode: e.statusCode,
      ));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }
}

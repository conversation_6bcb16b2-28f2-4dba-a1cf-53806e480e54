import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../providers/face_detection_provider.dart';
import '../core/constants/face_cropping_constants.dart';
import 'face_cropping_service.dart';
import 'face_cropping_api_service.dart';
import 'face_cropping_side_effects_handler.dart';

/// Priority-based face processing queue that processes one face at a time
/// Prioritizes largest/closest face and completes full pipeline before next face
class PriorityFaceProcessingQueue {
  static const String _logTag = '🎯 PriorityFaceQueue';
  
  // Singleton pattern for global queue management
  static final PriorityFaceProcessingQueue _instance = PriorityFaceProcessingQueue._internal();
  factory PriorityFaceProcessingQueue() => _instance;
  PriorityFaceProcessingQueue._internal();
  
  // Queue state
  final Queue<FaceProcessingTask> _queue = Queue<FaceProcessingTask>();
  bool _isProcessing = false;
  FaceProcessingTask? _currentTask;
  
  // Services
  final FaceCroppingApiService _apiService = FaceCroppingApiService();
  
  // Configuration
  int _maxQueueSize = 10; // Giảm từ 50 xuống 10 để tránh quá tải
  Duration _processingTimeout = const Duration(seconds: 30);
  
  // Statistics
  int _processedCount = 0;
  int _failedCount = 0;
  DateTime? _lastProcessingTime;
  
  /// Add face processing task to priority queue
  /// Only processes the best (largest/closest) face to reduce device load
  Future<String> addFaceProcessingTask({
    required String imagePath,
    required List<Face> detectedFaces,
    required FaceDirection direction,
    Map<String, dynamic>? context,
    List<SideEffectType>? enabledSideEffects,
    int priority = 0,
  }) async {
    if (_queue.length >= _maxQueueSize) {
      throw Exception('Queue is full. Current size: ${_queue.length}');
    }
    
    // Select best face (largest area = closest/most prominent)
    final bestFace = _selectBestFace(detectedFaces);
    if (bestFace == null) {
      throw Exception('No suitable face found for processing');
    }
    
    final task = FaceProcessingTask(
      id: _generateTaskId(),
      imagePath: imagePath,
      face: bestFace,
      direction: direction,
      priority: priority,
      context: context ?? {},
      enabledSideEffects: enabledSideEffects ?? FaceCroppingConstants.defaultEnabledSideEffects,
      addedAt: DateTime.now(),
      status: FaceProcessingStatus.queued,
    );
    
    // Insert task based on priority (higher priority first)
    _insertTaskByPriority(task);
    
    debugPrint('$_logTag Added task ${task.id} for ${direction.name} (Priority: $priority, Queue size: ${_queue.length})');
    
    // Start processing if not already running
    if (!_isProcessing) {
      _startProcessing();
    }
    
    return task.id;
  }
  
  /// Select the best face from detected faces (largest area = closest)
  Face? _selectBestFace(List<Face> faces) {
    if (faces.isEmpty) return null;
    
    Face? bestFace;
    double maxArea = 0;
    
    for (final face in faces) {
      final area = face.boundingBox.width * face.boundingBox.height;
      
      // Additional quality checks
      final hasGoodQuality = _checkFaceQuality(face);
      
      if (hasGoodQuality && area > maxArea) {
        maxArea = area;
        bestFace = face;
      }
    }
    
    if (bestFace != null) {
      debugPrint('$_logTag Selected best face with area: $maxArea');
    }
    
    return bestFace;
  }
  
  /// Check face quality for processing suitability
  bool _checkFaceQuality(Face face) {
    // Check minimum face size
    final area = face.boundingBox.width * face.boundingBox.height;
    if (area < 15000) return false; // Minimum area threshold
    
    // Check eye open probability (if available)
    if (face.leftEyeOpenProbability != null && face.leftEyeOpenProbability! < 0.3) return false;
    if (face.rightEyeOpenProbability != null && face.rightEyeOpenProbability! < 0.3) return false;
    
    // Check head angle (not too tilted)
    if (face.headEulerAngleY != null && face.headEulerAngleY!.abs() > 30) return false;
    if (face.headEulerAngleZ != null && face.headEulerAngleZ!.abs() > 20) return false;
    
    return true;
  }
  
  /// Insert task into queue based on priority
  void _insertTaskByPriority(FaceProcessingTask task) {
    if (_queue.isEmpty) {
      _queue.add(task);
      return;
    }
    
    // Convert to list for easier manipulation
    final list = _queue.toList();
    _queue.clear();
    
    // Find insertion point
    int insertIndex = list.length;
    for (int i = 0; i < list.length; i++) {
      if (task.priority > list[i].priority) {
        insertIndex = i;
        break;
      }
    }
    
    // Insert at correct position
    list.insert(insertIndex, task);
    
    // Rebuild queue
    _queue.addAll(list);
  }
  
  /// Start processing queue (one task at a time)
  Future<void> _startProcessing() async {
    if (_isProcessing) return;
    
    _isProcessing = true;
    debugPrint('$_logTag Starting priority queue processing...');
    
    while (_queue.isNotEmpty && _isProcessing) {
      final task = _queue.removeFirst();
      _currentTask = task;
      
      try {
        await _processTask(task);
      } catch (e) {
        debugPrint('$_logTag ❌ Task ${task.id} failed: $e');
        task.status = FaceProcessingStatus.failed;
        task.error = e.toString();
        task.completedAt = DateTime.now();
        _failedCount++;
      }
      
      _currentTask = null;
      
      // Small delay between tasks to prevent overwhelming the device
      await Future.delayed(const Duration(milliseconds: 500));
    }
    
    _isProcessing = false;
    debugPrint('$_logTag Queue processing completed');
  }
  
  /// Process a single task through complete pipeline
  Future<void> _processTask(FaceProcessingTask task) async {
    debugPrint('$_logTag Processing task ${task.id} for ${task.direction.name}');
    
    task.status = FaceProcessingStatus.processing;
    task.startedAt = DateTime.now();
    
    try {
      // Step 1: Crop face from image
      debugPrint('$_logTag Step 1: Cropping face for task ${task.id}');
      task.status = FaceProcessingStatus.cropping;
      
      final croppedImagePath = await FaceCroppingService.cropFaceFromImage(
        imagePath: task.imagePath,
        face: task.face,
        padding: FaceCroppingConstants.defaultPadding,
        outputQuality: FaceCroppingConstants.defaultOutputQuality,
      ).timeout(_processingTimeout);
      
      if (croppedImagePath == null) {
        throw Exception('Face cropping failed');
      }
      
      task.croppedImagePath = croppedImagePath;
      debugPrint('$_logTag ✅ Face cropped successfully: $croppedImagePath');
      
      // Step 2: Process with API (synchronous for immediate response)
      debugPrint('$_logTag Step 2: API processing for task ${task.id}');
      task.status = FaceProcessingStatus.apiProcessing;
      
      final apiResult = await _apiService.processFaceCroppingSynchronous(
        imagePath: task.imagePath,
        face: task.face,
        context: {
          ...task.context,
          'task_id': task.id,
          'direction': task.direction.name,
          'processing_mode': 'priority_queue',
          'face_area': task.face.boundingBox.width * task.face.boundingBox.height,
        },
        enabledSideEffects: task.enabledSideEffects,
      ).timeout(_processingTimeout);
      
      task.apiResult = apiResult;
      
      if (!apiResult.success) {
        throw Exception('API processing failed: ${apiResult.error}');
      }
      
      debugPrint('$_logTag ✅ API processing completed successfully');
      
      // Step 3: Execute side effects
      debugPrint('$_logTag Step 3: Executing side effects for task ${task.id}');
      task.status = FaceProcessingStatus.sideEffects;
      
      final sideEffectsResults = await FaceCroppingSideEffectsHandler.executeSideEffects(
        response: apiResult.apiResponse ?? {},
        enabledSideEffects: task.enabledSideEffects,
        context: {
          ...task.context,
          'task_id': task.id,
          'api_success': true,
        },
      ).timeout(_processingTimeout);
      
      task.sideEffectsResults = sideEffectsResults;
      debugPrint('$_logTag ✅ Side effects executed successfully');
      
      // Step 4: Complete task
      task.status = FaceProcessingStatus.completed;
      task.completedAt = DateTime.now();
      _processedCount++;
      _lastProcessingTime = DateTime.now();
      
      final processingDuration = task.completedAt!.difference(task.startedAt!);
      debugPrint('$_logTag ✅ Task ${task.id} completed in ${processingDuration.inMilliseconds}ms');
      
      // Cleanup temporary files
      await _cleanupTaskFiles(task);
      
    } catch (e, stackTrace) {
      debugPrint('$_logTag ❌ Task ${task.id} failed: $e');
      if (kDebugMode) {
        debugPrint('$_logTag Stack trace: $stackTrace');
      }
      
      task.status = FaceProcessingStatus.failed;
      task.error = e.toString();
      task.completedAt = DateTime.now();
      _failedCount++;
      
      // Cleanup on failure
      await _cleanupTaskFiles(task);
      
      rethrow;
    }
  }
  
  /// Cleanup temporary files for task
  Future<void> _cleanupTaskFiles(FaceProcessingTask task) async {
    if (task.croppedImagePath != null) {
      try {
        await FaceCroppingService.cleanupCroppedFiles([task.croppedImagePath!]);
      } catch (e) {
        debugPrint('$_logTag ⚠️ Failed to cleanup files for task ${task.id}: $e');
      }
    }
  }
  
  /// Get current queue status
  Map<String, dynamic> getQueueStatus() {
    return {
      'is_processing': _isProcessing,
      'queue_size': _queue.length,
      'current_task_id': _currentTask?.id,
      'current_task_status': _currentTask?.status.name,
      'processed_count': _processedCount,
      'failed_count': _failedCount,
      'last_processing_time': _lastProcessingTime?.toIso8601String(),
      'max_queue_size': _maxQueueSize,
      'processing_timeout_seconds': _processingTimeout.inSeconds,
      'queue_tasks': _queue.map((task) => {
        'id': task.id,
        'direction': task.direction.name,
        'priority': task.priority,
        'status': task.status.name,
        'added_at': task.addedAt.toIso8601String(),
      }).toList(),
    };
  }
  
  /// Clear queue (emergency stop)
  void clearQueue() {
    debugPrint('$_logTag Clearing queue (${_queue.length} tasks)');
    _queue.clear();
  }
  
  /// Stop processing
  void stopProcessing() {
    debugPrint('$_logTag Stopping queue processing');
    _isProcessing = false;
  }
  
  /// Configure queue settings
  void configure({
    int? maxQueueSize,
    Duration? processingTimeout,
  }) {
    if (maxQueueSize != null) {
      _maxQueueSize = maxQueueSize;
      debugPrint('$_logTag Max queue size set to: $maxQueueSize');
    }
    
    if (processingTimeout != null) {
      _processingTimeout = processingTimeout;
      debugPrint('$_logTag Processing timeout set to: ${processingTimeout.inSeconds}s');
    }
  }
  
  /// Generate unique task ID
  String _generateTaskId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 1000).toString().padLeft(3, '0');
    return 'face_task_${timestamp}_$random';
  }
  
  /// Get processing statistics
  Map<String, dynamic> getStatistics() {
    final totalTasks = _processedCount + _failedCount;
    final successRate = totalTasks > 0 ? (_processedCount / totalTasks * 100) : 0.0;
    
    return {
      'total_processed': _processedCount,
      'total_failed': _failedCount,
      'success_rate_percent': successRate.toStringAsFixed(1),
      'current_queue_size': _queue.length,
      'is_currently_processing': _isProcessing,
      'last_processing_time': _lastProcessingTime?.toIso8601String(),
    };
  }
}

/// Face processing task status
enum FaceProcessingStatus {
  queued,
  processing,
  cropping,
  apiProcessing,
  sideEffects,
  completed,
  failed,
}

/// Face processing task
class FaceProcessingTask {
  final String id;
  final String imagePath;
  final Face face;
  final FaceDirection direction;
  final int priority;
  final Map<String, dynamic> context;
  final List<SideEffectType> enabledSideEffects;
  final DateTime addedAt;
  
  FaceProcessingStatus status;
  DateTime? startedAt;
  DateTime? completedAt;
  String? error;
  String? croppedImagePath;
  FaceCroppingSyncResult? apiResult;
  Map<SideEffectType, SideEffectResult>? sideEffectsResults;
  
  FaceProcessingTask({
    required this.id,
    required this.imagePath,
    required this.face,
    required this.direction,
    required this.priority,
    required this.context,
    required this.enabledSideEffects,
    required this.addedAt,
    required this.status,
  });
  
  Duration? get processingDuration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image_path': imagePath,
      'direction': direction.name,
      'priority': priority,
      'context': context,
      'enabled_side_effects': enabledSideEffects.map((e) => e.name).toList(),
      'added_at': addedAt.toIso8601String(),
      'status': status.name,
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'processing_duration_ms': processingDuration?.inMilliseconds,
      'error': error,
      'cropped_image_path': croppedImagePath,
      'api_success': apiResult?.success,
      'side_effects_count': sideEffectsResults?.length ?? 0,
    };
  }
}

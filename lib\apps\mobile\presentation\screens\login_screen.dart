import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/constants/app_dimensions.dart';
import '../../../../shared/components/app_logo.dart';
import '../../../../shared/components/app_button.dart';
import '../../../../shared/components/app_input_field.dart';
import '../../../../shared/components/app_toggle_button.dart';
import '../../../../shared/services/service_locator.dart';
import '../../../../shared/core/config/app_config.dart';
import '../../../../shared/presentation/providers/base/base_auth_provider.dart';
import '../../../../shared/components/app_notification.dart';

import '../../../../shared/services/validation_service.dart';
import '../providers/auth_provider.dart';

/// Màn hình đăng nhập
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serverAddressController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _validationService = ValidationService();
  int _selectedToggle = 0; // 0: On Cloud, 1: On Premise
  bool _isLoading = false;
  // Note: _obscurePassword removed as AppInputField handles password visibility internally

  bool get _isFormValid {
    final hasUsername = _usernameController.text.isNotEmpty;
    final hasPassword = _passwordController.text.isNotEmpty;
    final hasServerAddress = _selectedToggle == 0 || _serverAddressController.text.isNotEmpty;

    return hasUsername && hasPassword && hasServerAddress;
  }

  @override
  void initState() {
    super.initState();

    // Add listeners to update button state
    _serverAddressController.addListener(_updateButtonState);
    _usernameController.addListener(_updateButtonState);
    _passwordController.addListener(_updateButtonState);
  }

  void _updateButtonState() {
    setState(() {
      // This will trigger rebuild and update button state
    });
  }

  void _clearFormFields() {
    _serverAddressController.clear();
    _usernameController.clear();
    _passwordController.clear();

    // Also clear any form validation errors
    _formKey.currentState?.reset();
  }

  @override
  void dispose() {
    _serverAddressController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    // Determine base URL based on toggle selection
    final isOnCloudMode = _selectedToggle == 0;

    // Get auth provider first (before async operations)
    final authProvider = context.read<AuthProvider>();

    // Clear any existing errors first
    authProvider.clearError();

    // Validate form using validation service
    final validationError = _validationService.validateLoginForm(
      userName: _usernameController.text,
      password: _passwordController.text,
      serverAddress: _serverAddressController.text,
      isOnPremise: !isOnCloudMode,
    );

    if (validationError != null) {
      authProvider.setError(validationError);
      return;
    }

    // Also validate using form key for UI feedback
    if (!(_formKey.currentState?.validate() ?? false)) return;

    setState(() {
      _isLoading = true;
    });

    try {

      // Switch base URL mode using ServiceLocator
      await serviceLocator.switchBaseUrlMode(
        isOnCloudMode: isOnCloudMode,
        environment: isOnCloudMode ? AppConfig().environment : null,
        onPremiseUrl: isOnCloudMode ? null : _serverAddressController.text.trim(),
      );

      // Perform login
      final success = await authProvider.loginWithValidation(
        userName: _usernameController.text.trim(),
        password: _passwordController.text,
      );

      if (mounted) {
        if (success) {
          // Clear form data
          _usernameController.clear();
          _passwordController.clear();
          if (!isOnCloudMode) {
            _serverAddressController.clear();
          }

          // Navigate to dashboard using enhanced navigation service
          await authProvider.handlePostLoginNavigation(context);
        }
        // Error handling is done by auth provider
        // UI will react to provider state changes via Consumer
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('Lỗi không xác định: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show error message using SnackBar
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _handleForgotPassword() {
    Navigator.of(context).pushNamed('/forgot-password');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: AppDimensions.spacing16),
                _buildHeader(),
                SizedBox(height: AppDimensions.spacing24),
                _buildTitle(),
                SizedBox(height: AppDimensions.spacing24),
                _buildToggleButtons(),
                SizedBox(height: AppDimensions.spacing24),
                _buildForm(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return const AppLogo();
  }

  Widget _buildTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Đăng nhập',
          style: AppTextStyles.heading3,
        ),
        SizedBox(height: AppDimensions.spacing4),
        Text(
          'Nhập tài khoản và mật khẩu để đăng nhập',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildToggleButtons() {
    return Center(
      child: AppToggleButton(
        options: const ['On Cloud', 'On Premise'],
        selectedIndex: _selectedToggle,
        onChanged: (index) {
          setState(() {
            _selectedToggle = index;
            // Clear form when switching tabs to prevent value mixing
            _clearFormFields();
          });
          _updateButtonState();
        },
      ),
    );
  }

  Widget _buildForm() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Column(
          children: [
            // Show error message if any
            if (authProvider.failure != null) ...[
              AppNotification.auth(
                message: authProvider.getUserFriendlyError(authProvider.failure!),
                details: authProvider.failure?.toString(),
                onDismiss: () => authProvider.clearError(),
                onRetry: authProvider.isErrorRetryable(authProvider.failure!)
                    ? () => _handleLogin()
                    : null,
              ),
            ],

            // Server Address input - chỉ hiển thị khi chọn On Premise
            if (_selectedToggle == 1) ...[
              AppInputField(
                key: const ValueKey('server_address_field'),
                controller: _serverAddressController,
                label: 'Địa chỉ máy chủ',
                placeholder: 'Nhập địa chỉ máy chủ (ví dụ: https://server.com)',
                height: 38,
                isRequired: true,
                validator: (value) => _validationService.validateServerAddress(value),
              ),
              SizedBox(height: AppDimensions.spacing20),
            ],
            AppInputField(
              key: ValueKey('username_field_$_selectedToggle'),
              controller: _usernameController,
              label: 'Tên đăng nhập',
              placeholder: 'Nhập tên đăng nhập',
              height: 38,
              isRequired: true,
              validator: (value) => _validationService.validateUserName(value),
            ),
            SizedBox(height: AppDimensions.spacing20),
            AppInputField(
              key: ValueKey('password_field_$_selectedToggle'),
              controller: _passwordController,
              label: 'Mật khẩu',
              placeholder: 'Nhập mật khẩu của bạn',
              height: 38,
              isRequired: true,
              isPassword: true,
              // Note: AppInputField handles password visibility toggle internally
              // so obscureText and onToggleObscure are not needed
              validator: (value) => _validationService.validatePassword(value),
            ),
            SizedBox(height: AppDimensions.spacing24),
            _buildLoginButton(authProvider),
            SizedBox(height: AppDimensions.spacing16),
            _buildForgotPasswordButton(),
          ],
        );
      },
    );
  }

  Widget _buildLoginButton(AuthProvider authProvider) {
    final isLoading = _isLoading || authProvider.authStatus == AuthStatus.loading;
    final isFormValid = _isFormValid;

    return AppButton(
      text: 'Đăng nhập',
      type: AppButtonType.primary,
      onPressed: (isLoading || !isFormValid) ? null : _handleLogin,
      isLoading: isLoading,
    );
  }



  Widget _buildForgotPasswordButton() {
    return Center(
      child: AppButton(
        text: 'Quên mật khẩu?',
        type: AppButtonType.text,
        onPressed: _handleForgotPassword,
        isFullWidth: false,
      ),
    );
  }



}

import '../../../domain/entities/user/user.dart';
import '../../../domain/entities/user/user_profile.dart';

/// User data model for API communication
/// Based on MongoDB Users schema with all required fields
class UserModel {
  final String id;
  final String? faceId;
  final String? avatarId;
  final String code;
  final int subId;
  final String name;
  final String? email;
  final String? phone;
  final DateTime? dob;
  final String? gender;
  final String username;
  final String? password;
  final String status;
  final String? createdBy;
  final List<String> mappings;
  final String? currentUnitId;
  final String? currentTenantId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  // Additional fields from API response
  final Map<String, dynamic>? unit;
  final Map<String, dynamic>? tenant;
  final Map<String, dynamic>? currentRole;

  const UserModel({
    required this.id,
    this.faceId,
    this.avatarId,
    required this.code,
    required this.subId,
    required this.name,
    this.email,
    this.phone,
    this.dob,
    this.gender,
    required this.username,
    this.password,
    required this.status,
    this.createdBy,
    this.mappings = const [],
    this.currentUnitId,
    this.currentTenantId,
    this.createdAt,
    this.updatedAt,
    this.unit,
    this.tenant,
    this.currentRole,
  });

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String? ?? json['_id'] as String,
      faceId: json['face_id'] as String?,
      avatarId: json['avatar_id'] as String?,
      code: json['code'] as String? ?? '',
      subId: json['sub_id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      dob: json['dob'] != null ? DateTime.parse(json['dob'] as String) : null,
      gender: json['gender'] as String?,
      username: json['username'] as String? ?? '',
      password: json['password'] as String?,
      status: json['status'] as String? ?? 'ACTIVE',
      createdBy: json['created_by'] as String?,
      mappings: (json['mappings'] as List<dynamic>?)?.cast<String>() ?? [],
      currentUnitId: json['current_unit_id'] as String?,
      currentTenantId: json['current_tenant_id'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      unit: json['unit'] as Map<String, dynamic>?,
      tenant: json['tenant'] as Map<String, dynamic>?,
      currentRole: json['currentRole'] as Map<String, dynamic>?,
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'face_id': faceId,
      'avatar_id': avatarId,
      'code': code,
      'sub_id': subId,
      'name': name,
      'email': email,
      'phone': phone,
      'dob': dob?.toIso8601String(),
      'gender': gender,
      'username': username,
      'password': password,
      'status': status,
      'created_by': createdBy,
      'mappings': mappings,
      'current_unit_id': currentUnitId,
      'current_tenant_id': currentTenantId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'unit': unit,
      'tenant': tenant,
      'currentRole': currentRole,
    };
  }

  /// Convert UserModel to User entity
  User toEntity() {
    // Create UserProfile from available data
    final profile = UserProfile(
      avatarId: avatarId,
      // Note: Other profile fields would need to be added to UserModel if available from API
      // For now, we only have avatarId
    );

    return User(
      id: id,
      username: username,
      name: name,
      email: email,
      phone: phone,
      dob: dob,
      gender: gender,
      avatar: avatarId,
      isActive: status == 'ACTIVE',
      currentTenantId: currentTenantId,
      createdAt: createdAt,
      updatedAt: updatedAt,
      profile: profile,
      metadata: {
        'face_id': faceId,
        'code': code,
        'sub_id': subId,
        'status': status,
        'created_by': createdBy,
        'mappings': mappings,
        'current_unit_id': currentUnitId,
      },
    );
  }

  /// Create UserModel from User entity
  factory UserModel.fromEntity(User entity) {
    return UserModel(
      id: entity.id,
      faceId: entity.metadata['face_id'] as String?,
      avatarId: entity.avatar ?? entity.profile?.avatarId, // Use avatar from entity or profile
      code: entity.metadata['code'] as String? ?? '',
      subId: entity.metadata['sub_id'] as int? ?? 0,
      name: entity.name,
      email: entity.email,
      phone: entity.phone,
      dob: entity.dob,
      gender: entity.gender,
      username: entity.username,
      status: entity.metadata['status'] as String? ?? (entity.isActive == true ? 'ACTIVE' : 'INACTIVE'),
      createdBy: entity.metadata['created_by'] as String?,
      mappings: (entity.metadata['mappings'] as List<dynamic>?)?.cast<String>() ?? [],
      currentUnitId: entity.metadata['current_unit_id'] as String?,
      currentTenantId: entity.currentTenantId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Create a copy of UserModel with updated fields
  UserModel copyWith({
    String? id,
    String? faceId,
    String? avatarId,
    String? code,
    int? subId,
    String? name,
    String? email,
    String? phone,
    DateTime? dob,
    String? gender,
    String? username,
    String? password,
    String? status,
    String? createdBy,
    List<String>? mappings,
    String? currentUnitId,
    String? currentTenantId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? unit,
    Map<String, dynamic>? tenant,
    Map<String, dynamic>? currentRole,
  }) {
    return UserModel(
      id: id ?? this.id,
      faceId: faceId ?? this.faceId,
      avatarId: avatarId ?? this.avatarId,
      code: code ?? this.code,
      subId: subId ?? this.subId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dob: dob ?? this.dob,
      gender: gender ?? this.gender,
      username: username ?? this.username,
      password: password ?? this.password,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      mappings: mappings ?? this.mappings,
      currentUnitId: currentUnitId ?? this.currentUnitId,
      currentTenantId: currentTenantId ?? this.currentTenantId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      unit: unit ?? this.unit,
      tenant: tenant ?? this.tenant,
      currentRole: currentRole ?? this.currentRole,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, name: $name, status: $status)';
  }
}

import 'package:get_it/get_it.dart';
import '../data_sources/remote/user_remote_data_source.dart';
import '../models/user/user_model.dart';
import '../../core/network/api_client.dart';
import '../../services/api_endpoints.dart';

/// User status enum for compatibility with existing UI
enum UserStatus { active, inactive }

/// Extension to convert between API status and enum
extension UserStatusExtension on UserStatus {
  String get apiValue {
    switch (this) {
      case UserStatus.active:
        return 'active';
      case UserStatus.inactive:
        return 'inactive';
    }
  }

  static UserStatus fromApiValue(String value) {
    switch (value.toLowerCase()) {
      case 'active':
        return UserStatus.active;
      case 'inactive':
        return UserStatus.inactive;
      default:
        return UserStatus.active;
    }
  }
}

/// Wrapper model for UI compatibility
class UserUIModel {
  final String id; // Display ID (code)
  final String realId; // Real ID (_id) for API calls
  final String name;
  final String unit;
  final UserStatus status;
  final String avatar;
  final String email;
  final String phone;
  final UserModel _originalModel;

  UserUIModel._({
    required this.id,
    required this.realId,
    required this.name,
    required this.unit,
    required this.status,
    required this.avatar,
    required this.email,
    required this.phone,
    required UserModel originalModel,
  }) : _originalModel = originalModel;

  factory UserUIModel.fromUserModel(UserModel model, {String? unitName}) {
    return UserUIModel._(
      id: model.code.isNotEmpty ? model.code : model.id,
      realId: model.id, // Always use the real _id from API
      name: model.name,
      unit: unitName ?? 'Unknown Unit',
      status: UserStatusExtension.fromApiValue(model.status),
      avatar: model.avatarId ?? '',
      email: model.email ?? '',
      phone: model.phone ?? '',
      originalModel: model,
    );
  }

  UserModel get originalModel => _originalModel;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserUIModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Pagination info from API response
class PaginationInfo {
  final int total;
  final int pageIndex;
  final int pageSize;
  final bool hasNext;

  const PaginationInfo({
    required this.total,
    required this.pageIndex,
    required this.pageSize,
    required this.hasNext,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      total: json['total'] as int? ?? 0,
      pageIndex: json['pageIndex'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 10,
      hasNext: json['hasNext'] as bool? ?? false,
    );
  }
}

/// Response wrapper for users API
class UsersResponse {
  final List<UserModel> users;
  final PaginationInfo pagination;

  const UsersResponse({
    required this.users,
    required this.pagination,
  });

  factory UsersResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>;
    final usersData = data['items'] as List<dynamic>? ?? [];
    final paginationData = data['pagination'] as Map<String, dynamic>? ?? {};

    return UsersResponse(
      users: usersData
          .map((userData) => UserModel.fromJson(userData as Map<String, dynamic>))
          .toList(),
      pagination: PaginationInfo.fromJson(paginationData),
    );
  }
}

/// Service to handle users API calls with UI compatibility
class UsersApiService {
  static final UsersApiService _instance = UsersApiService._internal();
  factory UsersApiService() => _instance;
  UsersApiService._internal();

  final UserRemoteDataSource _remoteDataSource = UserRemoteDataSourceImpl(
    apiClient: GetIt.instance<ApiClient>(),
  );

  // Store last pagination info for UI
  PaginationInfo? _lastPaginationInfo;

  /// Get total count from last API call
  int get totalCount => _lastPaginationInfo?.total ?? 0;

  /// Get users with pagination and filters - returns response with pagination info
  Future<UsersResponse> getUsersWithPagination({
    required int page,
    required int pageSize,
    String? searchQuery,
    String? unitFilter,
    UserStatus? statusFilter,
  }) async {
    try {
      // Make direct API call to get full response
      final apiClient = GetIt.instance<ApiClient>();

      // Build query parameters
      final queryParameters = <String, dynamic>{
        'pageSize': pageSize.toString(),
        'pageIndex': page.toString(),
        if (searchQuery != null) 'search': searchQuery,
        if (statusFilter != null) 'status': statusFilter == UserStatus.active ? 'active' : 'inactive',
      };

      // Build endpoint URL based on whether unit filter is applied
      String endpoint;
      if (unitFilter != null && unitFilter.isNotEmpty) {
        // Use unit-specific endpoint: /users/unit/{unitId}
        endpoint = '/users/unit/$unitFilter';
      } else {
        // Use general users endpoint: /users
        endpoint = '/users';
      }

      final response = await apiClient.get(
        endpoint,
        queryParameters: queryParameters,
      );

      if (response['success'] == true && response['data'] != null) {
        final usersResponse = UsersResponse.fromJson(response);

        // Store pagination info for later use
        _lastPaginationInfo = usersResponse.pagination;

        return usersResponse;
      } else {
        throw Exception('Failed to get users: ${response['error']?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      throw Exception('Failed to get users: $e');
    }
  }

  /// Get users with pagination and filters
  Future<List<UserUIModel>> getUsers({
    required int page,
    required int pageSize,
    String? searchQuery,
    String? unitFilter,
    UserStatus? statusFilter,
  }) async {
    try {
      final usersResponse = await getUsersWithPagination(
        page: page,
        pageSize: pageSize,
        searchQuery: searchQuery,
        unitFilter: unitFilter,
        statusFilter: statusFilter,
      );

      // Convert to UI models
      List<UserUIModel> uiModels = usersResponse.users.map((user) {
        // Extract unit name from user unit field if available
        String unitName = 'Unknown Unit';
        if (user.unit != null) {
          unitName = user.unit!['name'] as String? ?? 'Unknown Unit';
        }

        return UserUIModel.fromUserModel(user, unitName: unitName);
      }).toList();

      return uiModels;
    } catch (e) {
      throw Exception('Failed to get users: $e');
    }
  }

  /// Get user by ID
  Future<UserUIModel?> getUserById(String id) async {
    try {
      final user = await _remoteDataSource.getUserById(id);
      return UserUIModel.fromUserModel(user);
    } catch (e) {
      // Return null if user not found or other error
      return null;
    }
  }

  /// Create new user
  Future<UserUIModel> createUser(UserUIModel user) async {
    try {
      // Convert UI model back to API model
      final apiUser = user.originalModel;
      final createdUser = await _remoteDataSource.createUser(
        username: apiUser.username,
        password: apiUser.password ?? '',
        name: apiUser.name,
        unitId: apiUser.currentUnitId ?? '',
        email: apiUser.email,
        phone: apiUser.phone,
        dob: apiUser.dob?.toIso8601String(),
        gender: apiUser.gender,
      );

      return UserUIModel.fromUserModel(createdUser);
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  /// Create new user with request body (for UserDetailScreen)
  Future<UserModel> createUserWithBody(Map<String, dynamic> requestBody) async {
    try {
      final apiClient = GetIt.instance<ApiClient>();

      final response = await apiClient.post(
        ApiEndpoints.createUser,
        body: requestBody,
      );

      if (response['success'] == true && response['data'] != null) {
        return UserModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to create user';
        throw Exception(errorMessage);
      }
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  /// Update user
  Future<UserUIModel> updateUser(UserUIModel user) async {
    try {
      final apiUser = user.originalModel;
      final updatedUser = await _remoteDataSource.updateUser(
        userId: apiUser.id,
        name: apiUser.name,
        email: apiUser.email,
        phone: apiUser.phone,
        dob: apiUser.dob?.toIso8601String(),
        gender: apiUser.gender,
        unitId: apiUser.currentUnitId,
      );

      return UserUIModel.fromUserModel(updatedUser);
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  /// Update user with request body (for UserDetailScreen)
  Future<UserModel> updateUserWithBody(String userId, Map<String, dynamic> requestBody) async {
    try {
      final apiClient = GetIt.instance<ApiClient>();

      final response = await apiClient.put(
        ApiEndpoints.updateUser(userId),
        body: requestBody,
      );

      if (response['success'] == true && response['data'] != null) {
        return UserModel.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        final errorMessage = response['error']?['message'] ?? 'Failed to update user';
        throw Exception(errorMessage);
      }
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  /// Delete user
  Future<void> deleteUser(String id) async {
    try {
      await _remoteDataSource.deleteUser(id);
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }

  /// Get user statistics (mock for now, should be from API)
  Future<Map<String, int>> getUserStats() async {
    // This should be implemented with a real API endpoint
    return {
      'total': 0,
      'active': 0,
      'inactive': 0,
      'units': 0, // Updated from departments to units
    };
  }
}

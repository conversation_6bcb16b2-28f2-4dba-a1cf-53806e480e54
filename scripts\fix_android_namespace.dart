#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// <PERSON>ript to fix Android namespace issues with flutter packages
void main() async {
  print('🔧 Fixing Android namespace issues...');
  
  await fixFlutterBluetoothSerial();
  await fixUsbSerial();
  
  print('✅ Android namespace fixes applied successfully!');
}

/// Fix flutter_bluetooth_serial namespace issue
Future<void> fixFlutterBluetoothSerial() async {
  print('📱 Fixing flutter_bluetooth_serial namespace...');
  
  try {
    // Find the package in pub cache
    final pubCacheDir = await findPubCacheDir();
    final packageDir = Directory('$pubCacheDir/hosted/pub.dev/flutter_bluetooth_serial-0.4.0');
    
    if (!await packageDir.exists()) {
      print('⚠️  flutter_bluetooth_serial package not found in pub cache');
      return;
    }
    
    final buildGradleFile = File('${packageDir.path}/android/build.gradle');
    if (!await buildGradleFile.exists()) {
      print('⚠️  build.gradle not found for flutter_bluetooth_serial');
      return;
    }
    
    // Read current content
    String content = await buildGradleFile.readAsString();
    
    // Check if already fixed
    if (content.contains('namespace')) {
      print('✅ flutter_bluetooth_serial namespace already fixed');
      return;
    }
    
    // Add namespace after android {
    content = content.replaceFirst(
      'android {',
      '''android {
    namespace "io.github.edufolly.flutterbluetoothserial"'''
    );
    
    // Write back
    await buildGradleFile.writeAsString(content);
    print('✅ flutter_bluetooth_serial namespace fixed');
    
  } catch (e) {
    print('❌ Failed to fix flutter_bluetooth_serial: $e');
  }
}

/// Fix usb_serial namespace issue
Future<void> fixUsbSerial() async {
  print('🔌 Fixing usb_serial namespace...');
  
  try {
    // Find the package in pub cache
    final pubCacheDir = await findPubCacheDir();
    final packageDir = Directory('$pubCacheDir/hosted/pub.dev/usb_serial-0.5.2');
    
    if (!await packageDir.exists()) {
      print('⚠️  usb_serial package not found in pub cache');
      return;
    }
    
    final buildGradleFile = File('${packageDir.path}/android/build.gradle');
    if (!await buildGradleFile.exists()) {
      print('⚠️  build.gradle not found for usb_serial');
      return;
    }
    
    // Read current content
    String content = await buildGradleFile.readAsString();
    
    // Check if already fixed
    if (content.contains('namespace')) {
      print('✅ usb_serial namespace already fixed');
      return;
    }
    
    // Add namespace after android {
    content = content.replaceFirst(
      'android {',
      '''android {
    namespace "dev.bessems.usb_serial"'''
    );
    
    // Write back
    await buildGradleFile.writeAsString(content);
    print('✅ usb_serial namespace fixed');
    
  } catch (e) {
    print('❌ Failed to fix usb_serial: $e');
  }
}

/// Find pub cache directory
Future<String> findPubCacheDir() async {
  // Try environment variable first
  String? pubCache = Platform.environment['PUB_CACHE'];
  if (pubCache != null) {
    return pubCache;
  }
  
  // Default locations
  if (Platform.isWindows) {
    final userProfile = Platform.environment['USERPROFILE'];
    return '$userProfile\\AppData\\Local\\Pub\\Cache';
  } else {
    final home = Platform.environment['HOME'];
    return '$home/.pub-cache';
  }
}

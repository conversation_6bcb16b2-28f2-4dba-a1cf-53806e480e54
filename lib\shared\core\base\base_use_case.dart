import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../errors/failures.dart';

/// Base abstract class cho tất cả Use Cases
///
/// Định nghĩa contract chung cho business logic operations
abstract class BaseUseCase<Type, Params> {
  /// Execute use case với parameters
  Future<Either<Failure, Type>> call(Params params);
}

/// Base class cho Use Cases không cần parameters
abstract class BaseUseCaseNoParams<Type> {
  /// Execute use case không cần parameters
  Future<Either<Failure, Type>> call();
}

/// Base class cho Use Cases đồng bộ
abstract class BaseSyncUseCase<Type, Params> {
  /// Execute use case đồng bộ với parameters
  Either<Failure, Type> call(Params params);
}

/// Base class cho Use Cases đồng bộ không cần parameters
abstract class BaseSyncUseCaseNoParams<Type> {
  /// Execute use case đồng bộ không cần parameters
  Either<Failure, Type> call();
}

/// Base class cho Stream Use Cases
abstract class BaseStreamUseCase<Type, Params> {
  /// Execute use case trả về Stream
  Stream<Either<Failure, Type>> call(Params params);
}

/// Base parameters class
abstract class BaseParams extends Equatable {
  const BaseParams();
}

/// No parameters class
class NoParams extends BaseParams {
  const NoParams();

  @override
  List<Object?> get props => [];
}

/// Pagination parameters
class PaginationParams extends BaseParams {
  final int page;
  final int limit;
  final String? sortBy;
  final String? sortDirection;

  const PaginationParams({
    this.page = 1,
    this.limit = 20,
    this.sortBy,
    this.sortDirection,
  });

  @override
  List<Object?> get props => [page, limit, sortBy, sortDirection];

  PaginationParams copyWith({
    int? page,
    int? limit,
    String? sortBy,
    String? sortDirection,
  }) {
    return PaginationParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortDirection: sortDirection ?? this.sortDirection,
    );
  }
}

/// Search parameters
class SearchParams extends PaginationParams {
  final String? search;

  const SearchParams({
    super.page,
    super.limit,
    super.sortBy,
    super.sortDirection,
    this.search,
  });

  @override
  List<Object?> get props => [...super.props, search];

  @override
  SearchParams copyWith({
    int? page,
    int? limit,
    String? sortBy,
    String? sortDirection,
    String? search,
  }) {
    return SearchParams(
      page: page ?? this.page,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortDirection: sortDirection ?? this.sortDirection,
      search: search ?? this.search,
    );
  }
}

/// ID parameters
class IdParams extends BaseParams {
  final String id;

  const IdParams({required this.id});

  @override
  List<Object?> get props => [id];
}

/// File upload parameters
class FileUploadParams extends BaseParams {
  final String fileName;
  final String fileData;
  final String? mimeType;
  final Map<String, dynamic>? metadata;

  const FileUploadParams({
    required this.fileName,
    required this.fileData,
    this.mimeType,
    this.metadata,
  });

  @override
  List<Object?> get props => [fileName, fileData, mimeType, metadata];
}

/// Mixin để thêm validation cho Use Cases
mixin ValidationMixin<Params extends BaseParams> {
  /// Validate parameters trước khi execute
  Either<Failure, void> validateParams(Params params);

  /// Execute với validation
  Future<Either<Failure, Type>> executeWithValidation<Type>(
    Params params,
    Future<Either<Failure, Type>> Function() operation,
  ) async {
    // Validate parameters first
    final validationResult = validateParams(params);
    if (validationResult.isLeft()) {
      return Left(validationResult.fold((failure) => failure, (_) => throw Exception()));
    }

    // Execute operation if validation passes
    return await operation();
  }
}

/// Mixin để thêm caching cho Use Cases
mixin CachingMixin<Type, Params extends BaseParams> {
  /// Cache key generator
  String generateCacheKey(Params params);

  /// Cache duration
  Duration get cacheDuration => const Duration(minutes: 5);

  /// Get cached result
  Future<Type?> getCachedResult(String cacheKey);

  /// Cache result
  Future<void> cacheResult(String cacheKey, Type result);

  /// Execute với caching
  Future<Either<Failure, Type>> executeWithCaching(
    Params params,
    Future<Either<Failure, Type>> Function() operation,
  ) async {
    final cacheKey = generateCacheKey(params);
    
    // Try to get cached result first
    final cachedResult = await getCachedResult(cacheKey);
    if (cachedResult != null) {
      return Right(cachedResult);
    }

    // Execute operation if no cache
    final result = await operation();
    
    // Cache successful result
    result.fold(
      (failure) => null, // Don't cache failures
      (data) => cacheResult(cacheKey, data),
    );

    return result;
  }
}

/// Mixin để thêm retry logic cho Use Cases
mixin RetryMixin<Type, Params extends BaseParams> {
  /// Maximum retry attempts
  int get maxRetryAttempts => 3;

  /// Retry delay
  Duration get retryDelay => const Duration(seconds: 1);

  /// Should retry on failure
  bool shouldRetry(Failure failure) {
    // Default: retry on network failures
    return failure is NetworkFailure;
  }

  /// Execute với retry logic
  Future<Either<Failure, Type>> executeWithRetry(
    Params params,
    Future<Either<Failure, Type>> Function() operation,
  ) async {
    int attempts = 0;
    
    while (attempts < maxRetryAttempts) {
      final result = await operation();
      
      if (result.isRight()) {
        return result; // Success, return immediately
      }
      
      final failure = result.fold((l) => l, (r) => throw Exception());
      
      attempts++;
      
      // If max attempts reached or shouldn't retry, return failure
      if (attempts >= maxRetryAttempts || !shouldRetry(failure)) {
        return Left(failure);
      }
      
      // Wait before retry
      await Future.delayed(retryDelay);
    }
    
    // This should never be reached, but just in case
    return Left(ServerFailure('Max retry attempts exceeded'));
  }
}

/// Utility class để tạo common use case implementations
class UseCaseUtils {
  /// Tạo simple use case từ function
  static BaseUseCase<Type, Params> create<Type, Params extends BaseParams>(
    Future<Either<Failure, Type>> Function(Params params) operation,
  ) {
    return _SimpleUseCase(operation);
  }

  /// Tạo no params use case từ function
  static BaseUseCaseNoParams<Type> createNoParams<Type>(
    Future<Either<Failure, Type>> Function() operation,
  ) {
    return _SimpleUseCaseNoParams(operation);
  }
}

/// Internal implementation cho simple use case
class _SimpleUseCase<Type, Params extends BaseParams> extends BaseUseCase<Type, Params> {
  final Future<Either<Failure, Type>> Function(Params params) _operation;

  _SimpleUseCase(this._operation);

  @override
  Future<Either<Failure, Type>> call(Params params) => _operation(params);
}

/// Internal implementation cho simple no params use case
class _SimpleUseCaseNoParams<Type> extends BaseUseCaseNoParams<Type> {
  final Future<Either<Failure, Type>> Function() _operation;

  _SimpleUseCaseNoParams(this._operation);

  @override
  Future<Either<Failure, Type>> call() => _operation();
}

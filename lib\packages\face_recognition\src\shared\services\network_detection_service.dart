import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Network detection service for monitoring connectivity
class NetworkDetectionService extends ChangeNotifier {
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = false;
  bool _isInitialized = false;
  
  bool get isOnline => _isOnline;
  bool get isInitialized => _isInitialized;
  
  /// Initialize the network detection service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('🌐 Initializing NetworkDetectionService');
      }
      
      // Check initial connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      _isOnline = _isConnected(connectivityResult.isNotEmpty ? connectivityResult.first : ConnectivityResult.none);
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          _onConnectivityChanged(results.isNotEmpty ? results.first : ConnectivityResult.none);
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ Connectivity stream error: $error');
          }
        },
      );
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ NetworkDetectionService initialized');
        print('   Initial status: ${_isOnline ? "Online" : "Offline"}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize NetworkDetectionService: $e');
      }
      rethrow;
    }
  }
  
  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = _isConnected(result);
    
    if (wasOnline != _isOnline) {
      if (kDebugMode) {
        print('🌐 Network status changed: ${_isOnline ? "Online" : "Offline"}');
      }
      notifyListeners();
    }
  }
  
  /// Check if connectivity result indicates online status
  bool _isConnected(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
      case ConnectivityResult.ethernet:
        return true;
      case ConnectivityResult.none:
      case ConnectivityResult.bluetooth:
      case ConnectivityResult.vpn:
      case ConnectivityResult.other:
        return false;
    }
  }
  
  /// Test actual internet connectivity
  Future<bool> testInternetConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
  
  /// Force check network status
  Future<void> forceCheck() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      _onConnectivityChanged(connectivityResult.isNotEmpty ? connectivityResult.first : ConnectivityResult.none);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Force network check failed: $e');
      }
    }
  }
  
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('🗑️ NetworkDetectionService disposed');
    }
    
    super.dispose();
  }
}
